name: research-image-sync-test

on:
  workflow_dispatch:
  push:
    branches:
      - "main"
  pull_request:

permissions:
  contents: read

jobs:
  research-image-sync-test:
    if: github.repository_owner == 'augmentcode'
    name: Research Image Sync Test
    runs-on:
      - self-hosted # magic constant
      - gcp-us1 # machine with GPU
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0

      - name: Get Changed "*_tag.txt" Files
        id: changes
        uses: ./.github/actions/changed-files
        with:
          files: research/environments/*_tag.txt

      - name: Noop
        if: steps.changes.outputs.any_changed == 'false'
        run: |
          echo "No research/environments/*_tag.txt files changed."

      - name: Research Image Sync Test
        if: steps.changes.outputs.any_changed == 'true'
        # NOTE(mattm): This requires that the gh_runner service account can read secret/image-sync-test, which has the registry creds.
        run: |
          research/environments/containers/image-sync-test.sh "" $(cat "${{ steps.changes.outputs.all_changed_files_path }}")

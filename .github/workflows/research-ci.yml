name: research-ci

on:
  workflow_dispatch:
  push:
    branches:
      - "main"
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  research-ci:
    if: github.repository_owner == 'augmentcode'
    name: Research CI
    runs-on:
      - self-hosted # magic constant
      - gcp-us1 # machine with GPU
    timeout-minutes: 90
    env:
      AUGMENT_EFS_PATH: /mnt/efs/augment-ci-testing
    steps:
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"
      - name: XML Metadata
        timeout-minutes: 1
        shell: bash
        env:
          PR_NUMBER: ${{ github.event.number }}
        run: |
          # Set up the metadata file first so we have it if the run is cancelled
          set +e
          mkdir -p -m 755 ${AUGMENT_EFS_PATH}/jobs/junit/${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}
          chown 1000:1000 ${AUGMENT_EFS_PATH}/jobs/junit/${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}
          echo "pr_number: ${PR_NUMBER}" > ${AUGMENT_EFS_PATH}/jobs/junit/${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}/metadata.yaml
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        env:
          ACTIONS_STEP_DEBUG: true
        with:
          fetch-depth: 0

      - name: Get changed files
        id: pr-changes
        uses: ./.github/actions/changed-files
        with:
          separator: ","

      - name: Get Head SHA
        if: ${{ github.event_name == 'pull_request' }}
        id: get-head-sha
        run: |
          echo "GH_PR_HEAD_SHA=${{ github.event.pull_request.head.sha }}" >> ${GITHUB_ENV}

      - name: Get Branch Age
        if: github.event_name == 'pull_request'
        id: get-branch-age
        run: |
          echo "Checking age of the merge base of ${{ github.event.pull_request.head.sha }} against origin/${{ github.event.pull_request.base.ref }}"
          echo "HEAD SHA ${{ github.event.pull_request.head.sha }}"
          git log -n1 ${{ github.event.pull_request.head.sha }} --pretty=fuller --date=rfc-local
          pr_date=$(git log -n1 ${{ github.event.pull_request.head.sha }} --pretty=format:"%ct")

          echo ""
          echo "HEAD of origin/${{ github.event.pull_request.base.ref }}"
          base_ref_sha=$(git rev-parse origin/${{ github.event.pull_request.base.ref }})
          git log -n1 origin/${{ github.event.pull_request.base.ref }} --pretty=fuller --date=rfc-local
          base_ref_date=$(git log -n1 origin/${{ github.event.pull_request.base.ref }} --pretty=format:"%ct")

          merge_base=$(git merge-base ${{ github.event.pull_request.head.sha }} $base_ref_sha)
          echo ""
          echo "MERGE BASE $merge_base"
          git log -n1 $merge_base --pretty=fuller --date=rfc-local
          mb_date=$(git log -n1 $merge_base --pretty=format:"%ct")

          pr_age=$(( $base_ref_date - $mb_date ))

          # Three is the number thou shall count...
          max_age=$(( 60 * 60 * 24 * 3 ))
          echo "PR age: ${pr_age}s, max age 3 days(${max_age}s)"

          if [[ $pr_age -gt $max_age ]]; then
            echo "PR is too old, failing ($pr_age > $max_age)"
            status="failure"
          else
            echo "PR is not too old ($pr_age < $max_age)"
            status="success"
          fi
          python research/infra/svc/invalidate-stale-prs/invalidate-stale-prs.py set-status --head-sha ${{ github.event.pull_request.head.sha }} --status $status
          exit 0

      - name: Test
        id: research-test
        shell: bash
        env:
          # Comma separated list of paths we search for tests to run
          PATHS_TO_TEST: "research"
          # Comma separated list of paths that will trigger tests in PATHS_TO_TEST
          PATHS_TO_WATCH: "base"
          # Comma separated list of files outside of the above paths that will trigger testing
          FILES_TO_WATCH: pyproject.toml,research/requirements.txt
          # Comma separated list of paths to test only if that path changes.
          EXTRA_PATHS_TO_TEST: "experimental/guy/agent_qa"
          # Environment variables starting with DET_ GH_ GITHUB_ pass through to k8s
          DET_MASTER: "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud"
          DET_USER: "github"
          DET_PASS: ${{ secrets.GH_DETERMINED_PASSWORD }}
          GH_EVENT_ACTION: ${{ github.event_name }}
          GH_EVENT_LABEL: ${{ github.event.label.name }}
          GH_EVENT_TYPE: ${{ github.event.action }}
          GH_PR_LABELS: ${{ toJson(github.event.pull_request.labels) }}
          ALL_CHANGED_FILES_PATH: ${{ steps.pr-changes.outputs.all_changed_files_path }}
          CHANGED_FILES_COUNT: ${{ steps.pr-changes.outputs.all_changed_files_count }}
        run: |
          git status
          echo "Number of changed files: ${CHANGED_FILES_COUNT}"
          pip install jwt==1.3.1
          research/environments/containers/github/ci_launcher.py
      - name: slack notify
        if: ${{ failure() && github.event_name == 'push' && github.ref_name == 'main' }}
        id: slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        with:
          payload: |
            {
              "text": "Post merge research-ci: ${{ job.status }}\nPR: ${{ github.event.pull_request.html_url || github.event.head_commit.url }}\nRUN: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_INFRA_ALERTS_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
      - name: Test Summary
        timeout-minutes: 10
        uses: test-summary/action@31493c76ec9e7aa675f1585d3ed6f1da69269a86
        with:
          paths: "${{ env.AUGMENT_EFS_PATH }}/jobs/junit/${{ github.run_id }}_${{ github.run_number }}/*.xml"
        if: always()
      - name: XML Upload
        timeout-minutes: 5
        shell: bash
        if: always()
        env:
          METRICS_DB_PASSWORD: ${{ secrets.METRICS_DB_PASSWORD }}
          PR_NUMBER: ${{ github.event.number }}
        run: |
          set -x
          # never ever fail for this step
          set +e
          echo "Parsing ${AUGMENT_EFS_PATH}/jobs/junit/${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}/"
          # Don't fail on main.
          if [[ -n "$PR_NUMBER" ]]; then
              pr="--pr-num ${PR_NUMBER}"
          else
              pr=""
          fi
          python tools/k8s/parse_xunit_xml.py \
            --update-db ${pr} \
            --run-id ${{ github.run_id }}_${{ github.run_number }} \
            ${AUGMENT_EFS_PATH}/jobs/junit/${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER} && {
            echo "Removing ${AUGMENT_EFS_PATH}/jobs/junit/${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}"
            rm -rf ${AUGMENT_EFS_PATH}/jobs/junit/${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}
          } || true

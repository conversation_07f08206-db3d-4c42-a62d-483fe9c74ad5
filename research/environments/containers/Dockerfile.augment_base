ARG BASE_IMG
FROM $BASE_IMG AS augment_base

USER root

# Unset this - we inherit it from the upstream for the GPU containers
ENV PIP_NO_CACHE_DIR=

### Minimal APT packages to support adding repos.
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update -y \
 && apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        software-properties-common \
 && apt-get clean

### Prep additional APT repos

# apt: kubectl repo
# https://kubernetes.io/docs/tasks/tools/install-kubectl-linux/#install-using-native-package-management
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && mkdir -p /etc/apt/keyrings \
 && KUBECTL_VERSION=v1.32 \
 && curl -fsSL https://pkgs.k8s.io/core:/stable:/$KUBECTL_VERSION/deb/Release.key | gpg --dearmor -o /etc/apt/keyrings/kubernetes-apt-keyring.gpg \
 && echo "deb [signed-by=/etc/apt/keyrings/kubernetes-apt-keyring.gpg] https://pkgs.k8s.io/core:/stable:/$KUBECTL_VERSION/deb/ /" > /etc/apt/sources.list.d/kubernetes.list

# apt: git repo (AU-1128)
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && add-apt-repository ppa:git-core/ppa -y

#### System package (uses Python 3.11 version in Ubuntu 22.04)
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update -y \
 && apt-get install -y \
        ack \
        autojump \
        autotools-dev \
        awscli \
        build-essential \
        bzip2 \
        chrpath \
        cmake \
        curl \
        docker.io \
        dumb-init \
        g++ \
        gcc \
        git \
        gosu \
        gnupg \
        htop \
        ibutils \
        ibverbs-utils \
        iftop \
        infiniband-diags \
        iputils-ping \
        iotop \
        jq \
        kubectl \
        less \
        libcupti-dev \
        libjson-c-dev \
        libmariadb-dev \
        libmlx4-1 \
        libmunge-dev \
        libtinfo5 \
        llvm-14-dev \
        make \
        mariadb-server \
        munge \
        nano \
        net-tools \
        nfs-common \
        openssh-client \
        patchelf \
        pciutils \
        pdsh \
        perftest \
        perl \
        postgresql-client \
        rdma-core \
        rdmacm-utils \
        rsync \
        ssh \
        sudo \
        tmux \
        tree \
        unzip \
        vim \
        wget \
        zstd \
  && apt-get clean

ENV LD_LIBRARY_PATH=/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/lib/x86_64-linux-gnu:/usr/local/nvidia/lib:/usr/local/nvidia/lib64
ENV PATH=/usr/local/mpi/bin:/home/<USER>/.local/bin:/usr/local/go/bin:/opt/conda/bin:/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:

# Install uv (it's written in Rust).
RUN --mount=type=cache,target=/root/dl : \
  && UV_VERSION=0.7.13 \
  && UV_SHA256SUM=909278eb197c5ed0e9b5f16317d1255270d1f9ea4196e7179ce934d48c4c2545 \
  && UV_URL="https://github.com/astral-sh/uv/releases/download/$UV_VERSION/uv-x86_64-unknown-linux-gnu.tar.gz" \
  && UV_TGZ="uv-x86_64-unknown-linux-gnu-$UV_VERSION.tar.gz" \
  && UV_DL="/root/dl/$UV_TGZ" \
  && test -e "$UV_DL" || curl -fsSL "$UV_URL" -o "$UV_DL" \
  && printf "%s %s\n" "$UV_SHA256SUM" "$UV_DL" | sha256sum -c \
  && tar -xz --no-same-owner -C /usr/local/bin --strip-components=1 -f "$UV_DL" \
  && /usr/local/bin/uv generate-shell-completion bash | install -oroot -groot -m0644 /dev/stdin -DT /usr/local/share/bash-completion/completions/uv \
  && /usr/local/bin/uv generate-shell-completion fish | install -oroot -groot -m0644 /dev/stdin -DT /usr/local/share/fish/vendor_completions.d/uv.fish \
  && /usr/local/bin/uv generate-shell-completion zsh  | install -oroot -groot -m0644 /dev/stdin -DT /usr/local/share/zsh/site-functions/_uv

### Python
ARG PYTHON_VERSION=3.11.7
COPY --chown=root:root --chmod=0755 install_python.sh /root/install_python.sh
COPY --chown=root:root --chmod=0644 pip.conf /etc/pip.conf
RUN --mount=type=cache,target=/root/.cache /root/install_python.sh ${PYTHON_VERSION}

### Bazel and Buildifier
RUN --mount=type=cache,target=/root/dl : \
 && BAZELISK_VERSION=v1.25.0 \
 && BAZELISK_256SUM=fd8fdff418a1758887520fa42da7e6ae39aefc788cf5e7f7bb8db6934d279fc4 \
 && BAZELISK_DL=/root/dl/bazelisk-"$BAZELISK_VERSION" \
 && test -e "$BAZELISK_DL" || curl -fsSL https://github.com/bazelbuild/bazelisk/releases/download/"$BAZELISK_VERSION"/bazelisk-linux-amd64 -o "$BAZELISK_DL" \
 && printf "%s %s\n" "$BAZELISK_256SUM" "$BAZELISK_DL" | sha256sum -c \
 && install -oroot -groot -m0755 "$BAZELISK_DL" /usr/local/bin/bazel

RUN --mount=type=cache,target=/root/dl : \
 && BUILDIFIER_VERSION=v7.1.1 \
 && BUILDIFIER_256SUM=54b7f2ce8f22761cfad264416e912056ae9c8645f59eb6583b846b4864a1ee83 \
 && BUILDIFIER_DL=/root/dl/buildifier-"$BUILDIFIER_VERSION" \
 && test -e "$BUILDIFIER_DL" || curl -fsSL https://github.com/bazelbuild/buildtools/releases/download/"$BUILDIFIER_VERSION"/buildifier-linux-amd64 -o "$BUILDIFIER_DL" \
 && printf "%s %s\n" "$BUILDIFIER_256SUM" "$BUILDIFIER_DL" | sha256sum -c \
 && install -oroot -groot -m0755 "$BUILDIFIER_DL" /usr/local/bin/buildifier

### SSH config and bashrc
RUN echo 'Host *'                       >  /etc/ssh/ssh_config.d/00-augment-base.conf \
 && echo '    StrictHostKeyChecking no' >> /etc/ssh/ssh_config.d/00-augment-base.conf \
 && echo 'export PDSH_RCMD_TYPE=ssh'                                                       >  /etc/profile.d/00-augment-base.sh \
 && echo 'export PATH=/usr/local/mpi/bin:/home/<USER>/.local/bin:/usr/local/go/bin:$PATH' >> /etc/profile.d/00-augment-base.sh

### Install go toolchain (/usr/local/go/bin is added to PATH above)
RUN --mount=type=cache,target=/root/dl : \
 && _GO_VER=1.24.3 \
 && _GO_256SUM=3333f6ea53afa971e9078895eaa4ac7204a8c6b5c68c10e6bc9a33e8e391bdd8 \
 && _GO_TGZ="go$_GO_VER.linux-amd64.tar.gz" \
 && _GO_DL="/root/dl/$_GO_TGZ" \
 && test -e "$_GO_DL" || curl -fsSL https://golang.org/dl/"$_GO_TGZ" -o "$_GO_DL" \
 && printf "%s %s\n" "$_GO_256SUM" "$_GO_DL" | sha256sum -c \
 && rm -fr /usr/local/go \
 && tar -C /usr/local -xzf "$_GO_DL"

### Install `augi`
COPY --chmod=755 augi.build /usr/local/bin/augi.live
RUN /usr/local/bin/augi.live release select live

### User account
RUN useradd --create-home --uid 1000 --shell /bin/bash augment \
 && printf '%s ALL=(ALL:ALL) NOPASSWD: ALL\n' augment | install -o root -g root -m400 /dev/stdin /etc/sudoers.d/zzz-augment

### AU-1492 install homebrew and graphite
# NOTE(mattm): Homebrew insists on installing as a regular user to /home/<USER>
# install to avoid warnings. We set HOME=/home/<USER>/home/<USER>/.cache/Homebrew.
RUN install -o augment -g augment -m0755 -d /home/<USER>/
RUN --mount=type=cache,target=/home/<USER>/.cache : \
 && chown augment:augment /home/<USER>/.cache \
 && curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh -o /home/<USER>/brew-install.sh \
 && chmod +x /home/<USER>/brew-install.sh \
 && echo 'if [[ -x /home/<USER>/.linuxbrew/bin/brew ]]; then'      >  /etc/profile.d/90-homebrew.sh \
 && echo '    eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"' >> /etc/profile.d/90-homebrew.sh \
 && echo 'fi'                                                         >> /etc/profile.d/90-homebrew.sh \
 && sudo -iu augment HOME=/home/<USER>/home/<USER>/brew-install.sh \
 && sudo -iu augment HOME=/home/<USER>/tap/graphite

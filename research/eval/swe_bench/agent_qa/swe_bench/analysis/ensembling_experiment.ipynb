#%%
run_dirs_old = [
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11601_11602_11603_11604_11605_11606_11607_11608_11609_11610/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11671_11672_11673_11674_11675_11676_11677_11678_11679_11680/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11754_11755_11756_11757_11758_11759_11760_11761_11762_11763/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11768_11769_11770_11771_11772_11773_11774_11776_11777_11778/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11882_11884_11886_11888_11890_11892_11894_11896_11899_11901/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11906_11907_11908_11909_11910_11911_11912_11913_11914_11915/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12264_12266_12268_12270_12272_12273_12274_12275_12276_12277/",
]

run_dirs = [
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12264_12266_12268_12270_12272_12273_12274_12275_12276_12277/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12349_12350_12351_12352_12353_12354_12355_12356_12357_12358/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12359_12360_12361_12362_12363_12364_12365_12366_12367_12368/",
    # less good oens but still good
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12303_12304_12305_12306_12307_12308_12309_12310_12311_12312/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11671_11672_11673_11674_11675_11676_11677_11678_11679_11680/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/",
]


def get_consolidated_run_id(dir: str) -> str:
    return dir.split("consolidated_runs/")[1].split("/")[0]
#%%
from glob import glob
import json
from functools import lru_cache
import pickle
from pathlib import Path
from tqdm import tqdm
from research.eval.swe_bench.agent_qa.swe_bench import (
    instruction_prompt_sequential_thinking,
)


@lru_cache(maxsize=100)
def get_example_eval_dirs(run_dir):
    return glob(run_dir + "workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/")


def get_example_pickle(dir):
    path = Path(dir).parent.parent.parent.parent.parent / "agent_log.pickle"
    with open(path, "rb") as f:
        data = pickle.load(f)
    return data


def get_answer_explanation(data):
    complete_call = None
    for entry in data:
        if (
            hasattr(entry, "tool")
            and hasattr(entry.tool, "name")
            and entry.tool.name == "complete"
        ):
            complete_call = entry

    return complete_call.tool_input["answer"]


instance_id_to_run_results = {}
for run_dir in tqdm(run_dirs):
    run_id = get_consolidated_run_id(run_dir)
    for example_eval_dir in get_example_eval_dirs(run_dir):
        patch_diff_path = example_eval_dir + "patch.diff"
        report_path = example_eval_dir + "report.json"

        try:
            example_pickle = get_example_pickle(example_eval_dir)
            answer_explanation = get_answer_explanation(example_pickle)
        except Exception as e:
            print(f"Failed to load answer explanation for {example_eval_dir}: {e}")
            answer_explanation = None

        with open(patch_diff_path, "r") as f:
            patch_diff = f.read()
        try:
            with open(report_path, "r") as f:
                report = json.load(f)
            instance_id, eval_res = list(report.items())[0]
        except FileNotFoundError:
            instance_id = str(Path(report_path).parent.name)
            eval_res = {"resolved": False}
        is_success = eval_res["resolved"]

        issue_path = (
            Path(example_eval_dir).parent.parent.parent.parent.parent / "issue.json"
        )
        with open(issue_path, "r") as f:
            issue = json.load(f)
        problem_statement = issue["problem_statement"]

        location = f"/run/determined/workdir/swe_work/workspace_{instance_id}"
        instruction = instruction_prompt_sequential_thinking.PROMPT.format(
            location=location,
            pr_description=problem_statement,
            step7_str="Run select tests from the repo to make sure that your fix doesn't break anything else.",
        )

        if instance_id not in instance_id_to_run_results:
            instance_id_to_run_results[instance_id] = {}
        instance_id_to_run_results[instance_id][run_id] = {
            "patch_diff": patch_diff,
            "is_success": is_success,
            "instruction": instruction,
            "answer_explanation": answer_explanation,
        }


dataset_save_dir = (
    "/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment10.pickle"
)
with open(dataset_save_dir, "wb") as f:
    pickle.dump(instance_id_to_run_results, f)
#%%
import pickle

# dataset_save_dir = "/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment3.pickle"
# dataset_save_dir = "/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment5.pickle"
dataset_save_dir = (
    "/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment10.pickle"
)
with open(dataset_save_dir, "rb") as f:
    instance_id_to_run_results = pickle.load(f)
#%% md
# Summary stats
#%%
print(len(instance_id_to_run_results))
print(list(instance_id_to_run_results["django__django-11099"].values())[0].keys())
#%%
import numpy as np

# separate success rates

num_succeses = {get_consolidated_run_id(dir): [] for dir in run_dirs}
success_percs = []
for instance_id, res_dct in instance_id_to_run_results.items():
    for i, (run_id, run_data) in enumerate(res_dct.items()):
        num_succeses[run_id].append(run_data["is_success"])
for i, (dir, successes) in enumerate(num_succeses.items()):
    success_percs.append(sum(successes) / 500)
    print(f"Run {i}: {sum(successes) / 500 } (dir: {dir})")

print("Mean score: ", np.mean(success_percs))
#%%
# pass rate if we get best of n for each instance
num_successes = 0
for k, res_ls in instance_id_to_run_results.items():
    if any(res["is_success"] for res in res_ls.values()):
        num_successes += 1
print(num_successes / len(instance_id_to_run_results))
#%%
# compute avg pair-wise num of differing examples
import numpy as np

run_ids = [get_consolidated_run_id(dir) for dir in run_dirs]
num_differing_ls = []
for run_id_1 in run_ids:
    for run_id_2 in run_ids:
        if run_id_1 == run_id_2:
            continue
        # num overlapping successes
        num_differing = 0
        found_good_run = True
        for k, res_dct in instance_id_to_run_results.items():
            res_1 = res_dct.get(run_id_1, {"is_success": False})["is_success"]
            res_2 = res_dct.get(run_id_2, {"is_success": False})["is_success"]
            if (res_1 and not res_2) or (not res_1 and res_2):
                num_differing += 1
        num_differing_ls.append(num_differing)
print(num_differing_ls)

print("Avg num differing: ", np.mean(num_differing_ls))
#%%
# compute number of examples that succeed for at least N runs
instance_id_to_success_ct = {}
for k, res_dct in instance_id_to_run_results.items():
    success_ct = 0
    for run_id, run_data in res_dct.items():
        if run_data["is_success"]:
            success_ct += 1
    instance_id_to_success_ct[k] = success_ct

# for i = 1...len(run_dirs), print the number of instances that have at least i successes
for i in range(1, len(run_dirs) + 1):
    print(
        f"Percentage of instances with at least {i} successes: {sum(v >= i for v in instance_id_to_success_ct.values())/len(instance_id_to_success_ct)}"
    )
#%%
from research.llm_apis.llm_client import AnthropicDirectClient
from research.llm_apis.llm_client import OpenAIDirectClient
from research.llm_apis.llm_client import TextPrompt
from tqdm import tqdm
from functools import partial
from concurrent.futures import ThreadPoolExecutor
import concurrent.futures
import random

anthropic_client = AnthropicDirectClient(
    model_name="claude-3-7-sonnet-20250219",
    max_retries=50,
    use_low_qos_server=True,
)

openai_client = OpenAIDirectClient(
    model_name="o1-2024-12-17",
    max_retries=50,
    cot_model=True,
)


def compute_success_output(
    res_ls,
    prompt_generator,
    client,
    verbose=False,
    shuffle=False,
    use_answer_explanations=False,
):
    instruction = list(res_ls.values())[0]["instruction"]
    diffs = [res["patch_diff"] for res in res_ls.values()]
    if use_answer_explanations:
        answer_explanations = [res["answer_explanation"] for res in res_ls.values()]
        assert len(answer_explanations) == len(diffs)
    else:
        answer_explanations = None
    if shuffle:
        random.shuffle(diffs)

    prompt = prompt_generator(instruction, diffs, answer_explanations)

    messages = [[TextPrompt(text=prompt)]]
    for i in range(3):
        try:
            output = client.generate(messages, max_tokens=16384, temperature=0.0)
            break
        except ValueError as e:
            print("Failed to generate response, retrying.")
            if i == 2:
                raise e

    try:
        solution_index = (
            int(
                output[0][0]
                .text.split("<solution_index>")[-1]
                .split("</solution_index>", 1)[0]
            )
            - 1
        )
    except (IndexError, ValueError) as e:
        print(
            f"Failed to parse solution index from with exception ({e}).  Output: {'-' * 30}\n\n {output[0][0].text}. {'-' * 30}\n\n Just picking the first solution."
        )
        solution_index = -1

    solution_res = (
        list(res_ls.values())[solution_index]
        if solution_index >= 0
        else list(res_ls.values())[0]
    )
    is_success = solution_res["is_success"]

    if verbose:
        print("PROMPT")
        print(prompt)
        print("RESPONSE")
        print(output[0][0].text)

    return solution_index, is_success
#%% md
# Ensembling experiments
#%% md
### Best-first
#%%
USE_ANSWER_EXPLANATIONS = True


def get_llm_judge_prompt_anthropic_bestofn(
    instruction: str, diffs: list[str], answer_explanations: list[str] | None = None
) -> str:
    prompt = ""

    prompt += f"""\

I am a software engineer. I am working on a task in my codebase. Here is the task:

<instruction>
{instruction}
</instruction>

I have generated {len(diffs)} different solutions to this task. Please evaluate each solution and respond with the number of the solution that best solves the task. 
There is always at least 1 correct solution, so responding that there is no correct solution is not an option. Here are the solutions:

"""

    for i, diff in enumerate(diffs):
        prompt += f"""\

<candidate_solution index={i+1}>
{diff}
</candidate_solution index={i+1}>
"""

        if answer_explanations is not None:
            assert len(answer_explanations) == len(diffs)
            prompt += f"""\

<candidate_explanation index={i+1}>
{answer_explanations[i]}
</candidate_explanation index={i+1}>
"""

    prompt += """\

Follow these steps to pick the best solution:
1. Analyze each solution and understand what it does.
2. Describe your thinking process on how to pick the best solution.
3. Compare how the different candidate solutions address the task.
4. Pick the best solution. Explicitly write the number of the best solution inside XML tags <solution_index>...</solution_index>. Do not put anything inside the XML tags other than the number.

"""

    return prompt


def process_instance_bestofn_vote(item):
    k, res_ls = item
    shuffle = False
    for i in range(3):
        try:
            solution_index, is_success = compute_success_output(
                res_ls,
                get_llm_judge_prompt_anthropic_bestofn,
                openai_client,
                shuffle=shuffle,
                use_answer_explanations=USE_ANSWER_EXPLANATIONS,
            )
            return k, solution_index, is_success
        except Exception:
            print("Failed to generate response, retrying.")
            shuffle = True
    raise ValueError("Failed to generate response after 3 retries")


results = []
example_id_to_soln_run_idx = {}
with ThreadPoolExecutor(max_workers=32) as executor:
    # Submit all tasks
    futures = [
        executor.submit(process_instance_bestofn_vote, item)
        for item in list(instance_id_to_run_results.items())[:1]
    ]

    # Process results as they complete
    for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures)):
        k, solution_index, is_success = future.result()
        results.append(is_success)
        example_id_to_soln_run_idx[k] = solution_index

# Count successes
success_ct = sum(results)
print(success_ct / 500)
#%% md
### Majority voting
#%%
USE_ANSWER_EXPLANATIONS = True


def get_llm_judge_prompt_anthropic_majority_vote(
    instruction: str, diffs: list[str], answer_explanations: list[str] | None = None
) -> str:
    prompt = ""

    prompt += f"""\

I am a software engineer. I am working on a task in my codebase. Here is the task:

<instruction>
{instruction}
</instruction>

I have generated {len(diffs)} different solutions to this task. Please evaluate each solution below. Each solution is in a <candidate_solution> tag. Along,
with each solution, there is a <candidate_explanation> tag that provides a justification for the solution, along with some additional context about it.

"""

    for i, diff in enumerate(diffs):
        prompt += f"""\

<candidate_solution index={i+1}>
{diff}
</candidate_solution index={i+1}>
"""
        if answer_explanations is not None:
            assert len(answer_explanations) == len(diffs)
            prompt += f"""\

<candidate_explanation index={i+1}>
{answer_explanations[i]}
</candidate_explanation index={i+1}>
"""

    prompt += """\

Follow these steps to pick the best solution:
1. Analyze each solution, along with its explanation, and understand what it does.
2. Compare and contrast the different approaches to the solution. Evaluate the pros and cons of each solution.
3. Pick the majority vote solution. Explicitly write the number of one example of the majority vote solution inside XML tags <solution_index>...</solution_index>. Do not put anything inside the XML tags other than the number.

"""

    return prompt


def process_instance_majority_vote(item):
    k, res_ls = item
    solution_index, is_success = compute_success_output(
        res_ls,
        get_llm_judge_prompt_anthropic_majority_vote,
        openai_client,
        use_answer_explanations=USE_ANSWER_EXPLANATIONS,
    )
    return k, solution_index, is_success


results = []
example_id_to_soln_run_idx = {}
example_id_to_is_success = {}
with ThreadPoolExecutor(max_workers=32) as executor:
    # Submit all tasks
    futures = [
        executor.submit(process_instance_majority_vote, item)
        for item in list(instance_id_to_run_results.items())
    ]

    # Process results as they complete
    for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures)):
        k, solution_index, is_success = future.result()
        results.append(is_success)
        example_id_to_soln_run_idx[k] = solution_index
        example_id_to_is_success[k] = is_success

# Count successes
success_ct = sum(results)
print(success_ct / 500)
#%%
# Count successes
success_ct = sum(results)
print(success_ct / 500)
#%% md
### analyze results
#%%
success_rate_by_repo = {}
example_ct_by_repo = {}
for k, is_success in example_id_to_is_success.items():
    repo = "-".join(k.split("-")[:-1]).replace("__", "/")
    success_rate_by_repo[repo] = success_rate_by_repo.get(repo, 0) + int(is_success)
    example_ct_by_repo[repo] = example_ct_by_repo.get(repo, 0) + 1
for repo, success_ct in sorted(success_rate_by_repo.items(), key=lambda x: x[0]):
    print(f"{repo}: {success_ct} / {example_ct_by_repo[repo]}")
#%%
with open(
    "/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment/mar15perc_results.json",
    "w",
) as f:
    output = {
        "run_dirs": run_dirs,
        "example_id_to_soln_run_idx": example_id_to_soln_run_idx,
        "success_rate": success_ct / 500,
    }
    json.dump(output, f, indent=2)
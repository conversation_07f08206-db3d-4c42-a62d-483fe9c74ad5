import json
import pickle
from functools import lru_cache
from glob import glob
from pathlib import Path

from tqdm import tqdm

from research.eval.swe_bench.agent_qa.swe_bench import (
    instruction_prompt_sequential_thinking,
)

run_dirs_old = [
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11601_11602_11603_11604_11605_11606_11607_11608_11609_11610/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11671_11672_11673_11674_11675_11676_11677_11678_11679_11680/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11754_11755_11756_11757_11758_11759_11760_11761_11762_11763/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11768_11769_11770_11771_11772_11773_11774_11776_11777_11778/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11882_11884_11886_11888_11890_11892_11894_11896_11899_11901/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11906_11907_11908_11909_11910_11911_11912_11913_11914_11915/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12264_12266_12268_12270_12272_12273_12274_12275_12276_12277/",
]

run_dirs = [
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11794_11795_11796_11797_11798_11799_11800_11801_11802_11803/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12264_12266_12268_12270_12272_12273_12274_12275_12276_12277/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12349_12350_12351_12352_12353_12354_12355_12356_12357_12358/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12359_12360_12361_12362_12363_12364_12365_12366_12367_12368/",
    # less good oens but still good
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/12303_12304_12305_12306_12307_12308_12309_12310_12311_12312/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11671_11672_11673_11674_11675_11676_11677_11678_11679_11680/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11535_11536_11537_11538_11539_11540_11541_11542_11543_11544/",
    "/mnt/efs/augment/public_html/swebench/consolidated_runs/11883_11885_11887_11889_11891_11893_11895_11897_11898_11900/",
]


def get_consolidated_run_id(dir: str) -> str:
    return dir.split("consolidated_runs/")[1].split("/")[0]


@lru_cache(maxsize=100)
def get_example_eval_dirs(run_dir):
    return glob(run_dir + "workspace_*/logs/run_evaluation/swe_work/Augment_Agent/*/")


def get_example_pickle(dir):
    path = Path(dir).parent.parent.parent.parent.parent / "agent_log.pickle"
    with open(path, "rb") as f:
        data = pickle.load(f)
    return data


def get_answer_explanation(data):
    complete_call = None
    for entry in data:
        if (
            hasattr(entry, "tool")
            and hasattr(entry.tool, "name")
            and entry.tool.name == "complete"
        ):
            complete_call = entry
    if not complete_call:
        raise ValueError("No complete call found in pickle.")

    return complete_call.tool_input["answer"]


instance_id_to_run_results = {}
for run_dir in tqdm(run_dirs):
    run_id = get_consolidated_run_id(run_dir)
    for example_eval_dir in get_example_eval_dirs(run_dir):
        patch_diff_path = example_eval_dir + "patch.diff"
        report_path = example_eval_dir + "report.json"

        try:
            example_pickle = get_example_pickle(example_eval_dir)
            answer_explanation = get_answer_explanation(example_pickle)
        except Exception as e:
            print(f"Failed to load answer explanation for {example_eval_dir}: {e}")
            answer_explanation = None

        with open(patch_diff_path, "r") as f:
            patch_diff = f.read()
        try:
            with open(report_path, "r") as f:
                report = json.load(f)
            instance_id, eval_res = list(report.items())[0]
        except FileNotFoundError:
            instance_id = str(Path(report_path).parent.name)
            eval_res = {"resolved": False}
        is_success = eval_res["resolved"]

        issue_path = (
            Path(example_eval_dir).parent.parent.parent.parent.parent / "issue.json"
        )
        with open(issue_path, "r") as f:
            issue = json.load(f)
        problem_statement = issue["problem_statement"]

        location = f"/run/determined/workdir/swe_work/workspace_{instance_id}"
        instruction = instruction_prompt_sequential_thinking.PROMPT.format(
            location=location,
            pr_description=problem_statement,
            step7_str="Run select tests from the repo to make sure that your fix doesn't break anything else.",
        )

        if instance_id not in instance_id_to_run_results:
            instance_id_to_run_results[instance_id] = {}
        instance_id_to_run_results[instance_id][run_id] = {
            "patch_diff": patch_diff,
            "is_success": is_success,
            "instruction": instruction,
            "answer_explanation": answer_explanation,
        }


dataset_save_dir = (
    "/mnt/efs/augment/user/colin/swe_bench_ensembling_experiment10.pickle"
)
with open(dataset_save_dir, "wb") as f:
    pickle.dump(instance_id_to_run_results, f)

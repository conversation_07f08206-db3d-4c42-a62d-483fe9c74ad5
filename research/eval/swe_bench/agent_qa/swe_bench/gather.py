#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to gather logs from nested workspace directories, merge predictions, and collect agent logs.

This script takes an input path containing workspace folders (like workspace_sphinx-doc__sphinx-9367)
and extracts deeply nested log folders to become top-level folders in the output directory.
It also collects all predictions.json files from workspace directories and merges them into a single file.
Additionally, it collects agent_log.txt files from workspace directories and saves them individually
to the trajs/ folder with repository names as filenames.

Example usage:
python research/eval/swe_bench/agent_qa/swe_bench/gather.py /mnt/efs/augment/public_html/swebench/consolidated_runs/18035_18036_18037_18038_18039_18040_18041_18042_18044_18045 /home/<USER>/opus4_r0_processed --overwrite

Expected input structure:
input_path/
├── workspace_sphinx-doc__sphinx-9367/
│   ├── predictions.json
│   ├── agent_log.txt
│   └── ...deep nested structure.../logs/run_evaluation/swe_work/Augment_Agent/sphinx-doc__sphinx-9367/
├── workspace_other-repo__issue-123/
│   ├── predictions.json
│   ├── agent_log.txt
│   └── ...deep nested structure.../logs/run_evaluation/swe_work/Augment_Agent/other-repo__issue-123/
└── ...

Expected output structure:
output_path/
├── predictions.json (merged from all workspace predictions.json files)
├── logs/
│   ├── sphinx-doc__sphinx-9367/
│   ├── other-repo__issue-123/
│   └── ...
└── trajs/
    ├── sphinx-doc__sphinx-9367.txt (copied from workspace_sphinx-doc__sphinx-9367/agent_log.txt)
    ├── other-repo__issue-123.txt (copied from workspace_other-repo__issue-123/agent_log.txt)
    └── ...
"""

import argparse
import json
import shutil
import sys
from pathlib import Path


def find_log_folders(workspace_path: Path) -> list[Path]:
    """
    Find all log folders matching the pattern:
    .../logs/run_evaluation/swe_work/Augment_Agent/<repo_name>/

    Args:
        workspace_path: Path to a workspace directory

    Returns:
        List of paths to log folders found
    """
    log_folders = []

    # Look for the specific nested structure
    logs_pattern = workspace_path.glob(
        "**/logs/run_evaluation/swe_work/Augment_Agent/*"
    )

    for log_path in logs_pattern:
        if log_path.is_dir():
            log_folders.append(log_path)

    return log_folders


def extract_repo_name(log_path: Path) -> str:
    """
    Extract the repository name from the log path.
    The repo name is the last component of the path.

    Args:
        log_path: Path to the log folder

    Returns:
        Repository name (e.g., 'sphinx-doc__sphinx-9367')
    """
    return log_path.name


def copy_log_folder(source_path: Path, dest_path: Path, repo_name: str) -> None:
    """
    Copy the log folder to the destination with the repo name as the folder name.

    Args:
        source_path: Source log folder path
        dest_path: Destination base path
        repo_name: Repository name to use as folder name
    """
    target_path = dest_path / repo_name

    if target_path.exists():
        print(f"Warning: Target path {target_path} already exists. Removing it.")
        shutil.rmtree(target_path)

    shutil.copytree(source_path, target_path)


def collect_predictions(workspace_dirs: list[Path]) -> list:
    """
    Collect all predictions.json files from workspace directories.

    Args:
        workspace_dirs: List of workspace directory paths

    Returns:
        List containing all predictions merged together
    """
    all_predictions = []
    predictions_found = 0

    for workspace_dir in workspace_dirs:
        predictions_file = workspace_dir / "predictions.json"

        if predictions_file.exists():
            try:
                with open(predictions_file, "r") as f:
                    predictions_data = json.load(f)

                # If it's a dictionary, merge it
                if isinstance(predictions_data, list):
                    all_predictions.extend(predictions_data)
                    predictions_found += 1
                else:
                    print(f"  Warning: {predictions_file} is not a list.")

            except (json.JSONDecodeError, IOError) as e:
                print(
                    f"  Warning: Could not read predictions.json from {workspace_dir.name}: {e}"
                )

    return all_predictions


def save_merged_predictions(
    predictions: list, output_path: Path, dry_run: bool
) -> None:
    """
    Save the merged predictions to a JSON file.

    Args:
        predictions: Dictionary containing all predictions
        output_path: Output directory path
        dry_run: Whether this is a dry run
    """
    if not predictions:
        print("No predictions found to save")
        return

    predictions_file = output_path / "predictions.json"

    if dry_run:
        print(
            f"[DRY RUN] Would save {len(predictions)} predictions to: {predictions_file}"
        )
    else:
        try:
            with open(predictions_file, "w") as f:
                json.dump(predictions, f, indent=2)
        except IOError as e:
            print(f"Error saving predictions file: {e}")


def extract_repo_name_from_workspace(workspace_dir: Path) -> str:
    """
    Extract the repository name from the workspace directory name.
    Removes the 'workspace_' prefix.

    Args:
        workspace_dir: Path to the workspace directory

    Returns:
        Repository name (e.g., 'sphinx-doc__sphinx-9367')
    """
    workspace_name = workspace_dir.name
    if workspace_name.startswith("workspace_"):
        return workspace_name[len("workspace_") :]
    return workspace_name


def collect_agent_logs(
    workspace_dirs: list[Path], output_path: Path, dry_run: bool
) -> int:
    """
    Collect all agent_log.txt files from workspace directories and copy them to trajs/ folder.

    Args:
        workspace_dirs: List of workspace directory paths
        output_path: Output directory path
        dry_run: Whether this is a dry run

    Returns:
        Number of agent_log.txt files found and processed
    """
    trajs_path = output_path / "trajs"
    agent_logs_found = 0

    # Create trajs directory if not in dry run mode
    if not dry_run:
        trajs_path.mkdir(parents=True, exist_ok=True)
    else:
        print(f"[DRY RUN] Would create trajs directory: {trajs_path}")

    for workspace_dir in workspace_dirs:
        agent_log_file = workspace_dir / "agent_log.txt"

        if agent_log_file.exists():
            repo_name = extract_repo_name_from_workspace(workspace_dir)
            target_file = trajs_path / f"{repo_name}.txt"

            if dry_run:
                print(f"  [DRY RUN] Would copy {agent_log_file} -> {target_file}")
            else:
                try:
                    shutil.copy2(agent_log_file, target_file)
                    print(f"  Copied {agent_log_file.name} -> {target_file}")
                except IOError as e:
                    print(
                        f"  Warning: Could not copy agent_log.txt from {workspace_dir.name}: {e}"
                    )
                    continue

            agent_logs_found += 1
        else:
            print(f"  No agent_log.txt found in {workspace_dir.name}")

    return agent_logs_found


def main():
    parser = argparse.ArgumentParser(
        description="Gather logs from nested workspace directories, merge predictions, and collect agent logs",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )
    parser.add_argument(
        "input_path", type=Path, help="Input directory containing workspace folders"
    )
    parser.add_argument(
        "output_path",
        type=Path,
        help="Output directory where log folders will be extracted",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually copying files",
    )
    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="Remove existing output directory if it exists (default: exit with error)",
    )

    args = parser.parse_args()

    input_path = args.input_path.resolve()
    output_path = args.output_path.resolve()

    # Validate input path
    if not input_path.exists():
        print(f"Error: Input path {input_path} does not exist")
        sys.exit(1)

    if not input_path.is_dir():
        print(f"Error: Input path {input_path} is not a directory")
        sys.exit(1)

    # Handle existing output directory
    if output_path.exists():
        if args.overwrite:
            if not args.dry_run:
                print(f"Removing existing output directory: {output_path}")
                shutil.rmtree(output_path)
            else:
                print(
                    f"[DRY RUN] Would remove existing output directory: {output_path}"
                )
        else:
            print(f"Error: Output directory {output_path} already exists")
            print("Use --overwrite flag to remove it automatically")
            sys.exit(1)

    # Create output directory and logs subdirectory
    logs_output_path = output_path / "logs"
    if not args.dry_run:
        logs_output_path.mkdir(parents=True, exist_ok=True)
        print(f"Created output directory: {output_path}")
        print(f"Created logs directory: {logs_output_path}")
    else:
        print(f"[DRY RUN] Would create output directory: {output_path}")
        print(f"[DRY RUN] Would create logs directory: {logs_output_path}")

    # Find all workspace directories
    workspace_dirs = [
        d
        for d in input_path.iterdir()
        if d.is_dir() and d.name.startswith("workspace_")
    ]

    if not workspace_dirs:
        print(f"Warning: No workspace directories found in {input_path}")
        return

    print(f"Found {len(workspace_dirs)} workspace directories")

    # Collect predictions.json files
    print("\nCollecting predictions.json files...")
    all_predictions = collect_predictions(workspace_dirs)

    # Collect agent_log.txt files
    print("\nCollecting agent_log.txt files...")
    agent_logs_found = collect_agent_logs(workspace_dirs, output_path, args.dry_run)

    total_logs_found = 0

    # Process each workspace directory
    for workspace_dir in workspace_dirs:
        # Find log folders in this workspace
        log_folders = find_log_folders(workspace_dir)

        if not log_folders:
            print(f"  No log folders found in {workspace_dir.name}")
            continue

        # Copy each log folder
        for log_folder in log_folders:
            repo_name = extract_repo_name(log_folder)

            if not args.dry_run:
                copy_log_folder(log_folder, logs_output_path, repo_name)
            else:
                target_path = logs_output_path / repo_name
                print(f"    [DRY RUN] Would copy {log_folder} -> {target_path}")

            total_logs_found += 1

    # Save merged predictions
    print("\nSaving merged predictions...")
    save_merged_predictions(all_predictions, output_path, args.dry_run)

    print("\nSummary:")
    print(f"  Processed {len(workspace_dirs)} workspace directories")
    print(f"  Found {total_logs_found} log folders")
    print(f"  Merged {len(all_predictions)} predictions")
    print(f"  Collected {agent_logs_found} agent_log.txt files")

    if args.dry_run:
        print("  [DRY RUN] No files were actually copied")
    else:
        print(f"  Extracted logs to: {logs_output_path}")
        print(f"  Saved predictions to: {output_path / 'predictions.json'}")
        print(f"  Saved agent logs to: {output_path / 'trajs'}")


if __name__ == "__main__":
    main()

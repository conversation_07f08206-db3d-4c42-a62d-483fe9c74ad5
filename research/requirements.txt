# Research-only requirements.
#
# You can find production requirements in //tools/python_deps/requirements.txt.
# NOTE: certain packages (noted below) come from our internal pypi server. You can access it with:
#  --extra-index-url https://us-central1-python.pkg.dev/system-services-dev/pypi-public/simple
#
# Conventions for this file:
# 1. PLEASE KEEP THE REQUIREMENTS SORTED!
# 2. Where possible please add a version specifier.
# 3. Recommended to add inline-comments describing why a package is useful.
#

# Combined requirements for all augment research code.
absl-py==2.0.0       # Needed for some pytests
accelerate==1.3.*
anthropic==0.47.0
anthropic[vertex]==0.47.0
attrdict3
autograd==1.6.2 # autograd used for can_it_edit benchmark
autopep8==1.5.6
best_download
bitsandbytes
black==23.12.1
blobfile==2.1.1
boto3==1.36.*
clang-format==13.0.1
click>=7.1
colorama==0.4.*
compress_pickle
cuda-python==12.6.0 # 12.8.0
cupy-cuda11x
dataclasses-json>=0.6.1 # 0.6.7
datasets==3.0.2
deepdiff==8.1.1
determined==0.36.*
docker-pycreds==0.4.0
docker==7.1.0
editdistance==0.6.* # 0.8.1
einops==0.6.1 # 0.8.0
evaluate>=0.3.0
fairscale==0.4.13  # LLAMA2 models need this
faiss-cpu==1.8.*   # 1.10.0 Required by megatron/memorize/memory_snapshot.py
fast-diff-match-patch==2.1.*
fast-diff-match-patch>=2.0.1
faust-cchardet==2.1.19
fireworks-ai==0.15.*
flask
ftfy==6.0.1
gcsfs==2024.9.*  # For CoreWeave access to GCP buckets
gguf>=0.1.0 # Required by llama.cpp
gojsonnet==0.20.*
google-cloud-aiplatform  # Required for Gemini api
google-cloud-aiplatform[tokenization]>=1.60.0 # For Gemini api
google-cloud-bigquery==3.29.*   # Required to access request insight data.
google-cloud-storage   # Required to access request insight data.
google-cloud-pubsub  # Required for replay utils to work
google-cloud-logging  # Required to access logging data.
google-genai==1.12.*
google==3.0.*  # Required by GoogleSearchTool in agent_qa
googlesearch-python==1.2.*  # Required by WebSearchTool in agent_qa
gpustat==1.1.*
gql>=3.4.0  # Required for Linear API GraphQL client
gradio>=4.16.0
html2text==2024.2.26  # Required for WebPageFetcherTool HTML to Markdown conversion
html5lib==1.1.0
huggingface-hub==0.30.*
humanfriendly==10.0
immutabledict   # for base.component_registry
immutabledict==4.1.0
intervaltree>=3.1.0
ipykernel==6.22.0
ipywidgets  # for building notebook widgets
isort
jinja2
jsonlines==2.0.0
jsonref
jupyter
jupyterlab
jwt==1.3.1
kubernetes==26.1.* # 32.0.0
launchdarkly-server-sdk==9.9.*
line_profiler
lm_dataformat==0.0.20   # NOTE(arun): This is used to process data from lm_eval in data/, but shouldn't be used much.
lru-dict==1.2.0  # Matches what we use in production.
markdown==3.5 # 3.7
markdown2
markdownify==0.13.1  # Required by WebPageGetterTool for converting HTML to markdown
matplotlib
mcp==1.2.*
mistralai==0.0.8
mock==4.0.3
more_itertools
natsort
nbconvert
nbformat==5.10.*
ninja==********
nltk
notion-client==2.2.1  # Required by NotionPageExporter in agent_qa
notion2md==2.8.3  # Required by NotionPageExporter in agent_qa
numexpr==2.8.4
numpy==1.26.* # 2.2.2
openai==1.59.* # 1.61.0
pandas==2.2.*
parameterized==0.9.0
pebble~=5.0.3
pexpect
pip==25.* # transformer-engine
plotly==5.23.0
pluggy==1.3.0  # AU-1066
pre-commit~=2.17.0
prometheus-client==0.21.1
protobuf==4.21.10   # Should track what we use in production.
psycopg2-binary==2.9.6     # for accessing metastore
pudb # A better debug library suggested by Yuri
py==1.11.0  # AU-1066
pyarrow
pyarrow-hotfix
pybind11==2.11.1    # > 2.10.1 required for python3.11
pycountry==20.7.3
pydantic==2.10.5
pygit2==1.12.1
pyglove>=0.4.4.dev20230926
pylint
pylint-protobuf
pylint-pytest
pympler==1.*
pypandoc-binary==1.13
pyright==1.1.374
pyrsistent>=0.20.0
pyspark~=3.4.1
pytablewriter==0.58.0
pytest-asyncio==0.23.5  # Required for testing async code in agent_qa
pytest-cov==4.1.0  # AU-1066
pytest-forked==1.6.0  # AU-1066
pytest-testmon==2.1.*
pytest-xdist==3.5.0  # AU-1066
pytest==7.4.3  # AU-1066
python-dateutil
python-magic
pyyaml==6.*
ray[data,default]==2.44.*
regex
requests>=2.31.0 # 2.32.3
requests_toolbelt==1.0.0
rouge_score
ruff==0.4.2
scikit-learn==1.6.*
scrapy==2.12.*  # Required by WebsiteSpider in agent_qa
seaborn>=0.12.2
sentencepiece==0.2.*
setuptools==78.1.1 # https://avd.aquasec.com/nvd/2025/cve-2025-47273/ AU-10286
shortuuid==1.*
six
slack-bolt==1.18.1
snakeviz
sqlitedict==1.6.0
structlog==23.1.* # 25.1.0
supabase==2.15.*
tenacity==8.2.* # 9.0.0
termcolor==2.3.0  # 2.4.0+ breaks type checking
tiktoken==0.7.0
tokenizers==0.21.*
torch==2.5.1 # 2.6.0
torchaudio==2.5.1
torchmetrics==1.6.0
torchvision==0.20.1
tqdm-multiprocess==0.0.11
tqdm==4.66.6
transformers==4.51.*
tree-sitter-languages~=1.10.2 # NOTE(2024-07-08, arun): tree-sitter 0.22 introduces a
tree-sitter~=0.21.3           # breaking change that is incompatible with tree-sitter-languages.
unidiff==0.7.5
urllib3==2.3.*
wandb==0.13.* # 0.19.6
wheel==0.45.*  # deepspeed build dep
xgboost==2.1.0
z3-solver==4.12 # used for can_it_edit benchmark
zstandard==0.15.* # 0.23.0
# NOTE: PLEASE KEEP THE REQUIREMENTS SORTED!


################################################################################
#
# Requirements built and served from our internal index. Most of these are
# the gpu-specific requirements.
#
# https://us-central1-python.pkg.dev/system-services-dev/pypi-public/simple
#

# apex==0.1 NOTE(mattm): Figure out how to handle gpu vs cpu build
flash-attn==2.5.8+augment.pytorch23
flashattn-hopper==3.0.0b1+c1d146c
megablocks==0.5.1 # 0.7.0
mpi4py==3.1.5 # 4.0.2
transformer-engine==0.13.0+augment.cu124.torch25.9b42134
triton==3.1.* # 3.2.0 NOTE(mattm): No longer custom build?

################################################################################
#
# Requirements built from source
#

codellama@git+https://github.com/facebookresearch/codellama.git@e66609cfbd73503ef25e597fd82c59084836155d # Required by the database behind prototype extension
deepspeed@git+https://github.com/augmentcode/DeeperSpeed.git@40617c259ff7aacacdd2f994006d1a29c97628f5
tensordict@git+https://github.com/pytorch/tensordict.git@646683c65a9a235e3639311598ee529fe53d1442  # Fixes the multigpu problem at https://github.com/augmentcode/augment/pull/17131

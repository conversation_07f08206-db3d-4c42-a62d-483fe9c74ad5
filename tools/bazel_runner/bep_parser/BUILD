load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_proto_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:go.bzl", "go_proto_library")

py_library(
    name = "bep_parser",
    srcs = [
        "bep_parser.py",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":test_summary_py_proto",
    ],
)

py_binary(
    name = "bep_parser_util",
    srcs = [
        "bep_parser_util.py",
    ],
    deps = [
        ":bep_parser",
        "//base/logging:console_logging",
    ],
)

pytest_test(
    name = "bep_parser_test",
    srcs = ["bep_parser_test.py"],
    data = glob(["test_data/*"]),
    deps = [":bep_parser"],
)

py_library(
    name = "test_case_parser",
    srcs = [
        "test_case_parser.py",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":test_summary_py_proto",
        requirement("defusedxml"),
    ],
)

pytest_test(
    name = "test_case_parser_test",
    srcs = ["test_case_parser_test.py"],
    data = glob(["test_data/*"]),
    deps = [":test_case_parser"],
)

proto_library(
    name = "test_summary_proto",
    srcs = [
        "test_summary.proto",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//third_party/proto/bazel_build:build_event_stream_proto",
        "@protobuf//:duration_proto",
    ],
)

py_proto_library(
    name = "test_summary_py_proto",
    protos = ["test_summary_proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//third_party/proto/bazel_build:build_event_stream_py_proto",
        requirement("delimited-protobuf"),
    ],
)

go_proto_library(
    name = "test_summary_go_proto",
    proto = "test_summary_proto",
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//third_party/proto/bazel_build:build_event_stream_go_proto",
    ],
)

�

  Z 
�

original�
	canonical	�
tool� b r "

//clients/...J *�

$a37a5057-d9a4-4823-bf02-44840552ab36ԧ���06.1.0"�	--verbose_failures --experimental_ui_max_stdouterr_bytes=4194304 --force_pic --experimental_cc_implementation_deps --java_runtime_version=remotejdk_11 --action_env='DOCKER_HOST=void:8888' --cxxopt='-std=c++17' --cxxopt=-Wall --copt=-fPIC --reuse_sandbox_directories --experimental_guard_against_concurrent_changes --workspace_status_command=./tools/bzl/workspace-status --incompatible_strict_action_env --remote_timeout=600s --incompatible_enable_cc_toolchain_resolution --attempt_to_print_relative_paths --action_env=DOCKER_HOST --incompatible_exclusive_test_sandboxed --build_tests_only --test_output=errors --test_summary=detailed --test_tag_filters=-postmerge-test --remote_cache=grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092 --host_platform=//tools/bzl:aws_ubuntu2004_linux_x86 --platforms=//tools/bzl:aws_ubuntu2004_linux_x86 --action_env='TORCH_VERSION=11.7' --action_env='CUDA_PATH=/usr/local/cuda-11.7' --action_env='CUDA_HOME=/usr/local/cuda-11.7' --action_env='LD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal' --build_event_binary_file=/home/<USER>/src/augment/bep.pb*test2/home/<USER>/src/augment:/home/<USER>/src/augment@ɚ?JեҠ�����K
Z b�K
test
--startup_time=6
--command_wait_time=0
--extract_data_time=0
b--binary_path=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel
--rc_source=client
&--default_override=0:common=--isatty=1
2--default_override=0:common=--terminal_columns=238
---rc_source=/home/<USER>/src/augment/.bazelrc
)--default_override=1:build:ci=--curses=no
7--default_override=1:build:ci=--noshow_loading_progress
2--default_override=1:build:ci=--test_summary=short
/--default_override=1:build:ci=--show_timestamps
Q--default_override=1:build:ci=--aspects=@rules_rust//rust:defs.bzl%rustfmt_aspect
=--default_override=1:build:ci=--output_groups=+rustfmt_checks
1--default_override=1:run=--action_env=DOCKER_HOST
---default_override=1:build:deploy=--curses=no
;--default_override=1:build:deploy=--noshow_loading_progress
3--default_override=1:build:deploy=--show_timestamps
)--default_override=1:build:deploy=--stamp
2--default_override=1:test=--action_env=DOCKER_HOST
A--default_override=1:test=--incompatible_exclusive_test_sandboxed
,--default_override=1:test=--build_tests_only
.--default_override=1:test=--test_output=errors
1--default_override=1:test=--test_summary=detailed
<--default_override=1:test=--test_tag_filters=-postmerge-test
---default_override=1:build=--verbose_failures
H--default_override=1:build=--experimental_ui_max_stdouterr_bytes=4194304
&--default_override=1:build=--force_pic
@--default_override=1:build=--experimental_cc_implementation_deps
V--default_override=1:build=--@rules_cuda//cuda:archs=compute_80:compute_80,sm_80,sm_86
5--default_override=1:build=--@rules_cuda//cuda:enable
q--default_override=1:build=--@rules_cuda//cuda:copts=--use_fast_math,-std=c++17,-forward-unknown-to-host-compiler
X--default_override=1:build=--@rules_cuda//cuda:runtime=@local_cuda//:cuda_runtime_static
>--default_override=1:build=--java_runtime_version=remotejdk_11
=--default_override=1:build=--action_env=DOCKER_HOST=void:8888
.--default_override=1:build=--cxxopt=-std=c++17
)--default_override=1:build=--cxxopt=-Wall
'--default_override=1:build=--copt=-fPIC
C--default_override=1:build=--experimental_reuse_sandbox_directories
J--default_override=1:build=--experimental_guard_against_concurrent_changes
R--default_override=1:build=--workspace_status_command=./tools/bzl/workspace-status
;--default_override=1:build=--incompatible_strict_action_env
0--default_override=1:build=--remote_timeout=600s
M--default_override=1:build=--@aspect_rules_format//format:swift_enabled=false
P--default_override=1:build=--@aspect_rules_format//format:prettier_enabled=false
H--default_override=1:build=--incompatible_enable_cc_toolchain_resolution
<--default_override=1:build=--attempt_to_print_relative_paths
2--client_env=BASH_ENV=/usr/share/modules/init/bash
�--client_env=BASH_FUNC__module_raw%%=() {  unset _mlshdbg;
 if [ "${MODULES_SILENT_SHELL_DEBUG:-0}" = '1' ]; then
 case "$-" in 
 *v*x*)
 set +vx;
 _mlshdbg='vx'
 ;;
 *v*)
 set +v;
 _mlshdbg='v'
 ;;
 *x*)
 set +x;
 _mlshdbg='x'
 ;;
 *)
 _mlshdbg=''
 ;;
 esac;
 fi;
 unset _mlre _mlIFS;
 if [ -n "${IFS+x}" ]; then
 _mlIFS=$IFS;
 fi;
 IFS=' ';
 for _mlv in ${MODULES_RUN_QUARANTINE:-};
 do
 if [ "${_mlv}" = "${_mlv##*[!A-Za-z0-9_]}" -a "${_mlv}" = "${_mlv#[0-9]}" ]; then
 if [ -n "`eval 'echo ${'$_mlv'+x}'`" ]; then
 _mlre="${_mlre:-}${_mlv}_modquar='`eval 'echo ${'$_mlv'}'`' ";
 fi;
 _mlrv="MODULES_RUNENV_${_mlv}";
 _mlre="${_mlre:-}${_mlv}='`eval 'echo ${'$_mlrv':-}'`' ";
 fi;
 done;
 if [ -n "${_mlre:-}" ]; then
 eval `eval ${_mlre}/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash '"$@"'`;
 else
 eval `/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash "$@"`;
 fi;
 _mlstatus=$?;
 if [ -n "${_mlIFS+x}" ]; then
 IFS=$_mlIFS;
 else
 unset IFS;
 fi;
 unset _mlre _mlv _mlrv _mlIFS;
 if [ -n "${_mlshdbg:-}" ]; then
 set -$_mlshdbg;
 fi;
 unset _mlshdbg;
 return $_mlstatus
}
=--client_env=BASH_FUNC_module%%=() {  _module_raw "$@" 2>&1
}
�--client_env=BASH_FUNC_switchml%%=() {  typeset swfound=1;
 if [ "${MODULES_USE_COMPAT_VERSION:-0}" = '1' ]; then
 typeset swname='main';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd.tcl ]; then
 typeset swfound=0;
 unset MODULES_USE_COMPAT_VERSION;
 fi;
 else
 typeset swname='compatibility';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd-compat ]; then
 typeset swfound=0;
 MODULES_USE_COMPAT_VERSION=1;
 export MODULES_USE_COMPAT_VERSION;
 fi;
 fi;
 if [ $swfound -eq 0 ]; then
 echo "Switching to Modules $swname version";
 source /usr/share/modules/init/bash;
 else
 echo "Cannot switch to Modules $swname version, command not found";
 return 1;
 fi
}
#--client_env=CONDA_DEFAULT_ENV=base
+--client_env=CONDA_EXE=/opt/conda/bin/conda
$--client_env=CONDA_PREFIX=/opt/conda
*--client_env=CONDA_PROMPT_MODIFIER=(base) 
3--client_env=CONDA_PYTHON_EXE=/opt/conda/bin/python
--client_env=CONDA_SHLVL=1
,--client_env=CUDA_HOME=/usr/local/cuda-11.3/
B--client_env=DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
3--client_env=ENV=/usr/share/modules/init/profile.sh
--client_env=HOME=/home/<USER>
--client_env=LANG=C.UTF-8
--client_env=LC_TERMINAL=iTerm2
'--client_env=LC_TERMINAL_VERSION=3.4.19
�--client_env=LD_LIBRARY_PATH=/opt/amazon/efa/lib:/opt/amazon/openmpi/lib:/usr/local/cuda/efa/lib:/usr/local/cuda/lib:/usr/local/cuda:/usr/local/cuda/lib64:/usr/local/cuda/extras/CUPTI/lib64:/usr/local/cuda/targets/x86_64-linux/lib:/usr/local/lib:/usr/lib:
.--client_env=LESSCLOSE=/usr/bin/lesspipe %s %s
,--client_env=LESSOPEN=| /usr/bin/lesspipe %s
1--client_env=LE_WORKING_DIR=/home/<USER>/.acme.sh
--client_env=LOADEDMODULES=
--client_env=LOGNAME=ubuntu
�--client_env=LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
I--client_env=MANPATH=/home/<USER>/.nvm/versions/node/v18.12.1/share/man::
�--client_env=MODULEPATH=/etc/environment-modules/modules:/usr/share/modules/versions:/usr/share/modules/$MODULE_VERSION/modulefiles:/usr/share/modules/modulefiles
�--client_env=MODULEPATH_modshare=/etc/environment-modules/modules:1:/usr/share/modules/$MODULE_VERSION/modulefiles:1:/usr/share/modules/modulefiles:1:/usr/share/modules/versions:1
+--client_env=MODULESHOME=/usr/share/modules
@--client_env=MODULES_CMD=/usr/lib/x86_64-linux-gnu/modulecmd.tcl
--client_env=MOTD_SHOWN=pam
A--client_env=NVM_BIN=/home/<USER>/.nvm/versions/node/v18.12.1/bin
--client_env=NVM_CD_FLAGS=
&--client_env=NVM_DIR=/home/<USER>/.nvm
J--client_env=NVM_INC=/home/<USER>/.nvm/versions/node/v18.12.1/include/node
 --client_env=OLDPWD=/home/<USER>
�--client_env=PATH=/home/<USER>/src/augment/tools:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/opt/conda/bin:/opt/conda/condabin:/home/<USER>/.cargo/bin:/home/<USER>/.nvm/versions/node/v18.12.1/bin:/opt/amazon/openmpi/bin:/opt/amazon/efa/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/usr/local/go/bin:/home/<USER>/go/bin
)--client_env=PWD=/home/<USER>/src/augment
--client_env=SHELL=/bin/bash
--client_env=SHLVL=2
,--client_env=SSH_CLIENT=************ 8389 22
<--client_env=SSH_CONNECTION=************ 8389 *********** 22
--client_env=SSH_TTY=/dev/pts/4
 --client_env=TERM=xterm-256color
--client_env=USER=ubuntu
M--client_env=XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop
+--client_env=XDG_RUNTIME_DIR=/run/user/1000
#--client_env=XDG_SESSION_CLASS=user
--client_env=XDG_SESSION_ID=130
!--client_env=XDG_SESSION_TYPE=tty
--client_env=_CE_CONDA=
--client_env=_CE_M=
'--client_env=BAZELISK_SKIP_WRAPPER=true
l--client_env=BAZEL_REAL=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel
%--client_cwd=/home/<USER>/src/augment
k--remote_cache=grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092
4--host_platform=//tools/bzl:aws_ubuntu2004_linux_x86
0--platforms=//tools/bzl:aws_ubuntu2004_linux_x86
--action_env=TORCH_VERSION=11.7
+--action_env=CUDA_PATH=/usr/local/cuda-11.7
+--action_env=CUDA_HOME=/usr/local/cuda-11.7
[--action_env=LD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal
9--build_event_binary_file=/home/<USER>/src/augment/bep.pb

//clients/...�
b j�
--max_idle_secs=10800
--noshutdown_on_low_sys_mem
--connect_timeout_secs=30
:--output_user_root=/home/<USER>/.cache/bazel/_bazel_ubuntu
V--output_base=/home/<USER>/.cache/bazel/_bazel_ubuntu/e07873bf2c524a5f3a550b9b08c46b59
u--failure_detail_out=/home/<USER>/.cache/bazel/_bazel_ubuntu/e07873bf2c524a5f3a550b9b08c46b59/failure_detail.rawproto
--expand_configs_in_place
--idle_server_tasks
--write_command_log
--nowatchfs
--nofatal_event_bus_exceptions
--nowindows_enable_symlinks
--noclient_debug--verbose_failures---experimental_ui_max_stdouterr_bytes=4194304--force_pic%--experimental_cc_implementation_deps#--java_runtime_version=remotejdk_11"--action_env=DOCKER_HOST=void:8888--cxxopt=-std=c++17--cxxopt=-Wall--copt=-fPIC--reuse_sandbox_directories/--experimental_guard_against_concurrent_changes7--workspace_status_command=./tools/bzl/workspace-status --incompatible_strict_action_env--remote_timeout=600s---incompatible_enable_cc_toolchain_resolution!--attempt_to_print_relative_paths--action_env=DOCKER_HOST'--incompatible_exclusive_test_sandboxed--build_tests_only--test_output=errors--test_summary=detailed"--test_tag_filters=-postmerge-testk--remote_cache=grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:90924--host_platform=//tools/bzl:aws_ubuntu2004_linux_x860--platforms=//tools/bzl:aws_ubuntu2004_linux_x86--action_env=TORCH_VERSION=11.7+--action_env=CUDA_PATH=/usr/local/cuda-11.7+--action_env=CUDA_HOME=/usr/local/cuda-11.7[--action_env=LD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal9--build_event_binary_file=/home/<USER>/src/augment/bep.pb"k--remote_cache=grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092"4--host_platform=//tools/bzl:aws_ubuntu2004_linux_x86"0--platforms=//tools/bzl:aws_ubuntu2004_linux_x86"--action_env=TORCH_VERSION=11.7"+--action_env=CUDA_PATH=/usr/local/cuda-11.7"+--action_env=CUDA_HOME=/usr/local/cuda-11.7"[--action_env=LD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal"9--build_event_binary_file=/home/<USER>/src/augment/bep.pb* 
� � ��

�

original���
original

executable
bazel
startup options 
command
test��
command options��
-
--rc_source=client	rc_sourceclient"*
U
&--default_override=0:common=--isatty=1default_override0:common=--isatty=1"*
m
2--default_override=0:common=--terminal_columns=238default_override0:common=--terminal_columns=238"*
c
---rc_source=/home/<USER>/src/augment/.bazelrc	rc_source!/home/<USER>/src/augment/.bazelrc"*
[
)--default_override=1:build:ci=--curses=nodefault_override1:build:ci=--curses=no"*
w
7--default_override=1:build:ci=--noshow_loading_progressdefault_override$1:build:ci=--noshow_loading_progress"*
m
2--default_override=1:build:ci=--test_summary=shortdefault_override1:build:ci=--test_summary=short"*
g
/--default_override=1:build:ci=--show_timestampsdefault_override1:build:ci=--show_timestamps"*
�
Q--default_override=1:build:ci=--aspects=@rules_rust//rust:defs.bzl%rustfmt_aspectdefault_override>1:build:ci=--aspects=@rules_rust//rust:defs.bzl%rustfmt_aspect"*
�
=--default_override=1:build:ci=--output_groups=+rustfmt_checksdefault_override*1:build:ci=--output_groups=+rustfmt_checks"*
k
1--default_override=1:run=--action_env=DOCKER_HOSTdefault_override1:run=--action_env=DOCKER_HOST"*
c
---default_override=1:build:deploy=--curses=nodefault_override1:build:deploy=--curses=no"*

;--default_override=1:build:deploy=--noshow_loading_progressdefault_override(1:build:deploy=--noshow_loading_progress"*
o
3--default_override=1:build:deploy=--show_timestampsdefault_override 1:build:deploy=--show_timestamps"*
[
)--default_override=1:build:deploy=--stampdefault_override1:build:deploy=--stamp"*
m
2--default_override=1:test=--action_env=DOCKER_HOSTdefault_override1:test=--action_env=DOCKER_HOST"*
�
A--default_override=1:test=--incompatible_exclusive_test_sandboxeddefault_override.1:test=--incompatible_exclusive_test_sandboxed"*
a
,--default_override=1:test=--build_tests_onlydefault_override1:test=--build_tests_only"*
e
.--default_override=1:test=--test_output=errorsdefault_override1:test=--test_output=errors"*
k
1--default_override=1:test=--test_summary=detaileddefault_override1:test=--test_summary=detailed"*
�
<--default_override=1:test=--test_tag_filters=-postmerge-testdefault_override)1:test=--test_tag_filters=-postmerge-test"*
c
---default_override=1:build=--verbose_failuresdefault_override1:build=--verbose_failures"*
�
H--default_override=1:build=--experimental_ui_max_stdouterr_bytes=4194304default_override51:build=--experimental_ui_max_stdouterr_bytes=4194304"*
U
&--default_override=1:build=--force_picdefault_override1:build=--force_pic"*
�
@--default_override=1:build=--experimental_cc_implementation_depsdefault_override-1:build=--experimental_cc_implementation_deps"*
�
V--default_override=1:build=--@rules_cuda//cuda:archs=compute_80:compute_80,sm_80,sm_86default_overrideC1:build=--@rules_cuda//cuda:archs=compute_80:compute_80,sm_80,sm_86"*
s
5--default_override=1:build=--@rules_cuda//cuda:enabledefault_override"1:build=--@rules_cuda//cuda:enable"*
�
q--default_override=1:build=--@rules_cuda//cuda:copts=--use_fast_math,-std=c++17,-forward-unknown-to-host-compilerdefault_override^1:build=--@rules_cuda//cuda:copts=--use_fast_math,-std=c++17,-forward-unknown-to-host-compiler"*
�
X--default_override=1:build=--@rules_cuda//cuda:runtime=@local_cuda//:cuda_runtime_staticdefault_overrideE1:build=--@rules_cuda//cuda:runtime=@local_cuda//:cuda_runtime_static"*
�
>--default_override=1:build=--java_runtime_version=remotejdk_11default_override+1:build=--java_runtime_version=remotejdk_11"*
�
=--default_override=1:build=--action_env=DOCKER_HOST=void:8888default_override*1:build=--action_env=DOCKER_HOST=void:8888"*
e
.--default_override=1:build=--cxxopt=-std=c++17default_override1:build=--cxxopt=-std=c++17"*
[
)--default_override=1:build=--cxxopt=-Walldefault_override1:build=--cxxopt=-Wall"*
W
'--default_override=1:build=--copt=-fPICdefault_override1:build=--copt=-fPIC"*
�
C--default_override=1:build=--experimental_reuse_sandbox_directoriesdefault_override01:build=--experimental_reuse_sandbox_directories"*
�
J--default_override=1:build=--experimental_guard_against_concurrent_changesdefault_override71:build=--experimental_guard_against_concurrent_changes"*
�
R--default_override=1:build=--workspace_status_command=./tools/bzl/workspace-statusdefault_override?1:build=--workspace_status_command=./tools/bzl/workspace-status"*

;--default_override=1:build=--incompatible_strict_action_envdefault_override(1:build=--incompatible_strict_action_env"*
i
0--default_override=1:build=--remote_timeout=600sdefault_override1:build=--remote_timeout=600s"*
�
M--default_override=1:build=--@aspect_rules_format//format:swift_enabled=falsedefault_override:1:build=--@aspect_rules_format//format:swift_enabled=false"*
�
P--default_override=1:build=--@aspect_rules_format//format:prettier_enabled=falsedefault_override=1:build=--@aspect_rules_format//format:prettier_enabled=false"*
�
H--default_override=1:build=--incompatible_enable_cc_toolchain_resolutiondefault_override51:build=--incompatible_enable_cc_toolchain_resolution"*
�
<--default_override=1:build=--attempt_to_print_relative_pathsdefault_override)1:build=--attempt_to_print_relative_paths"*
*
--startup_time=6startup_time6"*
4
--command_wait_time=0command_wait_time0"*
4
--extract_data_time=0extract_data_time0"*
�
b--binary_path=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazelbinary_pathT/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel"*
m
2--client_env=BASH_ENV=/usr/share/modules/init/bash
client_env%BASH_ENV=/usr/share/modules/init/bash"*
�
�--client_env=BASH_FUNC__module_raw%%=() {  unset _mlshdbg;
 if [ "${MODULES_SILENT_SHELL_DEBUG:-0}" = '1' ]; then
 case "$-" in 
 *v*x*)
 set +vx;
 _mlshdbg='vx'
 ;;
 *v*)
 set +v;
 _mlshdbg='v'
 ;;
 *x*)
 set +x;
 _mlshdbg='x'
 ;;
 *)
 _mlshdbg=''
 ;;
 esac;
 fi;
 unset _mlre _mlIFS;
 if [ -n "${IFS+x}" ]; then
 _mlIFS=$IFS;
 fi;
 IFS=' ';
 for _mlv in ${MODULES_RUN_QUARANTINE:-};
 do
 if [ "${_mlv}" = "${_mlv##*[!A-Za-z0-9_]}" -a "${_mlv}" = "${_mlv#[0-9]}" ]; then
 if [ -n "`eval 'echo ${'$_mlv'+x}'`" ]; then
 _mlre="${_mlre:-}${_mlv}_modquar='`eval 'echo ${'$_mlv'}'`' ";
 fi;
 _mlrv="MODULES_RUNENV_${_mlv}";
 _mlre="${_mlre:-}${_mlv}='`eval 'echo ${'$_mlrv':-}'`' ";
 fi;
 done;
 if [ -n "${_mlre:-}" ]; then
 eval `eval ${_mlre}/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash '"$@"'`;
 else
 eval `/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash "$@"`;
 fi;
 _mlstatus=$?;
 if [ -n "${_mlIFS+x}" ]; then
 IFS=$_mlIFS;
 else
 unset IFS;
 fi;
 unset _mlre _mlv _mlrv _mlIFS;
 if [ -n "${_mlshdbg:-}" ]; then
 set -$_mlshdbg;
 fi;
 unset _mlshdbg;
 return $_mlstatus
}
client_env�BASH_FUNC__module_raw%%=() {  unset _mlshdbg;
 if [ "${MODULES_SILENT_SHELL_DEBUG:-0}" = '1' ]; then
 case "$-" in 
 *v*x*)
 set +vx;
 _mlshdbg='vx'
 ;;
 *v*)
 set +v;
 _mlshdbg='v'
 ;;
 *x*)
 set +x;
 _mlshdbg='x'
 ;;
 *)
 _mlshdbg=''
 ;;
 esac;
 fi;
 unset _mlre _mlIFS;
 if [ -n "${IFS+x}" ]; then
 _mlIFS=$IFS;
 fi;
 IFS=' ';
 for _mlv in ${MODULES_RUN_QUARANTINE:-};
 do
 if [ "${_mlv}" = "${_mlv##*[!A-Za-z0-9_]}" -a "${_mlv}" = "${_mlv#[0-9]}" ]; then
 if [ -n "`eval 'echo ${'$_mlv'+x}'`" ]; then
 _mlre="${_mlre:-}${_mlv}_modquar='`eval 'echo ${'$_mlv'}'`' ";
 fi;
 _mlrv="MODULES_RUNENV_${_mlv}";
 _mlre="${_mlre:-}${_mlv}='`eval 'echo ${'$_mlrv':-}'`' ";
 fi;
 done;
 if [ -n "${_mlre:-}" ]; then
 eval `eval ${_mlre}/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash '"$@"'`;
 else
 eval `/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash "$@"`;
 fi;
 _mlstatus=$?;
 if [ -n "${_mlIFS+x}" ]; then
 IFS=$_mlIFS;
 else
 unset IFS;
 fi;
 unset _mlre _mlv _mlrv _mlIFS;
 if [ -n "${_mlshdbg:-}" ]; then
 set -$_mlshdbg;
 fi;
 unset _mlshdbg;
 return $_mlstatus
}"*
�
=--client_env=BASH_FUNC_module%%=() {  _module_raw "$@" 2>&1
}
client_env0BASH_FUNC_module%%=() {  _module_raw "$@" 2>&1
}"*
�

�--client_env=BASH_FUNC_switchml%%=() {  typeset swfound=1;
 if [ "${MODULES_USE_COMPAT_VERSION:-0}" = '1' ]; then
 typeset swname='main';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd.tcl ]; then
 typeset swfound=0;
 unset MODULES_USE_COMPAT_VERSION;
 fi;
 else
 typeset swname='compatibility';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd-compat ]; then
 typeset swfound=0;
 MODULES_USE_COMPAT_VERSION=1;
 export MODULES_USE_COMPAT_VERSION;
 fi;
 fi;
 if [ $swfound -eq 0 ]; then
 echo "Switching to Modules $swname version";
 source /usr/share/modules/init/bash;
 else
 echo "Cannot switch to Modules $swname version, command not found";
 return 1;
 fi
}
client_env�BASH_FUNC_switchml%%=() {  typeset swfound=1;
 if [ "${MODULES_USE_COMPAT_VERSION:-0}" = '1' ]; then
 typeset swname='main';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd.tcl ]; then
 typeset swfound=0;
 unset MODULES_USE_COMPAT_VERSION;
 fi;
 else
 typeset swname='compatibility';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd-compat ]; then
 typeset swfound=0;
 MODULES_USE_COMPAT_VERSION=1;
 export MODULES_USE_COMPAT_VERSION;
 fi;
 fi;
 if [ $swfound -eq 0 ]; then
 echo "Switching to Modules $swname version";
 source /usr/share/modules/init/bash;
 else
 echo "Cannot switch to Modules $swname version, command not found";
 return 1;
 fi
}"*
O
#--client_env=CONDA_DEFAULT_ENV=base
client_envCONDA_DEFAULT_ENV=base"*
_
+--client_env=CONDA_EXE=/opt/conda/bin/conda
client_envCONDA_EXE=/opt/conda/bin/conda"*
Q
$--client_env=CONDA_PREFIX=/opt/conda
client_envCONDA_PREFIX=/opt/conda"*
]
*--client_env=CONDA_PROMPT_MODIFIER=(base) 
client_envCONDA_PROMPT_MODIFIER=(base) "*
o
3--client_env=CONDA_PYTHON_EXE=/opt/conda/bin/python
client_env&CONDA_PYTHON_EXE=/opt/conda/bin/python"*
=
--client_env=CONDA_SHLVL=1
client_env
CONDA_SHLVL=1"*
a
,--client_env=CUDA_HOME=/usr/local/cuda-11.3/
client_envCUDA_HOME=/usr/local/cuda-11.3/"*
�
B--client_env=DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
client_env5DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus"*
o
3--client_env=ENV=/usr/share/modules/init/profile.sh
client_env&ENV=/usr/share/modules/init/profile.sh"*
E
--client_env=HOME=/home/<USER>
client_envHOME=/home/<USER>"*
;
--client_env=LANG=C.UTF-8
client_envLANG=C.UTF-8"*
G
--client_env=LC_TERMINAL=iTerm2
client_envLC_TERMINAL=iTerm2"*
W
'--client_env=LC_TERMINAL_VERSION=3.4.19
client_envLC_TERMINAL_VERSION=3.4.19"*
�
�--client_env=LD_LIBRARY_PATH=/opt/amazon/efa/lib:/opt/amazon/openmpi/lib:/usr/local/cuda/efa/lib:/usr/local/cuda/lib:/usr/local/cuda:/usr/local/cuda/lib64:/usr/local/cuda/extras/CUPTI/lib64:/usr/local/cuda/targets/x86_64-linux/lib:/usr/local/lib:/usr/lib:
client_env�LD_LIBRARY_PATH=/opt/amazon/efa/lib:/opt/amazon/openmpi/lib:/usr/local/cuda/efa/lib:/usr/local/cuda/lib:/usr/local/cuda:/usr/local/cuda/lib64:/usr/local/cuda/extras/CUPTI/lib64:/usr/local/cuda/targets/x86_64-linux/lib:/usr/local/lib:/usr/lib:"*
e
.--client_env=LESSCLOSE=/usr/bin/lesspipe %s %s
client_env!LESSCLOSE=/usr/bin/lesspipe %s %s"*
a
,--client_env=LESSOPEN=| /usr/bin/lesspipe %s
client_envLESSOPEN=| /usr/bin/lesspipe %s"*
k
1--client_env=LE_WORKING_DIR=/home/<USER>/.acme.sh
client_env$LE_WORKING_DIR=/home/<USER>/.acme.sh"*
?
--client_env=LOADEDMODULES=
client_envLOADEDMODULES="*
?
--client_env=LOGNAME=ubuntu
client_envLOGNAME=ubuntu"*
�
�--client_env=LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
client_env�LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:"*
�
I--client_env=MANPATH=/home/<USER>/.nvm/versions/node/v18.12.1/share/man::
client_env<MANPATH=/home/<USER>/.nvm/versions/node/v18.12.1/share/man::"*
�
�--client_env=MODULEPATH=/etc/environment-modules/modules:/usr/share/modules/versions:/usr/share/modules/$MODULE_VERSION/modulefiles:/usr/share/modules/modulefiles
client_env�MODULEPATH=/etc/environment-modules/modules:/usr/share/modules/versions:/usr/share/modules/$MODULE_VERSION/modulefiles:/usr/share/modules/modulefiles"*
�
�--client_env=MODULEPATH_modshare=/etc/environment-modules/modules:1:/usr/share/modules/$MODULE_VERSION/modulefiles:1:/usr/share/modules/modulefiles:1:/usr/share/modules/versions:1
client_env�MODULEPATH_modshare=/etc/environment-modules/modules:1:/usr/share/modules/$MODULE_VERSION/modulefiles:1:/usr/share/modules/modulefiles:1:/usr/share/modules/versions:1"*
_
+--client_env=MODULESHOME=/usr/share/modules
client_envMODULESHOME=/usr/share/modules"*
�
@--client_env=MODULES_CMD=/usr/lib/x86_64-linux-gnu/modulecmd.tcl
client_env3MODULES_CMD=/usr/lib/x86_64-linux-gnu/modulecmd.tcl"*
?
--client_env=MOTD_SHOWN=pam
client_envMOTD_SHOWN=pam"*
�
A--client_env=NVM_BIN=/home/<USER>/.nvm/versions/node/v18.12.1/bin
client_env4NVM_BIN=/home/<USER>/.nvm/versions/node/v18.12.1/bin"*
=
--client_env=NVM_CD_FLAGS=
client_env
NVM_CD_FLAGS="*
U
&--client_env=NVM_DIR=/home/<USER>/.nvm
client_envNVM_DIR=/home/<USER>/.nvm"*
�
J--client_env=NVM_INC=/home/<USER>/.nvm/versions/node/v18.12.1/include/node
client_env=NVM_INC=/home/<USER>/.nvm/versions/node/v18.12.1/include/node"*
I
 --client_env=OLDPWD=/home/<USER>
client_envOLDPWD=/home/<USER>"*
�
�--client_env=PATH=/home/<USER>/src/augment/tools:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/opt/conda/bin:/opt/conda/condabin:/home/<USER>/.cargo/bin:/home/<USER>/.nvm/versions/node/v18.12.1/bin:/opt/amazon/openmpi/bin:/opt/amazon/efa/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/usr/local/go/bin:/home/<USER>/go/bin
client_env�PATH=/home/<USER>/src/augment/tools:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/opt/conda/bin:/opt/conda/condabin:/home/<USER>/.cargo/bin:/home/<USER>/.nvm/versions/node/v18.12.1/bin:/opt/amazon/openmpi/bin:/opt/amazon/efa/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/usr/local/go/bin:/home/<USER>/go/bin"*
[
)--client_env=PWD=/home/<USER>/src/augment
client_envPWD=/home/<USER>/src/augment"*
A
--client_env=SHELL=/bin/bash
client_envSHELL=/bin/bash"*
1
--client_env=SHLVL=2
client_envSHLVL=2"*
a
,--client_env=SSH_CLIENT=************ 8389 22
client_envSSH_CLIENT=************ 8389 22"*
�
<--client_env=SSH_CONNECTION=************ 8389 *********** 22
client_env/SSH_CONNECTION=************ 8389 *********** 22"*
G
--client_env=SSH_TTY=/dev/pts/4
client_envSSH_TTY=/dev/pts/4"*
I
 --client_env=TERM=xterm-256color
client_envTERM=xterm-256color"*
9
--client_env=USER=ubuntu
client_envUSER=ubuntu"*
�
M--client_env=XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop
client_env@XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop"*
_
+--client_env=XDG_RUNTIME_DIR=/run/user/1000
client_envXDG_RUNTIME_DIR=/run/user/1000"*
O
#--client_env=XDG_SESSION_CLASS=user
client_envXDG_SESSION_CLASS=user"*
G
--client_env=XDG_SESSION_ID=130
client_envXDG_SESSION_ID=130"*
K
!--client_env=XDG_SESSION_TYPE=tty
client_envXDG_SESSION_TYPE=tty"*
7
--client_env=_CE_CONDA=
client_env
_CE_CONDA="*
/
--client_env=_CE_M=
client_env_CE_M="*
W
'--client_env=BAZELISK_SKIP_WRAPPER=true
client_envBAZELISK_SKIP_WRAPPER=true"*
�
l--client_env=BAZEL_REAL=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel
client_env_BAZEL_REAL=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel"*
S
%--client_cwd=/home/<USER>/src/augment
client_cwd/home/<USER>/src/augment"*
�
k--remote_cache=grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092remote_cache\grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092" 
p
4--host_platform=//tools/bzl:aws_ubuntu2004_linux_x86
host_platform$//tools/bzl:aws_ubuntu2004_linux_x86"
h
0--platforms=//tools/bzl:aws_ubuntu2004_linux_x86	platforms$//tools/bzl:aws_ubuntu2004_linux_x86"
D
--action_env=TORCH_VERSION=11.7
action_envTORCH_VERSION=11.7"
\
+--action_env=CUDA_PATH=/usr/local/cuda-11.7
action_envCUDA_PATH=/usr/local/cuda-11.7"
\
+--action_env=CUDA_HOME=/usr/local/cuda-11.7
action_envCUDA_HOME=/usr/local/cuda-11.7"
�
[--action_env=LD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal
action_envNLD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal"
x
9--build_event_binary_file=/home/<USER>/src/augment/bep.pbbuild_event_binary_file/home/<USER>/src/augment/bep.pb"
m
5--@aspect_rules_format//format:prettier_enabled=false-@aspect_rules_format//format:prettier_enabledfalse
g
2--@aspect_rules_format//format:swift_enabled=false*@aspect_rules_format//format:swift_enabledfalse
y
;--@rules_cuda//cuda:archs=compute_80:compute_80,sm_80,sm_86@rules_cuda//cuda:archs!compute_80:compute_80,sm_80,sm_86
�
Z--@rules_cuda//cuda:copts=[--use_fast_math, -std=c++17, -forward-unknown-to-host-compiler]@rules_cuda//cuda:copts@[--use_fast_math, -std=c++17, -forward-unknown-to-host-compiler]
}
=--@rules_cuda//cuda:runtime=@local_cuda//:cuda_runtime_static@rules_cuda//cuda:runtime!@local_cuda//:cuda_runtime_static
residual

//clients/...�
�
	canonical�Ѽ
	canonical

executable
bazel�
startup options�
1
--max_idle_secs=10800
max_idle_secs10800"

=
--noshutdown_on_low_sys_memshutdown_on_low_sys_mem0"

8
--connect_timeout_secs=30connect_timeout_secs30"
{
:--output_user_root=/home/<USER>/.cache/bazel/_bazel_ubuntuoutput_user_root'/home/<USER>/.cache/bazel/_bazel_ubuntu"
�
_--install_base=/home/<USER>/.cache/bazel/_bazel_ubuntu/install/f0478d8547df16461adab7eb1d945d57install_baseP/home/<USER>/.cache/bazel/_bazel_ubuntu/install/f0478d8547df16461adab7eb1d945d57"*
f
.--install_md5=f0478d8547df16461adab7eb1d945d57install_md5 f0478d8547df16461adab7eb1d945d57"*
�
V--output_base=/home/<USER>/.cache/bazel/_bazel_ubuntu/e07873bf2c524a5f3a550b9b08c46b59output_baseH/home/<USER>/.cache/bazel/_bazel_ubuntu/e07873bf2c524a5f3a550b9b08c46b59"
f
.--workspace_directory=/home/<USER>/src/augmentworkspace_directory/home/<USER>/src/augment"*
�
>--default_system_javabase=/usr/lib/jvm/java-11-amazon-correttodefault_system_javabase$/usr/lib/jvm/java-11-amazon-corretto"*
�
u--failure_detail_out=/home/<USER>/.cache/bazel/_bazel_ubuntu/e07873bf2c524a5f3a550b9b08c46b59/failure_detail.rawprotofailure_detail_out`/home/<USER>/.cache/bazel/_bazel_ubuntu/e07873bf2c524a5f3a550b9b08c46b59/failure_detail.rawproto"
=
--expand_configs_in_placeexpand_configs_in_place1"*
/
--idle_server_tasksidle_server_tasks1"	
/
--write_command_logwrite_command_log1"

--nowatchfswatchfs0" *
C
--nofatal_event_bus_exceptionsfatal_event_bus_exceptions0"

<
--nowindows_enable_symlinkswindows_enable_symlinks0"
/
--client_debug=falseclient_debugfalse"
3
--product_name=Bazelproduct_nameBazel"*
)
--option_sources=option_sources"*
2
--ignore_all_rc_filesignore_all_rc_files1"
command
test�
command optionsέ


--isatty=1isatty1" *
5
--terminal_columns=238terminal_columns238" *
,
--verbose_failuresverbose_failures1"
`
---experimental_ui_max_stdouterr_bytes=4194304#experimental_ui_max_stdouterr_bytes4194304"

--force_pic	force_pic1"
U
%--experimental_cc_implementation_deps#experimental_cc_implementation_deps1"* 
L
#--java_runtime_version=remotejdk_11java_runtime_versionremotejdk_11" 
J
"--action_env=DOCKER_HOST=void:8888
action_envDOCKER_HOST=void:8888"
-
--cxxopt=-std=c++17cxxopt
-std=c++17"

#
--cxxopt=-Wallcxxopt-Wall"


--copt=-fPICcopt-fPIC"

L
(--experimental_reuse_sandbox_directoriesreuse_sandbox_directories1"	
f
/--experimental_guard_against_concurrent_changes-experimental_guard_against_concurrent_changes1" 
t
7--workspace_status_command=./tools/bzl/workspace-statusworkspace_status_command./tools/bzl/workspace-status" 
K
 --incompatible_strict_action_envincompatible_strict_action_env1"*
0
--remote_timeout=600sremote_timeout600s" 
e
---incompatible_enable_cc_toolchain_resolution+incompatible_enable_cc_toolchain_resolution1"*
J
!--attempt_to_print_relative_pathsattempt_to_print_relative_paths1"
6
--action_env=DOCKER_HOST
action_envDOCKER_HOST"
Y
'--incompatible_exclusive_test_sandboxed%incompatible_exclusive_test_sandboxed1" *
,
--build_tests_onlybuild_tests_only1" 
0
--test_output=errorstest_outputerrors"
4
--test_summary=detailedtest_summarydetailed"
J
"--test_tag_filters=-postmerge-testtest_tag_filters-postmerge-test" 
-
--rc_source=client	rc_sourceclient"*
U
&--default_override=0:common=--isatty=1default_override0:common=--isatty=1"*
m
2--default_override=0:common=--terminal_columns=238default_override0:common=--terminal_columns=238"*
c
---rc_source=/home/<USER>/src/augment/.bazelrc	rc_source!/home/<USER>/src/augment/.bazelrc"*
[
)--default_override=1:build:ci=--curses=nodefault_override1:build:ci=--curses=no"*
w
7--default_override=1:build:ci=--noshow_loading_progressdefault_override$1:build:ci=--noshow_loading_progress"*
m
2--default_override=1:build:ci=--test_summary=shortdefault_override1:build:ci=--test_summary=short"*
g
/--default_override=1:build:ci=--show_timestampsdefault_override1:build:ci=--show_timestamps"*
�
Q--default_override=1:build:ci=--aspects=@rules_rust//rust:defs.bzl%rustfmt_aspectdefault_override>1:build:ci=--aspects=@rules_rust//rust:defs.bzl%rustfmt_aspect"*
�
=--default_override=1:build:ci=--output_groups=+rustfmt_checksdefault_override*1:build:ci=--output_groups=+rustfmt_checks"*
k
1--default_override=1:run=--action_env=DOCKER_HOSTdefault_override1:run=--action_env=DOCKER_HOST"*
c
---default_override=1:build:deploy=--curses=nodefault_override1:build:deploy=--curses=no"*

;--default_override=1:build:deploy=--noshow_loading_progressdefault_override(1:build:deploy=--noshow_loading_progress"*
o
3--default_override=1:build:deploy=--show_timestampsdefault_override 1:build:deploy=--show_timestamps"*
[
)--default_override=1:build:deploy=--stampdefault_override1:build:deploy=--stamp"*
m
2--default_override=1:test=--action_env=DOCKER_HOSTdefault_override1:test=--action_env=DOCKER_HOST"*
�
A--default_override=1:test=--incompatible_exclusive_test_sandboxeddefault_override.1:test=--incompatible_exclusive_test_sandboxed"*
a
,--default_override=1:test=--build_tests_onlydefault_override1:test=--build_tests_only"*
e
.--default_override=1:test=--test_output=errorsdefault_override1:test=--test_output=errors"*
k
1--default_override=1:test=--test_summary=detaileddefault_override1:test=--test_summary=detailed"*
�
<--default_override=1:test=--test_tag_filters=-postmerge-testdefault_override)1:test=--test_tag_filters=-postmerge-test"*
c
---default_override=1:build=--verbose_failuresdefault_override1:build=--verbose_failures"*
�
H--default_override=1:build=--experimental_ui_max_stdouterr_bytes=4194304default_override51:build=--experimental_ui_max_stdouterr_bytes=4194304"*
U
&--default_override=1:build=--force_picdefault_override1:build=--force_pic"*
�
@--default_override=1:build=--experimental_cc_implementation_depsdefault_override-1:build=--experimental_cc_implementation_deps"*
�
V--default_override=1:build=--@rules_cuda//cuda:archs=compute_80:compute_80,sm_80,sm_86default_overrideC1:build=--@rules_cuda//cuda:archs=compute_80:compute_80,sm_80,sm_86"*
s
5--default_override=1:build=--@rules_cuda//cuda:enabledefault_override"1:build=--@rules_cuda//cuda:enable"*
�
q--default_override=1:build=--@rules_cuda//cuda:copts=--use_fast_math,-std=c++17,-forward-unknown-to-host-compilerdefault_override^1:build=--@rules_cuda//cuda:copts=--use_fast_math,-std=c++17,-forward-unknown-to-host-compiler"*
�
X--default_override=1:build=--@rules_cuda//cuda:runtime=@local_cuda//:cuda_runtime_staticdefault_overrideE1:build=--@rules_cuda//cuda:runtime=@local_cuda//:cuda_runtime_static"*
�
>--default_override=1:build=--java_runtime_version=remotejdk_11default_override+1:build=--java_runtime_version=remotejdk_11"*
�
=--default_override=1:build=--action_env=DOCKER_HOST=void:8888default_override*1:build=--action_env=DOCKER_HOST=void:8888"*
e
.--default_override=1:build=--cxxopt=-std=c++17default_override1:build=--cxxopt=-std=c++17"*
[
)--default_override=1:build=--cxxopt=-Walldefault_override1:build=--cxxopt=-Wall"*
W
'--default_override=1:build=--copt=-fPICdefault_override1:build=--copt=-fPIC"*
�
C--default_override=1:build=--experimental_reuse_sandbox_directoriesdefault_override01:build=--experimental_reuse_sandbox_directories"*
�
J--default_override=1:build=--experimental_guard_against_concurrent_changesdefault_override71:build=--experimental_guard_against_concurrent_changes"*
�
R--default_override=1:build=--workspace_status_command=./tools/bzl/workspace-statusdefault_override?1:build=--workspace_status_command=./tools/bzl/workspace-status"*

;--default_override=1:build=--incompatible_strict_action_envdefault_override(1:build=--incompatible_strict_action_env"*
i
0--default_override=1:build=--remote_timeout=600sdefault_override1:build=--remote_timeout=600s"*
�
M--default_override=1:build=--@aspect_rules_format//format:swift_enabled=falsedefault_override:1:build=--@aspect_rules_format//format:swift_enabled=false"*
�
P--default_override=1:build=--@aspect_rules_format//format:prettier_enabled=falsedefault_override=1:build=--@aspect_rules_format//format:prettier_enabled=false"*
�
H--default_override=1:build=--incompatible_enable_cc_toolchain_resolutiondefault_override51:build=--incompatible_enable_cc_toolchain_resolution"*
�
<--default_override=1:build=--attempt_to_print_relative_pathsdefault_override)1:build=--attempt_to_print_relative_paths"*
*
--startup_time=6startup_time6"*
4
--command_wait_time=0command_wait_time0"*
4
--extract_data_time=0extract_data_time0"*
�
b--binary_path=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazelbinary_pathT/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel"*
m
2--client_env=BASH_ENV=/usr/share/modules/init/bash
client_env%BASH_ENV=/usr/share/modules/init/bash"*
�
�--client_env=BASH_FUNC__module_raw%%=() {  unset _mlshdbg;
 if [ "${MODULES_SILENT_SHELL_DEBUG:-0}" = '1' ]; then
 case "$-" in 
 *v*x*)
 set +vx;
 _mlshdbg='vx'
 ;;
 *v*)
 set +v;
 _mlshdbg='v'
 ;;
 *x*)
 set +x;
 _mlshdbg='x'
 ;;
 *)
 _mlshdbg=''
 ;;
 esac;
 fi;
 unset _mlre _mlIFS;
 if [ -n "${IFS+x}" ]; then
 _mlIFS=$IFS;
 fi;
 IFS=' ';
 for _mlv in ${MODULES_RUN_QUARANTINE:-};
 do
 if [ "${_mlv}" = "${_mlv##*[!A-Za-z0-9_]}" -a "${_mlv}" = "${_mlv#[0-9]}" ]; then
 if [ -n "`eval 'echo ${'$_mlv'+x}'`" ]; then
 _mlre="${_mlre:-}${_mlv}_modquar='`eval 'echo ${'$_mlv'}'`' ";
 fi;
 _mlrv="MODULES_RUNENV_${_mlv}";
 _mlre="${_mlre:-}${_mlv}='`eval 'echo ${'$_mlrv':-}'`' ";
 fi;
 done;
 if [ -n "${_mlre:-}" ]; then
 eval `eval ${_mlre}/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash '"$@"'`;
 else
 eval `/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash "$@"`;
 fi;
 _mlstatus=$?;
 if [ -n "${_mlIFS+x}" ]; then
 IFS=$_mlIFS;
 else
 unset IFS;
 fi;
 unset _mlre _mlv _mlrv _mlIFS;
 if [ -n "${_mlshdbg:-}" ]; then
 set -$_mlshdbg;
 fi;
 unset _mlshdbg;
 return $_mlstatus
}
client_env�BASH_FUNC__module_raw%%=() {  unset _mlshdbg;
 if [ "${MODULES_SILENT_SHELL_DEBUG:-0}" = '1' ]; then
 case "$-" in 
 *v*x*)
 set +vx;
 _mlshdbg='vx'
 ;;
 *v*)
 set +v;
 _mlshdbg='v'
 ;;
 *x*)
 set +x;
 _mlshdbg='x'
 ;;
 *)
 _mlshdbg=''
 ;;
 esac;
 fi;
 unset _mlre _mlIFS;
 if [ -n "${IFS+x}" ]; then
 _mlIFS=$IFS;
 fi;
 IFS=' ';
 for _mlv in ${MODULES_RUN_QUARANTINE:-};
 do
 if [ "${_mlv}" = "${_mlv##*[!A-Za-z0-9_]}" -a "${_mlv}" = "${_mlv#[0-9]}" ]; then
 if [ -n "`eval 'echo ${'$_mlv'+x}'`" ]; then
 _mlre="${_mlre:-}${_mlv}_modquar='`eval 'echo ${'$_mlv'}'`' ";
 fi;
 _mlrv="MODULES_RUNENV_${_mlv}";
 _mlre="${_mlre:-}${_mlv}='`eval 'echo ${'$_mlrv':-}'`' ";
 fi;
 done;
 if [ -n "${_mlre:-}" ]; then
 eval `eval ${_mlre}/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash '"$@"'`;
 else
 eval `/usr/bin/tclsh8.6 /usr/lib/x86_64-linux-gnu/modulecmd.tcl bash "$@"`;
 fi;
 _mlstatus=$?;
 if [ -n "${_mlIFS+x}" ]; then
 IFS=$_mlIFS;
 else
 unset IFS;
 fi;
 unset _mlre _mlv _mlrv _mlIFS;
 if [ -n "${_mlshdbg:-}" ]; then
 set -$_mlshdbg;
 fi;
 unset _mlshdbg;
 return $_mlstatus
}"*
�
=--client_env=BASH_FUNC_module%%=() {  _module_raw "$@" 2>&1
}
client_env0BASH_FUNC_module%%=() {  _module_raw "$@" 2>&1
}"*
�

�--client_env=BASH_FUNC_switchml%%=() {  typeset swfound=1;
 if [ "${MODULES_USE_COMPAT_VERSION:-0}" = '1' ]; then
 typeset swname='main';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd.tcl ]; then
 typeset swfound=0;
 unset MODULES_USE_COMPAT_VERSION;
 fi;
 else
 typeset swname='compatibility';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd-compat ]; then
 typeset swfound=0;
 MODULES_USE_COMPAT_VERSION=1;
 export MODULES_USE_COMPAT_VERSION;
 fi;
 fi;
 if [ $swfound -eq 0 ]; then
 echo "Switching to Modules $swname version";
 source /usr/share/modules/init/bash;
 else
 echo "Cannot switch to Modules $swname version, command not found";
 return 1;
 fi
}
client_env�BASH_FUNC_switchml%%=() {  typeset swfound=1;
 if [ "${MODULES_USE_COMPAT_VERSION:-0}" = '1' ]; then
 typeset swname='main';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd.tcl ]; then
 typeset swfound=0;
 unset MODULES_USE_COMPAT_VERSION;
 fi;
 else
 typeset swname='compatibility';
 if [ -e /usr/lib/x86_64-linux-gnu/modulecmd-compat ]; then
 typeset swfound=0;
 MODULES_USE_COMPAT_VERSION=1;
 export MODULES_USE_COMPAT_VERSION;
 fi;
 fi;
 if [ $swfound -eq 0 ]; then
 echo "Switching to Modules $swname version";
 source /usr/share/modules/init/bash;
 else
 echo "Cannot switch to Modules $swname version, command not found";
 return 1;
 fi
}"*
O
#--client_env=CONDA_DEFAULT_ENV=base
client_envCONDA_DEFAULT_ENV=base"*
_
+--client_env=CONDA_EXE=/opt/conda/bin/conda
client_envCONDA_EXE=/opt/conda/bin/conda"*
Q
$--client_env=CONDA_PREFIX=/opt/conda
client_envCONDA_PREFIX=/opt/conda"*
]
*--client_env=CONDA_PROMPT_MODIFIER=(base) 
client_envCONDA_PROMPT_MODIFIER=(base) "*
o
3--client_env=CONDA_PYTHON_EXE=/opt/conda/bin/python
client_env&CONDA_PYTHON_EXE=/opt/conda/bin/python"*
=
--client_env=CONDA_SHLVL=1
client_env
CONDA_SHLVL=1"*
a
,--client_env=CUDA_HOME=/usr/local/cuda-11.3/
client_envCUDA_HOME=/usr/local/cuda-11.3/"*
�
B--client_env=DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
client_env5DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus"*
o
3--client_env=ENV=/usr/share/modules/init/profile.sh
client_env&ENV=/usr/share/modules/init/profile.sh"*
E
--client_env=HOME=/home/<USER>
client_envHOME=/home/<USER>"*
;
--client_env=LANG=C.UTF-8
client_envLANG=C.UTF-8"*
G
--client_env=LC_TERMINAL=iTerm2
client_envLC_TERMINAL=iTerm2"*
W
'--client_env=LC_TERMINAL_VERSION=3.4.19
client_envLC_TERMINAL_VERSION=3.4.19"*
�
�--client_env=LD_LIBRARY_PATH=/opt/amazon/efa/lib:/opt/amazon/openmpi/lib:/usr/local/cuda/efa/lib:/usr/local/cuda/lib:/usr/local/cuda:/usr/local/cuda/lib64:/usr/local/cuda/extras/CUPTI/lib64:/usr/local/cuda/targets/x86_64-linux/lib:/usr/local/lib:/usr/lib:
client_env�LD_LIBRARY_PATH=/opt/amazon/efa/lib:/opt/amazon/openmpi/lib:/usr/local/cuda/efa/lib:/usr/local/cuda/lib:/usr/local/cuda:/usr/local/cuda/lib64:/usr/local/cuda/extras/CUPTI/lib64:/usr/local/cuda/targets/x86_64-linux/lib:/usr/local/lib:/usr/lib:"*
e
.--client_env=LESSCLOSE=/usr/bin/lesspipe %s %s
client_env!LESSCLOSE=/usr/bin/lesspipe %s %s"*
a
,--client_env=LESSOPEN=| /usr/bin/lesspipe %s
client_envLESSOPEN=| /usr/bin/lesspipe %s"*
k
1--client_env=LE_WORKING_DIR=/home/<USER>/.acme.sh
client_env$LE_WORKING_DIR=/home/<USER>/.acme.sh"*
?
--client_env=LOADEDMODULES=
client_envLOADEDMODULES="*
?
--client_env=LOGNAME=ubuntu
client_envLOGNAME=ubuntu"*
�
�--client_env=LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
client_env�LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:"*
�
I--client_env=MANPATH=/home/<USER>/.nvm/versions/node/v18.12.1/share/man::
client_env<MANPATH=/home/<USER>/.nvm/versions/node/v18.12.1/share/man::"*
�
�--client_env=MODULEPATH=/etc/environment-modules/modules:/usr/share/modules/versions:/usr/share/modules/$MODULE_VERSION/modulefiles:/usr/share/modules/modulefiles
client_env�MODULEPATH=/etc/environment-modules/modules:/usr/share/modules/versions:/usr/share/modules/$MODULE_VERSION/modulefiles:/usr/share/modules/modulefiles"*
�
�--client_env=MODULEPATH_modshare=/etc/environment-modules/modules:1:/usr/share/modules/$MODULE_VERSION/modulefiles:1:/usr/share/modules/modulefiles:1:/usr/share/modules/versions:1
client_env�MODULEPATH_modshare=/etc/environment-modules/modules:1:/usr/share/modules/$MODULE_VERSION/modulefiles:1:/usr/share/modules/modulefiles:1:/usr/share/modules/versions:1"*
_
+--client_env=MODULESHOME=/usr/share/modules
client_envMODULESHOME=/usr/share/modules"*
�
@--client_env=MODULES_CMD=/usr/lib/x86_64-linux-gnu/modulecmd.tcl
client_env3MODULES_CMD=/usr/lib/x86_64-linux-gnu/modulecmd.tcl"*
?
--client_env=MOTD_SHOWN=pam
client_envMOTD_SHOWN=pam"*
�
A--client_env=NVM_BIN=/home/<USER>/.nvm/versions/node/v18.12.1/bin
client_env4NVM_BIN=/home/<USER>/.nvm/versions/node/v18.12.1/bin"*
=
--client_env=NVM_CD_FLAGS=
client_env
NVM_CD_FLAGS="*
U
&--client_env=NVM_DIR=/home/<USER>/.nvm
client_envNVM_DIR=/home/<USER>/.nvm"*
�
J--client_env=NVM_INC=/home/<USER>/.nvm/versions/node/v18.12.1/include/node
client_env=NVM_INC=/home/<USER>/.nvm/versions/node/v18.12.1/include/node"*
I
 --client_env=OLDPWD=/home/<USER>
client_envOLDPWD=/home/<USER>"*
�
�--client_env=PATH=/home/<USER>/src/augment/tools:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/opt/conda/bin:/opt/conda/condabin:/home/<USER>/.cargo/bin:/home/<USER>/.nvm/versions/node/v18.12.1/bin:/opt/amazon/openmpi/bin:/opt/amazon/efa/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/usr/local/go/bin:/home/<USER>/go/bin
client_env�PATH=/home/<USER>/src/augment/tools:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/opt/conda/bin:/opt/conda/condabin:/home/<USER>/.cargo/bin:/home/<USER>/.nvm/versions/node/v18.12.1/bin:/opt/amazon/openmpi/bin:/opt/amazon/efa/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/usr/local/go/bin:/home/<USER>/go/bin"*
[
)--client_env=PWD=/home/<USER>/src/augment
client_envPWD=/home/<USER>/src/augment"*
A
--client_env=SHELL=/bin/bash
client_envSHELL=/bin/bash"*
1
--client_env=SHLVL=2
client_envSHLVL=2"*
a
,--client_env=SSH_CLIENT=************ 8389 22
client_envSSH_CLIENT=************ 8389 22"*
�
<--client_env=SSH_CONNECTION=************ 8389 *********** 22
client_env/SSH_CONNECTION=************ 8389 *********** 22"*
G
--client_env=SSH_TTY=/dev/pts/4
client_envSSH_TTY=/dev/pts/4"*
I
 --client_env=TERM=xterm-256color
client_envTERM=xterm-256color"*
9
--client_env=USER=ubuntu
client_envUSER=ubuntu"*
�
M--client_env=XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop
client_env@XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop"*
_
+--client_env=XDG_RUNTIME_DIR=/run/user/1000
client_envXDG_RUNTIME_DIR=/run/user/1000"*
O
#--client_env=XDG_SESSION_CLASS=user
client_envXDG_SESSION_CLASS=user"*
G
--client_env=XDG_SESSION_ID=130
client_envXDG_SESSION_ID=130"*
K
!--client_env=XDG_SESSION_TYPE=tty
client_envXDG_SESSION_TYPE=tty"*
7
--client_env=_CE_CONDA=
client_env
_CE_CONDA="*
/
--client_env=_CE_M=
client_env_CE_M="*
W
'--client_env=BAZELISK_SKIP_WRAPPER=true
client_envBAZELISK_SKIP_WRAPPER=true"*
�
l--client_env=BAZEL_REAL=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel
client_env_BAZEL_REAL=/home/<USER>/.cache/bazelisk/downloads/bazelbuild/bazel-6.1.0-linux-x86_64/bin/bazel"*
S
%--client_cwd=/home/<USER>/src/augment
client_cwd/home/<USER>/src/augment"*
�
k--remote_cache=grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092remote_cache\grpc://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092" 
p
4--host_platform=//tools/bzl:aws_ubuntu2004_linux_x86
host_platform$//tools/bzl:aws_ubuntu2004_linux_x86"
h
0--platforms=//tools/bzl:aws_ubuntu2004_linux_x86	platforms$//tools/bzl:aws_ubuntu2004_linux_x86"
D
--action_env=TORCH_VERSION=11.7
action_envTORCH_VERSION=11.7"
\
+--action_env=CUDA_PATH=/usr/local/cuda-11.7
action_envCUDA_PATH=/usr/local/cuda-11.7"
\
+--action_env=CUDA_HOME=/usr/local/cuda-11.7
action_envCUDA_HOME=/usr/local/cuda-11.7"
�
[--action_env=LD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal
action_envNLD_LIBRARY_PATH=/usr/local/cuda-11.7/targets/x86_64-linux/lib:/usr/local/focal"
x
9--build_event_binary_file=/home/<USER>/src/augment/bep.pbbuild_event_binary_file/home/<USER>/src/augment/bep.pb"
m
5--@aspect_rules_format//format:prettier_enabled=false-@aspect_rules_format//format:prettier_enabledfalse
g
2--@aspect_rules_format//format:swift_enabled=false*@aspect_rules_format//format:swift_enabledfalse
y
;--@rules_cuda//cuda:archs=compute_80:compute_80,sm_80,sm_86@rules_cuda//cuda:archs!compute_80:compute_80,sm_80,sm_86
�
Z--@rules_cuda//cuda:copts=[--use_fast_math, -std=c++17, -forward-unknown-to-host-compiler]@rules_cuda//cuda:copts@[--use_fast_math, -std=c++17, -forward-unknown-to-host-compiler]
}
=--@rules_cuda//cuda:runtime=@local_cuda//:cuda_runtime_static@rules_cuda//cuda:runtime!@local_cuda//:cuda_runtime_static
residual

//clients/...
	�
tool� U
"

//clients/..."�
//clients/vscode:release.lint�
//clients/vscode:test2 �
 � ��[32mINFO: [0mInvocation ID: a37a5057-d9a4-4823-bf02-44840552ab36
[32mLoading:[0m 

[1A[K[32mLoading:[0m 

[1A[K[32mLoading:[0m 0 packages loaded
c
� �[
Y/home/<USER>/.cache/bazel/_bazel_ubuntu/e07873bf2c524a5f3a550b9b08c46b59/execroot/augmentT
DzB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3 �
DzB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3��
k8-fastbuildk8k8"
COMPILATION_MODE	fastbuild"

TARGET_CPUk8"$
GENDIRbazel-out/k8-fastbuild/bin"$
BINDIRbazel-out/k8-fastbuild/bin�
"�
//clients/vscode:release.linte*c
//clients/vscode:release.lintB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3�
py_test rulelint�
�
//clients/vscode:test]*[
//clients/vscode:testB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3�
_jest_test rulem
r �f

BUILD_EMBED_LABEL


BUILD_HOSTip-172-31-5-34

BUILD_TIMESTAMP
1679069911


BUILD_USERdirk�	
j
0�	�	
[1A[K[32mAnalyzing:[0m 2 targets (0 packages loaded, 0 targets configured)

[1A[K[32mAnalyzing:[0m 2 targets (105 packages loaded, 34 targets configured)
    currently loading: @rules_foreign_cc//foreign_cc/private/framework

[1A[K
[1A[K[32mAnalyzing:[0m 2 targets (157 packages loaded, 4708 targets configured)
    currently loading: @jest// ... (3 packages)

[1A[K
[1A[K[32mAnalyzing:[0m 2 targets (240 packages loaded, 8009 targets configured)
    currently loading: @bazel_tools//src/tools/launcher ... (155 packages)

[1A[K
[1A[K[32mAnalyzing:[0m 2 targets (565 packages loaded, 9765 targets configured)
    currently loading: @jest__strip-ansi__6.0.1// ... (235 packages)

[1A[K
[1A[K[32mAnalyzing:[0m 2 targets (1082 packages loaded, 12091 targets configured)
    currently loading: @npm__at_types_istanbul-reports__3.0.1// ... (5 packages)

[1A[K
[1A[K[32mINFO: [0mAnalyzed 2 targets (1087 packages loaded, 13149 targets configured).
[0m checking cached actions

[1A[K[32mINFO: [0mFound 2 test targets...
[0m checking cached actions

[1A[K[32m[0 / 3][0m [Prepa] BazelWorkspaceStatusAction stable-status.txt

[1A[K[32m[5,703 / 5,894][0m checking cached actions
�
j
0z�
�
clients/vscode/test.sh�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/9707f827cee3b406a875ef0c93a2e0e5202543f705f50920847bf34432f3f0cb/20909"	bazel-out"k8-fastbuild"bin*@9707f827cee3b406a875ef0c93a2e0e5202543f705f50920847bf34432f3f0cb0��
j
1 �
j
1z�
�
clients/vscode/release.lint�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/644f5d8c7bd9c1a94be4b5cef8c277f7a63e9fffa639f7e93f1b8ed589f78f00/3947"	bazel-out"k8-fastbuild"bin*@644f5d8c7bd9c1a94be4b5cef8c277f7a63e9fffa639f7e93f1b8ed589f78f000�
�
!tools/bzl/lint/py_lint_wrapper.py�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/8b7db79501563ecdbe1fd4323abaa5dc5ace09cf29f46737c5d13da8f5142702/445*@8b7db79501563ecdbe1fd4323abaa5dc5ace09cf29f46737c5d13da8f51427020��
e*c
//clients/vscode:release.lintB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3kBi
//clients/vscode:release.lint *B
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3e:c
//clients/vscode:release.lintB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3B�
default
1lint"�
clients/vscode/release.lint�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/644f5d8c7bd9c1a94be4b5cef8c277f7a63e9fffa639f7e93f1b8ed589f78f00/3947"	bazel-out"k8-fastbuild"bin*@644f5d8c7bd9c1a94be4b5cef8c277f7a63e9fffa639f7e93f1b8ed589f78f000�8�R��
]*[
//clients/vscode:testB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3cBa
//clients/vscode:test *B
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3]:[
//clients/vscode:testB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3B�
default
0"�
clients/vscode/test.sh�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/9707f827cee3b406a875ef0c93a2e0e5202543f705f50920847bf34432f3f0cb/20909"	bazel-out"k8-fastbuild"bin*@9707f827cee3b406a875ef0c93a2e0e5202543f705f50920847bf34432f3f0cb0��8�R��
kBi
//clients/vscode:release.lint *B
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3R��
test.log�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/439b679ce917496e45a121022079cf9a6e0e71755e0d8211f2d7e43a5fc6fd01/173�
test.xml�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/014adf9ff954060ff5173efa064be331f7eb5275b19f131f3f9ddc3f286ce159/562�E (0���0B R��Ҡ����Z�����
e:c
//clients/vscode:release.lintB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3J���bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/439b679ce917496e45a121022079cf9a6e0e71755e0d8211f2d7e43a5fc6fd01/173(08���0@����0H�EPb����j��Ҡ����r��Ҡ�Ɔ�x�
cBa
//clients/vscode:test *B
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3R��
test.log�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/288c153a9e08431492554f708460adfef3215c28eb2f96098b958c3e852a817a/13302�
test.xml�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/be3425c08e60c9eab18b666917de14f6e68484b6c8aa121a0d0129bf8db9619f/4916�7 (0����0B R��Ҡ���Z�ͤ#�
]:[
//clients/vscode:testB
@825af599e15dee866493e0356f04b9501bfbf536c41f949179dffb6f8b0ee2a3J���bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/288c153a9e08431492554f708460adfef3215c28eb2f96098b958c3e852a817a/13302(08����0@����0H�7Pb�ͤ#j��Ҡ���r��Ҡ����x,
J � r!�����0	
SUCCESS*إҠ���x�
� ��

elapsed time2.281000
�

critical path�Critical Path: 0.05s, Remote (0.00% of the time): [parse: 0.00%, queue: 0.00%, network: 0.00%, upload: 0.00%, setup: 0.00%, process: 0.00%, fetch: 0.00%, retry: 0.00%, processOutputs: 0.00%, other: 0.00%, input files: 0, input bytes: 0, memory bytes: 0, input files limit: 0, input bytes limit: 0, output files limit: 0, output bytes limit: 0, memory limit: 0, time limit: 0 seconds]
  0.05s action 'BazelWorkspaceStatusAction stable-status.txt'
'

process stats1 process: 1 internal.
�
command.profile.gz�bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/312bfcb20068743cd5323409309874c668b8e78d81beb54448609d5223158b5d/41849�
� �
�Test cases: finished with [0m[32m35 passing[0m and 0 failing out of 35 test cases

Executed 0 out of 2 tests: 2 tests pass.
There were tests whose specified size is too big. Use the --test_verbose_timeout_warnings command line option to see which ones these are.
�
[1A[K[32mINFO: [0mElapsed time: 2.281s, Critical Path: 0.05s
[32m[6,643 / 6,643][0m 2 / 2 tests;[0m checking cached actions[0m; last test: [32m//clients/vscode:test[0m

[1A[K[32mINFO: [0m1 process: 1 internal.
[32m[6,643 / 6,643][0m 2 / 2 tests;[0m checking cached actions[0m; last test: [32m//clients/vscode:test[0m

[1A[K[32mINFO:[0m Build completed successfully, 1 total action

[1A[K[32mINFO:[0m Build completed successfully, 1 total action

[1A[K[32mINFO:[0m Build completed successfully, 1 total action

[1A[K[32mINFO:[0m Build completed successfully, 1 total action
�
� ��
O�4�4",
BazelWorkspaceStatusAction�����0 �����02	
total2
internalC

G1 Old Gen��K
G1 Survivor Space���

G1 Eden Space�����f�f"�*	�n��	2X`:.���:�
���ҷ"
���з*
��ـ��B�^�4� ��(�^0�48�;H�
 �
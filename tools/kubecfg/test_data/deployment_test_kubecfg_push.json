[{"apiVersion": "apps/v1", "kind": "Deployment", "metadata": {"labels": {"app": "support-ui", "app.kubernetes.io/version": "1234567890", "augmentcode.com/deployed-by": "user1", "app.kubernetes.io/managed-by": "kubecfg", "augmentcode.com/deployment-target": "test-selection"}, "name": "support-ui"}, "spec": {"progressDeadlineSeconds": 1800, "template": {"metadata": {"labels": {"app": "support-ui", "app.kubernetes.io/version": "1234567890", "augmentcode.com/deployed-by": "user1", "app.kubernetes.io/managed-by": "kubecfg", "augmentcode.com/deployment-target": "test-selection"}}, "spec": {"containers": [{"env": [{"name": "CONFIG_FILE", "value": "/config/flask.cfg"}], "image": "image@sha256:1234567890", "name": "support-ui", "ports": [{"containerPort": 5000, "name": "http-svc"}], "volumeMounts": [{"mountPath": "/client-certs", "name": "client-certs"}]}], "volumes": [{"name": "client-certs", "secret": {"secretName": "support-ui-client-certificate"}}]}}}}]
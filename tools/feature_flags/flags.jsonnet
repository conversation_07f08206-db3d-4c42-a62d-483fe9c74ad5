// Feature Flags
//
// see https://www.notion.so/Dynamic-Feature-Flags-112bba10175a803fb0ceef0ba2ea986c?pvs=4
//
// Dev/Test
// Set `extraFakeFeatureFlags` flags in dev-defaults.jsonnet namespace config or your namespace config.
//

local agents_wave2_latest = [
  // Agents Wave 2 03/04/2025 - alpha survey respondents
  {
    user_id_hmac: [
      'f5945017b5eaf42b95747ab4d1c0979b173c726d1f0e318ed79fa88bdcde0975',
      '96d6b44dfad6d8671bc9633c9309f0918203a49b7b528e3a3b317b5c0c63cfd9',
      '2ff753b0d2a74c3c1ed88512fb816032000d65022bbf0d9a17e7d056ffadb7ba',
      '689e9a80cbec35ea4bf51788ef71ed5d967303f355c8fc2b60256c1faad7f07e',
      '9b06acfd9f4d0f9fc6861db3aee986f73d4f2aee4c088ffd691a1f9eec039278',
      'eb4d50c811e125cdceab1d442bce9ea84b921f707edc1d196d369d24f71647a6',
      'd9f7c9afc07f7e28c25b79c0ce14864bbfd272352c8810c4c3dbdb66f2234683',
      'f2ea37719a3ed3ba7f5e2812ea01f7c2a9e21331f5893f7f30188baa4998bfc9',
      'ed8b995aaeb6826afed6711dee15437b96a9d2e59b73a01922ecf08905278366',
      'b796e0dae62f846025ed92b31822a52dd41146e433463a75cc0b84f1b3acf2b1',
      'eb324c63b1a0478411e3843b0fe01f020903a6c9a75af1d24a3186be027ebb68',
      'a06b42b186b79f1dc83d13621956fe6316c588ccd6c3dd7a29a6e59487782de7',
      '02e660b7c0a94a4ee1d9e62c742707ebf7770c7a0a20bdd736b92dd7d8ac4a7e',
      'cbc965b2dae88e190a420ffea77fde7e81a7c1ed5797461f0bb5ab5d32eb54e9',
      '2087230ea760088d218808f0a0ab0a44bf005d98be0957aee35e41f3207058ff',
      '69e51793108c992b955c37985044fa85c0d9437c542b4c61d44fb8f7ec0ac0de',
      'fdd77de1e06af0902b67e3a3fb2938cdd3f9468bedd17c062fe82919eb250f6e',
      '03d3c69b20cb144eb4a22bbc4be6cea7c8d3662676167890f59280ab11c4ab88',
      '69b9934f6fb3e976b380dffad9f6043168b32d6145761a75e3495c0a15402535',
      'd5b5bdeeaed35704ec5c9986da0fbfe1304623f4415ad6b207c3d8b11b53c896',
      'b44123cf0b6d84aa13fd87efd3a8ecfdee5127507e95e0118d1d81dc33e5adfa',
      'a0af28e1d486d6aa12fccbb2afdc2ed9d82a7f201f67d0c2230992a1acf79966',
      '1097453508262e9c92d836d6445ca0b9cda4f76e1e1efbd06989e61bcfa619ba',
      '64f3b93b18f9f8879ff50212c219e55f826bfc0045f7ab56de0708c7f206bfc0',
      '154ff7f7ff20f57827d231790f5a790e0cbc935fe9932ed847674b1344c6794a',
      '358f649af621babc6f448c738b8d862b068987d355ee55c419d9d46a21a5a190',
      '546bf98956e058e96567e3e876904ce24aaae1e76d46bab60ca3d2817fb75be6',
      '161496ca5c3ed164f5ce0ee4e7c1a057ca72d19a42a202a6b3a0cf98eafad9b3',
    ],
    return_value: error 'Must override return_value',
  },
  {
    user_uuid: [
      '949bbece-5e2b-4446-9f9e-e66c3072a5d8',
      'ba7683ba-5405-4fd1-83b5-1c5928084f90',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave2_stable = [
  // Agents Wave 2 03/04/2025
  {
    tenant_name: [
      'lemonade',
      'gofundme',
      'silanano',
      'samaya',
    ],
    return_value: error 'Must override return_value',
  },
  // Per-user release for UXR study
  {
    user_uuid: [
      'aefe7a6d-7e82-49fa-8460-483915a0ee7c',
      'c21cac9a-356e-484f-a7c9-ff200b940be4',
      '793802e4-21b1-48c9-aac6-8bbc4410f757',
      'bc886046-fa20-4aa7-8901-d2139b36aa7b',
      'ee0089fc-47cd-4fa0-b9ad-426416748e90',
      'dae0eb29-9c8f-4dfd-9f3e-0d06996f73cd',
      'd21e5bba-00fe-4e4e-89e1-43ce9eaa3d72',
      '15c5b99a-9a8a-4940-867d-1725cfbb4b07',
      'aee3549a-8e9a-4fee-8c59-85ace6a24010',
      'fd4eeb80-caa6-48f2-8498-22fbb526c069',
      'f278b9e5-7a7f-47c4-9247-c82b413f289d',
      '1af7b81a-5c8c-4827-b985-ed3918c5b1a9',
      '9b6d6185-da34-45c8-a370-72a6951cf32e',
      'e657790c-e013-4833-a323-496dea9fc45e',
      '5a574608-a781-4123-82d6-f1f0d7a725da',
      '934dd871-0776-48d1-98b8-fea6c9ef65a5',
      'f218c87b-e934-4e8d-875a-2cb409d8a711',
      '137576a4-fcda-4e46-a919-4ae14d851b2e',
      '74a8fb90-2924-4647-8bec-9c444b62f078',
      'd7256a21-8dc9-4123-91ec-de4b83f8e381',
      '97d5a790-37c9-4960-be7f-af2893e5db71',
      '3e572482-dc8f-4c21-acc4-da34ae0579f5',
      '46c779dc-8e80-45ac-99a5-1675330101eb',
      '1400cee7-27c0-4937-bd5a-d3c65b5ae077',
      '2e6b0bc0-45f9-4a2e-8500-f55a35b10856',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave3_latest = [
  // Agents Wave 3 - 03/12/2025
  {
    // Discord survey respondents
    user_uuid: [
      '473404a3-8980-4ba2-aef4-b778708c7252',
      'e6761413-9d06-40f0-84d9-3c5c3546711d',
      'b91cf981-c7ff-4094-ac24-97c31b55bdd7',
      'f82846a7-9032-4679-9c2f-419346896ab3',
      '338f6962-7dfa-4768-a0c9-e8ffd4ff2aa1',
      '71e37722-10a8-433f-a0ee-dccfa6d7157b',
      '6d467092-679b-4205-8320-6a65bdcb6b0e',
      '451f9782-e35b-4229-8f48-b5606af372c9',
      '93b749a5-909d-487a-be14-29fa89d537f0',
      'a8639802-194f-4d64-9b28-9e962012990a',
      '8f356c27-790c-4c5f-8802-7f242c3e0b09',
      'fa2efe3f-76a5-4efa-b955-11589ec9d25c',
      '29bdfef2-b7d8-4541-8b16-b87969f708ba',
      'cc96af3d-fa9d-4867-9815-ffe8df28b904',
      '7865ea62-0cc4-42bb-b0d5-73d739af3fa4',
      '4a9d08be-836a-48b8-bd67-7d4f2db43549',
      '145c5c1e-4ede-4b81-bf3c-0b64f090a7fe',
      '23db6deb-912a-490c-89d0-9cb876ae0a93',
      'a09aa7a8-d8a7-4917-93f8-99dc0eba9ac3',
      '68287948-73d9-4f16-af34-561c6fd59ddb',
      '261136af-2ce1-4128-9c0e-6b0622bf8653',
      '02205316-bf32-48b7-a4e3-3fdf8dc07839',
      '2cb7e878-a3c4-4322-88b9-a1bb9c7c9dc8',
      '47c55acc-b01a-433a-b629-a29660f7265c',
      '985be049-e7a8-43a2-923c-76317b553077',
      'ba7322f9-b1e1-458f-bf04-c6c8b910b124',
      'a18b0d13-7b69-4160-a6cd-c25e16e4d41f',
      '749ef78b-dff4-46a5-98d2-6d8814ed5442',
      '1d7250dd-69bd-4eae-8b78-eb26f93e512a',
      '5b1fdaeb-64b0-4b86-b5d0-e5f5ad76d3f6',
      '91d474f4-eb2b-477e-b677-f9963c1061db',
      'fbdc52ba-bedf-4664-838f-e5c5acd0dafc',
      '5c826800-ff6f-4b5e-b944-a2a1ba6522ab',
      'bcefc6c3-ccd2-48fc-98d5-95ef9873cdae',
      'fe03e19f-4d43-4b86-80f1-a3b956ce5ddc',
      'd252fcda-d100-4eab-b0e4-061e3a189402',
      '5851d37e-d95f-402f-927b-bb5a18500bf6',
      '26e54dc5-e459-44db-95f1-8ed0f7dcc067',
      '2ba1de16-d0d4-41a5-97c0-476951451f46',
      'eeb88865-5c54-4e7c-a8c5-3dde3f306b62',
      '039ad530-fca8-4ff4-8218-1dc01d0c0bef',
      'ee05c9a2-38a0-48d1-826e-7a8bc1cc2f9f',
      '8cb3b90d-2f93-43b2-8e35-fb3e5d836437',
      '95282f8b-e684-440c-9f2c-6359aca3e130',
      'b5d9b2f8-72d0-40a4-bb1c-21faa5f772c0',
      'ceb06bc9-6a73-489a-8769-0ed892ec3da3',
      '1203f51d-1e24-4094-b67d-a29da7e695c5',
      '7747e9af-13a3-45f8-a7ec-8670e9e69c1c',
      '42746878-2be4-401b-be86-63f90811c77b',
      '812ac506-6f2f-4f6b-b39f-98f31de8bd5a',
      '7790a3ec-bfce-4b31-acfc-89046eb3160b',
      '7e2e48d2-3bd4-4852-86ac-2ecda0ed6e0c',
      '3c753a74-18b8-4cb1-9e38-21e5799dd9b9',
      '07616a04-c51d-47a6-abc2-f960c7eb31b7',
      '198016fc-ad58-4d4f-a2be-3247c28dda18',
      '2c35e823-ccaf-44e4-b478-31df3865547b',
      '93bf7295-1fa3-4f12-a6c2-a835d639c63e',
      '1eb302ce-fc5a-44fe-9aad-e03386932bc9',
      '9f4dfd6a-8ab4-4dff-930e-60ec062c7d7f',
      '7d0c9407-97b3-4bb9-9bcc-5939753542b6',
      'fc749434-7538-42fc-a93f-740cfb8dc3fc',
      '87ab1461-2219-4997-87ba-50c48f2fc647',
      'b5f6922e-b2a8-4683-bb7d-6ffbaaaced61',
      '1c56cdd6-1108-46e2-b820-6bc745885be0',
      '0358c2b2-7a26-44bf-bce4-4b0187a9eda9',
      'ded9d259-3690-4827-9d9d-e2d7d89227ab',
      '3f1dc451-fb9f-4366-9b7c-728c2c5671b7',
      'b634fff0-1302-41a5-99f6-439b2f1d3def',
      'f7232894-d418-467b-ad4c-812ddfc5850c',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave3_stable = [
  // Agents Wave 3 - 03/12/2025
  {
    tenant_name: [
      'intercom',
      'kognitos',
      'rippling',
      'lyrahealth',
      'newfront',
      'purestorage',
      'sifive',
      'sigma',
      'docker',
      'peek',
      'spoton',
      'reveart',
      'montecarlodata',
    ],
    return_value: error 'Must override return_value',
  },
  {
    // Individuals from enterprise tenants not listed above
    user_uuid: [
      'a0bc19f2-de12-45fd-8a2d-986b4c27fb07',
      '6a54216c-b046-4b63-b450-420a4f5d9a51',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave4_latest = [
  // Agents Wave 4 - 03/18/2025
  {
    // Fraction of Discovery namespaces
    namespace: [
      'd6',
      'd8',
      'd14',
    ],
    return_value: error 'Must override return_value',
  },
  {
    // Discord survey respondents, "influencers"
    user_uuid: [
      'b0dbb29a-8fa2-4dbc-99a4-7244874b9e54',
      'f492c2a9-932d-410b-96f4-4aadf95af595',
      'c07957e1-34eb-4988-a1ea-877331cbc47c',
      '433bd312-b5dd-48fa-9fab-22010de6129d',
      '5b0732ec-3565-4447-84d6-447ca4ed13e6',
      '03f22743-ae89-4f87-8bcf-d1a42b228c01',
      '426bcb86-941a-4d49-b21a-c2f13df441cc',
      'd1135721-5a85-410a-a50d-af9ac0d5db9d',
      'e4fd87bd-a116-4f7b-9bb7-b58eb3f6ab41',
      '5c3d327c-dbdc-45ac-8e29-be1acee321f2',
      '627e6bb7-a528-41cd-91fd-59282a5f0eee',
      'fd615575-2064-4451-93f2-61b7f766714b',
      'dc931be0-bc94-482a-b507-cdb99af5cd63',
      '0fb4b415-ccd5-40b2-9ad0-0e6774080950',
      '415c6aa3-b3f6-48db-ab54-395d2c19e6da',
      '35a3cd94-ce0f-497e-955d-b3f4f02c9208',
      '410f85f2-a012-4eec-9848-113ca0be08c6',
      'bb688832-03b0-419e-aa8e-dbe2c0e06b2f',
      '40accfae-ef9e-4ea0-9617-b1dfae99615d',
      '8393c37c-e3ce-4ba1-8e7e-d3870b6ba41f',
      '2514ff6e-aa7a-4856-8be2-4b917003c36b',
      '4a179971-9f37-4e31-821e-40fccfb1648a',
      'e6b33bef-346a-47f5-aaa6-979dafae664d',
      'a3c776ef-9d88-4385-9729-68e77ec1fd47',
      '3e208280-0a69-4fbb-9472-f376406dc29b',
      'e30c2e43-7ba8-441d-a5e1-6b0f8cf2eee4',
      'f0425203-4861-4ffe-81f2-53d298df5b0a',
      '8cd779cf-6b43-4c0e-a285-fbc45027e28e',
      '5cffe052-ff15-4cb3-aeb3-4c584f5631ab',
      'ed22e5f6-1a31-43d1-87e8-66fe44823832',
      '0ea14393-6e73-478a-95dc-a79e35a1a707',
      'ee44a967-7480-420e-a407-78823c533070',
      '174cb68a-53e7-4338-a316-2bc851ae2210',
      'c91e8f3d-4fa9-4789-89f4-c889a2948483',
      'f4eadcca-d0ad-4889-bdf6-66b154e03a30',
      '34ea6533-7f32-4383-95fb-aa2a25681b83',
      '66894ba5-d65f-406f-9d1f-0c62b6f03ca1',
      '8566479f-1ae4-447f-acf5-d7c855dd9979',
      '2f099e9d-1074-4cf4-8f75-8dee513a2f85',
      '683c9617-929e-421c-b294-b322ec4b57b8',
      '452a9339-2e61-46e6-95fa-c237a328d713',
      'c0916bb4-2217-4d6c-bade-5ec1ad8b8fe5',
      '8ef5925e-64aa-425c-b046-ae257ed81620',
      // Extending the list 03/21/2025 to bring in more jetbrains users for
      // that wave
      '6afcde04-9062-4962-97ad-969bd52f5b26',
      '21ed18dd-f621-46b7-9dcd-8e28d422e2b1',
      'ba7322f9-b1e1-458f-bf04-c6c8b910b124',
      'db44ab77-bcf3-4223-911a-9eba94789ac7',
      '93b749a5-909d-487a-be14-29fa89d537f0',
      '10266d40-ad47-423e-b195-2ff0ab2c41ea',
      'a6a7eea7-d735-4bda-8997-d917f4c1b704',
      '87ab1461-2219-4997-87ba-50c48f2fc647',
      '1203f51d-1e24-4094-b67d-a29da7e695c5',
      '3f1dc451-fb9f-4366-9b7c-728c2c5671b7',
      '194d0839-8ba8-42bb-b126-ac092bae0794',
      '198016fc-ad58-4d4f-a2be-3247c28dda18',
      'd0133efe-e915-4a8c-9e2e-27e5beb5087d',
      '0cd5e68a-7c74-40bd-81fe-9016abb3cd49',
      '03f50c38-1e21-4ec8-9c7f-9454fd81e59d',
      '93a12483-9044-4e2c-98cc-e455c38b282f',
      'e8f14080-4d10-4659-913a-52eff28d9460',
      '5ad4d7a1-4e18-4bf5-8e0e-1d29ba4fe468',
      'c1193993-2b3c-49dc-88cd-2a8607d5b95f',
      '2137863d-e068-4e68-bb1a-781cea6d3e4b',
      '67697b93-1348-4a36-9ba7-612bac4333b0',
      'eeb88865-5c54-4e7c-a8c5-3dde3f306b62',
      'ceb06bc9-6a73-489a-8769-0ed892ec3da3',
      'ee05c9a2-38a0-48d1-826e-7a8bc1cc2f9f',
      '261136af-2ce1-4128-9c0e-6b0622bf8653',
      '256660fd-7fc6-431b-a668-47ff98d47040',
      '7747e9af-13a3-45f8-a7ec-8670e9e69c1c',
      '71e37722-10a8-433f-a0ee-dccfa6d7157b',
      'b1bc7b3e-703e-4852-837c-8293ba0a17f0',
      '32fa9400-9ec0-4512-b98f-965f5fbab9a4',
      'a3639d1b-9f96-435f-a5de-0b9cabb534e3',
      '5c826800-ff6f-4b5e-b944-a2a1ba6522ab',
      '6d467092-679b-4205-8320-6a65bdcb6b0e',
      '6a19b5e2-f620-4b8d-8e15-7406ee4ee032',
      '092e678b-4ca8-4d54-8801-8d1d737e5856',
      'e7200aac-d918-4ff5-ba68-c5396483e0ae',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_wave4_stable = [
  // Agents Wave 4 - 03/18/2025
  {
    tenant_name: [
      'afresh',
      'maxar',
      'filevine',
      'tecton',
      'gojitsu',
      'cisco',
      'floqast',
      'digikey',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_rubrik_allowlist = [
  // Rubrik Allowlist - 05/21/2025
  {
    user_uuid: [
      '95c24b04-2721-4c67-af50-acf0ea3af438',
      'a42588e9-ac6d-452b-a8cd-a746788b7571',
      'c0bbadd4-a843-42f1-90e1-096a0476fd08',
      '15f0a4ed-c338-4321-8c2d-713f046ecfb1',
      '5393b8ab-3faf-436a-9055-d6602c85cd65',
      '2d40c058-d8ac-43b1-8fe3-45f97ef41b7a',
      '6caac782-3cfb-489e-a116-963d0f81901b',
      '883fb7ff-54d6-4ed9-84b0-460f6f7a78bc',
      'd888f816-0333-4af8-8a9e-a3ec27184699',
      '38118a4f-7bdc-4139-878f-f634d4dfe71a',
      '14ba1a44-5391-4af6-8c0d-2e476f19d643',
      '874b0108-0dd7-4f25-9e11-f6227eee45ae',
      'd05f3dcb-3160-45ac-8619-07bf43f5b07d',
      // 2nd wave
      '9152972f-ac65-42b2-80fd-a9c412b2daf3',
      'fcc75145-b1dc-4206-a2aa-f15d8b722476',
      '6b99e39d-2e43-49ae-b53d-b73bdbdc15b0',
      '1f2e37b9-5576-470f-8c2b-0028dc556543',
      '6c3d7f3f-79ee-48ee-b823-8163becfcae4',
      '65a3f65b-a27b-4830-8b90-fb0d14373c09',
      'ceae083a-13ca-4692-bbc8-839b2ebd31ef',
      '11202063-3a59-434a-9158-9a0d63f43d2b',
      'cbafc55d-2366-4578-ad07-9cefd40ec9db',
      '79e42b97-6b19-4f82-9293-534e51f74403',
      '28054859-f77f-40e9-a461-bc71107bf5ac',
      '1b1f2239-0d86-404f-a7dd-dacf0b844c4d',
      '409ba2f6-e017-46fe-bdb0-865a08315589',
      'c478b406-9920-45f2-8022-753de59fac5e',
      'c2d31b4f-b00b-4e9f-b488-ac4620fba2b5',
      '642340a0-b54a-4f69-b2cb-f8498dda15ea',
      'acdfa712-cbb0-47c2-828d-732e871eaf57',
      '28c7e51f-6a2a-4971-a7a1-aaf7bae3ff83',
      '38b20808-665f-4549-9c8f-d5fb9202cc8c',
      '17c06c71-5375-4b1d-84ee-8c98cc11b059',
      'f1aad7fb-5cdf-4523-a7a5-2b201ea7bedb',
      'a22ac128-d16f-4ea6-a962-e02623ed8e79',
      '303f33eb-88be-4a01-864d-ad18aecd448b',
      '0628bdfa-2724-4a98-a1db-624aa43c4c1e',
      'de965e36-5dd4-4de5-9fe5-f9df29642388',
      'ca1f9c24-1602-4acc-b566-f938a855ebee',
      '734938e1-32c8-4896-b285-8880254330af',
      '06a53e31-84c8-42b1-bd93-b849aa56c5e2',
      '32a921b6-08c4-4dbf-b0cb-0912f8a8a185',
      '2b43be61-37b4-404a-b8aa-02d9014b47e2',
      '7f660b67-14d3-404a-8ac3-02e9282fc4d1',
      '87ae217c-5183-486f-80c9-8804ea87aa6d',
    ],
    return_value: error 'Must override return_value',
  },
];
local agents_intellij = [
  // IntelliJ Wave - 03/21/2025
  {
    tenant_name: [
      'tecton',
      'filevine',
      'gojitsu',
      'egnyte',
      'webflow',
      'turvo',
      'gopigment',
      'purestorage',
      'collectors',
      'onelineage',
      'junipersquare',
      'aitutor-turing',
    ],
    return_value: error 'Must override return_value',
  },
];
// Until we can actually ban users, use this group to throttle their requests.
// Users are identified by queries added in #22313.
local chat_banlist = [
  {
    user_uuid: [
      '12763cb7-e22f-4caa-b462-2d20bc7d2a06',
      'e8e05ee5-89f6-42ff-8203-c488ef224f8e',
      'f585c242-d2e2-45a6-833d-5a4bc9cba79a',
      '1f55615e-c648-44ec-b9c5-e0d5b0a2b6a2',
      '4121ae59-c8cb-4598-a30b-28b85a838fa4',
      '0c36947b-3710-471e-87d1-6ac38e4bab5c',
      '8549867b-0e5b-46c3-bdda-c5f586e40d90',
      'fcdfca65-356a-47aa-863c-5c4d1ac53a93',
      '0d60b71a-0b9e-4897-8dc7-d8c0483126aa',
      'db92b6f9-66b6-4711-bdda-4d47d5a21b1d',
      'a615bc73-6e3c-4fa7-85c5-b262b0581b7a',
      '4a634ca6-e77b-4935-8d73-435e7feacbfc',
      '9691f885-6f10-4e45-a7f2-a7df9cf21d1b',
      '0ecfd906-01bd-4031-a67d-27e4b5453e3a',
      '9a6ffde9-0d7f-4c0b-a487-7e953b40e915',
      '4ce41b21-87f9-4f1a-a96f-c44b751f505c',
      'ccbc8f12-13e6-4cfb-aa50-ed10cf68be64',
      '595c59c4-b53b-4975-a512-7d3a56d4aeb5',
      '787f067e-cac5-4824-979b-f30c9de30cd1',
      'b3b5291c-f791-43cf-8fa8-421feac47e9c',
      '3fd7218a-48e4-40d6-a4ee-7c1f02a830b3',
      'f7dfbafc-2864-43fb-bea4-69ac1ea3c638',
      '955aa537-cb78-482e-a170-31c7ed81e40a',
      'a608bd9d-1d8e-4242-bcd7-3de2b046e81a',
      'b23bb4a4-9072-4d36-a8ea-b8c8d4b1f959',
      'c3abd85b-86c8-4f58-b80f-5b29be632280',
      'ecd930de-02f1-4949-b7ab-829ed348e1e0',
      '0a10bb0b-5c30-44de-8941-5a6c8373eda4',
      '2f1e7a36-2523-4bce-9ebd-aca9fd659f2a',
      'cb5c9f6b-655c-44b9-8bf3-2d2b5e6f3ba7',
      '5cc8aa8e-c57e-4b4a-ae18-9091344f4b05',
      '6f158176-fb25-476f-a2e1-2af70f410d56',
      'c4505de8-4156-4592-90d0-476a68b575d8',
    ],
    return_value: error 'Must override return_value',
  },
];

local remote_agents_wave0 = [
  // Remote Agents Wave 0
  {
    user_uuid: [
      '72d69a13-b03b-4a8a-ae68-41c6e6a99b4d',  // mpauly's personal account in i0-vanguard0
      'bc4b6969-f0d0-40c8-aca5-e221ea56711c',  // syl's personal account in i0-vanguard0
      '8960b2a3-3ac5-4839-b242-b49b57e6b405',
      '9ea8e965-dad4-4ae7-a8ed-38158d7254c3',
      '07616a04-c51d-47a6-abc2-f960c7eb31b7',
      'f99e4466-5f8b-4111-a9bf-adac72e5699a',
      '2b74e6a6-9298-4be9-b60e-7e35ff84bd92',
      'd4a1a1c8-9cdd-4f38-90ba-8cb69617dcc9',
      'b4dcb4f9-bf9f-4ca6-953b-4db193e7ca8b',
      'f82846a7-9032-4679-9c2f-419346896ab3',
      // Wave 0.5-- Content creators for growth team
      'e6b33bef-346a-47f5-aaa6-979dafae664d',
      '092e678b-4ca8-4d54-8801-8d1d737e5856',
      '121363c9-b232-4950-a387-206cd1fe2d2d',
      '40accfae-ef9e-4ea0-9617-b1dfae99615d',
      '8393c37c-e3ce-4ba1-8e7e-d3870b6ba41f',
      '4a179971-9f37-4e31-821e-40fccfb1648a',
      '2514ff6e-aa7a-4856-8be2-4b917003c36b',
      'ea9df087-3a33-408d-a6b3-b0da61f95e3a',
      '55f6b7e3-f218-401e-9770-ad2efa3bd078',
      'ad5c161b-32e1-41f1-9248-d48a2880df35',
    ],
    return_value: error 'Must override return_value',
  },
  {
    // aitutors
    namespace: [
      'aitutor-mercor',
    ],
    return_value: error 'Must override return_value',
  },
];

local remote_agents_wave1 = [
  // Remote Agents Wave 1: Release to 53 additional discord users
  {
    user_uuid: [
      '4a18ed91-e3d7-40f6-aa30-15ac732ed566',
      '05af824d-2c1d-476f-a008-550c571ad891',
      'a805c90e-6168-4acd-9ec0-c4247fac00ae',
      'f4bfcb9b-ca86-4c0b-9dc7-bd5f380266ce',
      '22eb734b-1bf1-4a46-9d50-45c7e9ce7c66',
      'ee05c9a2-38a0-48d1-826e-7a8bc1cc2f9f',
      '087097a0-ad29-4b5c-89c4-bcfc7ee737cf',
      'db4eddf4-d98d-4e00-9f57-5860ddf9d99f',
      '1a2ee20b-0c0a-42e7-b921-e73f228bf8bf',
      '45c22140-2d14-4730-a2b8-8bfb6f87d06a',
      'e7f2c54d-96bf-4e12-a243-092575bd49dd',
      '329b031c-9ded-4c45-b1a4-d8d83a2d94cf',
      '6219e2b7-ed41-46f7-89ce-153524d26038',
      '1cfb7445-a732-4884-a23c-fad987844f28',
      'db27302a-42a3-47d3-88eb-c9c61effafb4',
      '82606abf-3b94-40e5-a4d3-81952905ea45',
      'f7232894-d418-467b-ad4c-812ddfc5850c',
      '812ac506-6f2f-4f6b-b39f-98f31de8bd5a',
      '06d2f7c1-a863-4337-8cc5-48dc0bc7fc3d',
      'f3be1f08-a9b2-42b9-9daa-acbe69da1237',
      'e813871e-257c-4ad9-bcb7-d8c740002912',
      '5ef5e106-7575-4755-9fd8-e29c002dfc2f',
      '15c5b99a-9a8a-4940-867d-1725cfbb4b07',
      'b0ba2a5e-1af5-453b-b658-549c8d5cf42d',
      '16b74cb9-617a-4b7b-9013-00100053f3a8',
      'cfe89815-1469-42e0-a941-098ee673f04d',
      '5f3734aa-efb5-401e-a99e-1c9a505c42a7',
      '45a67c9e-2119-40b5-990a-6341a0c53b03',
      'be36cde5-d75c-4f4f-a7c8-fce110780f12',
      '7b763548-d3f7-4f0d-b384-5f1608ee4efc',
      'c5214c6d-e337-4621-8eed-f479c23df469',
      'b634fff0-1302-41a5-99f6-439b2f1d3def',
      '2b783bee-6c84-41c1-9364-dfe7a85dbd75',
      'c0184e07-2497-422f-8a2c-f4d88c968fde',
      '9f45fc18-2bc0-4aa8-b768-8bcedc7a92cb',
      '451f9782-e35b-4229-8f48-b5606af372c9',
      'e75b6144-ab0d-4320-bef0-a3e1121d4cdd',
      '4451825a-f4c5-4527-8c91-41174ed66df1',
      '82c70e60-480e-48ea-b24f-4d3556553d38',
      'cc5a872e-3a85-40b3-835e-edc6a43ca7c3',
      '756dd01e-159f-4996-9727-b7d0927fe225',
      'cdd797ab-74bf-415a-a0ac-2fa7753a83e0',
      'a08de044-bf4c-4f27-a3aa-f6ba96efa2f6',
      '44f0d928-3fc9-42b1-a6ac-cfe810ff54db',
      '94cf400f-d140-4e5f-884d-b2b87d6689ac',
      'c6220d78-8d92-4529-b9d6-514dd26c5df3',
      'c91e8f3d-4fa9-4789-89f4-c889a2948483',
      'e2138759-246b-4cfb-97ae-c2662c1d0a75',
      'c1e5ad38-c1c7-4467-9bd2-f3e93441e0a7',
      '2ba1de16-d0d4-41a5-97c0-476951451f46',
      '81e2d708-b1e5-4b71-a05f-359d14be6c45',
      '3d2d4d93-8276-4e35-9c7b-c49633965ad5',
      'd0133efe-e915-4a8c-9e2e-27e5beb5087d',
      '133ec489-643a-4643-ab53-fd37e6e42f21',
    ],
    return_value: error 'Must override return_value',
  },
  {
    // pentesters
    namespace: [
      'pentestra',
    ],
    return_value: error 'Must override return_value',
  },
  {
    // pentesting
    tenant_name: [
      'ysecurity',
      'ysecurity-cmk',
    ],
    return_value: error 'Must override return_value',
  },
];

local remote_agents_wave2 = [
  // Remote Agents Wave 2: Release to 512 additional users pulled from discord, a select group of
  // enterprise users, and users off the feature waitlist
  {
    user_uuid: [
      '7803e9ca-676c-42a0-892b-a7ac4c080c59',
      '6f30bcd4-e9d7-4209-9e06-f552df2062c5',
      '4d9107da-a534-4f5f-9f28-0c0dbadd36e1',
      '352e6587-7e75-49ec-8518-e7bcb3ff30be',
      '0e42a81b-6c42-4307-8d3a-a2687bce9560',
      '383b50ee-9122-4c99-9946-54e53d5d6de0',
      'b229db4d-69aa-4927-9ef2-cca0f8b55143',
      '852fcc22-aeed-4482-93aa-846a6c94bdcc',
      '37acd4ce-c70b-4911-9d1c-c0661c984bb7',
      '389806f0-6ab4-4964-a193-8aa6cefbf7d1',
      'd72fc2d8-7009-47e3-a66a-d0eb1e4062e4',
      '5490946e-f06f-4586-8c88-41cd39330271',
      '********-c1e4-4816-a91a-983c796fe2ed',
      '0cf6a6e1-7a87-45c5-967c-a518d5f33439',
      '0a63843d-f8fb-47f4-b86e-0e01be8e6160',
      'a5527505-0472-41e0-b2bd-15aae3b806c5',
      '0bc989eb-500b-4b5e-b5aa-1bc57c4484cb',
      '2fc0b02d-9cfe-4736-82f8-53c079d04d16',
      '36262324-e9e2-4abd-a144-cb0e6c6343d2',
      '4c8e82bb-6f44-4b77-a9b0-826895f7634f',
      'c7269372-d3ac-421c-97cc-9f262a9c4f01',
      'ea992309-2f07-4c10-bb84-c1af20c8c45f',
      'f869c168-c7a9-4736-93fd-ff007a8b3eb3',
      '6035e24e-ce01-4e4d-80e5-851e4d2db0c4',
      'bfd2068c-dbee-4997-8f7a-0e9bdc8db95e',
      '6cf20d33-c64e-422d-8183-7b79eba2330e',
      'd252fcda-d100-4eab-b0e4-061e3a189402',
      '2c58f2f3-69d9-4829-bc4c-b89ac3c14f37',
      'f31e85c0-2906-4378-b84f-d91df99a3bf0',
      'a7badbd0-c629-4520-9fb5-426eadfdbca0',
      '706e85b4-6b4c-4023-8748-7f7a950d365b',
      'f492c2a9-932d-410b-96f4-4aadf95af595',
      'c825cee9-15b8-42e3-adc0-2cbcc5e4be05',
      '6a19b5e2-f620-4b8d-8e15-7406ee4ee032',
      'c767b46d-9832-4c5e-b9fe-2676ba906743',
      '53ab33d0-13b1-482f-8a27-102ebdf403e0',
      '1606aa1c-2812-429d-aea6-d614f16d60eb',
      '92cb6218-a905-40a7-88c4-bcb8782b4c20',
      '35859b6a-7466-43d5-8135-06971c332675',
      '370c017d-f3b2-47de-a6b8-77ce97cbb40d',
      'eecc8a7c-d430-4070-bdfb-302e71281dd9',
      '98156160-9a6e-4161-9d7a-422119e94af4',
      '6eebb7d8-7db4-49cf-a237-f662592b14f3',
      '452a9339-2e61-46e6-95fa-c237a328d713',
      'ba908b6e-4e23-41dd-b308-5c082794826c',
      '6adf68ef-e4b3-4f28-89ff-c90e1fc80040',
      'f66bce6e-5b58-43f4-a96f-317367ef1dbe',
      '2bac2982-0e69-4fe8-b163-762ca0ec16fa',
      'a7d4f3e8-23b8-48f1-a51c-82f5e4174ea4',
      '8ead74b6-7d28-4687-8771-ec24e15ccc61',
      '44f8b67b-28ce-46fc-80fb-38d7e49f2136',
      '8d28cf10-3aac-4fa0-b73f-cdd449b997be',
      '924cd755-147b-490d-bb26-45f29e146025',
      'fe3a7e09-7abd-4fdd-a26c-2f3da29d5ed9',
      'e6b4abbf-b170-4dae-b2cc-5d743224b934',
      'e3238542-142f-4a01-8410-c1eb57a9a941',
      'b725a496-f3e2-4b90-9dc2-5a4feb578471',
      'fea53521-0217-4e3d-ab2b-f8eb8808c777',
      '2ff851f4-ee3c-42b1-a4b3-378e4b183d90',
      '78413599-01eb-48aa-a278-96a5ffd220e0',
      '1313733c-9efe-4645-a62f-ea166d9a8207',
      'f053d0c5-4b36-49ba-975e-b4373d607048',
      'c0700673-44e7-458c-b789-fb180cbf8050',
      '1ce6ecbe-a7ef-450b-86ac-987619a97fe0',
      '202cf680-1af1-47e3-9d5b-c25612004d01',
      '4f928415-7400-43c8-bd51-24ef26076cc9',
      '500c2364-9427-4634-a949-8137ede22cc4',
      'd879a9da-686d-4e95-b067-c2dc668ec65a',
      '456e2d73-4ee1-4ed9-9dc8-f25acd876748',
      'ca632eeb-9373-4846-a8e7-9fd0e0de5a38',
      '25617149-289d-46ea-86bd-ca0d9e89c4aa',
      '123061d0-7561-48ee-89a9-97086e595e99',
      'c272c35f-8d97-4494-8b66-10090a3282ba',
      '5732da9f-fd5e-4134-8e86-6e6342ca95ba',
      '2704ddb7-2989-4fb5-9d26-cefa8cf3110f',
      '9132fc23-0426-48e8-8d8a-e42d8da8324b',
      'c064d3e8-2032-465e-8529-8129d3de8658',
      '3a0d3acd-5b34-42b6-aafd-9e26e502d1f6',
      '8f73b35b-b420-4f90-add6-73a1bccb6396',
      '1d9eb324-1bbb-4f6b-bb5a-6f7cd1ccc4bf',
      '462e989a-380a-4383-847c-876d817f1f52',
      '5c89fca7-60ab-4c31-a0b1-cf09b91ec7f0',
      'a6bd5431-a532-467f-96ee-7d9e687d8827',
      '96f87bd4-92ac-4317-a3c3-56d1360b732c',
      '4474c549-1449-406e-a5e4-ad7cc417b569',
      '78f378b1-2b9c-4af9-97aa-f9b6ccbf5a06',
      'b66a6929-39fc-44a7-8a56-8311e3b6e189',
      '2c6c10d9-64d2-4e15-9404-5be0ced84dcd',
      'a1f1de58-5460-4731-97da-d8368d0d0d1b',
      '3fba9784-3fbc-431b-8019-e64c688baf3e',
      '797c3049-7787-41c2-9333-73b6eb69b834',
      '1d9fa183-c2c9-49fe-9dd8-221784da75f4',
      'ec5f14f1-ee41-480a-be41-a9e09067ad27',
      'aeca5c47-df6f-4d6c-a88f-7430b88a97cc',
      '4ee89184-9547-4520-a477-b8e01569e3e2',
      '77d6504c-c84a-4a0f-bfbe-87fda25b1601',
      '250f084e-830f-4f18-98bd-43d2f3f7f59f',
      'c8b73303-36dd-4335-be7a-007810d418cd',
      '20aeee62-b05e-4769-986c-d1baae89e4e8',
      '0bdf0478-dc02-455b-9eae-a9529c68961b',
      '31194f98-0240-40e8-b1ce-0a057f9e2a85',
      '28e09583-7192-4eba-9799-ba2b33b04350',
      'b21bb5b0-f1e9-497b-8cb0-742158523b23',
      '4fa164a9-3385-4408-8fb0-6d68efc46cec',
      '22f8ccdb-c50c-4a9c-a41f-e5f9813380e2',
      '25ff6227-f65f-4db8-acac-f0019ce99195',
      '911cab75-32c6-43bd-a31c-5b637ae9debe',
      '9f4dfd6a-8ab4-4dff-930e-60ec062c7d7f',
      'd0521578-0da7-4969-a5be-59039fd9be94',
      '5dce456c-c5e7-43a0-93c0-b2ab9dcebba6',
      'adf06d64-ef75-4519-ae87-611a1762ab41',
      '545e6bd7-91cb-4ea1-af37-ac7ca4dede8a',
      'edb4a820-8b03-47ae-a839-e612678e1b19',
      '95abacba-49c1-427e-badb-d9f80241e76b',
      '57b349a7-ed4d-4bcf-83b5-e6d61c1f84c0',
      '31d1fd38-e5be-4770-881a-90a7256ac531',
      '5d482c5e-dc9f-4fb3-90d7-61d17a37cf9c',
      'b9d23b52-cd46-47c6-bbd7-591dff2769be',
      '593d17e3-5db6-412e-8141-d8f04dc5f151',
      '03e9d9b3-9c1b-4b96-95fe-f2672f6cc034',
      '2d52e065-cfee-4a4a-91e5-df88ddf08a73',
      'c3be26a5-d8db-4d16-ab04-149eade30163',
      '515da89c-ec15-42a0-800c-ffa29d72fe08',
      '4488068b-bf12-4c99-a3dc-8882d8f5f393',
      '37842278-df0f-42cf-a1b0-79396a5e317b',
      'd2263d6c-969d-474e-9bd8-0292ef2f5c71',
      '462c8cab-32ce-45d4-8067-1ac19e432320',
      '37193c9e-d338-4282-ba32-600940a50424',
      '827bb016-5933-497a-a39d-4962455510df',
      'd550b69d-f8bd-4d54-90db-c0115162bbb0',
      '537024e1-0857-4db4-ae47-b312f095691e',
      '252d8b23-122c-485c-b18e-b553ebae0dd3',
      '89b6e120-f2e3-4af0-8c9d-ebf0cfa35260',
      'd5c20ca3-2e0a-455f-910f-4ca80679e118',
      '179292bd-07d0-4835-b77a-3e0a261d8deb',
      '74cc0b89-5e68-4bfc-b36e-62057999c2f3',
      'd82869c0-685f-409f-bad6-eaaab9500f20',
      '4d98499d-b2bf-4212-8310-a524c6a86709',
      '21ad67d9-4cf8-43cc-8545-c68f33878d18',
      '284d668c-350b-404a-85bb-3ebddf486e7f',
      '4cb6e1b5-7cd7-4cf6-8c1e-a6d80fc8d84a',
      '53a5d0ff-d3f0-46af-ac5b-6c9e4a479590',
      '2b8d9915-83bc-47e5-bb58-398814b51294',
      '94238502-f006-4107-a42d-66cf2a26ef31',
      '62ee5565-2435-457d-84fd-03d197e457c3',
      '72525162-3b8e-4d8f-a144-4a53662740f2',
      '46fb67d3-d3c0-4e30-81bd-a28e35700319',
      '4f53f2a3-1f81-44c2-aff7-e801d97f4887',
      '4273bfe7-ef0f-4598-90c3-6d02f7b5ed3d',
      '6ebeba60-7c77-4919-9e83-28d14c62b4ab',
      '1e59b9bc-4be4-4c4e-b88d-6b077e8a15ca',
      '566eecac-b795-49b5-b1b8-b96562c70d96',
      '185e9502-94d4-4c8c-b7a9-e590327c3ffc',
      'c8a993c2-c4a0-415d-8151-7e4d33df5bbb',
      '3f1dc451-fb9f-4366-9b7c-728c2c5671b7',
      'cbb9b8a8-0cf0-40e2-8d2e-0cc384d406d0',
      '4b608d9f-f271-429e-9f74-9039904c5876',
      '4e65d622-eee0-41dc-89d7-889fff4375a2',
      '69a732a8-e8ce-4958-a925-5c685826d909',
      'c757fce8-5905-4674-b6a5-639a1f34d2ff',
      '9f00f80f-56ef-43c8-b0d7-daca373d49cf',
      '1b703dd7-b03d-4d8e-b5e9-e7336c48014e',
      '3e0fb7d6-a6ce-465a-a97b-0ec33d944e46',
      '3e07c8f3-9c40-47a0-922f-012bd7d7ba7d',
      '8e675954-664d-4d52-b7a5-d53d84a18205',
      '48965526-3343-4a2e-957e-9ddffa0cd0bd',
      'daad4bca-595f-4cea-8552-77bd7d5697bd',
      'c0f18533-4186-4597-8bf9-b80a9e00d8a6',
      '1dbb7401-d336-4b6d-ba8f-627588c0bd13',
      'fab0c1ff-0911-4c33-858d-80109e7abd1a',
      'cd93de85-0ac0-4cd1-83de-0bf3ccab2e91',
      'a24eac7c-34c3-4b5a-98a9-daf8d2a46c3a',
      'bccdde63-e6db-44c5-a6d5-af73e07e4549',
      '89bb7387-aa35-4072-b94e-38859ac9ad2a',
      'baac3cc3-20da-4dc4-86c7-09a4689d45a0',
      'd2b27216-6960-432e-a795-4ff4e824b5b3',
      '1bd60631-0e32-4fed-bfd6-0c0610963013',
      '877ed478-a2b5-4349-9816-8e834adc4734',
      'ca4aed24-82d6-405d-9375-a4a41325cf90',
      '3dbfe6a5-14e5-465b-9c04-4bc302a7436d',
      'de98457c-fb61-42fb-9c60-dbc5c42c6777',
      'dcac7005-bc4e-4472-9a7d-6ba94bf31726',
      '3bf76680-b12e-4382-8ca9-f8f2abb183bb',
      '2437dbda-6e99-4edd-adfd-af9839a09eda',
      'dd40b000-8cec-45c9-821b-d4bd2d5187b2',
      'e5f58091-02bb-456b-a82b-816c6e152a56',
      'b91cf981-c7ff-4094-ac24-97c31b55bdd7',
      'ce4c8c7b-8d07-4fcd-9284-3d60100cf215',
      '958d1d6f-bfc8-442e-b201-4236019401d0',
      '096e5d51-c404-421d-bea3-bc0051554bd5',
      '18919bf7-e5f3-4fbd-ad76-e259e17f35db',
      '2e91a862-dc57-4645-80c2-0e65c0732fb4',
      'a51dac46-822f-4cbf-9306-82d95beff516',
      '7310f9fd-932b-405f-ae37-ca6b8b25323f',
      '98ca44ef-8dc3-4605-976c-4f5b7115c809',
      '4503785e-afbd-4adf-9422-8f3b9930a6e0',
      'e1e7fa50-598f-4d60-93ca-f85b211b90e4',
      'f95fd966-b7df-4834-85fc-20b3bd111484',
      '4506f0a2-91b5-49d4-a4c9-053d6eaded50',
      '7c782654-25f3-4864-ae2a-e3d99aa76212',
      '432fef11-f67c-4eb7-8780-f0bb753e6360',
      '8e16359f-ab6a-4c81-b0f6-8e1593fa536b',
      '2d3e710a-d26d-44db-b9cd-47ad2817b800',
      '656b62ab-dceb-4c8e-8d89-60b4d225b4e3',
      '630c5897-ebd2-4acf-9bbb-417df9118481',
      'c6d2c37a-911e-4603-a0c6-0b5beaf21840',
      '84450aa4-fafc-46a8-998d-63b8c0d44d5e',
      'ac270e7e-efd2-41a8-9b57-112fae4fadff',
      'ec5799da-4841-41b5-b1ae-e8df21453202',
      '1e55aa8a-f74e-44e6-a789-0f580e1e7ec3',
      '0c6fb6cf-e984-4ebc-ac06-1ebad0c1ec07',
      '54c433b7-1dfd-4ccf-b7f4-b7bb941d6aeb',
      '9da5d3b7-dd10-4110-9291-9ed291a6baab',
      '904dc997-7ab0-4ce2-b749-27e558f6b711',
      'f3795d73-4142-4f36-898a-3a33185af6aa',
      '5e3448d7-c823-4056-bee4-6b971d0026ce',
      '30d8a020-807b-4f83-88ec-ed827290d37f',
      '6d3ead8e-8e7e-4021-8346-0308744b6098',
      'f13929fa-7b38-4f0e-bc2d-af65026c9dc3',
      '1526f9c4-9f94-437e-8c2a-d44531e885f3',
      '13866a13-b3fd-4d30-93fd-e471651fbfd7',
      'e0be3919-1650-493e-bb6d-f33acd82a940',
      '12de3188-683d-4fc4-a5b8-3f1dacf765b4',
      '8b6ce91a-2eed-489e-a7a9-d38639983352',
      '98b0960c-ce22-4602-bc3b-518bf024e712',
      '2137863d-e068-4e68-bb1a-781cea6d3e4b',
      'eb259ddd-d3fa-4e4c-aa0a-aef4aab91dc1',
      'c047fb75-c587-470b-8476-f39df4269153',
      '8d6174b2-07e4-42a6-ad40-ff95e825b5b3',
      'b583ad3c-1784-4927-af98-09d3d196c338',
      '108b7d3d-9800-47ba-a2a0-076740b0b90b',
      '24e51fd9-c89d-4721-ac23-10f4eb430775',
      '59172155-a654-4487-ac51-fd786e983bbb',
      '1af7b81a-5c8c-4827-b985-ed3918c5b1a9',
      'c07957e1-34eb-4988-a1ea-877331cbc47c',
      '40a5c710-b937-4587-a97a-2bb9256ac9c0',
      '87d7f302-9216-4053-b2fb-7ccf48f00f48',
      '4a7b5571-6dec-44b7-9d45-1f479c84efdf',
      '612bd7f1-1d7a-46ae-8991-fc708245290b',
      '1b5d763d-f8df-43a7-8637-04e6c8a6deb3',
      'e4e11a7f-a13f-4a87-adc0-f7c31e52b784',
      'ce7c4234-d326-487d-ac17-caad244c7ab1',
      '28c49d91-b9c9-4b91-a301-17de7cfed7e4',
      'e54d97d2-26de-4189-bffe-aed314f0c36f',
      '840551eb-6abc-4131-be14-bc8561c0a414',
      '76c1b8cd-7bda-4302-947a-d6affa874eaa',
      '1f0e4c6b-29f4-4c6f-a1ff-51e06d5478d4',
      '029b4406-c51e-482a-b399-eb88a91a905c',
      '1c56cdd6-1108-46e2-b820-6bc745885be0',
      'd2660b5c-7e1b-4708-8921-6b107abacc4e',
      '6a21e0c2-4071-4f76-953b-b542f07df90a',
      '942009f2-fefe-474e-8146-bf772bf3ae87',
      '5b819d46-1cd3-4838-bbe8-70de14138dbe',
      '7348ccaa-8b23-4072-96ca-b7fc43c8d620',
      'c1ae8d14-76b2-4968-b1a7-d8d271b19fd6',
      '0b4fa849-e646-4ea5-b188-3b4c17a4ebbb',
      'ec1ab4e5-15ac-48f9-93c9-faa6fa1e97af',
      'd27c3b9a-bc4a-4b34-b631-4379b5cb71cb',
      'f3a3e975-1198-4171-b1ba-1f758a996ae5',
      'bd0daeff-aa5a-4698-8a6e-acf5dd357ecb',
      'f4ef0d26-326f-451e-978d-************',
      '5700eb93-91e8-4969-8ea3-e4a030707385',
      'cfb13cc7-d008-4332-886c-440f889eb482',
      'f1ab2e14-bdef-4d84-ae01-ad4b31e54637',
      '4e4adfad-15f7-416a-b448-68db9546af5d',
      '7209791b-ee73-41a4-9132-c631aa5e85d1',
      '1dbe8742-bd35-4c2e-b39b-e5fc344e2647',
      'd09a4c06-d97e-42e3-9686-cbffeaa06d23',
      '7f40b9ae-536f-4799-8dee-0a7cf8e1f6b8',
      'f9107be1-2237-41f9-9521-60d2eccb3b8d',
      '462e3a73-904b-4972-abab-4ba3cb8da553',
      'd2f5a3a5-fdd7-4079-b4b5-16e58c12b3a9',
      '89039a6c-0128-4d6d-bbd8-139384d9cb25',
      '3146b79c-8e42-4ced-bad6-7eb95ef8c131',
      '4a6e3d6f-aceb-4e29-9bc3-29d102ec6d96',
      'a5638ccb-f704-43be-9b3d-a005f7df53c3',
      '428a2555-292f-4370-9c63-30479ae6eae9',
      'af7c45c9-f27c-4ad4-bb29-22a610edbc4c',
      'ebfb3078-1333-430d-9f57-13f96b4b89d0',
      '717d638e-d176-45f5-8d40-b1f478f9afde',
      'f79d5837-e6f7-4f8a-9010-913ae6e2381d',
      'd2c5abee-143f-424d-97b0-a3e294d74a67',
      '6d66b884-72d9-43bd-86b8-e686715df6be',
      '84717a90-951a-47a2-a851-dcff5e525d22',
      '51382fb2-1e8e-4f99-b8d8-d1f6d0fcf240',
      'bf3b73a8-a5fd-409e-8501-036467962527',
      'd21e5bba-00fe-4e4e-89e1-43ce9eaa3d72',
      '07ee890e-5bcd-49ff-87a6-29f6303afe8a',
      'f251929f-4f5c-4b47-8761-6b250ec4477b',
      'c96523dd-0937-4dd3-b273-aded74bb6347',
      '07b9803d-bf89-46ca-bdd2-cca383957872',
      '139ac407-769e-46a5-b37d-f37492b5dac4',
      '60dc292c-c193-4073-a804-e32bd04f5084',
      '67903cea-26d6-4459-a3af-c45fd58dcac1',
      '81332f5e-8a0e-4ec6-878b-0deb92fe3c6d',
      '8135dd7d-235b-4014-b5ab-4d5a5f62c0bd',
      '89efb29e-f4c9-4208-b728-dc0cad252cc3',
      '9b0a43b1-64a7-48f0-8280-f1daf8e2433b',
      'cea00a2a-d4da-4e63-95be-0795b91c418e',
      'f335042f-c481-45f6-85e6-e2bc5f4c1390',
      '02a3e191-a96b-4182-afa3-5f3f1979e117',
      '5fa03029-6e88-43f6-bce3-ca0103b3d42b',
      'ef23bcd1-29e9-4af7-bc9d-bce3503ff8ee',
      '638a1dc5-e89d-49cb-8f6b-42c990fe87f6',
      '784bb0a8-2765-4cca-a2ee-d74bbe74bae3',
      'b3b7b403-d6c8-41f6-a28c-a6a144ed70ae',
      'a62ffba9-0968-4226-89a5-5141fdd542ce',
      'd2654144-ecfa-403d-a7a9-dc52622bc6e1',
      'ae7df84f-9ee9-43d9-88f6-1e0e0bf6d9b6',
      '9b44e1d5-fdbc-4292-b714-ad5684286bc1',
      'a4347558-56d1-4844-84f8-9c23140e3482',
      'a51cd476-3f9e-4a04-be69-30cf99a22b49',
      '6bf66b9a-91a2-47d3-93cf-167beb90f914',
      '407cc514-f87f-45f6-8b91-f8b904f973b0',
      'd0b3fb07-6535-4e86-a176-54836fb78631',
      '1cbeb0ce-0819-4306-929a-674eb9437ebc',
      '1deed707-5034-4251-9163-2695fa5843ff',
      '8adbe553-3632-439f-8fb2-d36c8881168b',
      '7740f3ef-f7ad-44d3-b1f6-cd17df8abad0',
      '45743860-be41-4fbe-bc01-2a2da74183a1',
      '4284e975-4208-40ba-8172-4b364db169ab',
      '794902d4-ffac-49e2-a936-e8b8292d5bd4',
      'e420ab5e-eb1c-4090-b0d3-392c6f9ea23f',
      '1681d4da-2fb2-4e5e-94a9-363b1fa3c635',
      '68d96c8a-24a7-48c4-b68c-9185a89937bb',
      'fcd77435-e6e9-4a14-ba21-8e39cad78974',
      'a2d90e45-07be-45a4-a66a-d461b6058e48',
      'c16b2e2b-b437-451a-afcc-f29a7dbf085e',
      'cd5a19ab-600e-4737-88f7-a863adf02ecb',
      'd502fe13-3e81-41ca-b6c1-91e605c0e28b',
      '1eb3e021-f79f-462a-9f2c-16ce4c30cd59',
      '058e42e9-1d37-4ae3-9485-26e27d6c6496',
      '5953cfce-9f49-43dd-b769-2bc7c05242be',
      '3026b56f-1a9b-48ab-a366-ebc072d33914',
      '5de978b0-8540-4d08-bbb4-4bab95214eec',
      'a5c44701-ba4f-4dba-821a-fb0b42fe6f09',
      '95282f8b-e684-440c-9f2c-6359aca3e130',
      'b8188e00-287d-4a25-b7b8-2033574f2baf',
      'a60b7bf6-986b-41b7-a3bf-b5cfdc06b44f',
      '24a63471-9ade-4031-8421-d8b8da6316cb',
      '34f12246-056d-4a0b-aefb-bdd645ad34a6',
      '8d8b6ed9-7aa8-4230-ab42-b755591aac86',
      '9ada5da6-0e6c-4091-bf00-ba769232ac41',
      'a80aeb3d-e91e-4fd7-bbf6-76e88b46f913',
      '23e8f3af-7134-4c23-a6da-4daf266d1f19',
      '5bbf8231-e951-40ce-864d-47c1725374b4',
      '15bfbad2-10d9-4335-9cb1-add2c24ab839',
      '61605e4b-3519-4a37-990a-c8c6311cb7b8',
      '0aa301a9-1b15-4453-8cd8-561b41338228',
      '0fd68da5-f50c-4b9d-9a74-08dd5c3deabe',
      '4a712fa9-3dae-472d-af9f-658c5ce0aba9',
      '4e4c409e-284c-4675-a8b1-441da18dcb76',
      '96d9ffd5-c8e2-4217-919d-b5cd99a159ea',
      '66f3c11c-46fd-492a-be14-c0651d09b356',
      '2cef2b78-8c55-42e9-84ad-21a4d3b1eaf7',
      '9d5767ac-547d-4252-be06-18d44a102cd9',
      'fd980359-97ba-4f5b-b244-311ccb1f6ce4',
      'c582a681-7efc-47e5-9936-a7bfe1c13a34',
      'd04af37e-3e54-44ac-9159-2418708537e0',
      'a4047b49-0d10-483a-a59e-d9633c192aca',
      '50736e17-e380-4ada-8f24-827b1dc86165',
      'fc8ccdda-5630-48a4-a708-6ee70251f623',
      '2a081aeb-7678-4767-a6e7-c727f95ba6b3',
      'c81f851a-51b6-4d75-908f-617d088c0748',
      '968aae54-3803-49ee-acca-6c4ed50898a9',
      '540fff77-1ca0-4913-ad40-f014e40275c5',
      '230dfb34-017f-4b77-888b-b60c564efbba',
      'c782b669-cd3f-455d-a06d-9b7be2f42a9f',
      'e5be1835-487c-4e95-8398-936f53734b1c',
      '67778743-668f-408f-a829-5dd68c87398c',
      '79d8e341-fb18-4264-b9aa-e11b3db502cc',
      'e9fac2d7-b49b-4f26-831b-ab9f45ab177a',
      '85789e4d-737f-4b11-9e27-e3ce4e193f9f',
      '311aa972-459e-4412-a61b-c513eee54113',
      '4dc9616f-6d78-44b4-8575-33502e53022e',
      '6d8d8dea-3c99-4e9d-bc98-d7e879f32ae6',
      'f04fc51d-954e-4ac1-9b88-f6ecd1cad026',
      '36633b36-475d-4f4c-8129-dc50c2cc999f',
      'ed63ca11-3b8d-494f-afcd-963f9dc699d2',
      '93b60e18-ef53-4f85-862b-dacea8c31985',
      '0ce6c854-0f73-4213-9270-3278f47fde50',
      '73c11b70-dc18-44e5-a09c-721a4c9f2f64',
      '5b8074c3-25b1-4d18-ad56-8158d41b5162',
      'a6b888d5-f991-47c1-8237-331483cf5fe4',
      '2a3c7ea8-8b80-4d27-941b-66e41fd19ac4',
      '1346bbb4-e2bf-4d0a-9875-866ac4ac7be8',
      '635392ac-d6d4-4952-8637-23ccd31c80f1',
      'f0ee5d4d-0fd2-4eac-a86e-4a375e379c23',
      '27212f27-cb67-41bc-a0c2-068c5d7af90d',
      'a64cc57a-524b-43f5-ba01-be11c048ab9f',
      '0a672935-2589-4262-b2a9-04eb0922465a',
      'b9d09727-1776-4f3e-a356-6e39291326a1',
      '04193f22-b562-4acd-861b-ae4154848a53',
      '24b2a2a2-dcef-4b2e-8593-145c1a8ce300',
      'dfac2863-c58f-405a-96e0-a3d7b3bf6a8d',
      '9d9c021b-4a45-48b4-8ea8-68c4426bcf02',
      'da0a89cf-7fa7-4097-86b2-35d1c046d529',
      'ea0b0433-2cd2-4fe7-bd14-acafe6def15c',
      '1b079754-55ae-4861-a337-681600555a24',
      '327289e0-4226-4c5d-a1e8-ddb756c7072f',
      '31c154e3-19f2-4764-a634-09e0b691b266',
      'bba3f41a-3ac3-47c7-81df-91b253a60131',
      '36e35410-c63c-4c73-9e7f-8744d36a706f',
      '58d1f9c4-871b-4a64-bbe0-c3ce69b18118',
      'a2be7a88-904f-479a-bfe8-e6409bdc6780',
      '3ddf6c84-2790-48f0-9b01-b4282c1e368e',
      '16c1db9b-9873-4ed1-b936-2564a35af6e2',
      'cfb4affe-2f06-4ada-9662-875a43ad6a62',
      '16a82357-0150-4dc4-a6c5-ddc93e811c54',
      '2a68b122-2545-4804-bac9-5a10e07b7693',
      '4182e69b-966b-451e-959e-15762f4468de',
      'c2d3d994-a736-41dc-a771-7719747a55d9',
      '38d7dc90-06ec-4c41-926b-5f7161b850fb',
      '005960bb-77d3-4d14-8b2a-1a60a0d233ee',
      '006dc0bf-3913-40be-a592-194a84bbbc3c',
      'a8b2ebc4-cda8-46fd-a8c0-318e1009b1af',
      'f9d9a35e-78b3-42c8-87f6-64be4282a2fa',
      'befb3e47-77b0-4fd7-9347-68a1991759e8',
      'f75d7a12-cbad-41fa-9692-60caffeea8ec',
      '8066d2a3-61fe-483d-b0d4-2fba114f971b',
      'a43a4cc5-40f7-44e5-a8f9-91c017eceffc',
      'db05f326-48d5-41de-ae23-762bbe8d3b4e',
      '10bf6d26-a450-4216-942e-f94cd7a7ffcd',
      '528125ae-c7aa-48a3-a65d-9646aaeb8f9c',
      'dc81b6f9-42a0-46a8-bf5a-8a17567feae4',
      '9008cc89-ebbe-485e-8732-6372a67fcb2e',
      '43a644e0-f1b9-493b-98ec-de491dc71f21',
      '7be5ae98-2500-401e-82ee-df4fa028436e',
      '2fde5f36-421b-489f-9673-8ca8f457849a',
      'af66f1ca-1c7c-4e6b-ad1b-38ca9998dc7c',
      '5ad0f0a9-9174-470d-8a48-2e21e84b537c',
      '2389b76b-a486-4077-a3fd-24bc5fee49fe',
      '07019b76-6c07-43fb-b3ff-f8352b2542ea',
      'efaeb8a2-42bc-4c53-9661-236c38d68398',
      '80a3662c-9831-4dea-99a8-a78ab5093397',
      '50a647c0-1982-4e18-a523-e147a35a9e3f',
      '79d976b1-525b-47a5-862e-c940d49fe817',
      'b9e4c2d8-f49c-423c-a01c-610cd1d44423',
      '6ecba5d4-5ee9-4fdf-9c42-9d24c5c4adca',
      'cc376268-1a0f-46f8-bcbb-4e144222fcec',
      '02506e2c-4cae-410f-87c0-84b39bd5b99e',
      'c81e7e09-b63b-49c8-8644-e20c1dbfe4ec',
      '76482180-1f10-4b67-9863-a88bed9419e8',
      'f344ed28-a171-4ba6-84c9-8fd26075a24c',
      '8e6647ee-3fb5-4aa8-80f0-2edca73505ef',
      '41b9f272-a6eb-4464-919e-446f00fbe9ca',
      'cc52f8bb-7e27-48e0-b79b-17dfdfa03ead',
      '8bca4c44-6b51-45b9-a5e2-717fca23c257',
      '87a481a8-a2a2-475c-a3c7-a02c0efa1f0e',
      '51ad2ab0-d090-47ba-9ed6-2bc461ba556a',
      'ca5d0e88-a1cc-4227-b6cc-ef1b7e366b4f',
      '4a86008e-887c-45cf-bf5f-bc21abbceb69',
      '6c1d26ad-93ca-466c-998c-16ff4bb00578',
      'e02b883c-b3d3-42b1-b3ea-e57f0893d940',
      '9b1f5065-f540-443e-aabb-5ec6b05861ff',
      '45a280a1-b9fe-4b49-bdd8-c2d7b1c811a4',
      '1ea8bb9c-9a82-4c21-803a-109473b6fec0',
      'b8808511-adbd-48e0-a1e5-df5fe8d8b050',
      '9b447884-6040-488a-95e8-675077244526',
      '73646a7a-2daf-4c91-8477-cc6e76249dbe',
      '2f4f6cff-0dce-4b2a-be9e-6b35a1df7c55',
      'e74b9928-65b6-4983-a531-fbfb05b3e6f2',
      'caff0922-ece5-4d34-b4da-bae6f7392081',
      '3aef777f-b715-48b4-a260-6d4bb0527468',
      '9b49a99b-892e-47d4-a0cf-3fa90c48bc9c',
      '18516352-0e3f-4b41-95b0-c9c206e9f8b7',
      '422cd82e-c3d2-4156-9dcb-40ef93afe04f',
      '1f2fed68-3bca-4a80-bc83-c3bf1caa78cb',
      'a0a1d82b-66a5-48ad-8f3f-f036baac2430',
      '29f8fcad-e39e-4fde-a90c-4a2bc9283b7b',
      '201e0926-9640-4ec9-8862-4f60764684c6',
      '48b6e75b-c876-4703-bfb8-cc2980a8c78c',
      'a1f4fafb-6f69-4aa3-8aed-d3edcfd30d66',
      '87c7b718-38e2-4a5b-984a-39b33db7850f',
      '78e37e5f-d2b8-48d2-9bfb-bd039707ccfe',
      '19513b58-eb87-447b-a75c-08742729450e',
      '401ef492-23bc-4eab-a073-3035e39ebd43',
      'fe9c1549-9f13-4c0c-ada8-b4fe45144c79',
      'bbddda3e-b95d-4d81-aaf2-3da2b0c5e41f',
      '44ad708b-25eb-4b8a-90a1-163f1465efe9',
      '852114a4-f7b8-4520-a1ca-2634a5e3ffa9',
      '080e115f-6475-41a3-8e51-7e57f934df53',
      '33765449-9faf-4c43-a358-5be555f5eec5',
      '1bd1a701-df90-49c0-bc43-549eaa75e72f',
      'de9509f4-1398-47ab-96fe-79c39f81cc45',
      '8884d3ef-dbd3-4ecb-8f54-c70092bb7d68',
      'c85d607c-f9ba-4f5e-8c0f-b1fee765b9c0',
      'df27ed53-f4e1-4264-bd8b-a27555083b21',
      '53094d1e-cd99-4539-8ca8-f159e06bb089',
      '6392e231-7c93-4c54-8a16-e638e266a5b1',
      '7df10e84-fc64-405b-8ddc-0221ef35d3e3',
      '1ce502a3-32f9-4a51-9e9f-d57f9b8b7176',
      'c0b6d978-011b-4a1f-a174-2fdef1e1b4ec',
      'e0bf4699-90f2-44ba-a5e5-fcfb3ba15464',
      'b9e79b54-0023-4261-a1b2-5e7cf95ede47',
      // Additional set of enterprise users provided by sales
      '53db2e42-901c-4226-b006-f49dacee9450',
      'e868fbc4-dab7-4d89-998d-f87ffc234af0',
      '08b66715-7257-4cb0-8f16-79820a315f24',
      '14d82513-fe33-40c2-a241-d16083c91edc',
      '79fc3b64-7898-41b6-841a-cc18ade94ab6',
      'ca61e8c3-b04d-4d9b-a330-2711e82f8a42',
      'deebfc6a-9b9a-40ba-a983-d939ee1a2be2',
      '7d823d43-782f-4713-91b0-ca66a1ea8b13',
      'e2cc0f87-4571-46f3-8f10-3c958dad98e3',
      '75f9900e-7973-4935-973c-2c3b87445daa',
      '4a798f83-5dd7-4e8b-a397-51bf26ea1987',
      '0d433052-d142-43a8-a5cf-ea4f64e82830',
      '980a4530-1f17-4812-b423-87acdc064156',
      '7ddac8d8-cd94-4369-aaa1-d30fa13daa52',
      '2d7d52ed-f885-49bc-bf82-776381b2ce95',
      '21931379-e258-4417-99b0-0badec9f09f2',
      '1bab86a1-fd49-4f45-b5b2-fc4fcc6aa920',
      'baee401c-fdb9-4909-8d20-a047c93afa2a',
      '7e688163-efde-4581-87d3-980addbea3e6',
      '20c9cb19-2d6c-47d7-9aa5-f05fe3ab7cce',
      'dc18edc7-b513-4fcb-a9b0-d26fa98739c9',
      'c34d6135-b46d-40ff-9834-094029c261cc',
      '74cc12b8-f1d2-4663-a064-ddc35d8b174d',
      '7c61fbf4-769f-4d2c-9881-260504ac3696',
      '15c5b99a-9a8a-4940-867d-1725cfbb4b07',
      '2f893a7b-7a98-4b45-b583-053ae48044e4',
      '2f22272b-0b7b-4fd5-928e-360fe72f7c2e',
      'cd2d6c06-f1ff-4020-8d1f-63aa234577b6',
      'a0bc19f2-de12-45fd-8a2d-986b4c27fb07',
      '363579ed-d1ec-46a7-b4ef-ce210d919880',
    ],
    return_value: error 'Must override return_value',
  },
];

local remote_agents_wave3 = [
  // Remote Agents Wave 3: Release to an additional ~3k users. This is all
  // users from the waitlist who had an Augment account, plus a few users from
  // the discord list who didn't get in the previous waves.
  {
    user_uuid: [
      'a18b0d13-7b69-4160-a6cd-c25e16e4d41f',
      '27afb8f4-1cb8-465f-8e34-f5e5f5870472',
      'c1ae8d14-76b2-4968-b1a7-d8d271b19fd6',
      '********-c1e4-4816-a91a-983c796fe2ed',
      '0b4fa849-e646-4ea5-b188-3b4c17a4ebbb',
      'ec1ab4e5-15ac-48f9-93c9-faa6fa1e97af',
      'd27c3b9a-bc4a-4b34-b631-4379b5cb71cb',
      'f3a3e975-1198-4171-b1ba-1f758a996ae5',
      'bd0daeff-aa5a-4698-8a6e-acf5dd357ecb',
      '73740fc4-13c3-4933-bbef-8ad49a9dd0cc',
      'f4ef0d26-326f-451e-978d-************',
      '5700eb93-91e8-4969-8ea3-e4a030707385',
      'cfb13cc7-d008-4332-886c-440f889eb482',
      '20aeee62-b05e-4769-986c-d1baae89e4e8',
      '4dc9616f-6d78-44b4-8575-33502e53022e',
      'f1ab2e14-bdef-4d84-ae01-ad4b31e54637',
      '4e4adfad-15f7-416a-b448-68db9546af5d',
      '87c7b718-38e2-4a5b-984a-39b33db7850f',
      '7209791b-ee73-41a4-9132-c631aa5e85d1',
      '1dbe8742-bd35-4c2e-b39b-e5fc344e2647',
      '0bef1722-8263-4243-9f6b-c208afa9884d',
      'd09a4c06-d97e-42e3-9686-cbffeaa06d23',
      '7f40b9ae-536f-4799-8dee-0a7cf8e1f6b8',
      'f9107be1-2237-41f9-9521-60d2eccb3b8d',
      '462e3a73-904b-4972-abab-4ba3cb8da553',
      'd2f5a3a5-fdd7-4079-b4b5-16e58c12b3a9',
      '89039a6c-0128-4d6d-bbd8-139384d9cb25',
      '3146b79c-8e42-4ced-bad6-7eb95ef8c131',
      '4a6e3d6f-aceb-4e29-9bc3-29d102ec6d96',
      'a5638ccb-f704-43be-9b3d-a005f7df53c3',
      '428a2555-292f-4370-9c63-30479ae6eae9',
      'af7c45c9-f27c-4ad4-bb29-22a610edbc4c',
      'ebfb3078-1333-430d-9f57-13f96b4b89d0',
      '4a7b5571-6dec-44b7-9d45-1f479c84efdf',
      '717d638e-d176-45f5-8d40-b1f478f9afde',
      'f79d5837-e6f7-4f8a-9010-913ae6e2381d',
      'd2c5abee-143f-424d-97b0-a3e294d74a67',
      '924cd755-147b-490d-bb26-45f29e146025',
      '8da7b968-ada8-4383-82dc-cc04a7821fdc',
      '6d66b884-72d9-43bd-86b8-e686715df6be',
      '84717a90-951a-47a2-a851-dcff5e525d22',
      '51382fb2-1e8e-4f99-b8d8-d1f6d0fcf240',
      '4f4da038-5a20-4cbd-b485-667e0d8821d6',
      '1ce502a3-32f9-4a51-9e9f-d57f9b8b7176',
      '85e0109a-77ee-49ff-bb4a-b2f8c2ff52c2',
      '77cc179d-b479-4265-8633-64b5ba49eb6f',
      '8d819f10-29ff-4b01-8e7d-70ca34880f9b',
      '766696e5-113c-4465-8111-3983e43d8a06',
      'b725a496-f3e2-4b90-9dc2-5a4feb578471',
      'd2e959f3-c1e8-4062-81d3-08948981f4cc',
      'b07a9505-701b-46da-ad12-2982ca219370',
      'b962ca76-3fe9-470b-a477-27c7d44088e4',
      '5c89fca7-60ab-4c31-a0b1-cf09b91ec7f0',
      '8bde5f19-257f-4081-a329-62562f57eb73',
      '1ca2a38a-4af7-4d30-81a6-4a4dd797a969',
      '5e4c109a-6365-4394-b14b-bfe770d31b9a',
      '19d39ba3-33a8-47a4-82d1-0d6e5da214ec',
      '2cf406d5-7edb-4ad6-bc72-7cb241c64bf9',
      '72525162-3b8e-4d8f-a144-4a53662740f2',
      'ba64449c-5889-4c26-add3-d9f2d8f7b7cc',
      '4de1db7c-98bc-46b3-897a-3dbcd51f4f81',
      'a625e11e-3485-4505-9ce1-8ba2a06936ea',
      'd252fcda-d100-4eab-b0e4-061e3a189402',
      'ada94c46-5bad-4db8-a154-5c24a93235d7',
      '0cefbd2c-e6b2-4270-9ecc-a8c57c44e725',
      'b4a2bdfd-53b5-4617-9674-6dcf603b4346',
      '123061d0-7561-48ee-89a9-97086e595e99',
      'c1e6898e-794f-40c9-8343-42f859c5d279',
      'c2dddcd2-b964-4034-8479-f1bba9c4b173',
      '096e5d51-c404-421d-bea3-bc0051554bd5',
      '3d13d455-1aed-4ad2-b005-2e3bbc3fc447',
      'b9e98b2c-06a4-44ee-85df-2c844655d4e4',
      '3ed9eda8-cdfa-4e21-9a34-cb2b99047a77',
      'a1f1de58-5460-4731-97da-d8368d0d0d1b',
      '229598c7-18da-473d-b25d-f0b19c23e089',
      '5ab7458a-7199-45ea-b647-dcada691aebb',
      '6b2740d5-30ea-4fb7-bcf9-159e6ea61d66',
      '6abdc7eb-3162-448e-91fd-861377c5f299',
      '13d33bc1-8738-4504-80fb-ab4867e98d31',
      'e9e931f1-f067-4f47-a682-9d094d29fdf8',
      '9665cdca-f7d7-4f5a-b940-70987ee3a80d',
      '7a1c2a8c-f157-4278-a611-4c1128fa5a6e',
      '15dd33c2-d770-4c0c-866b-674bec5056cd',
      'db8609c2-8982-4f86-a17a-f428379ef28d',
      'ca3f8db8-d0fe-480d-a486-a13a4c28a044',
      'b92be01f-2df9-4a96-81da-ee3c4f8d0742',
      '3ecdd0fc-bd65-4ec5-958f-6c726e0f897a',
      '47ecf2a5-381b-4174-9606-2b1844c98d05',
      '19763424-a625-47c8-815b-0769ef3a2ed2',
      '1fd0d545-312a-4c13-b1b0-a88d4e4f20a5',
      '44f8b67b-28ce-46fc-80fb-38d7e49f2136',
      '164b9b68-6bdc-40a9-912f-5da4e1a04eac',
      '8b068d74-2a7d-4072-a347-5e1dcd7df154',
      '10fa2605-c9d5-40bc-b65c-2f3dd0e40dfc',
      '23f455db-0e75-4fa1-8d5b-501c290fc9ac',
      '2cfa1f81-166e-4573-b6e9-6b9d13ce2678',
      'e1e67515-1cc8-41a7-a588-aa83c0c35ff6',
      '4456c093-21c4-4ea3-bd0e-aa88962c3172',
      '7740f3ef-f7ad-44d3-b1f6-cd17df8abad0',
      'b6cdeeb7-327b-4e74-8c22-625bf78bdfc4',
      '95c2dc11-30fa-4e53-b456-a50395d4a283',
      '5920c2bd-78ab-4c3b-95f0-00e3667c42a6',
      'bc4f0e10-9ac0-4313-8862-dd8d5825fec4',
      '726fea45-8e0b-45eb-9c17-23074f6b3a01',
      'ecbc8e44-2ccd-48da-b7b5-ba21d06fc8be',
      'fa66c653-42f5-442e-8cc3-f1f5dae096c9',
      '0ab44d0b-ec96-4fab-8753-490d8589c59b',
      '1bd97b5d-f169-4fe7-8fb1-b4dc9136d1d5',
      '79fa9270-8e4c-44a2-b258-6354e38ddb20',
      '4d28466e-f314-4a0b-b596-4472bd5108be',
      '87481f6b-c95c-4714-9c99-c99b3cfe9e17',
      'd58f6e87-53e8-40e3-ba8d-500d649d9c9b',
      '9fc28e45-8896-4950-a98c-a6b44ddf561a',
      '10266d40-ad47-423e-b195-2ff0ab2c41ea',
      '958d3042-1883-4a42-9e15-0372b45cc25f',
      '4fce8db8-9448-4dfe-a59c-5a27935acaf7',
      'c63f195c-26c3-4ee8-bcbf-3074ac81d248',
      'a1a87038-8381-4740-925a-78cac7de9715',
      '518d8e56-b173-4f17-9853-bb5c205741fe',
      'a9192491-244c-4139-9fad-014aa02ac0d2',
      '8e02ed1b-3854-4006-a9e1-9dbf3d84c00e',
      '1e3c8d08-84d3-46e7-bb6f-b97a41458be9',
      '88a4f3af-0e45-4476-aed7-2e2509175de2',
      '73260a1a-9c66-446f-b0c6-dc48cab67eec',
      '36e5ac02-1fb1-44e4-a4d0-7054650784d6',
      'ac512786-7227-4873-a6de-011b4209e866',
      'c31bf114-e383-438f-8b95-59610a26b122',
      'b0f18f44-4536-48e7-94c5-b06d156c674d',
      'a71f0228-4b2b-4aaf-80c3-179f906219fc',
      '58d1f9c4-871b-4a64-bbe0-c3ce69b18118',
      'a43f9a0c-f553-4603-88d1-ee43caaf1da9',
      'c22de67d-dcd7-44e8-893a-4bbeebe2f5b4',
      'd131b687-e298-4008-8d4c-35a43ad0f813',
      '17f289c6-b2a3-45d4-849f-af67fac9208d',
      '198016fc-ad58-4d4f-a2be-3247c28dda18',
      '50b2ba74-5316-4fdd-a562-c08262f30082',
      '750aaca2-d430-409e-85ac-cb24eec7ec0f',
      'f0435990-15fe-48a3-af21-b73c56fa5465',
      '4a74c2cb-ae11-4927-995d-e2ff2b3da447',
      'd6c89a13-3bcb-4990-9171-8de9ed3cb729',
      '3644e6a5-ced5-47cc-a06b-6572922bade6',
      'ef0f14da-ea36-4058-a61b-4e1108ab81c3',
      '769794b2-06e9-4eba-9519-3764ac94a0c0',
      'e1dcf3a0-7c7d-4219-a530-40c429cb429c',
      'e21e7d62-7e7a-4d28-a15c-e71d1d9929c7',
      'fdcf095f-59d8-49bc-907d-8cd8c8a1e1df',
      '50377f33-c003-463b-b878-55ed75d89e14',
      'e2e20319-333d-48d0-b067-a7d97790b1c8',
      '50a989ad-1518-4b24-872f-86f298962d9f',
      '8f7b3987-de65-426c-aba0-8ca9e36c174e',
      '17495510-3088-45a7-8ff1-d6616b8cf1ae',
      '3972d8fb-cb93-4093-ba35-af17562f3759',
      'a6e73ec1-78d7-49ed-b347-74d224fad536',
      '81f8c7a7-719f-4b0c-9057-70c5963e0009',
      '2d927933-0330-4b8d-bd46-2fb6eb44daf2',
      '19f0153c-a10c-4872-8956-5700a0662c2c',
      'e4e11a7f-a13f-4a87-adc0-f7c31e52b784',
      'dfe11103-3e88-4668-9de6-0c3f95299c02',
      '433bd312-b5dd-48fa-9fab-22010de6129d',
      '75b32af1-902d-4a43-bfc1-590ed8958312',
      '71544d49-7264-4f3e-9e7d-4b1a7bc21acc',
      '338f6962-7dfa-4768-a0c9-e8ffd4ff2aa1',
      '05227c0a-8a80-4544-abd7-a384b02a4335',
      '22877422-ce05-46ee-b164-42dbc99b6071',
      '23ef195f-fdf6-440b-a338-e2ba907c37ee',
      '574376e1-96ef-46ed-9678-82deea135a6c',
      '817b2c58-7c65-49bd-9b99-214284cb2cd4',
      'f97c7792-ac4d-4040-9644-c87caed8d0c9',
      '42f44fc3-b18f-4c5f-826e-ef2cd1fc7335',
      '9209c732-aeae-44b4-9ca6-34d45f84becb',
      '26496639-82be-4fe3-9604-c5a4d8abc802',
      '7eed7649-1386-49e1-9cfd-7bf7cae1cad0',
      '2d0ef991-1b49-4a2a-9bb0-f240c04f8457',
      'aa2c1c4b-1f19-4db6-85ec-a17944387feb',
      'c73651b2-f78a-44bd-b094-2b8110238484',
      '82b86897-8704-4506-8444-090b1a21e05d',
      '06973b3e-a38f-41b9-83c2-dca92561553c',
      'b0d7bfa4-efa4-43b3-89fa-3793c1fb9c2f',
      '8c268194-1be8-4a98-9bea-0951e138b13f',
      '11b29af1-67a3-40af-9d2b-43595f0c2033',
      '64db49f9-f2ae-4aa8-b4e1-ca929978213e',
      '7bf1ae20-f004-4929-95de-8a2b5c3a2ad8',
      '703a640f-5cdf-4067-98fa-39a009ee935f',
      'c0ba0cc2-747e-452b-a409-47d6dc4c8a74',
      '8b3e1fdd-891b-4039-b968-001af6975a07',
      '2f6fc04a-5c0b-4f17-9b9f-dd250077c352',
      'bed286dc-89ff-46aa-ba15-c0ba648ae615',
      '903d270c-d3dc-4900-b3e5-699eb4be42a1',
      '7b4b557b-caa8-4b23-880d-6f2161814b13',
      '71faeeeb-2dbe-4b6d-8106-33e6d12bf36c',
      '0341f322-501c-48aa-a337-c2cba02ba800',
      '01d6ea05-ac43-4bd3-8a8a-dbb9046fe007',
      'af36a086-f432-41aa-ae69-4ea0f8670a7e',
      '24dcd59f-7b95-4505-a537-c6bf71cfab9c',
      '51faa9f7-39bb-4a2d-83d7-834b4e48d515',
      '245be4cb-fc5e-4006-8ceb-c97f3bc6d9f1',
      'fd30fb3b-fa39-41c2-8024-33677dc3f733',
      '7b39be3b-1c0a-4822-9ce3-de277b9142f0',
      'e04efa70-94f0-4a16-951e-025d2ae36561',
      'f5cec720-64fd-4566-badd-79d2fa338c50',
      '40f9d5f7-9d03-46c0-88d5-abed46984cd6',
      '8f9fec12-86f4-4e80-810d-3d89b9ef4e07',
      '3d0466d6-9b40-418c-90ce-92fc2adc59d3',
      'd0f6ff43-4ad0-4bb2-9c5e-7220eaf84790',
      '799adb1d-56ef-4343-9c92-a078b7fd7016',
      '900cde08-9ea6-4f57-946e-f1b1926b2882',
      '3f48752f-81da-4ab6-a7ec-fb00a8e5dfca',
      'b4d94cc2-fc35-4bca-b653-c122e9420edd',
      'e7e0e986-78a0-4972-8f24-c88d844741b8',
      'd397fe7a-acb4-4b12-9e43-81af422ecab4',
      'f26f8ee1-72c3-4f33-985f-cdbb07dd5ac1',
      'd1e467ed-37e8-4a5c-b406-9a1db0b69093',
      '035d1264-e8ec-4730-9f0b-451b8a2a5994',
      '2b56666a-99cb-411b-9254-ef9ad7069505',
      '252d8b23-122c-485c-b18e-b553ebae0dd3',
      'eae9d180-279c-46a3-ac18-18748ace7a5d',
      '06ea27d0-4546-4088-b123-de11b52b0b31',
      '05a32ab9-582f-4f3a-bc74-fb63591110bf',
      '3b6a906e-9d49-453c-893c-477cfdc92579',
      '7aad13a0-3f71-4a49-ba19-6536e1b6c009',
      'cc3f6b2f-9883-4d71-8eb6-7a5004b20132',
      '65bef639-7925-4bf3-abf7-a287da8a8cd6',
      'fec601db-b3d9-4e82-a9f9-92f2970c2d30',
      '75888023-3f93-486f-8cd6-154fc5fb05c8',
      '373705b1-06e6-44ed-852c-498bf9461694',
      '2422d9d1-6987-43e9-8784-bb7b8d28158e',
      '9d9db725-a725-471e-9072-3bd0ad1cb62d',
      'a5896f13-0bbb-43e2-89a9-8ae8df334973',
      'a24490c4-ef7b-4bda-a533-053f5014df84',
      '05eeefed-f877-45e3-b0c4-25e6e30ad132',
      '4d18093c-79d0-4d08-af8d-1754f37a3c55',
      '8cd18084-b662-4e93-809a-40aefbcb9efb',
      'a3fd9a28-a3c4-41ee-81a0-2c01bda55b8c',
      'c8392596-8141-491c-ae06-14cdd55646d4',
      '66d6216f-f0d6-4575-9ae3-f615cfa25204',
      'c1d4ce3a-df77-4a48-8170-0aa362f57ba9',
      '81dc9454-6e30-49ec-b55b-a1c0dbe04a9f',
      '29ce0246-72fb-47b8-8370-4e407255b7d0',
      '3685bc83-d5c5-4d14-8aae-a5baa6cab682',
      '5b64b419-589a-459b-9098-977f42f0bb5e',
      'dd1322cc-c6e6-4bd4-900b-e6f36332f369',
      'ccfc4a88-0526-409f-bff0-059da0aab0bc',
      '39a87ccb-a4f8-4d22-8e47-1d9bc6d34a0f',
      '3c98aa8e-bb62-490c-bd63-7213a929be2a',
      '2e27d297-8821-4cb0-8f94-2ff6f14634c0',
      'acfb2c5f-a92f-48f1-b65c-5e2fecbd4133',
      '4a490da4-89dd-423f-8c15-052c2e80ccc5',
      '270f2cc9-643e-43a0-87a8-55c52e5e6f88',
      '37d630b5-f4eb-4866-ac47-b5f05044dccc',
      '7d603e92-b4c3-4dee-abf8-3912acfce5c7',
      '2f3fc309-4e89-48fa-b4e3-ce1c591afafe',
      'f4b99600-5a1c-4fe8-b6f7-25c3e8bb1727',
      '49e5eee7-53d7-4f92-9da0-794e9b614e72',
      'cddfee10-9cf9-4f5b-9d12-afbe4211cf1c',
      'b811d61d-53f2-4f80-8afa-74eed1f8638f',
      '3e42a4dc-cf8d-423f-ba69-2f115c289e97',
      'bf77776e-ebf1-401f-8c4e-154aa89157b0',
      '17e66204-cc0d-448f-a2a0-56f9b4887534',
      'c7d6eebe-2138-4560-8b86-2f33423f0d87',
      '382b6bed-2490-4aee-a335-2fae615c2df3',
      '54a927ba-c9fd-4d78-86c9-77fccdb16750',
      'b7e129c3-a616-45bc-9085-4f906103043f',
      'ac946e84-8dc7-4043-8a1b-f09148be936a',
      '27d6396a-f876-468d-a986-fdf1552cc190',
      '949bbece-5e2b-4446-9f9e-e66c3072a5d8',
      'ec9ccbf3-52bb-4c2f-ac7a-7d325fced7be',
      'c1e3128b-7aa4-43ec-9184-7224dd98427b',
      '349b255c-5e4f-4aa4-94ae-8acdfd88d9b7',
      '0678b98a-f677-44d2-b391-166561b69ce5',
      'fe73cb48-8012-4a36-b5d8-911d972c509a',
      '1154a9d2-e6bb-4a3b-948e-0007560df893',
      '571cdf83-0452-4e44-954f-808ec3a9a296',
      '7ad3103d-654e-48b8-b2cf-97f628210e2e',
      'b91c3953-2c15-45d6-8d28-f584b68422d6',
      'df898d38-c4e1-4870-852a-5cbb851186f0',
      'f3399b0f-82ae-445c-b949-9c7d77aeac88',
      'cd036b19-4c46-4115-af38-84e352116b5c',
      '91b57c24-e9da-4cf9-afb2-8c8bc44f7c2d',
      'b220d3b8-d711-4c28-8b9a-4844fe68aa0b',
      '320f0f6a-9c75-485f-b235-96871bbec1c9',
      'e9b0be37-9de9-4c77-ac69-064407e09376',
      '7de9857b-9d96-490d-95fe-aff4d9811737',
      '06c18e43-73b2-427f-ace9-6c1ce4083905',
      '218b7a1d-de31-4b1d-9da5-c95d1320b49d',
      '43acc871-fdff-4358-9e4b-5ce6384da625',
      '9a73fb52-620f-4ebd-bb37-903af158be39',
      'd84eb883-f25f-420c-a8ad-be5bec5b440e',
      '9c44e042-eacc-411b-bfac-cc77a99ae30a',
      'a7a173e6-660f-4587-9152-de479cc9bc47',
      '14c3b055-170f-461c-8b4e-7319e11dcef9',
      'c846f468-1193-4693-8af8-6d994dde14d1',
      '97f62d54-2600-4a45-9f71-07c001a3f93d',
      '4527b210-1e7e-49d5-9693-949be3fb2778',
      '0ddf870b-516b-4f35-a23b-bd554f9efd47',
      '0c4e22b5-a4b6-4418-8872-97c582a47f72',
      '5cb9e0bb-3268-41ac-afb6-a1e2dec613b6',
      'def9a832-ecee-4736-91a2-4e35495951e9',
      '1f04d52e-a873-426b-9c9a-1a17174f811c',
      '3d0305ed-0419-4890-a682-3e1e4ae3c701',
      '9a4eae8f-7d7e-4c8e-bf6b-f7d7d256c385',
      '17ec0a2e-7d91-429d-8dfe-97cbaaecabd6',
      '06d49e90-26a6-4b7a-8973-b1d6afb01eee',
      '94ff6c0f-1099-4164-9895-7974ed23f9e7',
      'd46a25b8-e336-4b37-be56-256c3a5f2286',
      'fd86386a-4690-41d2-b4a3-13e8b2699fd7',
      '080e115f-6475-41a3-8e51-7e57f934df53',
      '011d5631-3f2c-4fef-9d59-76daa7b47d14',
      '2f7b99d7-51c6-4f3d-9279-f540c446be37',
      '5b47501b-dbde-4457-b6df-e2e640c29cbb',
      '50135abb-6d2b-44b4-af86-f07df8f57eb4',
      '6e7297a2-95f7-43b5-bbfc-62a3a345369d',
      'af0b6e28-3113-4d07-9484-5d346a4c1319',
      '12a2dc49-d0c1-4911-9b97-43eef2321949',
      '6408a865-f5e0-488e-8cb0-1d4ba01ac55f',
      '04b23e2e-60de-46a3-a1cd-996f86105350',
      '8ca8d03e-837b-4c1d-b2ac-ec61f1dbbc58',
      '91847128-515c-47ea-aeed-aaacba1ebdf7',
      'c96523dd-0937-4dd3-b273-aded74bb6347',
      'cae0b06f-3960-41c5-acd2-2aea55ffe908',
      '07b9803d-bf89-46ca-bdd2-cca383957872',
      'd2e3912d-8cfb-48b2-a030-668f294d224c',
      '6f661aa4-278c-408e-9704-e16635542fb0',
      'bae936cf-4a0b-4274-bab3-9ed2ce22323c',
      'fcd8dc74-bc29-4eac-8ff0-3a91f8908b53',
      '47a396bb-7139-4914-954a-39d32a4da13b',
      '5254803f-0a73-490f-8465-c6aa5835c48a',
      'e71f8630-12f9-4a28-be68-53012f204602',
      'fda4ba45-73b0-4e1e-9bfb-9913a20865e6',
      '94ec292f-2dc5-4b61-b04c-b4b16be11731',
      '254047da-17e9-436d-9b74-42c8caf0d47e',
      'e8441925-6182-4120-bf85-5613f0f85d98',
      'e1bb533e-fb57-48dc-beb7-e305d87ef957',
      '4d506ece-0386-4692-bb4d-f4acdd1ee571',
      '029fd424-38d5-489e-8f89-f10fa947f09c',
      'f21829e8-2357-4117-b18d-bd6e3e351db6',
      '482db534-8e80-41fb-8950-8c498774629f',
      '6413d420-3cd3-42af-a6a1-78be4b4dbf01',
      '60dc292c-c193-4073-a804-e32bd04f5084',
      'e3638cb8-e36d-4905-bc48-9ef21e85eec4',
      '6183eac6-4c35-4878-9d41-cd07c516bbfb',
      'c3727e4c-5a31-41bc-b816-b1aea00ed212',
      '15186536-a97e-40ea-a294-d5ff3897006e',
      'fbd19729-3296-429d-9676-858a8bed6668',
      'c0f6eb2b-e8c4-4d63-b50a-075825d1e27f',
      'd3af4250-5b0e-4821-8d0e-b8d5c80b3593',
      '11f1e073-f210-4470-be87-0b9ef2b1592c',
      '88acd505-7775-49b3-85a6-15706c60efa2',
      '417d9fbe-e0f0-4b60-b5b2-d811eb8cb94e',
      '97ae4fcf-ec92-4e99-b21f-06dbfcfad8ec',
      'a7e22678-112a-4c4d-9cad-ca0deedd4cba',
      '122032d1-153f-4451-82d2-9498765114e1',
      '14a951b8-cf3a-40f9-971b-fb7408e71d8a',
      'bc3d9fe1-f0e6-4aa1-a23f-77a312a13716',
      '4faa5f43-3922-43ea-b113-6fbebc8ea25c',
      '5d8aa2eb-74c4-4728-8235-84f4fc725246',
      '713c7abc-49d5-4861-8629-1f7c671fde8a',
      'fd1ba26e-078e-4cb8-bdd6-2d14e3c77600',
      'd6b810b0-616a-4835-bae8-5d40684fbfe8',
      '530827a5-65f1-423a-a774-8e00592315f4',
      '2d481ef7-bd2d-450c-a46a-2d0840160092',
      '6ab295b8-e5c2-4c76-8326-16a93e274e1c',
      '41011f4e-c31f-4711-86c0-a6b8eab642b4',
      '49841cac-6fb0-41d3-9bbb-5f0107965401',
      '6386e47d-04a2-4174-874d-a9d1801f0043',
      'fbc13296-85b2-452c-b8a6-1a8c9e4b7868',
      '7c4485a8-01d7-4c0e-8e1c-9462db81d394',
      'f963d593-44ee-458a-a96e-dff34c5eea3b',
      '39cd3021-5b69-4a04-a57a-d014927e54ed',
      '5cb930ec-0c23-44fd-b034-4547916ceaa8',
      'b49ca4b6-5126-4194-ab3e-cf76f88e91aa',
      '9a511491-849d-490f-9831-0a03280a0e9f',
      'ce4c8c7b-8d07-4fcd-9284-3d60100cf215',
      '36722027-7cfd-4322-8958-8cd93a589df2',
      '2ace54dd-8dfc-4ff6-973b-1da5a47d282c',
      '70c6d1b6-a8d2-4fc2-a8e5-506744164080',
      '90eda6be-fa14-4cf7-a46f-b2d989121718',
      '76a58232-a6e9-40ba-8906-6075afb1558b',
      '0fb1fff1-ca98-428b-baef-21ae7e6ee39a',
      'bda3c278-383d-4be2-85a9-d9051c5558c3',
      '80728c83-d8b1-4fdf-8bda-12d2e6885186',
      '52dfff71-be5b-4b64-9d02-b1722c653c4c',
      '4fab739e-e742-4958-b3bc-7e5a749b1a6e',
      'c3be26a5-d8db-4d16-ab04-149eade30163',
      '38153e36-095b-40e1-bd2e-15de9183ecd1',
      '853f40c6-c4bd-44d2-bc2e-1936cbc0fc94',
      '8cdcaff7-5be1-4b83-8512-db0634a9b6bf',
      '852114a4-f7b8-4520-a1ca-2634a5e3ffa9',
      'df27ed53-f4e1-4264-bd8b-a27555083b21',
      '4feef364-216c-4ed7-a0da-2d45c302eb1b',
      'f2731621-8fdb-4675-b032-d9926c22e679',
      '4aea0047-7dbc-41fd-a32b-cc8be719e360',
      'd8803b56-6d8d-447e-83f8-e41fb6643c48',
      '658d8f4c-c47e-4452-ba6b-21e5455f9e07',
      '6cd04efc-ad11-4efa-b9d5-0508ba34070e',
      '37842278-df0f-42cf-a1b0-79396a5e317b',
      '6783c062-a257-4084-a179-ab76ff0c2de5',
      '697c927e-6f94-4492-90bb-b7ca4c7af9ba',
      'a246551e-c46b-4120-9f91-bf9f5a7701ea',
      'c36fd2f8-c5f3-48e5-9ab7-2a4605cac169',
      'f5176766-1dc8-4b20-9491-fcaf2eb42096',
      '20ac7fbb-8955-46a6-baa6-29467bdd4c12',
      '28f07f2c-6b6d-47fc-b7d8-34e8a8ba6ecf',
      '391a0e16-f1a5-4277-adba-624287ac11ba',
      '06f40491-a2a1-4fe4-bead-cd89cf26fd1e',
      'e18a8ebf-f72d-42f6-b0a7-97eb438601b2',
      'd5a4590a-60ce-4d83-9fb3-39ff2a468597',
      'a4e5f39b-24bd-43a0-8f41-1dfd19c7573f',
      '683064ab-a1b5-45e8-b760-5ad874f9b0f4',
      'f251929f-4f5c-4b47-8761-6b250ec4477b',
      'a8f1a74b-a149-408e-84b9-528762235337',
      '60af3522-2cbd-4f97-acff-7c2fd58382bb',
      'd9dffc6c-6872-49f5-bd35-a42019d6debb',
      '7b7ad2c2-b909-4bf0-aace-92b785e92224',
      '78af6310-e4bd-4af4-b0c2-5f8534e589be',
      'a71c6d8a-9383-4988-8531-dfc52471d76a',
      '82fb953e-2aa7-43bf-bca5-9960f53c638f',
      '153fb6c4-1cb1-44bb-8085-7356f8cb6c27',
      'e51bcb1c-5220-4fa9-9287-320ee095cab9',
      'd01683c6-8201-4eb5-a5e0-b911c9005a95',
      'a58be9a2-a060-43e8-b38b-288add44eca9',
      '7c81f889-ade4-4bcc-84be-c137c8f72589',
      'd37db85b-152f-40df-9fcc-bfa5a75f8750',
      '126fcfb1-545e-48c3-928f-44baca0d54cf',
      '0eb7ca44-0f0e-499c-9af8-d29ee0784e73',
      'ece3ae64-79bf-49c7-97c1-11e73a19cfc5',
      '145db27f-9924-4e6b-a5f2-d1cbc92f1162',
      'bfb86c57-b45d-4c66-ae75-169e5f0fce3a',
      'f7d593fc-4e38-4001-86be-fb43c94e5d25',
      'd67aa38b-4782-4549-8beb-a8123a937059',
      'f8048fd5-33f5-443f-a6dd-8615ed3e550a',
      '6aa9be41-b07d-4bca-9090-359d964e40af',
      '940a5f40-e909-41b6-ae50-3428155a9686',
      '9240a8e1-5c23-4d93-b805-cfb14e877f94',
      '139ac407-769e-46a5-b37d-f37492b5dac4',
      'c81a0fc9-2d77-4f96-88fe-f436b18999b0',
      'a62ffba9-0968-4226-89a5-5141fdd542ce',
      'd2654144-ecfa-403d-a7a9-dc52622bc6e1',
      'ae7df84f-9ee9-43d9-88f6-1e0e0bf6d9b6',
      '9b44e1d5-fdbc-4292-b714-ad5684286bc1',
      'a4347558-56d1-4844-84f8-9c23140e3482',
      '5fa03029-6e88-43f6-bce3-ca0103b3d42b',
      'ef23bcd1-29e9-4af7-bc9d-bce3503ff8ee',
      '638a1dc5-e89d-49cb-8f6b-42c990fe87f6',
      '784bb0a8-2765-4cca-a2ee-d74bbe74bae3',
      'b3b7b403-d6c8-41f6-a28c-a6a144ed70ae',
      '2c0639b2-c2ac-4769-b4f6-962f5749926d',
      '81332f5e-8a0e-4ec6-878b-0deb92fe3c6d',
      '8135dd7d-235b-4014-b5ab-4d5a5f62c0bd',
      '89efb29e-f4c9-4208-b728-dc0cad252cc3',
      '9b0a43b1-64a7-48f0-8280-f1daf8e2433b',
      'cea00a2a-d4da-4e63-95be-0795b91c418e',
      'f335042f-c481-45f6-85e6-e2bc5f4c1390',
      '02a3e191-a96b-4182-afa3-5f3f1979e117',
      '67903cea-26d6-4459-a3af-c45fd58dcac1',
      'a98f5209-bd89-43c3-b2c6-634e540868d3',
      '2a4f1e79-79fb-4e50-a1c8-863abbc01449',
      '249494dc-ee66-42ae-844c-5f51ffb6bb9a',
      'a890a10b-f13d-4acc-ae17-ca9a06a12083',
      '16c4c42a-5165-4c2a-81db-e7f651d37eab',
      '758e5ce8-f5c3-4328-a4d6-8d972e541fe3',
      '3f167cbd-0f89-4920-9d87-fa6746f63393',
      '1227478e-2355-4ba7-a4f1-a0caeb01bd49',
      '4083bd10-f845-4070-9420-5697a7c1e3fd',
      'c5197397-02e8-422f-9f60-74de44200a09',
      'c1e70281-b152-4cf8-a272-4d6731f16489',
      '1ceb95f8-1aad-41b3-948b-797dfd681d48',
      'fb696d79-4bc9-434a-a0b9-5b0798af3bb2',
      'a3ff71d4-1880-45fb-bf98-4ae64807edbb',
      'b67526bf-ba03-4832-8e75-edd5c819a67c',
      '1e3f0079-5f45-4c83-a16c-489b8c45b341',
      '34f3b423-362d-4a9a-a116-d5cc467ca151',
      'd455768c-664d-471f-b974-a3427d2a0739',
      'befec401-2839-4626-89bf-ec08bfc4e714',
      'dc409b2b-b2e0-4dae-9ed3-b4c9eee27a48',
      '520eedfe-0260-4fb0-8df0-9618f19ee131',
      '88732383-e0fb-4dc1-8e62-f77ceef42acb',
      '540185e9-dad2-4af6-bce0-5209d0f06251',
      '94b04c6b-c1de-42cf-90ff-d4c74bf083d3',
      '8c7e4454-b898-4af3-b9b0-8b7bab07e097',
      'a85fe7db-fb4b-487b-8cd5-5abfe91bc180',
      'c6450223-100d-4243-9f98-824bc36be32d',
      '580e9327-212e-4c1f-8440-86271d05d35c',
      '7803e9ca-676c-42a0-892b-a7ac4c080c59',
      '15f17947-cf91-4de8-8ed3-1cf16b9ca548',
      'fe9c1549-9f13-4c0c-ada8-b4fe45144c79',
      '38860dde-5512-43b0-b857-a4c4d70e3d56',
      '7dd66328-10c7-4476-be05-c5e706c4a9fc',
      'a370b8b9-e6be-4487-a2fc-b620253c3266',
      'b82ec8f9-3bb9-4ed7-b583-7b00019debb6',
      'f6ab550d-20bd-4387-b67b-a84447952252',
      'a6b3abd8-7985-48cb-9ec5-a630aa460c12',
      '6194584d-aceb-4d78-8687-f8a65717cf63',
      'f9c1b6e6-210e-486f-bcb8-987163da2ba7',
      '43a644f4-e457-4f49-9906-7f3d751d4880',
      '401ef492-23bc-4eab-a073-3035e39ebd43',
      '239a8ac8-45e2-476e-8fb6-6caa305bca3d',
      '41fd73b7-3a1e-46a7-b625-c186e45bd428',
      '6eab27c2-3186-4d9b-95ef-46084224f953',
      '8d6174b2-07e4-42a6-ad40-ff95e825b5b3',
      '2ddbaa10-a4ac-4c5b-b452-18fcd626f300',
      'd75137d9-28b9-49e6-913c-52e34fc52b10',
      '3e572482-dc8f-4c21-acc4-da34ae0579f5',
      'd858305a-8262-4afb-89d9-066c723b1147',
      'c6c9db90-95c7-4c29-a0e2-123ee10d03da',
      '58315231-14e7-480d-b4b1-dbebe2ecdb71',
      '09f702d0-fca2-4e1c-ad16-e991e5b91d59',
      'da945f46-7233-47a6-a8ed-62312524b868',
      '7fdcb69a-56f2-4140-8fd7-07e6663e9a64',
      '6bb9793a-fd39-4be0-bfde-f7c46b1d663e',
      'd0e8fc38-cdd8-4381-9c96-03dadbf2eacc',
      'bfd2068c-dbee-4997-8f7a-0e9bdc8db95e',
      'a9a43b0c-2cab-4770-9e87-15d718c6b9e0',
      '9bcfc7e6-55d0-44a6-9a7e-abd28434ff00',
      'e14f70b4-de79-4854-a813-08fab40e6c19',
      '8ac24adc-7f1d-4c37-868e-0b9d6a7dfe57',
      '231cb4e1-a6b3-46c3-9d54-7018957768a0',
      '9ae1bf2d-cacc-4740-b7d5-b1d536d3583a',
      '5eae28b4-fbee-4969-af55-e84c7375c675',
      'e598c3ad-fc23-4db4-8b97-fbdce38a8b7a',
      'd6501cec-3ab3-420a-9efb-bb34fe75bd6c',
      '4534fc13-d38d-4fa6-924d-8cc12893be20',
      '12c4df93-6549-4001-abba-f0a2e167f764',
      '1aa448d2-323e-4e31-a63e-5ec6311d9de1',
      '080200d4-2c3c-48ae-926d-0a99af94cb69',
      '79ad4dab-e2b9-4386-a7cb-45dd5a46835f',
      '4451825a-f4c5-4527-8c91-41174ed66df1',
      '3fba9b6c-6666-4fb6-b102-237ff7e44988',
      'ba68dc95-413e-4d30-b0be-7781c9af210f',
      '6c2d11e0-02b5-4c01-9ec5-a3c22c78a316',
      '5ad4d7a1-4e18-4bf5-8e0e-1d29ba4fe468',
      '300a706f-20b2-4305-904d-e2f07b2edc2e',
      'd0f2ef6e-68a5-4a89-a447-e4565ad63a16',
      '9bf5243a-f4e7-4253-a0a6-6ba66bd49eff',
      '267d1098-68ae-4c2e-8815-8acdb1f71708',
      'ded03767-5212-4f78-b5a8-61b2a574e657',
      'fd329e02-35b0-4374-90c4-3a7110ec4993',
      '0821e16d-7024-4329-a96c-bea121d1b8d6',
      '01687df1-f05e-4f59-a7fe-cdb208a460b8',
      '7392fde4-2ff3-4486-a4da-bd5aac3e9a2f',
      '64f2a389-2990-47df-afa6-119184fa016b',
      'df2233f6-8b87-4d7d-b3b0-5d4ddded1268',
      'c5c911bc-c14e-498a-8678-dfbe8b77785b',
      '50f445d2-796d-4551-a80f-3c3d322bdf6a',
      '1eca173d-60a1-4c1d-9345-aaadb6d77736',
      'a85fcbcc-bca7-4c38-95a7-715ccf640c51',
      'f6ac1524-0a3f-452c-bd18-578050940021',
      'a7f8c3c3-b000-4012-9df5-395557386132',
      'eaead9bc-88fd-41b4-a425-4b49c2fd1587',
      '4a61aa9f-1cd3-41c3-b84c-0ef54718b6da',
      '67b6b83e-a075-4d34-a511-6613c66ea9dc',
      '93d660a7-2707-4838-81ff-7ad1dc964abc',
      '8ecba679-e898-41c7-b688-4714fa920475',
      'c8d674a5-dd9b-47a4-95de-c6cb2e196089',
      'bd8b4dbb-cdab-48d5-ae15-3a477be1da7a',
      '3a41f75b-0f3f-4c2f-8760-164e8ba2cedc',
      '71208a5d-d604-4121-b472-9e713ad42e40',
      '6eeae2f1-875d-48a1-907d-ece2cf78ecfc',
      '2ca09e48-6550-49d8-8f31-d9c3679d6458',
      'f049da7f-a36b-4c83-b015-622d05138e59',
      '3af6cfd9-6976-4080-8526-ba42dbadf445',
      'c9ffd9c5-5d1e-461e-8883-2623783f1b60',
      'b562be33-49c7-49ca-b7a7-003019cdb133',
      'a5f556f1-45e0-4b61-8b16-5683264916ee',
      '358bda83-e1dd-4c9c-b3b0-07b243925401',
      'dd439f20-da4b-40ec-a411-96d2f826d4c2',
      '4688a40f-86e1-46e1-87bd-21898eb9b392',
      '48965526-3343-4a2e-957e-9ddffa0cd0bd',
      '01c7ea97-6d60-40e9-9a20-844f34eea1cf',
      'b6dcea85-5e19-4404-8704-880075246cdc',
      'f29ebdd5-b99e-4e11-944d-5846f70563ff',
      '6d467092-679b-4205-8320-6a65bdcb6b0e',
      'be36cde5-d75c-4f4f-a7c8-fce110780f12',
      'ee0c5969-0914-49e6-b1f0-2671bc0484d2',
      'f15534b3-cc6c-4c7d-a48c-be9ebadaf928',
      'cf3c5225-ddf3-4f6e-8d14-28ef3056d15f',
      '4b70783e-96c7-476b-941e-50cb02c5d148',
      '9476ce6d-857f-43cd-ac33-f00f70af7523',
      'bd3baa3a-1e3b-45b6-84ca-56d6e2d6353d',
      '5e25acb0-e7e7-4da6-82b7-1424cbce7ba4',
      'bcb12999-8209-46bf-92cc-91cee3b91407',
      '89d6bac9-260e-4c35-a384-d6a7da261330',
      'd8a1ddd2-ca24-48dd-887c-dc9636b24d3d',
      'b1dc90f0-b643-479f-87ae-e63291bf7da1',
      '4ae27235-e747-4f7e-a307-9ce10a895dbb',
      '926f3428-30e2-458a-9d3b-03953e69fd04',
      '1ff22f1f-b4cd-47e3-94bf-289ef098726f',
      '9baeefe8-45d9-4a06-ae7d-a1a56a3eec60',
      'c4ae05ee-ad0a-4729-ba09-808121170f45',
      '5f9f5fc7-5542-4a25-b14f-2550fb680245',
      '11bb4070-a13b-4976-87dc-8d9f004de8a8',
      '484cc8fe-5589-464f-8219-079a39e95615',
      '4b9bd882-88ea-46fc-8ced-324d9cf276d3',
      'ccab9da7-d2ed-46af-bf5c-aacfaa5e0da8',
      '123e8216-4077-4b09-b007-29112a90a406',
      '6152a101-376d-4fdd-b922-be5f87f306a5',
      '6e94174c-1a14-4de9-8602-76a7f4a2f725',
      '19f3be2c-5285-4799-a712-21028cf808b6',
      'f0d688a1-c545-4999-bba3-61cd23d447ce',
      'da4d7ace-d44a-4d0a-882d-422d8748b2b7',
      'ce1e4a12-0336-4f1c-8641-f3a76f143cff',
      'a24a10bb-5d42-4134-9707-490296ef600a',
      '406092f8-0961-405e-93d3-35ab47785875',
      'a03ef079-a737-4b3e-a8a7-eb7b2945cc4f',
      '9f123c17-6757-4742-949a-49fdd144f7bb',
      '20d91e59-f2c2-4bcf-87fe-83c315eef1e2',
      'f3e3ca0f-2869-47a2-83f1-a3a8643ef072',
      '9a298f2d-aa82-4881-b200-16027bf49014',
      '2a5215a8-b735-4831-98db-319aa2f3045f',
      '6b996e73-f3f8-47d1-98be-46563e81c471',
      '51f39232-11a7-4fb9-83d1-0531e47c320b',
      '8b62622a-f339-47f1-8001-111955583b6b',
      'ffbe938a-f62a-409f-a234-bbd79761bfe4',
      'dfc00809-c008-4b51-ab65-955d280a143a',
      'ade7c0ec-a80a-4b34-8de2-8349aadc502e',
      '95e569ac-3d02-4f8d-a0b7-b08276c843b7',
      '50369b5c-8894-498c-8294-58eeb4f4edff',
      'f37e75b9-36dd-435f-973a-187966e67b46',
      '0dc027ee-95d7-4b42-add8-b2ff52605b15',
      '0cf37550-60d6-4212-8c6c-38f526a01cfd',
      '7ca38355-d434-4625-9ed6-2f72921d6224',
      '6a5dd45e-f68a-4ef3-a2a0-d461ebd43add',
      '094cb427-41b5-406b-982d-160216118fe1',
      '5eaf18ab-ea33-4cb1-82d9-d8de5eff031b',
      'cd3c46fe-8732-4877-8936-a178f44320bf',
      '8fe0c17b-1bea-4d08-92fc-ca9266d2fac6',
      '01b1c594-e684-448c-864e-a3f0b05a0607',
      '7bc89d2c-6015-4057-b3ee-a6a43891611b',
      'c54b03e5-1024-4176-9661-edfa5983f1cf',
      'bfc1f4f8-9847-48c3-9730-c376e328aa48',
      '4972ddbb-aac9-458e-8414-41303d121a53',
      'a90be264-308c-494e-ad9b-5ab9fd57f222',
      '31d1fd38-e5be-4770-881a-90a7256ac531',
      '2e3dfd75-a9cc-4c49-9ca7-6449bcac8739',
      '4a86008e-887c-45cf-bf5f-bc21abbceb69',
      '6c1d26ad-93ca-466c-998c-16ff4bb00578',
      'e02b883c-b3d3-42b1-b3ea-e57f0893d940',
      '9b1f5065-f540-443e-aabb-5ec6b05861ff',
      '45a280a1-b9fe-4b49-bdd8-c2d7b1c811a4',
      '1ea8bb9c-9a82-4c21-803a-109473b6fec0',
      'b8808511-adbd-48e0-a1e5-df5fe8d8b050',
      '9b447884-6040-488a-95e8-675077244526',
      '73646a7a-2daf-4c91-8477-cc6e76249dbe',
      '2f4f6cff-0dce-4b2a-be9e-6b35a1df7c55',
      'e74b9928-65b6-4983-a531-fbfb05b3e6f2',
      '3f1786b2-27f9-41b5-aa4e-2297a1f1928f',
      'caff0922-ece5-4d34-b4da-bae6f7392081',
      '8bca4c44-6b51-45b9-a5e2-717fca23c257',
      '87a481a8-a2a2-475c-a3c7-a02c0efa1f0e',
      'cc376268-1a0f-46f8-bcbb-4e144222fcec',
      '6ecba5d4-5ee9-4fdf-9c42-9d24c5c4adca',
      '76482180-1f10-4b67-9863-a88bed9419e8',
      'f344ed28-a171-4ba6-84c9-8fd26075a24c',
      '8e6647ee-3fb5-4aa8-80f0-2edca73505ef',
      '41b9f272-a6eb-4464-919e-446f00fbe9ca',
      'cc52f8bb-7e27-48e0-b79b-17dfdfa03ead',
      'e6a5329c-624f-4f9d-b1bf-f30aad1543e8',
      '02506e2c-4cae-410f-87c0-84b39bd5b99e',
      'c81e7e09-b63b-49c8-8644-e20c1dbfe4ec',
      '42ae335a-23e3-475f-89e4-557793609217',
      'bc6d10e5-7386-4145-bdf0-9693d980de78',
      '6969f0c9-f188-40b7-b99e-48df0a3da9d1',
      '8506bef4-2b55-43f0-b96f-bc4db7202f89',
      '51ad2ab0-d090-47ba-9ed6-2bc461ba556a',
      'ca5d0e88-a1cc-4227-b6cc-ef1b7e366b4f',
      'cb16257a-669f-4006-bab1-0d5483b42496',
      '74211679-53f8-4e14-8ff3-22a748c5b391',
      '4bd7e8b0-c104-4564-b3a4-9128958e35bb',
      '86354d7c-512b-4a4c-9f41-288cd6021b8e',
      '958d1d6f-bfc8-442e-b201-4236019401d0',
      'b56818e9-93db-4e70-a132-147038c04065',
      '02b0b147-04b3-4dd5-94f1-2327d39a134e',
      '41d6c244-f986-41e6-8754-3597c083836f',
      '2c80c6b6-691b-4647-ac0e-17561455e7bc',
      '4a18ed91-e3d7-40f6-aa30-15ac732ed566',
      '4377dda5-ff7e-44d5-aa6f-d9652f6d76c3',
      '3e0660f4-9b3b-428e-9ee1-cdd5b74d309e',
      '34cdab69-d19f-4a60-bb1f-6148680508d6',
      'de6f33b5-7cfa-4d62-8bc8-91678df10f44',
      '4838e999-af77-477a-a8f4-b29f94be20de',
      '6a1e575e-c35c-4bab-8a39-bda0f8eb77a5',
      '16eebbaf-c0b6-4d05-88fd-f2a61f6297b2',
      '35130fbb-0f75-4550-9899-b85091af25fb',
      'eac9f517-dc40-4a09-9f49-f93f2d20cc3f',
      '2868b276-3e22-4b8a-ad82-893b2f635245',
      '6c74326b-89cd-42ee-b4ac-9ad9f2c93434',
      'b2a33423-af91-4b54-aead-d868c731b7f3',
      '9fe8318e-815b-4c15-9c7e-cda3d0191aa3',
      '49682ff5-16d7-4749-883b-76c88f7adad8',
      '866fa347-034f-4ca8-ab04-ba808bf16a98',
      '9c81c03c-6102-4ad1-98cb-a13ce8bbdde7',
      '957d05dd-c65c-469e-a5ad-a14e6a341ee7',
      '3ec892c4-46bf-4925-8480-fa8d22c42e66',
      'b7e9c53f-3b8d-4223-95d6-1dd5f8a361f4',
      '9c2ee478-573a-459b-a397-8552a42b5279',
      'c32f4551-6342-4e0e-831e-f6f855e9f01d',
      '9a21d35b-e391-4ac4-a6c5-580d73dfa3de',
      '4e90f4b2-fcd8-4e87-873b-33bb5fa61060',
      '4c529fa3-6d95-49aa-8297-6ef6d9d0ece0',
      '03485875-8274-46c3-9240-557e2a8872a1',
      '999f6154-194e-4ba4-afc5-4c25c82ea56e',
      '6c389717-1862-4646-86b2-9216ca86a8e6',
      '26faa14c-8aee-4fff-a692-c0c0041777d7',
      'f5dbb42a-53b8-4bb0-85d7-a199a16c9478',
      '3632fca9-d137-4595-b1d9-e09e140d0520',
      '6b22c660-d9cc-41c0-a121-4723468ff231',
      '949cb052-8423-4d67-9cdd-927824ff4c2f',
      '5328a7fb-00bf-42d9-94d3-47ef7ac11e09',
      'd432992e-d4b4-412c-805e-7e76beea0365',
      'b68255c3-f2e7-4747-8353-5274da1fc465',
      'f883a971-d625-4b1f-8ae8-9639157a0001',
      'cc1b535f-c496-4b1b-ac5c-57d0859e0570',
      '707ce1c4-9408-4075-a91d-9936164bc8c6',
      '345972a0-37fd-4651-bfe3-cbf8534419cc',
      'c5927b1f-3723-4034-a431-4b55e42f47e8',
      '0b95e00a-1c9e-4b53-a7a8-9f00d081cd08',
      '439b93c8-6397-4e44-abd1-eda31698fd5b',
      '770b805d-5a25-41f1-8579-4e80cbcec7ba',
      '0309b464-6266-4cd0-8911-f76361e0b7c1',
      '1c1b18c5-c3fb-4415-822c-ff45fa44d3f5',
      'd36e8d73-8fb6-4ea1-aed5-3d4b0906c090',
      '071496f2-2e04-4271-a05d-1533ae91d8ec',
      '9e826953-703b-46d2-93ee-cd6281d03d24',
      '1a2ee20b-0c0a-42e7-b921-e73f228bf8bf',
      '247a0d66-e719-4fd8-abb8-054636f93f5c',
      'fc2aa5c0-159d-4907-a282-0ea6f0b489a7',
      '972a1897-4381-451f-8c0b-06e743be8168',
      '0ba7d0b7-6719-40c7-b5c9-363586a167e3',
      '70d7ce29-d4b2-4862-8bc7-bfe02efba90e',
      '5d1f983d-ed6a-4702-a3a5-cc5e083b9586',
      '1033ae7f-840a-4ea3-9bbf-e406bb259a6a',
      '89b6e120-f2e3-4af0-8c9d-ebf0cfa35260',
      '3fd7e21b-a47b-4bfd-b8de-a3d46f34e038',
      'aec33dee-4c6b-45b4-a337-338407fd4cc7',
      '4f50ddbd-da75-490d-aa35-156c0f40666e',
      '6a3b1efc-dc89-47f4-a27a-b2766fe5d7fb',
      '012b8963-09c6-4e8b-9f81-5bace5348d00',
      '851cd190-7443-4589-a072-b72664bf8696',
      '12d732f2-b003-4323-a0ff-e8e02749e468',
      'a22062be-ed54-4532-a8ca-2138e335a645',
      'e959983c-b5da-45da-80d6-e26e404c7d10',
      '90a91cf9-f5ba-4ed1-8f9a-d11cb2690f7b',
      'c5e258f3-3a40-4ffa-beea-e4c51827a343',
      '8b3dc767-1cfd-4d41-92fd-56c82556416d',
      'da09df5f-84f1-49f2-a7d4-31fcbfc2694f',
      'e05b3a6a-30fd-4cf1-b643-1d1f837d05f1',
      'a824256f-2fbb-43a6-973f-067ff0ced8d2',
      'ab38556f-e139-4fa5-bb1c-c3f37a6a7793',
      '2789f9a6-6dbf-45df-a3cf-a16b0880165e',
      '9fbdbaaa-0911-4611-adc5-7d7ab74693f1',
      '555412c7-16dc-4572-b5c0-b14fc5c6c1fb',
      'd550b69d-f8bd-4d54-90db-c0115162bbb0',
      '4aa5087c-3976-4599-9d09-ba3b187849e4',
      'dc30aa8a-f79d-4f6c-bede-08cd60fd7d41',
      '2e01473b-c468-44b9-b81c-0feb7b11291e',
      '70960c5f-906e-47b0-b0b7-51dd349b5ace',
      'd5b3148d-04a6-4c92-b056-41b094c314d7',
      '2f0a8a31-9796-48d4-bc6d-4bafa4f9f915',
      'fa69c279-8dfa-45dc-be8d-deac585b597e',
      '24a38b0a-4ef0-4636-8005-2b2a1a587036',
      'f2d1adf8-9d2d-47f4-90b4-7504421524ca',
      '8cab69a4-1a44-49cb-a9e9-65d11816cc21',
      'd888cffe-1c66-4e06-8cd8-5a2ba9fd6333',
      'c3572b7c-0a06-4f66-ba5e-7ced477fa021',
      '8566479f-1ae4-447f-acf5-d7c855dd9979',
      '0b1e092b-7ad2-4123-ad83-da4603b74f94',
      'db0d406a-ab55-40e2-87fd-fff2a42d4a43',
      'f06eafeb-ee74-4a07-a4ad-38691f98abb3',
      'ee644da5-9ba0-4e50-975e-d06294565779',
      '14676e24-b073-415a-a4af-1729286a6ac2',
      '443df8c6-185f-45d9-beaf-fe3b417248de',
      '127020b8-eff7-4145-a82b-db9bb44a6da3',
      '48d8786e-6f44-4e4e-a6de-dd5398bada8b',
      'c63a76ff-0554-4ded-9e89-e2dd01e8df4f',
      '514cacf1-3eb3-4716-ae96-f62940d58a01',
      '19e5f25f-bb98-4f14-926c-5f06b9226df7',
      '88fdb2a9-3d82-42f3-9f3a-fdd485a0a2e7',
      '152eedfa-674f-4505-84ef-4616dc19832a',
      '2818cf1e-9c3c-4a52-bddd-dfd3c74f94f0',
      'd67b1521-690e-4a99-aed2-867f49cd2baa',
      '344cf963-764c-42e0-8d67-d61832b702f7',
      '6d1a43e1-6467-4386-95d7-aff109bf083c',
      '13cdfc63-9026-456d-b357-f2be7bf5e9f3',
      '19a73397-27dd-4753-8d07-b6a494eefb3a',
      'bae95b18-da0c-4ea6-88a0-b93d867578af',
      '38465cfb-9125-40a6-83f3-a4dad408137c',
      '28b6a641-9aec-4361-a0e2-92d66b96ae02',
      '00bfd30e-52ea-4909-90fb-9eb3252a3a16',
      'b58e4364-4fe0-4b45-899f-3c5dca40752c',
      'f0ce63ee-b976-42c0-b29b-edb140ae198a',
      '3cc7b8e9-6008-4aaf-9042-b117bdae919f',
      '9021cf88-6886-404d-bdb8-a79479898a02',
      'ccf05a5f-ff5b-4004-91b2-eb23fe315612',
      'bd872e8f-6bb0-4c24-8a36-5b6be8e6b1e5',
      'bef66bf2-f5fa-42dc-9cc5-181b7a1f00b5',
      'b7d6fe66-817c-46b7-9a85-f5fa84fba1bf',
      '748fefdb-6069-44a2-84c4-a2730a2a539c',
      'd036cbe6-3445-40c9-ad4c-7e064b26d38c',
      'c45cc08d-ec1b-4be7-8c67-3708441fc412',
      '90b95078-6235-4b21-ae47-48a173c70d69',
      'dc8d4ccd-54f2-4371-8bbe-d8aa5b737d98',
      '30f46e26-5d88-4cf5-b744-a14e6093f17c',
      '5c012026-adb9-4208-91bd-ddccb740c926',
      '0620ff02-825a-4985-a302-622a9febabe6',
      '43f76318-f873-48a9-9561-d57d151ad2af',
      '68ee7c7f-d6f5-4636-a3d6-7f24d6913db7',
      'd728db14-e88e-4778-8214-4558d4783030',
      '5215275e-c6e6-469a-87ac-2399fa6d3a26',
      '0e5c4c63-1a51-44ce-98e1-5726792033aa',
      '2a803c78-949d-4e98-82a8-aaed36fec969',
      'acb92974-74e4-4ca2-9b23-689faf62e1a4',
      '42654cec-18df-4b1f-8457-f6cee5a8a92f',
      '9b1bb3f5-7ac0-4a3d-b9d4-d2463dd9bae0',
      '1ed1fa69-09ed-4d1b-b42e-fae8c1a90f1f',
      '92c71fd8-21aa-4ef3-9629-e7579194421e',
      '354718ae-025b-4e19-b6d0-3ed0fead8e20',
      '0c5a5336-e8c3-4737-89b6-0e90bef14922',
      '7ea15051-91ae-4a8a-a3b6-fc0500fdbaac',
      'eb7345b5-a9db-4d54-874c-ecf7d827ea1b',
      'e5484661-5935-40e2-ab8c-65c2203eb5e5',
      'f174c494-b430-4c3b-9a79-db409772b930',
      '24f4b4f2-b879-44de-abe5-8379b74c113c',
      'd1008170-93c4-4de3-9004-bf72e3890216',
      '814dc65c-514f-4a3d-b1f5-87c92dca681e',
      '2f896f06-a1a0-4a99-834c-2cf6ce9e9cff',
      'c05d73f8-9757-4eee-943c-12772c8d99b8',
      'e5be1835-487c-4e95-8398-936f53734b1c',
      '149bb1ca-5db3-49a5-8a3f-56434a01b3d0',
      'd3bf28d8-3aa8-4cb4-99ad-60875ae4cc07',
      '67778743-668f-408f-a829-5dd68c87398c',
      '79d8e341-fb18-4264-b9aa-e11b3db502cc',
      '2a081aeb-7678-4767-a6e7-c727f95ba6b3',
      '968aae54-3803-49ee-acca-6c4ed50898a9',
      '540fff77-1ca0-4913-ad40-f014e40275c5',
      '230dfb34-017f-4b77-888b-b60c564efbba',
      'b0e60826-acae-455d-8242-00012732ee61',
      'd04af37e-3e54-44ac-9159-2418708537e0',
      '8cb16616-77db-420a-a796-ffc1db999eea',
      'c81f851a-51b6-4d75-908f-617d088c0748',
      '2a224b18-2641-495a-89ef-f75bca314ab6',
      'a4047b49-0d10-483a-a59e-d9633c192aca',
      '50736e17-e380-4ada-8f24-827b1dc86165',
      'fc8ccdda-5630-48a4-a708-6ee70251f623',
      '8f6c0619-d801-4112-a94c-2e796b4b6110',
      '4c6dfa88-0bae-430d-8886-f3cf6f69aa56',
      '540b6062-fb08-4a22-b77d-6d3d4b853e00',
      '6291b2d0-26c5-4392-bbb6-40245a053779',
      'c782b669-cd3f-455d-a06d-9b7be2f42a9f',
      'e9fac2d7-b49b-4f26-831b-ab9f45ab177a',
      '83bb4715-11f2-4615-b163-b5896b322193',
      '2b49c272-cdf3-4280-9dcc-a5e506923a12',
      'a6c07746-540e-4168-8510-1ef0e814999b',
      '139cdae1-54c5-44c3-819e-2475838601bf',
      '23afe2e2-1711-4da8-b92c-4dc3d45a80a7',
      '2e8ebca9-becf-4c9e-9ae5-0367732480e1',
      'ddb7c180-fe13-4402-9ae9-f00851cfae2b',
      '207497c7-6301-40e0-988f-adb39baa0115',
      'ca70d8c8-89ce-41bf-98b7-259d6797053e',
      '34b3f0eb-355d-4e70-9d3f-6e2ea6a7c488',
      'c2b9cec7-3e1d-4328-8c91-dd555ac999d2',
      '82225c29-5dad-44b2-85d3-ff531de2fb37',
      '94744240-d5ed-403c-93e7-1162b5e21f4a',
      'c13e6716-2eeb-484a-945e-a38d24394e4f',
      '4a95804f-5e2e-40b5-8e14-24daa88595d6',
      '9d32df61-6707-4ebd-bf27-2b4ddc394e9d',
      'c9766c3c-fb69-4d29-a6bd-328c37c60963',
      'c6e09585-c4b4-480d-8ada-2bf8562b1684',
      '7163ca61-e599-4551-80d5-e8071bafd11b',
      '2c57ee29-b5cd-4f5a-a212-58849fc0c638',
      'b08fc60a-3d34-409e-9640-615ab4857fae',
      'e7503eb5-6423-4f91-bcb9-8aee36f823a9',
      '9518c8e4-175e-4137-b010-978dfd72f814',
      '49eca88a-aade-4a19-97f7-b94ecd4e0495',
      '183d6bfe-10c8-409b-b159-51fe7f574583',
      '1c7e1ba6-7986-4c94-97e6-6505b958d293',
      'ca7120aa-9224-4fed-a9f6-334360003641',
      '635392ac-d6d4-4952-8637-23ccd31c80f1',
      'f0ee5d4d-0fd2-4eac-a86e-4a375e379c23',
      '27212f27-cb67-41bc-a0c2-068c5d7af90d',
      'a64cc57a-524b-43f5-ba01-be11c048ab9f',
      'cf67a159-6cd3-4e60-b3e6-00482a62e28b',
      '0a672935-2589-4262-b2a9-04eb0922465a',
      '73c11b70-dc18-44e5-a09c-721a4c9f2f64',
      '5b8074c3-25b1-4d18-ad56-8158d41b5162',
      '85789e4d-737f-4b11-9e27-e3ce4e193f9f',
      '311aa972-459e-4412-a61b-c513eee54113',
      'a6b888d5-f991-47c1-8237-331483cf5fe4',
      '2a3c7ea8-8b80-4d27-941b-66e41fd19ac4',
      '1346bbb4-e2bf-4d0a-9875-866ac4ac7be8',
      '6d8d8dea-3c99-4e9d-bc98-d7e879f32ae6',
      'f04fc51d-954e-4ac1-9b88-f6ecd1cad026',
      '36633b36-475d-4f4c-8129-dc50c2cc999f',
      'ed63ca11-3b8d-494f-afcd-963f9dc699d2',
      '93b60e18-ef53-4f85-862b-dacea8c31985',
      '0ce6c854-0f73-4213-9270-3278f47fde50',
      'ef617913-bc2c-4401-b071-7219e8c40419',
      '59569961-955d-4673-b93c-1c2d4da6dfa2',
      '38d00224-9c69-41e0-b7d4-6805ed0f389f',
      '0079aa07-2b15-4c5e-8044-85c09c3da60f',
      'dc745acb-c06e-40bc-bf06-44b52ee40817',
      '184ea6c7-24de-4db0-902f-868b10115c99',
      'a874371d-f11e-4b0d-8381-b1a6a7205647',
      'e877b310-b974-4384-a047-6abe035169ed',
      '20c56243-3a53-4435-818f-acf2c95bdd1d',
      'd7eb2e02-cc0e-4a3e-a2de-25829a59c867',
      '01757b06-0aba-4928-a4aa-9be746b95055',
      '95ac40f5-8b1e-4b56-844f-c21856a80576',
      '34439731-0be8-4ab7-ac69-1addf2b4c9ff',
      '280ec9dc-0353-4631-85b6-eac1d6677dce',
      '2490533a-0807-4966-997a-c477c1e5227b',
      '74f12678-580f-4edb-8de5-8795dc8d2fc8',
      '20f2799f-a78a-450a-9c3e-193dde1c7d5e',
      '7458632a-79c9-4682-8eb7-e7ee9f51a4c3',
      'a4ed7dc6-0452-49d4-899a-c3e2e22de8db',
      '6b70ed2a-7ee9-4e33-8339-cc5927dd5a47',
      '248763b3-6550-4eb8-b0b0-c973de2a30c7',
      '8717ef94-1fa2-4124-8f38-f7af15a07678',
      '904dc997-7ab0-4ce2-b749-27e558f6b711',
      '7ba486b3-e6f8-438a-a391-258fdd7d93f0',
      '1b30275b-fd5a-47af-908d-52d09f18addd',
      '3dbcd78c-2303-4db6-92f4-370c17d44796',
      'cec00a9b-08e6-48c2-a557-4629b3f68f37',
      'cd93de85-0ac0-4cd1-83de-0bf3ccab2e91',
      'a88e0e50-6145-440e-a956-e4e60a31625e',
      '6b633ff5-7536-4966-a066-1f1b1aa693c5',
      'a306611c-dcdd-48e5-a18d-1e48495e4959',
      'b78eba2c-caf8-401c-8b10-ff7b9eca998f',
      '29397909-b153-44c1-81cb-5dbd833f7dbe',
      '17c8ae1e-df12-4b2e-b1ea-a926b52424b2',
      'cb5112ab-a959-45b4-b4ea-8c02ac46aa1d',
      'f0758516-9998-43ce-836e-4bf924378d19',
      'c48ef79a-09cb-41b0-bf1c-7451cc447d1f',
      '4c32346d-519a-4e87-88d7-255b79218fa8',
      '2902ec2d-f537-4fb8-b622-2d23bf5feea5',
      'a4c2113f-409a-4370-82ba-74a82f5fcec0',
      '59a6e30e-df5a-47e6-8bc1-6d68c741a60a',
      '92812fe5-3510-42fd-8540-79bbb4e86025',
      '82af0685-c79f-4e9a-8e25-ed3c3096160f',
      'cd3eb5ea-e475-4b09-bef0-9a12772ec9a9',
      '6a21e0c2-4071-4f76-953b-b542f07df90a',
      '60a1812c-4a5b-483f-9f9e-67cceefac239',
      'f3b0ca79-9d7e-430d-967d-3fd903c08a78',
      '2943a774-79b2-4f8d-8c15-d779a330ad16',
      '8a2e97f2-6b69-4a1c-9b3b-86f0dece9518',
      '955e5794-5ad1-479f-b3e1-fe6bdfb8e853',
      'c18be155-ab60-4be9-9bb2-42df53d98140',
      '38db9530-b11c-4b36-ab7d-69e8c6d31e75',
      'e1835fa8-2515-4539-bc6f-8395c939454a',
      'd60e6777-9326-49ad-9bea-30ee00ddb9ba',
      'db04cd38-4d7e-419b-a831-c47a1d2e89e8',
      'd4306088-df75-4733-930c-c9633e84b5bf',
      '78fcc4c8-3520-4005-acb3-528cbf4df09e',
      '7b2c03b7-0186-4662-9970-4c344b2e3ad4',
      '3a2471a4-0daa-4404-b894-e9b6017d23d0',
      'cdeacf13-deca-443a-b23a-8e17fae5a540',
      'a601c959-5acb-47ce-ae8e-93fa5bc4c494',
      '111c5f73-7443-4afe-aeda-56c8f4784174',
      '031558f8-3770-4766-9c27-0552fd22a932',
      'cac46d2b-8b2b-4894-976d-1376ffd9be67',
      '2ca8aa7c-e410-436e-9092-321bc5c7906f',
      'da662a28-9e00-44e7-a7d9-57aba67370c7',
      'd6a0cf7a-ee71-4b51-85c9-b897ef6b1840',
      '28893bb4-6a51-403b-9202-3291f5f11052',
      '94204eb1-22b6-44dc-9864-4e8a0122bc5f',
      'be156b18-e0f6-45d8-9471-d181d5f76d62',
      '191ccc82-0636-4f02-ad79-61a5cadd752a',
      '8c5bacb4-7611-4533-ac31-72753ef875a2',
      'c0470354-925c-49c1-ab91-0ba141142df4',
      '760fec51-22e2-4520-b1ec-55460b15f13e',
      'c88758b2-2ed4-4d77-b7c0-669808096872',
      '630c5897-ebd2-4acf-9bbb-417df9118481',
      'ccd61b2e-eb91-4c78-ab4f-47de75d50738',
      '14f40dd0-3dda-4de0-87ee-59dbdf0dbe8e',
      '9f424637-84b1-42a7-acdf-4ece03bc3fe2',
      '582dae42-18f9-44ef-ae00-407f844a79de',
      '718a8c95-9c03-45b5-b343-8547325eaee4',
      '0ffa8064-808c-4cd6-b14c-36dcda4fa455',
      'd7d82398-40d4-4211-97ce-e4718e5cff61',
      'e08b4066-704f-4638-8a1d-e79e087dba7e',
      'b901ca11-ee8b-4331-bffc-6f1b8633610a',
      '66b727b0-aec9-4947-b585-eecc705dd342',
      '8f91c43a-6833-41c9-bb1a-7686b7c0e289',
      '15a25801-a555-415c-88c3-fe15824ce52e',
      '28b5a635-a0c6-442c-a1aa-6218a47d39c7',
      '0f49d5f7-478f-45fc-8c79-871c8f5ab19c',
      '4c3d0d39-f4ad-4d4f-adef-07482399d804',
      '71b91556-1a23-4323-a0fb-5edb987984b9',
      '1e899b48-4504-4434-b859-5e9f45b58c5b',
      '3a67c21b-71ad-49a6-b3db-fcad2e933e7d',
      '9fc34f40-8059-4004-9087-3819111ac91d',
      'c3ab7aad-d370-4612-a0a4-6a798ef521a9',
      'eb01cc77-8f93-4afd-910c-9bb4184e61b0',
      'a14a64c9-416d-4e2b-91d5-69381d0dff54',
      '9bba7969-4140-4722-9a74-9a127c62b44c',
      '20e43d62-0345-4289-aa08-c26900921d28',
      '44160542-a711-455f-86b7-afbb5cb7dab2',
      '306d5c8e-2d4c-4f09-9cc4-2fae1d684ca5',
      'ab6be613-29e8-4782-9407-628e45dc8ed2',
      '3e267e1c-71b0-48b0-8678-1bd0a4f48e39',
      '346062dc-fab9-47ee-aa5e-9e6cbe2482fd',
      '029b4406-c51e-482a-b399-eb88a91a905c',
      'db3fbb77-85af-40ab-a86b-8a39cdb20420',
      '9ed8f1c5-5e09-4bda-9223-ef607caae6ca',
      '0f1eb990-0f79-4eb9-a20a-6595ef47221a',
      'ab781158-711b-46ce-aa17-6ea2ef7f8d88',
      '37c2da66-d830-428b-85f6-483b3792393d',
      '25690768-f8fe-4ce8-87ae-a804b8f0abd6',
      'ccc5b0e4-f8df-4e75-b217-7f835a9bfb7f',
      '3fd7bd60-264a-4ff1-9e06-5acc1ca7bfc1',
      '566d5a87-fdfa-4cae-95b1-39f59472390e',
      '326c521e-7c8c-4898-813d-9146455783f8',
      '9388ecec-61f2-4ac4-89aa-f1ab82b0cb56',
      '0ee6925a-f0ad-4295-9dfb-3248d8a3a583',
      '2b28485d-a657-4354-a7a5-9f0890fc96f2',
      'f58ee27a-101c-418a-83cf-7d7d52c32e59',
      'ac52758d-8bac-4f57-8820-0b3abf8620be',
      '969b055d-e93e-4a3d-9b8a-6de633c6b727',
      'a5aff4e0-dd67-46d1-a0d3-2e8449c763ba',
      '35d93093-d229-4367-8150-9f74e3571f02',
      '2f4dc824-08a8-4190-a6c1-ee7bdd149965',
      '2f3b0794-6e88-4ffd-9642-25f03b72add4',
      '16306667-2ad3-407f-bfb4-5cf5eb26a812',
      '94de3959-83c6-4f79-9035-a36af53dbe0f',
      '36239c99-4952-494f-a43c-132076ff9bc8',
      '522c8341-579d-41b8-ad29-1b7acd6b582c',
      '0a63120d-9130-4951-9fb4-16aceef62f84',
      '379b3cbb-50e2-4638-bc5b-55c181749033',
      '0853bee1-794f-4b66-9153-8e01c3063741',
      '15188520-99af-4051-bbca-3aae4be6c4ef',
      '0066291b-438e-491b-9f53-71aa898eea0f',
      'ff09564c-cf9a-4250-ad6a-eab4978de420',
      '96883a53-e45f-4000-a620-e4536fd3c470',
      'a2be7a88-904f-479a-bfe8-e6409bdc6780',
      '3ddf6c84-2790-48f0-9b01-b4282c1e368e',
      '16c1db9b-9873-4ed1-b936-2564a35af6e2',
      '1b079754-55ae-4861-a337-681600555a24',
      '327289e0-4226-4c5d-a1e8-ddb756c7072f',
      '31c154e3-19f2-4764-a634-09e0b691b266',
      'bba3f41a-3ac3-47c7-81df-91b253a60131',
      '36e35410-c63c-4c73-9e7f-8744d36a706f',
      'b9d09727-1776-4f3e-a356-6e39291326a1',
      'cfb4affe-2f06-4ada-9662-875a43ad6a62',
      '7e2e968c-96a0-4986-a037-76f5f0baf7df',
      '8e675954-664d-4d52-b7a5-d53d84a18205',
      '04193f22-b562-4acd-861b-ae4154848a53',
      '24b2a2a2-dcef-4b2e-8593-145c1a8ce300',
      'dfac2863-c58f-405a-96e0-a3d7b3bf6a8d',
      '9d9c021b-4a45-48b4-8ea8-68c4426bcf02',
      'da0a89cf-7fa7-4097-86b2-35d1c046d529',
      'ea0b0433-2cd2-4fe7-bd14-acafe6def15c',
      '36ecbc78-98fc-4115-b085-f66f2c7f0bba',
      '1575855a-317f-4c4f-86a6-f874d77573da',
      'aecd8af0-57d3-40b7-8efe-bcffa566b7f8',
      '11a19c11-2522-4d74-a45d-6ddf11f8c673',
      '48c36207-b07c-4f27-ba09-e3103bf5689d',
      '03e44935-8ff8-49c1-bf2e-5529e84e09db',
      '82a1f1bd-7916-4470-ae03-009eb0bd9938',
      'd7a4cbe2-7b93-45cd-9509-c676444ac24a',
      '571d4beb-d6d5-48a7-8ff8-fff5720c73b0',
      '666b07d8-fa01-4887-9b72-003edbdd099b',
      '9bcb2f8e-a568-4ce8-93e6-d43c8565dbfc',
      '434e07a0-6679-4c21-9e8f-78556551cdbc',
      'e7a0c87c-7394-423a-9b19-fc592d1d9486',
      'c4dee7a9-2c7b-4f89-9aa1-efca883d307d',
      'fb9633b5-ac67-46de-b8ac-b24f14682315',
      '03f8caaf-d8fb-429c-a64e-51ac5379763e',
      '3c6e00c2-c07b-4851-a6ca-e21b3cd0fb4f',
      'e1f8cf12-a41c-49d1-9ee0-a22d094d7f72',
      '443d354c-d5c2-46e6-8b06-2bf2b0f68133',
      '494f3968-c75b-4d2f-b400-65e71aa134fc',
      'eac7dfeb-fecc-42d8-913a-c6f02f2f98f3',
      '2923dd5b-778f-40fc-8884-a41ae1dec496',
      'd3681398-0e46-49ab-9ba7-693478412ff9',
      'a5fc4fbd-90ea-4ca7-b7bc-a84e12021a26',
      '4a712fa9-3dae-472d-af9f-658c5ce0aba9',
      '4e4c409e-284c-4675-a8b1-441da18dcb76',
      '1313733c-9efe-4645-a62f-ea166d9a8207',
      '96d9ffd5-c8e2-4217-919d-b5cd99a159ea',
      '66f3c11c-46fd-492a-be14-c0651d09b356',
      '2cef2b78-8c55-42e9-84ad-21a4d3b1eaf7',
      '9d5767ac-547d-4252-be06-18d44a102cd9',
      'fd980359-97ba-4f5b-b244-311ccb1f6ce4',
      'a80aeb3d-e91e-4fd7-bbf6-76e88b46f913',
      '15bfbad2-10d9-4335-9cb1-add2c24ab839',
      '61605e4b-3519-4a37-990a-c8c6311cb7b8',
      '0aa301a9-1b15-4453-8cd8-561b41338228',
      '0fd68da5-f50c-4b9d-9a74-08dd5c3deabe',
      '9ada5da6-0e6c-4091-bf00-ba769232ac41',
      'c582a681-7efc-47e5-9936-a7bfe1c13a34',
      '325c95f2-ad3a-473b-abbe-7c518fcabf6c',
      '23e8f3af-7134-4c23-a6da-4daf266d1f19',
      '5bbf8231-e951-40ce-864d-47c1725374b4',
      '2a44c77a-2af1-417d-8b18-4d24d02afb80',
      'c92fb22b-0593-430c-872d-87762a099aac',
      '8f6abb23-1ee2-4ecd-8350-f3592bdbafaf',
      '70ec750e-97ca-4ef6-8f42-a11735d07d27',
      'e45fbcca-8599-4f89-888b-dbffd6eb3255',
      'a537231d-fad9-4c53-aedc-b408253b445c',
      '773aff3d-e5ac-4f5b-91d7-a1c55def3ba1',
      '47bbb426-9873-4298-b48c-a8c5a1bd71df',
      'a23bdf26-0e81-4c28-a2e4-2f968ae1ef90',
      'bd226ec6-b072-4f5f-b686-4cfdfc38da3f',
      '34a94917-c0b6-45df-80d7-0a15050a2498',
      'e95f62ce-bdad-4f89-9f16-fa1da03661f8',
      'faf1e8b0-a0c1-4ddb-9c86-8f37a694d1a2',
      '9bd24687-aadb-42b2-9092-4577f9d36172',
      'dcac7005-bc4e-4472-9a7d-6ba94bf31726',
      '71599f9b-11ab-40f1-9682-697e23039f4e',
      'f0344efd-f65d-43a3-b03a-4dd4aa7fbe9f',
      '25a24be9-e881-4f1c-912a-417c2ac9d75e',
      'cdfba2ec-74ab-405f-b102-bf8aa72c67f8',
      '0ca2bdc8-e350-4732-8114-4c391599ac28',
      'd0061502-8ad7-496d-8ccc-f7bdcf563b0d',
      '66b27bd3-8c58-45b2-b1be-2279a9c3d912',
      'cfa1cf1c-8432-42d2-88d6-4caaf0a8a941',
      'e1e7fa50-598f-4d60-93ca-f85b211b90e4',
      '365e702b-37cb-4f62-b355-0a5320289ac2',
      '8ba42df5-94f5-4fd9-88f7-c559922f7afe',
      'a3f7bb16-285f-437e-acaf-2ad27b9e6b02',
      'd9e02644-1606-449c-8add-c83b41984bb4',
      '8dbd281d-3682-4e8b-bacf-c81582141640',
      '75dcb8da-b6ff-48e1-9ba4-2c72dc9e6739',
      '52a9f89b-cd6f-4e67-915b-7a76b15efac2',
      '1fb6a75f-1d33-4ed2-8d43-b42b68dafea0',
      'd85e0c36-e15c-4a59-9c02-a4c9fcd81508',
      'f1c0c10f-5e12-4426-bb02-3d76a2db10a9',
      'b0c8cda3-e656-41fb-8b1a-e313b393bc48',
      '93cfa66e-987c-483e-ae48-6c7a82c7e37f',
      'ef72a630-d103-41b9-9a83-6337bc522ed0',
      'c486d86e-9971-4ec4-9b0f-fda7ac219501',
      '90917a8f-5c07-4770-8532-d1f449db0b6f',
      '1c3b36d0-02ab-4f5f-bad5-0ada8a7c8c32',
      'aca6ed54-c94a-41c3-87ef-9f4e8ad69f55',
      '4e138879-ec80-4448-8250-ec92b946099f',
      'd4a16ff0-2c00-4fe7-a490-37e474b1b71b',
      'dd4d1b4d-f79c-4afb-a5be-ec0c7d92a879',
      'da5aa3b9-37e3-4067-ad6e-1c704370a9c7',
      'faaaf834-76e2-48b1-bb91-1a524d34afdd',
      'fcb106bc-1fa4-4e8c-bb95-4a77cf324ba5',
      'd0a3bade-309f-466d-b2d3-2e2d9b6418d0',
      'db4eddf4-d98d-4e00-9f57-5860ddf9d99f',
      '13f8bcc8-8e9e-4e78-bfd0-2996c590645c',
      '9567196e-6142-4654-9c1f-e71ea4ebc39e',
      '38d15c21-efc7-46a4-ae4f-025c3be48015',
      'a6d2161c-9669-4c58-b4c9-7c11d2abaac8',
      '1b6e41c8-4935-4eac-8cec-c14931c9d527',
      'a3c776ef-9d88-4385-9729-68e77ec1fd47',
      'ae13e506-3de2-41e5-943d-5772734b48ff',
      '50df980e-3ec1-4909-9aa9-fa8b4954d689',
      '701fefd3-89e5-4188-836b-c21dc689ed3f',
      '4fb75d44-f4a0-4cee-8fc1-6e530dd70324',
      '560bcc28-8c66-4be7-be86-a76c225c33c5',
      'b94b48ca-8af4-49e1-99f9-3bd5e4478890',
      '5249e0c6-b3a8-4121-bf98-912a5807542c',
      '6c4d1fe6-651c-4512-81c6-6c6ff9518ad3',
      'a75a72bb-033c-4f8e-844e-a7fb0136299e',
      '5946cfd0-2639-47c8-91c6-eb920946e311',
      '45e14777-f70c-4869-8df5-43c1a833f5f5',
      'aa2214cb-1a4b-409b-8995-3f30fadf6322',
      'bed40f03-3a9f-42fb-9a9e-1b81218e8a06',
      '2474a118-78ff-45dd-ba30-b88f2855ce37',
      '33ed6c91-482d-4c5d-98e4-ecc1831d52e5',
      '2389b76b-a486-4077-a3fd-24bc5fee49fe',
      '07019b76-6c07-43fb-b3ff-f8352b2542ea',
      'efaeb8a2-42bc-4c53-9661-236c38d68398',
      '80a3662c-9831-4dea-99a8-a78ab5093397',
      '50a647c0-1982-4e18-a523-e147a35a9e3f',
      '79d976b1-525b-47a5-862e-c940d49fe817',
      'b9e4c2d8-f49c-423c-a01c-610cd1d44423',
      '528125ae-c7aa-48a3-a65d-9646aaeb8f9c',
      'dc81b6f9-42a0-46a8-bf5a-8a17567feae4',
      '0bc989eb-500b-4b5e-b5aa-1bc57c4484cb',
      '7be5ae98-2500-401e-82ee-df4fa028436e',
      '2fde5f36-421b-489f-9673-8ca8f457849a',
      'e9eed74c-3ca7-4d9a-888f-44e7736b25f3',
      '8066d2a3-61fe-483d-b0d4-2fba114f971b',
      '9008cc89-ebbe-485e-8732-6372a67fcb2e',
      '43a644e0-f1b9-493b-98ec-de491dc71f21',
      '5f535914-d87f-49e4-8973-1e9c5831f72b',
      'a43a4cc5-40f7-44e5-a8f9-91c017eceffc',
      'db05f326-48d5-41de-ae23-762bbe8d3b4e',
      '10bf6d26-a450-4216-942e-f94cd7a7ffcd',
      '588cfa8b-f7b1-494c-9832-f23791c3d0c8',
      '3c70e8ae-3c31-4eda-9a65-8ff169af04ef',
      'af66f1ca-1c7c-4e6b-ad1b-38ca9998dc7c',
      '5ad0f0a9-9174-470d-8a48-2e21e84b537c',
      'f4200513-0772-46ec-a0e7-5a9a81941d6e',
      '033d3a80-5f9e-46f5-8ba9-d663aa8d9eaa',
      '8a974bd8-5dbb-4ba8-a6b3-db122b7dd27c',
      'bbe95c3f-c8a4-4425-9ed0-6fc4e3ecf232',
      '74cb3261-ee2c-47c6-ab9c-9e3de59156fd',
      'd535c447-b8bd-4bd1-a1d0-2c7b06f0f9d7',
      '54a8daf2-bde1-417b-8103-35aa1f7ac614',
      '4a8a37eb-4aa6-4aa9-857b-77b68ab3fb6b',
      'dd6fc944-a0b4-4753-ba9c-1c4696f0a9b8',
      'a7c4f6a9-c541-4ed4-8339-0f8634e38e48',
      '490d8a98-19fc-4d1c-be2c-c8397f73d2e6',
      '6b64c388-ef48-4769-9c5a-3d359c9302b2',
      'fa7a13ef-e293-4bba-87f1-f9882ef9079d',
      '391febcb-9025-4949-9e2c-188b7980ddaa',
      '28d86fa2-4eb6-4bd1-84cd-f045bca4ef28',
      '5605b50e-c1aa-46dd-934f-89493eb16540',
      'a6a8c8bb-d398-4911-b16c-f35e5846d3c3',
      'b295aaf5-ac4b-4090-b176-d3bf5aaad613',
      '8ce2f436-430e-4149-a7d3-43b9827e8de5',
      'd4fc253d-bbe1-430d-90a1-16ec468cc4d2',
      '6047d71a-b237-4ed9-9952-4c3b47c58ce5',
      '5bcab697-f57f-4529-b5cb-2e3a7b10960e',
      'b8c219c5-c44a-4f62-b02d-3c67f22cf9c0',
      '16ae4590-6581-4404-a8ef-cb0fa39adcae',
      '2cecec88-cee7-4a92-9f30-bdb75dcc6a96',
      'dc9ef8d2-7ba7-480e-bb14-805c87e219c6',
      '93f682dc-0c3f-439b-847e-e3be497089d3',
      'b24c6abd-22a0-49f6-bfd5-43a60576dd2b',
      'fc278f54-1197-48b8-b0ac-86407abdb670',
      '2f4d17a8-c2da-4c21-985f-717a256a8b4e',
      '934e253b-c44a-4f46-84f3-ad950a2f4caa',
      'ca5768d8-0999-4016-970f-de46cbb4ca06',
      '30df0484-95df-4f25-9c05-8d9c1f2bd473',
      'd1690f04-37ed-4236-8407-c2e6818b00f7',
      '548e7df7-23b2-4a75-b405-93d73c9672b2',
      'd8e7dd75-163e-4f24-ae29-8b04159bbadc',
      '222c03a0-3668-4a33-8ab6-8d10b3bf4b9d',
      '363579ed-d1ec-46a7-b4ef-ce210d919880',
      '32c0e9d1-6e91-4d64-a86e-9c46f77e66ad',
      '753350cc-e1b9-4f8e-8bec-1b5dcd13f530',
      'b6f14b3e-9a85-42dc-804c-5dcce8e2a27a',
      'c41cc482-3a83-4a3e-827c-0203655fb309',
      '54d30dc5-9be2-44d9-82ad-843874ec1b06',
      '32758fec-e574-4d36-bd10-5f4fe4e059b0',
      '3175527d-faf4-4207-ac21-ea4161a198b1',
      '1b818fe6-97f6-47b5-8590-d5b855200705',
      '75bd60af-a19f-4f49-bf32-24050d4ab973',
      'd099046b-7da0-4a91-8fd9-16f4c255c5f2',
      'f13929fa-7b38-4f0e-bc2d-af65026c9dc3',
      '39e31893-f48a-42d6-94bb-0b1fd786cf23',
      '9e057e0d-6931-4e90-8f2d-4025217732a8',
      'f900ce38-78d0-48aa-ba0c-c08726fc15d5',
      '93656744-2fe0-4dbd-961e-68433a786059',
      '62cef510-1d77-44b4-9d0a-605147c72925',
      '5fcf1dc7-c60d-4692-a48e-4107e7ea00e8',
      '5b5d9f57-5cab-4dce-9d6a-17ec04f88003',
      '7cee02bd-645b-49ee-953e-6edd3b81208c',
      '22c5d379-937b-4186-a4b4-56820fe8b199',
      '692f1dd9-34d0-4272-99d5-188eec0f787a',
      '9322a4b9-8ffc-457a-84dd-1b731110bcc1',
      '2b9752ed-9c41-44ea-86d2-9e2c1a28553e',
      '52d69d89-cb8b-48f8-8f0c-3cf7fcba9702',
      'f40fee90-5016-43ab-a287-d4ee5cded4e5',
      '74d1133a-c09c-4aa9-bde3-d61a50461d1a',
      'dfdd44fd-57e8-466a-bf8a-9643a1240ab8',
      '4e8d5144-5931-467c-8c65-fa0ebe8bce83',
      '4ffddc1d-b2d7-4245-a702-97b479464ef0',
      '138f1fe2-c382-4e4b-877e-5a317bb2cfef',
      'fd4f7999-21fb-4d29-ab2f-9dcd3ca08d1e',
      '97d5a790-37c9-4960-be7f-af2893e5db71',
      'a222bf7f-08a1-4028-8794-79b32489a404',
      '1aaa48bd-fd58-4ca8-a3d1-4ae2b43b6371',
      '4e3ea9ca-257c-4485-9f7d-fe442430e840',
      'af51f5ba-5547-4601-8f9d-9e81dd119b59',
      '7d8ad99d-6d18-4d75-aab5-bfcb3c051ce8',
      'c245631a-e87b-4916-abef-d83f63382b89',
      '83f0f585-6293-484a-9aa0-385c57ce0b34',
      '857a211a-70ab-4e9f-a1cb-61366422d834',
      'a9a013cc-5112-489f-b21d-9c5b750ebab8',
      '55eab8a3-1471-4a3d-9ff3-d07fabcf49ef',
      'd30378df-3b97-4e8f-94bb-10cad20dced4',
      '2399f358-4a0b-4f19-ae45-de66515e83c9',
      'dcf05015-546a-43f8-ad48-d3a699584b17',
      'd5b27413-d34f-4860-a4db-1a54cd5a215f',
      '353ad30c-7049-4e40-ad0a-aaa830bb708e',
      'eac42d7d-2576-4f51-b9c9-acd3ec93c1d2',
      '04f5fe4b-50db-418d-9b59-7c243196ce52',
      '4b188cac-f2e1-405d-a5cb-903e1ed0dcc7',
      '07ebf3d0-e096-49e5-97fe-a8e25506d1ff',
      'a82b1ed3-0f97-4659-bfa4-e135e37fc23f',
      '6ff04da5-237e-4bc3-8dd8-9dac0aff4461',
      'b8852320-66ff-423a-acda-18fe986c76f9',
      '0415553d-6152-40de-b740-f4003f3e396e',
      'a50c07e2-572d-4beb-901b-b06f833d1317',
      '7863c873-6e23-466e-8830-337338fb780a',
      'b076643e-003b-4ddc-8b5f-c80f94ae5bcc',
      'c99845f8-950a-4e4c-8fcb-99f26fab2143',
      'e019a214-18e7-438a-a64e-0a301808f01d',
      '512dcabb-05fd-4ee6-9c87-2a61aea5195d',
      'd22ad492-482c-48e8-9ccb-38cf5f35f7fc',
      '8b84304a-f94c-496c-b491-643174a37c3e',
      '6efc1a7c-6363-473f-94d6-84ac526a6722',
      '07755933-9e97-49ca-8b90-d4ae0407425a',
      '80528ddb-b378-4370-aea1-55f6cd65a503',
      'f2006ec8-f4fe-4f2b-814f-921463409706',
      'b688b158-5aa4-47df-a7cb-1135014cc50c',
      '93dd3381-a84a-4094-b9e3-8aa1d4472cae',
      'bf7a7531-ca0a-4e89-9b93-c546e887fb38',
      'c2e4ee1f-2a34-4cbf-92c5-327ecd0dd9a7',
      '9d1c535e-b831-4033-b23b-67e20c7ab54a',
      '0d4b5674-e586-45c2-aee2-e4eeee55ece3',
      '34327b40-161b-4669-8ced-e355c02df189',
      '389e6a0c-bc51-4140-b317-4c5e2a92a77b',
      '69f2f293-5f8d-436b-933f-59cba2e64daa',
      'd7bfedb4-0f2b-4266-b044-d5da3891615e',
      '0bef8874-6c44-4af0-9d28-5cfac6963709',
      '1f72e686-107b-41ea-8acf-db4d9db16f3f',
      'f095bc9c-b871-44a2-b6d9-ab7f207a7e7b',
      'a6ae7a9e-03a6-4535-aa6a-82c34f44036b',
      'dc1d7a5f-e38b-4e06-81dc-3fd6f58b724e',
      '096530aa-379c-4bac-bb13-52f1af671ee4',
      '864451f6-95b7-4eec-b3cc-d69de483fa31',
      'ed25c7eb-8fd5-4ff0-8be8-bb750cbadc26',
      'ac30625a-7472-4eb2-b9bc-07a2441326f7',
      'c46a8bb6-a961-4331-9a62-6bc02ae888a0',
      'e467928f-875d-4af9-be11-1fab421463cf',
      'd0f6a604-0451-4a83-9b68-2350d45a6a79',
      '784fadb6-8df3-42b8-902e-f21c2bf9ad8c',
      'b2aea4d8-5e84-4906-99b6-5c0382e64d74',
      '79e7b26d-fb78-4cc0-90bb-2486a9660ebf',
      '496fcbbe-8441-44f1-9f92-8b712576d98c',
      '4fb8def1-3bb4-494d-b60f-665912b9fba7',
      '78e4fa66-a8cd-432d-8c91-0c06c3683c12',
      'f3be1f08-a9b2-42b9-9daa-acbe69da1237',
      '34da10a5-9e5f-40fe-9795-c1df1ec3c11f',
      'fb91fc08-138a-4092-9ada-f6aa79ebddb7',
      'b229db4d-69aa-4927-9ef2-cca0f8b55143',
      '2097c189-6a97-4fe2-b398-131bae62d2b7',
      'ab13503a-aa40-4214-9210-b95e8c7f6537',
      'fff7c9cd-0276-4ac0-adcf-ef9d1fc5b5d9',
      '8e7fd9bd-5e4b-485a-84be-00d295cefa62',
      'a53b9ce6-ca18-4e74-b8ea-33fb05b2cf69',
      '779d6485-b760-422c-adae-fd079e42474e',
      '34335c22-5998-41ae-a163-69faa485bb30',
      '37814b37-c8fd-4585-91d7-ec7bf6ebfb97',
      '45c1d766-3de4-4d3f-8452-0d2c819843b9',
      'fa7cb0d6-d3b4-490a-bbed-642ddd92c71b',
      '5cf6fd36-44df-454f-92cc-0f7cac94a466',
      'e00f249e-44c1-4f78-958b-f906c7397f63',
      '32d5cf98-c128-425b-9992-8f8b7634118b',
      '0295e979-8333-4de6-a1b2-f5e366fc9163',
      '1eaed741-b643-4e05-af6b-cd677d01b89e',
      '8fa39b5a-97bf-4b7c-a770-3d4db401e951',
      '5773dced-c642-46ee-8f1e-6720a20a9a7d',
      'a4930444-7da8-48dc-bad0-d41ed287889c',
      'bdf090d1-d297-49d1-ad6f-457979664298',
      '87196912-2d45-4b0d-aa84-b16bf751d7de',
      '8764878b-7e1a-45f4-bed4-2821e8d99434',
      '09d556a8-7e8a-4eaa-9f94-2ab35055d915',
      '12ad125a-7233-4157-b1a1-a2133ef4262e',
      '77fb1db0-4651-4f95-a8d3-fa7ad0cdb6cd',
      '52a1be0a-b9cf-4a24-aadc-4cbfac8d8000',
      'f9635647-06ca-4957-885a-a93a8032b325',
      'b628d908-43f2-4723-88ae-19a5dfa80029',
      '39dddb4e-5ade-4070-a58c-48f536a526e4',
      '57e26040-b162-4d9d-a619-778a1ee66e83',
      'f58a9ff1-efdc-431b-b3be-375ee204a2b4',
      '36912ba9-8939-4c40-8c61-2fd133ca61b9',
      'edcbb7fe-8e6e-469d-b6ec-5655294a5cdd',
      '7d5ebd56-553f-4d1a-9b4b-afe2187df89f',
      '708876b4-668b-4699-9da2-c9219b0f56c9',
      '7ab970bb-3cc8-46da-be66-cb68fd11ae01',
      '5a6fd6c9-0157-4593-a8fe-e97a6271a0f6',
      'f66bce6e-5b58-43f4-a96f-317367ef1dbe',
      '5001223b-0b85-41f8-8723-6b3d5c73c300',
      '132c7d3f-a5d2-49cb-907e-7c6eb47a7e45',
      'f767a3ca-2c0d-4779-8b84-3e689cb52f35',
      'cb6bb0ba-f932-4b14-9da7-9e0c20331c46',
      '2cca0708-53ec-409f-8095-f656eccb4b2e',
      '2db24202-2d5e-4a83-a040-82cd5602283d',
      '46d063ce-df2f-443f-8fe6-537d301c8951',
      '9b2b2557-69a2-4062-8fd5-adcb440e5cb9',
      '32c6000b-402d-4267-8d4e-28f0cae303a0',
      '83f64e1c-0040-4cc7-9dd9-488c29b5312b',
      '4324a40f-0176-4e4e-a57b-33914a9ba77c',
      '03cc6258-0f3e-4257-9323-cc7ab7680077',
      'ed4afb36-2d8c-4c53-bc67-927b23ebb507',
      '64e26909-5136-4b38-ba79-01311bd02998',
      '86ab4a33-a242-4c6d-8dcf-fbb37ded57a7',
      'efa50541-a38e-40d5-b28e-5a505e1f8068',
      'fab0673a-ec1e-402f-bedb-856a25472403',
      '2bd86a61-391b-4531-94d5-e28c2116108c',
      '9b37e58a-a1d8-46e4-9a1b-9826f2c5181d',
      '1b6a1427-a9d3-41dc-bf58-2d3b54391d92',
      '9fa67e6c-ff72-49d5-b9f3-71483411ab45',
      '04f969f6-06bb-45dd-bb21-6d3bf5e6f0f3',
      '0e2a18e6-20b4-4225-86c6-b4cb856a1578',
      'c497b0d3-63fc-47e6-bff3-baaca541cebf',
      'd766ce39-5770-4f65-b352-92582a416b33',
      '61b9d002-365c-4b7f-9174-ba92f6eeefa0',
      'ef1cd603-c82f-407f-8a4a-80c45533a8d8',
      '95bb6319-285d-4872-ba87-767a489d7902',
      'ad579d25-de15-473d-a117-f45573b54a36',
      'b44993d8-1b38-462b-b304-1313980f96a6',
      '14946f81-fb12-42f7-9767-72cfdbfe8702',
      'c833fb7d-8cc6-462a-9b50-97fa94d5b126',
      'd5d1a594-2405-4102-89be-94fdb9463d48',
      '25be3072-6bb5-4817-a5da-9fe0024a1dc5',
      'f965fe57-ddfb-4b4e-b136-180e975eac17',
      'bb4df1a3-f433-4fcd-8469-b44fed3727ca',
      '9cb97869-2cfc-4ec0-826c-e760e8a4ab61',
      'b17b7190-c6ab-4250-908c-d93cdb9eeea5',
      '066767b2-39c4-45a8-acdc-438903671d9e',
      'e33f4b87-b019-4503-8709-60b3867ee646',
      '405986c4-15c0-4df8-92ea-42d5468b1557',
      '2fc9aa45-cd25-4f39-a664-53f803447f3c',
      'f95fd966-b7df-4834-85fc-20b3bd111484',
      '6b8843dd-efc4-43de-9260-34f4f2ad75f4',
      '7dd49ccf-ece1-4cc5-bb6d-360bb1960db0',
      '3a9886e5-f735-409c-8598-9ce24e1afe0b',
      '78e37e5f-d2b8-48d2-9bfb-bd039707ccfe',
      '19513b58-eb87-447b-a75c-08742729450e',
      'a63fcf43-21fa-4150-8a8a-2c44c9f4980e',
      '63af1147-c606-44c8-a2a4-c233c6c7539b',
      '18516352-0e3f-4b41-95b0-c9c206e9f8b7',
      '422cd82e-c3d2-4156-9dcb-40ef93afe04f',
      '1f2fed68-3bca-4a80-bc83-c3bf1caa78cb',
      'a0a1d82b-66a5-48ad-8f3f-f036baac2430',
      '29f8fcad-e39e-4fde-a90c-4a2bc9283b7b',
      '201e0926-9640-4ec9-8862-4f60764684c6',
      '48b6e75b-c876-4703-bfb8-cc2980a8c78c',
      'b166cdb5-313f-4433-8fc8-83e7cd91b534',
      '91a27a28-3c40-492f-afb8-eeb93d277d8e',
      '01f50431-0479-41e9-83c5-97c8be4bbb3a',
      '3aef777f-b715-48b4-a260-6d4bb0527468',
      '9b49a99b-892e-47d4-a0cf-3fa90c48bc9c',
      'f703dbcb-ea4f-4151-bc24-d65e7803691f',
      'a1f4fafb-6f69-4aa3-8aed-d3edcfd30d66',
      'f84ae50a-c6dc-4ca3-8977-e0eb90391600',
      'c0fab4e0-9596-4509-aaf0-81859dc979f8',
      'dbf0678f-d89e-4179-9797-8fc51bb02c13',
      '46216c12-2796-40c3-8b3e-f6f2de0c2967',
      '27933834-6d7a-4838-a64d-d5afed9ff7f7',
      'e937c1ba-c800-407c-89d6-b491678ad1bb',
      '5687d3a5-2f5d-4b6a-9714-960b5092cdde',
      '0df9f163-cffd-49e5-88a0-6a265ff6adec',
      '2fd40dde-44ef-4aeb-bbcf-ff0ac3b9db06',
      '4aeb9680-187c-4aac-8d68-118d111a9a32',
      '4e023d20-ae3c-4641-a247-de79af8f09c5',
      '65de6e4f-4d88-4928-8fa0-ca82aa6c13cf',
      '17f16ce7-d351-45e8-9a19-2739367f593a',
      '5fb1d647-690b-4d4d-bd38-99b6aa3a9066',
      '48619cc2-077a-473b-b2ac-af9a9f463039',
      '90d0df9e-0687-4de6-bba5-d1695c840b06',
      'd55bc8ec-7b24-43b6-94f5-9e0e3eb68d78',
      '5cc846d1-829f-49b1-85de-070ff3dad21d',
      'a6bd5431-a532-467f-96ee-7d9e687d8827',
      '5b564fbd-c049-4364-b673-4d0e89a7a713',
      '9971a86d-96b2-409d-b99d-a3963ca1002d',
      'f889b2a9-609a-4451-9f6d-b5297a09c3d3',
      '72a86a2d-ace9-440d-bf4d-010eb2a077db',
      '56e0e5f8-0427-49a4-9fd8-2dde0b1bc144',
      'df5d5c0c-7b22-4601-a4cf-c78f63b1b947',
      '153c0e8a-5caf-4df6-98e2-8895e5c63fa3',
      'eed5221c-693a-4193-acca-023d2e5050c3',
      '65509c27-16ad-47df-a5ed-a992ae2e376c',
      '872784ba-0743-4f05-a768-e56d57e3e8d5',
      '5c33f96d-f273-48c3-ba05-7d714f0bdedd',
      '473dd3c5-9332-41a0-a055-f176fe234ce1',
      'bbb37c49-21ef-4f39-b6a1-5e913c22669f',
      '6f11b365-1454-4f22-980c-f5908194ac6a',
      '687f041a-ea3e-459f-8203-66683e99b198',
      '1a3f0ec3-af36-4335-a688-ca694880bf49',
      '9dff49dd-8479-41f1-8613-57a6fdb61543',
      'c0e8a4c4-f1ed-4a92-b472-9269d604f457',
      '4354aecd-38d3-4e39-8c97-0624756fb500',
      'a75843cc-5478-476c-ac0f-ef07bb241182',
      '3295a65e-a8fe-4968-99b7-2728b00c20d0',
      '3dd63742-6e36-4ced-b06b-0298d21716b0',
      '33ae3757-6371-4607-b485-5f7362e87481',
      '53d83650-138c-45bd-86fc-96fdf22e7d5c',
      '7e79f2e3-d91d-431b-8e41-1ebeba2eb623',
      '730f8956-a21a-482c-9371-d8937d63ff39',
      'd5761b31-acc1-4548-ab0b-79743035b2fc',
      '1052c187-6406-43a3-a811-6ec57fc43d28',
      '31bf058d-f665-465c-b05f-b0b09b72f8c3',
      '9f46fd3f-812d-4e5f-af74-34e9a80b0f28',
      '0db4b83d-7a22-4062-adb8-088ab07391d9',
      '620db552-5f95-4d84-a8ca-be6883eb09dc',
      '7305a0dc-869f-4944-b051-45c24924147f',
      '8b20bacf-095a-4a5f-9ff9-83f21bedcc8f',
      '26a02f12-9f1a-4705-8159-30ccd970b57a',
      '9be7f609-db7b-467e-9f86-d5c69b816d8d',
      'e311c06c-933d-44ee-a861-70a298aa6dec',
      '926931d3-7d37-489f-970a-c220bcd17612',
      'ef6f14f5-fad2-43cb-a9f0-147ee870902d',
      '9f605dda-9de4-4cee-9c47-1d09dfeff783',
      '67b9912b-5bbb-4581-be19-fa7602976b72',
      'ccb4c8ae-a136-48d8-a4b9-e1795e65c825',
      '0ed87fe7-64ff-4de8-934c-6976d40bf6a7',
      'a8850119-1451-49fd-9fce-36600f12cfe7',
      '86d38dc4-c571-49c9-ac4b-3d8d7be83ca6',
      'bd7c73f6-3086-4b68-bbd1-cbdeb0e12880',
      'b715c511-fc23-4495-a9c3-f2cde0c8a064',
      '55a3b410-f7d8-4838-8f29-fa2c3b17ac35',
      '66965da1-6b75-464a-9546-9f50036fccc5',
      '282cfe44-ad7e-4853-b6e4-82b3ce41a7d6',
      '726e0c77-f58c-4544-b470-cf1e1f93e52e',
      '36888733-0faf-46d8-a380-64ae4d273326',
      '1012ee80-681c-4fb8-b750-3280e9bf41d2',
      '181df5e8-8438-4769-af2c-8f9381a0bea4',
      '5f5dd261-d035-42c3-931b-49eb4bc4381d',
      '9a9d25ec-3f4b-4621-a8ff-28312d2754b9',
      '1c526da1-4cec-4802-b301-bb195e3dd96d',
      'bfad7b5e-4500-4da3-8fde-4ab1ac5ba8b2',
      'fd185860-ba8d-4fd8-9b10-7160d17dabef',
      'c5ae44e1-4be3-497a-82a5-3ce510cb3eb4',
      '5faf7617-efaf-4acd-9560-d03e8917eb97',
      '42cdba11-2aa2-4935-8310-62576440df40',
      'd9c2cdf9-18b9-40ca-9d78-85d4f9131972',
      '89c48acb-0b0c-4a1e-b927-40ee85a7cb08',
      '81abc081-2c52-45e1-b494-2208494169d3',
      'c27ad28e-c23e-4043-859c-39bf9803fd46',
      '5ad7dfd5-d030-42e7-914d-7cf2f6178c12',
      '6d672947-42a8-4ba7-9ac8-6bc62c730808',
      'c298693f-1ee9-485f-9e3b-254cf74c41dc',
      'f571b093-fc08-4219-be83-9806b5d07687',
      '160528b8-4bd6-41e9-977e-7b8c148539ae',
      '552c2290-58c5-4a74-b9a6-3003bdd7fb82',
      'd31509c7-a520-499b-8830-22a5aeffe9de',
      '9259d540-49ad-4155-9edf-6c42f1e3570c',
      '61033b8f-64d4-4729-ad14-8e0770b18540',
      '14cd49cd-af8a-4256-8b41-73f42d85794f',
      '22f10047-db8a-4d16-b058-9dd5a70b0cd6',
      '819c257f-9e4e-46fb-820b-9dfd40d9a61c',
      'c0af19f6-5208-4ac6-9549-dbabca27e8fb',
      '3092317f-4e75-4ea8-8850-da4374078ddb',
      '60dd2e41-c5f6-4f9f-ae46-36faa86b0133',
      'a2fd1eac-24a0-4b1b-9aa8-1ff7e4812e6c',
      '6aa8a646-8d89-4a1d-b872-f576344e148b',
      'da72a0be-90f2-41e0-b05a-546dc2f4b60c',
      '693e8f4d-3e5d-49da-9bec-64465fe6449e',
      '92d4983b-9bdb-4347-b9c0-14e0159bfa6c',
      'f28ff6e0-8565-447a-90c4-3ea5ebde0115',
      '4e71df14-b7aa-41e5-b6b4-cf07b172985b',
      '9c1fa060-067b-4ab0-b41f-1be2445dfbea',
      '139b8620-f980-4187-a0d9-e444ac87ebfd',
      '0c44e062-133e-4c7b-85e3-1143d4421386',
      '0a018100-101d-4da3-a147-9511b4c15d9a',
      '312e0928-ab53-4943-9597-ed5b1bf9778b',
      '65f713a7-f625-4a50-8d97-f7a24a5d8921',
      '377c7936-7421-46fd-8b61-6956e866eb86',
      'c67cc02a-8a5c-41ff-beea-7f549c951d2f',
      '205cd1e4-a2cd-49c1-bfb1-ecad2ebc1741',
      '9181146d-6fb7-4006-9446-32664634b8a2',
      '707a1685-3a0b-4b09-adf7-418071e872f1',
      '7835e9b0-703d-4aff-92db-90461c022067',
      '46a8a05a-935a-42e2-a487-bb2c389ac744',
      '72a5ee79-8898-4509-a802-68f580d9ccbb',
      '6c1c5a1d-42d5-4567-b445-e83872e4c6ae',
      '51f3e6ac-3781-4bba-bfdb-f31a9cb8bb2f',
      '5fdf06a6-b7a3-4a14-9261-0e02333cbbf2',
      'f976858b-3509-4468-a31b-eff0f3e00ac4',
      'd627a2f6-9897-4d51-a5bf-73c20a6fe621',
      '9ce7c902-39a5-4114-8383-82ec07d0405b',
      '0b8d56e8-a3dd-4497-ba2e-324ffa96de21',
      'efdf33b6-7408-404c-848f-3cec6dbbd627',
      '2ae903a1-2a06-421b-a655-469afe7491b0',
      '9b08c9bb-347d-4cd0-b498-4ac39166ed46',
      'db50c9e0-4e03-400a-9d52-150315cd25d1',
      '3aef2336-0360-4730-bff8-3f1455788886',
      'b2871b26-2807-4290-95d5-3d75dc9ab77b',
      'd7051673-352e-4628-94c1-dd1b37d9cbbd',
      'e434d441-2e9d-49a2-8a4b-64b851c85dbe',
      '9eda90d1-b987-4088-90f8-3a136340c90a',
      '15022d4c-c683-41a2-bca7-066f19807e10',
      '88e36756-6e2d-47f8-8db6-f548b45697ba',
      '3bda00aa-3f06-456f-8d27-30e2a8e2c356',
      '183552cb-5c4f-4af3-8f09-a1fdd055ee68',
      '67460e71-4adc-4ed5-b518-227e5d61a902',
      '76c17a17-89e6-4607-b1ea-d83e8ca9f23d',
      '80cfa9f1-794c-4079-bb31-9b7cb0eb78d5',
      '1e832908-d117-48d4-88de-dc8b9154b958',
      'cf58060f-fca6-44e4-b65f-b32570a8fe38',
      'bad35740-f399-43b6-8778-a25b6185c771',
      'e4b3a39c-ee50-4fae-92d6-6000355e4a5e',
      '5888400e-12a6-453e-9cee-eab444e5c838',
      '4eca9270-aebb-4b85-93d0-37c63e3dfa05',
      '50177850-c1d4-4cb4-9659-e37cedaf98d2',
      'fd611c25-bea8-4a3a-a8ca-743a269ae756',
      'a1fa92a5-187b-4a0f-bf81-361fbaf32d5e',
      '6a12439b-41c3-4b25-99f8-d34f9f299986',
      '5b402c12-96af-4b1a-b600-c52b79219953',
      'dd40b000-8cec-45c9-821b-d4bd2d5187b2',
      '59856c4b-d505-4214-ad71-dc7c93f945d7',
      '6d00c0ac-8ea6-47c7-96e8-7ba3935474c8',
      'f6686377-9bb9-4b49-b071-e84b5ad317af',
      '868c15b4-b698-478d-9bae-71cc40b94b92',
      '054e7ef6-6271-4eb6-b5b9-a8d6c9f3df9a',
      'c47d6d95-06fc-438c-813f-affda8bf4bcc',
      '70201487-ad2f-4913-84e5-c2517b09cbc4',
      '311f5766-3008-47ac-8fc3-53e067ce8797',
      'c21299e0-aa09-4177-a671-4f9969250936',
      '6d3b0c57-cca3-4003-9235-580f601d7a11',
      'c2f0613d-f164-4c16-bd01-f461f74ed467',
      'e79daba4-3090-4605-98c8-587bd5cdd378',
      '9ad9a317-0039-44d1-8e66-a6a209a882b3',
      '2db83785-6001-429c-ad1a-98781733d232',
      '24564463-eedb-4690-ae92-c1123b1bf766',
      '49d2fed0-009a-43d4-871c-1200957281c7',
      'b2942201-f16a-4fdf-8eef-4427ce2be88b',
      '84e9ed4d-9cad-46c8-8d13-8de0ce68fe52',
      'c22641d4-cd91-442a-bc30-84053460cb8d',
      'a6bee968-b5ef-486e-bfbc-ab5bdd8e7b60',
      '14461da6-0f95-45b6-9769-8106dccdf36e',
      '2d3e710a-d26d-44db-b9cd-47ad2817b800',
      '2437dbda-6e99-4edd-adfd-af9839a09eda',
      'dc210f10-7844-4a74-8944-e694c9c5aba5',
      '39a06a08-9950-4898-8651-787a51d22e9b',
      'ace7051d-2e0d-4bf8-b239-8fe80cdb714f',
      'd665ce27-045c-436e-b743-fcd53e3eba58',
      'c29539f2-d78f-4019-a0c0-dd0b4a9814c6',
      'c9b08440-09a5-42b3-ae28-a272c7d04199',
      '696db524-66f2-4758-a54c-183966a36c01',
      '0be652c1-15b4-49a2-b962-ec0f0ad36be2',
      'e28de404-83a4-40a0-bf4f-ab9068fa4cdd',
      '7267e418-f7fd-4e4d-839b-8156bba18886',
      'c87013a7-7a8b-425b-bca5-5d3c31756896',
      '36b6340b-ced2-4f1c-a679-98defa9f7582',
      '6808cfff-7deb-4ccb-a64f-080b24ab02aa',
      '40b2d38c-161e-4582-8f1e-34aba95981e5',
      'd8c4dace-18cb-43d5-a8d0-97987fdea90a',
      '904bd90d-510a-4f12-a812-122a6162e3c5',
      '093d8810-b788-4c3f-b619-323f187f57c2',
      '30c5a9cf-9efe-4771-8c6a-05c4851915c9',
      'f3879019-7d45-48c5-827b-552b8b988088',
      'e661d67d-71eb-4198-99ca-245ce69d1180',
      'a4901b9d-deb8-4d1a-ad9b-f567870a7cec',
      '07f3b738-a482-40b5-ad6b-5e4627ba3fa7',
      'b010efe6-8950-42e1-b2dc-f21da73bdb50',
      '78f56d05-86c9-414e-98a4-c3acf0f4aa2f',
      '5126c4ed-1fe2-4bcb-8537-d7e3809fc9e5',
      '08178714-6111-4d38-88c5-a3bb805adf57',
      '666be390-fc46-46ac-9a37-80b914b73ae8',
      'def11651-0db2-4bdf-9f5c-5bc484b34684',
      '53843f9e-cbe5-430e-b22a-fd5e2472c2d4',
      'c064d3e8-2032-465e-8529-8129d3de8658',
      'aaccda45-56e2-4778-b91e-31d26fc624f5',
      '2b980eda-3636-420c-ad43-c43a218d2114',
      '6d89e76a-f493-4cea-b183-4e0415863aec',
      'e0b3df47-2da9-46b6-8b78-ad613b59159b',
      '7066a75e-0762-42d3-8b21-67db96169553',
      'b1d0b907-4e2c-4e55-b6e8-ac4bc6491cce',
      '4502c4c1-8e5f-4e8c-869a-ffd5ac65de6a',
      'e651cf86-7ae3-4013-9d1c-769023f50549',
      '88929f0e-519d-4256-9efa-816f2017f953',
      '179292bd-07d0-4835-b77a-3e0a261d8deb',
      'edec0f0f-72c0-45f5-b219-bf958ca61099',
      'bf2bc9f4-11fa-41bc-b2a7-4c4626f94b89',
      '5edc1fda-ce29-44fa-8d42-5968a9155272',
      'b0fe343d-b494-4bab-bf7c-40f73ef07334',
      'd5968b4e-a997-4ad9-b92c-c31e5b4e2ed1',
      '4342f9bb-9182-47bf-b315-19b4d142b78b',
      '2a3a7fee-fec5-4b74-9c4a-be8799b69d20',
      '0aa41b77-d153-492e-818b-4de4aafacb32',
      '1af7b81a-5c8c-4827-b985-ed3918c5b1a9',
      '0c0b33ed-e6e4-48f6-a55b-2b063b3928be',
      'aa430cb8-fa2d-4c3f-9ea7-605979ad9b5a',
      '94bbe266-67c6-494a-be91-edfde402edbb',
      '8aa73e83-d01f-41c7-9b2e-606a0668a524',
      '1bd0cf4a-58f4-48d7-ba9a-553f1944dedf',
      'df47ccd9-c855-4a1c-aad6-9873b8a876fc',
      'ad9ec653-82c9-4f75-a910-6086243dc38b',
      'ba91567b-3926-4d30-968f-aa71a6c76125',
      'bed6967f-21cc-46eb-8803-29f64c563d35',
      '32d792f5-555b-4930-9914-b03f4c9f213a',
      '7e82e998-67ce-410a-adfc-dbca3af934f0',
      '6ee81196-9104-4951-99e1-36846694a1ff',
      '78ed1c5f-4cf8-4e52-aca7-f908f5931492',
      '1c1792a9-2a94-4c1d-b9bc-4e140148530e',
      '6293a09e-deea-42fe-8f0a-faad5574732b',
      'd1f85743-d04d-4a9c-8a2c-4d0c599c0729',
      'bcec0195-9e60-420a-8042-72cd4b9bf170',
      '4305f85b-b06e-4d6a-86df-75f26b713223',
      '3038050a-c6f8-4681-9329-bb7ff5d01917',
      '6fd92c4d-ef51-40f7-99fd-e60db060691d',
      '3374894c-675b-41d9-b209-359c1ee5a6ba',
      '54d8deba-42b5-4c65-85e5-9c50d991496f',
      '59b83486-1f93-4374-8640-82f0625a7144',
      'bd2c6b88-49d1-4c2a-b7e6-d8748db16c1a',
      'bd67da96-470b-468a-b370-c07f3d6cfdc2',
      '8965b115-890a-46b7-9c4a-4598be889711',
      'a3ba4d55-2615-4d04-b732-f85b67988e7c',
      'dc053b5e-0b82-4849-8feb-4f96291ebf03',
      '4f4ac809-7e2d-4954-9945-59e1a05ddb45',
      'c91ad59f-37b3-4236-a247-a03156a98f4e',
      'cb9bbb5e-8548-40c1-af6a-556da90da0b2',
      'c1955637-1f6e-41df-80ed-4876fdc684f4',
      '04fa80f6-6000-4224-bf59-9ef80c793b1e',
      '69cf7812-b4cb-4bcd-a386-218c7756ff8e',
      '255ed1d8-a111-4fc2-8e0a-5a7ced949a53',
      'd88866cf-0bb3-4872-9a0c-c866c9f7c6ac',
      'd1909bdc-8492-47df-abf5-e41d82fe9e80',
      '6324b430-f7fc-4b31-bb5f-476bfc9a9b61',
      '2092886f-e5e3-403e-88fa-cc6be5092691',
      '394e5ad4-56e8-495c-873b-2561934494a6',
      '73282820-8cf1-42e9-bf9e-6e0c58a47a3d',
      '8e6ddead-184b-4569-81f4-8d81016b36a1',
      'ab150113-faee-4219-97ba-ebe24d5c27c0',
      'fb143028-b5e3-4a4d-b172-b255efb315fe',
      '1e219ff6-5f8c-4e19-bb6e-0092f85435e6',
      '50d652fa-a905-4547-8261-a601ee345e9d',
      '7c84a1c4-0dcd-4b8c-8134-77bc5be642da',
      '9edec71d-3541-409f-a704-7ec5c1b9567e',
      'ebe6dd87-149b-4526-b04c-508f56c7d2bd',
      'e946ad63-4e78-4566-8260-325fd532b999',
      'aa0819cc-725a-458f-81ac-8af70deb515a',
      '21b29a05-3d11-4e83-b7b0-ef3dc8f6da25',
      'e97faaca-b722-466a-a65d-b6842414df8c',
      '88339b58-32c7-4b43-9327-008e3a75e58b',
      '252b2272-b408-4b3f-80de-681410f92d90',
      'c7d76aa8-5cda-4912-a8ac-a52168a0effa',
      'f9c82cc5-3e84-40a3-b93e-c8ffa51a3165',
      'e8b47368-4e17-4eef-8434-85822260ce91',
      '811efb95-aa11-4419-af50-1e19fa4ed025',
      '768b8b0f-82c3-49f4-8192-01c3fbb23cb5',
      'a352d0d4-2eab-4b98-b557-862ef4583e31',
      '665e23e8-d8c2-41bd-94c1-fa520f53146c',
      'b3373e58-2eda-4760-9a6d-6d2039c3dc6c',
      '0e334dc3-d762-4505-b9da-1ba10937c4b1',
      '0ff426e7-76fa-49cb-9908-44703ae0f21a',
      '7a8012bb-0a53-4184-96ce-7db1e7002d51',
      '7310f9fd-932b-405f-ae37-ca6b8b25323f',
      '20121bb7-e38c-462a-8576-42a50fc41a35',
      'cd1e35fe-0f41-46f3-86ed-78136fd5226f',
      'b087d6c5-f048-4a5c-bdc1-ce7dfbdf36fc',
      '909c726e-7c20-4490-9db4-24c301ef2940',
      'b2896ff7-f79f-47e6-9375-1f20114d8a79',
      'c08f3485-c8e0-4847-bf4e-7f6a171031ee',
      '1fc7d77d-a592-4abb-8994-69a56f441248',
      'c60069fc-0a21-4d80-9611-b82e9b43f5c8',
      '298d0759-3f8e-4e49-87da-4f227abdc218',
      '1cf036ed-33d1-4c2e-bbfe-dc26e4b150a4',
      '0da172d2-9311-4f4f-adfb-a6118bffa4f1',
      '7c32e656-f8e4-46e0-bfe0-654cc118ecd1',
      '8ed3cd85-5844-450d-be56-0b3306a86098',
      '5054010f-36cb-42d4-9dc8-a829c22f068f',
      'f4b1135b-10c6-40a1-a04f-9dd051c81aca',
      'ffff92c1-efca-4647-beaf-e2398f7c1636',
      '83549018-ac62-43fb-9adf-55876075be51',
      '4ce6a39f-d00e-4e4a-9c7a-ddcf8ec51e6b',
      'ad8279f6-1817-4f24-8b5d-d99c45c8b101',
      '285a74d8-2697-446a-ab37-43673d9016ba',
      '13cb475e-8153-49d7-8259-673c32938c32',
      'ebab794f-1dd2-46c2-9b10-ab3c40689e72',
      '61257947-17ad-4f5c-b569-dccf740b7e21',
      '0f87d1ef-23e9-4170-9354-4a0dba4747f5',
      '0a7cd9a1-d5a9-4a8a-8415-b071b90e17b7',
      '5826331c-c1b4-4f87-8e2c-332152a4a0cb',
      'b93ca365-ae56-41d5-aa9d-4938d9da3a9e',
      '28f71ad7-ed0c-4049-af15-1cadb3e61b3b',
      'ed813f55-5927-4b1a-83d2-98d51b87ed72',
      'eaabd6e1-a727-4e63-9a0a-ace17ac54fa5',
      '03d8d25e-8fbc-4877-9ae1-3067f541cfe8',
      'f36b6248-4192-4a0d-b5af-2e8e1145391a',
      '4e4d0fe3-af8d-4208-897d-b4164a591c9d',
      '55b61b74-a709-4a41-ac2a-1706c63e7334',
      '519531fe-15af-4874-abd6-981e6ff92fe8',
      '56633195-7dc8-4b95-b316-068b76521a19',
      'd24b34c8-d26e-44e4-9731-98cb8fc94316',
      'fd79fb2f-90f6-40fd-9342-bee4bdf5d8ed',
      'b0ba2a5e-1af5-453b-b658-549c8d5cf42d',
      'a770d948-0ecb-4982-a0eb-116c8828f721',
      'b6d4885d-b970-413f-a687-8d54a04c8b08',
      'aa3bdfb4-c362-48ba-82e9-354048e57aab',
      '593ebbab-d9cc-400a-929f-9621b2f032c4',
      'a44b4aeb-a5c9-4d99-be37-92bc4089599b',
      'd2c6362e-a6e0-4223-8735-d1227754da39',
      '728948e2-6d86-4d84-8536-5be3d76ce182',
      '03190da9-895c-4976-a828-cd06d1de0bd9',
      'f2a555f4-d134-45d3-bacc-6837492fd6dc',
      '8836a12a-2345-4c5e-ba55-dcccfea3ed95',
      'fcea47be-336c-4cd1-b4f2-03c90ab357c7',
      '4cd5e62e-15a4-4730-81f9-b3430db87132',
      'c3db94ea-cd51-466c-a95a-cbe1d1dcfead',
      '34cc03aa-f7d2-41a7-a56f-8e2d19dfe942',
      '7731da68-4b39-4991-9ca4-6316282f0344',
      '57dde00d-b17f-4a02-8a3e-c05aab8542ed',
      '00620dc5-efef-4c11-8f8a-d709429efdad',
      'a61d0912-0447-4958-a18a-092ca8d05bb5',
      '2086e6f5-5471-4eb7-bc45-7d9c583bf365',
      '6980828d-5761-4940-a8b6-4fbc0b7e005f',
      '9c7082fc-1062-46dc-a8a9-a29b2719925a',
      'ab345b14-4d52-489d-a7be-dab1d06de36d',
      'f80815a8-0872-49ab-92f4-561c8241423b',
      '2704ddb7-2989-4fb5-9d26-cefa8cf3110f',
      '5daabcac-2dd0-46ec-9f56-06aa0fc0756c',
      'd815c327-9bec-4531-8704-3bf20541dd92',
      '4ad1deb2-912d-49e4-9068-85a42b6e325a',
      'c17f5571-ff60-4563-8306-82feaa7ea4a5',
      'a74b6e31-62c8-4da6-a13f-f40ab5da459e',
      '4aeb2068-212c-4f39-a161-0ad0806681c2',
      '214f68ef-055f-4433-9f45-34fbc8dae232',
      '49d47207-3f82-40ba-8bbb-4b0a45cd9dcb',
      'f3271c8f-123f-455c-a78a-afb551d3b208',
      'e7354153-7586-42c9-971b-8fe617723b9c',
      '29255846-7026-4b39-8a09-432c3f95e040',
      '5d60aaf2-7de0-4aaf-8de1-12934834db7f',
      '1398078b-0f19-42f5-8e2e-8852f9bb12b4',
      '4ddc98d5-aca9-4989-b036-510e60815b34',
      '2f8ab643-e6d0-45cd-90ec-260ddf7fcc43',
      '67e2b411-9afc-450d-8bb2-0fd73af99c5d',
      '1969dbbe-563d-4c32-938a-3c08655524fd',
      '3ba04dd3-f731-4ab5-980f-a201a6e75fc0',
      '8b1df4b6-02eb-4ad8-93ba-cfc3823e3308',
      'ac4827d3-7580-4fde-a319-b2690cd48f7e',
      '087097a0-ad29-4b5c-89c4-bcfc7ee737cf',
      '2fc34e92-1adc-46ec-a3cb-dc381faa99db',
      '03d24241-3ba3-41f3-b7e3-eca40e417975',
      'd3d90e9f-5fb1-42d6-870c-99c2fdf71733',
      '6ae0145d-ee0a-476e-b96b-0c2362e70f70',
      'a991f4bf-a00f-42d4-b1eb-da7df51a464f',
      'dc7043f4-50d3-47b7-8c99-aaf73a56b1f8',
      'ef407d86-5941-4f63-bd83-23c29cad79a8',
      'ca115745-b0d5-4a11-868c-19eb612363ce',
      '452eb277-a2e8-4c4c-b7f8-327fc5a4dcb6',
      '6f045693-5adc-40b6-b8be-6ad979a38229',
      'f02261ac-e2c5-44d5-8a1c-b8d703a3c8e7',
      '03258aa7-f908-42e1-8b78-97435d11c896',
      'a2bd8c21-68a4-473f-8132-6d34c0744ade',
      '26f01a49-d91a-4334-b429-b00b916c4e19',
      'fb58bac8-839d-4755-9200-f5cec78a957b',
      '0c066575-f147-48d0-8629-b63dfb77299e',
      '40af9500-afe7-4cc4-9c36-a4a530d2331e',
      '681eab75-f2b0-460d-8cd1-d18b84fc0e2a',
      '834818d7-b868-49c4-906f-51966731ce55',
      '09c40732-62b8-4910-b154-bd58b8077c88',
      '946693a8-c404-405d-b3ee-f22bf86f6f45',
      '65a1dfd2-a8a3-42f4-b443-c2052d85fc5f',
      '8764912d-60f8-4a2f-87a6-946d18cef29b',
      '27d1a5ed-ea65-469f-bfa0-b2f5277fd799',
      'bc1971ab-d0cc-4cf1-97fd-5e1031585ba9',
      '3dd537c6-635b-47f2-81c4-86094e83b657',
      'ff580e42-6aa3-4eba-86fc-4ebf96a383e8',
      'd3e5f97a-ecba-405e-8612-f4631d745591',
      '55c6adb5-e63e-4906-9fcb-d6d04e729842',
      '498044c9-862a-4fc0-864f-440caf582d31',
      '2b46c568-38e0-44e1-8f3b-9f63a05beb88',
      'fc7bde74-5003-42bb-ab9b-392609a6e3e6',
      '0e3ca424-f3ea-424c-bc9d-dd9ecad6c99d',
      '752fd97a-1e2f-435e-b378-12d36552c546',
      '37db1870-9b1a-442d-a6ea-42a027e55398',
      '03e9d9b3-9c1b-4b96-95fe-f2672f6cc034',
      'f78a711b-c483-4873-94bc-a090509d1165',
      'cac7ebcb-348b-4156-969e-d0e07613b98d',
      '312569be-e063-4b80-9d98-bcd638332e82',
      'e59fd67d-4e58-4332-a636-81a4e6dcac3d',
      'dd01b1e8-f43a-41c6-8d2c-6fea3f7780a0',
      'e73c5f76-2340-48c1-b895-67dbb487204b',
      '7c04e278-dfd0-44a5-a25b-64d0198ade54',
      '438b6dd2-39e8-4166-b373-0abede83d4f0',
      'b7760216-21de-4b2c-b864-89f57f0a78d7',
      '78e1c45f-b1d5-42f6-bd9c-f0395892e1d1',
      'af44b430-1dca-4365-8335-db843037f3e0',
      'c129ccde-7897-4794-9b33-97b0a6a15bfc',
      '1924ae67-241b-4492-be26-7d110e27e3ef',
      '5d018b8e-6213-4c05-91bc-22b999d3d603',
      '379deb24-f586-40ac-9684-d259433e7423',
      '2691892c-391d-453d-964d-5af362f783f9',
      '260852d3-ea34-4a2d-8dc8-0c01acea9020',
      '73302406-408f-4d4f-9841-6466f964c4b6',
      '8493ae34-bf71-494f-9b39-86d09b943f98',
      'a26d905a-f34b-437c-8d43-7de5a260e0cd',
      'bc027649-58bc-44b7-a3c6-b7a8365bf8f0',
      'f18493e4-22b3-4a46-8a0d-491b47cb54b4',
      '337051a3-8570-43d8-ba10-d6a7bf9f79f8',
      '1917b966-9a62-4a6d-8f5e-0475117f23d4',
      '8a4d6426-749a-493f-8624-edf5df6d98ed',
      '18d14e2a-3667-4adb-b8d1-98643f96ae15',
      '38579507-9050-4373-84f3-8e23dbe5f9ed',
      '58fc8b97-7db0-498e-9fab-099b465a6777',
      'be7e34e9-58a9-47e6-a038-b6f086a9ab45',
      '691beca7-6257-41e8-a28f-d1684381030c',
      '1b8f8bae-f853-4582-b34f-9680fa42cbe3',
      'f93cd2c1-2074-402a-b0f7-5d8db5598a9c',
      'fa9abed7-1d78-4ef2-8670-99981f0cdcf0',
      '61ce2372-c97c-4dee-8613-7ef2bfb52744',
      'c142e485-0c49-4ea9-a1b3-36b9b86b1072',
      '47cfe73c-7d21-495e-b9b0-2fd0942406ad',
      '2af12240-74f1-4fb0-9d0f-4ac00c72ccd8',
      '4ea2572a-2860-437b-a68f-cea84aa387ae',
      'b7559eaa-1c70-48bf-a439-97e61d55971b',
      'd3913173-0b94-47b8-a9bc-7a858b219b82',
      '588a05d7-be60-4296-a035-e89183c0f380',
      '36c65eb8-1e22-42aa-a0df-d9959c68ec6b',
      '9d52a9cc-1c92-4a1a-b1cf-92fd1b4e7500',
      '7faa895c-0ce2-4aa5-b4dd-1594698eb166',
      '6d2db3cd-da76-4b21-9d12-b1ab7323c824',
      '82715c27-9df0-4620-945f-3b00960cc25e',
      '9b8b6af6-da58-4c36-a4db-566c601f1c4b',
      'd544d8dd-0bb4-4f07-a7c9-ebbdb8c1833a',
      '08d0ae97-7e04-4809-847e-5a0bda86e0cc',
      'd89a078a-b5a3-4813-a8eb-379bb9997203',
      'a8b42933-abec-4dc9-87cf-6d87a810af04',
      'ebb5fe98-0ea7-4214-b438-f0508a67393a',
      'f93623f4-c17e-4c6c-b4dd-2a53628c8aad',
      'a5b2aeeb-fb86-4785-9f7d-bac0a7cd581f',
      '756dd01e-159f-4996-9727-b7d0927fe225',
      '3a451ae1-c1db-4d87-a5aa-30288cbcf824',
      '572ed4a9-6d24-4474-a23a-0b975c9a9c7d',
      'd8a04abe-b19c-4c18-9dd9-f6a9500da606',
      'd04057c9-0c0c-401b-8b7b-b17ec35632bd',
      '6f4fc0c4-7fd0-4292-8bd0-e8ca545b106c',
      '21e0d5a6-8815-449a-852f-9aa0d7a9a866',
      '7e3709ba-ecc3-4a2f-a33e-5d32aa2fabff',
      '1c9582b5-6706-4e3a-b38c-2b13f4137f4d',
      'be6ff5dc-cc61-44fe-825d-e62a017bea0d',
      '7fbc4f0d-da37-44c5-8c97-cd955a83d325',
      'f0060db0-fee5-49d1-b809-322dca8db416',
      '661d97dc-4e6e-4037-8abb-1b718fb8ea95',
      '71042475-1dfa-43e8-bad9-73f48ce5a861',
      '35ab8964-a7e1-4182-bd92-c059749b265f',
      '9a5a009b-f60d-4f29-bf4b-9c9eca116c22',
      '5355b725-561e-4218-bdcd-4670238cf1c0',
      '71875e2e-20d2-40bd-b44f-1ad4a58198c5',
      'cc2beabf-999a-4ca5-aa57-3cc0160add80',
      '59e5f744-8a9e-4bf6-a534-1c6e95a90e11',
      '597af693-3978-4959-b77e-be6b9f9e994f',
      '7edf2538-e293-4a94-a3bc-dc5f96546194',
      '8fbcca28-bb0e-492f-ba28-056f288ea292',
      'cd956879-072f-473c-9403-4b70f46e3676',
      '6de98e0c-4376-4707-8b05-a11338cbc136',
      '703d19b9-7cd8-4456-9be9-5e63fdb5b1cb',
      '848f7ff4-0929-4be7-9b1b-069fbf34a823',
      '1254957d-c84e-49db-b808-ead6088946a6',
      'd9ecbbf2-2ac6-4b65-ab52-d3dca88c2262',
      'c5e96006-6764-4302-b55c-07e24c8aaf9f',
      'a7badbd0-c629-4520-9fb5-426eadfdbca0',
      '6508b52f-62dc-43dd-b588-8701eedf5e0f',
      '7f3ca701-0a8c-4c61-ba5d-3edb9b6385cc',
      '72d69a13-b03b-4a8a-ae68-41c6e6a99b4d',
      'b25d066b-b78f-4da9-b0a6-fa5a3a25faff',
      '3aea7bff-fb03-42ed-a637-592edaa466eb',
      '5d885717-0dd0-4d19-b1eb-528151e27c9c',
      '4d0ac500-04cb-4fd8-b3ee-95d93eb9994d',
      '16a83acd-986d-4731-852f-0364fb1127a1',
      '88cc9434-3bc1-4867-b6e2-1cd4a6bba47b',
      '62b80661-f351-4d78-8f48-9039bf851e88',
      '6401f115-05d8-49f0-9818-623997033d6f',
      '50ebb98f-1526-4398-997c-165914c0c046',
      '58d83e63-4e95-444c-b103-3146466628ca',
      '8ef5925e-64aa-425c-b046-ae257ed81620',
      'ce499b61-9619-461d-a6e8-308723de468c',
      '33012f29-bba2-4d29-9e1f-fb172d4bb224',
      '8cfe9def-c306-41f1-8aed-78da8fe45813',
      '68859add-26c1-4bfd-a168-f229b1361dfa',
      'b9b8b8ff-c6fe-4b35-9967-dcb8b0e0d616',
      'e3a012cb-3140-45f1-838d-51bf94162250',
      '6fdf6407-594e-45b4-bd67-67ea3e5702c3',
      '171e276c-df67-4cc2-b0ca-4797c179a14d',
      'eaa26e06-9188-4172-9879-dcea253eff20',
      'aaf3b7fe-a9a1-455d-86d4-58fa3e957692',
      '5163be7b-52e7-4253-8e67-007888c6fbaa',
      '665bf073-0605-43b5-8957-b967d969e852',
      'fa57d9c5-edb4-4d49-ac7e-5236fb734cb7',
      'e658b055-e1ad-4dd6-af8a-5f5fb8a28d05',
      '7e6542bf-6221-40c5-acdb-ef57cc9e3870',
      '97dfa52a-9730-4296-9721-cacff4e71610',
      '781b9907-b586-4058-b439-ac18e88d682c',
      '8ef5c317-53ec-4ffd-9fda-2045dbb257fd',
      '99fbcbd5-83cc-4fcf-b8e1-08ce82874b06',
      '108b7d3d-9800-47ba-a2a0-076740b0b90b',
      'a5c44701-ba4f-4dba-821a-fb0b42fe6f09',
      '95282f8b-e684-440c-9f2c-6359aca3e130',
      'b8188e00-287d-4a25-b7b8-2033574f2baf',
      'a60b7bf6-986b-41b7-a3bf-b5cfdc06b44f',
      '24a63471-9ade-4031-8421-d8b8da6316cb',
      '34f12246-056d-4a0b-aefb-bdd645ad34a6',
      'fa91f562-0d50-40fa-860c-e8922733c72e',
      'ee62eced-c810-48e3-aa21-602aaff00b23',
      '5953cfce-9f49-43dd-b769-2bc7c05242be',
      '3026b56f-1a9b-48ab-a366-ebc072d33914',
      '5de978b0-8540-4d08-bbb4-4bab95214eec',
      'a0759d43-a303-489f-bc90-b5d9281ec430',
      '43f3b66f-c521-4b78-8cd7-a69e6fb45607',
      'c16b2e2b-b437-451a-afcc-f29a7dbf085e',
      'cd5a19ab-600e-4737-88f7-a863adf02ecb',
      'd502fe13-3e81-41ca-b6c1-91e605c0e28b',
      '1eb3e021-f79f-462a-9f2c-16ce4c30cd59',
      '058e42e9-1d37-4ae3-9485-26e27d6c6496',
      '94dba4dc-33ac-44c7-8569-92f0f1fc0fb3',
      '68d96c8a-24a7-48c4-b68c-9185a89937bb',
      'fcd77435-e6e9-4a14-ba21-8e39cad78974',
      'a2d90e45-07be-45a4-a66a-d461b6058e48',
      '4017f253-bd4f-4ef8-9578-9b86a1177078',
      'd448ca35-413c-4f6d-92c1-5d660026577b',
      '035d854a-04cf-48a7-bb45-d6fe00540cd2',
      '3ff9010e-f02a-4307-8878-1640774e0337',
      '1c56cdd6-1108-46e2-b820-6bc745885be0',
      '72f13efe-3633-4724-89a2-2f51246a2796',
      'd9f68755-7d96-4200-9623-219259d7aece',
      '5351f9ea-84d8-482e-833b-a58f83c90533',
      'f59c69da-3c3e-414a-be07-bbadcf13f1d0',
      '8b37bed1-a654-4507-9fe8-bdd7388522d3',
      '8a877257-9e6f-482b-a225-f5ccfb20569a',
      'ca632eeb-9373-4846-a8e7-9fd0e0de5a38',
      '9791d6fd-f83d-42db-95cc-76067889183e',
      '8cb3b90d-2f93-43b2-8e35-fb3e5d836437',
      'a6e556c7-9f1d-4019-ac5c-a19be762f229',
      'f392add1-ca0e-47bd-86b9-8f9cf6b25e4c',
      '31e6abfd-2fe4-4682-ad25-fa2fa476d429',
      'f8be2109-7f34-412b-943b-07cd1a3b0737',
      '18842c5a-c45e-4196-b299-6641baf57a5f',
      'f0ce9c33-b0ad-43ac-bb8a-e0327c4ec1a7',
      'f04c65c4-a369-4378-b694-7f6a4b6d47b7',
      'acfa34f1-37a9-4e9e-a9d7-4ab4cac96047',
      '079dbe32-7d87-4cb1-a1de-96e987811de7',
      'e9ed246c-aec8-4c81-88f0-3271c17dac5a',
      'a90bcf2b-caab-4faf-ab43-fe5666104d4f',
      'df41e901-9768-4ee9-b44c-49aae4633ea5',
      '3000597d-cf1b-44e4-a72c-cec955d0a569',
      '3cde1490-5a35-42f4-baed-d8670e094302',
      '3ef0a16d-ac36-4428-aa6f-d24498f2696d',
      '870b57a3-8d91-4587-8fbd-b5a6fdea585e',
      '44577cd4-9a79-4ea6-b605-3c7eff5337e9',
      '5cec9f34-3665-4492-b7a7-0852cb8ed494',
      '0b02a0b9-3cd0-4a59-81eb-30ac0a569d5d',
      '8e0e6060-94dc-4a25-97a7-f94edc94c088',
      'f2559d7c-edad-4963-ab53-458bda7e845a',
      'fa548d9d-f039-416c-8255-8ff485775ece',
      '070ed41c-52d9-43c4-8612-97f98670e344',
      '39a23073-acde-4f93-a49a-3797371220b3',
      '83f1dadf-354d-46c9-965f-f5250581e0b7',
      '71646849-874d-44c6-8d4d-117fa9aebe5f',
      '4ff07054-30e9-4232-8d38-e00112cc71a6',
      'a4702198-f80e-4c30-aac5-21fd46f8330d',
      '24163540-ff6e-4f5b-a370-e0fa449db97e',
      '24385c22-2915-4472-973a-2781fb973770',
      'e43be01a-3cd1-48b1-a7f7-5581df5e83e4',
      'ca417014-4dcc-46b1-b404-22c7330e75e8',
      '7d6ac207-3c05-456b-a9c1-496c0f216762',
      'e2346387-622f-4580-9558-aebd139cf4ac',
      '03a17c69-15df-4640-b819-fa96e7f374fa',
      '83704833-d587-4109-b504-e1431ebf4062',
      'c2985d6a-18a5-4653-baec-f092d8c51986',
      '8b6ce91a-2eed-489e-a7a9-d38639983352',
      'cd0fd27e-0fcf-41ea-bd78-ef1e000bd484',
      'beeafc98-118f-4e9d-82bf-240b55d07297',
      'd89924ea-fda4-475e-8be4-cdcaf13f22df',
      '60f0818e-123a-48aa-9734-363f3ac03975',
      '22bff2f9-1b8f-4160-8dc9-facf3c5d0692',
      '8752a7b4-b62c-4c12-8d53-509f172c2154',
      '456e2d73-4ee1-4ed9-9dc8-f25acd876748',
      '6339cbb3-3f2c-490f-964f-f1a6805ee69f',
      '753619b0-9c22-4694-a62a-b4c3ed20182b',
      '20d92dbf-5851-402e-b97a-c94be114cfd7',
      '0c262575-4f85-44af-8fdd-4b49ce44b4e2',
      '9934c0de-8a77-417a-aa69-1531fc5d8313',
      '7ce77b9d-0c54-46bf-8e0d-0a8c64936f0c',
      '7955fb42-83e3-4340-bdf7-8b15fba810a6',
      'af3f6c14-55ea-4cfb-b1bf-d8e255f26214',
      'da6b7787-b16d-4556-8446-ec962501ef6a',
      '60a5653d-d3a5-4c1e-9e72-d846fecb2f1e',
      '6eeb1c61-4391-4338-a83a-0810aa5755c2',
      '50fbb42e-540b-48e1-9d14-f36d9d1b05eb',
      '6e22ffc1-b733-484a-ba77-c62a2261c0f1',
      'd72d4d0f-7f87-4f33-be16-1c31f283c663',
      '3fe1e663-7e77-4f27-90ef-29e3e8dec05a',
      '1fd0d92a-0b31-4432-ad7e-db6ca619b2e8',
      'f87d303f-ce37-4279-9ebb-568491556bea',
      '1526f9c4-9f94-437e-8c2a-d44531e885f3',
      '33f92fd1-a7db-46d9-a3fc-fa39bcdd6681',
      'eb891658-3005-4410-ab45-a5328172b986',
      '0a544fd5-d432-4b32-85ce-0f4858966963',
      '1b478bd9-0113-4c29-b869-d827f9959c3c',
      'f055dbfd-c6c7-4d97-962c-a68bb868dad7',
      '5fb55531-c599-440d-9ecd-8b3a7eea74c4',
      'c3f9dba3-a945-43b6-8fb1-479fdf722252',
      '9e18dde7-d299-4b40-94a8-12461aa3b3e2',
      '9ac6c6c7-7cad-4c5b-9d69-7716479f8711',
      '90caa683-5050-4a6e-85f9-50dc0aec6785',
      'e1348483-76a7-417f-ba92-68a7dc9fd457',
      '3dc70baf-b303-48b9-9cd5-f2a53d8eea0e',
      '8ee8d6e9-8fbf-49ae-ac01-c9879e230d48',
      '35fdd6d4-360b-48db-a892-8c82b385f3fd',
      '0bbab137-c02c-412b-b7c0-56e82037a423',
      '0eb8452c-8b18-4df4-a37e-4c814ce593a7',
      'd0366d71-aed9-47a0-bc3a-3a6fce35cf05',
      '0d52e5e9-4f6c-4785-a6db-8feb1d0a8e06',
      '81165942-316b-4712-a258-3e72c5d9bc96',
      'a1d73455-4629-4f42-bd90-f0ff1c884e48',
      '918bb253-06d0-467b-91eb-cf839a5e7aac',
      'c51643c8-a6c4-47ab-b588-758c65093d22',
      'c28a9db6-1777-43e0-8e01-c205001ca8cc',
      '6d7d4232-aeca-4718-bfab-570f49088a1e',
      '445c4da1-6d72-4ec1-bbad-22c7c530e61f',
      '7f687cd2-26cf-4122-a363-5c91a7a4fe79',
      '1159fb12-162d-4bc9-8c77-ce35cb2f2ee9',
      '147f7209-2778-4538-990a-d05f87909be5',
      '250f084e-830f-4f18-98bd-43d2f3f7f59f',
      'd7e3dda4-d00b-41a9-a7ef-c6b46005a6b0',
      '32bbe2e7-a316-4dda-bf5f-779e7acff90a',
      '34ee9c75-7c38-42f0-a708-ba7d974cd68b',
      'b434510c-0a97-4907-aff8-5c8d095f36e8',
      'fc6ebd76-ae53-40ba-816b-d8367021a2ca',
      '1c08b2c1-7f4b-4b57-99f3-503819138e4b',
      '88e7ac70-0e8d-4a6f-ac74-b6406db8a7a9',
      '3a45c18a-a566-4515-aefa-7a05b066ebee',
      '71e26343-a33b-4c9c-9c41-fc46c10d4d42',
      '30e7f345-80af-43b9-84ef-4da23a13173d',
      'f963dab8-9f37-417a-9a55-96df7b563802',
      '18cd5dd9-6685-432f-a0f1-2df221c64ab8',
      'a9ea345c-0790-4400-ba8c-6f5d5f872583',
      '242ce527-2e75-4e2e-92f0-b1352ec1a5a6',
      'da09c257-e606-4e5a-87c0-8b48909c9320',
      'a763c2f7-c7a4-45ed-a886-110eecf920c7',
      '7607ae09-21e6-43bf-8789-8cc1d4fd1605',
      '626c2836-302e-4efd-a349-a77cc5d7e243',
      '0da49e23-d3ce-4636-b6bf-7a3d39d551f2',
      '30f17479-9411-4465-bd3b-bb069d152773',
      '2e2ee216-6d33-4cf9-bfc5-cc2045f4a81a',
      'ae80ce0c-df86-4e56-bd11-122c18eb5aa0',
      'e1ef6021-8a1e-4694-b36e-48b7be1e3bed',
      '3a051901-ec18-4f6b-9a47-65c9c7c091cd',
      '394dea8d-115c-4838-be4b-12aeae0feb7c',
      '2badabfe-9e5b-4fdc-b9b9-f77d1f64871a',
      '6adf68ef-e4b3-4f28-89ff-c90e1fc80040',
      'f7c1ab2d-4828-4e4f-9a10-475e3b62d349',
      'afbbd108-6a74-4c7f-a35d-74c2d5ac2136',
      'f60a95af-6ff2-4655-b61e-77611dfcfc84',
      '5e000abf-25b7-438b-aa36-2c84529c89ac',
      '800b29a4-1230-448b-ba68-04696f4f6394',
      '90dd119a-abfb-4191-a225-cbbcdf999b0b',
      '91b23c42-226f-4b77-afb1-656ea3f5a082',
      '5dce456c-c5e7-43a0-93c0-b2ab9dcebba6',
      'ece6f9b0-9f0e-4f3d-9d19-37b13e7a9a18',
      'c1ad11b8-9c47-4885-b3aa-416268e49606',
      '082be6b3-910c-49e6-bf57-09b50fad34dd',
      '94b69500-d07a-4441-b6d3-34c40fdb412a',
      '84b41cd4-01f3-45e3-9d09-df5cca98780b',
      '8dfc09c6-1ad5-4acd-8b0c-f9561523a94e',
      '5fbd4eb8-75b2-424c-a9f1-2ff8f9b64116',
      'f9573bca-4a2f-4256-9bcf-bf959fbb6546',
      '849c21ad-a34a-439d-980e-ff89e04ed5e4',
      '8f1532e2-61f4-401b-822f-42ad1257c5c2',
      'ef676c6e-f882-4333-a7d5-25effba8cf6d',
      '3facfdc3-830e-4f03-b58e-5c5c29f19a37',
      '43f8301b-f569-4262-bb0a-bfffd8de51be',
      '40543b4a-ad27-42c5-a8e4-12c7b1dffc8a',
      'ee98819b-f4bd-43da-a814-c8eaf139ed01',
      '8ce41aaa-d163-4fb9-b58b-4b21919100fe',
      '4d02d1dc-2618-4b2e-9de6-2451ee16252a',
      '4c9b76df-0809-4724-9123-f7fbb12ce834',
      '514c7a46-4468-4c66-aca4-fa54382010e1',
      'be26f0fd-4672-432f-9319-2a53d8da531e',
      'bfad8291-0612-4e27-bd72-c40b286c571b',
      '3c399048-e272-4a18-91f8-e01c82583d71',
      'cafb526c-3f7d-4696-883e-c72d3b0db0e8',
      '6eebb7d8-7db4-49cf-a237-f662592b14f3',
      'f9304470-ab34-4963-b1e6-6893fff01712',
      '0f0d105b-f0db-410c-b82a-a6c27e9add48',
      '25ff6227-f65f-4db8-acac-f0019ce99195',
      '966cc758-3a9d-4c81-806b-550413b0de6c',
      '4ee89184-9547-4520-a477-b8e01569e3e2',
      '6151a67a-4245-4a6b-8918-681276b92c14',
      '36265eca-06b0-489c-aa86-25532f00d082',
      '80a24730-6d97-4bd6-a2ba-c6f29d7a5773',
      '5f9e7394-1742-4232-b1e2-8275d407d92d',
      '02f259d3-dba2-4be8-8e9a-93d323c88096',
      '1c9c694a-8539-49e0-8681-0c2033910d07',
      '0860263b-2db5-4bac-b92c-94fc4682f9d0',
      '900c58b5-cbf3-4b79-ac64-969cd3b9e798',
      '7b9a3b42-967a-4eec-812e-7a43e5ee1910',
      'ca0565d1-772f-444f-b2e4-52979213f899',
      '9f4dab95-6fd5-43e6-a8cc-c9cb4f983184',
      '62778de0-2d75-47f1-b509-b25fbf9d8e1a',
      'c111e02e-e360-4b4d-8157-b85f3acbe12a',
      'f7df0c51-02d5-42e3-8fe0-0536e3702ec8',
      '99b2d762-1520-4203-b213-d38ccf2b772b',
      '874780af-ecf5-4af5-ba0f-105bbf771c8e',
      '8ba0a57d-baa5-4aca-b358-4e3b3ac4232b',
      '84fffd20-4ffa-44f3-9e88-57b84d445642',
      '4e5d8591-9156-48f3-9250-dfa1344a6fdf',
      '8e4088a9-eaf0-498f-8806-5a48c53b8011',
      '24a5d403-430e-497a-9ab3-25df2e7481b7',
      '2a3e78a6-5fbe-413d-acd9-d2d33336b41b',
      '502db40d-28aa-42d3-ba6f-a3ce3b3775b7',
      '1ba0e412-746d-4ed2-90e6-3b8f30767c95',
      'df908bc8-5d48-4a79-a0e0-edaab908eb05',
      'deb80142-ebf9-40b2-ba9b-108ad4ba6aef',
      '6170dd72-63f4-40ac-b676-1c91e944ab96',
      'ef9c54aa-6ca5-492a-9b90-84b1308dbedf',
      'aee2cea1-adab-4c5a-aae8-c8eb47ebc3a1',
      'a738095d-d3a6-4d09-8d77-623ffc1b633c',
      '86983d2a-8b58-485d-b5a2-ee050cbf6d84',
      'b4792c8c-e268-4879-86d7-ad19f019bd27',
      '60381cdc-fcf2-44fd-a1d6-b8973d471fb9',
      '81e6b11d-b11c-4290-8df5-a0e6d77631c1',
      '7c782654-25f3-4864-ae2a-e3d99aa76212',
      '8b4a12b9-c8f6-437c-bfa2-92df78939b3b',
      '3fe924d0-d730-44db-92c8-8d1097ed9514',
      '1db60d81-5035-4a3b-aced-5ca00ada36d8',
      '5b440f08-44df-4078-9765-a2247f0bc0cf',
      '023a9de3-6557-48f5-90ae-9c87182444db',
      'e6613ca6-7cc9-4ae6-b72b-97d9c39aaa7b',
      '9b4bfc37-3b0b-4bc4-b40c-d0d5c0d747b5',
      'd08f09e0-ea78-426a-8a30-c40d56015c32',
      '3879ae91-7a03-4fcf-96e6-076a57b23e5c',
      'c0f4de1f-f89a-4cdd-94c6-876ad18896a9',
      'c6049d14-a5f8-4248-b786-44cd084263bd',
      'd6864340-132c-4826-b077-5256befb2ca8',
      '19997193-fb6c-40fc-ade9-80dfd3425fba',
      '9ec7d6fe-2b11-48bc-83a8-05a351d79dee',
      'eae31d10-eb6b-49ea-8da9-4bf4ea3c4de0',
      '355ae44f-9c89-4dc4-a90b-1f2201be1f83',
      '29c7c4f3-812b-4a76-ac6e-3508cb61a6ab',
      '67c6db2a-4c51-4c9b-afb4-2c0f3c4f5b72',
      'e50ad31e-c3d3-420a-88a6-d390be236e1a',
      '4ca95e82-ade8-493a-811b-f404e3f15bb7',
      '54f85237-2f97-4f95-9b78-8e66e562aec7',
      '2d2c3bfd-ab19-4d4c-924d-7a6041a58ed3',
      '5f75d7b2-06c1-4917-ad96-737e8810d222',
      'cfd754b4-ebbb-4feb-9969-581a7e1b654f',
      '80053ae6-cbd9-47e8-b2dd-436ff9b296b1',
      '7efeba5f-d756-44ca-bcc2-d354004f17df',
      '97d330ee-f3f0-425a-950b-e259cd86be5f',
      '6ff73d4e-a151-473d-9724-dd5d13306c1a',
      '2bb99e73-8454-4a60-a47b-c2f53df09355',
      '01b1c86f-e86e-4649-8c9f-fb1866c79632',
      '1681d4da-2fb2-4e5e-94a9-363b1fa3c635',
      '2921a794-35e9-4522-a8a0-6d0388007b26',
      '1deed707-5034-4251-9163-2695fa5843ff',
      '8adbe553-3632-439f-8fb2-d36c8881168b',
      '45743860-be41-4fbe-bc01-2a2da74183a1',
      'e420ab5e-eb1c-4090-b0d3-392c6f9ea23f',
      'a51cd476-3f9e-4a04-be69-30cf99a22b49',
      '4284e975-4208-40ba-8172-4b364db169ab',
      '794902d4-ffac-49e2-a936-e8b8292d5bd4',
      '6bf66b9a-91a2-47d3-93cf-167beb90f914',
      '407cc514-f87f-45f6-8b91-f8b904f973b0',
      'd0b3fb07-6535-4e86-a176-54836fb78631',
      '1cbeb0ce-0819-4306-929a-674eb9437ebc',
      '258a85d8-5abb-4f6c-9f7e-9a62a3667784',
      'b8c86317-2391-4caa-9e71-f20177498b34',
      'dcc37228-3a6a-4e3b-bfa1-e52bc6dd8524',
      'e0069630-bb29-41c7-860c-f29bc862d0f5',
      '30212b9c-063b-4c3c-923c-6e5640e7870f',
      '3e98e599-eb84-4169-b8f3-29d61a84deb8',
      '0f5ba58b-563b-41b2-80ea-8931e7279189',
      'f6d8ef55-6fea-402b-834f-d58bc7d17364',
      '7ad7e17f-9f77-48a4-bdd7-f8855ea5a950',
      '1483cd85-a11f-4c68-9bc2-8240b19ef897',
      '2ba1de16-d0d4-41a5-97c0-476951451f46',
      'a9459b95-06e1-4a46-8e19-3d0b2d3a371d',
      'a7940189-7d36-4b79-910c-312a5288be4e',
      '2ff63874-9874-4000-bdb5-69f23cb97dd5',
      '8bb2a4a4-3390-4e77-ae88-7d0d78901a19',
      '0176991d-6b8e-4375-a602-d44da930371e',
      'a99846b5-322a-423e-a300-b4e8260a546a',
      'b04b78c0-ca47-4445-b0be-b8d477c9ab1f',
      'ed99b9ae-457c-4c7a-8fd2-75fd4c061c20',
      '4bb05e09-8f69-44cc-9aec-d09ccf8dcfdb',
      'a91c0a4c-ec6d-4bcf-b437-6ff292e770cb',
      '05d31c01-3b93-44df-ad5f-ff0124ab1424',
      '44ba4ff8-7016-4954-941d-37f01e698340',
      'dbe0fd5d-6159-45f8-bdf4-b3b11ba83bb0',
      '4aa8a4f0-0e42-4139-b5c4-9b46af9eda6a',
      '14ca03bf-c2c6-416f-9973-7bfacc1526e2',
      '094b3b31-0b1b-45fb-ad0a-51dbe84d7e29',
      '2a86a1a2-e229-42f7-b7b2-efe90df0ec48',
      '246bdbc9-5c5c-4f62-bf79-932ce7758b7a',
      'bbcd74a1-c8c9-4562-a610-fa837f4e8831',
      'a7cb02b2-a3f8-4f14-b640-7a70aaa8aa56',
      '8d18307d-a6ef-46a2-a05c-46fba281149a',
      '86f7af8a-425b-488d-8241-6ab9041af6c6',
      'be902585-c158-40c4-806d-ed702ac28107',
      'a3f618c5-7a4e-4feb-a669-ef6cb3afe677',
      '069791c7-b9ac-4b2e-90d9-a33e8296c1ab',
      '713b020b-64aa-4d04-8e0a-02afdb0b54e7',
      '61ce2317-e79d-4c82-b6ad-31f36dae52fa',
      'd81dd41e-25eb-418a-b1ca-85508a2deda3',
      '145f9d8e-1799-46bb-bcfb-14dba1c96301',
      '95b9cd5f-3e5a-4915-8153-04f92fa55854',
      '0d44ae57-3142-47da-8622-04a5e0894315',
      'b807dedd-90fb-4d95-a6ad-b399174a2563',
      '51e9a898-04c1-4641-866d-d9c5d0a826c2',
      'cf89f476-ec18-4809-9c50-3ec57de627eb',
      '4faca900-3743-431d-97f5-d603d81feec3',
      'c9737f4a-46b6-4ee9-84b3-eb62add177a6',
      '80bf006b-e190-4c40-9c60-44fe09cf259d',
      '724a7941-bdf4-4511-9f11-0d0bc722c086',
      '1b949878-69e6-439f-8762-c0d9af36065c',
      '3a1c5c0d-738c-44b1-8f9a-73b2fcb579f6',
      'c2077e60-7134-4ea3-b6fc-aa297e5b49ed',
      'ff088743-4504-4588-a73b-cda2f22f3afd',
      '10334c95-ff0d-4aa7-a626-d931c78090f5',
      'b42a5e56-c236-4b33-8cf0-cd772d466e0d',
      '6e81d5ef-7351-4cb8-8990-0ae4c129669a',
      '8f91c54b-4110-46dd-8d40-9d81b1b0297c',
      '0bfcba17-5b7a-4097-ac93-cdf04a199f13',
      '0e2a4ff1-8e22-4945-bd14-390fc910a6e1',
      '21d57d6a-bb0d-44a2-9b0a-d76e6695f563',
      'ea773f60-6ff5-4903-8527-aea3c438dfb6',
      '2338cb4c-4b59-4670-a42d-d6420a7a5117',
      'e2597884-ae6c-412d-bf26-52d4721e6f32',
      '487139d4-16f6-48a2-83d3-7d1acf245ea3',
      '4c164863-524c-4f1e-8570-338ae0f01ca9',
      '4d98499d-b2bf-4212-8310-a524c6a86709',
      '01ee64d1-b242-4476-9f9a-5b3527273e4c',
      'c8318a6c-5706-4343-b371-9cd5e3404325',
      'b989c4f0-8942-4086-9437-c9d71dddbb4f',
      '050548cb-8deb-4ffe-bf94-05c6f9543454',
      'b9937c75-4df1-45e3-8944-790103ba7e98',
      '882b1631-8b6a-4049-8302-6250cd510436',
      'c4b7dd0a-90f7-4c60-b173-90b78f9254c5',
      'ce8a2410-60f8-4c92-be7a-8cd061139dc9',
      '42b063f4-b92f-4009-bd9e-b526f1a0dc74',
      '21e5cde4-f574-4065-b8e1-c0a8124eba8e',
      'c73c42bb-ed0e-4822-9774-116257498adc',
      'adc8be46-645d-4d81-8608-bbb4231d89e0',
      'b99e5aa6-3e06-42ef-9bbc-53534caf8c9d',
      '92c88bc7-51f7-464b-8fb3-b0d060e4fbf4',
      '36262324-e9e2-4abd-a144-cb0e6c6343d2',
      '43dbdf3f-63ba-4d16-af24-ef0a588ce3dd',
      'f112086a-c0be-4a71-80c4-d900fd609117',
      '1d68ca71-9efc-4ff3-a116-3a9326f38f7c',
      '02f080de-1b83-4099-bf20-d9bb6a5fe55a',
      '122c16bc-645f-4d20-bd17-9f076cbbb36f',
      'd54619d3-f7be-414e-a746-34ed0667504b',
      'a4e94982-db9f-4b7f-8d9d-2d17f011d933',
      'ecacf626-e634-4233-b07d-c1e744fe97af',
      'a8284a1a-b3be-4cc5-8621-e47c933cb2ef',
      '13866a13-b3fd-4d30-93fd-e471651fbfd7',
      '568ec64e-56df-4399-be8b-05f798a39e99',
      '71a2dd51-0b0f-487f-9856-0bb6c8afeb77',
      'dfe3fef4-f43b-43f4-96fd-c7a8033c0667',
      'aa02b66f-2786-40bd-aa21-c2a39fea7383',
      '41a16053-d5f0-4fc8-83d8-6010eec22ee5',
      '1f5ad30f-e074-4362-8567-8bd57dd5da71',
      'ac233f84-3216-4827-a88b-438d8f241ff4',
      '1512035e-57dd-450b-96cd-742b04b0d0b6',
      '775244d8-92f8-4d1d-9a33-1678d130563c',
      'cdb28652-88ba-431d-8d5f-ed0e08693a8d',
      'ba06f2c6-04b2-45f7-99f4-20391eea4baf',
      '5a55fce5-ea4e-4f1a-849f-c7e2819d274e',
      '99ce7eb4-daa7-4cb4-ab37-a6d154b6edc5',
      '262ab784-88e7-49cc-bd16-9a9f5d7627ba',
      'af7bc5fb-2436-4cca-bfe7-463606387423',
      'ce7c4234-d326-487d-ac17-caad244c7ab1',
      '8cce9b64-1a67-41a7-a4d7-46c6eadc9e0e',
      '57bd914c-9938-4c8f-a6de-0c7305052ab3',
      '7dfba4c2-5ec6-486a-a54b-59009c9cb095',
      'b956e17d-6d44-477b-89ab-f383c62194a5',
      '5d6c9270-370a-4c26-b12c-c92a1fba7006',
      '7be80424-632d-4476-99ff-91a123f68123',
      '1530c2c2-401d-41df-8389-1e63808b1678',
      '4d957b9b-65b8-46ce-8cb7-ed1255adba87',
      '657d63fe-b1c7-423b-bf3a-d508d4ea16ac',
      '9d81834b-26bd-4dab-89fa-e9b16206a48c',
      'a0cea935-af99-4ed2-a31a-efa369bb1561',
      'b16b2521-783e-4933-8e31-f4d1e2b5fb61',
      'c6f26dcb-f67d-49b7-8d7d-d47d8a4cb3f9',
      '23ddf5e9-6967-450d-8818-450fc70933b2',
      '4a26ae82-99c0-4edf-aaee-651aed57d0eb',
      '74308223-07a3-4a90-a549-e494e4144b41',
      '94fe33d3-605f-406e-9c5c-ce930714d9f0',
      '32b182e3-74a4-4152-89be-307803275dec',
      '40e059f8-5f4a-4e62-ac87-93fab0857866',
      '0f25b2c6-f41d-4f28-95dd-bc4a45e0cb0d',
      'ac77517c-54ac-498e-9c59-0837747facee',
      '69b066d9-e541-4b01-9073-0464305edc5e',
      'd4269242-d50a-4221-b93a-a3d471b332aa',
      'd299dc19-995b-4711-a51e-60feff782d8e',
      '55b4789b-5e62-4810-b130-b225db39adea',
      '545dba90-0f50-46c8-94e0-c358bbb10141',
      'a36e8009-3ae7-4a05-8df3-269ac9eabe6d',
      '0cc4b3c7-c5e9-465f-97de-95bb5ee2302b',
      '5b6e4ad6-40de-4095-a8bd-ea0db7e59b1b',
      '58d7b103-d5c3-45ec-8454-7e6424cf863f',
      'ff52cbe3-4d30-49ae-bff0-afc9967e5b1c',
      '2a344505-aacd-4a9e-8be9-68f651b96a7d',
      'c9bab022-159c-44ef-8b6c-09690bc57513',
      '8dc1ab66-4f63-47d7-83aa-b5b98f6024a3',
      '4503785e-afbd-4adf-9422-8f3b9930a6e0',
      'dfe9811f-d68a-4c92-b25b-2417a09a3640',
      '1b3f478a-a074-4421-9384-52ae8b93b54b',
      '5042f673-8b65-4634-8fd2-084da2dd0515',
      'a468b533-d59e-4ef8-9743-ea84f188ab11',
      'd6220859-dee3-4733-9deb-4ceab5eb3ab5',
      'f869c168-c7a9-4736-93fd-ff007a8b3eb3',
      '541adb10-5305-4c4b-a44b-152bc945ba1b',
      'c9b9f824-5c29-431a-b9ea-ba08c7b7cdbd',
      '089de033-2d87-4ab3-b66c-c4f2d3067652',
      'd82869c0-685f-409f-bad6-eaaab9500f20',
      '1fe5e3cb-9dcb-4cb6-a993-d77dedbc28f8',
      'e42c061f-68fd-45d8-ac46-911aa2b21e79',
      '8271e1bc-e26c-4629-9356-a44983dd3a45',
      '2607e822-5489-4cd1-838e-0e4c8f5221a8',
      '15efa740-400f-4d92-b6ed-11c55a95caaf',
      'd68364a7-8984-41fa-bd30-f3fcc1ff1c04',
      '8574c653-1e07-4584-9bae-4395c0c47beb',
      'c68fc95e-d319-483d-8263-eebf5b1127e5',
      'f8059ef8-4ec4-408b-8366-589e567b080b',
      'fbdedd85-f33c-48a6-910b-db28999e7449',
      'bdad9592-5e26-4607-b2a3-608443470936',
      '24afbc73-1033-455e-b506-766fc4d420fc',
      '7b763548-d3f7-4f0d-b384-5f1608ee4efc',
      '15d79fb9-052b-4a79-a99b-fc89e8e94f9e',
      '99af79ea-1c93-4f61-8898-fa68e2ba2f5e',
      '96f87bd4-92ac-4317-a3c3-56d1360b732c',
      'fbd3d895-ea27-40ca-beb2-4933b3c42ee1',
      'e8fa9528-ad30-4402-a690-df934d022dbb',
      'b792af79-fdf1-4987-a2fe-219306863f3e',
      '55c75cae-11af-4eb2-b14f-9ee42b9732d6',
      '3628c3d4-33b5-4c60-8434-e64a41ad8c86',
      '43ca884d-a942-4c0f-b2f1-355e3546846c',
      '3fa41326-64ba-42fd-a657-48a25b6ef6ec',
      'ce1e2e5a-c65c-442c-8ee9-de7c0089ff2b',
      '54577c08-fd61-4fe4-8651-56481fce3a66',
      'e24e910f-509e-453b-8776-31d8af319a97',
      'af9f22c4-ef23-42ba-850a-08997c86690c',
      '807becf7-2cde-41d3-8833-ae5c42b14d27',
      'e1026cb4-4f17-41d4-aa5c-bd43c0d4a3b1',
      '4a92493a-5618-4064-bf3e-490c70d20f3d',
      'cb755050-4f21-4623-8db1-3d55e6e4031b',
      'efbd0e88-1618-4b90-8978-72827b9bcaff',
      'e5a2addd-1b95-40e3-931b-86561c3d02a8',
      '5807da8b-cf54-43fe-846a-9111c61b14e3',
      '41e3e9fb-f5aa-46ff-82e4-2c855c20cda3',
      '5d789aac-c059-4608-bd82-b04bd2df46e7',
      '9990091f-d3d2-4503-a4c8-c5aa05d5b705',
      '26115392-ab05-4f95-ba3e-dd153a8d82db',
      '3225672a-9549-4a09-af22-e57f04caaf25',
      '4b5d3d46-37f5-4186-901f-b00e64380c99',
      '660e93b9-7cf4-49f0-af53-d2ff9f1aca4d',
      'db144ed8-1861-4db4-aefe-8e247ba96b36',
      '1b703dd7-b03d-4d8e-b5e9-e7336c48014e',
      '80821b75-9e2d-4a3f-9001-73c5bbcfa15f',
      '1adc5b9a-f1bb-46ac-bfb2-745b8ae89020',
      '370dc44a-8da8-43be-a267-51592e5a6195',
      'a13bd7df-68c7-4b49-9669-20af51f3ffc7',
      '1d086a63-0868-46a8-9b21-7c2aaac3e177',
      '7ee557aa-c0d0-44f9-b0da-cfee0b4ce56e',
      'cfe2c8e7-9160-4dfa-9e50-b5f173e4a329',
      'd261a774-496a-40df-8963-54d7fa750eea',
      '70a2c693-383a-495c-a933-64f7090804d0',
      '88b31d6d-f1cb-4647-8dfa-8e53f91fcf9b',
      '0772841d-694e-4e88-9c75-b93a491e67a3',
      '10438d79-8ae7-4541-a02a-10c3884b7b8e',
      '7d31b8bd-9028-43eb-b586-5470e0abf68f',
      '28c49d91-b9c9-4b91-a301-17de7cfed7e4',
      'fa2efe3f-76a5-4efa-b955-11589ec9d25c',
      '414252f7-28a7-41e5-a11f-2bd9d89be682',
      '67a3e830-bf07-407e-b19b-907780fdc0da',
      'd30f9901-b817-4291-8159-27057235a747',
      'ddea3448-5294-4772-b185-b09859c1fdbd',
      'd0d0b797-00c5-4853-8b22-d09f63971690',
      '1afc6c19-0b94-4de1-abf4-e946c881a997',
      'ac748831-0170-4fa4-a41d-fdcc5b361910',
      'da2950c1-62ca-441a-a15d-354cf32b7e22',
      '90725579-44f3-4963-a823-d4bf6c24072a',
      '0902ffe9-eff4-4ea1-92a9-b4fa62b1a7bb',
      '4f5f8f00-cebe-4413-9737-f9db5ffea609',
      'ade35fcd-b0bb-4df6-b37f-1f01cd43d60b',
      'eac5d197-42f9-4104-8efb-6be1d21ba611',
      '2310b407-6428-4573-b03c-11b35b72c6a4',
      '2765c3f6-2705-46b3-a65c-d61dfb200a6e',
      '9083320e-1284-42aa-9bd5-ba4794707f07',
      '5b7d1ef9-5263-4c65-871f-0221d0013d21',
      'be6e03e7-8cee-455b-9689-11213d9fbf6c',
      'addbe8c6-5185-42a8-8958-51a948eeda2b',
      '4308e83b-120b-4251-b702-e3f98583d84b',
      '2330a274-9c0e-4e35-9ef5-e04b89e4af07',
      '47899078-9e30-426e-8081-21ba8fa47284',
      '3007d742-bf2d-4f12-9be3-7a201e2780d7',
      '59d2c6bb-84de-4111-98e5-e0c2850f967a',
      '2bc5112f-1e2d-4d63-902d-37a38149ec86',
      'cffe49f5-901b-4875-aece-3cc23e890fec',
      'c6220d78-8d92-4529-b9d6-514dd26c5df3',
      'f8f5ea9d-f0fb-429a-83c9-c705685ce41b',
      '01f9d754-b42b-4920-8f5e-615a78346f72',
      '202df7f8-ce9c-4c6b-8538-5be1c776e75c',
      '74146524-a767-464d-8306-ee445b597ce9',
      'b07b2b86-17d8-4730-9677-99f9099b29da',
      'ab161c21-bc0a-4c72-b9f3-66f17422dda7',
      '2ecb21e6-625f-4284-b52e-9c99f0f38921',
      'c0ad0730-f210-4dea-838d-0c88ef9c82cf',
      '79fd471c-3559-4a69-aeaf-bff632c292e2',
      'c20ba555-7c58-45ef-b32a-396cd44a3d86',
      'be8bb1c0-3654-4a85-8949-1d355f31403a',
      '934c8666-ff3f-4fc3-b668-6891fdd0e488',
      '29fde1b3-ca33-4ce7-895f-ff930c320bb4',
      'b8f93943-e0e1-4eef-abec-e5fa9524b722',
      '40605466-44bd-48f7-974a-b8f0bfae0779',
      '10f3acd9-1b82-4bdc-80f0-f4f8111fe17a',
      'd2285cb6-b8fd-49bb-8fe8-9c83c47688e6',
      'b8bdf825-2011-4b52-b1c2-6b1fe0799f18',
      'bab56823-e488-43a1-8f4b-9912ec1e28c7',
      'b1aca655-3ea4-4a75-b97b-d6c5f8236b31',
      '93f209ac-c9be-4f75-9cd2-9dc6bfa945ae',
      'c3f9de04-e339-4632-ab2c-cbeb364db786',
      '6ab319db-7780-4e66-9384-d8fcc2b8ce69',
      'ceb5e3a0-a44c-4261-99d2-8db492abae27',
      'bcc6fd23-6477-40c6-a237-250887e2f167',
      'fc67fcbb-1c30-4716-ad33-144db5559e4c',
      'e7cbaa03-696b-4425-b53d-b1b9bb0d88dd',
      '656b62ab-dceb-4c8e-8d89-60b4d225b4e3',
      '0901c3cb-9f26-4f52-a1ab-26c71926b91f',
      '4aa8f4ba-69d1-40b8-b16c-2076a2892a22',
      'd33e6e70-2964-4295-b15a-3716d28d9632',
      '36e16f17-f36a-4342-8f85-fe40af7d4fcf',
      'f9d9a35e-78b3-42c8-87f6-64be4282a2fa',
      'befb3e47-77b0-4fd7-9347-68a1991759e8',
      'f75d7a12-cbad-41fa-9692-60caffeea8ec',
      '4182e69b-966b-451e-959e-15762f4468de',
      'c2d3d994-a736-41dc-a771-7719747a55d9',
      '38d7dc90-06ec-4c41-926b-5f7161b850fb',
      '005960bb-77d3-4d14-8b2a-1a60a0d233ee',
      '54ef33e0-b646-4796-a7c2-b07a19a5b68a',
      '006dc0bf-3913-40be-a592-194a84bbbc3c',
      'a8b2ebc4-cda8-46fd-a8c0-318e1009b1af',
      '5de701f3-2a61-4a41-b6eb-f975345f8de2',
      '16a82357-0150-4dc4-a6c5-ddc93e811c54',
      '2a68b122-2545-4804-bac9-5a10e07b7693',
      'fb008f40-ba5b-40a8-b1af-3499bf1a2e1a',
      '02fc1b11-a25d-4f25-837b-2f78133b1756',
      'b8fecddc-f668-4ee9-93ef-08735f056190',
      '1cc7158a-648e-4af8-a634-12d9e195e225',
      'd10001d4-9941-4100-a889-eafb3d01dc52',
      '91e247fa-d5ee-41a9-ad64-9afd780e121f',
      'f883ddf5-34ff-4eac-b1d7-e64fc9b8f6b3',
      'e607ecda-1265-429e-aabc-fb1e84c2b12c',
      '741b5312-304a-4701-b2fc-a0dc4189f4bb',
      'b91cf981-c7ff-4094-ac24-97c31b55bdd7',
      '794c2e1d-81af-4b0c-b7a8-005ecc43a0d4',
      '0b6aead6-e243-4115-86b0-f4395034d319',
      '5d6ef8b4-c9b3-496e-a1e0-af75db2cfb18',
      '26ff8886-372f-40cf-bf83-894e8c70135f',
      'f9d70b50-b793-4d66-9468-c6b1225a03a5',
      '46f85dd0-2c6d-482a-9c79-325396d30ac2',
      '6589a4da-0e70-42bb-8482-74c0de93eda0',
      '084c8c00-d1bc-401d-bcd3-cb6bb9b49cd7',
      '6605ce94-8e0e-4e03-96a9-403cf158fad1',
      'ae45c419-4857-4b19-94bf-ee72cfb32469',
      '93346dfe-2f18-4908-95bf-b5879123f543',
      'c6589289-f132-4a59-a127-a4b9582ca920',
      '7df1cdee-070a-443a-8896-3370cd797b3c',
      '6352fe0b-6e40-4d00-895a-4ea2fc3af464',
      'd52c66ed-d634-44b2-b4e9-3dd78c988cbf',
      'f35a454a-bd24-4d9c-b060-7be19d732998',
      'c2223b2f-ee3c-427d-a3d0-a2ffee8aa94a',
      'e0014b48-03ec-4426-b473-b0fe1087701c',
      'c6eabee2-2b49-4737-90b2-330ce1eb43bd',
      '7d8ef791-4c19-4533-befa-9cafc6fccb9e',
      '15213f9f-b823-4373-b940-2380e1b1f5df',
      'f22787d0-3476-46c5-9a95-d6689e94516b',
      '911cab75-32c6-43bd-a31c-5b637ae9debe',
      'cf1bdb06-b5ea-4100-90e6-a868f683031b',
      '5b819d46-1cd3-4838-bbe8-70de14138dbe',
      'b1447bc6-c0cf-47c8-8f20-b9fc0efcdabe',
      '49d808ae-3da7-431f-8056-dbee46055b98',
      '95538898-4024-487d-814c-ae79be44a709',
      'b471a310-9afe-4073-b57d-3fab72d0fbc1',
      'bbddda3e-b95d-4d81-aaf2-3da2b0c5e41f',
      '8b055de4-8428-4963-a824-9a46ed38a7ae',
      '035e64f0-0dd0-4ec4-b254-f2e3a8da8a14',
      '823351b2-898e-4fc5-9a9a-a5ffb25a54ae',
      'a771fe16-c50b-4d64-8069-4fa8a762c3b5',
      '9d9a8cc6-7d85-49d7-a159-a667d2cf22b5',
      'fe03e19f-4d43-4b86-80f1-a3b956ce5ddc',
      '41115d72-2e0c-4af7-a299-10048d20ca95',
      '763cd4df-f471-42c9-b721-251621c66de3',
      '96f4d132-2371-451f-9d2e-0989bb843d46',
      '72c2d900-7a34-49a5-9067-59d13a8cb0a7',
      '078ae408-e9af-46f0-b1b1-6cd919246bba',
      '44ad708b-25eb-4b8a-90a1-163f1465efe9',
      '05e83d51-30f0-4fd0-942c-347e18bbf384',
      '1fb0b7e7-b247-48b7-a952-dad5b8f66f78',
      'ab93d622-e3bd-4a8a-9d0d-465aeb2826cf',
      '17b3b152-c6f1-4466-8851-91c5bcc3ccdf',
      '63b26ffe-f3a9-4dc7-b136-da04d956e558',
      '448aca99-cef9-458f-a566-324a1b9da037',
      '5117c1c2-6b45-43f4-8895-e1ce86c59252',
      'eb259ddd-d3fa-4e4c-aa0a-aef4aab91dc1',
      '76dc0442-a24d-4471-89e5-68fd682c04d0',
      '2141da4d-92cc-4c69-b0e6-72516ae4ce9d',
      '33765449-9faf-4c43-a358-5be555f5eec5',
      '7e3d4000-4261-4472-9044-c416052476c3',
      '0ff64131-38e5-4203-b694-6e15e4ca078a',
      'de9509f4-1398-47ab-96fe-79c39f81cc45',
      '04f729dc-69c7-4834-bf3f-656039109e70',
      '00719647-696d-4660-ba31-61cfacd0983c',
      '49981a1b-8cfb-46f4-b97f-ae93f34fb6ac',
      '1bd1a701-df90-49c0-bc43-549eaa75e72f',
      'b66a6929-39fc-44a7-8a56-8311e3b6e189',
      'c7b106fe-8a2b-464a-9679-4bb6c548d41c',
      'fc5705c1-25af-49c3-b9f2-2014035b4e3b',
      'bbc81f66-6c81-429f-9650-23f923148f8f',
      'b791759b-4f3a-44ed-9aae-c6e0d6c5d3c9',
      '104fee62-3db6-43d9-a511-8bb4656eb077',
      '8884d3ef-dbd3-4ecb-8f54-c70092bb7d68',
      'e8653a51-ec99-4256-a0a2-035f10fd7f18',
      '7bbdafed-8706-4e67-9910-e3d02abf7c7c',
      'f7c04331-83a8-4cae-a92a-336159d45832',
      'e7f79a99-5b0c-4c10-bb68-c2d42948b5ce',
      'd351de8e-7f3f-45f6-a785-b6f6e26abc02',
      'c7f91d82-d055-47f4-b118-01847a6c3bfd',
      '5f54eed7-921f-4ed3-bff8-985db0502028',
      '047088f2-5f28-43c3-9a72-da7d4b147322',
      '6c1497d5-af12-4ae6-a344-09c120874515',
      '0127e25f-e8b2-4d56-b0e0-5248d7e947ce',
      '1e0c301a-0c2f-4520-9307-6a0ac73a94c4',
      '34d80938-fa1e-4dc1-a2c1-549ab839b2ff',
      'e0ac6bcc-ec2c-4fc8-8e91-793b3ae292b7',
      '6051c652-613f-4cbe-a1f4-c26f7d0386d3',
      '03570469-4310-4d3f-b441-a50f71d9ae05',
      '5a6e039e-f380-4c0e-af62-aff76c153a1d',
      '3fa3184e-4ba7-464f-88bc-7491fd1a4da4',
      '0203c9c7-d8d1-4085-8111-edc735983d2f',
      'cf5e37cd-90f3-4280-9b44-7453eb353b80',
      '37072496-f116-4a6d-97fc-327d650007e3',
      '5a643d3f-9485-4906-a4b2-91cea9c994d6',
      'bd43dd89-36a6-4a72-be13-4e601a0ef4b4',
      'b098587c-64f4-4934-b08c-4b523ed807a1',
      'd0173a9d-c92d-4550-86bb-e6feee884056',
      'f8f30ac4-91a0-415e-9422-b934675cf1dd',
      '1ce6ecbe-a7ef-450b-86ac-987619a97fe0',
      'c89961f1-3504-420d-a59d-bb4b94bc21dc',
      'e51e8e34-ba7a-4fed-addd-5af89d0efb81',
      '6259d1d0-1590-4767-b3f3-6fd96ea4c5b8',
      'f1064da8-3324-4e02-be4e-7abcb019796f',
      'b6405030-daa3-4f3a-97da-4942049abe5f',
      '1cf3271c-0a03-44d9-864a-c03888bd003c',
      '23cedc30-f263-4afc-91f3-a7e3fc9f4b60',
      '32d5c97c-7582-4d27-9ad8-a4184a3a43c3',
      'af9af6f7-41fe-40b0-b10d-00cfc42d236d',
      '56487f95-5da0-4a26-800b-ccabe3bfe266',
      '680d2433-1509-49d7-8c77-2fd7ef8e3f66',
      'f8333717-34ba-4a3f-a135-41ef7c209b08',
      '1c1bd95d-58cb-4a00-990c-acd27082d61a',
      'c85d607c-f9ba-4f5e-8c0f-b1fee765b9c0',
      '1b8dee19-3559-407f-8c9c-f0baca1788aa',
      '7a9bd30e-e679-49eb-8f92-2d0fc2a4ca16',
      '2fdeafa4-22c3-47e0-b322-bcaf126309a0',
      '53094d1e-cd99-4539-8ca8-f159e06bb089',
      '6392e231-7c93-4c54-8a16-e638e266a5b1',
      '9cd9502d-ce7d-4b07-902f-a03945251544',
      '4a449842-8b5a-489e-b7d0-1464fe2eb6d9',
      '164c9a05-35af-4852-8372-2bc27afbd75a',
      '7be81665-c90c-43e2-b766-9c1cbababac0',
      '329b031c-9ded-4c45-b1a4-d8d83a2d94cf',
      'c7a17073-6f18-47a6-aa97-47720546612b',
      '603db171-24e8-469d-b0f4-745319f4cd26',
      '8fe9d133-c344-4693-965f-b229510f38a9',
      'c9453e17-60f3-4bcb-bc8a-73b8a0200b89',
      '349e96f2-67da-4a6c-bc77-57fe22586ab4',
      'd8ed3428-3b7b-4480-bf38-d06f62f48a51',
      '12237360-1f08-4e6a-a59b-fa82e7393562',
      '5e3448d7-c823-4056-bee4-6b971d0026ce',
      '0522910d-4eba-474a-a4bf-9c31c42a6101',
      '65d8dc9a-ba43-4d9d-a00b-4decbc22bf1c',
      '88a0c5a0-fc36-468d-b65d-52a9b308d89e',
      '8e2715d4-e40f-47df-8ffe-f43ab3e89d6e',
      '31ba9876-04cc-43a5-8b80-7f31a5953101',
      '41fc6791-0126-484a-9b42-561d41f7df51',
      'f79610b3-5952-4406-a04e-c82150c47aa5',
      '19f46ed0-151d-4f06-9ab4-c452f6a6ac9e',
      'cf914693-aec6-4800-9631-8efd84ef5885',
      'b44e5ebb-d414-4f09-9827-65eed927c682',
      '0487c4f8-4d7f-458c-a561-592324d2831c',
      'b9e79b54-0023-4261-a1b2-5e7cf95ede47',
      'ad93b1f2-d623-41fb-8e64-4f7ab8eda7c4',
      'f17baaf5-348a-4f8e-bd87-847cf87afa12',
      'ce4ee958-bde2-46c1-9ad9-567ca0baec6f',
      'eb2f288f-097f-486f-89c5-896f55ac72f8',
      'f47c94a3-ccc6-4394-9f00-799ba026ea6f',
      'b21a8d26-c327-4318-8be7-d4cdf7d8962a',
      '1a90fe26-bc7a-496c-a80b-09a6f9c56340',
      'c467a924-836e-406a-884c-322a7d1a6cf1',
      '7c71a775-15c0-448d-b360-7ff11e39b9b4',
      '954c33dc-cb7e-4d94-b038-b696c7c0265a',
      'f60c64e6-2e61-4595-ae36-e7237c0ab82a',
      '5d1c67e8-5f95-4086-988a-547777fe5d45',
      '2bac2982-0e69-4fe8-b163-762ca0ec16fa',
      '977360ed-4341-48c0-88d0-696cddf1b7d4',
      'daad4bca-595f-4cea-8552-77bd7d5697bd',
      '70347582-acac-4dad-8eee-ef26840fa6b8',
      'f7232894-d418-467b-ad4c-812ddfc5850c',
      '18701a67-670d-4770-ba6d-77e549e26b47',
      'd6b911d3-7024-46d7-800f-32f60c2f8641',
      'cb1c9dc9-f246-4c44-8480-2ce88569dc9f',
      '7c5fe5fa-57df-4280-83a9-a3fd422ee0a2',
      'bec80db5-c39d-4876-8deb-c4033d09040b',
      '1bbbcc0f-7a27-41fd-8f42-59bba5b06f7a',
      'db4bfc34-b935-4ee2-8627-1ecc24ae3721',
      '6e776243-e122-443f-9a4a-715cd903b612',
      '284d668c-350b-404a-85bb-3ebddf486e7f',
      'fe91d4d0-4137-44ae-9e23-477231977b28',
      '96a95536-e9e5-412a-8458-9a2368de41b6',
      'c35257d9-e691-4985-b911-76da4330fa4f',
      'f4bfcb9b-ca86-4c0b-9dc7-bd5f380266ce',
      '33e26f95-fa35-47e7-b21f-b178dbebcfe0',
      'd84915b9-870c-4860-b464-d77876adb939',
      '135d1024-8ace-4067-bcd6-004380e70522',
      'adda9252-632c-4a61-92d3-3176c4f61b31',
      'b33641c5-639b-4b54-90a0-4f2dab1407bc',
      '545a4bd7-096e-4d2c-a242-0e437902f963',
      '8c4546b1-0bd0-42fb-9c2e-97b7f5edc56e',
      '4db12be3-b785-4eb4-bc99-25ccb3346abf',
      '80231666-42fb-4a6c-ad1e-c779faac5852',
      '0d433052-d142-43a8-a5cf-ea4f64e82830',
      'e45da65d-8bd4-4a29-8e1b-abd1496ae425',
      'f76f6858-4e6f-437a-888a-22d3884ad553',
      '6cf20d33-c64e-422d-8183-7b79eba2330e',
      '274a6e8b-42df-4f18-9c42-8522c2eeb2ca',
      '93fe224d-0d36-49ef-9515-97ee470bf6b4',
      '7b07b9fa-af27-43f5-8f89-18829181b400',
      '55c168f2-2ab6-4bef-9561-30c8be5fc550',
      '46c8d1d4-208a-4820-9e83-d511bb86556b',
      '675c802f-43b7-4304-99a2-f98b08eeabe9',
      'b3435b73-d8ce-4981-9da5-278c2dcbbc7d',
      'e12b4374-b67e-40e7-b18f-b5f3e934dee5',
      'd77391d5-39f2-4381-89f0-d3868e4799f1',
      '455f5ddf-1464-4fe9-9e67-00745ae9f834',
      'e0cda0e0-c0c9-466b-bd7c-54c250d4c46c',
      '041298c4-ce2f-4c5a-84ef-458678c15a5a',
      'f401c26b-525b-4c67-b2c8-4c04db60013d',
      '32891ced-f089-4788-aeec-506d3fcf15fb',
      'e35ed12e-4d68-4f5e-a7bd-8018754f1449',
      '5aa5faf8-e8b6-4dc5-bace-2a54f8a056d8',
      '0f49813d-cbb3-4f2d-9ee0-937e34f06b3d',
      'd8443a49-d639-450b-9b8a-e25610755149',
      'f9790e5a-99b8-4a16-a054-1cfb0b1337e7',
      '8a0f7a80-d8fd-4eb4-aafc-16e1b89311b0',
      'ca291a6d-b5de-4ed7-84eb-a1512b5642c3',
      'd507c2a0-7e81-47ae-8fec-f816e94d6dbc',
      '2853288d-371f-465e-ae40-c6b1d611ea9f',
      '92ff5408-d87e-4aa9-a2fa-75f56e8b4791',
      '1762871e-2b1b-4105-a919-048d4d22be91',
      '1d9fa183-c2c9-49fe-9dd8-221784da75f4',
      'fc97fe97-c3d8-479e-90ea-1343bc6b5a0b',
      '651d4621-dd9c-4681-bca8-75cf3c74b2cf',
      'aeca5c47-df6f-4d6c-a88f-7430b88a97cc',
      '939e13b5-3bde-47fa-a0a6-30c05eece0bb',
      'c7ee7e21-b2a0-4f7f-ae24-177a6051881b',
      '17bc07f3-9c7b-41c2-a6f5-4ba3d833459e',
      'b66c355e-4e8a-4ea7-a6bd-da7f79277bfc',
      'd46dc210-8859-4e5c-b9a8-c78562fdc31b',
      '0e42a81b-6c42-4307-8d3a-a2687bce9560',
      'd5c20ca3-2e0a-455f-910f-4ca80679e118',
      'd9afa72d-f726-441e-be12-50d58efa34b1',
      '5e06831c-03d0-4271-8dd8-04a81e8749b8',
      'f01cafbf-3122-4616-914d-ffa7b2ee5a8e',
      'bac95be6-ba0a-4741-9a3b-d694a4fffdee',
      '5d2d8ef5-34ce-42ea-957b-9beb115cbe45',
      '3737bd8e-6f1f-467d-9169-5a0530e9c1d9',
      'df8a85a1-2695-46ef-aa70-c45d13536f7b',
      '636c328d-a1ed-4c5b-9374-fb50353f87db',
      'e485bd92-a085-47b8-9323-7f090ea7ef69',
      'e0bf4699-90f2-44ba-a5e5-fcfb3ba15464',
      '4c9c901d-f189-41ec-a236-497a7483a408',
      'c0b6d978-011b-4a1f-a174-2fdef1e1b4ec',
      'caf8adf6-55f5-4298-a15f-3aa1fd271d52',
      '3a52cf6c-736e-4a92-86a2-5545171847ff',
      '64dcb3ae-abf0-4ca1-9b8f-50e6a5a463a6',
      '34c77937-9859-449a-8d06-fa028cd0c887',
      '997a1b6d-33a2-48bb-8d76-c8bb09b74bd7',
      '37f1e165-2a4a-4a1d-a513-bbd879f2d5f6',
      '51be79d1-e786-4973-96fe-977d0342ba31',
      'aa61c1ed-0484-4497-aaa2-178bde232b2a',
      'cbb9b8a8-0cf0-40e2-8d2e-0cc384d406d0',
      '568077fc-19a1-48ce-bd59-496d12fdf3d4',
      '8388f83d-f5d5-419d-ae6a-5a87045edfc6',
      '47cee697-ae34-4ae2-9515-c32bf5a0a9ee',
      'bf10a883-44c0-457f-a1fa-2c385de8d029',
      '09a7d8dc-6d20-434f-8491-d214bc147a0f',
      '861c2a99-0304-444c-811c-4fc6b790b757',
      'a021788a-23f7-43e0-904c-d1cd41b1dd89',
      '235f58fe-820c-4f8e-b9be-6de93985c617',
      '4b8b4f4b-2ec3-47d2-b301-e5dad3f022ba',
      'ecd45128-d606-4a8b-ba86-c429eede6aba',
      '827d6dbc-a295-4b05-b0f7-e7948e97fdb6',
      '85c9ec0c-e62c-4146-9d41-1211b300196e',
      '95ebe4e0-0f11-4266-bcfc-b4dc02e70452',
      '88f5df2f-ef0b-4ff8-8510-bb5ecbebbbe1',
      '450af10b-ec48-472c-9c6f-ee7bda9f951d',
      '309bcd17-3e6d-4e89-9d78-7685bc0c120f',
      '7df10e84-fc64-405b-8ddc-0221ef35d3e3',
      '20f4686a-4620-4bbf-9bda-26f65dbb3e84',
      '7ba7d26c-f702-445d-b8ed-c83bcdd529d3',
      'fea53521-0217-4e3d-ab2b-f8eb8808c777',
      'ed107a73-148d-4aae-8450-0a3b964d1edb',
      '206e8175-9dfa-46e9-be43-8279e532b598',
      '83706a67-aacb-4fb7-b533-1a5b554253d6',
      '76c6c188-b929-436b-91d1-25d023798821',
      '457dd40c-314b-486c-b50d-9bea224c1df2',
      '2628a25f-95d4-4c34-bbfd-3fbfb723511d',
      'd5cca68a-6f83-4250-afaa-8a477a35414d',
      '7510c5e8-a705-4b69-a04d-677cca22fe00',
      '968a75dd-3350-409a-a4ac-0491f5b504dd',
      '37acd4ce-c70b-4911-9d1c-c0661c984bb7',
      '4474c549-1449-406e-a5e4-ad7cc417b569',
      '7bcfc9fc-9449-427a-b055-1681f2582a1e',
      '9ea8e965-dad4-4ae7-a8ed-38158d7254c3',
      '0f3d5883-e43d-48ed-ac8c-62f0ec70e43a',
      '2e17c7ce-eea8-43b7-a26b-8f30993cef28',
      'b21d990d-8e28-42c2-b6f1-e58196c4b298',
      '3aa62000-bd20-43a6-a32d-d9ae27ef2e87',
      '8b50b96c-6547-4d7b-8578-b11cc7cbc71a',
      '1f56a546-4581-423b-b46e-673c304ffb66',
      '7d3e0bed-bafe-48ce-bbde-7f0fe4b39d4f',
      'b848312e-2809-46f2-824d-ad8901896593',
      '74b6a732-ee26-43be-b334-cefd168c0e42',
      '95318a05-feca-4425-adcf-83fc8cd21536',
      '06ed31fa-aaa5-47eb-a897-c7e530dcabb3',
      'e6842ba4-e992-4cea-a310-b5e2afa81ca5',
      '08b6cb18-fc97-4592-bb05-478d355124ff',
      '91feaf7f-3bcd-43f0-9431-0bc9e9421cec',
      '1f546bb0-ad6b-4758-b1dd-04cb7f95df85',
      'eb66a647-3684-49f1-bee3-898fcf1a0ac2',
      '011eefd9-5ad5-43c1-ae48-191b3cff4975',
      '971f2256-4d54-46b2-a393-b456a39e9bdf',
      '1bce9822-4826-4b9e-8126-161d581e7790',
      '5d41ae8e-de9a-47d6-90a3-148a3e7b4519',
      'a30802cd-fd02-44f2-b3b0-66e000befa46',
      '094846cc-dc24-4448-993f-3f9ecc46b0be',
      'e82688e6-8ac8-4338-b780-a6e567748518',
      'b26e8754-2a3f-479b-ad3b-97a4ec561ed5',
      '4fdcb653-119b-4581-a38b-242d42e46080',
      '2b4ed3ec-d0ca-4985-bd02-666b475ec55f',
      'ca6e1262-0bf4-4a36-a7ee-008384ea0f63',
      '7570a64f-d863-4d07-8d1f-880527158411',
      '358b3608-c73d-42ae-bc83-7e82c76f0806',
      '8eac1e8d-d9bc-44a2-90e7-ffbafee9951f',
      '7ec1fffa-2782-4d75-810d-903de1930a15',
      'e18d5b48-cd5a-4250-b0fe-a3b20384f431',
      '1fa99657-ed89-4846-94a5-7e6b2c5e48ce',
      'e9165b23-830c-48cc-9beb-20de70054ea7',
      'a579059a-30eb-4d86-83a7-e6da0694ef22',
      'cc08c356-3a33-4839-b029-e1ee9726ff4b',
      '0acce245-2c67-4438-a7a8-d84ad6b9b19f',
      'f2beaece-772e-4a59-9a7f-75faf62c7dc6',
      '5a4a8cf7-3c73-4a17-8113-cac73c30354b',
      '197f99ad-5bec-4419-a4fb-6d6e162013d5',
      '6498e22b-450c-44c3-9cae-8e45b03a58e2',
      '9b4ddaf1-3f2b-4973-877b-7c9ff6ffad73',
      'edb4a820-8b03-47ae-a839-e612678e1b19',
      'bedd3126-d439-45ca-9398-196f304e49c5',
      '1306713e-b949-4bef-b139-c8dfa443a169',
      '471a75e0-23a8-4dde-b21b-b7ac39f4f805',
      'cc5a872e-3a85-40b3-835e-edc6a43ca7c3',
      'a39cf9ac-6428-4dce-92fc-d2549347f58e',
      '4d7eeff3-9c9c-46be-9ca1-0955a25fce02',
      'f15155c1-957c-48cd-aa70-b5ed4edde7f8',
      'a213ed8d-f832-4f32-99ee-500052ecbb23',
      '0e441d0a-cf33-41aa-9652-dc31531a7de5',
      'bca4a925-8b17-4344-8438-8f7545a8d465',
      '129f7805-e522-4920-bfc5-9678426fed66',
      'f76c5a23-9ae7-4eed-b91c-22ec0d5a8c28',
      '61e0e760-863d-4c48-8433-15683d39d7a4',
      '25b7e5e9-c0b4-4d20-9db3-0faff53df8c2',
      'a1004b09-b8f1-4ac7-8bb9-8f4382d5dd1a',
      '207055aa-5054-4fe0-8a9e-43425b716935',
      '632e2fb3-3e80-4c4f-b876-9b154baeda3e',
      '5dd52c7d-6831-4ef0-8a62-7b57744f1566',
      '2c114574-3266-4efe-a5d6-160296b90bd2',
      'ece867ce-1238-4706-91f8-bd0ac0edbe31',
      // Additional set of enterprise users provided by sales
      'f9fbf59e-5164-4c65-ae3c-6a008dc752d2',
      '5994fdf8-ede1-4d33-bd86-e5aa5c2a9bf8',
      '27686f57-e1eb-4490-8f20-6c564e6b7928',
      '06ebd001-9e47-4d99-b2f6-2fe265f2ef61',
      '37110f70-160c-43bc-a933-53f8bea3d228',
      'b35a36f2-f917-4edf-a8a2-4e9048137156',
      '54a56335-8622-4ca5-b0fa-374f8791e543',
      '6ff9d3a9-be01-4c4e-a01c-a371dded9233',
      '79aa3271-1915-445d-a383-240ae6b98736',
      '1a993be7-716a-4932-ae75-c4e65d7b4b8c',
      '03896333-6ebc-4ddc-9359-9d2fd0d0e871',
      '770ddbed-f531-4317-8244-38d7e478b9c1',
      '8472273c-dc7d-4876-aafe-b55d61f6c322',
      'e79a7b14-d288-4bbf-9543-0733ab33da81',
      'fecce4c7-7ee2-465f-bfc2-0363b7110531',
      '9abf925e-0735-4115-914d-74214e84d06c',
      'e4b38b83-b4d3-4d11-96fd-13efa95e0c90',
      '894d051f-5f65-4db7-bfbc-9efb8ea80bc1',
      '542eb063-3699-4ed3-972d-77f2fbf7bcd8',
      '21ed18dd-f621-46b7-9dcd-8e28d422e2b1',
      '009eea62-58ed-475f-800f-0c20618abb51',
      // Ad-hoc additions
      'aaf4f743-0cb6-4fb3-aedc-97287058405f',
      'ed70b18a-c13b-4c83-9c66-579c724b9795',
      'bcaa79ce-32e3-44fd-a41e-91c279fb9aef',
      // User study participants
      'e065aa3a-0488-445f-b619-6d38fe0c5409',
      '17655ee3-37db-405e-8b5c-c7e5ded21983',
      'b52a27a7-e986-4216-a8a5-ee624e57eb4e',
      '7e29811c-a766-4766-9ae2-be43cdbe7a67',
      // Content creators requested by growth team
      'a423c5fd-5970-44e3-aeaa-1bce904d757f',
      '5edb9002-c342-426e-85f3-d201011046bc',
      'bc33cea7-53c5-4a9c-9b0c-4d0c6cb2ec17',
      '1415fa9e-8db8-4ac3-acc2-8770e654b777',
      'c7708c7f-d34e-4042-a4bc-f01601fbbdd1',
    ],
    return_value: error 'Must override return_value',
  },
];

// Remote Agents Wave 4: Enable ~20% of discovery users
local remote_agents_wave4 = [
  {
    namespace: [
      // d1-5 are closed and have fewer users, so enable 1/5
      'd2',
      // d6-20 are open and have approximately the same number of users each, so enable 3/15
      'd6',
      'd7',
      'd8',
    ],
    tenant_name: [
      // i1-vanguard0 is larger than the other vanguard tenants, so enabling these is roughly 20%
      'i1-vanguard1',
      'i1-vanguard2',
      'i1-vanguard3',
      'i1-vanguard4',
    ],
    return_value: error 'Must override return_value',
  },
];

// Enterprise tenants with remote agents enabled. This list contains all enterprise tenants that do
// not have CMK enabled and are not in the EU, as those are not supported yet.
local remote_agents_enterprise_tenants = [
  'advisor360',
  'aitutor-mercor',
  'aitutor-pareto',
  'aitutor-turing',
  'ampsortation',
  'augmentdemo',
  'chaidiscovery',
  'coalitioninc',
  'collective',
  'collectors',
  'docana',
  'eikon',
  'enfabrica',
  'flume',
  'gladly',
  'gofundme',
  'humaninterest',
  'lemonade',
  'lettuce',
  'lmnt',
  'lyrahealth',
  'montecarlodata',
  'newfront',
  'nucleus',
  'observeinc',
  'onebuild',
  'onelineage',
  'paystone',
  'pocketlaw',
  'pollyex',
  'purestorage',
  'realtor',
  'replicahq',
  'reveart',
  'roboto',
  'samaya',
  'schoolstatus',
  'sifive',
  'sigma',
  'tangotango',
  'webflow',
  'jotai',
  'accenture',
  'accountants',
  'aeroflowinc',
  'afresh',
  'alida',
  'amazon',
  'amplitude',
  'antler',
  'atommap',
  'atmosphere',
  'athena',
  'augmentmarket',
  'avalara',
  'banyaninfra',
  'betterup',
  'bigcartel',
  'bigcommerce',
  'blend360',
  'boeing',
  'bonfy',
  'brainvoy',
  'brex',
  'campus',
  'capsule',
  'carta',
  'chegg',
  'cisco',
  'clearme',
  'clutch',
  'codem',
  'comarch',
  'coolplanet',
  'cribl',
  'datastax',
  'ddn',
  'detroitsoftware',
  'discord',
  'divelement',
  'docker',
  'doctoranywhere',
  'dotdashmdp',
  'drata',
  'dreamgames',
  'dropbox',
  'ebrd',
  'eightfold',
  'epam',
  'etsy',
  'faire',
  'farmart',
  'ferryhealth',
  'filevine',
  'flyr',
  'fmad',
  'fmglobal',
  'fnal',
  'fourkites',
  'gatik',
  'gilead',
  'glassnode',
  'globality',
  'google',
  'grafana',
  'grammarly',
  'greenberry',
  'groq',
  'gusto',
  'hawaiianair',
  'hey',
  'holidayextras',
  'infogain',
  'itradenetwork',
  'itsmorse',
  'jackson',
  'jaggaer',
  'jpmchase',
  'juniper',
  'kindermorgan',
  'king',
  'kla-tencor',
  'knak',
  'knowbe4',
  'kyro',
  'lattice',
  'leadtech',
  'lentra',
  'lepton',
  'lodgistics',
  'logos',
  'makenotion',
  'medallia',
  'metacoregames',
  'momentummedia',
  'monzo',
  'netdocuments',
  'nextgeneration',
  'nextracker',
  'niceforyou',
  'novozymes',
  'onwish',
  'osf',
  'outreach',
  'oxide',
  'paloaltonetworks',
  'peek',
  'perfios',
  'plaid',
  'plutis',
  'proximity',
  'questlabs',
  'quorum',
  'ramp',
  'redis',
  'ritchiebros',
  'ro',
  'roomsync',
  'sequencefilm',
  'shipwire',
  'shv',
  'silanano',
  'slalom',
  'smartfren',
  'snowflake',
  'sparelabs',
  'specstory',
  'spoton',
  'stratadecision',
  'sysco',
  'taskrabbit',
  'teamwork',
  'techmahindra',
  'tecton',
  'tencent',
  'thebrowser',
  'timescale',
  'toasttab',
  'trustpilot',
  'twilio',
  'uncountable',
  'upwork',
  'usestyle',
  'vercel',
  'veson',
  'viasat',
  'vista',
  'walleyecapital',
  'wearenotch',
  'wehaa',
  'wiz',
  'workato',
  'wwt',
  'x',
  'zapier',
  'zenbusiness',
  'zoyya',
  'zup',
  'augment-test-1',
  'regrello1',
  'pentestra',
  'pentestra-1',
  'pentestra-2',
  'abrigo',
  'acadialps',
  'admarketplace',
  'adobe',
  'affirm',
  'agilize',
  'agoda',
  'aim',
  'aimclear',
  'airslate',
  'airwallex',
  'aisleplanner',
  'aivoicespace',
  'alarm',
  'alteryx',
  'altscore',
  'anduril',
  'answerrocket',
  'arista',
  'ascendum',
  'ashleyfurniture',
  'asite',
  'avaropoint',
  'axonius',
  'bailliegifford',
  'banedigital',
  'bedrock',
  'bhphoto',
  'bidgely',
  'bigid',
  'bizdras',
  'blend-ed',
  'bridgefront',
  'bullish',
  'cadstrom',
  'catonetworks',
  'cello',
  'cigna',
  'citrine',
  'clarifyventures',
  'clay',
  'cloudera',
  'comprehensive',
  'compstak',
  'condenast',
  'convai',
  'coralogix',
  'crowdfarming',
  'crypto',
  'dialpad',
  'digikey',
  'displayr',
  'docyt',
  'doordash',
  'dremio',
  'dxc',
  'egnyte',
  'eibach',
  'elea',
  'elevancehealth',
  'emids',
  'empower',
  'familysearch',
  'fieldai',
  'figma',
  'flockfreight',
  'fortissolutionsg',
  'foundationdevice',
  'fpcomplete',
  'friedkin',
  'futurefertility',
  'gameopedia',
  'garnerhealth',
  'gemini',
  'gloat',
  'gmicloud',
  'gojitsu',
  'gomomentus',
  'graydi',
  'groww',
  'hardy',
  'hasura',
  'hebbia',
  'hillspire',
  'hist',
  'hockeystack',
  'incode',
  'indykite',
  'infinitereality',
  'infotech',
  'instacart',
  'intercom',
  'intrigma',
  'jarustech',
  'jumptrading',
  'junipersquare',
  'keyplay',
  'knorket',
  'kognitos',
  'labcorp',
  'lambda',
  'learnosity',
  'lendable',
  'lendingtree',
  'luminarycloud',
  'luxurypresence',
  'matrixcare',
  'mercury',
  'moneygram',
  'mongodb',
  'mookti',
  'moonactive',
  'multiplier',
  'namadgi',
  'narvar',
  'netflix',
  'netinspect',
  'netlify',
  'neuroforge',
  'nexthop',
  'ninetwothree',
  'ninthwave',
  'nomihealth',
  'novaprime',
  'nsightcare',
  'nue',
  'numenta',
  'octonova',
  'optient',
  'optimalcomplianc',
  'ostro',
  'overit',
  'packsize',
  'pax8',
  'paychex',
  'pepperdash',
  'personio',
  'pingidentity',
  'place',
  'planyear',
  'platformqcouk',
  'plazz',
  'practice',
  'pres',
  'produce8',
  'producthunt',
  'psum',
  'pylon',
  'quantumsoft',
  'rain',
  'relativityspace',
  'rewind',
  'rippling',
  'root',
  'runllama',
  'shinyrobot',
  'showpad',
  'silverkeytech',
  'simplicate',
  'simplifi',
  'siteboss',
  'skf',
  'smartsensesoluti',
  'smyrna',
  'sorenson',
  'statsig',
  'stingray',
  'stravu',
  'surgepays',
  'sweep',
  'swizzleai',
  'sysdig',
  'techempower',
  'teiko',
  'tekion-us',
  'tetrascience',
  'thoughtworks',
  'thumbtack',
  'tide',
  'toolsgroup',
  'toolstation',
  'triumpharcade',
  'uhligcom',
  'ujet',
  'ukr',
  'unigroup',
  'upgrade',
  'veefin',
  'vizientinc',
  'volarium',
  'walmart',
  'wealthsimple',
  'welltower',
  'withintrinsic',
  'workday',
  'x5wgroup',
  'xai',
  'xoriant',
  'ylopo',
  'youscience',
  'ysecurity',
  'zemosolabs',
  'zerity',
];

// Make sure we don't enable remote agents for rubrik
assert !std.any(
  std.map(
    function(tenant) std.member(tenant, 'rubrik'),
    remote_agents_enterprise_tenants,
  ),
) : 'rubrik must NOT be enabled for remote agents';

local self_serve_namespaces = [
  'i0',
  'i1',
  'd1',
  'd2',
  'd3',
  'd4',
  'd5',
  'd6',
  'd7',
  'd8',
  'd9',
  'd10',
  'd11',
  'd12',
  'd13',
  'd14',
  'd15',
  'd16',
  'd17',
  'd18',
  'd19',
  'd20',
];

local apply_return_value(rules, value) =
  std.map(function(rule) rule + { return_value: value }, rules);

{
  model: {
    sync: true,
    description: '',
    envs: {
      production: {
        rules: [
          {
            return_value: 'qweldenv3-2-14b',
          },
        ],
      },
    },
  },
  edit_model: {
    sync: true,
    description: '',
    envs: {
      production: {
        rules: [
          {
            return_value: 'droid-187-33B-FP8-seth-edit',
          },
        ],
      },
    },
  },
  next_edit_model: {
    sync: true,
    description: 'Model for next-edit requests',
    envs: {
      production: {
        rules: [
          {
            return_value: 'raven-edit-v6-15b',
          },
        ],
      },
    },
  },
  instruction_model: {
    sync: true,
    description: 'Model for instruction requests',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: 'claude-instruction-v4-edit',
          },
          {
            return_value: 'claude-instruction-v3-edit',
          },
        ],
      },
    },
  },
  instruction_fallback_models: {
    sync: true,
    description: 'Comma separated list of fallback Models for instruction requests',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-instruction-v2-edit',
          },
        ],
      },
    },
  },
  smart_paste_model: {
    sync: true,
    description: 'Model for smart-paste requests',
    envs: {
      production: {
        rules: [
          {
            return_value: 'forger-v2-qwen-14b-q-32k-edit',
          },
        ],
      },
    },
  },
  smartpaste_force_fuzzy_search: {
    sync: true,
    description: 'Whether or not to use fuzzy search for Forger-based smart paste',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'i0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  apply_instruction_streaming_in_handler: {
    sync: true,
    description: 'If true, enable instruction streaming in the instruction handler',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  connectivity_test_flag: {
    sync: false,
    description: '',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  chat_model: {
    sync: true,
    description: 'The default chat model to use for LEGACY front ends. All newer clients should use chat_raw_output_model instead.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-sonnet-3-5-16k-v5-2-c4-p2-chat',
          },
        ],
      },
    },
  },
  chat_raw_output_model: {
    sync: true,
    description: 'Default model for chat requests with raw output feature enabled',
    envs: {
      production: {
        rules: [
          {
            // Keep AI Tutors on the same version as Dogfood, unless due to deployment
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 'claude-sonnet-v18-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-v17-balanced-c4-p2-chat',
          },
        ],
      },
    },
  },
  chat_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for chat requests',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '',
          },
          {
            return_value: 'claude-sonnet-3-5-16k-v5-2-direct-c4-p2-chat',
          },
        ],
      },
    },
  },
  chat_raw_output_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for chat requests with raw output feature enabled',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 'claude-sonnet-v17-balanced-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-v17-c4-p2-chat',
          },
        ],
      },
    },
  },
  additional_chat_models: {
    sync: true,
    description: 'Add secondary models to chat - PUBLIC - use sha256 hash of model name',
    envs: {
      production: {
        rules: [
          {
            return_value: '{}',
          },
        ],
      },
    },
  },
  agent_chat_model: {
    sync: true,
    description: 'Default chat model for agent-mode chat requests',
    envs: {
      production: {
        rules: [
          {
            // Keep AI Tutors on the same version as Dogfood, unless due to deployment issues
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 'claude-sonnet-4-0-200k-v7-c4-p2-agent',
          },
          {
            return_value: 'claude-sonnet-4-0-200k-v8-c4-p2-agent',
          },
        ],
      },
    },
  },
  agent_chat_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for agent-mode chat requests',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '',
          },
          {
            return_value: 'claude-sonnet-3-7-200k-v3-balanced-c4-p2-agent',
          },
        ],
      },
    },
  },
  client_announcement: {
    sync: true,
    description: 'A message to display to all clients. Should be set to empty string to disable.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  remote_agent_chat_model: {
    sync: true,
    description: 'Default chat model for remote-agent-mode chat requests, falls back to agent_chat_model if empty',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  remote_agent_chat_fallback_model: {
    sync: true,
    description: 'Comma separated list of fallback models for remote-agent-mode chat requests, falls back to agent_chat_fallback_model if empty',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  retrieval_tool_name: {
    sync: true,
    description: 'The name of the retrieval agent. Default is "query_rewrite". Invalid selection will result in server crashing during startup.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'query_rewrite',
          },
        ],
      },
    },
  },
  retrieval_tool_generation_model: {
    sync: true,
    description: 'LLM used by retrieval agent; one of the models deployed in agents-svc. Developed with claude-3-5-sonnet-v2; other model(s) added for experimentation. Invalid selection will result in using the deployed default.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-3-7-sonnet-lb',
          },
        ],
      },
    },
  },
  edit_file_tool_generation_model: {
    sync: true,
    description: 'LLM used by edit file agent; one of the models deployed in agents-svc. Developed with claude-3-5-sonnet-v2; other model(s) added for experimentation. Invalid selection will result in using the deployed default.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-3-7-sonnet-lb',
          },
        ],
      },
    },
  },
  enable_agents: {
    sync: true,
    description: 'If Agents endpoints are enabled for a given tenant or namespace',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  smartpaste_with_pure_additions: {
    sync: true,
    description: 'Improve smartpaste on pure additions.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_smart_paste: {
    sync: true,
    description: 'If Smart Paste is enabled for a given tenant or namespace',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_view_text_document: {
    sync: true,
    description: 'DEPRECATED (enabled everywhere) - If extension should use viewTextDocument behavior to limit vscode impact',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  completion_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for completion requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  edit_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for edit requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 120000,
          },
        ],
      },
    },
  },
  chat_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for chat requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 300000,
          },
        ],
      },
    },
  },
  llm_generate_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for llm_generate requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 120000,
          },
        ],
      },
    },
  },
  codebase_retrieval_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for codebase_retrieval requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 40000,
          },
        ],
      },
    },
  },
  edit_file_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for edit_file requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 90000,
          },
        ],
      },
    },
  },
  upload_blob_content_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for upload_blob_content requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  batch_upload_blob_content_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for batch_upload_blob_content requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  find_missing_blobs_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for find_missing_blobs requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  checkpoint_blobs_timeout_ms: {
    sync: true,
    description: 'The timeout (in ms) for checkpoint_blobs requests made by api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60000,
          },
        ],
      },
    },
  },
  enable_completion_load_balancing: {
    sync: true,
    description: 'If true, enable load balancing for completion requests made by api-proxy. Note that for this change to take effect you probably need to restart api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_workspace_manager_ui: {
    sync: true,
    description: 'DEPRECATED - superceded by enable_workspace_manager_ui_launch. If true, enable the workspace manager UI.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_workspace_manager_ui_launch: {
    sync: true,
    description: 'DEPRECATED - rolled out. If true, enable the workspace manager UI.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_intellij_chat: {
    sync: true,
    description: 'DEPRECATED - superceded by intellij_chat_min_version',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_slow_payload_threshold_ms_completion: {
    sync: true,
    description: 'The maximum time a completion request payload can take to arrive before it is considered slow.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_slow_payload_threshold_ms_next_edit: {
    sync: true,
    description: 'The maximum time a next edit request payload can take to arrive before it is considered slow.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_check_tenant_data_access: {
    sync: true,
    description: 'If true, api proxy will check if the tenant has access to data stored in bigtable proxy.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
              'ysecurity-cmk',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_find_missing: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_checkpoint_blobs: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_client_metrics: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_report_error: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_record_user_events: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_preference_sample: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_edit_resolution: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit_resolution: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit_user_event: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_onboarding_session_event: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_resolve_completions: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_chat_feedback: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit_feedback: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_completion_feedback: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_edit: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_completion: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_memorize: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_agents: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',

    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_batch_upload_blob: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_get_models: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_chat: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              '8edb9096-4819-479d-8bb4-12b637c014e3',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_get_subscription_info: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_server_ban_rules_url_and_hash: {
    sync: true,
    description: 'URL and optional hash of the chat server ban rules. Hash and URL separated by comma.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: 'https://storage.googleapis.com/augment-bazel-data/public/blockexp_2025-04-11T03-26-12.txt',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  chat_server_filter_use_prefix_and_suffix: {
    sync: true,
    description: 'If true, use the prefix and suffix when filtering chat requests (non-strict)',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_server_strict_uses_prefix_and_suffix: {
    sync: true,
    description: 'If true, include the prefix and suffix when filtering chat requests with the strict regex',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_chat_stream: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              '8edb9096-4819-479d-8bb4-12b637c014e3',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_list_external_source_types: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_search_external_sources: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_circuit_breaker_next_edit: {
    sync: true,
    description: 'If true, all requests will fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat: {
    sync: true,
    description: 'If true, chat requests will be throttled -> fail with resource_exhausted. Note that this will be applied to both chat and chat_stream.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '2b5bd6392e3f0c911f6099fc676d4a460387c7a49a1884b52ebe9305fd745ee7',  // eval bot
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for chat requests if api_proxy_throttle_chat is true. The value is the number of tokens per second. Each token represents a single chat request.',
    envs: {
      production: {
        rules:
          apply_return_value(chat_banlist, 0.05)
          + [
            {
              namespace: self_serve_namespaces,
              return_value: 0.4,
            },
            {
              return_value: 0.8,
            },
          ],
      },
    },
  },
  api_proxy_throttle_chat_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for chat requests if api_proxy_throttle_chat is true. Each token represents a single chat request.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  memory_classification_on_first_token: {
    sync: true,
    description: 'If true, memory classification happens on first token received rather than before sending a message.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  use_memory_snapshot_manager: {
    sync: true,
    description: 'If true, use the memory snapshot manager to get memories.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_agent: {
    sync: true,
    description: 'If true, agent requests will be throttled -> fail with resource_exhausted. Note that this will be applied to both chat and chat_stream.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '2b5bd6392e3f0c911f6099fc676d4a460387c7a49a1884b52ebe9305fd745ee7',  // eval bot
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_agent_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for chat requests if api_proxy_throttle_chat_agent is true. The value is the number of tokens per second. Each token represents a single chat request.',
    envs: {
      production: {
        rules:
          apply_return_value(chat_banlist, 0.05)
          + [
            {
              namespace: self_serve_namespaces,
              return_value: 0.4,
            },
            {
              return_value: 0.8,
            },
          ],
      },
    },
  },
  api_proxy_chat_cbf_failure_threshold: {
    sync: true,
    description: 'DISABLED - WILL PROBABLY MOVE TO THIRDPARTY PROXY - The failure threshold for the chat circuit breaker. If the error rate exceeds this value, the circuit breaker will trip.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1.0,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_agent_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for chat requests if api_proxy_throttle_chat_agent is true. Each token represents a single chat request.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_remote_agent: {
    sync: true,
    description: 'If true, requests will be throttled -> fail with resource_exhausted. Note that this will be applied to both chat and chat_stream.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '2b5bd6392e3f0c911f6099fc676d4a460387c7a49a1884b52ebe9305fd745ee7',  // eval bot
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_chat_remote_agent_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for remote agent chat requests if api_proxy_throttle_chat_remote_agent is true. The value is the number of tokens per second. Each token represents a single chat request.',
    envs: {
      production: {
        rules:
          apply_return_value(chat_banlist, 0.05)
          + [
            {
              namespace: self_serve_namespaces,
              return_value: 0.4,
            },
            {
              return_value: 0.8,
            },
          ],
      },
    },
  },
  api_proxy_throttle_chat_remote_agent_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for remote agent chat requests if api_proxy_throttle_chat_remote_agent is true. Each token represents a single chat request.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  api_proxy_chat_max_retries: {
    sync: true,
    description: 'The maximum number of retries for chat requests.',
    envs: {
      production: {
        rules: [
          {
            return_value: 2,
          },
        ],
      },
    },
  },
  anthropic_rate_limit_is_resource_exhausted: {
    sync: true,
    description: 'DEPRECATED - If true, raise a resource exhausted error instead of unavailable.' +
                 'The latter was chosen specifically to trigger retries from api-proxy.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  anthropic_cache_user_message: {
    sync: true,
    description: 'If true, include the user message in the prompt cache (being extra cautious)',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_find_missing: {
    sync: true,
    description: 'If true, requests will be throttled -> fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_find_missing_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for find_missing requests if api_proxy_throttle_find_missing is true. The value is the number of tokens per second. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_throttle_find_missing_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for find_missing requests if api_proxy_throttle_find_missing is true. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 500000,
          },
        ],
      },
    },
  },
  api_proxy_throttle_upload: {
    sync: true,
    description: 'If true, requests will be throttled -> fail with resource_exhausted.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_throttle_upload_fill_rate: {
    sync: true,
    description: 'The fill rate of the token bucket for upload requests if api_proxy_throttle_find_missing is true. The value is the number of tokens per second. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_throttle_upload_capacity: {
    sync: true,
    description: 'The capacity of the token bucket for upload requests if api_proxy_throttle_find_missing is true. Each token represents a single blob name.',
    envs: {
      production: {
        rules: [
          {
            return_value: 500000,
          },
        ],
      },
    },
  },
  check_subscription_status: {
    sync: true,
    description: 'If true, api proxy will check the users subscription status and reject requests with no subscription',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_user_suspension_enabled: {
    sync: true,
    description: 'If true, api proxy will reject requests made by users with suspensions.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_trial_expiration_disclaimer: {
    sync: true,
    description: 'If true, show a disclaimer message to users whose trial is about to expire',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_trial_expiration_days_threshold: {
    sync: true,
    description: 'The number of days before trial expiration when the disclaimer should start showing',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  auth_central_allow_similar_signups: {
    sync: true,
    description: 'If true, allow signups for users with similar emails',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_similar_signups_whitelist_domains: {
    sync: true,
    description: 'Comma separated list of domains to whitelist for similar signups',
    envs: {
      production: {
        rules: [
          {
            return_value: 'augm.io,turing.com,augmentcode.com',
          },
        ],
      },
    },
  },
  auth_central_get_user_uses_idp_user_id: {
    sync: true,
    description: 'If true, the auth central service will use the idp user id to get the user.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_verisoul_enabled: {
    sync: true,
    description: 'If true, the auth central service will use verisoul to check signups',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_bearer_token_auth_enabled: {
    sync: true,
    description: 'If true, the auth central service will process Authorization bearer tokens for test user authentication',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_inactive_subscription_disclaimer: {
    sync: true,
    description: 'If true, inject a message to users whose subscription is inactive in chat and agent mode. check_subscription_status must also be enabled for this to take effect. If this flag is false while check_subscription_status is true, the user will receive a 402 error instead when their subscription is inactive.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enforce_usage_credits: {
    sync: true,
    description: 'If true, inject a message to users whose subscription is out of user message credits in chat and agent mode and do not let them make requests.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_billing_for_remote_agents: {
    sync: true,
    description: 'If true, count remote-agent messages for billing',
    envs: {
      production: {
        rules: [
          {
            namespace: ['staging-shard-0'] + self_serve_namespaces,
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_external_sources_in_chat_min_version: {
    sync: true,
    description: 'The minimum version that we enable external sources (like documentation sets) for in chat in vscode.  This is being used to rollout the feature. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.243.2',
          },
        ],
      },
    },
  },
  embeddings_search_replicate_cache_on_startup: {
    sync: true,
    description: 'If true, get cache state form peer embeddings searchers on startup.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  intellij_chat_min_version: {
    sync: true,
    description: 'Sets the minimum version number of intellij to allow chat. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to the old enable_intellij_chat flag',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.0.73',
          },
          {
            tenant_name: [
              'advisor360',
              'chainalysis',
              'paystone',
              'plejd',
              'purestorage',
              'realtor',
              'bitwarden',
              'rewst',
              'redis',
              'webflow',
              'samaya',
              'bighatbio',
              'sanity',
              'river',
              'ritchiebros',
              'gladly',
              'pocketlaw',
            ],
            return_value: '0.2.0',
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: '0.0.73',
          },
          {
            tenant_name: [
              'i0-vanguard0',
            ],
            return_value: '0.1.4',
          },
          {
            return_value: '0.12.0',
          },
        ],
      },
    },
  },
  bypass_language_filter: {
    sync: true,
    description: 'DEPRECATED- rolled out. If true, allow the client extensions to upload files regardless of the language.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  indexer_retry_max_retries: {
    sync: true,
    description: 'The number of times to retry indexing a blob. This helps to avoid that retryable errors during indexing (timeouts in the embedders) fail the entire indexing of a blob. The plan is to roll this out to all tenants once we validated that it works in dogfood.',
    envs: {
      production: {
        rules: [
          {
            return_value: 7,
          },
        ],
      },
    },
  },
  embeddings_indexer_upload_timeout_seconds: {
    sync: true,
    description: 'The timeout (in seconds) for uploading transformed content to content manager. Longer timeout can help mitigate wasted work on large blobs when embedders are saturated.',
    envs: {
      production: {
        rules: [
          {
            return_value: 300,
          },
        ],
      },
    },
  },
  enable_hindsight: {
    sync: true,
    description: 'Enable Hindsight data collection for the namespace. This flag should be enabled only for those namespaces where user data collection is allowed, such as with contractors and Vanguard. The flag is permanent, and will be rolled out to Vanguard after a short period of time on Dogfood.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            namespace: [
              'i0',
              'i1',  // self serve vanguard is also trainable
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  max_upload_size_bytes: {
    sync: true,
    description: 'Set the recommended maximum file size a client should upload for retrieval. This flag allows us to increase the default max file size from 128KB to 512KB, or larger, in a controlled way. The plan is to rollout the increase in incremental steps (128KB, 256KB, 512KB, etc.) across dogfood, Vanguard, and then all customers.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: 524288,
          },
          {
            return_value: 524288,
          },
        ],
      },
    },
  },
  intellij_force_completion_min_version: {
    sync: true,
    description: 'Sets the minimum version number of intellij to allow forcing completions on all edits. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will do nothing.',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '379fa71c2fda84c36fb3744d9b8403be6c06271bd6073ce22cbc32d4f3d1391e',
            ],
            return_value: '',
          },
          {
            namespace: [
              'aitutor-mercor',
            ],
            return_value: '',
          },
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.5.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'pocketlaw',
              'montecarlodata',
              'sigma',
            ],
            return_value: '0.10.0',
          },
          {
            return_value: '0.68.0',
          },
        ],
      },
    },
  },
  vscode_next_edit_ux1_max_version: {  // temporarily we keep everyone on old experience. This will go away soon
    sync: true,
    description: 'ux1 for next-edit per vscode version',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '634e3c4fb2c5a494a08ebadf9fcd315267876910189eef01992f60fc92268a86',
              'af3bc9ae9872910a9d958c4abc5c08014ac5bbaaf57d4d110d07db3e6b8d07aa',
              '56d407b5f2c9f8baf8b69bf69a4ddeec8ff47c10b861b646e8e25e29bf7eb176',
              'ced4d412e80feac9ef0956e8a4775c9350cf940e4681bbb16fcf88a84b2bbbfa',
            ],
            return_value: '0.338.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'discovery0',
              'ampsortation',
              'observeinc',
              'montecarlodata',
              'reveart',
              'collective',
              'webflow',
            ],
            return_value: '0.338.0',
          },
          {
            namespace: [
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
              'augmentdemo',
            ],
            return_value: '0.338.0',
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.331.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_next_edit_ux2_max_version: {  // temporarily we keep everyone on old experience. This will go away soon
    sync: true,
    description: 'ux2 for next-edit per vscode version',

    // wave-1 users will at some point go here with value 999.0.0
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              'a53aeb555d81788f81755da5ba93866e1d0ba22691975036c71fb33c3664f4dd',
              '12d1694744c5bd7c79589ad1ae75fefb4a6520740df98a27e09829c747d51452',
              '4ccdda635e9a53b53e418e6e055543365c0df43590feae81d9fa0aa061eb5045',
            ],
            return_value: '0.341.0',
          },
          {
            user_id_hmac: [
              '634e3c4fb2c5a494a08ebadf9fcd315267876910189eef01992f60fc92268a86',
              'af3bc9ae9872910a9d958c4abc5c08014ac5bbaaf57d4d110d07db3e6b8d07aa',
              '56d407b5f2c9f8baf8b69bf69a4ddeec8ff47c10b861b646e8e25e29bf7eb176',
              'ced4d412e80feac9ef0956e8a4775c9350cf940e4681bbb16fcf88a84b2bbbfa',
            ],
            return_value: '0.341.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'discovery0',
              'ampsortation',
              'observeinc',
              'montecarlodata',
              'reveart',
              'collective',
              'webflow',
            ],
            return_value: '0.341.0',
          },
          {
            namespace: [
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
              'augmentdemo',
            ],
            return_value: '0.341.0',
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.331.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_next_edit_bottom_panel_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to allow next-edit bottom panel. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to enabled_debug_features on the extension',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            cloud: [
              'GCP_US_CENTRAL1_PROD',
            ],
            return_value: '0.394.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_direct_apply_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to allow direct apply of code changes from codeblocks without going through the diff view. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.393.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_next_edit_min_version: {  //decides if next-edit on or off
    sync: true,
    description: 'Sets the minimum version number of vscode to allow next-edit. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to enabled_debug_features on the extension',
    envs: {
      production: {
        rules: [
          {
            user_id_hmac: [
              '942e9d59dc1e32064e6d589ef6d1d511566131fbd2ab901569ce89e864fe2f15',
              '3044c7a11548c187919dc3b8f1feb87ae111d5e2ff76dce6c5baf37e30682e96',
              '85a8208f519e915f79e9c2bf07c84dba6acccf0adbf309fd2d338e5aa8020aaf',
              '650eeb7a34dea174ec17c722d733802b4514cd057fbf7ab59b1d5595df74a6c2',
              '44df2713e25c920462cd03adfd1f4a80792bbe623f91616f09d48b804c36c55b',
              '7089af64108f96101642d3b53dffb761ad300ff33d77c5ef139efed27f0fe70d',
              '0ee6916c2c3b2070d72f2007a901661643c4b5bf7b6eaa5eff571bc60a62e619',
              'fc275db9f43e6d35bfc770afcde33bb185bbafc68e5b3cafa43257837a9c2743',
              '489ae51a08b573fbdee9b98e6680038fa58df4e1c2a73117269e29d4f0e2c596',
              '9921c110c61e8b986b870fb71cb17c075337bcf0aae4af8158451dc9b37b993d',
              '61e34fa4ac3ffb2ea318acc2680fb820f61426991b67496f37a0b5666eaba6cc',
              'a564eeb2bd416e4fae33e7ecf412b1caa46bafcfc34ea6246035f2e522a0690d',
              'a58755135ba13734ab016f656291058f12a6dcb01e2b373f80278e5dc95c622d',
              '9e9215ba016501252cc47502fd3b4a0514ab294f539e0d5c12e31d0c70a8e752',
              'e9e9e86d829012ce9e4dc053ae2b6dbeb161b1a358d42bbb53798478d8f9e6d6',
              'c70ed1a3ff443f2f962e9e999a51411b9563b5327496f33986d2f0ecedd1dc07',
            ],
            return_value: '0.342.0',
          },
          // uxr 01_21_2025 https://docs.google.com/spreadsheets/d/1-JXmcIsRy21VtEGu5FVnqBWPaqpqts0fqL-aWKDG8ww/edit?usp=sharing
          {
            user_id_hmac: [
              '7ec665134b83b078c9b684096c0ef9f739a852b9ed48f1de20c6d9a8adfcc0e1',
              '403bf9297b3014d1ab2ca1ae74838ce25499d4278a13d1a8385a66ec53d98af9',
              '6f2c156ac1d61c7b13e101d88ee8f9ce322f6d014a3340c9e437d201f9924ac6',
              '0a040aafb3de216a57915e7cfdd6f61be5ab126ccab9ee5c6c6d570ced1bab81',
              'a286ad6655fa85261267ead7ad02f41447d3bcf50790e89adb87e977aef3edc4',  // test-user
            ],
            return_value: '0.335.0',
          },
          // kognito + filevine
          {
            user_id_hmac: [
              '2b91c58105fff4541b92d8a891587b10b58ef17e13ece1350999a7ef34d86b1d',
              'e6166b420e72e21df1940ab5272e392c3bd0d75ae769b4f2c8d02af200a62f68',
              'f94e0751a8972353c479fe76ca3a1ae5bae8e70352bb3fe567e2a0ac5935ca7b',
              'b7c4ff553daa23b827771278fac188c9217d0e54382a321ba1458684a7552bfa',
              'd9565a424cda1df74857eb5d4e69b8090246383d98a9360ebd2814e9e5df7f12',
            ],
            return_value: '0.338.0',
          },
          {
            user_id_hmac: [
              '634e3c4fb2c5a494a08ebadf9fcd315267876910189eef01992f60fc92268a86',
              'af3bc9ae9872910a9d958c4abc5c08014ac5bbaaf57d4d110d07db3e6b8d07aa',
              '56d407b5f2c9f8baf8b69bf69a4ddeec8ff47c10b861b646e8e25e29bf7eb176',
              'ced4d412e80feac9ef0956e8a4775c9350cf940e4681bbb16fcf88a84b2bbbfa',
            ],
            return_value: '0.228.0',
          },
          // wave-1
          {
            tenant_name: [
              'ampsortation',
              'observeinc',
              'montecarlodata',
              'reveart',
              'collective',
              'webflow',
            ],
            return_value: '0.282.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'discovery0',
            ],
            return_value: '0.250.0',
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'augmentdemo',
            ],
            return_value: '0.228.0',
          },
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.215.0',
          },
          {
            tenant_name: [
              'collectors',
              'humaninterest',
              'onelineage',
              'eikon',
              'ddn',
              'guideline',
              'zerity',
              'purestorage',
              'lemonade',
              'advisor360',
              'sifive',
              'realtor',
              'quorum',
              'enfabrica',
              'sigma',
              'afresh',
              'newfront',
              'filevine',
            ],
            return_value: '0.343.0',
          },
          {
            cloud: [
              'GCP_US_CENTRAL1_PROD',
            ],
            return_value: '0.343.0',
          },
          {
            namespace: [
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'e0',
              'e1',
              'e10',
              'e11',
              'e2',
              'e3',
              'e4',
              'e5',
              'e6',
              'e7',
              'e8',
              'e9',
              'flume',
              'i0',
              'i1',
              'lmnt',
              'newfront',
              'nucleus',
              'onebuild',
              'paystone',
              'pocketlaw',
              'pollyex',
              'roboto',
              'samaya',
              'xlb',
              'xlc',
              'xle',
            ],
            return_value: '0.343.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_flywheel_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to allow flywheel features. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work. Clients that are unaware of the flag will fall back to enabled_debug_features on the extension',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
              'augment-test-1',
            ],
            return_value: '0.0.0',
          },
          {
            user_id_hmac: [
              'ab4e6618667e1253d4a1be7ad88d2d1bbca9b95b9cbb70cf26ce29e726d2aaa2',
              'b8bc06a8e0733ded6c57fd47b6ee7b1cf1c1b86850d3917d756b43483448c5ac',
              '7824434b6c3834a2d74a41675f6d880fdf48ec2ee02f54ba5386967c1a02bf4d',
              '24e2294ffea3086215eb5df598c50e04f9811ebfd67d4e06ccb590e3c66d184f',
              '1584bd5544ac0cec85b42f9865d00b58d83adda5fe7211e2b51aeeb909cb808c',
              '7b9945ce0cfbfee179e125f974e597e6baedea0c7c56e033cdf54a8ba3d0c78b',
              '8cb88cb236fc5f50b83cc26daf89e1675976120c3b78146a35287b2431a5e0d9',
              '67e5994487fecb480bd6413e104f89d3193ade9028f7f92a1ef4a8a8847af7ef',
              '03dd024f2ae8bfeec997b90fe096506eaf1d06769cb718099f7d888d3813c997',
            ],
            return_value: '0.234.0',
          },
          {
            return_value: '0.282.0',
          },
        ],
      },
    },
  },
  enable_content_manager_subscriptions_group3: {
    sync: true,
    description: 'Enables content manager subscriptions to pull blobs from the group3 queue. This queue is currently used by docsets and catchup indexing. This flag is a failsafe in case docsets cause too much load, to give us a way to stop pulling blobs from the group3 queue. During normal operation, this should be true everywhere.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  content_manager_pull_messages_batch_size: {
    sync: true,
    description: 'Positive value enables using pull instead of streaming pull. Sets the batch size for pull.',
    envs: {
      production: {
        rules: [
          {
            return_value: 16,
          },
        ],
      },
    },
  },
  enable_auto_docsets: {
    sync: true,
    description: 'If true, docsets are automatically added in addition to the at-mentioned docsets to improve chat quality.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_docset_uploads: {
    sync: true,
    description: 'Enables docset uploads. If this is true, then the docsets in the docset.jsonnet file will be uploaded to content manager (and indexed). If false, the docset server will pretend it has no docsets. This flag is used to stage our rollout of docset uploads, to avoid overwhelming the indexing pipeline, particularly embedders. Once the initial docset list is uploaded to all namespaces, we can remove this flag.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_completion_embeddings_search_cancel: {
    sync: true,
    description: 'If true, the embeddings search service can cancel when a more recent completion request is submitted.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  next_edit_low_quality_threshold_strict: {
    sync: true,
    description: 'A stringent threshold for identifying low-quality edits in the next-edit feature. 1.0 for no threshold.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'dogfood-shard',
            ],
            model_name: [
              'raven-edit-v3-15b',
            ],
            // TODO(vzhao): This value should be specific to the model.
            // The filter rate is 40%.
            // See services/next_edit_host/server/prism_models/README.md
            return_value: '0.85',
          },
          {
            return_value: '1.0',
          },
        ],
      },
    },
  },
  slackbot_max_response_latency_secs: {
    sync: true,
    description: "The maximum time, in seconds, between a Slack message from a user and when the Slackbot will try to respond. If more than this amount of time has passed the message will be dropped. This value should be kept large enough that clock skew isn't a concern.",
    envs: {
      production: {
        rules: [
          {
            return_value: 600,
          },
        ],
      },
    },
  },
  github_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the github processor will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail, then set back to false once all of those messages have been processed.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  github_process_reregister_on_failed_diff: {
    sync: true,
    description: 'If true, the github processor will reregister the repo when diff fails to apply. The intention is that this will be set temporarily when we may need to clear some bad existing state, or if there are known bugs in diff handling.',
    envs: {
      production: {
        rules: [
          {
            // TODO: There is an issue that we seem to run into occasionally with
            // diffs. It seems to always happen for larger files, but I'm not exactly
            // sure what the bug is or why these diffs are failing. This doesn't
            // happen that often, so reregister on these failures until we have time
            // to debug and do a real fix.
            return_value: true,
          },
        ],
      },
    },
  },
  ri_support_database_exporter_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the RI support database exporter will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_share_min_version: {
    sync: true,
    description: 'Minimum version required for a VSCode client to use the Share service. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.284.0',
          },
          {
            namespace: [
              'aitutor-mercor',
              'aitutor-turing',
            ],
            return_value: '0.314.0',
          },
          {
            namespace: [
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: '',  // Explicitly disable sharing for discovery tenants (AU-6267)
          },
          {
            return_value: '0.314.0',
          },
        ],
      },
    },
  },
  intellij_share_min_version: {
    sync: true,
    description: 'Minimum version required for a Intellij client to use the Share service. Empty means "disable for all versions". "0.0.0" means "enable for all versions" Note that the client must actually support this flag for this to work.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.58.0',
          },
          {
            namespace: [
              'aitutor-mercor',
              'aitutor-turing',
            ],
            return_value: '0.87.0',
          },
          {
            namespace: [
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: '',  // Explicitly disable sharing for discovery tenants (AU-6267)
          },
          {
            return_value: '0.87.0',
          },
        ],
      },
    },
  },
  enable_smart_paste_min_version: {
    sync: true,
    description: 'Minimum version required for Smart Paste feature',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'shv-staging',
              'staging-shard-0',
            ],
            return_value: '0.267.0',
          },
          {
            return_value: '0.267.0',
          },
        ],
      },
    },
  },
  workspace_guidelines_length_limit: {
    sync: true,
    description: 'The maximum length of Workspace Guidelines. We use this value to reject over-length Workspace Guidelines on the frontend. Do not decrease this value because it could disrupt existing guidelines users have been using.',
    envs: {
      production: {
        rules: [
          {
            return_value: 49512,
          },
        ],
      },
    },
  },
  user_guidelines_length_limit: {
    sync: true,
    description: 'The maximum length of User Guidelines. We use this value to reject over-length User Guidelines on the frontend. Do not decrease this value because it could disrupt existing guidelines users have been using.',
    envs: {
      production: {
        rules: [
          {
            return_value: 24576,
          },
        ],
      },
    },
  },
  intellij_preference_collection_allowed_min_version: {
    sync: true,
    description: 'If true, the intellij client will allow preference collection.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  retrieval_embedder_multiplex: {
    sync: true,
    description: 'Enable multiplexing over multiple embedder clients during the retrieval process',
    envs: {
      production: {
        // note: eu namespaces are not configured in multiplex mode and will always use the default embedder
        rules: [
          {
            return_value: '{"default": 0, "gsc": 1.0}',
          },
        ],
      },
    },
  },
  indexer_embedder_multiplex: {
    sync: true,
    description: 'Enable multiplexing over multiple embedder clients during the indexing process',
    envs: {
      production: {
        // note: eu namespaces are not configured in multiplex mode and will always use the default embedder
        rules: [
          {
            return_value: '{"default": 0.0, "gsc": 1.0}',
          },
        ],
      },
    },
  },
  completion_inferer_multiplex: {
    sync: true,
    description: 'Enable multiplexing over multiple inference hosts for completion models',
    envs: {
      production: {
        // We limit GSC use to us-central only right now to sidestep EU configuration.
        // TODO(carl): extend to EU support.
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'pre-prod',
            ],
            return_value: '',
          },
          {
            cloud: [
              'GCP_US_CENTRAL1_PROD',
            ],
            return_value: '{"default": 0.65, "gsc": 0.35}',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  max_trackable_file_count: {
    sync: true,
    description: 'The maximum number of files in a trackable source folder. The front end will refuse to track folders with more than this many files. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: 250000,
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: 1000000,
          },
          {
            tenant_name: [
              'adobe',
            ],
            return_value: 500000,
          },
          {
            return_value: 400000,
          },
        ],
      },
    },
  },
  max_trackable_file_count_without_permission: {
    sync: true,
    description: 'The maximum number of files in a source folder before the front will request permission to track it. A value that is greater than or equal to max_trackable_file_count effectively disables this check, as the max_trackable_file_count check takes precedence. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'i0',
              'i1',
            ],
            return_value: 0,
          },
          {
            return_value: 150000,
          },
        ],
      },
    },
  },
  min_uploaded_percentage_without_permission: {
    sync: true,
    description: 'For the purpose of determining whether to request permission to sync a workspace, this value is the minimum percentage of files in a workspace that must have been uploaded for the front end to consider the workspace itself to be uploaded. If less than this percentage of files have been uploaded, the front end will request permission to sync the workspace. A value of 0 causes all workspaces to be considered uploaded and therefore effectively disables this check. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            return_value: 90,
          },
        ],
      },
    },
  },
  next_edit_debounce_ms: {
    sync: true,
    description: 'Debounce time in milliseconds for next edit',
    envs: {
      production: {
        rules: [
          {
            return_value: 400,
          },
        ],
      },
    },
  },
  enable_completion_file_edit_events: {
    sync: true,
    description: 'If true, the client will enable file edit event collection for completions and send to the backend',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  vscode_enable_cpu_profile: {
    sync: true,
    description: 'Enables CPU profiling in VS Code extension',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  verify_folder_is_source_repo: {
    sync: true,
    description: "If true, the front end will request permission to sync source folders that don't appear to be source repos (for example, if they don't have a .git directory or a .augmentroot file). (Currently vscode extension only.)",
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  refuse_to_sync_home_directories: {
    sync: true,
    description: "If true, the front end will refuse to sync a user's home directory. (Currently vscode extension only.)",
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_file_limits_for_syncing_permission: {
    sync: true,
    description: 'If true, the front end will honor the max_trackable_file_count and max_trackable_file_count_without_permission feature flags. If false, it will not count the number of files in a folder, or enforce any size limits when determining whether to request permission to sync it. (Currently vscode extension only.)',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_chat_mermaid_diagrams: {
    sync: true,
    description: 'DEPCREATED - superseded by min_version forms. If true, the front end will render Mermaid codeblocks as Mermaid diagrams in the Chat.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  longest_overlap_lm_model: {
    sync: true,
    description: 'Selects the implementation for the longest overlap language model',
    envs: {
      production: {
        rules: [
          {
            return_value: 'rust',
          },
        ],
      },
    },
  },
  longest_overlap_lm_max_tokens_to_speculate: {
    sync: true,
    description: 'The maximum number of tokens a non-neural model can speculate ahead',
    envs: {
      production: {
        rules: [
          {
            model_name: [
              'forger-smart-paste-sc2-7b-32k',
              'forger-smart-paste-v2-qwen-8b-32k',
              'forger-v2-qwen-14b-q-32k',
            ],
            return_value: 127,
          },
          {
            return_value: 15,
          },
        ],
      },
    },
  },
  enable_summary_titles: {
    sync: true,
    description: 'DEPRECATED - rolled out. If true, a summary title is generated for the chat conversation after the first exhange.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  generation_state_max_tokens_to_speculate_ahead: {
    sync: true,
    description: 'The maximum number of tokens a neural model can speculate ahead',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 1,
          },
          {
            return_value: 1,
          },
        ],
      },
    },
  },
  generation_state_min_speculation_probability: {
    sync: true,
    description: 'The minimum joint probability of speculated tokens for a neural speculation model to generate another token',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 0.0,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  generation_state_generate_with_main_model_first: {
    sync: true,
    description: 'If true, the first generated token will be from the main model',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  generation_state_neural_speculation_enabled: {
    sync: true,
    description: 'If true, neural speculation is enabled',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_signup_max_burst: {
    sync: true,
    description: 'The maximum number of signups allowed in an instant',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  auth_central_signups_per_day: {
    sync: true,
    description: 'The maximum number of signups allowed per day',
    envs: {
      production: {
        rules: [
          {
            return_value: 24000,
          },
        ],
      },
    },
  },
  auth_central_signup_tenant: {
    sync: true,
    description: 'The tenant to use for signups',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 'staging-vanguard0',
          },
          {
            namespace: [
              'central',
            ],
            return_value: 'i1-vanguard1,i1-vanguard2,i1-vanguard3,i1-vanguard4,i1-vanguard5,i1-vanguard6,i1-vanguard7,i0-vanguard1,i0-vanguard2,i0-vanguard3,i0-vanguard4,i0-vanguard5,i0-vanguard6,i0-vanguard7',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  auth_central_signup_done_redirect: {
    sync: true,
    description: 'The callback URL to use for open source signups',
    envs: {
      production: {
        rules: [
          {
            return_value: 'https://www.augmentcode.com/opensource/registration',
          },
        ],
      },
    },
  },
  auth_central_individual_redirect: {
    sync: true,
    description: 'The callback URL to use for individual signups',
    envs: {
      production: {
        rules: [
          {
            return_value: 'https://www.augmentcode.com/registration',
          },
        ],
      },
    },
  },
  auth_central_individual_tenant: {
    sync: true,
    description: 'The tenants to use for individual signups - comma separated list',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: 'staging-discovery0',
          },
          {
            namespace: [
              'central',
            ],
            return_value: 'discovery1,d1-discovery1,d1-discovery2,d1-discovery3,d1-discovery4,d1-discovery5,d1-discovery6,d1-discovery7,discovery2,d2-discovery1,d2-discovery2,d2-discovery3,d2-discovery4,d2-discovery5,d2-discovery6,d2-discovery7,discovery3,d3-discovery1,d3-discovery2,d3-discovery3,d3-discovery4,d3-discovery5,d3-discovery6,d3-discovery7,d4-discovery0,d4-discovery1,d4-discovery2,d4-discovery3,d4-discovery4,d4-discovery5,d4-discovery6,d4-discovery7,d5-discovery0,d5-discovery1,d5-discovery2,d5-discovery3,d5-discovery4,d5-discovery5,d5-discovery6,d5-discovery7,d6-discovery0,d6-discovery1,d6-discovery2,d6-discovery3,d6-discovery4,d6-discovery5,d6-discovery6,d6-discovery7,d7-discovery0,d7-discovery1,d7-discovery2,d7-discovery3,d7-discovery4,d7-discovery5,d7-discovery6,d7-discovery7,d8-discovery0,d8-discovery1,d8-discovery2,d8-discovery3,d8-discovery4,d8-discovery5,d8-discovery6,d8-discovery7,d9-discovery0,d9-discovery1,d9-discovery2,d9-discovery3,d9-discovery4,d9-discovery5,d9-discovery6,d9-discovery7,d10-discovery0,d10-discovery1,d10-discovery2,d10-discovery3,d10-discovery4,d10-discovery5,d10-discovery6,d10-discovery7,d11-discovery0,d11-discovery1,d11-discovery2,d11-discovery3,d11-discovery4,d11-discovery5,d11-discovery6,d11-discovery7,d12-discovery0,d12-discovery1,d12-discovery2,d12-discovery3,d12-discovery4,d12-discovery5,d12-discovery6,d12-discovery7,d13-discovery0,d13-discovery1,d13-discovery2,d13-discovery3,d13-discovery4,d13-discovery5,d13-discovery6,d13-discovery7,d14-discovery0,d14-discovery1,d14-discovery2,d14-discovery3,d14-discovery4,d14-discovery5,d14-discovery6,d14-discovery7,d15-discovery0,d15-discovery1,d15-discovery2,d15-discovery3,d15-discovery4,d15-discovery5,d15-discovery6,d15-discovery7,d16-discovery0,d16-discovery1,d16-discovery2,d16-discovery3,d16-discovery4,d16-discovery5,d16-discovery6,d16-discovery7,d17-discovery0,d17-discovery1,d17-discovery2,d17-discovery3,d17-discovery4,d17-discovery5,d17-discovery6,d17-discovery7,d18-discovery0,d18-discovery1,d18-discovery2,d18-discovery3,d18-discovery4,d18-discovery5,d18-discovery6,d18-discovery7,d19-discovery0,d19-discovery1,d19-discovery2,d19-discovery3,d19-discovery4,d19-discovery5,d19-discovery6,d19-discovery7,d20-discovery0,d20-discovery1,d20-discovery2,d20-discovery3,d20-discovery4,d20-discovery5,d20-discovery6,d20-discovery7',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  auth_central_login_invitations_enabled: {
    sync: true,
    description: 'If true, a list of invitations will be rendered during sign in, if any exist for the user signing in',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_recaptcha_threshold: {
    sync: true,
    description: 'The reCAPTCHA threshold to reject a sign-up. Negative disable reCaptcha. 0.0 allows all sign-ups but still logs.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.3,
          },
        ],
      },
    },
  },
  auth_central_verosint_fingerprinting: {
    sync: true,
    description: 'If true, the auth central will collect a Verosint fingerprint if it is collecting fingerprints.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_verosint_reporting: {
    sync: true,
    description: 'If true, the auth central will report user activity to Verosint and publish reports to RI.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  vscode_chat_hint_decoration_min_version: {
    sync: true,
    description: 'When enabled, a decoration is shown hinting to open the chat',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.274.0',
          },
        ],
      },
    },
  },
  vscode_new_threads_menu_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VSCode client to use the new chat thread UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.305.0',
          },
        ],
      },
    },
  },
  intellij_new_threads_menu_min_version: {
    sync: true,
    description: 'Defines the minimum version of the Intellij client to use the new chat thread UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.77.0',
          },
        ],
      },
    },
  },
  enable_new_threads_list: {
    sync: true,
    description: 'If true, enables the new threads list functionality. This flag can be used independently or in combination with enableBackgroundAgents to control the doUseNewDraftFunctionality behavior.',
    envs: {
      production: {
        rules: apply_return_value(
          agents_rubrik_allowlist,
          true
        ) + [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_untruncated_content_storage: {
    sync: true,
    description: 'If true, enables storage of untruncated content for retrieval tools',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  max_lines_terminal_process_output: {
    sync: true,
    description: 'Maximum number of lines to show from the end of truncated output (0 = use default truncation)',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 20,
          },
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  intellij_show_summary: {
    sync: true,
    description: 'If true, the IntelliJ client will show the codebase summary in chat after the first sync.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_editable_history_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VSCode client to use the editable history feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'i0-vanguard0',
            ],
            return_value: '0.305.0',
          },
          {
            return_value: '0.330.0',
          },
        ],
      },
    },
  },
  enable_guidelines: {
    sync: true,
    description: 'If true, the guidelines feature is enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_rules: {
    sync: true,
    description: 'If true, the .augment/rules feature is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            user_uuid: [
              // Intercom
              '682d007a-6124-46f3-a6a3-8c430fbc386f',
              'afb5bd2c-9bc6-4f70-9dbd-acb07fbe248b',
              '65dcafac-30e8-4970-a21e-8056e7f43fcf',
              'd0133efe-e915-4a8c-9e2e-27e5beb5087d',
              // Webflow
              '2f893a7b-7a98-4b45-b583-053ae48044e4',
              '15c5b99a-9a8a-4940-867d-1725cfbb4b07',
              '7e688163-efde-4581-87d3-980addbea3e6',
              'c34d6135-b46d-40ff-9834-094029c261cc',
              '7c61fbf4-769f-4d2c-9881-260504ac3696',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  codebase_retrieval_budget: {
    sync: true,
    description: 'The backend character budget for codebase retrieval requests.',
    envs: {
      production: {
        rules: [
          {
            return_value: 20000,
          },
        ],
      },
    },
  },
  codebase_retrieval_add_line_numbers: {
    sync: true,
    description: 'If true, line numbers are added to the codebase retrieval output.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  vscode_use_checkpoint_manager_context_min_version: {
    sync: true,
    description: 'The minimum version of the vscode client to rely on checkpoint manager context when computing request context. Legacy behavior is to scan the entire workspace to collect blob names for each request.',

    // Behavior is functional in 0.319.0; we switch most users over in a later version so we can tell from user
    // agent whether the client is using the new method without checking the flags received at startup.
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '0.319.0',
          },
          {
            return_value: '0.323.0',
          },
        ],
      },
    },
  },
  vscode_validate_checkpoint_manager_context: {
    sync: true,
    description: 'Paired with flag "vscode_use_checkpoint_manager_context_min_version". If both flags are enabled, then both new and legacy methods will be performed and the results will be compared to ensure they match.',
    envs: {
      production: {
        rules: [
          {
            // Note: All namespaces ran with validation for minimum 7 business days
            // before disabling.
            // If re-enabling validation for any large workspace (>50k files), we
            // should probably do so for only a fraction of requests, as it would
            // constitute a notable regression
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_enable_chat_mermaid_diagrams_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client where the front end will render Mermaid codeblocks as Mermaid diagrams in the Chat.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.314.0',
          },
        ],
      },
    },
  },
  intellij_completions_history_min_version: {
    sync: true,
    description: 'Defines the minimum version of the Intellij client to use the completions history feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  enable_glean: {
    sync: true,
    description: 'If true, users can sign-in to Glean and all slackbot requests will call the glean API to get relevant documents and add them to the prompt. This is deployed only in staging or only for friendly customers.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'dogfood-shard',
              'collectors',
              'ddn',
              'webflow',
              'intuit',
              'realtor',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_task_list_min_version: {
    sync: true,
    description: 'Sets the minimum version number of vscode to enable the task list functionality. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.482.0',
          },
        ],
      },
    },
  },
  intellij_task_list_min_version: {
    sync: true,
    description: 'Sets the minimum version number of intellij to enable the task list functionality. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_support_tool_use_start_min_version: {
    sync: true,
    description: 'Enable the use of TOOL_USE_START nodes in the vscode client.  The chatStream can use this to get notified when a tool use is started.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  slackbot_enable_v2_formatter: {
    sync: true,
    description: 'If true, use the v2 slackbot prompt formatter. Now that this has been enabled everywhere, we should remove this flag and the old formatter in the near future.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'collectors',
              'dogfood-shard',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  // Expected to be short-lived, as the client does pass this in ChatFeatureDetectionFlags
  // Just here to roll out to staging before enabling in prod
  chat_generate_tool_use_start: {
    sync: true,
    description: 'If true, TOOL_USE_START nodes may be returned by Chat/ChatStream to clients that also support it',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_reprompt_empty_response: {
    sync: true,
    description: 'If true, chat requests that produce no output will be retried once with a continuation prompt',
    envs: {
      production: {
        rules: [
          {
            // If we're happy with this feature, we can move to it being deploy-time
            // and configured as part of the model config
            model_name: [
              'claude-sonnet-3-7-200k-v3-c4-p2-agent',
              'claude-sonnet-4-0-200k-v5-c4-p2-agent',
              'claude-sonnet-4-0-200k-v6-c4-p2-agent',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_postprocess_code_block_filter_enabled: {
    sync: true,
    description: 'If true, Sentry postprocessing will only run on chat responses that contain code blocks (triple backticks or <augment_code_snippet> tags). This optimization reduces unnecessary postprocessing for regular text responses.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  smart_paste_precompute_mode: {
    sync: true,
    description: "Controls smart paste precomputation: 'off' (never), 'visible-hover' (default, on hover), 'visible' (when visible), 'on' (immediate)",
    envs: {
      production: {
        rules: [
          {
            return_value: 'visible',
          },
        ],
      },
    },
  },
  intellij_smart_paste_min_version: {
    sync: true,
    description: 'Defines the minimum version of the Intellij client to enable smart paste.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.119.0',
          },
        ],
      },
    },
  },
  vscode_design_system_rich_text_editor_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to use the new design system rich text editor component.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.363.0',
          },
        ],
      },
    },
  },
  intellij_design_system_rich_text_editor_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to use the new design system rich text editor component.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.146.0',
          },
        ],
      },
    },
  },
  allow_client_feature_flag_overrides: {
    sync: true,
    description: 'If true, the client can override feature flags received from the server. DO NOT ENABLE THIS OUTSIDE OF DOGFOOD',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_chat_with_tools_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable the use of tools in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_chat_with_tools_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable the use of tools in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_chat_multimodal_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to use chat multimodal features.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.373.0',
          },
          {
            return_value: '0.384.0',
          },
        ],
      },
    },
  },
  intellij_chat_multimodal_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to use chat multimodal features.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.207.0',
          },
        ],
      },
    },
  },
  intellij_enable_chat_mermaid_diagrams_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client where the front end will render Mermaid codeblocks as Mermaid diagrams in the Chat.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.128.0',
          },
        ],
      },
    },
  },
  vscode_agent_edit_tool: {
    sync: true,
    description: 'Specifies which edit tool to use in VSCode agent mode.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'str_replace_editor_tool',
          },
        ],
      },
    },
  },
  vscode_agent_mode_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable agent mode in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
        ] + apply_return_value(
          agents_rubrik_allowlist,
          '0.399.1'
        ) + [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '',
          },
          {
            namespace: [
              'aitutor-mercor',
            ],
            return_value: '0.367.0',
          },
        ] + apply_return_value(
          agents_wave2_latest +
          agents_wave2_stable,
          '0.373.0'
        ) + apply_return_value(
          agents_wave3_latest +
          agents_wave3_stable,
          '0.380.0'
        ) + apply_return_value(
          agents_wave4_latest +
          agents_wave4_stable,
          '0.387.0'
        ) + [
          {
            return_value: '0.395.0',
          },
        ],
      },
    },
  },
  vscode_agent_mode_min_stable_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable agent mode in chat conversations for stable (not pre-release) versions.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
        ] + apply_return_value(
          agents_rubrik_allowlist,
          '0.399.1'
        ) + [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '',
          },
          {
            namespace: [
              'aitutor-mercor',
            ],
            return_value: '0.367.0',
          },
        ] + apply_return_value(
          agents_wave2_latest +
          agents_wave2_stable,
          '0.373.0'
        ) + apply_return_value(
          agents_wave3_latest +
          agents_wave3_stable,
          '0.380.0'
        ) + apply_return_value(
          agents_wave4_latest +
          agents_wave4_stable,
          '0.387.0'
        ) + [
          {
            return_value: '0.399.1',
          },
        ],
      },
    },
  },
  intellij_agent_mode_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable agent mode in chat conversations.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '0.0.0',
          },
        ] + apply_return_value(
          agents_rubrik_allowlist,
          '0.170.0'
        ) + [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: '',
          },
        ] + apply_return_value(
          agents_wave2_latest +
          agents_wave3_latest +
          agents_wave4_latest +
          agents_intellij,
          '0.157.0'
        ) + [
          {
            return_value: '0.170.0',
          },
        ],
      },
    },
  },
  api_proxy_blacklisted_checkpoint_ids: {
    sync: true,
    description: 'Incident-10317 - Comma separated list of checkpoint ids that should be blacklisted from the api proxy',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'd1',
            ],
            return_value: '4aa2a5395a151c6055fbf7d6ddbd208a1785b0335f2c903c0745f7e95f01ac3f',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  agent_continuation_latency_injection: {
    sync: true,
    description: 'Number of seconds to delay AGENT_CHAT requests delivering tool results',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: 0.2,
          },
          {
            return_value: 0.2,
          },
        ],
      },
    },
  },
  vscode_deprecated_version: {
    sync: true,
    description: 'Defines the version of the VS Code client at or below which we will report it deprecated.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_deprecated_version: {
    sync: true,
    description: 'Defines the version of the IntelliJ client at or below which we will report it deprecated.',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_blocked_versions: {
    sync: true,
    description: 'Comma separated list of blocked IntelliJ versions',
    envs: {
      production: {
        rules: [
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_syncing_progress_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to show syncing progress in chat.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  elo_model_configuration: {
    sync: true,
    description: 'Configuration of models for ELO comparisons in AiTutors',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'staging-shard-0',
            ],
            return_value: {
              // highPriorityModels can be either:
              // 1. A flat list of models for random pair selection (battle royale)
              // 2. A list of pairs ([[model1, model2], [model3, model4]]) for specific comparisons
              highPriorityModels: [
                'claude-sonnet-v18-c4-p2-chat',
                'claude-sonnet-v17-balanced-c4-p2-chat',
              ],
              regularBattleModels: [
                'claude-sonnet-v17-balanced-c4-p2-chat',
                'claude-sonnet-16k-v16-c4-p2-chat',
                'claude-sonnet-16k-v16r2-chat',
                'claude-sonnet-3-5-16k-v11-4-chat',
                'claude-sonnet-16k-v15-1r2-chat',
                'claude-sonnet-16k-v15-1nor2-chat',
                'claude-sonnet-16k-v16nor2-chat',
                'claude-sonnet-16k-v16r2-chat',
              ],
              highPriorityThreshold: 0,
            },
          },
          {
            return_value: {},
          },
        ],
      },
    },
  },
  vscode_chat_stable_prefix_truncation_min_version: {
    sync: true,
    description: 'Whether to use the chat history truncation yielding more stable prefix to protect caching',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '0.402.0',
          },
        ],
      },
    },
  },
  memories_params: {
    sync: true,
    description: 'All parameters of the memories system, including prompts',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: {
              injection_prompt: |||
                Here are the memories already saved:
                ```
                {currentMemories}
                ```
                Here is the new memory to remember:
                ```
                - {newMemory}
                ```
                Incorporate the new memory into the current memories, by adding/removing/modifying memories as needed.
                If new memory is already present in current memories, just return current memories as is.

                Memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)
                Example format of updated memories:
                ```
                # Group 1
                - Memory 1
                - Memory 2

                # Group 2
                - Memory 3
                - Memory 4

                ...
                ```

                Write ONLY full updated memories and NOTHING else. Start your response with "```" and end it with "```". Don't do ANY preamble or postamble.
              |||,
              compression_prompt: |||
                Here are the full memories assembled through all interactions of user with coding agent:
                ```
                {memories}
                ```
                You task is to summarize and merge related or redundant memories to retain the most informative and distinct ones.
                Result should be ~{compressionTarget} lines long.

                Prioritize preserving information that is:
                - Relevant to codebase, policies, preferred technologies or patterns
                - Will be useful in long-term
                - Describes user, user knowledge, user preferences or long-term information about user (like name/email)
                {recentMemoriesSubprompt}

                Updated memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)
                Example format of updated memories:
                ```
                # Group 1
                - Memory 1
                - Memory 2

                # Group 2
                - Memory 3
                - Memory 4

                ...
                ```

                Write ONLY full updated memories and NOTHING else. Start your response with "```" and end it with "```". Don't do ANY preamble or postamble.
              |||,
              num_recent_memories_to_keep: 5,
              recent_memories_subprompt: |||
                - Also here are the most recent memories that should be preserved (do not create "recent memories" group, though):
                ```
                {recentMemories}
                ```
              |||,
              classify_and_distill_prompt: |||
                ###
                # ENTER MESSAGE ANALYSIS MODE
                # IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING
                # YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF
                # YOU RETURN ONLY JSON
                # ###

                Here is the next message from the user:
                ```
                {message}
                ```
                Your task is to detect if the next message contains some information worth remembering in long-term.
                Information is worth remembering if in the next message user gives some new information, asks to do something differently or describes user preferences.
                Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!
                Also, if user hints to how/where tests should be written, it is also worth remembering.
                If knowledge is overly specific to the current task, then it is NOT worth remembering.
                If user reports some task specific bug, it is NOT worth remembering.

                Exceptions (do not remember such information):
                - If user asks not to use some existing tools

                Return JSON with three keys (in provided order): "explanation" (str), "worthRemembering" (bool) and "content" (str).
                "explanation" should be short (1 sentence) text that describes why the information is worth remembering or not.
                "content" should be short (1 sentence) text that describes the information worth remembering.
                If "worthRemembering" is false, then "content" should be empty.

                Write ONLY JSON and no other text (start response with "{"). All planning/reasoning/etc should be put into "explanation". Don't use any tools for it.
                Example: {"explanation": "some explanation", "worthRemembering": true or false, "content": "memory content"}

              |||,
              enable_memories_tracing: true,
              upper_bound_size: 80,
              compression_target: 60,
              remember_tool_model_name: 'gemini-2-flash-001-simple-port',
              language_localization_prompt: |||
                This tree represents all folders in the codebase that contain {programmingLanguage} files:
                ```
                {languageTree}
                ```

                Please analyze this tree and summarize major places where {programmingLanguage} code is used.

                At the end write a final response as a one-line, comma-separated list of paths to these major places.
                Enclose this list in a pair of XML tags called <locations></locations>.

                Example response:
                <locations>src/project1,/project2/,/project3/tools</locations>
              |||,
              detect_languages_prompt: |||
                Here is the list of most common file extensions in the codebase:
                ```
                {fileExtensionsList}
                ```
                Can you please list most important programming languages used in the project?

                Exclude any languages that are declerative such as JSON, YAML, ProtoBuf, etc.
                Exclude any languages that have INSIGNIFICANT AMOUNT of code in the codebase.

                Combine any variants of a language into a single entry. For example, treat "ts" and "tsx" as "ts".
                Also combine language of the same family as a single entry. For example, "js/ts", "c/cpp".

                Respond a JSON that maps language name to the list of file extensions present in the codebase.
                Example response: {"python": ["py"], "js/ts": ["js", "ts", "tsx"], "java": ["java"]}

                Respond ONLY with the JSON. Do not include any preceeding or succeeding text.
                If no programming languages are used, respond with an empty JSON.
              |||,
              orientation_compression_prompt: |||
                Here are results of codebase analysis:
                ```
                {assembledKnowledge}
                ```

                Summarize it into a file `AGENT.md` containing important information about the codebase.
                Especially include:
                - build and test commands
                - tests organization

                The file you create will be given to agentic coding agents (such as yourself) that operate in this repository. Make it no longer than 60 lines.
                Enclose the file in a pair of XML tags called <agent_md></agent_md>.
              |||,
              orientation_build_test_query: |||
                You are inside a codebase. Content of root folder (output of "ls ."):
                ```
                {rootFolderContent}
                ```

                Here is a list of major locations where {language} code is used:
                ```
                {locationList}
                ```

                Your task is to figure out build and test commands (especially for running a single test) for {language} part of the codebase.
                Also figure out general tests organization.

                Make sure to check all of these locations when figuring out the build and test commands.
                Note that different part of the codebase might use different build and test tools, so you need to check all of them.

                Return your response using the "complete" tool.
              |||,
              orientation_max_languages: 3,
              orientation_concurrency_level: 2,
              orientation_model_name: 'claude-sonnet-3-7-simple-c4-p2-chat',
              enable_initial_orientation: true,
            },
          },
          {
            return_value: {
              injection_prompt: |||
                Here are the memories already saved:
                ```
                {currentMemories}
                ```
                Here is the new memory to remember:
                ```
                - {newMemory}
                ```
                Incorporate the new memory into the current memories, by adding/removing/modifying memories as needed.
                If new memory is already present in current memories, just return current memories as is.

                Memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)
                Example format of updated memories:
                ```
                # Group 1
                - Memory 1
                - Memory 2

                # Group 2
                - Memory 3
                - Memory 4

                ...
                ```

                Write ONLY full updated memories and NOTHING else. Start your response with "```" and end it with "```". Don't do ANY preamble or postamble.
              |||,
              compression_prompt: |||
                Here are the full memories assembled through all interactions of user with coding agent:
                ```
                {memories}
                ```
                You task is to summarize and merge related or redundant memories to retain the most informative and distinct ones.
                Result should be ~{compressionTarget} lines long.

                Prioritize preserving information that is:
                - Relevant to codebase, policies, preferred technologies or patterns
                - Will be useful in long-term
                - Describes user, user knowledge, user preferences or long-term information about user (like name/email)
                {recentMemoriesSubprompt}

                Updated memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)
                Example format of updated memories:
                ```
                # Group 1
                - Memory 1
                - Memory 2

                # Group 2
                - Memory 3
                - Memory 4

                ...
                ```

                Write ONLY full updated memories and NOTHING else. Start your response with "```" and end it with "```". Don't do ANY preamble or postamble.
              |||,
              num_recent_memories_to_keep: 5,
              recent_memories_subprompt: |||
                - Also here are the most recent memories that should be preserved (do not create "recent memories" group, though):
                ```
                {recentMemories}
                ```
              |||,
              classify_and_distill_prompt: |||
                ###
                # ENTER MESSAGE ANALYSIS MODE
                # IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING
                # YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF
                # YOU RETURN ONLY JSON
                # ###

                Here is the next message from the user:
                ```
                {message}
                ```
                Your task is to detect if the next message contains some information worth remembering in long-term.
                Information is worth remembering if in the next message user gives some new information, asks to do something differently or describes user preferences.
                Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!
                Also, if user hints to how/where tests should be written, it is also worth remembering.
                If knowledge is overly specific to the current task, then it is NOT worth remembering.
                If user reports some task specific bug, it is NOT worth remembering.

                Exceptions (do not remember such information):
                - If user asks not to use some existing tools

                Return JSON with three keys (in provided order): "explanation" (str), "worthRemembering" (bool) and "content" (str).
                "explanation" should be short (1 sentence) text that describes why the information is worth remembering or not.
                "content" should be short (1 sentence) text that describes the information worth remembering.
                If "worthRemembering" is false, then "content" should be empty.

                Write ONLY JSON and no other text (start response with "{"). All planning/reasoning/etc should be put into "explanation". Don't use any tools for it.
                Example: {"explanation": "some explanation", "worthRemembering": true or false, "content": "memory content"}

              |||,
              enable_memories_tracing: true,
              upper_bound_size: 80,
              compression_target: 60,
              remember_tool_model_name: 'gemini-2-flash-001-simple-port',
              language_localization_prompt: |||
                This tree represents all folders in the codebase that contain {programmingLanguage} files:
                ```
                {languageTree}
                ```

                Please analyze this tree and summarize major places where {programmingLanguage} code is used.

                At the end write a final response as a one-line, comma-separated list of paths to these major places.
                Enclose this list in a pair of XML tags called <locations></locations>.

                Example response:
                <locations>src/project1,/project2/,/project3/tools</locations>
              |||,
              detect_languages_prompt: |||
                Here is the list of most common file extensions in the codebase:
                ```
                {fileExtensionsList}
                ```
                Can you please list most important programming languages used in the project?

                Exclude any languages that are declerative such as JSON, YAML, ProtoBuf, etc.
                Exclude any languages that have INSIGNIFICANT AMOUNT of code in the codebase.

                Combine any variants of a language into a single entry. For example, treat "ts" and "tsx" as "ts".
                Also combine language of the same family as a single entry. For example, "js/ts", "c/cpp".

                Respond a JSON that maps language name to the list of file extensions present in the codebase.
                Example response: {"python": ["py"], "js/ts": ["js", "ts", "tsx"], "java": ["java"]}

                Respond ONLY with the JSON. Do not include any preceeding or succeeding text.
                If no programming languages are used, respond with an empty JSON.
              |||,
              orientation_compression_prompt: |||
                Here are results of codebase analysis:
                ```
                {assembledKnowledge}
                ```

                Summarize it into a file `AGENT.md` containing important information about the codebase.
                Especially include:
                - build and test commands
                - tests organization

                The file you create will be given to agentic coding agents (such as yourself) that operate in this repository. Make it no longer than 60 lines.
                Enclose the file in a pair of XML tags called <agent_md></agent_md>.
              |||,
              orientation_build_test_query: |||
                You are inside a codebase. Content of root folder (output of "ls ."):
                ```
                {rootFolderContent}
                ```

                Here is a list of major locations where {language} code is used:
                ```
                {locationList}
                ```

                Your task is to figure out build and test commands (especially for running a single test) for {language} part of the codebase.
                Also figure out general tests organization.

                Make sure to check all of these locations when figuring out the build and test commands.
                Note that different part of the codebase might use different build and test tools, so you need to check all of them.

                Return your response using the "complete" tool.
              |||,
              orientation_max_languages: 3,
              orientation_concurrency_level: 2,
              orientation_model_name: 'claude-sonnet-3-7-simple-c4-p2-chat',
              enable_initial_orientation: false,
            },
          },
        ],
      },
    },
  },  // Note! This flag supports more than just the /smart-paste-stream endpoint; backend
  // services may rely on a smart paste model being available in the namespace.
  intellij_ask_for_sync_permission_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to ask for sync permission.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_background_agents_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable launching background agents.',
    envs: {
      production: {
        rules: apply_return_value(remote_agents_wave0, '0.434.0') + apply_return_value(remote_agents_wave1, '0.439.0') + apply_return_value(remote_agents_wave2, '0.449.0') + apply_return_value(remote_agents_wave3, '0.462.0') + apply_return_value(remote_agents_wave4, '0.470.1') + [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            namespace: self_serve_namespaces,
            return_value: '0.472.1',
          },
          {
            tenant_name: remote_agents_enterprise_tenants,
            return_value: '0.472.1',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_background_agents_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable launching background agents.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_virtualized_message_list_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable virtualized message list.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  intellij_virtualized_message_list_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable virtualized message list.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  vscode_rich_checkpoint_info_min_version: {
    sync: true,
    description: 'Defines the minimum version of the VS Code client to enable rich checkpoint info.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.393.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  next_edit_inferer_multiplex: {
    sync: true,
    description: 'Enable multiplexing over inference hosts for next edit models',
    envs: {
      production: {
        rules: [
          {
            return_value: '{"default": 0, "gsc": 1.0}',
          },
        ],
      },
    },
  },
  next_edit_use_stream_mux: {
    sync: true,
    description: 'Enable stream muxing for next edit',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  api_proxy_enable_model_name: {
    sync: true,
    description: 'If false, the model name parameter sent to api proxy will be ignored.',
    envs: {
      production: {
        rules: [
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: true,
          },
          {
            env: ['STAGING'],
            return_value: true,
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_async_ops_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the auth central async ops worker will process messages from the dead letter queue',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_user_tier_change: {
    sync: true,
    description: 'If true, the auth central user tier change RPC & worker will be enabled',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_team_management_enabled: {
    sync: true,
    description: 'If true, the auth central team management RPCs will be enabled on the backend',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_invite_users_to_tenant_plus_allowed: {
    sync: true,
    description: 'If true, the auth central service will allow inviting users to a tenant with a + in their email',
    envs: {
      production: {
        rules: [
          {
            env: ['PROD'],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  // intellij remember tool min version. return 0.161 for staging and shv and false for everyoine else
  intellij_remember_tool_min_version: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to use the remember tool feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.161.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  agents_use_ide_state_in_prompt: {
    sync: true,
    description: 'If true, render the IDE state in the prompt. This is temporary while we roll out the feature. Shout at arun@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agents_add_supervisor_prompt_every_turn: {
    sync: true,
    description: 'If true, add the supervisor prompt every turn. This prompt is used to reminder the agent of important instructions. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agents_add_supervisor_prompt_to_prefill_every_turn: {
    sync: true,
    description: 'If true, add the supervisor prompt to model response prefill on every turn. This prompt is used to reminder the agent of important instructions. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agents_add_system_prompt_to_prefill_every_turn: {
    sync: true,
    description: 'If true, add the system prompt to model response prefill on every turn. This can potentially make the model follow the system prompt more closely. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agents_add_date_to_system_prompt: {
    sync: true,
    description: 'If true, add the current date to the system prompt. This is temporary while we roll out the feature. Shout at vpas@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agents_save_file_partial_tool_use: {
    sync: true,
    description: 'If true, return partial tool use for the save-file command. This is temporary while we roll out the feature. Shout at jeff@ to remove it.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  // intellij enable user guidelines feature flag
  intellij_enable_user_guidelines: {
    sync: true,
    description: 'If true, the intellij client will enable the user guidelines feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            client: 'intellij',
            min_client_version: '0.197.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_user_guidelines_in_settings: {
    sync: true,
    description: 'If true, the intellij client will enable the user guidelines feature in the settings page.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            client: 'intellij',
            min_client_version: '0.197.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_enable_workspace_guidelines: {
    sync: true,
    description: 'If true, the intellij client will enable the workspace guidelines feature.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            client: 'intellij',
            min_client_version: '0.197.0',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_enable_homespun_gitignore: {
    sync: true,
    description: 'If true, the intellij client will use a reimplemented gitignore parser, rather than nl.basjes.gitignore.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            user_uuid: [
              '11202063-3a59-434a-9158-9a0d63f43d2b',
              '17c06c71-5375-4b1d-84ee-8c98cc11b059',
              '38118a4f-7bdc-4139-878f-f634d4dfe71a',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  // intellij enable workspace guidelines feature flag
  enable_supabase_service: {
    sync: true,
    description: 'If true, the Supabase service is enabled for the user. This controls access to the Supabase Management API.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  misuse_monitor_dry_run: {
    sync: true,
    description: 'If true, the misuse monitor will run in dry run mode, which means it will log potential misuse but not take any action. This is useful for testing and validating the misuse detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  free_trial_duplication_dry_run: {
    sync: true,
    description: 'If true, the free trial duplication monitor will run in dry run mode, which means it will log potential free trial duplication but not take any action. This is useful for testing and validating the free trial duplication detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  free_trial_recent_user_duplication_dry_run: {
    sync: true,
    description: 'If true, the misuse monitor job checking recent users for free trial abuse will run in dry run mode, which means it will log potential free trial abuse but not take any action. This is useful for testing and validating the logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  free_trial_feature_vector_duplication_dry_run: {
    sync: true,
    description: 'If true, the free trial feature vector duplication monitor will run in dry run mode, which means it will log potential free trial duplication but not take any action. This is useful for testing and validating the free trial duplication detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  free_trial_feature_vector_duplication_v2_dry_run: {
    sync: true,
    description: 'If true, the free trial feature vector duplication monitor V2 will run in dry run mode, which means it will log potential free trial duplication but not take any action. This V2 version includes improved field name logging and enhanced fraud detection logic.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  community_abuse_dry_run: {
    sync: true,
    description: 'If true, the community abuse monitor will run in dry run mode, which means it will log potential community abuse but not take any action. This is useful for testing and validating the community abuse detection logic before enforcing it.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_throttle_block_duration_ms: {
    sync: true,
    description: "The duration (in ms) to block for when we're throtting a user in api proxy.",
    envs: {
      production: {
        rules: [
          {
            return_value: 30000,
          },
        ],
      },
    },
  },
  chat_server_max_cjk_char_count: {
    sync: true,
    description: 'The maximum number of CJK (Chinese, Japanese, Korean) characters allowed in a chat request. If the count exceeds this value, the request will be blocked (if blocking is enabled).',
    envs: {
      production: {
        rules: [
          {
            return_value: 2000,
          },
        ],
      },
    },
  },
  chat_server_block_high_cjk_count: {
    sync: true,
    description: 'If true, requests with a high count of CJK characters (exceeding chat_server_max_cjk_char_count) will be blocked. If false, these requests will only be logged but not blocked.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_suspicious_max_ips_per_user_hour: {
    sync: true,
    description: 'Maximum number of IP addresses allowed per user ID in single api proxy instance within the last hour. If a user exceeds this number, their requests will be marked as suspicious. Set to 0 to disable this check.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  auth_central_enable_stripe_event_processor: {
    sync: true,
    description: 'If true, the auth central service will initialize and run the Stripe event processor. Set to false in dev environments to make Stripe webhook optional.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_stripe_event_processor_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the Stripe event processor will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_enable_billing_event_processor: {
    sync: true,
    description: 'If true, the auth central service will initialize and run the Billing event processor. \n    Always set to false in dev environments to make Billing webhook optional.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  auth_central_billing_event_processor_process_dead_letter_queue: {
    sync: true,
    description: 'If true, the Billing event processor will pull messages from its dead letter queue and try to process them. The intention is that this flag will be flipped to true after we have resolved an incident that caused messages to fail.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  chat_anthropic_vertexai_load_balance_europe_rate: {
    sync: true,
    description: 'The percentage of requests to route to the Europe region for Anthropic VertexAI client.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.2,
          },
          {
            return_value: 0.27,
          },
        ],
      },
    },
  },
  chat_anthropic_vertexai_load_balance_asia_rate: {
    sync: true,
    description: 'The percentage of requests to route to the Asia region for Anthropic VertexAI client.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.2,
          },
          {
            return_value: 0.51,
          },
        ],
      },
    },
  },
  chat_anthropic_direct_rate: {
    sync: true,
    description: 'The percentage of requests to route to the Anthropic direct client.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0.1,
          },
          {
            return_value: 0.22,
          },
        ],
      },
    },
  },
  chat_anthropic_vertexai_load_balance_models: {
    sync: true,
    description: 'A comma-separated list of models for which to enable load balancing for Anthropic VertexAI client.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-3-7-sonnet@20250219',
          },
        ],
      },
    },
  },
  embeddings_search_checkpoint_ann_indexing_enabled: {
    sync: true,
    description: 'If true, the embeddings search service will trigger ann indexing of checkpoints it sees.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  embeddings_search_use_indexed_checkpoint_cache: {
    sync: true,
    description: 'If true, the embeddings search service will use the indexed checkpoint cache for searches.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  embeddings_search_indexed_checkpoint_quality_sampling_probability: {
    sync: true,
    description: 'Probability of selecting a checkpoint for quality verification. Set to 0.0 to disable',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.0005,
          },
        ],
      },
    },
  },
  team_management: {
    sync: true,
    description: 'If true, the team management page will be enabled.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  team_management_canary_domains: {
    sync: true,
    description: "A comma-separated list of email domains to whitelist for team management canary. Users with email addresses from these domains will have team management enabled, overriding the 'team_management' flag.",
    envs: {
      production: {
        rules: [
          {
            return_value: 'augm.io,turing.com',
          },
        ],
      },
    },
  },
  team_management_subscription_change_blocking_enabled: {
    sync: true,
    description: 'If true, enable blocking of operations when subscription/plan changes are in progress. When disabled, allows operations to proceed even during subscription changes.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_billing_event_ingestion: {
    sync: true,
    description: 'Whether the event ingestion to Orb is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  workingset_persist_to_bigtable: {
    sync: true,
    description: 'Persist workingset internal state to Bigtable',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_remote_agents: {
    sync: true,
    description: 'If true, remote agents APIs are enabled. Used to roll out remote agents incrementally while it is under development. Note that vscode_background_agents_min_version and intellij_background_agents_min_version need to be set to enable the background agents UI.',
    envs: {
      production: {
        rules: apply_return_value(remote_agents_wave0, true) + apply_return_value(remote_agents_wave1, true) + apply_return_value(remote_agents_wave2, true) + apply_return_value(remote_agents_wave3, true) + apply_return_value(remote_agents_wave4, true) + [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            tenant_name: remote_agents_enterprise_tenants,
            return_value: true,
          },
          {
            api_key_user_id: [
              'health-check-1',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  remote_agents_scan_interval_seconds: {
    sync: true,
    description: 'The interval in seconds for scanning remote agents in the background cron task. Controls how frequently the system checks for agents that need to be paused or cleaned up.',
    envs: {
      production: {
        rules: [
          {
            return_value: 300,
          },
        ],
      },
    },
  },
  max_remote_agents_per_user: {
    sync: true,
    description: 'The maximum number of remote agents per user (this includes paused agents).',
    envs: {
      production: {
        rules: [
          {
            return_value: 100,
          },
        ],
      },
    },
  },
  max_active_remote_agents_per_user: {
    sync: true,
    description: 'The maximum number of remote agents per user that can be active at a time.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  remote_agents_enable_auto_pause: {
    sync: true,
    description: 'If true, remote agents will be automatically paused after a period of inactivity.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  remote_agents_enable_auto_delete: {
    sync: true,
    description: 'If true, remote agents will be automatically deleted after a period of inactivity.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  remote_agents_deletion_pending_period_hours: {
    sync: true,
    description: 'The number of hours after which a remote agent is considered inactive (user, agent activity) and will be deleted.',
    envs: {
      production: {
        rules: [
          {
            return_value: 48,
          },
        ],
      },
    },
  },
  remote_agents_auto_pause_soft_ttl_minutes: {
    sync: true,
    description: 'The number of minutes after which a remote agent is considered inactive (user, agent, ssh activity) and will be paused.',
    envs: {
      production: {
        rules: [
          {
            return_value: 15,
          },
        ],
      },
    },
  },
  remote_agents_auto_pause_hard_ttl_minutes: {
    sync: true,
    description: 'The number of minutes after which a remote agent is considered inactive (user, agent activity) and will be paused.',  // 24 hours
    envs: {
      production: {
        rules: [
          {
            return_value: 1440,
          },
        ],
      },
    },
  },
  remote_agents_auto_delete_ttl_days: {
    sync: true,
    description: 'The number of days after which a remote agent is considered inactive (user, agent activity) and will be deleted.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 30,
          },
          {
            return_value: 30,
          },
        ],
      },
    },
  },
  agent_edit_tool_min_view_size: {
    sync: true,
    description: 'Minimum number of lines that agent can read from a file. If it tries to read fewer, we expand the range.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 0,
          },
          {
            return_value: 500,
          },
        ],
      },
    },
  },
  agent_edit_tool_schema_type: {
    sync: true,
    description: 'The schema type of the agent edit tool. Can be StrReplaceEditorToolDefinitionNested or StrReplaceEditorToolDefinitionFlat.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'StrReplaceEditorToolDefinitionFlat',
          },
          {
            return_value: 'StrReplaceEditorToolDefinitionFlat',
          },
        ],
      },
    },
  },
  agent_edit_tool_enable_fuzzy_matching: {
    sync: true,
    description: 'If true, fuzzy matching is enabled in the str-replace-editor-tool. If false, only exact matches are allowed.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_success_message: {
    sync: true,
    description: 'The success message to display when fuzzy matching is used in the str-replace-editor-tool.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'Replacement successful. old_str and new_str were slightly modified to match the original file content.',
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_max_diff: {
    sync: true,
    description: 'Maximum number of differences allowed in fuzzy matching for str-replace-editor-tool',
    envs: {
      production: {
        rules: [
          {
            return_value: 50,
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_max_diff_ratio: {
    sync: true,
    description: 'Maximum ratio of differences to string length allowed in fuzzy matching for str-replace-editor-tool',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.15,
          },
        ],
      },
    },
  },
  agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs: {
    sync: true,
    description: 'Minimum number of consecutive matching symbols required between differences in fuzzy matching',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  agent_save_file_tool_instructions_reminder: {
    sync: true,
    description: 'If true, special `instructions_reminder` field is added to the save-file tool input schema to remind the agent to limit the file content',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'i0',
              'd18',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agent_edit_tool_instructions_reminder: {
    sync: true,
    description: 'If true, special `instructions_reminder` field is added to the str-replace-editor tool input schema to remind the agent to limit the file content',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agent_edit_tool_show_result_snippet: {
    sync: true,
    description: 'If true, the agent edit tool will show result snippets after successful edits',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  agent_edit_tool_max_lines: {
    sync: true,
    description: 'Maximum number of lines for edit tool instructions reminder',
    envs: {
      production: {
        rules: [
          {
            return_value: 150,
          },
        ],
      },
    },
  },
  auth_enable_team_invitation_email: {
    sync: true,
    description: 'If true, the auth central service will send invitation emails for team management.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  chat_server_blocked_domains: {
    sync: true,
    description: 'Comma-separated list of domains that should be blocked from making requests.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'i0',
              'i1',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
              'd5',
              'd6',
              'd7',
              'd8',
              'd9',
              'd10',
              'd11',
              'd12',
              'd13',
              'd14',
              'd15',
              'd16',
              'd17',
              'd18',
              'd19',
              'd20',
            ],
            return_value: |||
              deepmails.org
              fage.asia
              falai.online
              fale.asia
              fanno.fun
              fanss.fun
              feiniao.site
              fengchemail0517.asia
              fengchemail.asia
              fengche.site
              fifaa.fun
              figuree.online
              fivee.site
              futuree.fun
              ggapi.dpdns.org
              gggapi.ggff.net
              gmail.pm
              hotanmi.com
              inctart.com
              ision.us
              justdefinition.com
              kccc.tk
              kenzor.me
              loveu.kg
              mail.xans.me
              mailto.plus
              mix27.tokyo
              mkzaso.com
              nuoxuan.tk
              overhell.me
              oz5j.us
              ptct.net
              redaaa.me
              sunanus.me
              swagpapa.com
              temp.now
              tmpmails.com
              voox.eu.org
              xmyy.shop
              xn--m7rx30aitf.site
              xoxome.dpdns.org
              xoxome.top
              yuanyoupush.com
              dcpa.net
            |||,
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  memories_model: {
    sync: true,
    description: 'Model used in Memories',
    envs: {
      production: {
        rules: [
          {
            return_value: 'gemini-2-flash-001-simple-port',
          },
        ],
      },
    },
  },
  memories_fallback_model: {
    sync: true,
    description: 'Fallback model used in Memories',
    envs: {
      production: {
        rules: [
          {
            return_value: 'gemini-2-flash-001-simple-port',
          },
        ],
      },
    },
  },
  memories_compression_model: {
    sync: true,
    description: 'Model used for Memories compression',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  memories_compression_fallback_model: {
    sync: true,
    description: 'Fallback model used for Memories compression',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  orientation_model: {
    sync: true,
    description: 'Model used in Orientation',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  orientation_fallback_model: {
    sync: true,
    description: 'Fallback model used in Orientation',
    envs: {
      production: {
        rules: [
          {
            return_value: 'claude-sonnet-3-7-simple-c4-p2-chat',
          },
        ],
      },
    },
  },
  api_proxy_chat_agent_daily_limit_enabled: {
    sync: true,
    description: 'If true, the chat agent will have a daily request limit. When a user exceeds this limit, they will receive a message indicating they have reached their limit for the day.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_chat_agent_daily_limit_message: {
    sync: true,
    description: 'The message shown to users when they reach their daily chat agent request limit.',
    envs: {
      production: {
        rules: [
          {
            return_value: "We're currently experiencing high system volume. To ensure service quality for all users, we've temporarily paused your Agent access. We apologize for the inconvenience. Agent access will resume at midnight UTC. [Fair Use Policy](http://www.augmentcode.com/terms-of-service/fair-use)",
          },
        ],
      },
    },
  },
  api_proxy_chat_agent_daily_limit_max_requests: {
    sync: true,
    description: 'The maximum number of chat agent requests a user can make per day. This limit resets at midnight UTC.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: 1000,
          },
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  api_proxy_chat_remote_agent_daily_limit_enabled: {
    sync: true,
    description: 'If true, the chat agent will have a daily request limit. When a user exceeds this limit, they will receive a message indicating they have reached their limit for the day.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  api_proxy_chat_remote_agent_daily_limit_message: {
    sync: true,
    description: 'The message shown to users when they reach their daily chat agent request limit.',
    envs: {
      production: {
        rules: [
          {
            return_value: "We're currently experiencing high system volume. To ensure service quality for all users, we've temporarily paused your Agent access. We apologize for the inconvenience. Agent access will resume at midnight UTC. [Fair Use Policy](http://www.augmentcode.com/terms-of-service/fair-use)",
          },
        ],
      },
    },
  },
  api_proxy_chat_remote_agent_daily_limit_max_requests: {
    sync: true,
    description: 'The maximum number of chat remote agent requests a user can make per day. This limit resets at midnight UTC.',
    envs: {
      production: {
        rules: [
          {
            namespace: self_serve_namespaces,
            return_value: 1000,
          },
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  misuse_monitor_banned_users_gcs_path: {
    sync: true,
    description: 'Relative path under ban_list/ in the GCS bucket for a CSV file of banned users. The CSV should have columns: opaque_user_id, email, tenant_id. Uses augment-data bucket for prod/staging and augment-data-dev for dev/test.',
    envs: {
      production: {
        rules: [
          {
            return_value: 'malicious_ip_20250426.csv',
          },
        ],
      },
    },
  },

  test_client_segmentation_flag: {
    sync: true,
    description: 'A test flag for new client segmentation of flags',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'intellij',
            min_client_version: '1.70.0',
            max_client_version: '1.80.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: 'vscode',
            min_client_version: '1.0.0',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
            ],
            client: [
              'vim',
              'emacs',
              'neovim',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  vscode_personalities_min_version: {
    sync: true,
    description: 'Sets the minimum version number of VSCode to enable personalities feature. Empty means "disable for all versions". "0.0.0" means "enable for all versions"',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  content_manager_rate_limit_by_user_id: {
    sync: true,
    description: 'If true, the content manager will rate limit by user id instead of tenant id.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  websearch_tool_safety: {
    sync: true,
    description: 'The safety of the web search tool. True means the tool is safe, False means the tool is unsafe.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'maxar',
              'maxar-cmk',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  remote_agent_chat_history_polling_interval_ms: {
    sync: true,
    description: 'The polling interval to use for polling the remote agent chat history',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  remote_agent_list_polling_interval_ms: {
    sync: true,
    description: 'The polling interval to use for polling the remote agent list',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  request_insight_find_missing_enabled: {
    sync: true,
    description: 'If true, the request insight find missing subscriber is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'i1',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  checkpoint_indexer_ack_deadline_s: {
    sync: true,
    description: 'Bigtable ack deadline (seconds) to set when extending the deadline during long running operations; 0 to never extend.',
    envs: {
      production: {
        rules: [
          {
            return_value: 60,
          },
        ],
      },
    },
  },
  vscode_generate_commit_message_min_version: {
    sync: true,
    description: 'The minimum version of the VS Code client to enable generate commit message',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  enable_prompt_enhancer: {
    sync: true,
    description: 'If true, the prompt enhancer button is visible.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  memories_text_editor_enabled: {
    sync: true,
    description: 'If true, the memories text editor is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            user_uuid: [
              // Intercom
              '682d007a-6124-46f3-a6a3-8c430fbc386f',
              'afb5bd2c-9bc6-4f70-9dbd-acb07fbe248b',
              '65dcafac-30e8-4970-a21e-8056e7f43fcf',
              'd0133efe-e915-4a8c-9e2e-27e5beb5087d',
              // Webflow
              '2f893a7b-7a98-4b45-b583-053ae48044e4',
              '15c5b99a-9a8a-4940-867d-1725cfbb4b07',
              '7e688163-efde-4581-87d3-980addbea3e6',
              'c34d6135-b46d-40ff-9834-094029c261cc',
              '7c61fbf4-769f-4d2c-9881-260504ac3696',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  model_registry: {
    sync: true,
    description: 'A mapping of model IDs to display names. This is used to populate the model selection dropdown in the chat UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: '{  "Claude Opus 4": "claude-opus-4-0-200k-v5-c4-p2-agent", "Gemini 2.5 Pro": "gemini2-5-pro-200k-v3-2-c4-p2-agent", "Grok SWE": "grok-swe-200k-v4-c4-p2-agent"}',
          },
          {
            tenant_name: [
              'shv',
            ],
            return_value: '{  "Claude Opus 4": "claude-opus-4-0-200k-v5-c4-p2-agent", "Gemini 2.5 Pro": "gemini2-5-pro-200k-v3-2-c4-p2-agent"}',
          },
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: '{\n  "Model A": "claude-opus-4-0-200k-v5-c4-p2-agent", "Model B": "claude-sonnet-3-7-200k-v3-balanced-c4-p2-agent"\n}',
          },
          {
            return_value: '{}',
          },
        ],
      },
    },
  },
  enable_model_registry: {
    sync: true,
    description: 'If true, the model registry will be enabled and used to populate the model selection dropdown in the chat UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'shv',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_prompt_enhancer_enabled: {
    sync: true,
    description: 'Defines the minimum version of the IntelliJ client to enable the prompt enhancer button.',
    envs: {
      production: {
        rules: [
          {
            min_client_version: '0.207.0',
            client: 'intellij',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_enable_webview_performance_monitoring: {
    sync: true,
    description: 'If true, enable performance monitoring in webviews for the IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_truncate_at_parenthesis: {
    sync: true,
    description: 'If true, the completion host will truncate completions at open parentheses.',
    envs: {
      production: {
        rules: [
          {
            user_uuid: [
              'bbac3382-250e-47e2-99ec-17065ee040c1',  // pranay's dogfood account
            ],
            return_value: true,
          },
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  truncate_at_parenthesis_randomize: {
    sync: true,
    description: 'If true, the completion host will 50% of the time truncate completions at open parentheses IF the enable_truncate_at_parenthesis flag is also true.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'dogfood-shard',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  open_file_manager_v2_enabled: {
    sync: true,
    description: 'If true, the open file manager v2 is enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'aitutor-turing',
              'aitutor-mercor',
              'i0',
              'i1',
            ],
            min_client_version: '0.467.0',
            client: 'vscode',
            return_value: true,
          },
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            min_client_version: '0.450.0',
            client: 'vscode',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  publish_user_session_events: {
    sync: true,
    description: 'If true, user session start and end events will be published to Request Insight.',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_enable_sentry: {
    sync: true,
    description: 'If true, enable Sentry error reporting in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: true,
          },
          {
            // Only enable Sentry for dogfood right now
            return_value: false,
          },
        ],
      },
    },
  },
  intellij_webview_error_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for webview errors in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 1.0,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  beachhead_enable_sentry: {
    sync: true,
    description: 'Enable Sentry error reporting in Beachhead service.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  beachhead_error_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for errors in Beachhead service.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: 1.0,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  beachhead_trace_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for traces in Beachhead service.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: 0.01,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  intellij_plugin_error_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for plugin errors in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 1.0,
          },
          {
            return_value: 0.0,
          },
        ],
      },
    },
  },
  intellij_webview_trace_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for webview traces in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 0.5,
          },
          {
            return_value: 0.00,
          },
        ],
      },
    },
  },
  intellij_plugin_trace_sampling_rate: {
    sync: true,
    description: 'Sampling rate (0.0-1.0) for plugin traces in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'aitutor-turing',
              'aitutor-mercor',
            ],
            return_value: 0.5,
          },
          {
            return_value: 0.00,
          },
        ],
      },
    },
  },
  intellij_edt_freeze_detection_enabled: {
    sync: true,
    description: 'Enable logging, collection of thread dumps, and reporting of EDT freezes in IntelliJ plugin.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_agent_auto_mode: {
    sync: true,
    description: 'If true, the agent auto mode is enabled.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: [
              'rubrik',
              'rubrik-cmk',
            ],
            return_value: false,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  vscode_remote_agent_ssh_min_version: {
    sync: true,
    description: 'When SSHing to a remote agent, the minimum version of the Augment extension that will be installed on the remote machine.',
    envs: {
      production: {
        rules: [
          {
            return_value: '0.456.0',
          },
        ],
      },
    },
  },
  enable_completion_session_state: {
    sync: true,
    description: 'If true, completion host will cancel requests via session state.',
    envs: {
      production: {
        rules: [
          {
            return_value: true,
          },
        ],
      },
    },
  },
  enable_completion_retrieval_cancel: {
    sync: true,
    description: 'If true, completion host will allow retrieval to cancel completion requests.',
    envs: {
      production: {
        rules: [
          {
            tenant_name: 'dogfood-shard',
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  grep_search_tool_enable: {
    sync: true,
    description: 'Enable the grep search tool for agents.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  grep_search_tool_timelimit_sec: {
    sync: true,
    description: 'Time limit for grep search tool execution in seconds.',
    envs: {
      production: {
        rules: [
          {
            return_value: 10,
          },
        ],
      },
    },
  },
  chat_server_enable_error_details_metadata: {
    sync: true,
    description: 'Enable error details metadata in chat server gRPC responses.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'i1',
              'i0',
              'd0',
              'd1',
              'd2',
              'd3',
              'd4',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  grep_search_tool_output_chars_limit: {
    sync: true,
    description: 'Character limit for grep search tool output.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  grep_search_tool_num_context_lines: {
    sync: true,
    description: 'Number of context lines to include before and after each match in grep search tool output.',
    envs: {
      production: {
        rules: [
          {
            return_value: 5,
          },
        ],
      },
    },
  },
  api_proxy_chat_heartbeat_stream: {
    sync: true,
    description: 'If true, enable sending heartbeats over chat-stream when no other data is being sent.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
              'i1',
            ],
            return_value: true,
          },
          {
            return_value: true,
          },
        ],
      },
    },
  },
  agent_report_streamed_chat_every_chunk: {
    sync: true,
    description: 'How often to report the chat history when streaming (number of chunks)',
    envs: {
      production: {
        rules: [
          {
            return_value: 3,
          },
        ],
      },
    },
  },
  agent_max_total_changed_files_size_bytes: {
    sync: true,
    description: 'Max total size of changed files to send in a single chat update (bytes)',
    envs: {
      production: {
        rules: [
          {
            return_value: 2 * 1024 * 1024,
          },
        ],
      },
    },
  },
  agent_max_changed_files_skipped_paths: {
    sync: true,
    description: 'Max number of skipped paths to send when the total changed files size is exceeded',
    envs: {
      production: {
        rules: [
          {
            return_value: 1000,
          },
        ],
      },
    },
  },
  agent_idle_status_update_interval_ms: {
    sync: true,
    description: 'How often to update the agent status when idle (milliseconds)',
    envs: {
      production: {
        rules: [
          {
            return_value: 60 * 1000,
          },
        ],
      },
    },
  },
  agent_max_iterations: {
    sync: true,
    description: 'Maximum number of iterations in a single turn for the agent loop',
    envs: {
      production: {
        rules: [
          {
            return_value: 100,
          },
        ],
      },
    },
  },
  agent_ssh_connection_check_interval_ms: {
    sync: true,
    description: 'Agent SSH connection check interval (milliseconds)',
    envs: {
      production: {
        rules: [
          {
            return_value: 5000,
          },
        ],
      },
    },
  },
  agent_ssh_connection_check_log_interval_ms: {
    sync: true,
    description: 'Agent SSH connection check log interval (milliseconds)',
    envs: {
      production: {
        rules: [
          {
            return_value: 5 * 60 * 1000,
          },
        ],
      },
    },
  },
  intellij_indexing_v3_enabled: {
    sync: true,
    description: 'If true, the intellij client will use indexing v3.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  history_summary_min_version: {
    sync: true,
    description: 'Sets the minimum version number to enable history summarization in chat conversations. Empty means "disable for all versions". "0.0.0" means "enable for all versions".',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: '0.0.0',
          },
          {
            return_value: '',
          },
        ],
      },
    },
  },
  history_summary_max_chars: {
    sync: true,
    description: 'Maximum expected characters before history summarization is triggered',
    envs: {
      production: {
        rules: [
          {
            return_value: 200000,
          },
        ],
      },
    },
  },
  history_summary_lower_chars: {
    sync: true,
    description: 'Lower threshold for history summarization',
    envs: {
      production: {
        rules: [
          {
            return_value: 80000,
          },
        ],
      },
    },
  },
  auth_central_windsurf_promotion_enabled: {
    sync: true,
    description: 'If true, auth central endpoints to get and process the Windsurf promotion will be enabled.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  history_summary_prompt: {
    sync: true,
    description: 'Custom prompt for conversation history summarization',
    envs: {
      production: {
        rules: [
          {
            return_value: |||
              Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
              This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.

              Your summary should be structured as follows:
              Context: The context to continue the conversation with. If applicable based on the current task, this should include:
              1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.
              2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.
              3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.
              4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.
              5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.
              6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.

              Example summary structure:
              1. Previous Conversation:
              [Detailed description]
              2. Current Work:
              [Detailed description]
              3. Key Technical Concepts:
              - [Concept 1]
              - [Concept 2]
              - [...]
              4. Relevant Files and Code:
              - [File Name 1]
                  - [Summary of why this file is important]
                  - [Summary of the changes made to this file, if any]
                  - [Important Code Snippet]
              - [File Name 2]
                  - [Important Code Snippet]
              - [...]
              5. Problem Solving:
              [Detailed description]
              6. Pending Tasks and Next Steps:
              - [Task 1 details & next steps]
              - [Task 2 details & next steps]
              - [...]

              Output only the summary of the conversation so far, without any additional commentary or explanation.
            |||,
          },
        ],
      },
    },
  },
  customer_ui_windsurf_promotion_enabled: {
    sync: true,
    description: 'If true, the Windsurf promotion page will be enabled on the customer UI. If false, the page will not be accessible.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  enable_spawn_sub_agent_tool: {
    sync: true,
    description: 'Enable the SpawnSubAgentTool for creating remote sub-agents',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
              'shv-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  customer_ui_content_deletion_enabled: {
    sync: true,
    description: 'If true, customer UI shows users a button to delete their indexed code',
    envs: {
      production: {
        rules: [
          {
            return_value: false,
          },
        ],
      },
    },
  },
  auth_central_promotion_recaptcha_threshold: {
    sync: true,
    description: 'reCAPTCHA threshold for promotions. If the score is below this threshold, the promotion will not be granted.',
    envs: {
      production: {
        rules: [
          {
            return_value: 0.5,
          },
        ],
      },
    },
  },
  enable_commit_indexing: {
    sync: true,
    description: 'If true, enables git commit indexing functionality in the client',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: false,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
  max_commits_to_index: {
    sync: true,
    description: 'Maximum number of commits to index for git commit indexing',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'staging-shard-0',
            ],
            return_value: 1000,
          },
          {
            return_value: 0,
          },
        ],
      },
    },
  },
  customer_ui_enable_user_feature_stats: {
    sync: true,
    description: 'If true, the user feature stats are enabled in customer UI.',
    envs: {
      production: {
        rules: [
          {
            namespace: [
              'central-staging',
            ],
            return_value: true,
          },
          {
            return_value: false,
          },
        ],
      },
    },
  },
}

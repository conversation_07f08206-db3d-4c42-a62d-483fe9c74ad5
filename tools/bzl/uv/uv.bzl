load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_file")

_UV_VERSION = "0.7.13"
_UV_SHA256 = "909278eb197c5ed0e9b5f16317d1255270d1f9ea4196e7179ce934d48c4c2545"

def _uv_extension_impl(mctx):
    http_file(
        name = "uv",
        url = "https://github.com/astral-sh/uv/releases/download/{}/uv-x86_64-unknown-linux-gnu.tar.gz".format(_UV_VERSION),
        sha256 = _UV_SHA256,
        downloaded_file_path = "uv-x86_64-unknown-linux-gnu.tar.gz",
    )

uv_extension = module_extension(implementation = _uv_extension_impl)

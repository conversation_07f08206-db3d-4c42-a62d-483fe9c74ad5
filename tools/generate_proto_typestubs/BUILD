# DO NOT EDIT: auto-generated by update_build.sh.
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("//tools/bzl:python.bzl", "py_test")

sh_binary(
    name = "generate_proto_typestubs",
    srcs = ["copy_proto_typestubs.sh"],
    data = [
        "//base/blob_names:blob_names_py_proto",
        "//base/diff_utils:edit_events_py_proto",
        "//base/error_details:error_details_py_proto",
        "//base/proto:tensor_py_proto",
        "//base/proto/services:access_py_proto",
        "//base/static_analysis:signature_py_proto",
        "//services/agents:agents_py_proto",
        "//services/api_proxy:model_finder_py_proto",
        "//services/api_proxy:public_api_py_proto",
        "//services/auth/central/server:auth_entities_py_proto",
        "//services/auth/central/server:auth_py_proto",
        "//services/auth/central/server:front_end_token_service_py_proto",
        "//services/auth/query:auth_query_py_proto",
        "//services/bigtable_proxy:bigtable_proxy_py_proto",
        "//services/chat_host:chat_proto_py_proto",
        "//services/completion_host:completion_proto_py_proto",
        "//services/content_manager:content_manager_py_proto",
        "//services/deploy/configs:repo_model_config_py_proto",
        "//services/deploy/model_instance:model_instance_py_proto",
        "//services/edit_host:edit_proto_py_proto",
        "//services/embedder_host:embedder_py_proto",
        "//services/embeddings_indexer:chunk_py_proto",
        "//services/embeddings_search_host:embeddings_search_py_proto",
        "//services/examples:route_guide_py_proto",
        "//services/gcs_proxy:gcs_proxy_py_proto",
        "//services/grpc_debug:grpc_debug_py_proto",
        "//services/inference_host:infer_py_proto",
        "//services/integrations/atlassian:atlassian_py_proto",
        "//services/integrations/docset:docset_py_proto",
        "//services/integrations/github:github_event_py_proto",
        "//services/integrations/github/processor:processor_py_proto",
        "//services/integrations/github/state:github_state_py_proto",
        "//services/integrations/glean:glean_py_proto",
        "//services/integrations/linear:linear_py_proto",
        "//services/integrations/notion:notion_py_proto",
        "//services/integrations/slack_bot:slack_event_py_proto",
        "//services/integrations/slack_bot/processor:processor_py_proto",
        "//services/integrations/slack_bot/webhook:slack_webhook_py_proto",
        "//services/integrations/supabase:supabase_py_proto",
        "//services/lib/grpc/stream_mux:stream_mux_py_proto",
        "//services/lib/proto:chat_py_proto",
        "//services/memstore:memstore_py_proto",
        "//services/next_edit_host:next_edit_proto_py_proto",
        "//services/ping_pong:ping_pong_py_proto",
        "//services/remote_agents:remote_agents_py_proto",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/central:request_insight_central_py_proto",
        "//services/reranker:reranker_py_proto",
        "//services/settings:settings_py_proto",
        "//services/share:share_proto_py_proto",
        "//services/tenant_watcher:tenant_watcher_py_proto",
        "//services/third_party_arbiter:third_party_arbiter_py_proto",
        "//services/thirdparty_proxy:thirdparty_proxy_py_proto",
        "//services/token_exchange:token_exchange_py_proto",
        "//services/working_set:working_set_py_proto",
        "//tools/bazel_runner/bep_parser:test_summary_py_proto",
        "//tools/bazel_runner/control:bazel_runner_py_proto",
        "//tools/bazel_runner/git:checkout_py_proto",
        "//tools/bazel_runner/github_webhook:github_py_proto",
        "//tools/bazel_runner/server:bazel_runner_store_py_proto",
        "//tools/bazel_runner/server:test_runner_py_proto",
        "//tools/bazel_runner/test_selection_server:test_selection_py_proto",
        "//tools/bazel_runner/test_selection_server:test_selection_store_py_proto",
        "//tools/bot:bot_py_proto",
        "//tools/deploy_runner:deploy_events_py_proto",
        "//tools/deploy_runner:metadata_py_proto",
        "//tools/deploy_runner/server:deploy_py_proto",
        "//tools/deploy_runner/server:deploy_store_py_proto",
        "//tools/feature_flags/syncer:syncer_py_proto",
        "//tools/load_test:load_test_py_proto",
    ],
)

sh_binary(
    name = "clean",
    srcs = ["//base:clean.sh"],
    data = [
        "//base:install_lib",
        "//tools/generate_proto_typestubs",
    ],
)

sh_binary(
    name = "generate_go_proto_stubs",
    srcs = ["generate_go_proto_stubs.sh"],
)

build_test(
    name = "generate_proto_typestubs_build_test",
    targets = [
        ":generate_proto_typestubs",
    ],
)

py_test(
    name = "validate_target_list_test",
    srcs = ["validate_target_list_test.py"],
    data = ["//:.shellcheckrc"],
    env = {
        "FILE": "$(location //:.shellcheckrc)",
    },
    tags = [
        "external",
        "no-cache",
        "no-sandbox",
    ],
)

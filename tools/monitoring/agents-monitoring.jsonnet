local agentMonitoringLib = import 'agents-monitoring-lib.jsonnet';

function(cloud)
  // Select namespace based on cloud
  local namespace =
    if cloud == 'GCP_AGENT_US_CENTRAL1_PROD' then 'cc-gcp-prod-agent0'
    else if cloud == 'GCP_AGENT_EU_WEST4_PROD' then 'cc-gcp-eu-w4-prod-agent0'
    else error 'Unsupported cloud for agent monitoring: ' + cloud;

  local pendingPodsSpec = {
    displayName: 'High number of non-running pods in agent cluster',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        (sum by (cluster) (kube_pod_status_phase{pod=~"raws-.*"}) -
        sum by (cluster) (kube_pod_status_phase{phase="Running", pod=~"raws-.*"})) > 10
      |||,
    },
  };

  local haproxyCountSpec = {
    displayName: 'Low HAProxy container count',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        count by (location) (
          kube_pod_status_phase{phase="Running", pod=~"ws-ssh-haproxy.*"}
        ) < 3
      |||,
    },
  };

  local beachheadFailureWarningSpec = {
    displayName: 'High beachhead failure rate (warning)',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (location) (logging_googleapis_com:user_remote_agent_beachhead_failure{monitored_resource="k8s_container"}) > 5
      |||,
    },
  };

  local beachheadFailureCriticalSpec = {
    displayName: 'Critical beachhead failure rate',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        sum by (location) (logging_googleapis_com:user_remote_agent_beachhead_failure{monitored_resource="k8s_container"}) > 15
      |||,
    },
  };

  local loggingEscapesSpec = {
    displayName: 'Remote agent logging escapes detected',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (location) (logging_googleapis_com:user_remote_agent_logging_escapes{monitored_resource="k8s_container"}) > 0
      |||,
    },
  };

  local longPendingRawsPodsSpec = {
    displayName: 'Agent Workspace Pods pending for too long',
    conditionPrometheusQueryLanguage: {
      duration: '600s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        kube_pod_status_phase{phase="Pending", pod=~"raws-.*"} >= 1
      |||,
    },
  };

  local podRestartWarningSpec = {
    displayName: 'Agent pods restarting frequently',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        kubernetes_io:container_restart_count{pod_name=~"raws-.*"} -
        kubernetes_io:container_restart_count{pod_name=~"raws-.*"} offset 1h > 2
      |||,
    },
  };

  [
    agentMonitoringLib.alertPolicy(
      cloud,
      pendingPodsSpec,
      'pending-pods-alert',
      'More than 10 pods are pending in cluster ${cluster} for the last 5+ minutes.',
      team='remote-agents',
      namespace=namespace
    ),
    agentMonitoringLib.alertPolicy(
      cloud,
      haproxyCountSpec,
      'haproxy-container-count-alert',
      'Fewer than 3 HAProxy containers are running for more than 5 minutes in ${location}.',
      team='remote-agents',
      namespace=namespace
    ),
    agentMonitoringLib.alertPolicy(
      cloud,
      beachheadFailureWarningSpec,
      'beachhead-failure-warning-alert',
      'Beachhead failure rate is above 5 for the last 5+ minutes.',
      team='remote-agents',
      namespace=namespace
    ),
    agentMonitoringLib.alertPolicy(
      cloud,
      beachheadFailureCriticalSpec,
      'beachhead-failure-critical-alert',
      'Beachhead failure rate is critically high (above 15) for the last 5+ minutes.',
      team='remote-agents',
      namespace=namespace
    ),
    agentMonitoringLib.alertPolicy(
      cloud,
      loggingEscapesSpec,
      'logging-escapes-alert',
      'Remote agent logging escapes detected in ${location}. This indicates potential security or data leakage issues.',
      team='remote-agents',
      namespace=namespace
    ),
    agentMonitoringLib.alertPolicy(
      cloud,
      longPendingRawsPodsSpec,
      'long-pending-raws-pods-alert',
      'Agent Workspace Pods have been in Pending state for more than 10 minutes in cluster ${cluster}.',
      team='remote-agents',
      namespace=namespace
    ),
    agentMonitoringLib.alertPolicy(
      cloud,
      podRestartWarningSpec,
      'pod-restart-warning-alert',
      'Agent pod ${pod} in cluster ${cluster} is restarting frequently.',
      team='remote-agents',
      namespace=namespace
    ),
  ]

local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

// Notification channels for agent-sandbox-prod project
local agentOncallSlackNotification = {
  external: 'projects/agent-sandbox-prod/notificationChannels/14962542865073070043',
};

local agentPagerDutyNotificationByTeam = {
  'remote-agents': {
    external: 'projects/agent-sandbox-prod/notificationChannels/14962542865073070043',
  },
  default: {
    external: 'projects/agent-sandbox-prod/notificationChannels/14962542865073070043',
  },
};

/*
Sample condition:
{
  displayName: 'API Proxy Health',
  conditionPrometheusQueryLanguage: {
    duration: '0s',
    evaluationInterval: '60s',
    labels: { severity: 'warning' },
    query: 'sum by (namespace) (increase(api_proxy_health_check_status_total{status="success"}[%sm])) / sum by (namespace)(increase(api_proxy_health_check_status_total{}[%sm])) < 0.9' % [minutes, minutes],
  },
}
*/

// Custom alertPolicy function for agent clusters
// Unlike the main monitoring lib, this doesn't check for lead clusters since agent clusters
// are in their own project (agent-sandbox-prod)
local alertPolicy(cloud, condition, name, description, team='remote-agents', enableInDev=false, namespace=null) =
  // Supported severities so far are _info_, _warning_, and _error_.
  // _info_ will not ping, good choice for experimental or low-signal alerts
  // _warning_ will ping remote-agents slack only
  // _error_ will also create a pagerduty incident
  assert std.objectHas(condition.conditionPrometheusQueryLanguage.labels, 'severity');
  local pdNotification = agentPagerDutyNotificationByTeam[team];

  // For agent clusters, we always enable alerts since they're production workloads
  // Filter out null notification channels
  local allNotificationChannels =
    if condition.conditionPrometheusQueryLanguage.labels.severity == 'error' then [agentOncallSlackNotification, pdNotification]
    else if condition.conditionPrometheusQueryLanguage.labels.severity == 'warning' then [pdNotification]
    else if condition.conditionPrometheusQueryLanguage.labels.severity == 'info' then []
    else
      assert false : 'Unsupported severity %s' % condition.conditionPrometheusQueryLanguage.labels.severity;
      [];
  local notificationChannels = std.filter(function(ch) ch != null, allNotificationChannels);

  {
    apiVersion: 'monitoring.cnrm.cloud.google.com/v1beta1',
    kind: 'MonitoringAlertPolicy',
    metadata: {
      name: name,
      // Add namespace if provided
      [if namespace != null then 'namespace']: namespace,
      // TODO: understand where these vs the condition labels show up in GCP
      labels: condition.conditionPrometheusQueryLanguage.labels,
    },
    spec: {
      alertStrategy: {
        autoClose: '604800s',  // 1 week
      },
      // Treat this as a no-op unary OR since promQL lets you compose conditions arbitrarily
      combiner: 'OR',
      conditions: [
        condition,
      ],
      displayName: condition.displayName,
      enabled: true,  // Always enabled for agent clusters
      notificationChannels: notificationChannels,
      documentation: {
        content: description,
        mimeType: 'text/markdown',
      },
    },
  };

{
  alertPolicy: alertPolicy,
  label: monitoringLib.label,
}

syntax = "proto3";
package devtools_bot;

import "base/proto/services/access.proto";

service DevtoolsBot {
  rpc NotifyDeploymentFailed(NotifyDeploymentRequest) returns (NotifyDeploymentResponse) {}
  rpc NotifyPostmergeTestFailed(NotifyPostMergeTestingFailedRequest) returns (NotifyPostMergeTestingFailedResponse) {}

  // NotifyLastKnownGoodUpdated is used to notify the engineers when the last known good was
  // updated
  rpc NotifyLastKnownGoodUpdated(NotifyLastKnownGoodUpdatedRequest) returns (NotifyLastKnownGoodUpdatedResponse) {}

  rpc NotifyAdhocTestFinished(NotifyAdhocTestFinishedRequest) returns (NotifyAdhocTestFinishedResponse) {}

  // send a message when a deployment finished
  rpc NotifyDeploymentFinished(NotifyDeploymentFinishedRequest) returns (NotifyDeploymentFinishedResponse) {}

  // send a message when an adhoc deployment was requested
  rpc NotifyAdhocDeployment(NotifyAdhocDeploymentRequest) returns (NotifyAdhocDeploymentResponse) {}

  // Two-key tenant access approval flow:
  // - user 1 (the "proposer") starts the flow: send a slack message with link to approval page
  rpc NotifyAccessProposed(NotifyAccessProposedRequest) returns (NotifyAccessProposedResponse) {}
  // - user 2 (the "approver") approves the access: send a second slack message with the approval
  rpc NotifyAccessApproved(NotifyAccessApprovedRequest) returns (NotifyAccessApprovedResponse) {}

  // send a message when a user provides feedback
  rpc NotifyUserFeedback(NotifyUserFeedbackRequest) returns (NotifyUserFeedbackResponse) {}

  // send a message when a scheduled test run is completed
  rpc NotifyScheduledTestResult(NotifyScheduledTestResultRequest) returns (NotifyScheduledTestResultResponse) {}
}

enum DeploymentStatus {
  UNKNOWN = 0;
  SUCCESS = 1;
  FAILED = 2;
  SKIPPED = 3;
}

message Deployment {
  // fully qualifiedname of the deployment target (see METADATA.jsonnet)
  // example names are:
  // <namespace>/<deployment name>@<cloud> for kubecfg targets
  // <deployment_name> for bazel targets
  string name = 1;

  // namespace of the deployment (might be empty)
  string namespace = 2;

  // the target cloud of the deployment
  string cloud = 4;

  // status of the deployment
  DeploymentStatus status = 3;
}

message NotifyDeploymentFinishedRequest {
  reserved 1, 2;

  // the commit that was deployed
  string commit = 3;
  string commit_url = 4;
  string details_url = 5;

  // information about all deployments
  repeated Deployment deployments = 6;

  // the id of the deployment
  string deploy_id = 7;

  // the name of the deployment schedule that triggered the deployment
  string deployment_schedule_name = 8;
}

message NotifyDeploymentFinishedResponse {}

message NotifyDeploymentRequest {
  string name = 1;
  string commit = 2;
  string commit_url = 3;
  string details_url = 4;

  // the namespace the deployment was deployed to
  string namespace = 5;

  // the cloud of the deployment
  string cloud = 6;

  reserved 7, 8;
}

message NotifyDeploymentResponse {}

message BreakageInfo {
  string run_id = 1;
  string run_url = 2;
  Commit commit = 3;
}

message FailedTest {
  string name = 1;
  optional BreakageInfo broken_since = 2;
}

// NotifyPostMergeTestingFailedRequest is used to notify the engineers when a post-merge test failed
// failed here means any non-successful status, e.g. FAILED, TIMEOUT, etc.
message NotifyPostMergeTestingFailedRequest {
  reserved 1, 2;

  // the url to the details of the failure (e.g. a linkt to the CI job)
  string details_url = 3;

  // list of all test targets that did not succeed with a success status
  // deprecated, use failedTests instead
  repeated string non_success_test_targets = 4;

  // list of all test targets that failed
  repeated FailedTest failed_test_targets = 9;

  // list of all test targets that were flaky
  repeated string flaky_test_targets = 7;

  // the commit that was tested
  Commit test_commit = 5;

  // all commits that were included in the test that are not known to be good
  repeated Commit included_commits = 6;

  // the augmentcode.com email address of the user who commit the test commit (if available)
  // the user will receive a CC copy of the message.
  string commit_email = 8;

  // whether the test was cancelled
  bool is_cancelled = 10;
}

message NotifyPostMergeTestingFailedResponse {}

message Commit {
  string sha = 1;
  string commit_url = 2;
  string commit_message = 3;
  string author_name = 4;
  string author_email = 5;
  string repo_name = 6;
  string repo_owner = 7;
}

message NotifyLastKnownGoodUpdatedRequest {
  // the new last known good commit
  Commit last_known_good_commit = 1;

  // the list of commits that were included in the last known good commit
  // compared to the last known good commit.
  //
  // this includes the last known good commit itself.
  repeated Commit included_commits = 2;
}

message NotifyLastKnownGoodUpdatedResponse {}

message NotifyAccessProposedRequest {
  access.AccessType access = 1;
  string proposer = 2;
  string reason = 3;
  optional string approval_url = 4;
  string cluster = 5;
}

message NotifyAccessProposedResponse {}

message NotifyAccessApprovedRequest {
  access.AccessType access = 1;
  string proposer = 2;
  string reason = 3;
  string approver = 4;
  string cluster = 5;
}

message NotifyAccessApprovedResponse {}

message NotifyAdhocTestFinishedRequest {
  // the id of the test run
  string run_id = 1;

  // the status of the test run
  string status = 2;

  // the url to the details of the failure (e.g. a linkt to the CI job)
  string details_url = 3;

  // list of all test targets that did not succeed with a success status
  repeated string non_success_test_targets = 4;

  // list of all test targets that were flaky
  repeated string flaky_test_targets = 6;

  // the name of the user who triggered the test
  string user_email = 5;
}

message NotifyAdhocTestFinishedResponse {}

message NotifyAdhocDeploymentRequest {
  // the branch to deploy
  string branch = 1;

  // the commit reference to deploy
  // if empty, it means the latest commit on the branch will be used
  string commit_ref = 2;

  // the clouds to deploy to
  repeated string clouds = 3;

  // the namespaces to deploy to
  repeated string namespaces = 5;

  // the targets to deploy
  repeated string target_names = 6;

  // the name of the user who requested the deployment
  string requestor = 7;

  // the reason for the deployment.
  // the reason should describe the valid business reason, e.g. that an outage is expected
  // to be solved by the deployment
  string reason = 8;

  // the id of the deployment
  string deploy_id = 9;

  // the url to the details of the deployment
  string deploy_url = 10;
}

message NotifyAdhocDeploymentResponse {}

message NotifyUserFeedbackRequest {
  // the id of the slack channel to post the feedback to (e.g. C1234567890). we
  // pass the channel id rather than pulling it from the config because this is
  // a generic api for feedback from completions/chat/next edit, each of which
  // has a dedicated feedback channel.
  string channel_id = 1;

  // the name of the tenant where the feedback was submitted
  string tenant_name = 2;

  // the id of the original request the feedback was submitted for
  string original_request_id = 3;

  // the url to the details of the original request (e.g. link to the support page)
  string details_url = 4;

  // the overall rating of the feedback
  string rating = 5;

  // a note attached to the feedback
  string note = 6;

  // the url to the genie page to submit a permission request
  string genie_url = 7;

  // the user agent of the caller who submitted the feedback (not sanitized any more than note)
  string user_agent = 8;
}

message NotifyUserFeedbackResponse {}

message NotifyScheduledTestResultRequest {
  // the id of the scheduled test run
  string run_id = 1;

  // the name of the scheduled test job
  string schedule_name = 2;

  // the status of the test run (e.g., "Passed", "Failed", "Cancelled")
  string status = 3;

  // the url to the details of the test run
  string details_url = 4;

  // list of all test targets that did not succeed
  repeated string non_success_test_targets = 5;

  // list of all test targets that were flaky
  repeated string flaky_test_targets = 6;

  // the date when the test was scheduled
  string schedule_date = 7;
}

message NotifyScheduledTestResultResponse {}

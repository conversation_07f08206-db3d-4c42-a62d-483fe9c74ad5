// K8S deployment file for the github webhook
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local engLib = import 'deploy/common/eng.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local githubAppSealedLib = import 'tools/deploy/github_readonly_app_sealed.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'quick-deploy';
  local githubSecret = githubAppSealedLib(cloud=cloud, namespace=namespace, appName=appName);
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local tolerations = nodeLib.tolerations(resource='prohibitGpu', env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource='prohibitGpu', env=env, cloud=cloud, appName=appName);

  // give PROD deploy job account deploy-role permissions in every cluster.
  local crossClusterRoles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: '%s-quick-deploy-role-binding' % namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        // always give the deploy service account of the prod project permission to deploy
        {
          kind: 'User',
          name: '%<EMAIL>' % serviceAccount.iamServiceAccountName,
        },
      ] + if cloudInfo.isDevCluster(cloud) then [
        // always give the deploy service account of the dev project permission to deploy
        {
          kind: 'User',
          name: '%<EMAIL>' % serviceAccount.iamServiceAccountName,
        },
      ] else [],
      roleRef: {
        kind: 'ClusterRole',
        name: 'quick-deploy-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
    if cloud == 'GCP_US_CENTRAL1_DEV' && namespace == 'devtools' then
      // give access to the bazel data bucket
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'prod-quick-deploy-sa-bazel-data-bucket-grant',
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: 'augment-bazel-data',
          },
          bindings: [
            {
              role: 'roles/storage.objectViewer',
              members: [
                {
                  member: 'serviceAccount:%<EMAIL>' % serviceAccount.iamServiceAccountName,
                },
              ],
            },
          ],
        },
      },
  ];

  if (!cloudInfo.isProdCluster(cloud) && env == 'DEV') || cloud == 'GCP_US_CENTRAL1_PROD' then
    local githubSubObjects = [
      {
        apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
        kind: 'PubSubTopic',
        metadata: {
          name: 'github-%s-%s-deadletter' % [namespace, appName],
          namespace: namespace,
          // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
          // topic is deleted before the pod.
          annotations: if env == 'DEV' then {
            'cnrm.cloud.google.com/deletion-policy': 'abandon',
          } else {},
          labels: {
            app: appName,
          },
        },
        spec: {
          messageRetentionDuration: '%ss' % (60 * 60 * 24),
        },
      },
      {
        apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
        kind: 'PubSubSubscription',
        metadata: {
          name: 'github-%s-%s-sub' % [namespace, appName],
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          topicRef: {
            name: 'github-%s-topic' % namespace,
          },
          deadLetterPolicy: {
            deadLetterTopicRef: {
              name: 'github-%s-%s-deadletter' % [namespace, appName],
            },
            maxDeliveryAttempts: 5,
          },
          // FIFO ordering.
          enableMessageOrdering: true,
          ackDeadlineSeconds: 300,
          retryPolicy: {
            minimumBackoff: '5s',
            maximumBackoff: '300s',
          },
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'github-%s-sub-policy' % namespace,
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          resourceRef: {
            kind: 'PubSubSubscription',
            name: 'github-%s-%s-sub' % [namespace, appName],
          },
          bindings: [
            {
              role: 'roles/pubsub.subscriber',
              members: [
                {
                  memberFrom: {
                    serviceAccountRef: {
                      name: serviceAccount.iamServiceAccountName,
                    },
                  },
                },
              ],
            },
          ],
        },
      },
    ];
    local userName = std.substr(namespace, 4, std.length(namespace));
    local config =
      local findGithubUser = function(username) local githubName = [e.github for e in engLib if e.username == username];
                                                if std.length(githubName) == 0 then username else githubName[0];
      local buildUserPattern = function(users) std.join('|', std.map(function(u) '^%s$' % u, std.map(findGithubUser, users)));
      {
        GCP_US_CENTRAL1_DEV: {
          project_id: 'system-services-dev',
          region: 'us-central1',
          github_app_path: '/github-app',
          github_subscription: 'github-%s-%s-sub' % [namespace, appName],
          github_ack_deadline: 300,
          base_directory: '/cache',
          repo_owner: 'augmentcode',
          repo_name: 'augment',
          slack_bot_endpoint: 'slack-bot-svc:80',
          source_branch: 'dirk-10001000-quick-deploy-2',  //userName + '-main',
          pusher_filter: if env == 'DEV' then buildUserPattern([userName]) else '',
          cloud_filter: cloud,
          env_filter: '',
          extra_startup_args: '--output_user_root=/cache/bazel-root --host_jvm_args=-Xmx%sg' % 7,
          extra_args: '--disk_cache=/cache/.cache/bazel-diskcache --curses=no --noshow_loading_progress --remote_cache=grpc://bazel-cache-3.devtools:9092',
        },
        GCP_US_CENTRAL1_PROD: {
          project_id: 'system-services-prod',
          region: 'us-central1',
          github_app_path: '/github-app',
          github_subscription: 'github-%s-%s-sub' % [namespace, appName],
          github_ack_deadline: 300,
          base_directory: '/cache',
          slack_bot_endpoint: 'slack-bot-svc:80',
          source_branch: 'main',
          repo_owner: 'augmentcode',
          repo_name: 'augment',
          pusher_filter: '',
          cloud_filter: '',
          env_filter: '',
          extra_startup_args: '--output_user_root=/cache/bazel-root --host_jvm_args=-Xmx%sg' % 7,
          extra_args: '--disk_cache=/cache/.cache/bazel-diskcache --curses=no --noshow_loading_progress --remote_cache=grpc://bazel-cache-3.devtools:9092',
        },
      }[cloud];
    local configMap =

      {
        apiVersion: 'v1',
        kind: 'ConfigMap',
        metadata: {
          name: '%s-config' % appName,
          namespace: namespace,
          annotations: {
            'reloader.stakater.com/match': 'true',
          },
          labels: {
            app: appName,
          },
        },
        data:

          {
            'config.json': std.manifestJson(config),
          },
      };
    local container =
      {
        name: appName,
        target: {
          name: '//tools/deploy_runner/quick_deploy:image',
          dst: 'quick-deploy-image',
        },
        volumeMounts: [
          {
            mountPath: '/dev/shm',
            name: 'dshm',
          },
          {
            name: 'config',
            mountPath: '/config',
            readOnly: true,
          },
          {
            name: 'cache-volume',
            mountPath: '/cache',
          },
          {
            mountPath: '/github-app',
            name: 'github-app-secret',
          },
        ],
        env: [
          {
            name: 'POD_NAME',
            valueFrom: {
              fieldRef: {
                fieldPath: 'metadata.name',
              },
            },
          },
          {
            name: 'POD_NAMESPACE',
            valueFrom: {
              fieldRef: {
                fieldPath: 'metadata.namespace',
              },
            },
          },
          {
            name: 'BAZEL_BUILD_USER',
            value: 'deploy',
          },
          {
            name: 'XDG_CACHE_HOME',
            value: '/cache/.cache',
          },
          {
            name: 'AU_GPU_COUNT',
            value: '0',
          },
        ],
        resources: {
          limits: {
            cpu: 7,
            memory: '28Gi',
          },
        },
      };
    local volume = {
      apiVersion: 'v1',
      kind: 'PersistentVolumeClaim',
      metadata: {
        name: 'quick-deploy-pvc',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        accessModes: [
          'ReadWriteOnce',
        ],
        storageClassName: 'premium-rwo',
        resources: {
          requests: {
            storage: '512Gi',
          },
        },
      },
    };
    // minAvailable=1 means for the deploy job that it should not be preempted by other workloads
    // see https://kubernetes.io/docs/tasks/run-application/configure-pdb/ for cronjob managed pods and pdbs.
    local pdb = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
    local pod =
      {
        containers: [
          container,
        ],
        tolerations: tolerations,
        affinity: affinity,
        priorityClassName: cloudInfo.envToPriorityClass(env),
        securityContext: {
          // bazel cannot be run as root
          runAsUser: 1000,
          fsGroup: 1000,
          fsGroupChangePolicy: 'OnRootMismatch',
        },
        serviceAccountName: serviceAccount.name,
        volumes: [
          // add extra large shared memory
          {
            name: 'dshm',
            emptyDir: {
              medium: 'Memory',
              sizeLimit: '10Gi',
            },
          },
          {
            name: 'cache-volume',
            persistentVolumeClaim: {
              claimName: 'quick-deploy-pvc',
            },
          },
          {
            name: 'github-app-secret',
            secret: {
              secretName: githubSecret.metadata.name,  // pragma: allowlist secret
              optional: false,
            },
          },
          {
            name: 'config',
            configMap: {
              name: '%s-config' % appName,
              items: [
                {
                  key: 'config.json',
                  path: 'config.json',
                },
              ],
            },
          },
        ],
      };
    local deployment =
      {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        metadata: {
          name: appName,
          namespace: namespace,
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/search': 'true',
          },
        },
        spec: {
          replicas: 1,
          minReadySeconds: 0,
          strategy: {
            type: 'RollingUpdate',
            rollingUpdate: {
              maxSurge: 0,
              maxUnavailable: 1,
            },
          },
          selector: {
            matchLabels: {
              'app.kubernetes.io/name': appName,
            },
          },
          template: {
            metadata: {
              labels: {
                'app.kubernetes.io/name': appName,
              },
            },
            spec: pod,
          },
        },
      };
    lib.flatten([
      githubSecret,
      configMap,
      serviceAccount.objects,
      deployment,
      githubSubObjects,
      crossClusterRoles,
      volume,
      pdb,
    ])
  else lib.flatten([crossClusterRoles])

// jsonnet file to run a shell for maintainance and debugging
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(cloud,
         namespace='devtools') [
  local tolerations = nodeLib.tolerations(resource='prohibitGpu', env='DEV', cloud=cloud);
  local affinity = nodeLib.affinity(resource='prohibitGpu', env='DEV', cloud=cloud, appName=null);
  {
    apiVersion: 'v1',
    kind: 'Pod',
    metadata: {
      name: 'deploy-shell',
      namespace: namespace,
    },
    spec: {
      serviceAccountName: 'deploy-sa',
      securityContext: {
        runAsUser: 1000,
        fsGroup: 1000,
        fsGroupChangePolicy: 'OnRootMismatch',
      },
      tolerations: tolerations,
      affinity: affinity,
      containers: [
        {
          name: 'deploy-shell',
          target: {
            name: '//tools/deploy_runner/deploy_job:image',
            dst: 'deploy-image',
          },
          command: ['/bin/bash'],
          args: ['-c', 'trap : TERM INT; sleep infinity & wait'],
          volumeMounts: [
            {
              mountPath: '/cache',
              name: 'cache-volume',
            },
          ],
          env: [
            {
              name: 'BAZEL_BUILD_USER',
              value: 'deploy',
            },
            {
              name: 'XDG_CACHE_HOME',
              value: '/cache/.cache',
            },
          ],
          resources: {
            limits: {
              cpu: '7',
              memory: '28Gi',
            },
          },
        },
      ],
      volumes: [
        {
          name: 'cache-volume',
          persistentVolumeClaim: {
            claimName: 'deploy-pvc',
          },
        },
      ],
      restartPolicy: 'Never',
    },
  },
]

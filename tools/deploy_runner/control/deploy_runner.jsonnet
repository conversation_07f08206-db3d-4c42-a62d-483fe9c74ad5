// K8S deployment file for the deploy job
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(cloud,
         env,
         namespace,
         deployId,
         branch,
         pull_request_number,
         ref,
         targetClouds,
         targetEnvs,
         targetNamespaces,
         targetNames,
         imageName,
         topicName,
         pause,
         allowRollback,
         deploymentTrack,
         deploymentScheduleName)
  local tolerations = nodeLib.tolerations(resource='prohibitGpu', env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource='prohibitGpu', env=env, cloud=cloud, appName=null);
  local projectId = cloudInfo[cloud].projectId;

  local ram_limit_gb = 28;
  local bazel_server_budget_gb = 4;
  local cpu_limit = 7;
  local config = {
    deploy_id: deployId,
    base_directory: '/cache',
    repo_owner: 'augmentcode',
    repo_name: 'augment',
    branch: branch,
    pull_request_number: pull_request_number,
    ref: ref,
    github_app_path: '/github-app',
    cloud: cloud,
    target_clouds: targetClouds,
    target_envs: targetEnvs,
    target_namespaces: targetNamespaces,
    target_names: targetNames,
    deployment_schedule_name: deploymentScheduleName,
    extra_startup_args: '--output_user_root=/cache/bazel-root --host_jvm_args=-Xmx%sg' % bazel_server_budget_gb,
    extra_args: '--disk_cache=/cache/.cache/bazel-diskcache --curses=no --noshow_loading_progress --remote_cache=grpc://bazel-cache-3.devtools:9092',
    ram_limit_gb: (ram_limit_gb - bazel_server_budget_gb),
    cpu_limit: cpu_limit,
    pause_after_staging_minutes: if pause then if cloud == 'GCP_US_CENTRAL1_DEV' then 0 else 40 else 0,
    dry_run: false,
    allow_rollback: allowRollback,
    num_parallel_targets: 16,
    abort_on_deploy_gate_failure: false,
    pubsub: if std.length(topicName) > 0 then {
      project_id: projectId,
      topic_name: topicName,
    } else null,
  };
  local container =
    {
      name: 'deploy',
      image: imageName,
      volumeMounts: [
        {
          name: 'cache-volume',
          mountPath: '/cache',
        },
        {
          mountPath: '/github-app',
          name: 'github-app-secret',
        },
      ],
      args: [
        '--config',
        std.manifestJson(config),
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
        {
          name: 'BAZEL_BUILD_USER',
          value: 'deploy',
        },
        {
          name: 'XDG_CACHE_HOME',
          value: '/cache/.cache',
        },
        {
          name: 'AU_GPU_COUNT',
          value: '0',
        },
      ],
      resources: {
        limits: {
          cpu: cpu_limit,
          memory: '%sGi' % ram_limit_gb,
        },
      },
    };
  local pod =
    {
      terminationGracePeriodSeconds: 30,
      restartPolicy: 'Never',
      containers: [
        container,
      ],
      tolerations: tolerations,
      affinity: affinity,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      securityContext: {
        // bazel cannot be run as root
        runAsUser: 1000,
        fsGroup: 1000,
        fsGroupChangePolicy: 'OnRootMismatch',
      },
      serviceAccountName: 'deploy-sa',
      volumes: [
        {
          name: 'cache-volume',
          persistentVolumeClaim: {
            claimName: if deploymentTrack == 'default' then 'deploy-pvc' else 'deploy-%s-pvc' % std.strReplace(deploymentTrack, '_', '-'),
          },
        },
        {
          name: 'github-app-secret',
          secret: {
            secretName: 'deploy-github-app-secret',  // pragma: allowlist secret
            optional: false,
          },
        },
      ],
    };
  local job =
    {
      apiVersion: 'batch/v1',
      kind: 'Job',
      metadata: {
        name: 'deploy-runner-%s' % deployId,
        namespace: namespace,
        labels: {
          app: 'deploy',
          'deploy-id': deployId,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        // disable backoff
        backoffLimit: 0,
        template: {
          metadata: {
            labels: {
              app: 'deploy',
              'deploy-id': deployId,
            },
            annotations: {
              'augmentcode.com/enable-prometheus-scraping': 'true',
              // TODO: also copy reloader annotation?
            },
          },
          spec: pod,
        },
      },
    };
  [job]

"""Blueprint for the deploy backend API."""

# pylint: disable=no-member
import json
import logging
import uuid
from typing import Any, Mapping, Optional

import google.protobuf.json_format as json_format
import grpc
import structlog
from flask import Blueprint, Response, current_app, jsonify, request
from base.cloud.k8s.kubectl import <PERSON><PERSON>ctlEx<PERSON>
from base.cloud.k8s.kubectl_factory import KubectlFactory
from base.cloud.k8s.kubernetes_client import KubernetesClient
from base.flask_util.iap_util import extract_user, iap_jwt_verified

from tools.deploy_runner.server import deploy_pb2, deploy_pb2_grpc

bp = Blueprint("api", __name__, url_prefix="/api")

log = structlog.get_logger()


@iap_jwt_verified
@bp.route("deployment", methods=["POST"])
def deployments_schedule_handler():
    data: Optional[Mapping[str, Any]] = request.get_json()
    assert data

    requestor = extract_user(request)
    assert requestor is not None

    logging.info("schedule request for %s", data)

    try:
        client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
        schedule_request = deploy_pb2.ScheduleDeploymentRequest()
        schedule_request.adhoc.requestor = requestor
        schedule_request.adhoc.reason = data["reason"]
        pr = data.get("pullRequestNumber", "")
        if pr:
            schedule_request.pull_request_number = pr
        else:
            schedule_request.branch = data.get("branch", "")
            if not schedule_request.branch:
                raise ValueError("Branch or PR must be set")
        schedule_request.commit_ref = data["commitRef"]
        if data.get("shouldBypass", False):
            schedule_request.deployment_track = deploy_pb2.DeploymentTrack.BYPASS
        else:
            schedule_request.deployment_track = deploy_pb2.DeploymentTrack.DEFAULT
        schedule_request.allow_rollback = data.get("allowRollback", False)
        for cloud in data["cloud"]:
            v = deploy_pb2.DeployCloud.Value(cloud)
            schedule_request.clouds.append(v)
        schedule_request.namespaces.extend(data["namespace"])
        schedule_request.target_names.extend(data["targets"])

        logging.info("schedule request %s", schedule_request)
        response = client.ScheduleDeployment(schedule_request)
        result_data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(result_data)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        elif rpc_error.code() == grpc.StatusCode.INVALID_ARGUMENT:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=400,
                mimetype="application/json",
            )
        else:
            raise


@iap_jwt_verified
@bp.route("deployment/<deploy_id>")
def get_deployment_handler(deploy_id: str):
    logging.info("request for %s", uuid.UUID(deploy_id))

    try:
        client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
        get_request = deploy_pb2.GetDeploymentRequest()
        get_request.deploy_id = deploy_id
        logging.info("get request %s", get_request)
        response = client.GetDeployment(get_request)
        logging.info("response %s", response)
        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        json_data = jsonify(data)
        return json_data
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@iap_jwt_verified
@bp.route("deployments")
def batch_get_deployment_handler():
    oldest_deploy_id = request.args.get("oldest_deploy_id", "")
    newest_deploy_id = request.args.get("newest_deploy_id", "")
    max_count = int(request.args.get("max_count", "20"))
    logging.info("request for %s to %s", oldest_deploy_id, newest_deploy_id)

    client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
    batch_get_request = deploy_pb2.GetDeploymentsRequest()
    batch_get_request.oldest_deploy_id = oldest_deploy_id
    batch_get_request.newest_deploy_id = newest_deploy_id
    batch_get_request.max_count = max_count
    logging.info("get deployments request %s", batch_get_request)
    response_stream = client.GetDeployments(batch_get_request)
    data = []
    for response in response_stream:
        for deployment in response.deployments:
            data.append(
                json_format.MessageToDict(
                    deployment,
                    including_default_value_fields=True,  # type: ignore
                )
            )
    json_data = jsonify({"deployments": data})
    return json_data


@iap_jwt_verified
@bp.route("deployment/<deploy_id>/events")
def get_deployments_events(deploy_id: str):
    try:
        min_sequence_number = int(request.args.get("min_sequence_number", "0"))
        limit = int(request.args.get("limit", "1000"))
        client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
        get_request = deploy_pb2.GetDeploymentEventsRequest()
        get_request.deploy_id = deploy_id
        get_request.min_sequence_number = min_sequence_number
        get_request.limit = limit
        logging.info("get request %s", get_request)
        events = []
        for response in client.GetDeploymentEvents(get_request):
            item = json_format.MessageToDict(
                response,
                including_default_value_fields=True,  # type: ignore
            )
            events.append(item)
        return jsonify(events)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("deployment/<deploy_id>/cancel", methods=["POST"])
def run_cancel_handler(deploy_id: str):
    logging.info("cancel request for %s", uuid.UUID(deploy_id))

    try:
        client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
        cancel_request = deploy_pb2.CancelDeploymentRequest()
        cancel_request.deploy_id = deploy_id

        logging.info("cancel request %s", cancel_request)
        client.CancelDeployment(cancel_request)
        return jsonify({})
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("scheduled_deployment/<deployment_track>", methods=["GET"])
def get_scheduled_deployment_handler(deployment_track: str):
    logging.info("get scheduled deployment config request")

    client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
    r = deploy_pb2.GetScheduledDeploymentConfigRequest()
    if deployment_track:
        r.deployment_track = deploy_pb2.DeploymentTrack.Value(deployment_track)
    response = client.GetScheduledDeploymentConfig(r)
    return jsonify({"enabled": response.enabled})


@bp.route("scheduled_deployment_tracks", methods=["GET"])
def get_deployment_tracks():
    return jsonify(
        {
            "tracks": [
                deploy_pb2.DeploymentTrack.Name(i)
                for i in deploy_pb2.DeploymentTrack.values()
                if i != deploy_pb2.DeploymentTrack.BYPASS
            ]
        },
    )


@bp.route("scheduled_deployment/<deployment_track>/disable", methods=["POST"])
def disable_scheduled_deployment_handler(deployment_track: str):
    logging.info("disable scheduled deployment request")

    client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
    r = deploy_pb2.ConfigureScheduledDeploymentRequest()
    if deployment_track:
        r.deployment_track = deploy_pb2.DeploymentTrack.Value(deployment_track)
    r.enabled = False
    client.ConfigureScheduledDeployment(r)
    return jsonify({})


@bp.route("scheduled_deployment/<deployment_track>/enable", methods=["POST"])
def enable_scheduled_deployment_handler(deployment_track: str):
    logging.info("enable scheduled deployment request")

    client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
    r = deploy_pb2.ConfigureScheduledDeploymentRequest()
    if deployment_track:
        r.deployment_track = deploy_pb2.DeploymentTrack.Value(deployment_track)
    r.enabled = True
    client.ConfigureScheduledDeployment(r)
    return jsonify({})


@bp.route("kubernetes/clouds", methods=["GET"])
def get_kubernetes_clouds():
    clouds_str = current_app.config["CLOUDS"]
    r = json.loads(clouds_str)
    logging.info("clouds: %s", r)
    return jsonify(r)


@iap_jwt_verified
@bp.route("commit_state", methods=["GET"])
def get_commit_state_handler():
    """Get commit state for a commit ref or pull request number."""
    commit_ref = request.args.get("commit_ref", "")
    pull_request_number = request.args.get("pull_request_number", "")

    if not commit_ref and not pull_request_number:
        return Response(
            json.dumps(
                {"error": "Either commit_ref or pull_request_number must be provided"}
            ),
            status=400,
            mimetype="application/json",
        )

    if commit_ref and pull_request_number:
        return Response(
            json.dumps(
                {
                    "error": "Only one of commit_ref or pull_request_number should be provided"
                }
            ),
            status=400,
            mimetype="application/json",
        )

    logging.info(
        "get commit state request for commit_ref=%s, pull_request_number=%s",
        commit_ref,
        pull_request_number,
    )

    try:
        client: deploy_pb2_grpc.DeployStub = current_app.deploy_rpc_client  # type: ignore
        get_request = deploy_pb2.GetCommitStateRequest()

        if commit_ref:
            get_request.commit_ref = commit_ref
        else:
            get_request.pull_request_number = pull_request_number

        logging.info("get commit state request %s", get_request)
        response = client.GetCommitState(get_request)
        logging.info("get commit state response %s", response)

        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(data)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"error": "Commit or pull request not found"}),
                status=404,
                mimetype="application/json",
            )
        elif rpc_error.code() == grpc.StatusCode.INVALID_ARGUMENT:  # type: ignore
            return Response(
                json.dumps({"error": "Invalid argument"}),
                status=400,
                mimetype="application/json",
            )
        else:
            logging.error("GetCommitState RPC error: %s", rpc_error)
            return Response(
                json.dumps({"error": "Internal server error"}),
                status=500,
                mimetype="application/json",
            )


@bp.route("kubernetes/namespaces", methods=["GET"])
def get_kubernetes_namespaces():
    cloud = request.args.get("cloud", None)
    if not cloud:
        raise ValueError("Missing cloud")
    kubectl_factory: KubectlFactory = current_app.kubectl_factory  # type: ignore
    try:
        kubectl = kubectl_factory(cloud)
        results = []
        for deployment in kubectl.list("namespaces", None):
            metadata = deployment["metadata"]
            d = {
                "name": metadata["name"],
                "cloud": cloud,
            }
            results.append(d)
        return jsonify(results)

    except KubectlException as ex:
        logging.info("stdout: %s", ex.stdout)
        logging.info("stderr: %s", ex.stderr)
        raise


@bp.route("kubernetes/state", methods=["GET"])
def get_kubernetes_state():
    batch_size = 100
    cloud = request.args.get("cloud", None)
    if not cloud:
        raise ValueError("Missing cloud")
    continue_token = request.args.get("continue_token", None)

    kubernetes_client: KubernetesClient = current_app.kubernetes_client  # type: ignore
    v1 = kubernetes_client.get_apps_api(cloud)
    ret = v1.list_deployment_for_all_namespaces(
        _continue=continue_token, limit=batch_size
    )
    result = []
    for deployment in ret.items:
        metadata = deployment.metadata
        labels = metadata.labels
        if not labels:
            continue
        conditions = deployment.status.conditions
        if conditions:
            # find condition with newest lastTransitionTime
            conditions = [c for c in conditions if c.type == "Available"]
            condition = {
                "lastTransitionTime": conditions[0].last_transition_time,
                "lastUpdateTime": conditions[0].last_update_time,
                "status": conditions[0].status,
                "type": conditions[0].type,
                "reason": conditions[0].reason,
                "message": conditions[0].message,
            }
        else:
            condition = None

        app = None
        if "app" in labels:
            app = labels["app"]
        elif "app.kubernetes.io/name" in labels:
            app = labels["app.kubernetes.io/name"]

        d = {
            "name": metadata.name,
            "namespace": metadata.namespace,
            "cloud": cloud,
            "deployed_by": labels.get("augmentcode.com/deployed-by"),
            "deployment_target": labels.get("augmentcode.com/deployment-target"),
            "version": labels.get("app.kubernetes.io/version"),
            "app": app,
            "condition": condition,
        }
        result.append(d)
    return jsonify({"deployments": result, "continue_token": ret.metadata._continue})

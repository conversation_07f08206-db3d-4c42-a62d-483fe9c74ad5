import argparse
import json
import logging
import pathlib
import subprocess
import time
from datetime import datetime, timedelta, timezone
from typing import List

from dateutil import parser as dateparser

from base.logging.struct_logging import setup_struct_logging

_KUBECTL_BIN = "../k8s_binary/file/kubectl"
_ENG_JSON_PATH = "deploy/common/eng.json"

# API groups that need to be cleaned up before deleting a namespace
_API_GROUPS_TO_CLEANUP = [
    "artifactregistry.cnrm.cloud.google.com",
    "bigquery.cnrm.cloud.google.com",
    "bigtable.cnrm.cloud.google.com",
    "container.cnrm.cloud.google.com",
    "customize.core.cnrm.cloud.google.com",
    "dns.cnrm.cloud.google.com",
    "eng.augmentcode.com",
    "filestore.cnrm.cloud.google.com",
    "monitoring.cnrm.cloud.google.com",
    "networking.gke.io",
    "networking.k8s.io",
    "pubsub.cnrm.cloud.google.com",
    "spanner.cnrm.cloud.google.com",
    "storage.cnrm.cloud.google.com",
]


class DevGpuCleanup:
    """Class to handle cleanup of GPU deployments."""

    def __init__(self, dry_run=False):
        """Initialize the cleanup handler.

        Args:
            dry_run: If True, don't actually perform any actions, just log what would be done.
        """
        self.dry_run = dry_run

    def get_kubectl_json(self, command):
        """Runs a kubectl command and returns the JSON output."""
        result = subprocess.run(command, stdout=subprocess.PIPE, check=True)
        return json.loads(result.stdout)

    def is_namespace_terminating(self, namespace):
        """Check if a namespace is in the Terminating state.

        Args:
            namespace: The namespace name

        Returns:
            True if the namespace is in Terminating state, False otherwise
        """
        try:
            command = [_KUBECTL_BIN, "get", "namespace", namespace, "-o", "json"]
            ns_json = self.get_kubectl_json(command)

            # Check if the namespace has a status with phase "Terminating"
            if "status" in ns_json and "phase" in ns_json["status"]:
                if ns_json["status"]["phase"] == "Terminating":
                    logging.info(f"Namespace {namespace} is in Terminating phase")
                    return True

            # Also check for deletionTimestamp which indicates the namespace is being deleted
            if "metadata" in ns_json and "deletionTimestamp" in ns_json["metadata"]:
                logging.info(f"Namespace {namespace} has deletionTimestamp set")
                return True

            return False
        except subprocess.CalledProcessError as e:
            # If we can't get the namespace, assume it's not terminating
            logging.warning(f"Failed to get namespace {namespace} status: {e}")
            return False
        except Exception as e:
            # Catch any other exceptions to ensure the script continues running
            logging.error(
                f"Error checking if namespace {namespace} is terminating: {e}"
            )
            return False

    def get_namespaces(self):
        """Get all namespaces and filter those starting with 'dev-'

        Because this is meant to find per-user development namespaces,
        also filter out dev-team-ra which is shared/still in use
        for dogfooding; should have chosen a different name. alas
        """
        # Get all namespaces with their status
        command = [_KUBECTL_BIN, "get", "namespaces", "-o", "json"]
        namespaces_json = self.get_kubectl_json(command)

        # Filter namespaces that start with 'dev-' and are not 'dev-team-ra'
        dev_namespaces = []
        for ns in namespaces_json["items"]:
            name = ns["metadata"]["name"]
            if name.startswith("dev-") and name != "dev-team-ra":
                dev_namespaces.append(name)

        return dev_namespaces

    def get_deployments_in_namespace(self, namespace):
        """Get all deployments in a specific namespace."""
        command = [_KUBECTL_BIN, "get", "deployments", "-n", namespace, "-o", "json"]
        return self.get_kubectl_json(command)

    def get_last_update_time(self, conditions):
        """Extract the last update time from the 'Progressing' condition."""
        for condition in conditions:
            if condition["type"] == "Progressing" and "lastUpdateTime" in condition:
                return dateparser.parse(condition["lastUpdateTime"])
        return None

    def is_old(self, timestamp):
        """Check if a timestamp is old"""
        return (datetime.now(timezone.utc) - timestamp) > timedelta(hours=72)

    def scale_down_gpu_deployment(self, namespace, deployment_name):
        """Scale down a GPU deployment.

        Args:
            namespace: The namespace containing the deployment
            deployment_name: The name of the deployment to scale down
        """
        prefix = "DRY RUN, not running: " if self.dry_run else ""
        logging.info(
            f"{prefix}kubectl scale deployment {deployment_name} -n {namespace} --replicas=0 --timeout=30s"
        )

        if not self.dry_run:
            subprocess.run(
                [
                    _KUBECTL_BIN,
                    "scale",
                    "deployment",
                    deployment_name,
                    "-n",
                    namespace,
                    "--replicas=0",
                    "--timeout=30s",
                ],
                check=True,
            )

    def cleanup_old_gpu_deployments(self):
        """Scale down GPU deployments that haven't been updated in a while."""
        namespaces = self.get_namespaces()
        logging.info(f"Found {len(namespaces)} dev namespaces")

        for ns in namespaces:
            try:
                # Skip namespaces that are in Terminating state
                if self.is_namespace_terminating(ns):
                    logging.info(
                        f"Skipping namespace {ns} as it is in Terminating state"
                    )
                    continue

                deployments = self.get_deployments_in_namespace(ns)

                for deployment in deployments["items"]:
                    containers = deployment["spec"]["template"]["spec"]["containers"]

                    # Check if any container in the deployment has GPU resource requests or limits
                    uses_gpu = any(
                        "nvidia.com/gpu"
                        in container.get("resources", {}).get("requests", {})
                        or "nvidia.com/gpu"
                        in container.get("resources", {}).get("limits", {})
                        for container in containers
                    )

                    has_replicas = deployment["spec"]["replicas"] > 0

                    if uses_gpu and has_replicas:
                        # Check the 'Progressing' condition for lastUpdateTime
                        conditions = deployment["status"].get("conditions", [])
                        last_update_time = self.get_last_update_time(conditions)

                        if last_update_time and self.is_old(last_update_time):
                            deployment_name = deployment["metadata"]["name"]
                            self.scale_down_gpu_deployment(ns, deployment_name)
            except Exception as e:
                logging.error(f"Error processing namespace {ns}: {e}")


class DevNamespaceCleanup:
    """Class to handle cleanup of development namespaces."""

    def __init__(self, dry_run=False):
        """Initialize the cleanup handler.

        Args:
            dry_run: If True, don't actually perform any actions, just log what would be done.
        """
        self.dry_run = dry_run
        self.active_usernames = set()
        self.load_active_engineers()

    def load_active_engineers(self):
        """Load the list of active engineers from eng.json."""
        try:
            eng_json_path = pathlib.Path(_ENG_JSON_PATH)
            if not eng_json_path.exists():
                logging.warning(f"Engineer list not found at {_ENG_JSON_PATH}")
                return

            with open(eng_json_path, "r") as f:
                engineers = json.load(f)

            # Extract usernames from the engineer list
            self.active_usernames = {eng["username"] for eng in engineers}
            logging.info(f"Loaded {len(self.active_usernames)} active engineers")
        except Exception as e:
            logging.error(f"Error loading engineer list: {e}")

    def get_kubectl_json(self, command):
        """Runs a kubectl command and returns the JSON output."""
        result = subprocess.run(command, stdout=subprocess.PIPE, check=True)
        return json.loads(result.stdout)

    def get_namespaces(self) -> list[str]:
        """Get all namespaces and filter those starting with 'dev-'

        Because this is meant to find per-user development namespaces,
        also filter out dev-team-ra which is shared/still in use
        for dogfooding; should have chosen a different name. alas
        """
        # Get all namespaces with their status
        command = [_KUBECTL_BIN, "get", "namespaces", "-o", "json"]
        namespaces_json = self.get_kubectl_json(command)

        # Filter namespaces that start with 'dev-' and are not 'dev-team-ra'
        dev_namespaces = []
        for ns in namespaces_json["items"]:
            name = ns["metadata"]["name"]
            owner = (
                ns["metadata"].get("annotations", {}).get("eng.augmentcode.com/owner")
            )
            if owner:
                if owner not in self.active_usernames:
                    dev_namespaces.append(name)

        return dev_namespaces

    def is_namespace_terminating(self, namespace):
        """Check if a namespace is in the Terminating state.

        Args:
            namespace: The namespace name

        Returns:
            True if the namespace is in Terminating state, False otherwise
        """
        try:
            command = [_KUBECTL_BIN, "get", "namespace", namespace, "-o", "json"]
            ns_json = self.get_kubectl_json(command)

            # Check if the namespace has a status with phase "Terminating"
            if "status" in ns_json and "phase" in ns_json["status"]:
                if ns_json["status"]["phase"] == "Terminating":
                    logging.info(f"Namespace {namespace} is in Terminating phase")
                    return True

            # Also check for deletionTimestamp which indicates the namespace is being deleted
            if "metadata" in ns_json and "deletionTimestamp" in ns_json["metadata"]:
                logging.info(f"Namespace {namespace} has deletionTimestamp set")
                return True

            return False
        except subprocess.CalledProcessError as e:
            # If we can't get the namespace, assume it's not terminating
            logging.warning(f"Failed to get namespace {namespace} status: {e}")
            return False
        except Exception as e:
            # Catch any other exceptions to ensure the script continues running
            logging.error(
                f"Error checking if namespace {namespace} is terminating: {e}"
            )
            return False

    def get_api_resources(self, api_group: str) -> List[str]:
        """Get all resource types for a specific API group.

        Args:
            api_group: The API group to get resources for

        Returns:
            List of resource types in the API group
        """
        try:
            command = [
                _KUBECTL_BIN,
                "api-resources",
                "--api-group",
                api_group,
                "-o",
                "name",
            ]
            result = subprocess.run(
                command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False
            )

            if result.returncode != 0:
                logging.warning(
                    f"Failed to get API resources for {api_group}: {result.stderr.decode('utf-8')}"
                )
                return []

            resources = result.stdout.decode("utf-8").strip().split("\n")
            # Filter out empty strings
            return [r for r in resources if r]
        except Exception as e:
            logging.error(f"Error getting API resources for {api_group}: {e}")
            return []

    def delete_api_group_resources(self, namespace: str, api_group: str):
        """Delete all resources of a specific API group in a namespace.

        Args:
            namespace: The namespace to delete resources from
            api_group: The API group to delete resources for
        """
        resources = self.get_api_resources(api_group)
        if not resources:
            logging.info(f"No resources found for API group {api_group}")
            return

        for resource in resources:
            prefix = "DRY RUN, not running: " if self.dry_run else ""
            logging.info(f"{prefix}kubectl delete {resource} --all -n {namespace}")

            if not self.dry_run:
                try:
                    # Use --wait=false to avoid waiting for resources to be deleted
                    # This speeds up the process and prevents hanging
                    command = [
                        _KUBECTL_BIN,
                        "delete",
                        resource,
                        "--all",
                        "-n",
                        namespace,
                        "--wait=false",
                    ]
                    result = subprocess.run(
                        command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        check=False,
                    )

                    if result.returncode != 0:
                        stderr = result.stderr.decode("utf-8")
                        # Ignore "not found" errors
                        if (
                            "not found" not in stderr
                            and "no matches for kind" not in stderr
                        ):
                            logging.warning(
                                f"Failed to delete {resource} in {namespace}: {stderr}"
                            )
                    else:
                        logging.info(f"Deleted {resource} resources in {namespace}")
                except Exception as e:
                    logging.error(f"Error deleting {resource} in {namespace}: {e}")

    def cleanup_namespace_resources(self, namespace: str):
        """Clean up all resources in a namespace from the specified API groups.

        Args:
            namespace: The namespace to clean up
        """
        logging.info(f"Cleaning up resources in namespace {namespace} before deletion")

        for api_group in _API_GROUPS_TO_CLEANUP:
            logging.info(
                f"Cleaning up resources from API group {api_group} in namespace {namespace}"
            )
            self.delete_api_group_resources(namespace, api_group)

        # Give Kubernetes some time to process the deletions
        if not self.dry_run:
            logging.info("Waiting 5 seconds for resource deletion to begin...")
            time.sleep(5)

    def delete_namespace(self, namespace):
        """Delete a namespace.

        Args:
            namespace: The namespace to delete
        """
        self.cleanup_namespace_resources(namespace)

        prefix = "DRY RUN, not running: " if self.dry_run else ""
        logging.info(f"{prefix}kubectl delete namespace {namespace}")

        if not self.dry_run:
            try:
                # Use --wait=false to avoid waiting for the namespace to be fully deleted
                subprocess.run(
                    [_KUBECTL_BIN, "delete", "namespace", namespace, "--wait=false"],
                    check=True,
                )
                logging.info(
                    f"Successfully initiated deletion of namespace {namespace}"
                )
            except subprocess.CalledProcessError as e:
                logging.error(f"Failed to delete namespace {namespace}: {e}")

    def cleanup_inactive_engineer_namespaces(self):
        """Delete namespaces that don't belong to active engineers."""
        namespaces = self.get_namespaces()
        logging.info(
            f"Checking {len(namespaces)} dev namespaces for inactive engineers"
        )

        for ns in namespaces:
            # Check if the namespace is already being terminated
            if self.is_namespace_terminating(ns):
                logging.info(
                    f"Skipping namespace {ns} as it is already in Terminating state"
                )
                continue

            logging.info(f"Namespace {ns} does not belong to an active engineer")
            self.delete_namespace(ns)


class DevOrphanedResourceCleanup:
    """Class to handle cleanup of orphaned Kubernetes resources in dev namespaces.

    This class identifies resources that don't have matching deployments by comparing
    the 'app' label in resource metadata with the 'app' label in deployment selectors.
    """

    # Default list of resource kinds to check for orphaned resources
    DEFAULT_RESOURCE_KINDS = ["service", "configmap", "secret"]

    def __init__(self, dry_run=False, resource_kinds=None):
        """Initialize the cleanup handler.

        Args:
            dry_run: If True, don't actually perform any actions, just log what would be done.
            resource_kinds: List of Kubernetes resource kinds to check (e.g., ["services", "configmaps"])
                          If None, uses DEFAULT_RESOURCE_KINDS
        """
        self.dry_run = dry_run
        self.resource_kinds = resource_kinds or self.DEFAULT_RESOURCE_KINDS

    def get_kubectl_json(self, command):
        """Runs a kubectl command and returns the JSON output."""
        result = subprocess.run(command, stdout=subprocess.PIPE, check=True)
        return json.loads(result.stdout)

    def get_namespaces(self):
        """Get all namespaces and filter those starting with 'dev-'

        Because this is meant to find per-user development namespaces,
        also filter out dev-team-ra which is shared/still in use
        for dogfooding; should have chosen a different name. alas
        """
        # Get all namespaces with their status
        command = [_KUBECTL_BIN, "get", "namespaces", "-o", "json"]
        namespaces_json = self.get_kubectl_json(command)

        # Filter namespaces that start with 'dev-' and are not 'dev-team-ra'
        dev_namespaces = []
        for ns in namespaces_json["items"]:
            name = ns["metadata"]["name"]
            if name.startswith("dev-") and name != "dev-team-ra":
                dev_namespaces.append(name)

        return dev_namespaces

    def get_resources_in_namespace(self, namespace, resource_kind):
        """Get all resources of a specific kind in a namespace."""
        command = [_KUBECTL_BIN, "get", resource_kind, "-n", namespace, "-o", "json"]
        return self.get_kubectl_json(command)

    def get_deployments_in_namespace(self, namespace):
        """Get all deployments in a specific namespace."""
        command = [_KUBECTL_BIN, "get", "deployments", "-n", namespace, "-o", "json"]
        return self.get_kubectl_json(command)

    def app_labels_match(self, resource_app_label, deployment_app_label):
        """Check if a resource's app label matches a deployment's app label.

        Args:
            resource_app_label: String value of the resource's 'app' label
            deployment_app_label: String value of the deployment's 'app' label

        Returns:
            True if both labels exist and match, False otherwise
        """
        if not resource_app_label or not deployment_app_label:
            return False

        return resource_app_label == deployment_app_label

    def get_resource_app_label(self, resource) -> str | None:
        """Extract the 'app' label from a resource's metadata.

        Args:
            resource: Kubernetes resource object

        Returns:
            String value of the 'app' label, or None if not present
        """
        labels = resource.get("metadata", {}).get("labels", {})
        return labels.get("app")

    def has_matching_deployment(self, deployments, app_label: str):
        """Check if a resource has a matching deployment based on app labels.

        Args:
            resource: Resource object from Kubernetes API
            resource_kind: The kind of resource
            deployments: List of deployment objects from Kubernetes API

        Returns:
            True if there's a matching deployment, False otherwise
        """

        for deployment in deployments["items"]:
            deployment_app_label = self.get_resource_app_label(deployment)
            if not deployment_app_label:
                continue
            if deployment_app_label == app_label:
                return True

        return False

    def delete_resource(self, namespace, resource_name, resource_kind):
        """Delete a resource.

        Args:
            namespace: The namespace containing the resource
            resource_name: The name of the resource to delete
            resource_kind: The kind of resource to delete
        """
        prefix = "DRY RUN, not running: " if self.dry_run else ""
        logging.info(
            f"{prefix}kubectl delete {resource_kind} {resource_name} -n {namespace}"
        )

        if not self.dry_run:
            try:
                subprocess.run(
                    [
                        _KUBECTL_BIN,
                        "delete",
                        resource_kind,
                        resource_name,
                        "-n",
                        namespace,
                        "--wait=false",
                    ],
                    check=True,
                )
                logging.info(
                    f"Successfully deleted {resource_kind} {resource_name} in namespace {namespace}"
                )
            except subprocess.CalledProcessError as e:
                logging.error(
                    f"Failed to delete {resource_kind} {resource_name} in namespace {namespace}: {e}"
                )

    def cleanup_orphaned_resources(self):
        """Remove resources that don't have matching Deployments based on 'app' label matching."""
        namespaces = self.get_namespaces()
        for ns in namespaces:
            try:
                deployments = self.get_deployments_in_namespace(ns)

                for resource_kind in self.resource_kinds:
                    try:
                        resources = self.get_resources_in_namespace(ns, resource_kind)

                        for resource in resources["items"]:
                            resource_name = resource["metadata"]["name"]

                            # Skip resources without app labels - they might be system resources
                            resource_app_label = self.get_resource_app_label(resource)
                            if not resource_app_label:
                                continue

                            if not self.has_matching_deployment(
                                deployments, resource_app_label
                            ):
                                resource_app_label = self.get_resource_app_label(
                                    resource
                                )
                                logging.info(
                                    f"Found orphaned {resource_kind} {resource_name} (app={resource_app_label}) in namespace {ns}"
                                )
                                self.delete_resource(
                                    ns, resource_name, resource_kind
                                )  # Remove 's' from plural
                            else:
                                logging.info(
                                    f"{resource_kind} {resource_name} (app={resource_app_label}) in namespace {ns} has a matching deployment"
                                )

                    except subprocess.CalledProcessError as e:
                        # Resource kind might not exist in this cluster, skip it
                        logging.debug(
                            f"Resource kind {resource_kind} not found in namespace {ns}: {e}"
                        )
                        continue

            except Exception as e:
                logging.error(f"Error processing namespace {ns}: {e}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dry-run", action="store_true", help="Don't actually perform any actions"
    )
    parser.add_argument(
        "--skip-gpu-cleanup",
        action="store_true",
        help="Skip cleaning up old GPU deployments",
    )
    parser.add_argument(
        "--skip-namespace-cleanup",
        action="store_true",
        help="Skip cleaning up inactive engineer namespaces",
    )
    parser.add_argument(
        "--skip-resource-cleanup",
        action="store_true",
        help="Skip cleaning up orphaned resources (services, configmaps, secrets)",
    )
    args = parser.parse_args()

    setup_struct_logging()

    if not args.skip_gpu_cleanup:
        cleanup = DevGpuCleanup(dry_run=args.dry_run)
        cleanup.cleanup_old_gpu_deployments()

    if not args.skip_namespace_cleanup:
        cleanup = DevNamespaceCleanup(dry_run=args.dry_run)
        cleanup.cleanup_inactive_engineer_namespaces()

    if not args.skip_resource_cleanup:
        cleanup = DevOrphanedResourceCleanup(dry_run=args.dry_run)
        cleanup.cleanup_orphaned_resources()


if __name__ == "__main__":
    main()

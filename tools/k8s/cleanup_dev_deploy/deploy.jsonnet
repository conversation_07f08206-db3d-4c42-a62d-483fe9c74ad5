local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'cleanup-dev';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=false,
  );
  local ram_limit_gb = 1;
  local cpu_limit = 0.1;
  local container =
    {
      name: appName,
      args: [],
      target: {
        name: '//tools/k8s/cleanup_dev_deploy:image',
        dst: 'cleanup-dev-image',
      },
      resources: {
        limits: {
          cpu: cpu_limit,
          memory: '%sGi' % ram_limit_gb,
        },
      },
    };
  local pod =
    {
      restartPolicy: 'Never',
      containers: [
        container,
      ],
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: serviceAccount.name,
    };
  local clusterRole = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRole',
    metadata: {
      name: 'cleanup-dev-role',
      labels: {
        app: appName,
      },
    },
    rules: [
      {
        apiGroups: ['', 'apps'],
        resources: ['deployments', 'deployments/scale'],
        verbs: ['get', 'list', 'update', 'patch'],
      },
      // list and delete namespaces
      {
        apiGroups: [''],
        resources: ['namespaces'],
        verbs: ['get', 'list', 'delete'],
      },
      // permissions for secrets and services
      {
        apiGroups: [''],
        resources: ['secrets', 'services'],
        verbs: ['get', 'list', 'delete'],
      },
      // access to api-resources for discovery
      {
        apiGroups: [''],
        resources: ['api-resources'],
        verbs: ['get', 'list'],
      },
      // permissions to delete resources from specific API groups
      {
        apiGroups: [
          'artifactregistry.cnrm.cloud.google.com',
          'bigquery.cnrm.cloud.google.com',
          'bigtable.cnrm.cloud.google.com',
          'container.cnrm.cloud.google.com',
          'customize.core.cnrm.cloud.google.com',
          'dns.cnrm.cloud.google.com',
          'eng.augmentcode.com',
          'filestore.cnrm.cloud.google.com',
          'monitoring.cnrm.cloud.google.com',
          'networking.gke.io',
          'networking.k8s.io',
          'pubsub.cnrm.cloud.google.com',
          'spanner.cnrm.cloud.google.com',
          'storage.cnrm.cloud.google.com',
        ],
        resources: ['*'],
        verbs: ['get', 'list', 'delete'],
      },
    ],
  };
  local clusterRoleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRoleBinding',
    metadata: {
      name: 'cleanup-dev-role-binding',
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
        namespace: namespace,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'cleanup-dev-role',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  };
  local cronJob = {
    apiVersion: 'batch/v1',
    kind: 'CronJob',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      schedule: '0 3 * * *',
      concurrencyPolicy: 'Forbid',
      startingDeadlineSeconds: 120,
      successfulJobsHistoryLimit: 16,
      failedJobsHistoryLimit: 4,
      timeZone: 'America/Los_Angeles',
      jobTemplate: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: {
          backoffLimit: 0,
          template: {
            metadata: {
              labels: {
                app: appName,
              },
            },
            spec: pod,
          },
        },
      },
    },
  };
  lib.flatten([
    serviceAccount.objects,
    cronJob,
    clusterRole,
    clusterRoleBinding,
  ])

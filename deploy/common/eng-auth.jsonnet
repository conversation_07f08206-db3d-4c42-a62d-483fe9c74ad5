local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local eng = import 'deploy/common/eng.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud)
  local projectId = cloudInfo[cloud].projectId;
  local appName = 'eng-auth';

  local permissionsForAllEngineers = [
    // Prefer roles to granting individual permissions.
    //
    // storage.buckets.list so people can navigate to their buckets in the console
    'storage.buckets.list',
    // Allow engineers to create secrets, but not modify or delete them.
    'secretmanager.secrets.create',
    // GKE backup
    'gkebackup.backupPlans.get',
    'gkebackup.backupPlans.getIamPolicy',
    'gkebackup.backupPlans.list',
    'gkebackup.backups.get',
    'gkebackup.backups.getBackupIndex',
    'gkebackup.backups.list',
    'gkebackup.locations.get',
    'gkebackup.locations.list',
    'gkebackup.operations.get',
    'gkebackup.operations.list',
    'gkebackup.restorePlans.get',
    'gkebackup.restorePlans.getIamPolicy',
    'gkebackup.restorePlans.list',
    'gkebackup.restores.get',
    'gkebackup.restores.list',
    'gkebackup.volumeBackups.get',
    'gkebackup.volumeBackups.list',
    'gkebackup.volumeRestores.get',
    'gkebackup.volumeRestores.list',
    // Full access to connectivity testing tools
    'networkmanagement.connectivitytests.create',
    'networkmanagement.connectivitytests.delete',
    'networkmanagement.connectivitytests.get',
    'networkmanagement.connectivitytests.list',
    'networkmanagement.connectivitytests.rerun',
    'networkmanagement.connectivitytests.update',
  ];

  // List of roles that we want all engineers to have in production.
  local prodRoles = [
    'projects/%s/roles/augment.engineer.prod.v2' % projectId,
    'roles/aiplatform.viewer',
    'roles/artifactregistry.writer',
    'roles/bigquery.jobUser',
    'roles/bigquery.metadataViewer',
    'roles/bigquery.resourceViewer',
    'roles/bigtable.viewer',
    'roles/cloudsupport.techSupportEditor',
    'roles/cloudtrace.user',
    'roles/compute.viewer',
    'roles/compute.networkViewer',
    'roles/container.clusterViewer',
    'roles/container.viewer',
    'roles/dns.reader',
    'roles/errorreporting.viewer',
    'roles/file.viewer',
    'roles/iam.roleViewer',
    'roles/iap.httpsResourceAccessor',
    'roles/logging.viewer',
    // allow engineers to create and edit notification channels, e.g. to pagerduty
    'roles/monitoring.notificationChannelEditor',
    'roles/monitoring.snoozeEditor',
    'roles/monitoring.dashboardEditor',
    'roles/monitoring.viewer',
    'roles/pubsub.viewer',
    // Allow engineers to view secret metadata and to create new secret
    // versions. Note that this does not allow deleting secret versions or
    // accessing their payloads.
    'roles/secretmanager.secretVersionAdder',
    'roles/secretmanager.viewer',
    'roles/spanner.viewer',
    'roles/logging.privateLogViewer',
    'roles/cloudquotas.viewer',
  ];

  local customRoles = if cloudInfo.isProjectLeadProdCluster(cloud) then {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMCustomRole',
    metadata: {
      annotations: {
        'cnrm.cloud.google.com/project-id': projectId,
        // It takes 44 days to delete a custom role, so don't delete it.
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      },
      name: 'role-prod-engineer',
      namespace: 'devtools',
    },
    spec: {
      title: 'Engineer prod role',
      resourceID: 'augment.engineer.prod.v2',
      description: 'This role is the permissions to give an engineer in prod',
      permissions: permissionsForAllEngineers,
    },
  };

  // CloudIdentityGroup for all technical staff (everyone listed in eng.jsonnet). Groups are
  // organization-wide, so only deploy in the lead prod cluster.
  local engCloudIdentityGroupName = 'eng-access';
  local engCloudIdentityGroup = gcpLib.cloudIdentityGroup(
    'devtools', appName, engCloudIdentityGroupName, 'Basic engineer access'
  );
  local engCloudIdentityMemberships = if cloudInfo.isProjectLeadProdCluster(cloud) then [
    gcpLib.cloudIdentityGroupMembership(
      'devtools', appName, engCloudIdentityGroupName, u.username
    )
    for u in eng
  ] else [];

  local extraProdRoles = {
  };
  local buildBucketAccess = if cloudInfo.isLeadDevCluster(cloud) then
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'iampolicymember-eng-bazel-data',
        namespace: 'devtools',
      },
      spec: {
        resourceRef: {
          kind: 'StorageBucket',
          external: 'augment-bazel-data',
        },
        bindings: [
          {
            role: 'roles/storage.objectViewer',
            members: [{ member: 'group:%s' % engCloudIdentityGroup.groupEmail }],
          },
        ],
      },
    };
  local prodPoliciesExtraBindings = std.map(function(role)
    {
      role: role,
      members: std.map(function(u)
        {
          member: 'user:%<EMAIL>' % u,
        }, std.get(extraProdRoles, role, [])),
    }, std.objectFields(extraProdRoles));
  local prodPoliciesBindings = [
    {
      role: roleName,
      members: [{ member: 'group:%s' % engCloudIdentityGroup.groupEmail }],
    }
    for roleName in prodRoles
  ] + prodPoliciesExtraBindings;
  local prodPolicies = lib.flatten(if cloudInfo.isProjectLeadProdCluster(cloud) then
    [
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'iampolicymember-eng-cluster-viewer-data',
          namespace: 'devtools',
        },
        spec: {
          resourceRef: {
            kind: 'Project',
            external: 'project/%s' % projectId,
          },
          bindings: prodPoliciesBindings,
        },
      },
      // give engineers read access to the weights bucket.
      if cloud == 'GCP_US_CENTRAL1_PROD' then
        {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMPartialPolicy',
          metadata: {
            name: 'iampolicymember-eng-weights-viewer',
            namespace: 'devtools',
          },
          spec: {
            resourceRef: {
              kind: 'StorageBucket',
              external: 'augment-data',
            },
            bindings: [
              {
                role: 'roles/storage.objectViewer',
                members: [{ member: 'group:%s' % engCloudIdentityGroup.groupEmail }],
              },
            ],
          },
        },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'iampolicymember-eng-prod-glassbreaker',
          namespace: 'devtools',
        },
        spec: {
          resourceRef: {
            kind: 'Project',
            external: 'project/%s' % projectId,
          },
          bindings: [
            {
              role: roleName,
              members: std.filterMap(
                function(u) std.get(u, 'glassbreakerProd', false),
                function(u)
                  {
                    member: 'user:%<EMAIL>' % u.username,
                  },
                eng
              ),
            }
            for roleName in [
              'roles/iap.tunnelResourceAccessor',  // AU-2636
            ]
          ],
        },
      },
    ] else []);
  local devPolicies = if cloudInfo.isLeadDevCluster(cloud) then
    [
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'iampolicymember-eng-iap',
          namespace: 'devtools',
        },
        spec: {
          resourceRef: {
            kind: 'Project',
            external: 'projects/%s' % projectId,
          },
          bindings: [
            {
              role: 'roles/iap.httpsResourceAccessor',
              members: [{ member: 'group:%s' % engCloudIdentityGroup.groupEmail }],
            },
          ],
        },
      },
    ] else [];
  local grantEngAccessEditor =
    [
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'grant-eng-access-editor',
          namespace: 'devtools',
        },
        spec: {
          bindings: [
            {
              members: [{ member: 'group:%s' % engCloudIdentityGroup.groupEmail }],
              role: 'roles/editor',
            },
            {
              members: [{ member: 'group:%s' % engCloudIdentityGroup.groupEmail }],
              role: 'roles/secretmanager.secretAccessor',
            },
            {
              members: [{ member: 'group:%s' % engCloudIdentityGroup.groupEmail }],
              role: 'roles/cloudkms.admin',
            },
          ],
          resourceRef: {
            kind: 'Project',
            external: 'projects/%s' % projectId,
          },
        },
      },
    ];
  lib.flatten([
    if cloudInfo.isProjectLeadProdCluster(cloud) then engCloudIdentityGroup.objects else [],
    if cloudInfo.isLeadDevCluster(cloud) then grantEngAccessEditor else [],
    engCloudIdentityMemberships,
    customRoles,
    prodPolicies,
    devPolicies,
    buildBucketAccess,
  ])

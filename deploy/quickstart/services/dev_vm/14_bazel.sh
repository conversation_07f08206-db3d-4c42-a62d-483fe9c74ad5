#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

wget https://github.com/bazelbuild/bazelisk/releases/download/v1.25.0/bazelisk-linux-amd64
sha256sum -c <<SUM
    fd8fdff418a1758887520fa42da7e6ae39aefc788cf5e7f7bb8db6934d279fc4  bazelisk-linux-amd64
SUM
BAZEL=~/.local/bin/bazel
mv ./bazelisk-linux-amd64 $BAZEL
chmod u+x $BAZEL

# Install buildifier
wget https://github.com/bazelbuild/buildtools/releases/download/v7.1.1/buildifier-linux-amd64
sha256sum -c <<SUM
    54b7f2ce8f22761cfad264416e912056ae9c8645f59eb6583b846b4864a1ee83  buildifier-linux-amd64
SUM
chmod +x ./buildifier-linux-amd64
mv ./buildifier-linux-amd64 ~/.local/bin/buildifier

THIS_DIR=$(dirname "$0")
ROOT_DIR=$THIS_DIR/../../../../

# Install bazel-augment-external
BAZEL_INFO=$($BAZEL info bazel-bin)
echo $BAZEL_INFO

if [ -d $ROOT_DIR/../bazel-augment-external ]; then
	rm -rf $ROOT_DIR/../bazel-augment-external
fi

ln -s $BAZEL_INFO/../../../external $ROOT_DIR/../bazel-augment-external

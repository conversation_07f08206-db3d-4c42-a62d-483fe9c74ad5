local config = import 'deploy/tenants/namespace_configs/staging-defaults.jsonnet';
local apiTokens = import 'deploy/tenants/tokens/internal_users.jsonnet';

config + {
  api_tokens: apiTokens(tenant_name='dogfood-shard'),
  flags: config.flags
         .override('enableClientDataCollection', true)  // upload client events for all tenants
         .override('exportFullData', true)  // the data of all tenants in the namespace is exported
         .override('request_insight_event_retention_days', 365)
         .override('passthroughBigtableProxy', false)
         .override('workingSetEndpoint', 'working-set-svc:50051')
         .override('useBigEmbeddingsSearch', true)
         .override('embeddingsSearchRamGbOverride', 128)
         .override('checkpointIndexerRamGbOverride', 128)
         .override('usePremiumCpuHighmem', true)  // give embeddings search extra memory
         .override('workingSetPublishIndexMetrics', true)  // enable experimental WS metrics
         .override('deployGlean', true)  // glean is deployed in staging
         .override('remoteAgentEnableInstructionFlags', true),  // allow internal users to pass instruction flags
}

// Configuration for all dev namespaces that do not have their own file.
// Dev namespaces that have their own file should import this file.
local flags = import 'deploy/tenants/namespace_configs/flags.jsonnet';
local tenantFlags = import 'deploy/tenants/tenant_flags.jsonnet';

local defaultTenantFlags = tenantFlags
                           // Enable multi-tenancy for all dev tenants
                           .override('multiTenantAllowed', true)
                           // Disable support access control for all dev tenants
                           .override('supportAccessControl', false);

{
  local this = self,

  api_tokens: (import 'deploy/tenants/tokens/internal_users.jsonnet')(tenant_name='augment'),

  // returns the tenant id for a given tenant namespace, central namespace and cloud
  getFallbackTenantId:: function(tenantName, centralNamespace, cloud)
    // the tenant id is not a security feature we rely on so MD5 is fine. We include the namespace, the central namespace
    // and the cloud to ensure uniqueness.
    local tenantId = std.md5(/* manifestJson is FRAGILE - do not copy pattern */ std.manifestJson([tenantName, centralNamespace, cloud]));
    tenantId,

  // this is a representation of tenants.jsonnet for dev environments
  // tenants.jsonnet can impact auth-central
  tenants:: function(namespace, cloud)
    [
      {
        name: 'augment',
        domain: 'augmentcode.com',
        tenantId: this.getFallbackTenantId('augment', namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'ENTERPRISE',
        tenantFlags: defaultTenantFlags
                     // This makes this the default tenant in the support UI
                     .override('defaultSupportTenant', true),
      },
      {
        name: 'other-tenant',
        domain: 'other.augmentcode.com',
        tenantId: this.getFallbackTenantId('other-tenant', namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'ENTERPRISE',
        tenantFlags: defaultTenantFlags
                     // Block Genie request access for this tenant
                     .override('blockGenieRequestAccess', true),
      },
      {
        name: 'vanguard0',
        tenantId: this.getFallbackTenantId('vanguard0', namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'COMMUNITY',
        tenantFlags: defaultTenantFlags,
      },
      {
        name: 'vanguard1',
        tenantId: this.getFallbackTenantId('vanguard1', namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'COMMUNITY',
        tenantFlags: defaultTenantFlags,
      },
      {
        name: 'discovery0',
        tenantId: this.getFallbackTenantId('discovery0', namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'PROFESSIONAL',
        tenantFlags: defaultTenantFlags,
      },
      {
        name: 'discovery1',
        tenantId: this.getFallbackTenantId('discovery1', namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'PROFESSIONAL',
        tenantFlags: defaultTenantFlags,
      },
      {
        name: 'migrated-tenant',
        tenantId: this.getFallbackTenantId('migrated-tenant', namespace, cloud),
        env: 'DEV',
        namespace: '%s-%s' % [namespace, 'migrated'],
        cloud: cloud,
        tier: 'ENTERPRISE',
        other_namespace: namespace,
      },
      {
        name: 'legacy-team0',
        tenantId: this.getFallbackTenantId('legacy-team0', namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'PROFESSIONAL',
        tenantFlags: defaultTenantFlags
                     // Set this as a legacy self-serve team
                     .override('isSelfServeTeam', true)
                     .override('isLegacySelfServeTeam', true)
                     .override('multiTenantAllowed', true)
                     .override('supportTenant', false),
      },
      {
        // The support tenant has the same name as the namespace
        name: namespace,
        tenantId: this.getFallbackTenantId(namespace, namespace, cloud),
        env: 'DEV',
        namespace: namespace,
        cloud: cloud,
        tier: 'ENTERPRISE',
        tenantFlags: defaultTenantFlags
                     // This is the support tenant
                     .override('supportTenant', true),
      },
    ],
  flags: flags
         .withDeployFlag('passthroughBigtableProxy', false)
         .withDeployFlag('useFakeFeatureFlags', true)
         .withDeployFlag('enableApiProxyGrpcDebugEndpoint', true)
         .withDeployFlag('iapJwtVerifierDisabled', true)
         .withDeployFlag('exportFullData', true)
         .withDeployFlag('useSharedDevRequestInsightBigquery', false)
         .withDeployFlag('deployGlean', true)
         .withDeployFlag('exposeInternalModelNames', true)
         .withDeployFlag('remoteAgentEnableInstructionFlags', true)
         .withDeployFlag('userTier', 'ENTERPRISE_TIER')
         .withDeployFlag('tokenExchangeAclCheck', false)
         .withDeployFlag('deployCommitRetrieval', false)
         // Set dynamic feature flags for the fake_feature_flags service in
         // dev/test.
         .withDeployFlag('extraFakeFeatureFlags', flags.extraFakeFeatureFlags + {
    enable_docset_uploads: true,
    connectivity_test_flag: true,
    max_upload_size_bytes: 512 * 1024,
    github_process_dead_letter_queue: false,
    ri_support_database_exporter_process_dead_letter_queue: false,
    vscode_next_edit_min_version: '0.228.0',
    vscode_enable_cpu_profile: true,
    chat_model: 'claude-sonnet-16k-v17-c4-p2-chat',
    chat_raw_output_model: 'claude-sonnet-3-5-16k-v11-4-chat',
    chat_fallback_model: 'claude-sonnet-3-5-16k-v11-4-direct-chat',
    agent_chat_model: 'claude-sonnet-4-0-200k-v5-c4-p2-agent',
    agent_chat_fallback_model: 'claude-sonnet-3-7-200k-v3-direct-c4-p2-agent',
    vscode_share_min_version: '0.0.0',
    intellij_share_min_version: '0.0.0',
    vscode_chat_with_tools_min_version: '0.0.0',
    vscode_agent_mode_min_version: '0.0.0',
    vscode_agent_mode_min_stable_version: '0.0.0',
    vscode_agent_edit_tool: 'str_replace_editor_tool',
    vscode_design_system_rich_text_editor_min_version: '0.0.0',
    embeddings_search_replicate_cache_on_startup: true,
    embeddings_search_checkpoint_ann_indexing_enabled: true,
    embeddings_search_use_indexed_checkpoint_cache: true,
    workingset_persist_to_bigtable: true,
    vscode_external_sources_in_chat_min_version: '0.0.0',
    auth_central_signup_tenant: 'vanguard0,vanguard1',
    auth_central_individual_tenant: 'discovery0,discovery1',
    auth_central_async_ops_process_dead_letter_queue: false,
    auth_central_user_tier_change: true,
    auth_central_allow_similar_signups: true,
    enable_supabase_service: true,
    chat_generate_tool_use_start: true,
    chat_server_enable_error_details_metadata: true,
    agents_use_ide_state_in_prompt: true,
    auth_central_team_management_enabled: true,
    auth_central_login_invitations_enabled: true,
    auth_central_invite_users_to_tenant_plus_allowed: true,
    enable_remote_agents: true,
    max_remote_agents_per_user: 10,
    max_active_remote_agents_per_user: 10,
    vscode_background_agents_min_version: '0.0.0',
    chat_server_max_cjk_char_count: 2000,
    chat_server_block_high_cjk_count: true,
    auth_central_enable_stripe_event_processor: false,
    auth_central_stripe_event_processor_process_dead_letter_queue: false,
    auth_central_enable_billing_event_processor: false,
    auth_central_billing_event_processor_process_dead_letter_queue: false,
    enable_billing_event_ingestion: true,
    team_management: true,
    check_subscription_status: true,
    enforce_usage_credits: true,
    api_proxy_trial_expiration_disclaimer: true,
    api_proxy_inactive_subscription_disclaimer: true,
    memory_classification_on_first_token: true,
    api_proxy_user_suspension_enabled: true,
    use_memory_snapshot_manager: true,
    free_trial_abuse_dry_run: false,
    vscode_generate_commit_message_min_version: '0.0.0',
    enable_model_registry: true,
    model_registry: std.manifestJson({
      'Gemini 2.5 Pro': 'gemini2-5-pro-200k-v3-2-c4-p2-agent',
      'Claude 3.7 Sonnet': 'claude-sonnet-3-7-200k-v3-c4-p2-agent',
    }),
    publish_user_session_events: false,
    open_file_manager_v2_enabled: true,
    enable_prompt_enhancer: true,
    codebase_retrieval_add_line_numbers: true,
    remote_agents_enable_auto_pause: true,
    customer_ui_enable_user_feature_stats: true,
    enable_agent_auto_mode: true,
    api_proxy_chat_heartbeat_stream: true,
    auth_central_windsurf_promotion_enabled: true,
    customer_ui_windsurf_promotion_enabled: true,
    vscode_chat_multimodal_min_version: '0.0.0',
    intellij_chat_multimodal_min_version: '0.0.0',
    allow_client_feature_flag_overrides: true,
  }),
  defaultTenantFlags: defaultTenantFlags,

  // Helpers for overriding Deploy Flags and Fake Feature Flags.
  withDeployFlag:: function(field, val) self + { flags: super.flags.withDeployFlag(field, val) },
  withFakeFeatureFlag:: function(field, val) self + { flags: super.flags.withFakeFeatureFlag(field, val) },
  withDeployFlags:: function(overrides) self + { flags: super.flags.withDeployFlags(overrides) },
  withFakeFeatureFlags:: function(overrides) self + { flags: super.flags.withFakeFeatureFlags(overrides) },
}

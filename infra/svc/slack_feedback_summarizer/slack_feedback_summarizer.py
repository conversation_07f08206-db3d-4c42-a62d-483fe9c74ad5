#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to read Slack messages from a feedback channel, get thread replies,
and generate a summary with <PERSON>.
"""

import argparse
import datetime
import json
import logging
import os
from dataclasses import dataclass
from urllib.parse import urlparse

import structlog
from google.cloud import storage as gcs

from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from infra.svc.slack_feedback_summarizer.slack_utils import SlackClient, SlackMessage
from research.environments import get_eng_secret

logger = structlog.get_logger()


# Reports to generate when calling with --all-channels-report.
_REPORTS = [
    ("feedback-agents-extension", "team-agents"),
    ("feedback-agents", "team-agents"),
    ("feedback-chat-extension", "team-chat"),
    ("feedback-chat", "team-chat"),
    ("feedback-remote-agent", "team-remote-agent"),
]


class JSONLogger:
    def __init__(self, file_path: str):
        self.file_path = file_path
        self._file = open(self.file_path, "w")

    def close(self):
        if self._file:
            self._file.close()
            self._file = None

    def log(self, **entry):
        assert self._file is not None, "JSONLogger not initialized"
        self._file.write(json.dumps(entry))
        self._file.write("\n")


@dataclass
class ThreadSummary:
    """Summary of a thread with its status."""

    original_message: SlackMessage
    summary: str
    status: str  # "unresolved", "resolved"
    type: str  # "bug report", "feature request", "question", "praise", or "other"
    request_id: str | None = None
    share_url: str | None = None


@dataclass
class ThreadSummaryGroup:
    name: str
    category: str
    threads: list[ThreadSummary]


class SlackFeedbackSummarizer:
    """Class to summarize feedback from Slack channels using Claude."""

    def __init__(
        self,
        anthropic_api_key: str,
        claude_model: str = "claude-3-5-haiku-20241022",
        logger: JSONLogger | None = None,
    ):
        """Initialize the summarizer.

        Args:
            anthropic_api_key: Anthropic API key
            claude_model: Claude model to use for summarization
        """
        self.anthropic_client = AnthropicDirectClient(
            api_key=anthropic_api_key,
            model_name=claude_model,
            temperature=0,
            max_output_tokens=1000,
        )
        self.logger = logger

    def get_model_response(self, prompt: str) -> str:
        response_stream = self.anthropic_client.generate_response_stream(
            model_caller="script",
            cur_message=prompt,
        )
        response_text = ""
        for response in response_stream:
            response_text += response.text
        return response_text

    def summarize_thread(self, message: SlackMessage) -> ThreadSummary | None:
        """Summarize a thread using Claude.

        Args:
            message: The thread parent message

        Returns:
            ThreadSummary object with the summary and status
        """
        _seen_names = {}

        def _get_user_name(user_id: str) -> str:
            if user_id not in _seen_names:
                _seen_names[user_id] = f"User{len(_seen_names)}"
            return _seen_names[user_id]

        # Prepare the thread content for Claude
        thread_content = (
            f"Original message from {_get_user_name(message.user)}:\n{message.text}\n"
        )

        if message.replies:
            thread_content += "Replies:\n"
            for reply in message.replies:
                thread_content += f"- {_get_user_name(reply.user)}: {reply.text}\n"

        types = [
            "Bug report",
            "Feature request",
            "Question",
            "Praise",
            "Other",
        ]
        type_list = "\n".join(f"{i}. {t}" for i, t in enumerate(types, 1))

        # Create prompt for Claude
        prompt = f"""
You are analyzing feedback from users who are using Augment Code's AI assistant to write
code. The feedback message may have been posted via an automated bot, in which case,
please focus on the thread content.

Thread content:
{thread_content}

Categorize this feedback into one of these types:
{type_list}

Categorize its status as Unresolved or Resolved.

Identify whether or not it has a request ID (a UUID like 8d5e4732-d845-444e-82af-b5d1165a5b63) or a chat share link like https://app.staging.augmentcode.com/share/7rpAoZogrUE.

Then provide a short 1-2 sentence summary of the issue or feedback.

Provide your response in the following JSON format:
{{"type": <type>, "status": <status>, "request_id": <request-id or null>, "share_url": <url or null>, "summary": "Can't find stop button."}}

DO NOT include any preceeding or succeeding text.
ONLY respond in JSON.
"""
        logger.info(
            "Summarizing thread %s with %d messages",
            message.ts,
            1 + len(message.replies),
        )
        # Get summary from Claude
        try:
            # Call the model
            response = self.get_model_response(prompt)
            # Log the model interaction
            if self.logger:
                self.logger.log(
                    method="summarize_thread",
                    prompt=prompt,
                    response=response,
                    metadata={"thread_id": message.ts},
                )

            fields = json.loads(response)
            if type(fields["type"]) == int:
                fields["type"] = types[fields["type"] - 1]
            return ThreadSummary(
                original_message=message,
                summary=fields["summary"],
                status=fields["status"],
                type=fields["type"],
                request_id=fields["request_id"],
                share_url=fields["share_url"],
            )
        except Exception:
            logger.exception("Error getting summary from Claude")
            return None

    def group_threads_by_theme(
        self, summaries: list[ThreadSummary]
    ) -> list[ThreadSummaryGroup]:
        """Group thread summaries by common themes using Claude.

        Args:
            summaries: List of ThreadSummary objects

        Returns:
            Dictionary with categories as keys and lists of ThreadSummary objects as values
        """
        if not summaries:
            return []

        # Create a numbered list of thread summaries for Claude
        thread_list = ""
        for i, summary in enumerate(summaries, 1):
            thread_list += f"{i}. {summary.summary} (Type: {summary.type}, Status: {summary.status})\n"

        # Create prompt for Claude
        prompt = f"""
        You are analyzing a collection of feedback threads from users who are using
        Augment Code's AI assistant to write code.
        I'll provide you with a numbered list of thread summaries. Your task is to:

        1. Group these threads with common themes like "trouble restoring checkpoint", "model doesn't save memories", "agent keeps failing with 500s".
        2. Categorize each group into one of the following: "ui/ux issue", "model quality issue", "stability issue", "other"

        There can be multiple groups with the same category.

        Here are the thread summaries:
        {thread_list}

        Provide your response in the following JSON format:
        {{
            "groups": [
                {{
                    "name": "<short descriptive name for this group>",
                    "category": "ui/ux issue",
                    "thread_indices": [1, 5, 8]  // List of thread numbers from the input list that belong to this group
                }},
                // More groups...
            ]
        }}

        DO NOT include any preceeding or succeeding text.
        ONLY respond in JSON.
        Make sure every thread is assigned to exactly one group.
        """
        logger.info("Grouping %d threads by theme", len(summaries))

        # Get grouping from Claude
        try:
            response = self.get_model_response(prompt)
            if self.logger:
                self.logger.log(
                    method="group_threads_by_theme",
                    prompt=prompt,
                    response=response,
                    metadata={"summary_count": len(summaries)},
                )
            # Parse the JSON response
            response = json.loads(response)
            return [
                ThreadSummaryGroup(
                    name=group.get("name", "<missing>"),
                    category=group.get("category", "<missing>"),
                    threads=[summaries[i - 1] for i in group["thread_indices"]],
                )
                for group in response.get("groups", [])
                if isinstance(group.get("thread_indices"), list)
                and len(group.get("thread_indices")) > 0
            ]
        except Exception:
            logger.exception("Error grouping threads by theme")
            return []


def generate_notification_message(
    groups: list[ThreadSummaryGroup],
    channel_name: str,
    start_date: str,
    report_filename: str,
) -> tuple[str, list]:
    """Generate a notification message for Slack.

    Args:
        groups: List of thread summary groups
        channel_name: Name of the channel the feedback was collected from
        start_date: Start date of the report period
        report_filename: Filename of the HTML report

    Returns:
        Tuple of (text message, blocks for rich formatting)
    """
    # Count threads by category
    categories = {}
    total_threads = 0
    for group in groups:
        if group.category not in categories:
            categories[group.category] = 0
        categories[group.category] += len(group.threads)
        total_threads += len(group.threads)

    # Create the report URL
    report_url = f"https://webserver.gcp-us1.r.augmentcode.com/feedback-reports/{channel_name}/{report_filename}"

    # Create text version (fallback)
    text = f"Slack Feedback Summary for #{channel_name}\n"
    text += f"Total feedback threads: {total_threads}\n"
    text += "Categories:\n"
    for category, count in categories.items():
        text += f"• {category}: {count}\n"
    text += f"View full report: {report_url}"

    # Create blocks for rich formatting
    blocks = [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": f"Slack Feedback Summary for #{channel_name}",
                "emoji": True,
            },
        },
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Period:* {start_date}\n*Total feedback threads:* {total_threads}",
            },
        },
        {"type": "section", "text": {"type": "mrkdwn", "text": "*Categories:*"}},
    ]

    # Add category list
    category_items = []
    for category, count in categories.items():
        category_items.append(f"• *{category}:* {count}")

    blocks.append(
        {
            "type": "section",
            "text": {"type": "mrkdwn", "text": "\n".join(category_items)},
        }
    )

    # Add link to report
    blocks.append(
        {
            "type": "section",
            "text": {"type": "mrkdwn", "text": f"<{report_url}|View full report>"},
        }
    )

    return text, blocks


def generate_html_report(
    groups: list[ThreadSummaryGroup],
    start_date: str,
    end_date: str,
) -> str:
    """Generate an HTML report from the thread summary groups."""
    # Start building the HTML
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slack Feedback Summary - {start_date} to {end_date}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="report.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Slack Feedback Summary - {start_date} to {end_date}</h1>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title">Summary</h2>
                        <p>Total groups: {len(groups)}</p>
                        <p>Total threads: {sum(len(group.threads) for group in groups)}</p>

                        <div class="mt-3">
                            <h3>Categories</h3>
                            <ul class="list-group">"""

    # Count threads by category
    categories = {}
    for group in groups:
        if group.category not in categories:
            categories[group.category] = 0
        categories[group.category] += len(group.threads)

    # Add category counts to the summary
    for category, count in categories.items():
        html += f"""
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {category}
                                    <span class="badge bg-primary rounded-pill">{count}</span>
                                </li>"""

    html += """
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <h2>Table of Contents</h2>
                <div class="list-group">"""

    # Add table of contents
    for i, group in enumerate(groups):
        group_id = f"group-{i}"
        html += f"""
                    <a href="#{group_id}" class="list-group-item list-group-item-action">
                        {group.name} <span class="badge bg-secondary">{group.category}</span>
                        <span class="badge bg-primary rounded-pill">{len(group.threads)}</span>
                    </a>"""

    html += """
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <h2>Feedback Groups</h2>"""

    # Add each group and its threads
    for i, group in enumerate(groups):
        group_id = f"group-{i}"
        html += f"""
                <div class="card mb-4" id="{group_id}">
                    <div class="card-header bg-category-{group.category.replace('/', '-').replace(' ', '-')}">
                        <h3 class="mb-0">
                            <button class="btn btn-link text-decoration-none text-dark" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{group_id}">
                                {group.name} <span class="badge bg-secondary">{group.category}</span>
                                <span class="badge bg-primary rounded-pill">{len(group.threads)}</span>
                            </button>
                        </h3>
                    </div>
                    <div id="collapse-{group_id}" class="collapse show">
                        <div class="card-body">"""

        # Add each thread in the group
        for j, thread in enumerate(group.threads):
            thread_id = f"{group_id}-thread-{j}"
            status_class = (
                "bg-success" if thread.status.lower() == "resolved" else "bg-danger"
            )

            # Determine type badge color
            type_class = "bg-secondary"
            if thread.type.lower() == "bug report":
                type_class = "bg-danger"
            elif thread.type.lower() == "feature request":
                type_class = "bg-primary"
            elif thread.type.lower() == "question":
                type_class = "bg-info"
            elif thread.type.lower() == "praise":
                type_class = "bg-success"

            html += f"""
                            <div class="card mb-3 thread-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="badge {status_class}">{thread.status}</span>
                                        <span class="badge {type_class}">{thread.type}</span>
                                    </div>
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-{thread_id}">
                                        Details
                                    </button>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">{thread.summary}</p>

                                    <div class="collapse" id="collapse-{thread_id}">
                                        <div class="card card-body mt-3 bg-light">
                                            <h5>Original Message</h5>
                                            <p>{thread.original_message.text}</p>

                                            <h5 class="mt-3">Thread Details</h5>
                                            <ul class="list-group list-group-flush">"""

            # Add request ID if available
            if thread.request_id:
                html += f"""
                                                <li class="list-group-item bg-light">Request ID: <code>{thread.request_id}</code></li>"""

            # Add share URL if available
            if thread.share_url:
                html += f"""
                                                <li class="list-group-item bg-light">Share URL: <a href="{thread.share_url}" target="_blank">{thread.share_url}</a></li>"""

            # Add permalink to Slack
            if hasattr(thread.original_message, "permalink"):
                html += f"""
                                                <li class="list-group-item bg-light">Slack Thread: <a href="{thread.original_message.permalink}" target="_blank">View in Slack</a></li>"""

            # Add reply count
            if (
                hasattr(thread.original_message, "replies")
                and thread.original_message.replies
            ):
                html += f"""
                                                <li class="list-group-item bg-light">Replies: {len(thread.original_message.replies)}</li>"""

            html += """
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>"""

        html += """
                        </div>
                    </div>
                </div>"""

    # Close the HTML
    html += """
            </div>
        </div>
    </div>

    <script>
        // JavaScript to handle collapsible sections
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to all links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 70,
                            behavior: 'smooth'
                        });

                        // Expand the target group if it's collapsed
                        const collapseElement = document.querySelector(targetId + ' .collapse');
                        if (collapseElement && !collapseElement.classList.contains('show')) {
                            const bsCollapse = new bootstrap.Collapse(collapseElement);
                            bsCollapse.show();
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>"""

    return html


def send_slack_notification(
    slack_client: SlackClient,
    notify_channel: str | None,
    channel_name: str,
    groups: list[ThreadSummaryGroup],
    start_date_str: str,
    report_filename: str,
) -> None:
    """Send notification to Slack channel if configured."""
    if not notify_channel:
        return

    logger.info(f"Sending notification to channel: {notify_channel}")
    try:
        # Get the channel ID for the notify channel
        notify_channel_id = slack_client.get_channel_id(notify_channel)

        # Extract just the filename if it's a path
        if "/" in report_filename:
            report_filename = report_filename.split("/")[-1]

        text, blocks = generate_notification_message(
            groups=groups,
            channel_name=channel_name,
            start_date=start_date_str,
            report_filename=report_filename,
        )

        # Send the message
        success = slack_client.send_message(
            channel_id=notify_channel_id,
            text=text,
            blocks=blocks,
        )

        if success:
            logger.info(f"Successfully sent notification to #{notify_channel}")
        else:
            logger.error(f"Failed to send notification to #{notify_channel}")

    except ValueError as e:
        logger.error(f"Error sending notification: {e}")
    except Exception as e:
        logger.exception(f"Unexpected error sending notification: {e}")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Summarize Slack feedback messages using Claude"
    )

    ### Behavior

    parser.add_argument(
        "--all-channels-report",
        action="store_true",
        help="Generate a report for all channels. Ignores the --channel flag.",
    )
    parser.add_argument(
        "--channel",
        default="feedback-agents-extension",
        help="Slack channel name (default: feedback-agents-extension). Ignored if --all-channels-report is set.",
    )
    parser.add_argument(
        "--notify-channel",
        help="Slack channel to notify if provided.",
    )

    ### Timerange

    parser.add_argument(
        "--start-date",
        required=False,
        default=None,
        help="Start date in YYYY-MM-DD or YYYYMMDD format (default: is calculated from --end-date and --duration-days). The start time will always be 00:00:00 on the start date.",
    )
    parser.add_argument(
        "--end-date",
        default=None,
        help="End date in YYYY-MM-DD or YYYYMMDD format (default: previous calendar day). The end time will always be 23:59:59 on the end date.",
    )
    parser.add_argument(
        "--duration-days",
        default=7,
        type=int,
        help="Default timespan in days of the report (default: 7 to use when either the --start-date or --end-date are unspecified.",
    )

    ### Output (HTML and Logs)

    # Default output filename using current date in yyyy-mm-dd.html format
    default_output = f"{datetime.datetime.now().strftime('%Y-%m-%d')}.html"
    parser.add_argument(
        "--output",
        default=default_output,
        help=f"Output HTML file (default: {default_output}). With --all-channels-report, this is used as a prefix.",
    )
    parser.add_argument(
        "--enable-logging",
        action="store_true",
        default=True,
        help="Enable logging of model interactions (default: True)",
    )
    parser.add_argument(
        "--log-dir",
        default="logs",
        help="Directory to store log files (default: logs)",
    )

    ### Auth

    parser.add_argument(
        "--slack-token",
        default=get_eng_secret("research-slack-search-token"),
        help="Slack API token (can also be set via SLACK_TOKEN env var)",
    )
    parser.add_argument(
        "--anthropic-api-key",
        default=get_eng_secret("seal-research-anthropic-key"),
        help="Anthropic API key (can also be set via ANTHROPIC_API_KEY env var)",
    )
    parser.add_argument(
        "--gcp-project",
        help="GCP project ID to use when writing to GCS",
    )

    args = parser.parse_args()

    # Validate required arguments
    if not args.slack_token:
        parser.error(
            "Slack token is required (use --slack-token or set SLACK_TOKEN env var)"
        )

    if not args.anthropic_api_key:
        parser.error(
            "Anthropic API key is required (use --anthropic-api-key or set ANTHROPIC_API_KEY env var)"
        )

    # Parse dates
    try:
        args.start_date, args.end_date = parse_and_calc_dates(
            args.start_date, args.end_date, args.duration_days
        )
    except Exception as e:
        parser.error(f"Error parsing dates: {e}")

    # Read slack token from file if starting with '/'
    if args.slack_token.startswith("/"):
        logger.info(f"Reading Slack token from file: {args.slack_token}")
        with open(args.slack_token, "r") as f:
            args.slack_token = f.read().strip()

    return args


def parse_date(
    spec: datetime.datetime | datetime.date | str | None,
) -> datetime.datetime | None:
    """Parse a date string in YYYY-MM-DD or YYYYMMDD format."""
    if spec is None:
        return None
    if isinstance(spec, datetime.datetime):
        return spec
    if isinstance(spec, datetime.date):
        return datetime.datetime.combine(spec, datetime.time.min)
    try:
        return datetime.datetime.strptime(spec, "%Y-%m-%d")
    except ValueError:
        try:
            return datetime.datetime.strptime(spec, "%Y%m%d")
        except ValueError:
            raise ValueError(f"Invalid date format: {spec}")


def parse_and_calc_dates(
    start_spec: str | None = None,
    end_spec: str | None = None,
    duration_days: int = 7,
    now: datetime.datetime | None = None,
) -> tuple[datetime.datetime, datetime.datetime]:
    """Parse and calculate start and end dates.

    Args:
        start_spec: Start date in YYYY-MM-DD or YYYYMMDD format
        end_spec: End date in YYYY-MM-DD or YYYYMMDD format. Defaults to the previous calendar day.
        duration: Default timespan of the report (default: 1w) to use when either the start_date or end_date are unspecified.
        now: The time used for now (defaults to datetime.now()).

    Returns:
        start_date, end_date: Start and end dates as datetime objects

    Raises:
        ValueError: If the input formats are incorrect.
    """
    # Parse initial dates from args, each may still be None after.
    start_date = parse_date(start_spec)
    end_date = parse_date(end_spec)

    # Get current date and time.
    if now is None:
        now = datetime.datetime.now()

    # When neither start nor end are provided, default end to previous calendar day.
    if start_date is None and end_date is None:
        end_date = now - datetime.timedelta(days=1)

    # Defaults for start_date and end_date based on duration_days.
    if start_date is None:
        assert end_date is not None
        start_date = end_date - datetime.timedelta(days=duration_days)
    if end_date is None:
        assert start_date is not None
        end_date = start_date + datetime.timedelta(days=duration_days)

    # Snap start_date and end_date to very beginning and and very end of the day, respectively.
    start_date = datetime.datetime.combine(
        start_date.date(), datetime.time.min, tzinfo=start_date.tzinfo
    )
    end_date = datetime.datetime.combine(
        end_date.date(), datetime.time.max, tzinfo=end_date.tzinfo
    )

    return start_date, end_date


def run_summarizer(
    # Slack Channels
    channel: str,
    slack_token: str,
    anthropic_api_key: str,
    enable_logging: bool,
    log_dir: str,
    start_date: datetime.datetime,
    end_date: datetime.datetime,
    output: str,
    gcp_project: str | None = None,
    notify_channel: str | None = None,
) -> None:
    """Run the slack feedback summarizer."""
    structlog.configure(
        wrapper_class=structlog.make_filtering_bound_logger(logging.INFO)
    )

    slack_client = SlackClient(token=slack_token)
    channel_id = slack_client.get_channel_id(channel)

    # Initialize summarizer
    # Ensure log directory exists
    if enable_logging:
        os.makedirs(log_dir, exist_ok=True)

    log_file_path = f"{log_dir}/slack_feedback_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.jsonl"
    model_logger = None

    # Create logger if logging is enabled
    if enable_logging:
        model_logger = JSONLogger(log_file_path)

    summarizer = SlackFeedbackSummarizer(
        anthropic_api_key=anthropic_api_key, logger=model_logger
    )

    # Step 1: Get the relevant messages.
    logger.info(
        "Getting messages from Slack from %s to %s...",
        start_date,
        end_date,
    )
    messages = slack_client.get_messages(
        channel_id=channel_id,
        start_date=start_date,
        end_date=end_date,
    )
    logger.info("Found %d threads", len(messages))

    # Filter the ones with positive feedback to save on cost.
    messages = [m for m in messages if "Rating: `POSITIVE`" not in m.text]
    logger.info(
        "Filtering to %d threads after ignoring positive feedback.", len(messages)
    )

    # If no messages found, list available channels with messages
    if len(messages) == 0:
        logger.info("No messages found in the specified date range.")
        return

    # Summarize threads
    logger.info("Summarizing threads...")
    summaries = []
    for i, message in enumerate(messages):
        logger.info(f"Summarizing thread {i + 1}/{len(messages)}...")
        summary = summarizer.summarize_thread(message)
        if summary:
            summaries.append(summary)
    logger.info(f"Successfully summarized {len(summaries)} threads")

    # Group summaries by theme
    logger.info("Grouping threads by theme...")
    groups = summarizer.group_threads_by_theme(summaries)
    logger.info(f"Found {len(groups)} groups")

    # Generate HTML report
    logger.info("Generating HTML report...")
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    logger.info(f"Generating report for period: {start_date_str} to {end_date_str}")
    html = generate_html_report(groups, start_date_str, end_date_str)

    # Write to File or GCS.
    if output.startswith("gs://"):
        parsed = urlparse(output)
        client = gcs.Client(project=gcp_project)
        bucket = client.bucket(parsed.netloc)
        blob = bucket.blob(parsed.path.lstrip("/"))
        blob.upload_from_string(html, content_type="text/html")
    else:
        os.makedirs(os.path.dirname(output), exist_ok=True)
        with open(output, "w") as f:
            f.write(html)

    logger.info(f"Report saved to {output}")

    # Send notification to Slack if notify-channel is provided
    if notify_channel:
        send_slack_notification(
            slack_client, notify_channel, channel, groups, start_date_str, output
        )


def main():
    """Main function."""
    args = parse_args()

    if not args.all_channels_report:
        logger.info(f"Generating report for channel {args.channel}: {args.output}.")
        run_summarizer(
            # Slack Channels
            channel=args.channel,
            notify_channel=args.notify_channel,
            # Timerange
            start_date=args.start_date,
            end_date=args.end_date,
            # Output (HTML and Logs)
            output=args.output,
            enable_logging=args.enable_logging,
            log_dir=args.log_dir,
            # Auth
            slack_token=args.slack_token,
            anthropic_api_key=args.anthropic_api_key,
            gcp_project=args.gcp_project,
        )
        return

    for channel, notify_channel in _REPORTS:
        output_dir = args.output + "/" + channel
        report_filename = f"{datetime.datetime.now().strftime('%Y-%m-%d')}.html"
        report_path = f"{output_dir}/{report_filename}"
        latest_path = f"{output_dir}/latest.html"

        logger.info(f"Generating report for channel {channel}: {report_path}.")

        run_summarizer(
            # Slack Channels
            channel=channel,
            notify_channel=notify_channel,
            # Timerange
            start_date=args.start_date,
            end_date=args.end_date,
            # Output (HTML and Logs)
            output=report_path,
            enable_logging=args.enable_logging,
            log_dir=args.log_dir,
            # Auth
            slack_token=args.slack_token,
            anthropic_api_key=args.anthropic_api_key,
            gcp_project=args.gcp_project,
        )

        if output_dir.startswith("gs://"):
            parsed = urlparse(output_dir)
            client = gcs.Client(project=args.gcp_project)
            bucket = client.bucket(parsed.netloc)

            # Copy the report file to latest.html
            source_blob = bucket.blob(f"{parsed.path.lstrip('/')}/{report_filename}")
            latest_blob = bucket.blob(f"{parsed.path.lstrip('/')}/latest.html")
            latest_blob.rewrite(source_blob)
        else:
            if os.path.exists(report_path):
                if os.path.exists(latest_path):
                    os.remove(latest_path)
                os.symlink(report_filename, latest_path)


if __name__ == "__main__":
    main()

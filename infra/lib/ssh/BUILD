load("//tools/bzl:go.bzl", "go_library", "go_test")

package(default_visibility = ["//infra:internal"])

go_library(
    name = "ssh",
    srcs = [
        "authorized_keys.go",
        "hostkeys.go",
        "keygen.go",
        "ssh.go",
        "testutil.go",
    ],
    importpath = "github.com/augmentcode/augment/infra/lib/ssh",
    deps = [
        "@com_github_google_go_cmp//cmp",
        "@org_golang_x_crypto//ssh",
    ],
)

go_test(
    name = "ssh_test",
    srcs = [
        "authorized_keys_test.go",
        "hostkeys_test.go",
    ],
    embed = [":ssh"],
    gotags = ["testing"],
    deps = ["@com_github_google_go_cmp//cmp"],
)

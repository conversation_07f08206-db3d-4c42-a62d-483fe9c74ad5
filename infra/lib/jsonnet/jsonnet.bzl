load("//tools/bzl:go.bzl", "go_library")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", _jsonnet_library = "jsonnet_library", _jsonnet_to_json = "jsonnet_to_json")

jsonnet_to_json = _jsonnet_to_json

def jsonnet_library(name, srcs, deps = None, goembed = True, importpath = True, visibility = None):
    deps = deps or []

    fg_name = name + "_files"
    jl_name = name
    gen_name = name + "_go_embedfs_go"
    go_fname = name + ".embed.go"
    go_name = name + "_go_embedfs"

    fg_deps = [d + "_files" for d in deps]
    jl_deps = [d for d in deps]
    go_deps = [d + "_go_embedfs" for d in deps]

    # Export raw sources as both individual files and a filegroup.
    native.exports_files(srcs, visibility = visibility)
    native.filegroup(
        name = fg_name,
        srcs = srcs + fg_deps,
        visibility = visibility,
    )

    # Upstream jsonnet_library() using `name` so that this target will work with the upstream @rules_jsonnet.
    _jsonnet_library(
        name = jl_name,
        srcs = [":" + fg_name],
        deps = jl_deps,
        visibility = visibility,
    )

    if not goembed:
        return

    go_imports = []
    for dep in go_deps:
        if dep.startswith("//"):
            # Handle "//path/to/pkg:rule" or "//path/to/pkg"
            parts = dep.split(":")
            package_path = parts[0][2:]  # Remove "//"
            go_imports.append(package_path)
        elif dep.startswith(":"):
            # Handle ":localrule" - use current package
            go_imports.append(native.package_name())

    # Generate a .go file which embeds the jsonnet into a global var called `FS` and
    # registrs it with the global jsonnet.VM.
    native.genrule(
        name = gen_name,
        outs = [go_fname],
        visibility = ["//visibility:private"],
        cmd_bash = """
        {{
            printf 'package %s\n' "{package_basename}"
            printf '\n'
            printf 'import (\n'
            printf '\t"embed"\n'
            printf '\n'
            printf '\t"github.com/augmentcode/augment/infra/lib/jsonnet"\n'
            if [[ "{go_imports}" ]]; then
                printf '\n'
                for imp in {go_imports}; do
                    printf '\t_ "github.com/augmentcode/augment/%s"\n' "$$imp"
                done
            fi
            printf ')\n'
            printf '\n'
            printf '//go:embed *.jsonnet\n'
            printf 'var FS embed.FS\n'
            printf '\n'
            printf 'func init() {{\n'
            printf '\tjsonnet.GlobalEmbedBindFS("/%s", FS)\n' "{package_name}"
            printf '}}\n'
        }} > "$@"
        """.format(
            package_name = native.package_name(),
            package_basename = native.package_name().split("/")[-1],
            go_imports = " ".join(sorted({imp: None for imp in go_imports}.keys())),
        ),
    )

    # Embedable go_library target with the above.
    go_library(
        name = go_name,
        srcs = [":" + gen_name],
        embedsrcs = srcs,
        importpath = "" if not importpath else "github.com/augmentcode/augment/" + native.package_name(),
        deps = ["//infra/lib/jsonnet"] + go_deps,
        visibility = visibility,
    )

{
  //////////////////////////////////////////////////////////////////////////////
  //
  // K8s Abstract Base Templates
  //

  BaseLabels:: {},

  BaseObject:: {
    // BaseObject is a base k8s object. It uplevels 'name' to the top level, and
    // enforces apiVersion, kind, and name.
    local o = self,
    name:: error 'BaseObject.name required',
    apiVersion: error 'BaseObject.apiVersion required',
    kind: error 'BaseObject.kind required',
    metadata+: $.Metadata + {
      name: o.name,
      labels+: $.BaseLabels,
    },

    // Need by some reference fields.
    apiGroup:: std.splitLimit(self.apiVersion, '/', 1)[0],
  },

  ClusterObject:: $.BaseObject + {
    // A cluster-scoped object.
  },

  Object:: $.BaseObject + {
    // A namespace-scoped object.
    local o = self,
    namespace:: null,  // TODO(mattm): Consider making namespace required.
    metadata+: std.prune({
      namespace: o.namespace,
    }),
  },

  BaseSpec:: {
    // BaseSpec is a common base for for non-object "spec" types.
  },

  BaseTemplateSpec:: {
    // BaseTemplateSpec is used for a spec that contains metadata.
    metadata+: {
      labels+: $.BaseLabels,
    },
  },

  //////////////////////////////////////////////////////////////////////////////
  //
  // K8s Common Templates
  //

  Metadata:: {
    name: error 'Metadata.name required',
  },

  MetadataTemplate:: {},

  LabelSelector:: {},
}

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "chunking",
    srcs = [
        "char_level_chunker.py",
        "chunking.py",
        "docset_chunkers.py",
        "line_level_chunker.py",
        "line_level_chunker_v2.py",
        "pre_chunked_chunker.py",
        "signature_chunker.py",
        "smart_line_chunker.py",
        "transform_chunker.py",
    ],
    visibility = [
        "//models:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        requirement("pandas"),
        requirement("grpcio"),
        requirement("structlog"),
        "//base/executor:robust_executor",
        "//base/prompt_format:common",
        "//base/ranges:string_utils",
        "//base/retrieval/chunking",
        "//base/retrieval/chunking/utils:jupyternb_conversion",
        "//base/static_analysis:common",
        "//base/static_analysis:signature_utils",
        "//base/static_analysis:usage_analysis",
    ],
)

py_library(
    name = "commit_summary_chunker",
    srcs = [
        "commit_summary_chunker.py",
    ],
    visibility = [
        "//models:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":chunking",
        requirement("structlog"),
        "//base/third_party_clients:clients",
    ],
)

pytest_test(
    name = "pre_chunked_chunker_test",
    size = "small",
    srcs = [
        "conftest.py",
        "pre_chunked_chunker_test.py",
    ],
    data = [
        "test_data/pre_chunked.jsonl",
    ],
    deps = [
        ":chunking",
    ],
)

pytest_test(
    name = "docset_chunkers_test",
    size = "small",
    srcs = [
        "conftest.py",
        "docset_chunkers_test.py",
    ],
    data = [
        "test_data/pre_chunked.jsonl",
    ],
    deps = [
        ":chunking",
    ],
)

pytest_test(
    name = "line_level_chunker_test",
    size = "small",
    srcs = [
        "conftest.py",
        "line_level_chunker_test.py",
    ],
    deps = [
        ":chunking",
    ],
)

pytest_test(
    name = "transform_chunker_test",
    size = "small",
    srcs = [
        "conftest.py",
        "transform_chunker_test.py",
    ],
    data = [
        "test_data/transform_chunker_input.ipynb",
        "test_data/transform_chunker_invalid_input.ipynb",
        "test_data/transform_chunker_output.py",
        "test_data/transform_chunker_preconverted_input.py",
    ],
    deps = [
        ":chunking",
    ],
)

pytest_test(
    name = "line_level_chunker_v2_test",
    size = "small",
    srcs = [
        "conftest.py",
        "line_level_chunker_v2_test.py",
    ],
    deps = [
        ":chunking",
    ],
)

pytest_test(
    name = "char_level_chunker_test",
    size = "small",
    srcs = [
        "char_level_chunker_test.py",
        "conftest.py",
    ],
    deps = [
        ":chunking",
    ],
)

pytest_test(
    name = "signature_chunker_test",
    size = "small",
    srcs = [
        "conftest.py",
        "signature_chunker_test.py",
    ],
    deps = [
        ":chunking",
        requirement("structlog"),
    ],
)

pytest_test(
    name = "smart_line_chunker_test",
    size = "small",
    srcs = [
        "conftest.py",
        "smart_line_chunker_test.py",
    ],
    deps = [
        ":chunking",
        "//base/test_utils:testing_utils",
        requirement("structlog"),
    ],
)

pytest_test(
    name = "chunking_test",
    size = "small",
    srcs = [
        "chunking_test.py",
        "conftest.py",
    ],
    deps = [
        ":chunking",
    ],
)

pytest_test(
    name = "commit_summary_chunker_test",
    size = "small",
    srcs = [
        "commit_summary_chunker_test.py",
        "conftest.py",
    ],
    deps = [
        ":commit_summary_chunker",
    ],
)

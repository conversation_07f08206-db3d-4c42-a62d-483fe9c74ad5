import { AggregateCheckpointManager } from "./checkpoint/aggregate-checkpoint-manager";
import { IWebviewMessageConsumer } from "../webview-messages/webview-messages-broker";
import { getLogger } from "../logging";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "../webview-messages/common-webview-messages";
import {
  AgentWebViewMessageType,
  ChatGetAgentEditListResponse,
  ChatGetAgentEditListRequest,
  AgentSetCurrentConversation,
  AgentMigrateConversationId,
  GetAgentEditChangesByRequestIdRequest,
  GetAgentEditChangesByRequestIdResponse,
  RevertToTimestamp,
  ReportAgentSessionEvent,
  ChatReviewAgentFileMessage,
  ReportAgentRequestEvent,
  SetHasEverUsedAgent,
  SetHasEverUsedRemoteAgent,
  GetAgentEditContentsByRequestIdRequest,
  GetAgentEditContentsByRequestIdResponse,
} from "../webview-messages/message-types/agent-messages";
import { QualifiedPathName } from "../workspace/qualified-path-name";
import { getAgentSessionEventReporter } from "../metrics/agent-session-event-reporter";
import { getClientActions } from "../client-interfaces/client-actions";
import { calculateDiffPosition } from "./diff-position-calculator";
import { getAgentRequestEventReporter } from "../metrics/agent-request-event-reporter";
import {
  getStateForSidecar,
  PluginStateNamespace,
  PluginStateScope,
} from "../client-interfaces/plugin-state";
import { AgentGlobalStateKeys } from "./agent-edit-types";
import { AggregateCheckpointInfo } from "./checkpoint/checkpoint-types";

export class AgentWebviewMessageHandler
  implements IWebviewMessageConsumer<AgentWebViewMessageType>
{
  private _logger = getLogger("AgentWebviewMessageHandler");

  public readonly supportedTypes = AgentWebViewMessageType;

  constructor(
    private readonly _checkpointManager: AggregateCheckpointManager,
  ) {}

  public async handle(
    msg: WebViewMessage<AgentWebViewMessageType>,
    postMessage: (
      msg: WebViewMessage<AgentWebViewMessageType | CommonWebViewMessageType>,
    ) => void,
  ): Promise<void> {
    switch (msg.type) {
      case AgentWebViewMessageType.getEditListRequest: {
        const webviewMsg = msg as ChatGetAgentEditListRequest;
        const response = await this._getAgentEditList(webviewMsg);
        postMessage(response);
        break;
      }
      case AgentWebViewMessageType.setCurrentConversation: {
        const webviewMsg = msg as AgentSetCurrentConversation;
        this._checkpointManager.setCurrentConversation(
          webviewMsg.data.conversationId,
        );
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.migrateConversationId: {
        const webviewMsg = msg as AgentMigrateConversationId;
        await this._checkpointManager.migrateConversationId(
          webviewMsg.data.oldConversationId,
          webviewMsg.data.newConversationId,
        );
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.getEditChangesByRequestIdRequest: {
        const webviewMsg = msg as GetAgentEditChangesByRequestIdRequest;
        const response = await this._getAgentEditChangesByRequestId(webviewMsg);
        postMessage(response);
        break;
      }
      case AgentWebViewMessageType.revertToTimestamp: {
        const webviewMsg = msg as RevertToTimestamp;
        if (webviewMsg.data.qualifiedPathNames?.length) {
          await Promise.all(
            webviewMsg.data.qualifiedPathNames.map(async (p): Promise<void> => {
              await this._checkpointManager.revertDocumentToTimestamp(
                QualifiedPathName.from(p),
                webviewMsg.data.timestamp,
              );
            }),
          );
        } else {
          await this._checkpointManager.revertToTimestamp(
            webviewMsg.data.timestamp,
          );
        }
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.chatAgentEditAcceptAll: {
        // Instead of clearing checkpoints, we'll just update the baseline timestamp
        // This is handled on the client side by updating the conversation's baselineTimestamp
        // We don't need to do anything here anymore, but we keep the handler for backward compatibility
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.reportAgentSessionEvent: {
        getAgentSessionEventReporter().reportEvent(
          (msg as ReportAgentSessionEvent).data,
        );
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.reportAgentRequestEvent: {
        getAgentRequestEventReporter().reportEvent(
          (msg as ReportAgentRequestEvent).data,
        );
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.chatReviewAgentFile: {
        const webviewMsg = msg as ChatReviewAgentFileMessage;
        await this._showAgentReview(webviewMsg);
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.checkHasEverUsedAgent: {
        const hasUsedAgent = await getStateForSidecar().getValue<boolean>(
          PluginStateNamespace.agent,
          AgentGlobalStateKeys.hasEverUsedAgent,
          PluginStateScope.global,
        );
        postMessage({
          type: AgentWebViewMessageType.checkHasEverUsedAgentResponse,
          data: hasUsedAgent ?? false,
        });
        break;
      }
      case AgentWebViewMessageType.setHasEverUsedAgent: {
        const webviewMsg = msg as SetHasEverUsedAgent;
        await getStateForSidecar().setValue<boolean>(
          PluginStateNamespace.agent,
          AgentGlobalStateKeys.hasEverUsedAgent,
          webviewMsg.data,
          PluginStateScope.global,
        );
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.checkHasEverUsedRemoteAgent: {
        const hasUsedRemoteAgent = await getStateForSidecar().getValue<boolean>(
          PluginStateNamespace.agent,
          AgentGlobalStateKeys.hasEverUsedRemoteAgent,
          PluginStateScope.global,
        );
        postMessage({
          type: AgentWebViewMessageType.checkHasEverUsedRemoteAgentResponse,
          data: hasUsedRemoteAgent ?? false,
        });
        break;
      }
      case AgentWebViewMessageType.setHasEverUsedRemoteAgent: {
        const webviewMsg = msg as SetHasEverUsedRemoteAgent;
        await getStateForSidecar().setValue<boolean>(
          PluginStateNamespace.agent,
          AgentGlobalStateKeys.hasEverUsedRemoteAgent,
          webviewMsg.data,
          PluginStateScope.global,
        );
        postMessage({ type: CommonWebViewMessageType.empty });
        break;
      }
      case AgentWebViewMessageType.getAgentEditContentsByRequestId: {
        const webviewMsg = msg as GetAgentEditContentsByRequestIdRequest;
        const response =
          await this._getAgentEditContentsByRequestId(webviewMsg);
        postMessage(response);
        break;
      }
    }
  }

  private async _getAgentEditContentsByRequestId(
    message: GetAgentEditContentsByRequestIdRequest,
  ): Promise<GetAgentEditContentsByRequestIdResponse> {
    const checkpoint = await this._checkpointManager.getCheckpointByRequestId(
      message.data.requestId,
    );
    const file = checkpoint?.files.at(0);
    if (
      checkpoint === undefined ||
      file === undefined ||
      checkpoint.files.length !== 1
    ) {
      return {
        type: AgentWebViewMessageType.getAgentEditContentsByRequestIdResponse,
        data: undefined,
      };
    }
    return {
      type: AgentWebViewMessageType.getAgentEditContentsByRequestIdResponse,
      data: {
        originalCode: file.changeDocument.originalCode,
        modifiedCode: file.changeDocument.modifiedCode,
      },
    };
  }

  /**
   * Retrieves a list of all tracked file edits made by the agent.
   * For each tracked file, it includes the qualified path name and line changes
   * if there are at least two checkpoints available.
   *
   * Line changes include:
   * - Total number of added lines
   * - Total number of removed lines
   * - Detailed changes information
   *
   * @returns Promise<ChatAgentEditListResponse> containing an array of tracked file edits
   */
  private _getAgentEditList = async (
    message: ChatGetAgentEditListRequest,
  ): Promise<ChatGetAgentEditListResponse> => {
    // Use the provided fromTimestamp or default to 0
    // In the client side, we'll use the conversation's baselineTimestamp if not explicitly provided
    const { fromTimestamp = 0, toTimestamp } = message.data;
    const ckptInfo: AggregateCheckpointInfo =
      await this._checkpointManager.getAggregateCheckpoint({
        minTimestamp: fromTimestamp,
        maxTimestamp: toTimestamp,
      });
    return {
      type: AgentWebViewMessageType.getEditListResponse,
      data: {
        edits: ckptInfo.files.map((file) => ({
          qualifiedPathName: file.changeDocument.filePath,
          changesSummary: file.changesSummary,
        })),
      },
    };
  };

  private _getAgentEditChangesByRequestId = async (
    message: GetAgentEditChangesByRequestIdRequest,
  ): Promise<GetAgentEditChangesByRequestIdResponse> => {
    const checkpoint = await this._checkpointManager.getCheckpointByRequestId(
      message.data.requestId,
    );
    if (checkpoint !== undefined) {
      return {
        type: AgentWebViewMessageType.getEditChangesByRequestIdResponse,
        data: checkpoint.files.at(0)?.changesSummary,
      };
    }

    return {
      type: AgentWebViewMessageType.getEditChangesByRequestIdResponse,
      data: undefined,
    };
  };

  private _showAgentReview = async (
    message: ChatReviewAgentFileMessage,
  ): Promise<WebViewMessage<CommonWebViewMessageType.empty>> => {
    const qualifiedPathName = QualifiedPathName.from(
      message.data.qualifiedPathName,
    );
    const ckptInfo =
      await this._checkpointManager.getAggregateCheckpointForFile(
        qualifiedPathName,
        {
          minTimestamp: message.data.fromTimestamp,
          maxTimestamp: message.data.toTimestamp,
        },
      );
    const diffDoc = ckptInfo.files.at(0)?.changeDocument;
    if (!diffDoc) {
      return Promise.resolve({ type: CommonWebViewMessageType.empty });
    }

    // Determine the best positioning for the diff view
    const positioning = calculateDiffPosition(
      diffDoc.originalCode,
      diffDoc.modifiedCode,
    );

    await getClientActions().showDiffView(
      diffDoc.filePath,
      diffDoc.originalCode,
      diffDoc.modifiedCode,
      {
        retainFocus: message.data.retainFocus,
        useNativeDiffIfAvailable: message.data.useNativeDiffIfAvailable,
        ...positioning,
      },
    );

    return Promise.resolve({ type: CommonWebViewMessageType.empty });
  };
}

/**
 * Utility for calculating optimal positioning in diff views using existing codebase utilities
 */

import { diffLines } from "diff";

export interface DiffPositionOptions {
  focusLine?: number; // 1-based line number to focus on
  focusRange?: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
}

/**
 * Converts diffLines result to line changes compatible with existing utilities
 * @param originalCode - Original file content
 * @param modifiedCode - Modified file content
 * @returns LineChanges object compatible with getSelectionForChanges
 */
function convertDiffToLineChanges(originalCode: string, modifiedCode: string) {
  const diffResult = diffLines(originalCode, modifiedCode);
  const lineChanges: Array<{
    originalStart: number;
    originalEnd: number;
    modifiedStart: number;
    modifiedEnd: number;
  }> = [];

  let originalLineNumber = 1;
  let modifiedLineNumber = 1;

  for (const part of diffResult) {
    const lineCount = part.count || 0;

    if (part.added) {
      // Addition: lines added to modified version
      lineChanges.push({
        originalStart: originalLineNumber,
        originalEnd: originalLineNumber,
        modifiedStart: modifiedLineNumber,
        modifiedEnd: modifiedLineNumber + lineCount - 1,
      });
      modifiedLineNumber += lineCount;
    } else if (part.removed) {
      // Deletion: lines removed from original version
      lineChanges.push({
        originalStart: originalLineNumber,
        originalEnd: originalLineNumber + lineCount - 1,
        modifiedStart: modifiedLineNumber,
        modifiedEnd: modifiedLineNumber,
      });
      originalLineNumber += lineCount;
    } else {
      // Unchanged: lines present in both versions
      originalLineNumber += lineCount;
      modifiedLineNumber += lineCount;
    }
  }

  return {
    lineChanges,
    lineOffset: 0,
  };
}

/**
 * Finds the first change line from diff analysis
 * @param originalCode - Original file content
 * @param modifiedCode - Modified file content
 * @returns 1-based line number of first change, or undefined if no changes
 */
function findFirstChangeFromDiff(
  originalCode: string,
  modifiedCode: string,
): number | undefined {
  const lineChanges = convertDiffToLineChanges(originalCode, modifiedCode);

  if (!lineChanges.lineChanges.length) {
    return undefined;
  }

  const firstChange = lineChanges.lineChanges[0];
  // Prefer modified line numbers for positioning in the new content
  return firstChange.modifiedStart || firstChange.originalStart;
}

/**
 * Calculates optimal range for multiple changes with context
 * @param originalCode - Original file content
 * @param modifiedCode - Modified file content
 * @param contextLines - Number of context lines to include
 * @returns Range object with start and end positions, or undefined if no changes
 */
function calculateOptimalRangeFromDiff(
  originalCode: string,
  modifiedCode: string,
  contextLines: number = 2,
) {
  const lineChanges = convertDiffToLineChanges(originalCode, modifiedCode);

  if (!lineChanges.lineChanges.length) {
    return undefined;
  }

  // Find the range that encompasses all changes
  let minLine = Number.MAX_SAFE_INTEGER;
  let maxLine = 0;

  for (const change of lineChanges.lineChanges) {
    // Use modified lines for positioning in the new content
    const startLine = change.modifiedStart;
    const endLine = change.modifiedEnd;

    minLine = Math.min(minLine, startLine);
    maxLine = Math.max(maxLine, endLine);
  }

  // Add context lines and ensure we don't go below 0
  const startLine = Math.max(0, minLine - 1 - contextLines); // Convert to 0-based
  const endLine = maxLine - 1 + contextLines; // Convert to 0-based

  return {
    start: { line: startLine, character: 0 },
    end: { line: endLine, character: 0 },
  };
}

/**
 * Determines the best positioning strategy for a diff view using existing utilities
 * @param originalCode - Original file content
 * @param modifiedCode - Modified file content
 * @param explicitLine - Explicitly requested line number
 * @param explicitRange - Explicitly requested range
 * @returns Object with focusLine and/or focusRange for positioning
 */
export function calculateDiffPosition(
  originalCode?: string,
  modifiedCode?: string,
  explicitLine?: number,
  explicitRange?: DiffPositionOptions["focusRange"],
): DiffPositionOptions {
  // If explicit positioning is provided, use it
  if (explicitLine) {
    return { focusLine: explicitLine };
  }

  if (explicitRange) {
    return { focusRange: explicitRange };
  }

  // Require both original and modified code for analysis
  if (!originalCode || !modifiedCode) {
    return {};
  }

  // If content is identical, no positioning needed
  if (originalCode === modifiedCode) {
    return {};
  }

  // Find the first change using diff analysis
  const firstChangeLine = findFirstChangeFromDiff(originalCode, modifiedCode);
  if (firstChangeLine) {
    // For single changes or when we want simple positioning, use the first change line
    const lineChanges = convertDiffToLineChanges(originalCode, modifiedCode);

    if (lineChanges.lineChanges.length === 1) {
      return { focusLine: firstChangeLine };
    }

    // For multiple changes, calculate an optimal range
    const optimalRange = calculateOptimalRangeFromDiff(
      originalCode,
      modifiedCode,
    );
    if (optimalRange) {
      return { focusRange: optimalRange };
    }

    // Fallback to first change line
    return { focusLine: firstChangeLine };
  }

  // No positioning information available
  return {};
}

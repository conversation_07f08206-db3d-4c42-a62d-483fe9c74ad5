import { safeJsonStringify } from "../../api/utils";

describe("safeJsonStringify", () => {
  test("handles undefined values", () => {
    const obj = {
      a: undefined,
      b: null,
      c: "value",
    };
    const result = safeJsonStringify(obj);

    const parsed = JSON.parse(result) as Record<string, any>;

    expect(parsed.a).toBeNull();
    expect(parsed.b).toBeNull();
    expect(parsed.c).toBe("value");
  });

  test("handles undefined as input", () => {
    const result = safeJsonStringify(undefined);
    expect(result).toBe("null");
  });

  test("replaces lone high surrogates", () => {
    // U+D800 is a high surrogate
    const obj = {
      text: "Hello\uD800World",
    };
    const result = safeJsonStringify(obj);

    // The lone surrogate should be replaced with the replacement character
    expect(result).toContain("Hello\uFFFDWorld");
    expect(result.toLowerCase()).not.toContain("\ud800");

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });

  test("replaces lone low surrogates", () => {
    // U+DC00 is a low surrogate
    const obj = {
      text: "Hello\uDC00World",
    };
    const result = safeJsonStringify(obj);

    // The lone surrogate should be replaced with the replacement character
    expect(result).toContain("Hello\uFFFDWorld");
    expect(result.toLowerCase()).not.toContain("\udc00");

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });

  test("removes split surrogates pairs", () => {
    const obj = {
      text: "😀Hello World😀".slice(1, -1),
    };
    const result = safeJsonStringify(obj);

    // The lone surrogates should be removed
    expect(result).toContain('"Hello World"');

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });

  test("replaces non-split surrogates at boundary", () => {
    const obj = {
      text: "\uDBFFHello World\uDC00",
    };
    const result = safeJsonStringify(obj);

    // The lone surrogate should be replaced
    expect(result).toContain("\uFFFDHello World\uFFFD");

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });

  test("preserves valid surrogate pairs", () => {
    // U+1F600 (😀) is represented as the surrogate pair U+D83D U+DE00
    const obj = {
      text: "Hello😀World",
    };
    const result = safeJsonStringify(obj);

    // The valid surrogate pair should be preserved
    expect(result).toContain("Hello😀World");

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });

  test("handles nested objects with lone surrogates", () => {
    const obj = {
      outer: "Normal",
      nested: {
        text: "Hello\uD800World",
      },
      array: ["Text", "With\uDC00Surrogate"],
    };
    const result = safeJsonStringify(obj);

    // Should not contain any lone surrogates
    expect(result.toLowerCase()).not.toContain("\ud800");
    expect(result.toLowerCase()).not.toContain("\udc00");

    // Should contain replacement characters
    expect(result).toContain("Hello\uFFFDWorld");
    expect(result).toContain("With\uFFFDSurrogate");

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });

  test("handles multiple valid and invalid surrogates in a string", () => {
    const obj = {
      text: "\uD800Hello\uDC00😀World\uD801",
    };
    const result = safeJsonStringify(obj);

    // All lone surrogates should be replaced
    expect(result).toContain("\uFFFDHello\uFFFD😀World");

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });

  test("handles two invalid surrogates at the boundary", () => {
    const obj = {
      text: "Hello World\uD801\uD801",
    };
    const result = safeJsonStringify(obj);

    // One should be replaced, one should be dropped
    expect(result).toContain('"Hello World\uFFFD"');

    // Should be valid JSON
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    expect(() => JSON.parse(result)).not.toThrow();
  });
});

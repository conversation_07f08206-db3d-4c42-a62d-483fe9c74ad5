import {
  IClientFeatureFlags,
  SidecarFlags,
} from "../../client-interfaces/feature-flags";

export class MockClientFeatureFlags implements IClientFeatureFlags {
  public flags: SidecarFlags = {
    agentEditTool: "",
    enableChatWithTools: false,
    enableAgentMode: false,
    enableNewThreadsList: false,
    memoriesParams: {},
    agentEditToolMinViewSize: 0,
    agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
    agentEditToolEnableFuzzyMatching: false,
    agentEditToolFuzzyMatchSuccessMessage:
      "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
    agentEditToolFuzzyMatchMaxDiff: 50,
    agentEditToolFuzzyMatchMaxDiffRatio: 0.15,
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: 5,
    agentEditToolInstructionsReminder: false,
    agentEditToolShowResultSnippet: true,
    agentEditToolMaxLines: 200,
    agentSaveFileToolInstructionsReminder: false,
    enableTaskList: false,
    enableSupportToolUseStart: false,
    grepSearchToolEnable: false,
    grepSearchToolTimelimitSec: 10,
    grepSearchToolOutputCharsLimit: 5000,
    grepSearchToolNumContextLines: 5,
    useHistorySummary: false,
    historySummaryMaxChars: 0,
    historySummaryLowerChars: 0,
    historySummaryPrompt: "",
    enableUntruncatedContentStorage: false,
    maxLinesTerminalProcessOutput: 0,
    enableCommitIndexing: false,
    maxCommitsToIndex: 0,
  };

  constructor(f: Partial<SidecarFlags> = {}) {
    this.flags = { ...this.flags, ...f };
  }
}

import {
  SidecarAnalytics,
  AnalyticsContext,
} from "../analytics/segment-analytics";

let _analytics: SidecarAnalytics | undefined;

export interface InitializeAnalyticsCommand {
  writeKey: string;
  context: AnalyticsContext;
  anonymousId: string;
}

export function initializeAnalytics(command: InitializeAnalyticsCommand): void {
  if (_analytics) {
    void _analytics.dispose();
  }

  _analytics = new SidecarAnalytics(command.writeKey);
  _analytics.initialize(command.context, command.anonymousId);
}

export async function disposeAnalytics(): Promise<void> {
  if (_analytics) {
    await _analytics.dispose();
    _analytics = undefined;
  }
}

export function trackEvent(
  eventName: string,
  properties?: Record<string, any>,
): void {
  _analytics?.trackEvent(eventName, properties);
}

export function identifyUser(
  userId: string,
  traits?: Record<string, any>,
): void {
  _analytics?.identifyUser(userId, traits);
}

from typing import Callable, Optional

from services.chat_arbiter import chat_arbiter_pb2
from services.lib.request_context import request_context


class MockChatArbiterClient:
    def __init__(self):
        self.get_target_func: Optional[
            Callable[
                [request_context.RequestContext], chat_arbiter_pb2.GetTargetResponse
            ]
        ] = None

    def get_target(
        self, request_context: request_context.RequestContext
    ) -> chat_arbiter_pb2.GetTargetResponse:
        if self.get_target_func:
            return self.get_target_func(request_context)
        return chat_arbiter_pb2.GetTargetResponse(region="", service="")

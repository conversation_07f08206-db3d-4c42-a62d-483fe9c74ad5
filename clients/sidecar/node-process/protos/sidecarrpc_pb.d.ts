// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/sidecarrpc.proto (package com.augmentcode.sidecar.rpc, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { Any, BinaryReadOptions, Extension, FieldList, FieldOptions, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { ChatMode } from "./tools_pb.js";

/**
 * @generated from enum com.augmentcode.sidecar.rpc.TextDocumentSyncKind
 */
export declare enum TextDocumentSyncKind {
  /**
   * @generated from enum value: None = 0;
   */
  None = 0,

  /**
   * @generated from enum value: Full = 1;
   */
  Full = 1,

  /**
   * @generated from enum value: Incremental = 2;
   */
  Incremental = 2,
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.JSONRPCRequest
 */
export declare class JSONRPCRequest extends Message<JSONRPCRequest> {
  /**
   * @generated from field: string jsonrpc = 1;
   */
  jsonrpc: string;

  /**
   * @generated from field: int32 id = 2;
   */
  id: number;

  /**
   * @generated from field: string method = 3;
   */
  method: string;

  /**
   * @generated from field: google.protobuf.Any params = 4;
   */
  params?: Any;

  constructor(data?: PartialMessage<JSONRPCRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.JSONRPCRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JSONRPCRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JSONRPCRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JSONRPCRequest;

  static equals(a: JSONRPCRequest | PlainMessage<JSONRPCRequest> | undefined, b: JSONRPCRequest | PlainMessage<JSONRPCRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.JSONRPCNotification
 */
export declare class JSONRPCNotification extends Message<JSONRPCNotification> {
  /**
   * @generated from field: string jsonrpc = 1;
   */
  jsonrpc: string;

  /**
   * @generated from field: string method = 2;
   */
  method: string;

  /**
   * @generated from field: google.protobuf.Any params = 3;
   */
  params?: Any;

  constructor(data?: PartialMessage<JSONRPCNotification>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.JSONRPCNotification";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JSONRPCNotification;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JSONRPCNotification;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JSONRPCNotification;

  static equals(a: JSONRPCNotification | PlainMessage<JSONRPCNotification> | undefined, b: JSONRPCNotification | PlainMessage<JSONRPCNotification> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.JSONRPCResponse
 */
export declare class JSONRPCResponse extends Message<JSONRPCResponse> {
  /**
   * @generated from field: string jsonrpc = 1;
   */
  jsonrpc: string;

  /**
   * @generated from field: int32 id = 2;
   */
  id: number;

  /**
   * @generated from field: google.protobuf.Any result = 3;
   */
  result?: Any;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.JSONRPCErrorData error = 4;
   */
  error?: JSONRPCErrorData;

  constructor(data?: PartialMessage<JSONRPCResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.JSONRPCResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JSONRPCResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JSONRPCResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JSONRPCResponse;

  static equals(a: JSONRPCResponse | PlainMessage<JSONRPCResponse> | undefined, b: JSONRPCResponse | PlainMessage<JSONRPCResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.JSONRPCErrorData
 */
export declare class JSONRPCErrorData extends Message<JSONRPCErrorData> {
  /**
   * @generated from field: int32 code = 1;
   */
  code: number;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  constructor(data?: PartialMessage<JSONRPCErrorData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.JSONRPCErrorData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): JSONRPCErrorData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): JSONRPCErrorData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): JSONRPCErrorData;

  static equals(a: JSONRPCErrorData | PlainMessage<JSONRPCErrorData> | undefined, b: JSONRPCErrorData | PlainMessage<JSONRPCErrorData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.InitializeParams
 */
export declare class InitializeParams extends Message<InitializeParams> {
  /**
   * @generated from field: int64 process_id = 1;
   */
  processId: bigint;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.Capabilities capabilities = 2;
   */
  capabilities?: Capabilities;

  constructor(data?: PartialMessage<InitializeParams>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.InitializeParams";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InitializeParams;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InitializeParams;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InitializeParams;

  static equals(a: InitializeParams | PlainMessage<InitializeParams> | undefined, b: InitializeParams | PlainMessage<InitializeParams> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.Capabilities
 */
export declare class Capabilities extends Message<Capabilities> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.SidecarFlags featureFlags = 1;
   */
  featureFlags?: SidecarFlags;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.SidecarInitialState initialState = 2;
   */
  initialState?: SidecarInitialState;

  constructor(data?: PartialMessage<Capabilities>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.Capabilities";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Capabilities;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Capabilities;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Capabilities;

  static equals(a: Capabilities | PlainMessage<Capabilities> | undefined, b: Capabilities | PlainMessage<Capabilities> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.SidecarFlags
 */
export declare class SidecarFlags extends Message<SidecarFlags> {
  /**
   * @generated from field: bool enableChatWithTools = 1;
   */
  enableChatWithTools: boolean;

  /**
   * @generated from field: bool enableAgentMode = 2;
   */
  enableAgentMode: boolean;

  /**
   * @generated from field: string agentEditTool = 3;
   */
  agentEditTool: string;

  /**
   * @generated from field: string memories_params_json = 4;
   */
  memoriesParamsJson: string;

  /**
   * @generated from field: int32 agentEditToolMinViewSize = 5;
   */
  agentEditToolMinViewSize: number;

  /**
   * @generated from field: string agentEditToolSchemaType = 6;
   */
  agentEditToolSchemaType: string;

  /**
   * @generated from field: string agentEditToolFuzzyMatchSuccessMessage = 7;
   */
  agentEditToolFuzzyMatchSuccessMessage: string;

  /**
   * @generated from field: int32 agentEditToolFuzzyMatchMaxDiff = 8;
   */
  agentEditToolFuzzyMatchMaxDiff: number;

  /**
   * @generated from field: double agentEditToolFuzzyMatchMaxDiffRatio = 9;
   */
  agentEditToolFuzzyMatchMaxDiffRatio: number;

  /**
   * @generated from field: int32 agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs = 10;
   */
  agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: number;

  /**
   * @generated from field: bool agentEditToolEnableFuzzyMatching = 11;
   */
  agentEditToolEnableFuzzyMatching: boolean;

  /**
   * @generated from field: bool agentEditToolInstructionsReminder = 12;
   */
  agentEditToolInstructionsReminder: boolean;

  /**
   * @generated from field: bool agentEditToolShowResultSnippet = 13;
   */
  agentEditToolShowResultSnippet: boolean;

  /**
   * @generated from field: bool agentSaveFileToolInstructionsReminder = 14;
   */
  agentSaveFileToolInstructionsReminder: boolean;

  /**
   * @generated from field: bool enableTaskList = 15;
   */
  enableTaskList: boolean;

  /**
   * @generated from field: int32 agentEditToolMaxLines = 16;
   */
  agentEditToolMaxLines: number;

  /**
   * @generated from field: bool grepSearchToolEnable = 17;
   */
  grepSearchToolEnable: boolean;

  /**
   * @generated from field: int32 grepSearchToolTimelimitSec = 18;
   */
  grepSearchToolTimelimitSec: number;

  /**
   * @generated from field: int32 grepSearchToolOutputCharsLimit = 19;
   */
  grepSearchToolOutputCharsLimit: number;

  /**
   * @generated from field: int32 grepSearchToolNumContextLines = 20;
   */
  grepSearchToolNumContextLines: number;

  /**
   * @generated from field: bool enableSupportToolUseStart = 21;
   */
  enableSupportToolUseStart: boolean;

  /**
   * @generated from field: bool useHistorySummary = 22;
   */
  useHistorySummary: boolean;

  /**
   * @generated from field: int32 historySummaryMaxChars = 23;
   */
  historySummaryMaxChars: number;

  /**
   * @generated from field: int32 historySummaryLowerChars = 24;
   */
  historySummaryLowerChars: number;

  /**
   * @generated from field: bool enableNewThreadsList = 25;
   */
  enableNewThreadsList: boolean;

  /**
   * @generated from field: string historySummaryPrompt = 26;
   */
  historySummaryPrompt: string;

  /**
   * @generated from field: bool enableUntruncatedContentStorage = 27;
   */
  enableUntruncatedContentStorage: boolean;

  /**
   * @generated from field: int32 maxLinesTerminalProcessOutput = 28;
   */
  maxLinesTerminalProcessOutput: number;

  /**
   * @generated from field: bool enableCommitIndexing = 29;
   */
  enableCommitIndexing: boolean;

  /**
   * @generated from field: int32 maxCommitsToIndex = 30;
   */
  maxCommitsToIndex: number;

  constructor(data?: PartialMessage<SidecarFlags>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.SidecarFlags";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SidecarFlags;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SidecarFlags;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SidecarFlags;

  static equals(a: SidecarFlags | PlainMessage<SidecarFlags> | undefined, b: SidecarFlags | PlainMessage<SidecarFlags> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.SidecarInitialState
 */
export declare class SidecarInitialState extends Message<SidecarInitialState> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ChatMode chatMode = 1;
   */
  chatMode: ChatMode;

  /**
   * @generated from field: string memories_abs_path = 3;
   */
  memoriesAbsPath: string;

  constructor(data?: PartialMessage<SidecarInitialState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.SidecarInitialState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SidecarInitialState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SidecarInitialState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SidecarInitialState;

  static equals(a: SidecarInitialState | PlainMessage<SidecarInitialState> | undefined, b: SidecarInitialState | PlainMessage<SidecarInitialState> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.InitializeResult
 */
export declare class InitializeResult extends Message<InitializeResult> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.ServerCapabilities capabilities = 1;
   */
  capabilities?: ServerCapabilities;

  constructor(data?: PartialMessage<InitializeResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.InitializeResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InitializeResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InitializeResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InitializeResult;

  static equals(a: InitializeResult | PlainMessage<InitializeResult> | undefined, b: InitializeResult | PlainMessage<InitializeResult> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.ServerCapabilities
 */
export declare class ServerCapabilities extends Message<ServerCapabilities> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.TextDocumentSyncKind textDocumentSync = 1;
   */
  textDocumentSync: TextDocumentSyncKind;

  constructor(data?: PartialMessage<ServerCapabilities>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.ServerCapabilities";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ServerCapabilities;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ServerCapabilities;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ServerCapabilities;

  static equals(a: ServerCapabilities | PlainMessage<ServerCapabilities> | undefined, b: ServerCapabilities | PlainMessage<ServerCapabilities> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.ReadFileInputSchema
 */
export declare class ReadFileInputSchema extends Message<ReadFileInputSchema> {
  /**
   * @generated from field: string file_path = 1;
   */
  filePath: string;

  constructor(data?: PartialMessage<ReadFileInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.ReadFileInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReadFileInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReadFileInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReadFileInputSchema;

  static equals(a: ReadFileInputSchema | PlainMessage<ReadFileInputSchema> | undefined, b: ReadFileInputSchema | PlainMessage<ReadFileInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.SaveFileInputSchema
 */
export declare class SaveFileInputSchema extends Message<SaveFileInputSchema> {
  /**
   * @generated from field: string file_path = 1;
   */
  filePath: string;

  /**
   * @generated from field: string file_content = 2;
   */
  fileContent: string;

  /**
   * @generated from field: bool add_last_line_newline = 3;
   */
  addLastLineNewline: boolean;

  constructor(data?: PartialMessage<SaveFileInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.SaveFileInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveFileInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveFileInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveFileInputSchema;

  static equals(a: SaveFileInputSchema | PlainMessage<SaveFileInputSchema> | undefined, b: SaveFileInputSchema | PlainMessage<SaveFileInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.CodebaseRetrievalInputSchema
 */
export declare class CodebaseRetrievalInputSchema extends Message<CodebaseRetrievalInputSchema> {
  /**
   * @generated from field: string information_request = 1;
   */
  informationRequest: string;

  constructor(data?: PartialMessage<CodebaseRetrievalInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.CodebaseRetrievalInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CodebaseRetrievalInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CodebaseRetrievalInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CodebaseRetrievalInputSchema;

  static equals(a: CodebaseRetrievalInputSchema | PlainMessage<CodebaseRetrievalInputSchema> | undefined, b: CodebaseRetrievalInputSchema | PlainMessage<CodebaseRetrievalInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.ProcessWebviewMessageRequest
 */
export declare class ProcessWebviewMessageRequest extends Message<ProcessWebviewMessageRequest> {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  constructor(data?: PartialMessage<ProcessWebviewMessageRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.ProcessWebviewMessageRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProcessWebviewMessageRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProcessWebviewMessageRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProcessWebviewMessageRequest;

  static equals(a: ProcessWebviewMessageRequest | PlainMessage<ProcessWebviewMessageRequest> | undefined, b: ProcessWebviewMessageRequest | PlainMessage<ProcessWebviewMessageRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.ProcessWebviewMessageResponse
 */
export declare class ProcessWebviewMessageResponse extends Message<ProcessWebviewMessageResponse> {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  constructor(data?: PartialMessage<ProcessWebviewMessageResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.ProcessWebviewMessageResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProcessWebviewMessageResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProcessWebviewMessageResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProcessWebviewMessageResponse;

  static equals(a: ProcessWebviewMessageResponse | PlainMessage<ProcessWebviewMessageResponse> | undefined, b: ProcessWebviewMessageResponse | PlainMessage<ProcessWebviewMessageResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.LaunchProcessInputSchema
 */
export declare class LaunchProcessInputSchema extends Message<LaunchProcessInputSchema> {
  /**
   * @generated from field: string command = 1;
   */
  command: string;

  /**
   * @generated from field: bool wait = 2;
   */
  wait: boolean;

  /**
   * @generated from field: int64 max_wait_seconds = 3;
   */
  maxWaitSeconds: bigint;

  /**
   * @generated from field: string cwd = 4;
   */
  cwd: string;

  constructor(data?: PartialMessage<LaunchProcessInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.LaunchProcessInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LaunchProcessInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LaunchProcessInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LaunchProcessInputSchema;

  static equals(a: LaunchProcessInputSchema | PlainMessage<LaunchProcessInputSchema> | undefined, b: LaunchProcessInputSchema | PlainMessage<LaunchProcessInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.KillProcessInputSchema
 */
export declare class KillProcessInputSchema extends Message<KillProcessInputSchema> {
  /**
   * @generated from field: int64 terminal_id = 1;
   */
  terminalId: bigint;

  constructor(data?: PartialMessage<KillProcessInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.KillProcessInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KillProcessInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KillProcessInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KillProcessInputSchema;

  static equals(a: KillProcessInputSchema | PlainMessage<KillProcessInputSchema> | undefined, b: KillProcessInputSchema | PlainMessage<KillProcessInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.ReadProcessInputSchema
 */
export declare class ReadProcessInputSchema extends Message<ReadProcessInputSchema> {
  /**
   * @generated from field: int64 terminal_id = 1;
   */
  terminalId: bigint;

  /**
   * @generated from field: bool wait = 2;
   */
  wait: boolean;

  /**
   * @generated from field: int64 max_wait_seconds = 3;
   */
  maxWaitSeconds: bigint;

  constructor(data?: PartialMessage<ReadProcessInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.ReadProcessInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReadProcessInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReadProcessInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReadProcessInputSchema;

  static equals(a: ReadProcessInputSchema | PlainMessage<ReadProcessInputSchema> | undefined, b: ReadProcessInputSchema | PlainMessage<ReadProcessInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.WriteProcessInputSchema
 */
export declare class WriteProcessInputSchema extends Message<WriteProcessInputSchema> {
  /**
   * @generated from field: int64 terminal_id = 1;
   */
  terminalId: bigint;

  /**
   * @generated from field: string input_text = 2;
   */
  inputText: string;

  constructor(data?: PartialMessage<WriteProcessInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.WriteProcessInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WriteProcessInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WriteProcessInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WriteProcessInputSchema;

  static equals(a: WriteProcessInputSchema | PlainMessage<WriteProcessInputSchema> | undefined, b: WriteProcessInputSchema | PlainMessage<WriteProcessInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.ListProcessesInputSchema
 */
export declare class ListProcessesInputSchema extends Message<ListProcessesInputSchema> {
  constructor(data?: PartialMessage<ListProcessesInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.ListProcessesInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListProcessesInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListProcessesInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListProcessesInputSchema;

  static equals(a: ListProcessesInputSchema | PlainMessage<ListProcessesInputSchema> | undefined, b: ListProcessesInputSchema | PlainMessage<ListProcessesInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.WaitProcessInputSchema
 */
export declare class WaitProcessInputSchema extends Message<WaitProcessInputSchema> {
  /**
   * @generated from field: int64 terminal_id = 1;
   */
  terminalId: bigint;

  /**
   * @generated from field: int64 wait_seconds = 2;
   */
  waitSeconds: bigint;

  constructor(data?: PartialMessage<WaitProcessInputSchema>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.WaitProcessInputSchema";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WaitProcessInputSchema;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WaitProcessInputSchema;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WaitProcessInputSchema;

  static equals(a: WaitProcessInputSchema | PlainMessage<WaitProcessInputSchema> | undefined, b: WaitProcessInputSchema | PlainMessage<WaitProcessInputSchema> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.ProgressParams
 */
export declare class ProgressParams extends Message<ProgressParams> {
  /**
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * @generated from field: google.protobuf.Any value = 2;
   */
  value?: Any;

  constructor(data?: PartialMessage<ProgressParams>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.ProgressParams";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ProgressParams;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ProgressParams;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ProgressParams;

  static equals(a: ProgressParams | PlainMessage<ProgressParams> | undefined, b: ProgressParams | PlainMessage<ProgressParams> | undefined): boolean;
}

/**
 * @generated from extension: string schema_description = 239001;
 */
export declare const schema_description: Extension<FieldOptions, string>;

/**
 * @generated from extension: bool schema_required = 239002;
 */
export declare const schema_required: Extension<FieldOptions, boolean>;


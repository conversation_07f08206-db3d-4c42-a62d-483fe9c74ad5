import { trackEvent } from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { createHash, randomBytes, randomUUID } from "crypto";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";
import { OnboardingSessionEventReporter } from "../metrics/onboarding-session-event-reporter";
import { OnboardingSessionEventName } from "../onboarding/onboarding-types";
import { eventAsPromise } from "../utils/events";
import { AugmentSession, AuthSessionStore, SESSION_SCOPES } from "./auth-session-store";

const OAUTH_KEY = "augment.oauth-state"; // pragma: allowlist secret
const AUGMENT_HOSTNAME = ".augmentcode.com";
const SIGN_IN_TIMEOUT_MINS = 10;

// This class manages the OAuth flow.
// Multiple sign in attempts are handled by this class.
// If the user attempts to sign-in multiple times in the same window, previous
// attempts are cancelled before starting a new flow.
// If the user has multiple windows opened, the OAuth state is stores in
// VSCode secrets, so any window can handle the browser -> VSCode Uri.
// The resulting session is stored in the AuthSessionStore, which should be
// used to get the session and listen for changes.
export class OAuthFlow {
    private _logger = getLogger("OAuthFlow");

    // Programmatically cancel any pending OAuth flow
    private _programmaticCancellation = new vscode.EventEmitter<string>();

    // Any previous login promise
    private _previousLogin: Promise<AugmentSession> | undefined;

    // The extension can only have one Uri handler, so we expose the auth URI
    // so that the extension can compare the path and parse the URI to the auth
    // instance.
    public readonly authRedirectURI: vscode.Uri;

    constructor(
        private readonly _context: vscode.ExtensionContext,
        private _config: AugmentConfigListener,
        private _apiServer: APIServer,
        private _authSession: AuthSessionStore,
        private _onboardingSessionEventReporter: OnboardingSessionEventReporter
    ) {
        this.authRedirectURI = vscode.Uri.from({
            scheme: vscode.env.uriScheme,
            authority: this._context.extension.id,
            path: "/auth/result",
        });
    }

    public doProgrammaticCancellation() {
        this._programmaticCancellation.fire("Cancelled by user");
    }

    public async startFlow(withProgress: boolean = true): Promise<void> {
        try {
            trackEvent("signin_started");

            // Cancel any pending OAuth flows.
            this._programmaticCancellation.fire("Cancelled due to new sign in");

            // Wait for any pending logins to stop
            await Promise.allSettled([this._previousLogin]);

            // Start the new login
            this._logger.info(`Creating new session...`);
            let session: AugmentSession;
            if (withProgress) {
                session = await this.loginWithProgress();
            } else {
                session = await this.loginWithoutProgress();
            }
            this._logger.info(`Created session ${session.tenantURL}`);
            this._onboardingSessionEventReporter.reportEvent(OnboardingSessionEventName.SignedIn);
        } catch (e) {
            trackEvent("signin_failed", {
                error: getErrmsg(e),
            });

            void vscode.window.showErrorMessage(`Sign in failed. ${getErrmsg(e)}`);
            throw e;
        }
    }

    private async createOAuthState(): Promise<OAuthState> {
        const verifier = base64URLEncode(randomBytes(32));
        const challenge = base64URLEncode(sha256(Buffer.from(verifier)));
        const state = randomUUID();
        const oauthState: OAuthState = {
            codeVerifier: verifier,
            codeChallenge: challenge,
            state,
            creationTime: new Date().getTime(),
        };
        await this._context.secrets.store(OAUTH_KEY, JSON.stringify(oauthState));
        return oauthState;
    }

    private async getOAuthState(): Promise<OAuthState | null> {
        const stateData = await this._context.secrets.get(OAUTH_KEY);
        if (stateData) {
            const state = JSON.parse(stateData) as OAuthState;
            // Ensure the state is not too old
            if (new Date().getTime() - state.creationTime < SIGN_IN_TIMEOUT_MINS * 60 * 1000) {
                return state;
            }
        }
        return null;
    }

    private async removeOAuthState(): Promise<void> {
        const state = await this._context.secrets.get(OAUTH_KEY);
        if (!state) {
            return;
        }
        await this._context.secrets.delete(OAUTH_KEY);
    }

    private async loginWithoutProgress(): Promise<AugmentSession> {
        const cancelToken = new vscode.CancellationTokenSource();
        return this.login(cancelToken.token);
    }

    private async loginWithProgress(): Promise<AugmentSession> {
        const progressOptions = {
            location: vscode.ProgressLocation.Notification,
            title: "Signing in...",
            cancellable: true,
        };
        const callback = (
            _: vscode.Progress<any>,
            uiCancellationToken: vscode.CancellationToken
        ) => {
            this._previousLogin = this.login(uiCancellationToken);
            return this._previousLogin;
        };

        return vscode.window.withProgress<AugmentSession>(progressOptions, callback);
    }

    // Login the user via OAuth flow
    private async login(uiCancellationToken: vscode.CancellationToken): Promise<AugmentSession> {
        if (!this._config.config.oauth.url) {
            throw new Error("No OAuth URL defined.");
        }

        // Start waiting for the session state to change; OR
        // timeout after  X minutes; OR
        // programmatically cancelled; OR
        // user cancelled
        // before we open the browser and wait for the auth result.
        // We do this *before* opening the browser so that tests can reliably
        // wait for the openExternal call.
        const promises = [
            this.waitForSessionChange(),
            new Promise<AugmentSession>((_, reject) =>
                setTimeout(() => reject("Timed out"), SIGN_IN_TIMEOUT_MINS * 60 * 1000)
            ),
            this.waitForProgrammaticCancellation(),
            this.waitForCancellation(uiCancellationToken, "User cancelled"),
        ];

        try {
            // Create the state for the OAuth flow
            const oauthState = await this.createOAuthState();

            // Open the browser for the user to sign in
            await this.openBrowser(oauthState);

            // Wait session, timeout, or cancellation
            return await Promise.race(promises);
        } finally {
            // Once one of the events above has triggered we no longer need the OAuth state
            await this.removeOAuthState();
        }
    }

    // Open the browser for the user to sign in
    private async openBrowser(oauthState: OAuthState) {
        const scopesString = SESSION_SCOPES.join(" ");
        const searchParams = new URLSearchParams({
            /* eslint-disable @typescript-eslint/naming-convention */
            response_type: "code",
            code_challenge: oauthState.codeChallenge,
            code_challenge_method: "S256",
            client_id: this._config.config.oauth.clientID || "",
            redirect_uri: this.authRedirectURI.toString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            state: oauthState.state,
            scope: scopesString,
            prompt: "login",
        });

        const authorizeURL = new URL(
            `/authorize?${searchParams.toString()}`,
            this._config.config.oauth.url
        );
        this._logger.info(`Opening URL: ${authorizeURL.toString()}`);

        try {
            const uri = vscode.Uri.parse(authorizeURL.toString());

            // VSCode allows the user to copy the URL, and openExternal will return
            // false when this occurs, so no need to check the return value.
            await vscode.env.openExternal(uri);
        } catch (e) {
            // Show message with copy button for the URL
            const copyAction = "Copy URL";
            const result = await vscode.window.showInformationMessage(
                `Failed to open browser. Copy the URL to sign in manually.`,
                copyAction
            );

            if (result === copyAction) {
                await vscode.env.clipboard.writeText(authorizeURL.toString());
                await vscode.window.showInformationMessage("URL copied to clipboard");
            }
        }
    }

    private async waitForSessionChange(): Promise<AugmentSession> {
        const session = await eventAsPromise<AugmentSession | null>(
            this._authSession.onDidChangeSession
        );
        if (!session) {
            throw new Error("No session");
        }
        return session;
    }

    private async waitForProgrammaticCancellation(): Promise<AugmentSession> {
        const reason = await eventAsPromise<string>(this._programmaticCancellation.event);

        trackEvent("signin_cancelled", { reason });

        throw new Error(reason);
    }

    // This is a function to wrap the cancellation token in a promise
    private async waitForCancellation(
        token: vscode.CancellationToken,
        msg: string
    ): Promise<AugmentSession> {
        await eventAsPromise<any>(token.onCancellationRequested);
        throw new Error(msg);
    }

    private async processAuthRedirect(uri: vscode.Uri) {
        const query = new URLSearchParams(uri.query);
        const state = query.get("state");
        if (!state) {
            throw new Error("No state");
        }
        const oauthState = await this.getOAuthState();
        if (!oauthState) {
            throw new Error("No OAuth state found");
        }

        // Since we have a URI response, we won't need this state again.
        await this.removeOAuthState();

        if (oauthState.state !== state) {
            throw new Error("Unknown state");
        }

        const error = query.get("error");
        if (error) {
            const parts: string[] = [`(${error})`];
            const errorDescrip = query.get("error_description");
            if (errorDescrip) {
                parts.push(errorDescrip);
            }
            throw new Error(`OAuth request failed: ${parts.join(" ")}`);
        }

        const code = query.get("code");
        if (!code) {
            throw new Error("No code");
        }

        const tenantURL = query.get("tenant_url");

        if (!tenantURL) {
            throw new Error("No tenant URL");
        } else if (!new URL(tenantURL).hostname.endsWith(AUGMENT_HOSTNAME)) {
            throw new Error("OAuth request failed: invalid OAuth tenant URL");
        }

        // Swap the code for an access token
        try {
            const accessToken = await this._apiServer.getAccessToken(
                this.authRedirectURI.toString(),
                tenantURL,
                oauthState.codeVerifier,
                code
            );
            await this._authSession.saveSession(accessToken, tenantURL);
        } catch (err) {
            this._logger.error(`Failed to get and save access token: ${getErrmsg(err)}`);
            throw new Error(`If you have a firewall, please add "${tenantURL}" to your allowlist.`);
        }
    }

    // When a URI is received by VSCode, our URI handler will call this method
    public async handleAuthURI(uri: vscode.Uri) {
        try {
            await this.processAuthRedirect(uri);
        } catch (err) {
            this._logger.warn("Failed to process auth request:", err);
            // If we run into errors, cancel any in-progress OAuth UI
            this._programmaticCancellation.fire(getErrmsg(err));
        }
    }
}

function base64URLEncode(data: Buffer): string {
    return data.toString("base64").replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}

function sha256(buffer: Buffer): Buffer {
    return createHash("sha256").update(buffer).digest();
}

interface OAuthState {
    codeVerifier: string;
    codeChallenge: string;
    state: string;
    creationTime: number;
}

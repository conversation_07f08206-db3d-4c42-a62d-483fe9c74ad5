// Refer to docs/feature-flags.md for more information.
import cloneDeep from "lodash/cloneDeep";
import * as vscode from "vscode";

import { AugmentConfigListener } from "./augment-config-listener";
import { getLogger } from "./logging";
import { MonitoredParameter } from "./monitored-parameter";
import { DisposableService } from "./utils/disposable-service";
import { type EloModelConfiguration } from "./webview-panels/preference-panel-types";
import { SmartPastePrecomputeMode } from "./webview-providers/webview-messages";

// Default max size for files to be uploaded. The backend is expected to
// override this through a feature flag.
const DEFAULT_MAX_UPLOAD_SIZE_BYTES: number = 128 * 1024;

type OverriddenFeatureFlags = Partial<FeatureFlags>;

/**
 * General fields an types in feature flags
 */
export interface FeatureFlags {
    gitDiff: boolean;
    gitDiffPollingFrequencyMSec: number;
    additionalChatModels: string;
    smallSyncThreshold: number;
    bigSyncThreshold: number;
    enableWorkspaceManagerUi: boolean;
    enableInstructions: boolean;
    enableSmartPaste: boolean;
    enableSmartPasteMinVersion: string;
    enablePromptEnhancer: boolean;
    enableViewTextDocument: boolean;
    bypassLanguageFilter: boolean;
    enableHindsight: boolean;
    maxUploadSizeBytes: number;
    vscodeNextEditMinVersion: string;
    vscodeNextEditBottomPanelMinVersion: string;
    vscodeNextEditUx1MaxVersion: string;
    vscodeNextEditUx2MaxVersion: string;
    vscodeFlywheelMinVersion: string;
    vscodeExternalSourcesInChatMinVersion: string;
    vscodeShareMinVersion: string;
    maxTrackableFileCount: number;
    maxTrackableFileCountWithoutPermission: number;
    minUploadedPercentageWithoutPermission: number;
    memoryClassificationOnFirstToken: boolean;
    vscodeSourcesMinVersion: string;
    vscodeChatHintDecorationMinVersion: string;
    nextEditDebounceMs: number;
    enableCompletionFileEditEvents: boolean;
    vscodeEnableCpuProfile: boolean;
    verifyFolderIsSourceRepo: boolean;
    refuseToSyncHomeDirectories: boolean;
    enableFileLimitsForSyncingPermission: boolean;
    enableChatMermaidDiagrams: boolean;
    enableSummaryTitles: boolean;
    smartPastePrecomputeMode: SmartPastePrecomputeMode;
    vscodeNewThreadsMenuMinVersion: string;
    enableNewThreadsList: boolean;
    enableUntruncatedContentStorage: boolean;
    maxLinesTerminalProcessOutput: number;
    vscodeEditableHistoryMinVersion: string;
    vscodeEnableChatMermaidDiagramsMinVersion: string;
    userGuidelinesLengthLimit: number;
    workspaceGuidelinesLengthLimit: number;
    enableGuidelines: boolean;
    useCheckpointManagerContextMinVersion: string;
    validateCheckpointManagerContext: boolean;
    vscodeDesignSystemRichTextEditorMinVersion: string;
    allowClientFeatureFlagOverrides: boolean;
    vscodeChatWithToolsMinVersion: string;
    vscodeChatMultimodalMinVersion: string;
    vscodeAgentModeMinVersion: string;
    vscodeAgentModeMinStableVersion: string;
    vscodeBackgroundAgentsMinVersion: string;
    vscodeAgentEditTool: string;
    vscodeRichCheckpointInfoMinVersion: string;
    vscodeDirectApplyMinVersion: string;
    memoriesParams: { [key: string]: string | number | boolean };
    eloModelConfiguration: EloModelConfiguration;
    vscodeVirtualizedMessageListMinVersion: string;
    vscodeChatStablePrefixTruncationMinVersion: string;
    agentEditToolMinViewSize: number;
    agentEditToolSchemaType: string;
    agentEditToolEnableFuzzyMatching: boolean;
    agentEditToolFuzzyMatchSuccessMessage: string;
    agentEditToolFuzzyMatchMaxDiff: number;
    agentEditToolFuzzyMatchMaxDiffRatio: number;
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: number;
    agentEditToolInstructionsReminder: boolean;
    agentEditToolShowResultSnippet: boolean;
    agentEditToolMaxLines: number;
    vscodePersonalitiesMinVersion: string;
    agentSaveFileToolInstructionsReminder: boolean;
    useMemorySnapshotManager: boolean;
    vscodeGenerateCommitMessageMinVersion: string;
    enableRules: boolean;
    memoriesTextEditorEnabled: boolean;
    enableModelRegistry: boolean;
    openFileManagerV2Enabled: boolean;
    modelRegistry: Record<string, string>;
    vscodeTaskListMinVersion: string;
    vscodeSupportToolUseStartMinVersion: string;
    enableAgentAutoMode: boolean;
    vscodeRemoteAgentSSHMinVersion: string;
    clientAnnouncement: string;
    grepSearchToolEnable: boolean;
    grepSearchToolTimelimitSec: number;
    grepSearchToolOutputCharsLimit: number;
    grepSearchToolNumContextLines: number;
    historySummaryMinVersion: string;
    historySummaryMaxChars: number;
    historySummaryLowerChars: number;
    historySummaryPrompt: string;
    enableSpawnSubAgentTool: boolean;
    enableCommitIndexing: boolean;
    maxCommitsToIndex: number;
}

export const defaultFeatureFlags: FeatureFlags = {
    gitDiff: false,
    gitDiffPollingFrequencyMSec: 0,
    additionalChatModels: "",
    smallSyncThreshold: 15,
    bigSyncThreshold: 1000,
    enableWorkspaceManagerUi: true,
    enableInstructions: false,
    enableSmartPaste: false,
    enableSmartPasteMinVersion: "",
    enablePromptEnhancer: false,
    enableViewTextDocument: false,
    bypassLanguageFilter: false,
    enableHindsight: false,
    maxUploadSizeBytes: DEFAULT_MAX_UPLOAD_SIZE_BYTES,
    vscodeNextEditBottomPanelMinVersion: "",
    vscodeNextEditMinVersion: "",
    vscodeNextEditUx1MaxVersion: "",
    vscodeNextEditUx2MaxVersion: "",
    vscodeFlywheelMinVersion: "",
    vscodeExternalSourcesInChatMinVersion: "",
    vscodeShareMinVersion: "",
    maxTrackableFileCount: 250_000,
    maxTrackableFileCountWithoutPermission: 150_000,
    minUploadedPercentageWithoutPermission: 90,
    memoryClassificationOnFirstToken: false,
    vscodeSourcesMinVersion: "",
    vscodeChatHintDecorationMinVersion: "",
    nextEditDebounceMs: 400,
    enableCompletionFileEditEvents: false,
    vscodeEnableCpuProfile: false,
    verifyFolderIsSourceRepo: false,
    refuseToSyncHomeDirectories: false,
    enableFileLimitsForSyncingPermission: false,
    enableChatMermaidDiagrams: false,
    enableSummaryTitles: false,
    smartPastePrecomputeMode: SmartPastePrecomputeMode.visibleHover,
    vscodeNewThreadsMenuMinVersion: "",
    enableNewThreadsList: false,
    enableUntruncatedContentStorage: false,
    maxLinesTerminalProcessOutput: 0,
    vscodeEditableHistoryMinVersion: "",
    vscodeEnableChatMermaidDiagramsMinVersion: "",
    userGuidelinesLengthLimit: 2000,
    workspaceGuidelinesLengthLimit: 2000,
    enableGuidelines: false,
    useCheckpointManagerContextMinVersion: "",
    validateCheckpointManagerContext: false,
    vscodeDesignSystemRichTextEditorMinVersion: "",
    allowClientFeatureFlagOverrides: false,
    vscodeChatWithToolsMinVersion: "",
    vscodeChatMultimodalMinVersion: "",
    vscodeAgentModeMinVersion: "",
    vscodeAgentModeMinStableVersion: "",
    vscodeBackgroundAgentsMinVersion: "",
    vscodeAgentEditTool: "backend_edit_tool",
    vscodeRichCheckpointInfoMinVersion: "",
    vscodeDirectApplyMinVersion: "",
    memoriesParams: {},
    eloModelConfiguration: {
        highPriorityModels: [],
        regularBattleModels: [],
        highPriorityThreshold: 0.5,
    },
    vscodeVirtualizedMessageListMinVersion: "",
    vscodeChatStablePrefixTruncationMinVersion: "",
    agentEditToolMinViewSize: 0,
    agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
    agentEditToolEnableFuzzyMatching: false,
    agentEditToolFuzzyMatchSuccessMessage:
        "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
    agentEditToolFuzzyMatchMaxDiff: 50,
    agentEditToolFuzzyMatchMaxDiffRatio: 0.15,
    agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: 5,
    agentEditToolInstructionsReminder: false,
    agentEditToolShowResultSnippet: true,
    agentEditToolMaxLines: 200,
    agentSaveFileToolInstructionsReminder: false,
    vscodePersonalitiesMinVersion: "",
    useMemorySnapshotManager: false,
    vscodeGenerateCommitMessageMinVersion: "",
    enableRules: false,
    memoriesTextEditorEnabled: false,
    enableModelRegistry: false,
    openFileManagerV2Enabled: false,
    modelRegistry: {},
    vscodeTaskListMinVersion: "",
    vscodeSupportToolUseStartMinVersion: "",
    enableAgentAutoMode: false,
    vscodeRemoteAgentSSHMinVersion: "",
    clientAnnouncement: "",
    grepSearchToolEnable: false,
    grepSearchToolTimelimitSec: 10,
    grepSearchToolOutputCharsLimit: 5000,
    grepSearchToolNumContextLines: 5,
    historySummaryMinVersion: "",
    historySummaryMaxChars: 0,
    historySummaryLowerChars: 0,
    historySummaryPrompt: "",
    enableSpawnSubAgentTool: false,
    enableCommitIndexing: false,
    maxCommitsToIndex: 0,
};

export interface FeatureFlagChangeEvent {
    readonly previousFlags: Readonly<FeatureFlags>;
    readonly newFlags: Readonly<FeatureFlags>;
    readonly changedFlags: ReadonlyArray<string>;
}

// Represents a subscription to a change in feature flags.
// The subscription is canceled when the object is disposed.
export class FeatureFlagChangeSubscription implements vscode.Disposable {
    private _disposed = false;
    private readonly _currentFlags: FeatureFlags;

    constructor(
        currentFlags: FeatureFlags,
        private readonly _watchedFlags: ReadonlyArray<keyof FeatureFlags>,
        private readonly _callback: (event: FeatureFlagChangeEvent) => void
    ) {
        this._currentFlags = cloneDeep(currentFlags);
    }

    public get disposed(): boolean {
        return this._disposed;
    }

    public trigger(newFlags: Readonly<FeatureFlags>): void {
        if (this._disposed) {
            return;
        }
        // check if any of my watched flags changed
        const changedFlags: string[] = [];
        for (const flag of this._watchedFlags) {
            if (newFlags[flag] !== this._currentFlags[flag]) {
                changedFlags.push(flag);
            }
        }
        if (changedFlags.length > 0) {
            this._callback({
                previousFlags: this._currentFlags,
                newFlags,
                changedFlags,
            });
        }
    }

    // Cancel the subscription
    public dispose() {
        this._disposed = true;
    }
}

export interface IFeatureFlagManagerOptions {
    initialFlags?: FeatureFlags;
    fetcher?: (cancelToken: vscode.CancellationToken) => Promise<FeatureFlags | undefined>;
    refreshIntervalMSec?: number;
}

/**
 * FeatureFlagManager is a class that maintains the current values of feature
 * flags and allows other components to listen for feature flag changes.
 */
export class FeatureFlagManager extends DisposableService {
    private _subscriptions: FeatureFlagChangeSubscription[] = [];
    private _refreshTimer?: NodeJS.Timer;
    private _disposed: boolean = false;

    private _logger = getLogger("FeatureFlagManager");

    private _flags: MonitoredParameter<FeatureFlags>;

    constructor(
        options?: IFeatureFlagManagerOptions,
        _augmentConfigListener?: AugmentConfigListener
    ) {
        super();
        this._flags = new MonitoredParameter<FeatureFlags>(
            "feature flags",
            this._logger,
            _augmentConfigListener
        );
        this._flags.update(options?.initialFlags ?? defaultFeatureFlags);
        this._setupRefreshTimer(options);
        // this is only to support overrides from the local config, mostly for testing.
        // note we don't change the stored flags here, the overrides are only applied
        // when the consumer reads the flags.
        this.addDisposable(
            vscode.workspace.onDidChangeConfiguration(() => {
                this._subscriptions = this._subscriptions.filter(
                    (subscription) => !subscription.disposed
                );
                for (const subscription of this._subscriptions) {
                    subscription.trigger(this.currentFlags);
                }
            })
        );
    }

    public get currentFlags(): Readonly<FeatureFlags> {
        if (this._disposed) {
            throw Error("FeatureFlagManager has been disposed");
        }
        // note that there is no circular issue here because we look directly at the
        // underlying flags and the not the overrides.
        if (this._flags.value?.allowClientFeatureFlagOverrides) {
            // overrides don't change the stored flags, we just apply them right here.
            return {
                ...cloneDeep(this._flags.value),
                ...this.readOverridesFromConfig(), // known to not contain undefined
            };
        }
        return cloneDeep(this._flags.value!);
    }

    /**
     *
     * @returns valid overrides only -- does not contain undefined values.
     */
    private readOverridesFromConfig(): Readonly<OverriddenFeatureFlags> {
        // We deliberately do not use AugmentConfigListener here because
        // we dont want to take on that dependency, and we don't want to add
        // the feature flags schema to the config because no other part of
        // the extension should be consuming that data.
        const config = vscode.workspace.getConfiguration("augment");
        if (config.advanced == null || typeof config.advanced !== "object") {
            return {};
        }

        const overrides: Record<string, unknown> = (
            config.advanced as { featureFlagOverrides: Record<string, unknown> }
        ).featureFlagOverrides;
        const result: OverriddenFeatureFlags = {};
        if (typeof overrides === "object") {
            for (const flag of Object.keys(overrides) as Array<keyof OverriddenFeatureFlags>) {
                if (defaultFeatureFlags[flag] === undefined) {
                    getLogger("AugmentConfigListener").warn(
                        `Feature flag override for ${flag} is not a valid feature flag.`
                    );
                } else if (typeof overrides[flag] !== typeof defaultFeatureFlags[flag]) {
                    getLogger("AugmentConfigListener").warn(
                        `Feature flag override for ${flag} is does not match default type ${typeof defaultFeatureFlags[flag]}.`
                    );
                } else if (
                    overrides[flag] !== undefined &&
                    defaultFeatureFlags[flag] !== undefined
                ) {
                    // At this point we know that overrides[flag] is non-null and
                    // has the same type as the default.
                    // Doing a simple assignment makes TS unhappy here because it
                    // seems unable to infer the type of result[flag] correctly.
                    Object.assign(result, { [flag]: overrides[flag] });
                }
            }
        }
        return result;
    }

    // When using update() to update the flags, any changes will trigger
    // the corresponding listeners.
    public update(other: FeatureFlags): void {
        if (this._disposed) {
            throw Error("FeatureFlagManager has been disposed");
        }
        this._flags.update(other);
        this._subscriptions = this._subscriptions.filter((subscription) => !subscription.disposed);
        // Try and trigger each subscription
        for (const subscription of this._subscriptions) {
            subscription.trigger(other);
        }
    }

    public subscribe(
        watchedFlags: ReadonlyArray<keyof FeatureFlags>,
        callback: (event: FeatureFlagChangeEvent) => void
    ): FeatureFlagChangeSubscription {
        if (this._disposed) {
            throw Error("FeatureFlagManager has been disposed");
        }
        const subscription = new FeatureFlagChangeSubscription(
            this._flags.value!,
            watchedFlags,
            callback
        );
        this._subscriptions.push(subscription);
        return subscription;
    }

    private _setupRefreshTimer(options?: IFeatureFlagManagerOptions) {
        if (!options?.fetcher || !options?.refreshIntervalMSec) {
            return;
        }

        this._cleanupRefreshTimer();
        const cancellationToken: vscode.CancellationToken = new vscode.CancellationTokenSource()
            .token;
        const fetcher = options.fetcher;
        const refreshIntervalMSec: number = options.refreshIntervalMSec;

        // Wrap the callback with cancellation
        const callback = async (): Promise<void> => {
            const newFlags = await fetcher(cancellationToken);
            if (newFlags) {
                this.update(newFlags);
            } else if (cancellationToken.isCancellationRequested) {
                this._cleanupRefreshTimer();
            }
        };

        this._refreshTimer = setInterval(() => void callback(), refreshIntervalMSec);
    }

    private _cleanupRefreshTimer() {
        clearInterval(this._refreshTimer);
        this._refreshTimer = undefined;
    }

    public dispose() {
        if (this._disposed) {
            return;
        }
        super.dispose();

        this._subscriptions.forEach((subscription) => subscription.dispose());
        this._subscriptions = [];
        this._cleanupRefreshTimer();
        this._disposed = true;
    }
}

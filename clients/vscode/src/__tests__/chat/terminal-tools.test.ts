import { MockClientFeatureFlags } from "@augment-internal/sidecar-libs/src/__tests__/mocks/mock-client-feature-flags";
import {
    resetLibraryClientFeatureFlags,
    setLibraryClientFeatureFlags,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";

// Import the exported test utilities
import { __test__ } from "../../chat/terminal-tools";

// Access the exported functions for testing
const stripControlCodes = __test__.stripControlCodes;
const shellPrompts = __test__.shellPrompts;

describe("TerminalProcessTools", () => {
    let mockFeatureFlags: MockClientFeatureFlags;

    beforeEach(() => {
        mockFeatureFlags = new MockClientFeatureFlags();
        setLibraryClientFeatureFlags(mockFeatureFlags);
    });

    afterEach(() => {
        resetLibraryClientFeatureFlags();
    });

    describe("stripControlCodes", () => {
        it("should remove ANSI color codes", () => {
            const input = "\u001b[31mRed text\u001b[0m";
            const expected = "Red text";
            expect(stripControlCodes(input)).toBe(expected);
        });

        it("should remove cursor movement codes", () => {
            const input = "Text with \u001b[2A\u001b[3B\u001b[4C\u001b[5D cursor movement";
            const expected = "Text with  cursor movement";
            expect(stripControlCodes(input)).toBe(expected);
        });

        it("should remove complex ANSI sequences", () => {
            const input = "\u001b[1;32;40mGreen on black\u001b[0m \u001b[4mUnderlined\u001b[24m";
            const expected = "Green on black Underlined";
            expect(stripControlCodes(input)).toBe(expected);
        });

        it("should handle text without control codes", () => {
            const input = "Plain text without any control codes";
            expect(stripControlCodes(input)).toBe(input);
        });

        it("should handle empty string", () => {
            expect(stripControlCodes("")).toBe("");
        });

        it("should handle OSC sequences by removing escape characters", () => {
            const input = "Text with \u001b]0;Window title\u0007 OSC sequence";
            // Check that the escape character and OSC content are removed
            const result = stripControlCodes(input);
            expect(result).not.toContain("\u001b");
            expect(result).not.toContain("0;Window title");
            expect(result).toBe("Text with  OSC sequence");
        });

        it("should handle repeated commands in terminal output", () => {
            // Simulate terminal output with command repetition
            const input = "\u001b[1m$ command\u001b[0m\noutput line 1\noutput line 2";
            const expected = "$ command\noutput line 1\noutput line 2";
            expect(stripControlCodes(input)).toBe(expected);
        });
    });

    describe("SHELL_PROMPTS", () => {
        describe("PowerShell prompt", () => {
            const powershellPrompt = shellPrompts["powershell"];

            test("matches simple PowerShell prompt", () => {
                expect("PS > ".match(powershellPrompt)?.[0]).toBe("PS > ");
            });

            test("matches PowerShell prompt with path", () => {
                expect("PS C:\\Users\\<USER>\\Users\\user> "
                );
            });

            test("matches PowerShell prompt with just >", () => {
                expect("> ".match(powershellPrompt)?.[0]).toBe("> ");
            });

            test("extracts command correctly", () => {
                expect("PS C:\\Users\\<USER>\\Users\\user> ".match(bashPrompt)).toBeNull();
                expect("user@host:~% ".match(bashPrompt)).toBeNull();
            });
        });

        describe("Zsh prompt", () => {
            const zshPrompt = shellPrompts["zsh"];

            test("matches simple zsh prompt", () => {
                expect("% ".match(zshPrompt)?.[0]).toBe("% ");
            });

            test("matches zsh prompt with username and hostname", () => {
                expect("user@host:~% ".match(zshPrompt)?.[0]).toBe("user@host:~% ");
            });

            test("matches zsh prompt with path", () => {
                expect("user@host:/path/to/dir% ".match(zshPrompt)?.[0]).toBe(
                    "user@host:/path/to/dir% "
                );
            });

            test("extracts command correctly", () => {
                expect("% ls -la".replace(zshPrompt, "")).toBe("ls -la");
                expect("user@host:~% cd /tmp".replace(zshPrompt, "")).toBe("cd /tmp");
            });

            test("doesn't match non-zsh prompts", () => {
                expect("PS C:\\Users\\<USER>\\Users\\user> ".match(fishPrompt)).not.toBeNull(); // Fish and PowerShell prompts are similar
                expect("user@host:~$ ".match(fishPrompt)).toBeNull();
                expect("user@host:~% ".match(fishPrompt)).toBeNull();
            });
        });
    });
});

import type { APIServer } from "../../augment-api";
import type { AugmentConfigListener } from "../../augment-config-listener";
import { getLocalBranchName } from "../../remote-agent-manager/utils/git";
import { executeCommand } from "../../vcs/command-utils";
import { GitReferenceMessenger } from "../../webview-panels/remote-agents/git-reference-messenger";
import { WebViewMessageType } from "../../webview-providers/webview-messages";
import type { GetGitBranchesRequestMessage } from "../../webview-providers/webview-messages";

// Mock the executeCommand function
jest.mock("../../vcs/command-utils", () => ({
    executeCommand: jest.fn(),
}));

// Mock the getLocalBranchName function
jest.mock("../../remote-agent-manager/utils/git", () => ({
    getLocalBranchName: jest.fn((branchName: string) => {
        // Simple mock implementation that removes remote prefixes
        if (branchName.startsWith("origin/")) {
            return branchName.replace("origin/", "");
        }
        if (branchName.startsWith("upstream/")) {
            return branchName.replace("upstream/", "");
        }
        return branchName;
    }),
}));

// Mock VSCode workspace
jest.mock(
    "vscode",
    () => ({
        workspace: {
            workspaceFolders: [
                {
                    uri: {
                        fsPath: "/test/workspace",
                    },
                },
            ],
        },
    }),
    { virtual: true }
);

const mockExecuteCommand = executeCommand as jest.MockedFunction<typeof executeCommand>;
const mockGetLocalBranchName = getLocalBranchName as jest.MockedFunction<typeof getLocalBranchName>;

describe("GitReferenceMessenger", () => {
    let gitReferenceMessenger: GitReferenceMessenger;
    let mockAPIServer: APIServer;
    let mockConfigListener: AugmentConfigListener;

    beforeEach(() => {
        jest.clearAllMocks();
        mockAPIServer = {} as APIServer;
        mockConfigListener = {} as AugmentConfigListener;
        gitReferenceMessenger = new GitReferenceMessenger(mockAPIServer, mockConfigListener);
    });

    describe("handleGetGitBranchesRequest", () => {
        const mockRequest: GetGitBranchesRequestMessage = {
            type: WebViewMessageType.getGitBranchesRequest,
            data: {
                prefix: "",
            },
        };

        it("should return all remote branches without filtering default branch", async () => {
            // Setup mocks for this specific test
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("origin") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current)
                .mockResolvedValueOnce("refs/remotes/origin/main") // git symbolic-ref
                .mockResolvedValueOnce("main\nfeature\ndevelop"); // git for-each-ref

            // getLocalBranchName is called once for each branch in the filtered list
            mockGetLocalBranchName
                .mockReturnValueOnce("main") // for "main" branch
                .mockReturnValueOnce("feature") // for "feature" branch
                .mockReturnValueOnce("develop"); // for "develop" branch

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            expect(response.type).toBe(WebViewMessageType.getGitBranchesResponse);
            expect(response.data.branches).toHaveLength(3);

            // Verify that all branches are included, including the default branch
            const branchNames = response.data.branches.map((b) => b.name);
            expect(branchNames).toContain("main");
            expect(branchNames).toContain("feature");
            expect(branchNames).toContain("develop");
        });

        it("should mark default branch correctly", async () => {
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("origin") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current)
                .mockResolvedValueOnce("refs/remotes/origin/main") // git symbolic-ref
                .mockResolvedValueOnce("main\nfeature"); // git for-each-ref

            mockGetLocalBranchName
                .mockReturnValueOnce("main") // for "main" branch
                .mockReturnValueOnce("feature"); // for "feature" branch

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            const defaultBranch = response.data.branches.find((b) => b.isDefault);
            expect(defaultBranch).toBeDefined();
            expect(defaultBranch?.name).toBe("main");
        });

        it("should mark current branch correctly", async () => {
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("origin") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current)
                .mockResolvedValueOnce("refs/remotes/origin/main") // git symbolic-ref
                .mockResolvedValueOnce("main\nfeature"); // git for-each-ref

            mockGetLocalBranchName
                .mockReturnValueOnce("main") // for "main" branch
                .mockReturnValueOnce("feature"); // for "feature" branch

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            const currentBranch = response.data.branches.find((b) => b.isCurrentBranch);
            expect(currentBranch).toBeDefined();
            expect(currentBranch?.name).toBe("main");
        });

        it("should handle dev deploy scenario where current and default branch are the same", async () => {
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("origin") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current)
                .mockResolvedValueOnce("refs/remotes/origin/main") // git symbolic-ref
                .mockResolvedValueOnce("main\nfeature"); // git for-each-ref

            mockGetLocalBranchName
                .mockReturnValueOnce("main") // for "main" branch
                .mockReturnValueOnce("feature"); // for "feature" branch

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            // Both current and default should be present
            const currentBranch = response.data.branches.find((b) => b.isCurrentBranch);
            const defaultBranch = response.data.branches.find((b) => b.isDefault);

            expect(currentBranch).toBeDefined();
            expect(defaultBranch).toBeDefined();
            expect(currentBranch?.name).toBe("main");
            expect(defaultBranch?.name).toBe("main");
        });

        it("should filter branches by prefix", async () => {
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("origin") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current)
                .mockResolvedValueOnce("refs/remotes/origin/main") // git symbolic-ref
                .mockResolvedValueOnce("main\nfeature\ndevelop"); // git for-each-ref

            // Only "feature" will match the "feat" prefix, so getLocalBranchName is called once
            mockGetLocalBranchName.mockReturnValueOnce("feature"); // for "feature" branch (only one that matches "feat")

            const requestWithPrefix: GetGitBranchesRequestMessage = {
                type: WebViewMessageType.getGitBranchesRequest,
                data: {
                    prefix: "feat",
                },
            };

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](requestWithPrefix);

            expect(response.data.branches).toHaveLength(1);
            expect(response.data.branches[0].name).toBe("feature");
        });

        it("should handle different remote names", async () => {
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("upstream") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current)
                .mockResolvedValueOnce("refs/remotes/upstream/main") // git symbolic-ref
                .mockResolvedValueOnce("main\nfeature"); // git for-each-ref

            // getLocalBranchName is called once for each branch in the filtered list
            mockGetLocalBranchName
                .mockReturnValueOnce("main") // for "main" branch
                .mockReturnValueOnce("feature"); // for "feature" branch

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            expect(response.data.branches).toHaveLength(2);
            expect(response.data.branches.map((b) => b.name)).toEqual(["main", "feature"]);
        });

        it("should handle empty branch list", async () => {
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("origin") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current)
                .mockResolvedValueOnce("refs/remotes/origin/main") // git symbolic-ref
                .mockResolvedValueOnce(""); // git for-each-ref returns empty

            // No branches to process, so getLocalBranchName is not called

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            expect(response.data.branches).toHaveLength(0);
        });

        it("should handle git command failures gracefully", async () => {
            mockExecuteCommand.mockRejectedValueOnce(new Error("Git command failed")); // First command fails

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            expect(response.type).toBe(WebViewMessageType.getGitBranchesResponse);
            expect(response.data.branches).toHaveLength(0);
        });

        it("should set isRemote correctly for all branches", async () => {
            mockExecuteCommand
                .mockResolvedValueOnce("main") // git branch --show-current
                .mockResolvedValueOnce("origin") // git remote
                .mockResolvedValueOnce("") // git branch -r | grep $(git branch --show-current) - empty means not remote
                .mockResolvedValueOnce("refs/remotes/origin/main") // git symbolic-ref
                .mockResolvedValueOnce("main\nfeature"); // git for-each-ref

            mockGetLocalBranchName
                .mockReturnValueOnce("main") // for "main" branch
                .mockReturnValueOnce("feature"); // for "feature" branch

            const response =
                await gitReferenceMessenger["handleGetGitBranchesRequest"](mockRequest);

            response.data.branches.forEach((branch) => {
                if (branch.name === "main" && branch.isCurrentBranch) {
                    expect(typeof branch.isRemote).toBe("boolean");
                } else {
                    expect(branch.isRemote).toBe(true);
                }
            });
        });
    });

    describe("handleGetRemoteUrlRequest", () => {
        it("should convert SSH URL to HTTPS URL correctly", async () => {
            // Mock git remote get-url origin command
            mockExecuteCommand.mockResolvedValueOnce("**************:augmentcode/augment.git");

            const mockRequest = {} as any; // Empty request object
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("https://github.com/augmentcode/augment");
        });

        it("should handle SSH URL without .git extension", async () => {
            mockExecuteCommand.mockResolvedValueOnce("**************:augmentcode/augment");

            const mockRequest = {} as any;
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("https://github.com/augmentcode/augment");
        });

        it("should handle SSH URL with .git in repository name", async () => {
            mockExecuteCommand.mockResolvedValueOnce("**************:augmentcode/my.git.repo.git");

            const mockRequest = {} as any;
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("https://github.com/augmentcode/my.git.repo");
        });

        it("should handle SSH URL with .git in repository name but no .git extension", async () => {
            mockExecuteCommand.mockResolvedValueOnce("**************:augmentcode/my.git.repo");

            const mockRequest = {} as any;
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("https://github.com/augmentcode/my.git.repo");
        });

        it("should handle HTTPS URL without modification", async () => {
            mockExecuteCommand.mockResolvedValueOnce("https://github.com/augmentcode/augment.git");

            const mockRequest = {} as any;
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("https://github.com/augmentcode/augment.git");
        });

        it("should handle HTTPS URL with .git in repository name", async () => {
            mockExecuteCommand.mockResolvedValueOnce(
                "https://github.com/augmentcode/my.git.repo.git"
            );

            const mockRequest = {} as any;
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("https://github.com/augmentcode/my.git.repo.git");
        });

        it("should handle SSH URL with ssh:// prefix", async () => {
            mockExecuteCommand.mockResolvedValueOnce(
                "ssh://**************:augmentcode/augment.git"
            );

            const mockRequest = {} as any;
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("https://github.com/augmentcode/augment");
        });

        it("should handle git command failure gracefully", async () => {
            mockExecuteCommand.mockRejectedValueOnce(new Error("Git command failed"));

            const mockRequest = {} as any;
            const response = await gitReferenceMessenger["handleGetRemoteUrlRequest"](mockRequest);

            expect(response.data.remoteUrl).toBe("");
        });
    });
});

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { Uri, Webview } from "../../__mocks__/vscode-mocks";
import { MainPanelAppController } from "../../main-panel/main-panel-app-controller";
import { MainPanelWebview } from "../../main-panel/main-panel-webview";
import { MainPanelApp, WebViewMessageType } from "../../webview-providers/webview-messages";

describe("MainPanelWebview", () => {
    test("should load html", async () => {
        mockFSUtils.writeFileUtf8(
            "/path/common-webviews/main-panel.html",
            "<html><head></head>example of webviews/dist/main-panel.html</html>",
            true
        );
        const webview = new Webview(
            {
                enableScripts: true,
                localResourceRoots: [],
            },
            "",
            "example csp source"
        );
        const mainPanel = new MainPanelWebview(webview);
        await mainPanel.loadHTML(Uri.parse("vscode://example/path"));
        expect(webview.html).toContain("example of webviews/dist/main-panel.html");
        expect(webview.html).toContain(`<meta http-equiv="Content-Security-Policy" content=`);
    });

    test("should post app type on load and changes", async () => {
        mockFSUtils.writeFileUtf8(
            "/path/common-webviews/main-panel.html",
            "<html><head></head>example of webviews/dist/main-panel.html</html>",
            true
        );
        const webview = new Webview(
            {
                enableScripts: true,
                localResourceRoots: [],
            },
            "",
            "example csp source"
        );
        const mainPanel = new MainPanelWebview(webview);
        await mainPanel.loadHTML(Uri.parse("vscode://example/path"));

        const messageSpy = jest.spyOn(webview, "postMessage");
        expect(messageSpy).not.toHaveBeenCalled();

        expect(webview._onDidReceiveListeners.length).toBe(1);
        webview._onDidReceiveListeners[0]({
            type: WebViewMessageType.mainPanelLoaded,
        });

        expect(messageSpy).toHaveBeenCalledTimes(1);
        expect(messageSpy).toHaveBeenCalledWith({
            type: WebViewMessageType.mainPanelDisplayApp,
            data: undefined,
        });

        mainPanel.changeApp(new TestApp());

        expect(messageSpy).toHaveBeenCalledTimes(2);
        expect(messageSpy).toHaveBeenCalledWith({
            type: WebViewMessageType.mainPanelDisplayApp,
            data: MainPanelApp.chat,
        });
    });
});

class TestApp implements MainPanelAppController {
    dispose(): void {}

    appType(): MainPanelApp {
        return MainPanelApp.chat;
    }

    title(): string {
        return "Test App";
    }

    register(_webview: Webview): void {}
}

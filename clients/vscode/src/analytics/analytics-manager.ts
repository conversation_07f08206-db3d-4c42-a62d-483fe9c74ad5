import {
    disposeAnalytics,
    initializeAnalytics,
    InitializeAnalyticsCommand,
    trackEvent,
} from "@augment-internal/sidecar-libs/src/client-interfaces/analytics";
import * as os from "os";
import * as vscode from "vscode";

import { AugmentConfigListener } from "../augment-config-listener";
import { getLogger } from "../logging";
import { AugmentGlobalState, GlobalContextKey } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";

export interface AnalyticsManagerConfig {
    context: vscode.ExtensionContext;
    configListener: AugmentConfigListener;
    globalState: AugmentGlobalState;
    sessionId: string;
}

/**
 * Manages Segment analytics for the VS Code extension.
 */
export class AnalyticsManager extends DisposableService {
    private readonly _logger = getLogger("AnalyticsManager");
    private readonly _segmentWriteKey: string;
    private readonly _extensionVersion: string;
    private readonly _sessionId: string;

    constructor(private readonly _config: AnalyticsManagerConfig) {
        super();

        const isDevelopment = _config.context.extensionMode === vscode.ExtensionMode.Development;
        this._segmentWriteKey = isDevelopment
            ? "eRx2DEU2tx5boyRfgRnotNsTDKhkykS8"
            : "ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg";

        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        this._extensionVersion = _config.context.extension.packageJSON.version ?? "unknown";

        this._sessionId = _config.sessionId;

        // Listen for config changes
        this.addDisposable(
            _config.configListener.onDidChange(() => {
                void this._reinitializeAnalytics();
            })
        );

        // Listen for VS Code telemetry changes
        this.addDisposable(
            vscode.env.onDidChangeTelemetryEnabled(() => {
                void this._reinitializeAnalytics();
            })
        );
    }

    private _shouldEnableAnalytics(): boolean {
        return (
            this._config.configListener.config.enableDebugFeatures && vscode.env.isTelemetryEnabled
        );
    }

    public initialize(): void {
        const shouldEnableAnalytics = this._shouldEnableAnalytics();

        if (!shouldEnableAnalytics) {
            void disposeAnalytics();
            return;
        }

        const command: InitializeAnalyticsCommand = {
            writeKey: this._segmentWriteKey,
            context: {
                clientType: "vscode",
                clientVersion: this._extensionVersion,
                platform: os.platform(),
                arch: os.arch(),
            },
            anonymousId: this._sessionId,
        };

        initializeAnalytics(command);

        this._logger.debug("Analytics initialized and enabled");

        this._trackInstallEventIfNeeded();
    }

    private _trackInstallEventIfNeeded(): void {
        const hasTrackedInstall = this._config.globalState.get<boolean>(
            GlobalContextKey.hasTrackedInstall
        );

        if (hasTrackedInstall) {
            return;
        }

        if (!this._shouldEnableAnalytics()) {
            return;
        }

        const lastEnabledVersion = this._config.globalState.get<string>(
            GlobalContextKey.lastEnabledExtensionVersion
        );

        // Only track as "extension_installed" if there's no previous version record
        // This indicates a genuine first-time installation
        if (!lastEnabledVersion) {
            trackEvent("extension_installed");
        }

        void this._config.globalState.update(GlobalContextKey.hasTrackedInstall, true);
    }

    private async _reinitializeAnalytics(): Promise<void> {
        this._logger.debug("Reinitializing analytics due to config change");

        await disposeAnalytics();

        // Reinitialize with new settings
        this.initialize();
    }

    protected async onDispose(): Promise<void> {
        await disposeAnalytics();
        this._logger.debug("Analytics manager disposed");
    }
}

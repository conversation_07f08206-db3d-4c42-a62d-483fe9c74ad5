import * as vscode from "vscode";

export type LineRange = {
    start: number;
    stop: number;
};

/**
 * Opens a diff view in VS Code showing the difference between old and new content.
 * This version uses local file for the left side and a virtual document for the right side.
 * Note: The oldContents parameter is not used in this function as it reads from the local file.
 *
 * @param oldContents - The original content (not used, reads from local file instead)
 * @param newContents - The modified content
 * @param filePath - The path to display in the diff title
 * @param options - Optional positioning options for the diff view
 */
export async function openDiffInBuffer(
    oldContents: string,
    newContents: string,
    filePath: string,
    options?: {
        focusLine?: number;
        focusRange?: {
            start: { line: number; character: number };
            end: { line: number; character: number };
        };
    }
): Promise<void> {
    const scheme = "virtual"; // Custom URI scheme
    const provider = new VirtualDocumentProvider();
    vscode.workspace.registerTextDocumentContentProvider(scheme, provider);

    // Create two virtual URIs for the left and right sides of the diff
    // Keep the original file extension in the URI to help VS Code detect the language
    const leftUri = vscode.Uri.parse(`${scheme}:/${filePath}?left`);
    const rightUri = vscode.Uri.parse(`${scheme}:/${filePath}?right`);

    // Set the content for both sides
    provider.setDocumentContent(leftUri, oldContents || "");
    provider.setDocumentContent(rightUri, newContents || "");

    // Close any existing editor with the same title
    const diffTitle = `Diff - ${filePath}`;
    const existingTab = vscode.window.tabGroups.all
        .flatMap((group) => group.tabs)
        .find((tab) => tab.label === diffTitle);

    if (existingTab) {
        await vscode.window.tabGroups.close(existingTab);
    }

    // Prepare TextDocumentShowOptions with positioning
    let showOptions: vscode.TextDocumentShowOptions | undefined;

    if (options?.focusRange) {
        // Use explicit range if provided
        showOptions = {
            selection: new vscode.Range(
                new vscode.Position(
                    options.focusRange.start.line,
                    options.focusRange.start.character
                ),
                new vscode.Position(options.focusRange.end.line, options.focusRange.end.character)
            ),
            preserveFocus: false,
        };
    } else if (options?.focusLine !== undefined) {
        // Convert 1-based line number to 0-based and create range
        const line = Math.max(0, options.focusLine - 1);
        showOptions = {
            selection: new vscode.Range(new vscode.Position(line, 0), new vscode.Position(line, 0)),
            preserveFocus: false,
        };
    }

    // Open diff with positioning options
    await vscode.commands.executeCommand("vscode.diff", leftUri, rightUri, diffTitle, showOptions);
}

class VirtualDocumentProvider implements vscode.TextDocumentContentProvider {
    private documents: Map<string, string> = new Map();

    setDocumentContent(uri: vscode.Uri, content: string): void {
        this.documents.set(uri.toString(), content);
    }

    provideTextDocumentContent(uri: vscode.Uri): string | undefined {
        return this.documents.get(uri.toString());
    }
}

import * as vscode from "vscode";

import { DisposableService } from "./disposable-service";
import { directoryExistsAsync, makeDirs, readFileUtf8, writeFileUtf8 } from "./fs-utils";

/**
 * Event emitted when a file storage key is updated
 */
export interface FileStorageChangeEvent<T> {
    key: FileStorageOrCacheKey;
    value: T;
}

/**
 * Listener for file storage changes
 */
export type FileStorageChangeListener<T> = (event: FileStorageChangeEvent<T>) => void;

export interface IAugmentGlobalState {
    // In-memory storage. Use for smaller data.
    update<T>(key: InMemoryContextKey, value: T): Thenable<void>;
    get<T>(key: InMemoryContextKey): T | undefined;

    // Persistent storage. Use for larger data.
    save<T>(
        key: FileStorageOrCacheKey,
        value: T,
        opts?: IGlobalContextSaveLoadOpts
    ): Thenable<void>;
    load<T>(key: FileStorageOr<PERSON>ache<PERSON>ey, opts?: IGlobalContextSaveLoadOpts): Thenable<T | undefined>;

    // Event listeners for file storage changes
    onDidChangeFileStorage<T>(
        key: FileStorageOrCacheKey,
        listener: FileStorageChangeListener<T>
    ): vscode.Disposable;
}

export function setContext(key: string, value: any) {
    void vscode.commands.executeCommand("setContext", key, value);
}

// Keys for the extensionContext.globalState
export enum GlobalContextKey {
    lastEnabledExtensionVersion = "lastEnabledExtensionVersion",
    userKeybindingInfo = "userKeybindingInfo",
    sessionId = "sessionId",
    hasTrackedInstall = "hasTrackedInstall",
    actionSystemStates = "actionSystemStates",
    nextEditSuggestionSeen = "nextEditSuggestionSeen",
    nextEditSuggestionAccepted = "nextEditSuggestionAccepted",
    nextEditKeybindingUsageCount = "nextEditKeybindingUsageCount",
    nextEditUxMigrationStatus = "nextEditUxMigrationStatus",
    workspaceMessageStates = "workspaceMessageStates",
    agentAutoModeApproved = "agentAutoModeApproved",
    memoriesFileOpenCount = "memoriesFileOpenCount",
    remoteHomeInfo = "remoteHomeInfo",
    lastRemoteAgentGitRepoUrl = "lastRemoteAgentGitRepoUrl",
    lastRemoteAgentGitBranch = "lastRemoteAgentGitBranch",
    lastRemoteAgentSetupScript = "lastRemoteAgentSetupScript",
    remoteAgentThreadsHeight = "remoteAgentThreadsHeight",

    // Moved to AgentGlobalStateKeys (used in sidecar)
    // hasEverUsedAgent = "hasEverUsedAgent",
}

// put new file storage keys here
export enum FileStorageKey {
    toolsConfiguration = "toolsConfiguration",
    mcpServers = "mcpServers",
    remoteAgentNotificationEnabled = "remoteAgentNotificationEnabled",
    remoteAgentPinnedStatus = "remoteAgentPinnedStatus",
    terminalSettings = "terminalSettings",
}

// These are keys that were migrated from in-memory storage to file storage, so
// they must work in both cases.
export enum WriteBackCacheKey {
    recentlyOpenedFiles = "recentlyOpenedFiles",
    fuzzyFsFoldersIndex = "fuzzyFsFoldersIndex",
    fuzzyFsFilesIndex = "fuzzyFsFilesIndex",
    fuzzyBlobNamesToSymbols = "fuzzyBlobNamesToSymbols",
    requestIdSelectionMetadata = "requestIdSelectionMetadata",
    agentEditStore = "agentEditStore",
}

export interface IGlobalContextSaveLoadOpts {
    uniquePerWorkspace?: boolean;
}

export type InMemoryContextKey = GlobalContextKey | WriteBackCacheKey;
export type FileStorageOrCacheKey = FileStorageKey | WriteBackCacheKey;

export class AugmentGlobalState extends DisposableService implements IAugmentGlobalState {
    private static storageSubDir: string = "augment-global-state";
    private _fileStorageChangeEmitters = new Map<
        FileStorageOrCacheKey,
        vscode.EventEmitter<FileStorageChangeEvent<any>>
    >();

    constructor(private _extensionContext: vscode.ExtensionContext) {
        super();
    }

    update<T>(key: InMemoryContextKey, value: T): Thenable<void> {
        return this._extensionContext.globalState.update(key, value);
    }

    get<T>(key: InMemoryContextKey): T | undefined {
        return this._extensionContext.globalState.get(key);
    }

    async save<T>(
        key: FileStorageOrCacheKey,
        value: T,
        opts?: IGlobalContextSaveLoadOpts
    ): Promise<void> {
        await this._ensureStorageUriExists(opts);
        const uri = this._getFileUri(key, opts);
        await writeFileUtf8(uri.fsPath, JSON.stringify(value));

        const emitter = this._fileStorageChangeEmitters.get(key);
        if (emitter) {
            emitter.fire({ key, value });
        }
    }

    /**
     * Register a listener for changes to a specific file storage key.
     * @param key The key to listen for changes on
     * @param listener The callback to invoke when the key changes
     * @returns A disposable that can be used to unregister the listener
     */
    onDidChangeFileStorage<T>(
        key: FileStorageOrCacheKey,
        listener: FileStorageChangeListener<T>
    ): vscode.Disposable {
        if (!this._fileStorageChangeEmitters.has(key)) {
            const emitter = new vscode.EventEmitter<FileStorageChangeEvent<any>>();
            this.addDisposable(emitter);
            this._fileStorageChangeEmitters.set(key, emitter);
        }

        const emitter = this._fileStorageChangeEmitters.get(key)!;
        return emitter.event(listener);
    }

    override dispose(): void {
        super.dispose();
        this._fileStorageChangeEmitters.clear();
    }

    async load<T>(
        key: FileStorageOrCacheKey,
        opts?: IGlobalContextSaveLoadOpts
    ): Promise<T | undefined> {
        await this._ensureStorageUriExists(opts);
        const uri = this._getFileUri(key, opts);
        try {
            const data = await readFileUtf8(uri.fsPath);
            return JSON.parse(data) as T;
        } catch {
            return undefined;
        }
    }

    private async _ensureStorageUriExists(opts?: IGlobalContextSaveLoadOpts): Promise<void> {
        const uri = this._getStorageUri(opts);
        if (!(await directoryExistsAsync(uri.fsPath))) {
            await makeDirs(uri.fsPath);
        }
    }

    private _getStorageUri(opts?: IGlobalContextSaveLoadOpts): vscode.Uri {
        // If we are in a workspace, and we request unique per workspace,
        // use the workspace's storageUri.
        if (opts?.uniquePerWorkspace && this._extensionContext.storageUri) {
            return vscode.Uri.joinPath(
                this._extensionContext.storageUri,
                AugmentGlobalState.storageSubDir
            );
        }
        return vscode.Uri.joinPath(
            this._extensionContext.globalStorageUri,
            AugmentGlobalState.storageSubDir
        );
    }

    private _getFileUri(key: FileStorageOrCacheKey, opts?: IGlobalContextSaveLoadOpts): vscode.Uri {
        return vscode.Uri.joinPath(this._getStorageUri(opts), `${key}.json`);
    }
}

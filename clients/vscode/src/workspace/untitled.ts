import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

/**
 * Checks if a file is untitled based on its qualified path.
 * @param qualifiedPath The qualified path of the file to check.
 * @returns A boolean indicating whether the file is untitled or not.
 */
export function isUntitledFile(qualifiedPath: QualifiedPathName): boolean {
    return qualifiedPath.absPath.startsWith("Untitled-");
}

/**
 * Gets the untitled file based on the given qualified path.
 * @param qualifiedPath The qualified path of the file to find.
 * @returns The untitled vscode.TextDocument if found, otherwise undefined.
 */
export function getUntitledDoc(qualifiedPath: QualifiedPathName): vscode.TextDocument | undefined {
    const allDocs = vscode.workspace.textDocuments;
    return allDocs.find((doc) => doc.isUntitled && doc.uri.path === qualifiedPath.absPath);
}

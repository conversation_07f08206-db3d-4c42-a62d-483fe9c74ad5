import { APITiming } from "../api/api-timing";

export class CompletionTimeline extends APITiming {
    public requestStart: number;
    public emitTime?: number;

    constructor(requestStart = Date.now(), rpcStart?: number, rpcEnd?: number, emitTime?: number) {
        super();
        this.requestStart = requestStart;
        this.rpcStart = rpcStart;
        this.rpcEnd = rpcEnd;
        this.emitTime = emitTime;
    }

    public isComplete(): boolean {
        const times = [this.rpcStart, this.rpcEnd, this.emitTime];
        return times.every((t) => t !== undefined);
    }
}

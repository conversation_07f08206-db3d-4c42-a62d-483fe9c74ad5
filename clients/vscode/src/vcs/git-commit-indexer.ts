import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { BlobUploader, BlobUploadRequest } from "../workspace/open-file-manager-v2/blob-uploader";
import { GitAdapterImpl } from "./git-adapter";
import { findVCS } from "./vcs-finder";

const COMMIT_FETCH_BATCH_SIZE = 10;
const DEFAULT_BRANCH_NAMES = ["main", "master"];

/**
 * Information about a git commit.
 */
interface CommitInfo {
    hash: string;
    authorName: string;
    authorEmail: string;
    timestamp: number;
    subject: string;
    body: string;
    filesChanged: FileChange[];
}

/**
 * Information about a file change in a commit.
 */
interface FileChange {
    changeType: string;
    filePath: string;
    oldFilePath?: string;
    diff?: string;
}

/**
 * A commit with its associated blob name.
 */
interface CommitWithBlob {
    commit: CommitInfo;
    blobName: string;
}

/**
 * GitCommitIndexer is responsible for indexing git commits in the current workspace.
 * It runs at startup and uploads all git commits on the current branch (up to a configured limit)
 * if they are not yet uploaded.
 */
export class GitCommitIndexer extends DisposableService {
    private readonly _logger = getLogger("GitCommitIndexer");
    private _folderCommitBlobNames = new Map<string, string[]>();
    private _isIndexing = false;
    private _checkpointId: string | undefined;
    private readonly _blobUploader: BlobUploader;

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _blobNameCalculator: BlobNameCalculator,
        private readonly _featureFlagManager: FeatureFlagManager
    ) {
        super();
        this._blobUploader = new BlobUploader(this._blobNameCalculator, this._apiServer);

        // Register for feature flag changes
        this.addDisposables(
            this._featureFlagManager.subscribe(["enableCommitIndexing"], () => {
                if (this._featureFlagManager.currentFlags.enableCommitIndexing) {
                    void this.indexCommits();
                }
            }),
            this._blobUploader
        );

        // Start indexing if enabled
        if (this._featureFlagManager.currentFlags.enableCommitIndexing) {
            void this.indexCommits();
        }
    }

    /**
     * Index git commits in the current workspace.
     * Creates a checkpoint after indexing all commits to avoid re-indexing the same commits in the future.
     */
    public async indexCommits(): Promise<void> {
        if (this._isIndexing) {
            this._logger.debug("Already indexing commits, skipping");
            return;
        }

        this._isIndexing = true;
        try {
            this._folderCommitBlobNames.clear();
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                this._logger.debug("No workspace folders found, skipping commit indexing");
                return;
            }

            for (const folder of workspaceFolders) {
                await this._indexCommitsInFolder(folder);
            }

            // After indexing all folders, create a checkpoint with all the blob names
            await this._createCheckpoint();
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error indexing commits: ${error.message}`);
        } finally {
            this._isIndexing = false;
        }
    }

    /**
     * Creates a checkpoint with all the currently indexed commit blob names.
     * This allows us to efficiently track which commits have been indexed.
     */
    private async _createCheckpoint(): Promise<void> {
        // Collect all blob names from all folders
        const allBlobNames: string[] = [];
        for (const blobNames of this._folderCommitBlobNames.values()) {
            allBlobNames.push(...blobNames);
        }

        if (allBlobNames.length === 0) {
            this._logger.debug("No commits to checkpoint");
            return;
        }

        try {
            // Create a checkpoint using the existing checkpoint mechanism
            const result = await this._apiServer.checkpointBlobs({
                checkpointId: undefined,
                addedBlobs: allBlobNames,
                deletedBlobs: [],
            });

            // Store the new checkpoint ID for future use
            this._checkpointId = result.newCheckpointId;
            this._logger.debug(`Created commit checkpoint with ID: ${this._checkpointId}`);
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error creating commit checkpoint: ${error.message}`);
        }
    }

    /**
     * Index git commits in a specific workspace folder.
     */
    private async _indexCommitsInFolder(folder: vscode.WorkspaceFolder): Promise<void> {
        try {
            const vcsDetails = await findVCS(folder.uri.fsPath);
            if (!vcsDetails) {
                this._logger.debug(
                    `No VCS found in folder ${folder.name}, skipping commit indexing`
                );
                return;
            }

            this._logger.debug(`Indexing commits in folder ${folder.name}`);

            // Create a git adapter for the folder
            const gitAdapter = new GitAdapterImpl(vcsDetails.root);

            // Get the branch to use for indexing
            // Our _getDefaultBranch function now tries to get the default branch from remote,
            // then tries common branch names locally, and finally falls back to the current branch
            const branchToUse = await this._getDefaultBranch(gitAdapter);
            if (!branchToUse) {
                this._logger.debug(
                    `Could not determine any branch in ${folder.name}, skipping commit indexing`
                );
            }

            this._logger.debug(`Using branch '${branchToUse}' for indexing in ${folder.name}`);

            // Get commits on the branch
            const maxCommits = this._featureFlagManager.currentFlags.maxCommitsToIndex;
            const commits = await this._getCommits(gitAdapter, branchToUse, maxCommits);

            if (commits.length === 0) {
                this._logger.debug(`No commits found on branch ${branchToUse} in ${folder.name}`);
                return;
            }
            this._logger.debug(
                `Found ${commits.length} commits on branch ${branchToUse} in ${folder.name}`
            );

            const commitsWithBlobs: CommitWithBlob[] = commits.map((commit) => {
                const blobName = this._getBlobName(commit);
                return { commit, blobName };
            });

            const commitsToUpload = commitsWithBlobs;

            this._folderCommitBlobNames.set(
                folder.uri.fsPath,
                commitsWithBlobs.map((cwb) => cwb.blobName)
            );

            if (commitsToUpload.length === 0) {
                this._logger.debug(`All commits are already indexed in ${folder.name}`);
                return;
            }

            this._logger.debug(`Uploading ${commitsToUpload.length} commits in ${folder.name}`);

            if (commitsToUpload.length > 0) {
                this._logger.debug(
                    `Enqueuing ${commitsToUpload.length} commits for upload in ${folder.name}`
                );
                for (const commitWithBlob of commitsToUpload) {
                    const uploadRequest: BlobUploadRequest = {
                        path: `git://commit/${commitWithBlob.commit.hash}`,
                        readContent: async () =>
                            Promise.resolve(this._formatCommitAsString(commitWithBlob.commit)),
                    };
                    this._blobUploader.enqueueUpload(
                        uploadRequest,
                        this._formatCommitAsString(commitWithBlob.commit)
                    );
                }
                this._blobUploader.startUpload();
                this._logger.debug(
                    `Started upload for ${commitsToUpload.length} commits in ${folder.name}`
                );
            }

            this._logger.debug(`Finished indexing commits in ${folder.name}`);
        } catch (error: any) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            this._logger.debug(`Error indexing commits in folder ${folder.name}: ${error.message}`);
        }
    }

    /**
     * Get the default branch for the repository.
     * First tries to get it from remote (origin/HEAD), then tries common names locally,
     * and finally falls back to the current branch if needed.
     */
    private async _getDefaultBranch(gitAdapter: GitAdapterImpl): Promise<string | undefined> {
        // First try to get the default branch from remote (origin/HEAD)
        try {
            const remoteHeadResult = await gitAdapter.symbolicRef({
                name: "refs/remotes/origin/HEAD",
            });
            if (remoteHeadResult) {
                // Format is typically "refs/remotes/origin/main"
                return remoteHeadResult.trim().replace("refs/remotes/origin/", "");
            }
        } catch {
            // Remote HEAD reference doesn't exist, continue to fallbacks
        }

        // Try common default branch names locally
        for (const branch of DEFAULT_BRANCH_NAMES) {
            try {
                const result = await gitAdapter.symbolicRef({ name: `refs/heads/${branch}` });
                if (result) {
                    return branch;
                }
            } catch {
                // Branch doesn't exist, try the next one
            }
        }

        // As a final fallback, get the current branch
        try {
            const currentBranchResult = await gitAdapter.symbolicRef({ name: "HEAD" });
            if (currentBranchResult) {
                return currentBranchResult.trim().replace(/^refs\/heads\//, "");
            }
        } catch {
            // Failed to get current branch
        }

        return undefined;
    }

    /**
     * Get commits on the default branch.
     */
    private async _getCommits(
        gitAdapter: GitAdapterImpl,
        branch: string | undefined,
        maxCommits: number
    ): Promise<CommitInfo[]> {
        const FORMAT = "%H%n%an%n%ae%n%at%n%s%n%b";
        const SEPARATOR = "---COMMIT---";

        const commits: CommitInfo[] = [];
        let skipCount = 0;
        let hasMoreCommits = true;

        // Fetch commits in batches until we reach the maximum or run out of commits
        while (hasMoreCommits && commits.length < maxCommits) {
            // Calculate how many commits to fetch in this batch.
            // We need to batch because if the output is too long child_process throws an error.
            const batchSize = Math.min(COMMIT_FETCH_BATCH_SIZE, maxCommits - commits.length);

            this._logger.verbose(`Fetching batch of ${batchSize} commits (skip=${skipCount})`);

            // Use skip parameter to paginate through commits
            const logResult = await gitAdapter.log({
                format: `${FORMAT}${SEPARATOR}`,
                commit1: `-n${batchSize} --skip=${skipCount}`,
                commit2: branch,
                noMerges: true,
            });

            if (!logResult || logResult.trim() === "") {
                hasMoreCommits = false;
                break;
            }

            const commitStrings = logResult.split(`${SEPARATOR}\n`);
            let batchCommitCount = 0;

            for (const commitString of commitStrings) {
                if (!commitString.trim()) {
                    continue;
                }

                const lines = commitString.split("\n");
                if (lines.length < 5) {
                    continue;
                }

                const hash = lines[0];
                const authorName = lines[1];
                const authorEmail = lines[2];
                const timestamp = parseInt(lines[3], 10);
                const subject = lines[4];
                const body = lines.slice(5).join("\n").trim();

                // Get the files changed in this commit
                const filesChanged = await this._getFilesChangedInCommit(gitAdapter, hash);

                commits.push({
                    hash,
                    authorName,
                    authorEmail,
                    timestamp,
                    subject,
                    body,
                    filesChanged,
                });

                batchCommitCount++;

                if (commits.length >= maxCommits) {
                    break;
                }
            }

            // If we got fewer commits than requested, there are no more commits
            if (batchCommitCount < batchSize) {
                hasMoreCommits = false;
            }

            // Update skip count for the next batch
            skipCount += batchCommitCount;

            this._logger.verbose(
                `Retrieved ${batchCommitCount} commits in this batch, total: ${commits.length}`
            );
        }

        return commits;
    }

    /**
     * Get the files changed in a specific commit, including their diffs.
     */
    private async _getFilesChangedInCommit(
        gitAdapter: GitAdapterImpl,
        commitHash: string
    ): Promise<FileChange[]> {
        // First, get the list of files changed in the commit
        const showResult = await gitAdapter.show({
            object: commitHash,
            nameStatus: true,
        });
        if (!showResult) {
            return [];
        }

        const lines = showResult.split("\n").slice(1); // Skip the first line (commit hash)
        const filesChanged: FileChange[] = [];
        const fileChangeMap = new Map<string, FileChange>();
        for (const line of lines) {
            const parts = line.split("\t");
            if (parts.length < 2) {
                continue;
            }

            const changeType = parts[0];
            // parts[1] is always the file path, parts[2] is the new path for renames
            const filePath = parts.length > 2 ? parts[2] : parts[1];
            const oldFilePath = parts.length > 2 ? parts[1] : undefined;
            const fileChange = {
                changeType,
                filePath,
                oldFilePath,
            };

            filesChanged.push(fileChange);

            // Store the file change by both paths for diff lookup
            fileChangeMap.set(filePath, fileChange);
            if (oldFilePath) {
                fileChangeMap.set(oldFilePath, fileChange);
            }
        }

        // Now, get the full diff for the commit
        const fullDiff = await gitAdapter.show({
            object: commitHash,
            patch: true,
        });
        if (fullDiff) {
            // Parse the full diff to extract per-file diffs
            this._extractFileDiffs(fullDiff, fileChangeMap);
        }

        return filesChanged;
    }

    /**
     * Extract individual file diffs from a full commit diff.
     */
    private _extractFileDiffs(fullDiff: string, fileChangeMap: Map<string, FileChange>): void {
        // Split the diff by file
        const diffLines = fullDiff.split("\n");
        let currentFile: string | null = null;
        let currentDiff: string[] = [];
        let inHeader = false;
        let headerEndIndex = -1;

        for (let i = 0; i < diffLines.length; i++) {
            const line = diffLines[i];

            // Check if this line starts a new file diff
            if (line.startsWith("diff --git ")) {
                // If we were processing a file, save its diff
                if (currentFile !== null && headerEndIndex !== -1) {
                    // Only include lines after the header
                    const cleanDiff = currentDiff.slice(headerEndIndex + 1).join("\n");
                    this._saveFileDiff(currentFile, cleanDiff, fileChangeMap);
                }

                // Reset for the new file
                currentDiff = [];
                headerEndIndex = -1;
                inHeader = true;

                // Extract the file path from the diff header
                // Format: "diff --git a/path/to/file b/path/to/file"
                const match = line.match(/diff --git a\/(.+) b\/.+/);
                if (match && match[1]) {
                    currentFile = match[1];
                } else {
                    currentFile = null;
                }
            }

            // Check if we're still in the header section
            if (inHeader) {
                // Headers typically end when we see the first line starting with "@@"
                if (line.startsWith("@@")) {
                    inHeader = false;
                    headerEndIndex = currentDiff.length;
                }
            }

            // Add the line to the current diff
            if (currentFile !== null) {
                currentDiff.push(line);
            }
        }

        // Save the last file's diff
        if (currentFile !== null && headerEndIndex !== -1) {
            // Only include lines after the header
            const cleanDiff = currentDiff.slice(headerEndIndex + 1).join("\n");
            this._saveFileDiff(currentFile, cleanDiff, fileChangeMap);
        }
    }

    /**
     * Save a file diff to the corresponding FileChange object.
     */
    private _saveFileDiff(
        filePath: string,
        diff: string,
        fileChangeMap: Map<string, FileChange>
    ): void {
        if (diff.trim() === "") {
            return;
        }
        const fileChange = fileChangeMap.get(filePath);
        if (fileChange) {
            fileChange.diff = diff;
        }
    }

    private _formatCommitAsString(commit: CommitInfo): string {
        return JSON.stringify(commit);
    }

    /**
     * Get the blob name for a commit.
     */
    private _getBlobName(commit: CommitInfo): string {
        return this._blobNameCalculator.calculateNoThrow(
            `git://commit/${commit.hash}`,
            this._formatCommitAsString(commit)
        );
    }

    /**
     * Get all blob names for commits in a specific folder.
     * @param folderPath The folder path to get blob names for
     * @returns Array of blob names, or empty array if no commits are found
     */
    public getBlobNamesForFolder(folderPath: string): string[] {
        return this._folderCommitBlobNames.get(folderPath) ?? [];
    }

    /**
     * Get the current checkpoint ID for git commits.
     * This can be used to efficiently retrieve all indexed commits.
     * @returns The checkpoint ID, or undefined if no checkpoint has been created
     */
    public getCheckpointId(): string | undefined {
        return this._checkpointId;
    }
}

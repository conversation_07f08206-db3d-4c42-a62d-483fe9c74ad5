import { McpServerConfig } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { omit } from "lodash";
import * as vscode from "vscode";
import { SafeParseReturnType, z } from "zod";

import { type AugmentLogger, getLogger } from "./logging";
import { MonitoredParameter } from "./monitored-parameter";
import { DisposableService } from "./utils/disposable-service";

export const DEFAULT_AUGMENT_OAUTH = "https://auth.augmentcode.com";

export type CodeInstructionConfig = {
    model?: string;
};

const DEFAULT_MODEL_DISPLAY_NAME_TO_ID = {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    Augment: null,
};
export type ChatConfig = {
    url?: string;
    model?: string;
    modelDisplayNameToId?: {
        [key: string]: string | null;
    };
    stream?: boolean;
    useRichTextHistory?: boolean;
    smartPasteUsePrecomputation?: boolean;
    experimentalFullFilePaste?: boolean;
    enableEditableHistory?: boolean;
    userGuidelines?: string;
};

export type AgentConfig = {
    model?: string;
};

export type AutofixConfig = {
    enabled?: boolean;
    locationUrl?: string;
    autofixUrl?: string;
};

export type OAuthConfig = {
    clientID?: string;
    url?: string;
};

export type RecencySignalManagerConfig = {
    collectTabSwitchEvents: boolean;
};

type NextEditAdvancedConfig = {
    enabled?: boolean; // TODO is this deprecated too?
    // TODO: Deprecate and replace with augment.nextEdit.enableBackgroundSuggestions.
    backgroundEnabled: boolean;

    url?: string;
    locationUrl?: string;
    generationUrl?: string;

    model?: string;

    /** How long to wait before sending a new request. */
    useDebounceMs?: number;

    /** When enabled, use cursor decorations instead of bottom decorations. */
    useCursorDecorations?: boolean;

    allowDuringDebugging?: boolean;

    /** Use mock results if $filename.next-edit-results.json5 exists. */
    useMockResults?: boolean;

    noDiffModeUseCodeLens?: boolean;

    enableBottomPanel?: boolean;
};

type NextEditBaseConfig = {
    enableBackgroundSuggestions: boolean;
    enableGlobalBackgroundSuggestions: boolean;
    highlightSuggestionsInTheEditor: boolean;
    showDiffInHover: boolean;
    enableAutoApply: boolean;
};

type NextEditConfig = NextEditBaseConfig & NextEditAdvancedConfig;

export type PreferenceCollectionConfig = {
    enable: boolean;
    enableRetrievalDataCollection: boolean;
    enableRandomizedMode: boolean;
};

export type SmartPasteConfig = {
    url?: string;
    model?: string;
};

export type InstructionsConfig = {
    model?: string;
};

// TODO: In the future these should be user-visible settings.
export type IntegrationsConfig = {
    atlassian?: {
        serverUrl: string;
        personalApiToken: string;
        username: string;
    };
    notion?: {
        apiToken: string;
    };
    linear?: {
        apiToken: string;
    };
    github?: {
        apiToken: string;
    };
};

export type GitConfig = {};

// NOTE: Prefer `enable*` for naming in config.
/**
 * UserConfig matches the shape of the settings.json, but puts types on it.
 * We normalize it to give all the fields we expect in there with sane defaults.
 * After normalizing we change the shape to AugmentConfig.
 * AugmentConfig is the canonical configuration that everything in the extension is based on.
 */
export type AugmentConfig = {
    apiToken: string;
    completionURL: string;
    codeInstruction: CodeInstructionConfig;
    chat: ChatConfig;
    agent: AgentConfig;
    autofix: AutofixConfig;
    modelName: string;
    enableUpload: boolean;
    shortcutsDisplayDelayMS: number;
    enableEmptyFileHint: boolean;
    enableDataCollection: boolean;
    enableDebugFeatures: boolean;
    enableReviewerWorkflows: boolean;
    oauth: OAuthConfig;
    completions: {
        // User settings
        enableAutomaticCompletions: boolean;
        disableCompletionsByLanguage: Set<string>;
        enableQuickSuggestions: boolean;

        // Advanced
        timeoutMs: number;
        maxWaitMs: number;
        addIntelliSenseSuggestions: boolean;
        filterThreshold?: number;
    };
    openFileManager: {
        v2Enabled?: boolean;
    };
    nextEdit: NextEditConfig;
    recencySignalManager: RecencySignalManagerConfig;
    preferenceCollection: PreferenceCollectionConfig;
    vcs: {
        watcherEnabled: boolean;
    };
    git: GitConfig;
    conflictingCodingAssistantCheck: boolean;
    smartPaste: SmartPasteConfig;
    instructions: InstructionsConfig;
    integrations: IntegrationsConfig;
    mcpServers: McpServerConfig[];

    advanced: {
        personalityPrompts?: {
            agent?: string;
            prototyper?: string;
            brainstorm?: string;
            reviewer?: string;
        };
    };
};
export type AugmentConfigKey = keyof AugmentConfig;

const rawSettingsSchema = z.object({
    // deprecated. use advanced.apiToken
    apiToken: z.string(),
    // deprecated. use advanced.completionURL
    completionURL: z.string(),
    // deprecated. use completions.disableCompletionsByLanguage
    disableCompletionsByLanguage: z.array(z.string()),
    // deprecated. use completions.enableAutomaticCompletions
    enableAutomaticCompletions: z.boolean(),

    completions: z.object({
        enableAutomaticCompletions: z.boolean(),
        disableCompletionsByLanguage: z.array(z.string()),
        enableQuickSuggestions: z.boolean(),
    }),
    shortcutsDisplayDelayMS: z.number(),
    enableEmptyFileHint: z.boolean(),
    conflictingCodingAssistantCheck: z.boolean(),

    chat: z.object({
        userGuidelines: z.string(),
    }),
    agent: z.object({
        model: z.string(),
    }),
    nextEdit: z.object({
        enableBackgroundSuggestions: z.boolean(),
        enableGlobalBackgroundSuggestions: z.boolean(),
        highlightSuggestionsInTheEditor: z.boolean(),
        showDiffInHover: z.boolean(),
        enableAutoApply: z.boolean(),
    }),
    advanced: z.object({
        apiToken: z.string(),
        completionURL: z.string(),
        // The following are internal only
        oauth: z.object({
            clientID: z.string(),
            url: z.string(),
        }),
        model: z.string(),
        codeInstruction: z.object({
            model: z.string(),
        }),
        chat: z.object({
            url: z.string(),
            model: z.string(),
            stream: z.boolean(),
            enableEditableHistory: z.boolean(),
            useRichTextHistory: z.boolean(),
            smartPasteUsePrecomputation: z.boolean(),
            experimentalFullFilePaste: z.boolean(),
            modelDisplayNameToId: z.record(z.string().nullable()),
        }),
        agent: z.object({
            model: z.string(),
        }),
        autofix: z.object({
            enabled: z.boolean(),
            locationUrl: z.string(),
            autofixUrl: z.string(),
        }),
        enableDebugFeatures: z.boolean(),
        enableWorkspaceUpload: z.boolean(),
        enableReviewerWorkflows: z.boolean(),
        completions: z.object({
            timeoutMs: z.number(),
            maxWaitMs: z.number(),
            addIntelliSenseSuggestions: z.boolean(),
            filterThreshold: z.number(),
            // deprecated. use filterThreshold.
            filter_threshold: z.number(), // eslint-disable-line @typescript-eslint/naming-convention
        }),
        openFileManager: z.object({
            v2Enabled: z.boolean(),
        }),
        enableDataCollection: z.boolean(),
        nextEditURL: z.string(),
        nextEditLocationURL: z.string(),
        nextEditGenerationURL: z.string(),
        nextEditBackgroundGeneration: z.boolean(),
        nextEdit: z.object({
            enabled: z.boolean(),
            backgroundEnabled: z.boolean(),
            url: z.string(),
            locationUrl: z.string(),
            generationUrl: z.string(),
            model: z.string(),
            useDebounceMs: z.number(),
            useCursorDecorations: z.boolean(),
            allowDuringDebugging: z.boolean(),
            useMockResults: z.boolean(),
            noDiffModeUseCodeLens: z.boolean(),
            enableBottomPanel: z.boolean(),
        }),
        recencySignalManager: z.object({
            collectTabSwitchEvents: z.boolean(),
        }),
        preferenceCollection: z.object({
            enable: z.boolean(),
            enableRetrievalDataCollection: z.boolean(),
            enableRandomizedMode: z.boolean(),
        }),
        vcs: z.object({
            watcherEnabled: z.boolean(),
        }),
        git: z.object({}),
        smartPaste: z.object({
            url: z.string(),
            model: z.string(),
        }),
        instructions: z.object({
            model: z.string(),
        }),
        integrations: z.object({
            atlassian: z.object({
                serverUrl: z.string(),
                personalApiToken: z.string(),
                username: z.string(),
            }),
            notion: z.object({
                apiToken: z.string(),
            }),
            linear: z.object({
                apiToken: z.string(),
            }),
            github: z.object({
                apiToken: z.string(),
            }),
        }),
        mcpServers: z.array(
            z.object({
                command: z.string(),
                args: z.array(z.string()),
                timeoutMs: z.number(),
                env: z.record(z.string()),
            })
        ),
        personalityPrompts: z
            .object({
                agent: z.string().optional(),
                prototyper: z.string().optional(),
                brainstorm: z.string().optional(),
                reviewer: z.string().optional(),
            })
            .optional(),
    }),
});
// deprecation of deepPartial is unclear. https://github.com/colinhacks/zod/issues/2854#issuecomment-2646548991
// using it until more information is available.
const partialRawSettingsSchema = rawSettingsSchema.deepPartial();

// exported for testing
export type PartialRawSettings = z.infer<typeof partialRawSettingsSchema>;

/**
 * AugmentConfigListener is a class that listens for changes to the extension
 * configuration. It logs configuration changes of interest and notifies listeners
 * when the config has changed. It is preferrable to listen to configuration changes
 * here than to use your own `onDidChangeConfiguration` event listener, as this class
 * will log configuration changes before notifying listeners.
 */
export class AugmentConfigListener extends DisposableService {
    private _config!: AugmentConfig;
    private _configChanged = new vscode.EventEmitter<ConfigChanges>();

    private _configMonitor: MonitoredParameter<AugmentConfig>;

    private readonly _logger: AugmentLogger = getLogger("AugmentConfigListener");

    constructor() {
        super();

        this._configMonitor = new MonitoredParameter<AugmentConfig>("Config", this._logger);

        this._refreshConfig();
        this.addDisposable(
            vscode.workspace.onDidChangeConfiguration(() => {
                return this._refreshConfig();
            })
        );
    }

    // `config` is the current configuration.
    get config(): Readonly<AugmentConfig> {
        return this._config;
    }

    // onDidChange is an event that clients can listen on to be notified of changes
    // to the extension configuration.
    get onDidChange(): vscode.Event<ConfigChanges> {
        return this._configChanged.event;
    }

    // _refreshConfig caches the current extension configuration and logs changes of
    // interest.
    private _refreshConfig() {
        const previousConfig = this._config;
        this._config = AugmentConfigListener.normalizeConfig(this._getRawSettings());
        if (this._configMonitor.update(this._config)) {
            this._configChanged.fire({
                previousConfig,
                newConfig: this._config,
            });
        }
    }

    // normalizeConfig converts UserConfig to an AugmentConfig.
    public static normalizeConfig(config: PartialRawSettings): AugmentConfig {
        return {
            apiToken: (config?.advanced?.apiToken ?? config.apiToken ?? "").trim().toUpperCase(),
            completionURL: (config?.advanced?.completionURL ?? config.completionURL ?? "").trim(),
            modelName: config?.advanced?.model ?? "",
            conflictingCodingAssistantCheck: config.conflictingCodingAssistantCheck ?? true,
            codeInstruction: {
                model: config.advanced?.codeInstruction?.model,
            },
            chat: {
                url: config.advanced?.chat?.url,
                model: config.advanced?.chat?.model,
                stream: config.advanced?.chat?.stream,
                enableEditableHistory: config.advanced?.chat?.enableEditableHistory ?? false,
                useRichTextHistory: config.advanced?.chat?.useRichTextHistory ?? true,
                smartPasteUsePrecomputation:
                    config.advanced?.chat?.smartPasteUsePrecomputation ?? true,
                experimentalFullFilePaste:
                    config.advanced?.chat?.experimentalFullFilePaste ?? false,
                modelDisplayNameToId:
                    config.advanced?.chat?.modelDisplayNameToId ?? DEFAULT_MODEL_DISPLAY_NAME_TO_ID,
                userGuidelines: config.chat?.userGuidelines || "",
            },
            agent: {
                model: config.advanced?.agent?.model,
            },
            autofix: {
                enabled: config.advanced?.autofix?.enabled ?? false,
                locationUrl: config.advanced?.autofix?.locationUrl,
                autofixUrl: config.advanced?.autofix?.autofixUrl,
            },
            oauth: {
                clientID: config.advanced?.oauth?.clientID ?? "augment-vscode-extension",
                url: config.advanced?.oauth?.url ?? DEFAULT_AUGMENT_OAUTH,
            },
            enableUpload: config.advanced?.enableWorkspaceUpload ?? true,
            shortcutsDisplayDelayMS: config.shortcutsDisplayDelayMS ?? 2000,
            enableEmptyFileHint: config.enableEmptyFileHint ?? true,
            enableDataCollection: config.advanced?.enableDataCollection ?? false,
            enableDebugFeatures: config.advanced?.enableDebugFeatures ?? false,
            enableReviewerWorkflows: config.advanced?.enableReviewerWorkflows ?? false,
            completions: {
                enableAutomaticCompletions:
                    config.enableAutomaticCompletions ??
                    config.completions?.enableAutomaticCompletions ??
                    true,
                disableCompletionsByLanguage: new Set(
                    config.disableCompletionsByLanguage ??
                        config.completions?.disableCompletionsByLanguage ??
                        [] // not sure why typescript insists of this here..
                ),

                enableQuickSuggestions: config.completions?.enableQuickSuggestions ?? true,

                timeoutMs: config.advanced?.completions?.timeoutMs ?? 800,
                maxWaitMs: config.advanced?.completions?.maxWaitMs ?? 1600,
                addIntelliSenseSuggestions:
                    config.advanced?.completions?.addIntelliSenseSuggestions ?? true,
                filterThreshold: config.advanced?.completions?.filter_threshold,
            },
            openFileManager: {
                v2Enabled: config.advanced?.openFileManager?.v2Enabled,
            },
            nextEdit: {
                enabled: config.advanced?.nextEdit?.enabled,
                backgroundEnabled: config.advanced?.nextEdit?.backgroundEnabled ?? true,

                url: config.advanced?.nextEdit?.url,
                locationUrl:
                    config.advanced?.nextEdit?.locationUrl ?? config.advanced?.nextEdit?.url,
                generationUrl:
                    config.advanced?.nextEdit?.generationUrl ?? config.advanced?.nextEdit?.url,
                model: config.advanced?.nextEdit?.model,
                useDebounceMs: config.advanced?.nextEdit?.useDebounceMs,
                useCursorDecorations: config.advanced?.nextEdit?.useCursorDecorations ?? false,
                allowDuringDebugging: config.advanced?.nextEdit?.allowDuringDebugging ?? false,
                useMockResults: config.advanced?.nextEdit?.useMockResults ?? false,
                noDiffModeUseCodeLens: config.advanced?.nextEdit?.noDiffModeUseCodeLens ?? false,
                enableBackgroundSuggestions: config.nextEdit?.enableBackgroundSuggestions ?? true,
                enableGlobalBackgroundSuggestions:
                    config.nextEdit?.enableGlobalBackgroundSuggestions ?? false,
                highlightSuggestionsInTheEditor:
                    config.nextEdit?.highlightSuggestionsInTheEditor ?? false,
                showDiffInHover: config.nextEdit?.showDiffInHover ?? false,
                enableAutoApply: config.nextEdit?.enableAutoApply ?? true,
                enableBottomPanel: config.advanced?.nextEdit?.enableBottomPanel,
            },
            recencySignalManager: {
                collectTabSwitchEvents:
                    config.advanced?.recencySignalManager?.collectTabSwitchEvents ?? false,
            },
            preferenceCollection: {
                enable: config.advanced?.preferenceCollection?.enable ?? false,
                enableRetrievalDataCollection:
                    config.advanced?.preferenceCollection?.enableRetrievalDataCollection ?? false,
                enableRandomizedMode:
                    config.advanced?.preferenceCollection?.enableRandomizedMode ?? true,
            },
            vcs: {
                watcherEnabled: config.advanced?.vcs?.watcherEnabled ?? false,
            },
            git: {},
            smartPaste: {
                url: config.advanced?.smartPaste?.url,
                model: config.advanced?.smartPaste?.model,
            },
            instructions: {
                model: config.advanced?.instructions?.model,
            },
            integrations: {
                atlassian: config.advanced?.integrations?.atlassian
                    ? {
                          serverUrl: config.advanced.integrations.atlassian.serverUrl || "",
                          personalApiToken:
                              config.advanced.integrations.atlassian.personalApiToken || "",
                          username: config.advanced.integrations.atlassian.username || "",
                      }
                    : undefined,
                notion: config.advanced?.integrations?.notion
                    ? {
                          apiToken: config.advanced.integrations.notion.apiToken || "",
                      }
                    : undefined,
                linear: config.advanced?.integrations?.linear
                    ? {
                          apiToken: config.advanced.integrations.linear.apiToken || "",
                      }
                    : undefined,
                github: config.advanced?.integrations?.github
                    ? {
                          apiToken: config.advanced.integrations.github.apiToken || "",
                      }
                    : undefined,
            },
            mcpServers: ((config.advanced?.mcpServers ?? []) as McpServerConfig[]) // not sure why typescript insists of this here..
                .filter((server) => server !== undefined)
                .map((server) => ({
                    command: expandVariables(server.command || ""),
                    args: server.args?.map((arg: string) => expandVariables(arg)),
                    timeoutMs: server.timeoutMs,
                    env: server.env,
                })),

            advanced: {
                personalityPrompts: config.advanced?.personalityPrompts,
            },
        };
    }

    private _getRawSettings(): PartialRawSettings {
        const config = vscode.workspace.getConfiguration("augment");

        return AugmentConfigListener.parseSettings(config);
    }

    /**
     * This method will move old settings to a new key.
     *
     * Because the APIs for this are not synchronous, you must either
     * check the old and new keys when normalizing the config OR you
     * must anticipate the new values getting updated shortly after
     * initialization.
     */
    async migrateLegacyConfig() {
        const config = vscode.workspace.getConfiguration("augment");
        // Setting was moved on 2024-09 (approx. v0.211.0)
        await this._moveConfig(
            config,
            "enableAutomaticCompletions",
            "completions.enableAutomaticCompletions"
        );

        // Setting was moved on 2024-09 (approx. v0.211.0)
        await this._moveConfig(
            config,
            "disableCompletionsByLanguage",
            "completions.disableCompletionsByLanguage"
        );

        await this._moveConfig(
            config,
            "enableBackgroundSuggestions",
            "nextEdit.enableBackgroundSuggestions"
        );
        await this._moveConfig(
            config,
            "enableGlobalBackgroundSuggestions",
            "nextEdit.enableGlobalBackgroundSuggestions"
        );
        await this._moveConfig(
            config,
            "highlightSuggestionsInTheEditor",
            "nextEdit.highlightSuggestionsInTheEditor"
        );
    }

    /**
     * This method moves a users setting from one key to another.
     *
     * VSCode has two locations where settings can be kept, at
     * the global level and at the workspace level. To move a config
     * we need to copy values from both global and workspace level
     * to the new key.
     *
     * If there is an old key AND a new key value, we drop the old
     * key and leave the new value as is.
     *
     * @param config
     * @param oldKey
     * @param newKey
     * @returns
     */
    private async _moveConfig(
        config: vscode.WorkspaceConfiguration,
        oldKey: string,
        newKey: string
    ) {
        const oldValue = config.inspect(oldKey);
        if (!oldValue) {
            return;
        }

        const newValue = config.inspect(newKey);
        const targets = [
            {
                target: vscode.ConfigurationTarget.Workspace,
                oldValue: oldValue.workspaceValue,
                newValue: newValue?.workspaceValue,
            },
            {
                target: vscode.ConfigurationTarget.Global,
                oldValue: oldValue.globalValue,
                newValue: newValue?.globalValue,
            },
        ];
        for (const t of targets) {
            if (t.oldValue === undefined) {
                // There is no old value to copy over, so do nothing.
                continue;
            }
            if (t.newValue === undefined) {
                // Update config for target if the new config hasn't been
                // set to a user value.
                await config.update(newKey, t.oldValue, t.target);
            }
            // Remove the old config value
            await config.update(oldKey, undefined, t.target);
        }
    }

    public static parseSettings(config: vscode.WorkspaceConfiguration): PartialRawSettings {
        const _logger = getLogger("AugmentConfigListener");
        const result: SafeParseReturnType<{}, PartialRawSettings> =
            partialRawSettingsSchema.safeParse(config);
        if (!result.success) {
            const validationErrors = result.error.issues.map(
                (issue) => "[" + issue.path.join(".") + "]: " + issue.message
            );
            _logger.error(`Failed to parse settings: \n${validationErrors.join("\n")}`);

            // remove problematic fields for best effort.
            const problematicFields = result.error.issues.map((issue) => issue.path.join("."));
            const cleanedConfig = omit(JSON.parse(JSON.stringify(config)), problematicFields);
            const cleanResult = partialRawSettingsSchema.safeParse(cleanedConfig);
            if (cleanResult.success) {
                _logger.info("settings parsed successfully after cleaning");
                return cleanResult.data;
            }
            return {};
        }
        _logger.info("settings parsed successfully");
        return result.data;
    }
}

export type ConfigChanges = {
    previousConfig: AugmentConfig;
    newConfig: AugmentConfig;
};

function expandVariables(value: string): string {
    const workspaceFolder = "${workspaceFolder}";
    if (!value.includes(workspaceFolder)) {
        return value;
    }

    const numOpenFolders = vscode.workspace.workspaceFolders?.length ?? 0;
    if (numOpenFolders !== 1) {
        const logger: AugmentLogger = getLogger("AugmentConfigListener");
        logger.warn(
            `Variable ${workspaceFolder} cannot be expanded because there are ` +
                `${numOpenFolders} workspace folders open.`
        );
    }

    const workspaceFolderUri = vscode.workspace.workspaceFolders?.[0]?.uri;
    if (workspaceFolderUri) {
        value = value.replaceAll(workspaceFolder, workspaceFolderUri.fsPath);
    }

    return value;
}

#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_innie_base_setup():
    oci_pull(
        name = "remote-agents-innie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-innie-base",
        digest = "sha256:6fbb37a81135fd93c795dfdf5fbdf0f190cfc7b48e611686d0be91f176af7cae",
    )

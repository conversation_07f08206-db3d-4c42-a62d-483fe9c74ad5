#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_outie_base_setup():
    oci_pull(
        name = "remote-agents-outie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-outie-base",
        digest = "sha256:0954acb2d86115a252ef40b97b4f3a1b1efe70baa162097484624eacae88e81e",
    )

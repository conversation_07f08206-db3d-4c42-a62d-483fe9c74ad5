import { APITiming } from "../api/api-timing";
import {
    APIServer,
    BatchUploadResult,
    BlobMetadata,
    Blobs,
    ChatResult,
    CheckpointBlobsResult,
    ClientCompletionTimline,
    ClientMetric,
    CompletionLocation,
    CompletionResolution,
    CompletionResult,
    FileEditEvent,
    FindMissingResult,
    Language,
    MemorizeResult,
    Model,
    ModelConfig,
    RecencyInfo,
    ReplacementText,
    UploadBlob,
} from "../augment-api";
import { Exchange } from "../chat/chat-types";
import { UnknownModelError } from "../exceptions";
import { defaultFeatureFlags } from "../feature-flags";
import { makePromise } from "../utils/promise-utils";
import * as vscode from "../vscode";
import { BlobNameCalculator } from "../workspace/blob-name-calculator";
// XXX Original exports this type from watcher
import { mockGetModelsResult } from "./mock-modelinfo";

// OnMemorizeAction is a function that defines an action to be taken when a blob with
// a given path name is memorized. The return value indicates whether the action should
// be kept (if true) or removed (if false) for future memorize calls with the path.
type OnMemorizeAction = (pathName: string, memoryName: string) => boolean;

type CompletionRequestArgs = {
    requestId: string;
    prefix: string;
    suffix: string;
    pathName: string;
    blobName: string | undefined;
    completionLocation: CompletionLocation | undefined;
    language: string;
    blobs: Blobs;
    recencyInfo?: RecencyInfo;
    completementTimeoutMs?: number;
};

/**
 * MockApiServer is a class that implements a mock of the ApiServer class. It supports
 * the ability to view its set of memory names, to clear its set of memory names, and
 * to delay the `memorize` api.
 */
export class MockAPIServer implements APIServer {
    private nextRequestId = 123;
    private textEncoder = new TextEncoder();
    private _blobNameCalculator: BlobNameCalculator;
    static readonly cannedCompletion = "canned completion";
    static readonly sentinelCompletion = "sentinel completion";
    static readonly sentinelKeyword = `sentinel keyword`;

    // store the last completion request parameters for tests
    private _lastCompletionRequestArgs: CompletionRequestArgs | undefined;
    public get lastCompletionRequestArgs(): CompletionRequestArgs | undefined {
        return this._lastCompletionRequestArgs;
    }

    static readonly defaultCompletionResponseParams = {
        suggestedPrefixCharCount: 644,
        suggestedSuffixCharCount: 147,
    };

    // Map of memory name -> path name
    private memories = new Map<string, string>();

    // Map of path name -> memory name. If a path name is present in this map then the
    // corresponding memory name overrides the true memory name in calls to memorize()
    // for that path.
    private overrideMemoryNames = new Map<string, string>();

    // A promise that memorize requests will wait on before completing.
    private memorizeDelay: Promise<void> | undefined;

    // Map of path name -> action. The corresponding action is performed when there
    // is a memorization request for a given path name.
    private onMemorizeActions = new Map<string, OnMemorizeAction>();

    // A promise that findMissing requests will wait on before completing.
    private _findMissingDelay: Promise<void> | undefined;

    // An optional generator for responding to checkpoint requests
    private _checkpointResponse: Generator<CheckpointBlobsResult, void, unknown> | undefined;
    private _checkpointDelay: Promise<void> | undefined;

    // A list of blob names for findMissing to report as not indexed
    public readonly nonindexedBlobNames = new Set<string>();

    // A history of the batch sizes of findMissing requests.
    public findMissingHistory = new Array<number>();

    public expectedCompletion: string | undefined = undefined;
    public mockModifiedCode: string = "console.log('hello world')";

    public featureFlags = defaultFeatureFlags;

    constructor(
        private readonly _maxBlobSize?: number,
        private readonly _maxFindMissingBatchSize?: number,
        readonly sessionId: string = "placeholder-session-id",
        readonly completionResponseParams: {
            suggestedPrefixCharCount: number | undefined;
            suggestedSuffixCharCount: number | undefined;
        } = MockAPIServer.defaultCompletionResponseParams
    ) {
        this._blobNameCalculator = new BlobNameCalculator(_maxBlobSize || 1000);
    }

    reportClientCompletionTimelines(_timelines: ClientCompletionTimline[]): Promise<void> {
        return Promise.resolve();
    }

    public getSessionId(): string {
        return `${this.sessionId}`;
    }

    public createRequestId(): string {
        const requestId = `request.${this.nextRequestId}`;
        this.nextRequestId += 1;
        return requestId;
    }

    public memorize = jest.fn(
        async (
            pathName: string,
            text: string,
            _blobName: string,
            _metadata: BlobMetadata,
            _timeoutMs?: number
        ): Promise<MemorizeResult> => {
            if (this.memorizeDelay) {
                const localDelay = this.memorizeDelay;
                this.memorizeDelay = undefined;
                await localDelay;
            }
            const memoryName = this.calculateMemoryName(pathName, text);

            const action = this.onMemorizeActions.get(pathName);
            if (action) {
                const keepAction = action(pathName, memoryName);
                if (!keepAction) {
                    this.onMemorizeActions.delete(pathName);
                }
            }
            this.memories.set(memoryName, pathName);
            return { blobName: memoryName };
        }
    );

    public batchUpload = jest.fn(async (blobs: Array<UploadBlob>): Promise<BatchUploadResult> => {
        if (this.memorizeDelay) {
            const localDelay = this.memorizeDelay;
            this.memorizeDelay = undefined;
            await localDelay;
        }

        const blobNames = [];
        for (const blob of blobs) {
            const memoryName = this.calculateMemoryName(blob.pathName, blob.text);

            const action = this.onMemorizeActions.get(blob.pathName);
            if (action) {
                const keepAction = action(blob.pathName, memoryName);
                if (!keepAction) {
                    this.onMemorizeActions.delete(blob.pathName);
                }
            }
            this.memories.set(memoryName, blob.pathName);
            blobNames.push(memoryName);
        }
        return {
            blobNames,
        };
    });

    public findMissing = jest.fn(async (memoryNames: string[]): Promise<FindMissingResult> => {
        if (this._findMissingDelay) {
            await this._findMissingDelay;
        }
        return this._findMissing(memoryNames);
    });

    private _findMissing(memoryNames: string[]): FindMissingResult {
        if (this._maxFindMissingBatchSize !== undefined) {
            expect(memoryNames.length).toBeLessThanOrEqual(this._maxFindMissingBatchSize);
        }
        this.findMissingHistory.push(memoryNames.length);

        const unknownBlobNames = new Set<string>();
        memoryNames.forEach((memoryName: string): void => {
            if (!this.memories.has(memoryName)) {
                unknownBlobNames.add(memoryName);
            }
        });

        // Don't report any blob names as both unknown and nonindexed
        const nonindexedBlobNames = new Set<string>();
        for (const nonindexedBlobName of this.nonindexedBlobNames) {
            if (!unknownBlobNames.has(nonindexedBlobName)) {
                nonindexedBlobNames.add(nonindexedBlobName);
            }
        }

        return {
            unknownBlobNames: Array.from(unknownBlobNames),
            nonindexedBlobNames: Array.from(nonindexedBlobNames),
        };
    }

    public complete = jest.fn(
        async (
            requestId: string,
            prefix: string,
            suffix: string,
            path: string,
            blobName: string | undefined,
            completionLocation: CompletionLocation | undefined,
            language: string,
            blobs: Blobs,
            _recenctChanges: ReplacementText[],
            _fileEditEvents?: FileEditEvent[],
            _completionTimeoutMs?: number,
            _probeOnly?: boolean,
            apiTiming?: APITiming
        ): Promise<CompletionResult> => {
            if (apiTiming) {
                apiTiming.rpcStart = Date.now();
            }
            this._lastCompletionRequestArgs = {
                requestId,
                prefix,
                suffix,
                pathName: path,
                blobName,
                completionLocation,
                language,
                blobs,
            };
            const findMissingResult = this._findMissing(blobs.addedBlobs);
            let completion = this.expectedCompletion;
            if (!completion) {
                completion = suffix.startsWith(MockAPIServer.sentinelKeyword)
                    ? MockAPIServer.sentinelCompletion
                    : MockAPIServer.cannedCompletion;
            }
            if (apiTiming) {
                apiTiming.rpcEnd = Date.now();
            }
            return {
                completionItems: [
                    {
                        text: completion,
                        suffixReplacementText: "",
                        skippedSuffix: "",
                    },
                ],
                unknownBlobNames: findMissingResult.unknownBlobNames,
                checkpointNotFound: false,
                ...this.completionResponseParams,
            };
        }
    );

    public checkpointBlobs(_blobs: Blobs): Promise<CheckpointBlobsResult> {
        let response = this._checkpointResponse?.next().value;
        if (!response) {
            // If there is no generated response, use a default
            response = { newCheckpointId: "0x1234" };
        }
        if (!response.newCheckpointId) {
            // Special case: if the generator produced an empty version, treat
            // that as an error from the backend.
            throw new Error(`HTTP error`);
        }
        return Promise.resolve(response);
    }

    public chat(
        _requestId: string,
        _message: string,
        _chatHistory: Exchange[],
        _blobs: Blobs,
        _userGuidedBlobs: string[],
        _externalSourceIds: string[],
        _model: string | undefined,
        // _vcsChange: VCSChange,
        _recentChanges: ReplacementText[],
        _selectedCode?: string,
        _prefix?: string,
        _suffix?: string,
        _pathName?: string,
        _language?: string
    ): Promise<ChatResult> {
        return Promise.resolve({
            text: "hello world",
        });
    }

    public chatStream(
        _requestId: string,
        _message: string,
        _chatHistory: Exchange[],
        _blobs: Blobs,
        _userGuidedBlobs: string[],
        _externalSourceIds: string[],
        _model: string | undefined,
        // _vcsChange: VCSChange,
        _recentChanges: ReplacementText[],
        _selectedCode?: string,
        _prefix?: string,
        _suffix?: string,
        _pathName?: string,
        _fileContent?: string,
        _language?: string
    ): Promise<AsyncIterable<ChatResult>> {
        async function* helloGenerator(): AsyncGenerator<ChatResult, void, undefined> {
            yield { text: "hello" };
            yield { text: " world" };
        }
        return Promise.resolve(helloGenerator());
    }

    public resolveCompletions(_resolutions: CompletionResolution[]): Promise<void> {
        return Promise.resolve();
    }

    public static readonly modelName = "default-model";
    public static readonly suggestedPrefixCharCount = 100;
    public static readonly suggestedSuffixCharCount = 100;
    public static readonly maxMemorizeSizeBytes = 100;

    public async getModelConfig(): Promise<ModelConfig> {
        const modelInfo: Model[] = [];
        for (const backModelInfo of mockGetModelsResult.models) {
            modelInfo.push({
                name: backModelInfo.name,
                suggestedPrefixCharCount: backModelInfo.suggested_prefix_char_count,
                suggestedSuffixCharCount: backModelInfo.suggested_suffix_char_count,
            });
        }

        const languages: Language[] = [];
        for (const languageInfo of mockGetModelsResult.languages) {
            languages.push({
                name: languageInfo.name,
                vscodeName: languageInfo.vscode_name,
                extensions: languageInfo.extensions,
            });
        }

        return {
            defaultModel: mockGetModelsResult.default_model,
            models: modelInfo,
            languages,
            featureFlags: this.featureFlags,
        };
    }

    public getModelInfo = jest.fn(async (modelName: string): Promise<Model> => {
        const models = (await this.getModelConfig()).models;
        const modelInfo = models.find((model) => model.name === modelName);
        if (modelInfo) {
            return modelInfo;
        }
        throw new UnknownModelError(modelName);
    });

    /**
     * The methods below are not part of APIServer.
     */

    // verifyContext verifies that the contents of the given context match our set of
    // memory names.
    public verifyContext(context: string[]) {
        expect(context.length).toBe(this.memories.size);
        for (const memoryName of context) {
            expect(this.memories.keys()).toContain(memoryName);
        }
    }

    // `forget` clears the set of uploaded memory names
    public forget(): void {
        this.memories.clear();
    }

    // calculateMemoryName calculates the memory name for the given pathName and
    // memory contents. If an override has been registered for the pathName, return
    // the override instead of the real name.
    private calculateMemoryName(pathName: string, text: string): string {
        if (this._maxBlobSize !== undefined && text.length > this._maxBlobSize) {
            throw new Error(
                `pathName ${pathName}, size ${text.length} ` +
                    `exceeds maxBlobSize of ${this._maxBlobSize}`
            );
        }
        const overrideName = this.overrideMemoryNames.get(pathName);
        if (overrideName) {
            return overrideName;
        }
        return this._blobNameCalculator.calculate(pathName, this.textEncoder.encode(text))!;
    }

    // overrideMemoryName registers an override for the memory name of the given
    // pathName. Or, if memoryName is undefined, it removes the override for the
    // path if there is one.
    public overrideMemoryName(pathName: string, memoryName?: string): void {
        if (memoryName) {
            this.overrideMemoryNames.set(pathName, memoryName);
        } else {
            this.overrideMemoryNames.delete(pathName);
        }
    }

    // delayMemorize causes memorize operations to wait until the returned disposable
    // is disposed. If `error` is defined, any delayed memorize operations will fail with
    // the given error.
    public delayMemorize(error?: Error): vscode.Disposable {
        const [promise, resolve, reject] = makePromise();
        const resume = new vscode.Disposable(() => {
            this.memorizeDelay = undefined;
            if (error) {
                reject(error);
            } else {
                resolve();
            }
        });
        this.memorizeDelay = promise;
        return resume;
    }

    // delayFindUnknown causes findMissing operations to wait until the returned disposable
    // is disposed.
    public delayFindMissing(): vscode.Disposable {
        const [promise, resolve, _] = makePromise();
        const resume = new vscode.Disposable(() => {
            this._findMissingDelay = undefined;
            resolve();
        });
        this._findMissingDelay = promise;
        return resume;
    }

    public delayCheckpoint(): vscode.Disposable {
        const [promise, resolve, _] = makePromise();
        const resume = new vscode.Disposable(() => {
            this._checkpointDelay = undefined;
            resolve();
        });
        this._checkpointDelay = promise;
        return resume;
    }

    // onMemorize registers an action to be taken when the given pathName is memorized.
    // Or, if `action` is undefined, removes the action for pathName if there is one.
    // If memorization is delayed via delayMemorize, the action run after the delay has
    // been lifted.
    public onMemorize(pathName: string, action?: OnMemorizeAction) {
        if (action) {
            this.onMemorizeActions.set(pathName, action);
        } else {
            this.onMemorizeActions.delete(pathName);
        }
    }

    // getMemoryNames returns all known memory names.
    public getMemoryNames(): string[] {
        return Array.from(this.memories.keys());
    }

    // getMemoryNamesForPath returns all known memory names for the given path name.
    public getMemoryNamesForPath(pathName: string): string[] {
        const memoryNames: string[] = [];
        for (const [memoryName, memoryPathName] of this.memories) {
            if (memoryPathName === pathName) {
                memoryNames.push(memoryName);
            }
        }
        return memoryNames;
    }

    public getBlobName(pathName: string): string | undefined {
        const blobNames = this.getMemoryNamesForPath(pathName);
        expect(blobNames.length).toBeLessThanOrEqual(1);
        return blobNames.at(0);
    }

    set checkpointResponse(
        response: Generator<CheckpointBlobsResult, void, undefined> | undefined
    ) {
        this._checkpointResponse = response;
    }
    get checkpointResponse(): Generator<CheckpointBlobsResult, void, undefined> | undefined {
        return this._checkpointResponse;
    }

    public async getAccessToken(
        _authURI: string,
        _tenantURL: string,
        _codeVerifier: string,
        _code: string
    ): Promise<string> {
        return "fake-access-token";
    }

    public async clientMetrics(_metrics: Array<ClientMetric>): Promise<void> {
        return Promise.resolve();
    }
}

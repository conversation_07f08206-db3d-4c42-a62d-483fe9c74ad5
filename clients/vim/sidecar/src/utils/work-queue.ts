import * as vscode from "../vscode";

export interface Kickable {
    kick(): void;
}

/**
 * Work Queue Subsystem
 *
 * This subsystem provides a set of classes for managing asynchronous work queues.
 * It's designed to handle various types of queues with different processing requirements.
 *
 * Key Components:
 *
 * 1. WorkQueueBase: Abstract base class for work queues
 * 2. ItemWorkQueue: Processes individual items sequentially
 * 3. KVWorkQueue: Processes key-value pairs
 * 4. OrderedWorkQueue: Processes items in the order they were inserted
 * 5. PeriodicKicker: Periodically triggers processing of a work queue
 *
 * Usage:
 *
 * 1. Choose the appropriate queue type based on your needs.
 * 2. Implement a processing function that handles individual items or key-value pairs.
 * 3. Create an instance of the chosen queue with the processing function.
 * 4. Insert items into the queue using the `insert` method.
 * 5. Call `kick()` to start processing items in the queue.
 *
 * Example:
 *
 * ```typescript
 * const processItem = async (item: string | undefined) => {
 *     if (item) {
 *         console.log(`Processing: ${item}`);
 *     }
 * };
 *
 * const queue = new ItemWorkQueue<string>(processItem);
 * queue.insert("Task 1");
 * queue.insert("Task 2");
 * await queue.kick();
 * ```
 *
 * Diagram:
 *
 * ```
 * Kickable
 * └── WorkQueueBase
 *     ├── ItemWorkQueue
 *     └── KVWorkQueue
 *
 * OrderedWorkQueue
 *
 * PeriodicKicker
 *     └── Kickable (uses)
 * ```
 */

abstract class WorkQueueBase<K, V> implements Kickable, vscode.Disposable {
    protected _items = new Map<K, V>();
    private _inProgress = false;
    private _stopping = false;

    protected abstract _processEntry(entry: [K, V] | undefined): Promise<void>;

    public dispose() {
        this._stopping = true;
    }

    public get size(): number {
        return this._items.size;
    }

    /**
     * Cancels the processing of an item with the given key.
     *
     * @param key The key of the item to cancel.
     */
    public cancel(key: K): void {
        this._items.delete(key);
    }

    // _insert inserts an item into the queue. If `replace` is false then the new item will be
    // added only if its key is not already present in the queue.
    //
    // The return value indicates whether an item with the same key was already present in the
    // queue. Hence:
    // * if `replace` is false then the return value indicates whether the new item was accepted
    // * if `replace` is true then the new item is always accepted, and the return value indicates
    //   whether it replaced an existing item
    protected _insert(key: K, value: V, replace = false): boolean {
        let present = this._items.has(key);
        if (!replace && present) {
            return false;
        }
        this._items.set(key, value);
        return !present;
    }

    public async kick() {
        if (this._inProgress || this._stopping || this._items.size === 0) {
            return;
        }
        this._inProgress = true;

        while (this._items.size > 0 && !this._stopping) {
            const activeItems = this._items;
            this._items = new Map<K, V>();
            for (const item of activeItems) {
                try {
                    await this._processEntry(item);
                } catch {}
                if (this._stopping) {
                    break;
                }
            }
        }

        this._inProgress = false;

        // Signal that the queue is empty after cleaning `_inProgress`. If we don't, and the call
        // to _processEntry calls `kick`, the kick will see that `this._inProgress` is true and
        // won't do anything. We won't do anything either, so the kick will be lost.
        await this._processEntry(undefined);
    }
}

/**
 * ItemWorkQueue is a specialized work queue for processing individual items of type T.
 * It extends WorkQueueBase, inheriting basic queue functionality and adding item-specific operations.
 *
 * This queue processes items one at a time, making it suitable for tasks that require
 * sequential processing or have potential side effects that shouldn't overlap.
 */
export class ItemWorkQueue<T> extends WorkQueueBase<T, void> {
    /**
     * Creates a new ItemWorkQueue.
     *
     * @param _processItem A function that processes a single item of type T.
     *                     It should return a Promise that resolves when processing is complete.
     *                     The function can also accept undefined, which signals that the queue is empty.
     */
    constructor(private _processItem: (item: T | undefined) => Promise<void>) {
        super();
    }

    /**
     * Inserts a new item into the queue.
     *
     * @param item The item to be inserted.
     * @param replace If true, replaces an existing item with the same key. Default is false.
     * @returns true if the item was inserted (or replaced an existing item), false otherwise.
     *
     * Note: In this implementation, the item itself serves as the key.
     */
    public insert(item: T, replace: boolean = false): boolean {
        return this._insert(item, undefined, replace);
    }

    /**
     * Checks if an item is already in the queue.
     *
     * @param item The item to check for.
     * @returns true if the item is in the queue, false otherwise.
     */
    public has(item: T): boolean {
        return this._items.has(item);
    }

    /**
     * Internal method to process a single entry from the queue.
     * This method is called by the WorkQueueBase's kick() method.
     *
     * @param entry The entry to process, or undefined if the queue is empty.
     */
    protected async _processEntry(entry: [T, void] | undefined): Promise<void> {
        if (entry === undefined) {
            // Signal that the queue is empty
            void this._processItem(undefined);
        } else {
            // Process the item
            await this._processItem(entry[0]);
        }
    }
}

/**
 * KVWorkQueue is a specialized work queue for processing key-value pairs.
 * It extends WorkQueueBase, inheriting basic queue functionality and adding key-value specific operations.
 *
 * This queue processes key-value pairs, making it suitable for tasks that require
 * associating values with unique keys and processing them sequentially.
 */
export class KVWorkQueue<K, V> extends WorkQueueBase<K, V> {
    /**
     * Creates a new KVWorkQueue.
     *
     * @param _processItem A function that processes a single key-value pair or undefined.
     *                     It should return a Promise that resolves when processing is complete.
     *                     The function can also accept undefined, which signals that the queue is empty.
     */
    constructor(private _processItem: (item: [K, V] | undefined) => Promise<void>) {
        super();
    }

    /**
     * Inserts a new key-value pair into the queue.
     *
     * @param key The key of the item to be inserted.
     * @param value The value associated with the key.
     * @param replace If true, replaces an existing item with the same key. Default is false.
     * @returns true if the item was inserted (or replaced an existing item), false otherwise.
     */
    public insert(key: K, value: V, replace = false): boolean {
        return this._insert(key, value, replace);
    }

    /**
     * Retrieves the value associated with the given key.
     *
     * @param key The key to look up.
     * @returns The value associated with the key, or undefined if the key is not in the queue.
     */
    public get(key: K): V | undefined {
        return this._items.get(key);
    }

    /**
     * Internal method to process a single entry from the queue.
     * This method is called by the WorkQueueBase's kick() method.
     *
     * @param entry The entry to process, or undefined if the queue is empty.
     */
    protected async _processEntry(entry: [K, V] | undefined): Promise<void> {
        return this._processItem(entry);
    }
}

/**
 * OrderedWorkQueue is a specialized work queue that processes items of type T in the order they were inserted.
 * It implements both Kickable and vscode.Disposable interfaces.
 */
export class OrderedWorkQueue<T> implements Kickable, vscode.Disposable {
    // Used for processing items of type T in the order they were inserted
    private _keys = new Set<T>();
    private _items = new Array<T>();
    private _inProgress = false;
    private _stopping = false;

    /**
     * Creates a new OrderedWorkQueue.
     * @param _processItem A function that processes a single item of type T or undefined.
     *                     It should return a Promise that resolves when processing is complete.
     *                     The function can also accept undefined, which signals that the queue is empty.
     */
    constructor(private readonly _processItem: (item: T | undefined) => Promise<void>) {}

    /**
     * Gets the number of items currently in the queue.
     * @returns The number of items in the queue.
     */
    public get size(): number {
        return this._items.length;
    }

    /**
     * Disposes of the queue by setting the stopping flag to true.
     */
    public dispose() {
        this._stopping = true;
    }

    /**
     * Inserts a new item into the queue if it doesn't already exist.
     * @param item The item to be inserted.
     * @returns true if the item was inserted, false if it already existed in the queue.
     */
    public insert(item: T): boolean {
        if (this._keys.has(item)) {
            return false;
        }
        this._keys.add(item);
        this._items.push(item);
        return true;
    }

    /**
     * Starts processing items in the queue if not already in progress.
     * Continues processing until the queue is empty or the queue is being stopped.
     */
    public async kick() {
        if (this._inProgress || this._stopping) {
            return;
        }
        this._inProgress = true;

        while (this._items.length > 0 && !this._stopping) {
            const activeItems = this._items;
            this._keys.clear();
            this._items = new Array<T>();
            for (const item of activeItems) {
                try {
                    await this._processItem(item);
                } catch {}
                if (this._stopping) {
                    break;
                }
            }
        }
        this._inProgress = false;

        // Signal that the queue is empty after cleaning `_inProgress`. If we don't, and the call
        // to _processEntry calls `kick`, the kick will see that `this._inProgress` is true and
        // won't do anything. We won't do anything either, so the kick will be lost.
        await this._processItem(undefined);
    }
}

/**
 * PeriodicKicker is a class that periodically triggers a 'kick' action on a Kickable object.
 * It implements the vscode.Disposable interface for proper resource management.
 */
export class PeriodicKicker implements vscode.Disposable {
    // Note(rich): Timeout seems to be the correct type for
    // current Node.js
    private _intervalId: NodeJS.Timeout | undefined;

    /**
     * Creates a new PeriodicKicker.
     * @param _toKick The Kickable object to be kicked periodically.
     * @param intervalMs The interval in milliseconds between kicks.
     */
    constructor(
        private readonly _toKick: Kickable,
        intervalMs: number
    ) {
        this._intervalId = setInterval(this._kick.bind(this), intervalMs);
    }

    /**
     * Private method that performs the kick action.
     */
    private _kick(): void {
        this._toKick.kick();
    }

    /**
     * Disposes of the PeriodicKicker by clearing the interval.
     */
    public dispose(): void {
        if (this._intervalId !== undefined) {
            clearInterval(this._intervalId);
        }
    }
}

/**
 * LIFOWorkQueue is a specialized work queue that processes items in Last-In-First-Out order.
 * It implements both Kickable and vscode.Disposable interfaces, following the pattern
 * of other work queues in the system.
 */
interface LIFOWorkQueueItem<TItemArg, TProcessItemReturn> {
    itemArg: TItemArg;
    resolve: (processItemReturn: TProcessItemReturn) => void;
    reject: (e?: any) => void;
}

export class LIFOWorkQueue<TItemArg, TProcessItemReturn> implements Kickable, vscode.Disposable {
    private _items: LIFOWorkQueueItem<TItemArg, TProcessItemReturn>[] = [];
    private _inProgress = false;
    private _stopping = false;

    /**
     * Creates a new LIFOWorkQueue.
     * @param _processItem A function that processes a single item of type T or undefined.
     *                     It should return a Promise that resolves when processing is complete.
     *                     The function can also accept undefined, which signals that the queue is empty.
     */
    constructor(
        private readonly _processItem: {
            (itemArg: TItemArg): Promise<TProcessItemReturn>;
            (itemArg: undefined): Promise<void>;
        }
    ) {}

    /**
     * Gets the number of items currently in the queue.
     */
    public get size(): number {
        return this._items.length;
    }

    /**
     * Disposes of the queue by setting the stopping flag to true.
     */
    public dispose() {
        this._stopping = true;
    }

    /**
     * Inserts a new item into the queue.
     * @param item The item to be inserted.
     */
    public insert(itemArg: TItemArg): Promise<TProcessItemReturn> {
        return new Promise((resolve, reject) => {
            this._items.push({ itemArg, resolve, reject });
        });
    }

    /**
     * Inserts a new item into the queue and immediately starts processing it.
     * @param item The item to be inserted and processed.
     * @returns A promise that resolves when the item is processed.
     */
    public insertAndKick(itemArg: TItemArg): Promise<TProcessItemReturn> {
        const returnPromise = this.insert(itemArg);
        void this.kick();
        return returnPromise;
    }

    /**
     * Starts processing items in the queue if not already in progress.
     * Processes items in LIFO order (most recent first) until the queue is empty
     * or the queue is being stopped.
     */
    public async kick(): Promise<void> {
        if (this._inProgress || this._stopping) {
            return;
        }
        this._inProgress = true;

        while (this._items.length > 0 && !this._stopping) {
            // Take the most recent item (LIFO)
            const item = this._items.pop()!;
            try {
                const result = await this._processItem(item.itemArg);
                item.resolve(result);
            } catch (e) {
                item.reject(e);
            }
        }

        this._inProgress = false;

        // Signal that the queue is empty after cleaning `_inProgress`. If we don't, and the call
        // to _processItem calls `kick`, the kick will see that `this._inProgress` is true and
        // won't do anything. We won't do anything either, so the kick will be lost.
        await this._processItem(undefined);
    }
}

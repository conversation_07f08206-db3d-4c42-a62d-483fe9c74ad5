package com.augmentcode.featurevector

import java.nio.charset.StandardCharsets
import java.security.MessageDigest

/**
 * Features class that exactly mirrors the TypeScript Features class.
 * Contains all the same properties and methods with identical logic.
 */
data class Features(
    val vscode: String,
    val machineId: String,
    val os: String,
    val cpu: String,
    val memory: String,
    val numCpus: String,
    val hostname: String,
    val arch: String,
    val username: String,
    val macAddresses: List<String>,
    val osRelease: String,
    val kernelVersion: String,
    val telemetryDevDeviceId: String,
    val requestId: String,
    val randomHash: String,
    val osMachineId: String,
    val homeDirectoryIno: String,
    val projectRootIno: String,
    val gitUserEmail: String,
    val sshPublicKey: String,
    val userDataPathIno: String,
    val userDataMachineId: String,
    val storageUriPath: String,
    val gpuInfo: String,
    val timezone: String,
    val diskLayout: String,
    val systemInfo: String,
    val biosInfo: String,
    val baseboardInfo: String,
    val chassisInfo: String,
    val baseboardAssetTag: String,
    val chassisAssetTag: String,
    val cpuFlags: String,
    val memoryModuleSerials: String,
    val usbDeviceIds: String,
    val audioDeviceIds: String,
    val hypervisorType: String,
    val systemBootTime: Long,
    val sshKnownHosts: String,
) {
    /**
     * SHA-256 hash function that exactly mirrors the TypeScript sha256 function.
     */
    private fun sha256(input: ByteArray): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(input)
        return hashBytes.joinToString("") { "%02x".format(it) }
    }

    /**
     * Calculate checksum for the feature vector.
     * Exactly mirrors the TypeScript calculateChecksum method.
     */
    fun calculateChecksum(vector: FeatureVector): String {
        val sortedKeys = vector.keys.sorted()
        val values = sortedKeys.map { vector[it] ?: "" }
        val combined = values.joinToString("")
        val hash = sha256(combined.toByteArray(StandardCharsets.UTF_8))
        return "v1#$hash"
    }

    /**
     * Canonicalize a string by converting to lowercase, trimming, and hashing.
     * Exactly mirrors the TypeScript canonicalize method.
     */
    fun canonicalize(s: String): String {
        return sha256(s.lowercase().trim().toByteArray(StandardCharsets.UTF_8))
    }

    /**
     * Canonicalize an array of strings.
     * Exactly mirrors the TypeScript canonicalizeArray method.
     */
    fun canonicalizeArray(array: List<String>): String {
        return canonicalize(
            array.map { it.lowercase().trim() }.joinToString(","),
        )
    }

    /**
     * Convert features to a feature vector.
     * Exactly mirrors the TypeScript toVector method.
     */
    fun toVector(): FeatureVector {
        val result = mutableMapOf<Int, String>()

        result[FeatureVectorKey.VSCODE.value] = canonicalize(vscode)
        result[FeatureVectorKey.MACHINE_ID.value] = canonicalize(machineId)
        result[FeatureVectorKey.OS.value] = canonicalize(os)
        result[FeatureVectorKey.CPU.value] = canonicalize(cpu)
        result[FeatureVectorKey.MEMORY.value] = canonicalize(memory)
        result[FeatureVectorKey.NUM_CPUS.value] = canonicalize(numCpus)
        result[FeatureVectorKey.HOSTNAME.value] = canonicalize(hostname)
        result[FeatureVectorKey.ARCH.value] = canonicalize(arch)
        result[FeatureVectorKey.USERNAME.value] = canonicalize(username)
        result[FeatureVectorKey.MAC_ADDRESSES.value] = canonicalizeArray(macAddresses)
        result[FeatureVectorKey.OS_RELEASE.value] = canonicalize(osRelease)
        result[FeatureVectorKey.KERNEL_VERSION.value] = canonicalize(kernelVersion)
        result[FeatureVectorKey.TELEMETRY_DEV_DEVICE_ID.value] = canonicalize(telemetryDevDeviceId)
        result[FeatureVectorKey.REQUEST_ID.value] = canonicalize(requestId)
        result[FeatureVectorKey.RANDOM_HASH.value] = canonicalize(randomHash)
        result[FeatureVectorKey.OS_MACHINE_ID.value] = canonicalize(osMachineId)
        result[FeatureVectorKey.HOME_DIRECTORY_INO.value] = canonicalize(homeDirectoryIno)
        result[FeatureVectorKey.PROJECT_ROOT_INO.value] = canonicalize(projectRootIno)
        result[FeatureVectorKey.GIT_USER_EMAIL.value] = canonicalize(gitUserEmail)
        result[FeatureVectorKey.SSH_PUBLIC_KEY.value] = canonicalize(sshPublicKey)
        result[FeatureVectorKey.USER_DATA_PATH_INO.value] = canonicalize(userDataPathIno)
        result[FeatureVectorKey.USER_DATA_MACHINE_ID.value] = canonicalize(userDataMachineId)
        result[FeatureVectorKey.STORAGE_URI_PATH.value] = canonicalize(storageUriPath)
        result[FeatureVectorKey.GPU_INFO.value] = canonicalize(gpuInfo)
        result[FeatureVectorKey.TIMEZONE.value] = canonicalize(timezone)
        result[FeatureVectorKey.DISK_LAYOUT.value] = canonicalize(diskLayout)
        result[FeatureVectorKey.SYSTEM_INFO.value] = canonicalize(systemInfo)
        result[FeatureVectorKey.BIOS_INFO.value] = canonicalize(biosInfo)
        result[FeatureVectorKey.BASEBOARD_INFO.value] = canonicalize(baseboardInfo)
        result[FeatureVectorKey.CHASSIS_INFO.value] = canonicalize(chassisInfo)
        result[FeatureVectorKey.BASEBOARD_ASSET_TAG.value] = canonicalize(baseboardAssetTag)
        result[FeatureVectorKey.CHASSIS_ASSET_TAG.value] = canonicalize(chassisAssetTag)
        result[FeatureVectorKey.CPU_FLAGS.value] = canonicalize(cpuFlags)
        result[FeatureVectorKey.MEMORY_MODULE_SERIALS.value] = canonicalize(memoryModuleSerials)
        result[FeatureVectorKey.USB_DEVICE_IDS.value] = canonicalize(usbDeviceIds)
        result[FeatureVectorKey.AUDIO_DEVICE_IDS.value] = canonicalize(audioDeviceIds)
        result[FeatureVectorKey.HYPERVISOR_TYPE.value] = canonicalize(hypervisorType)
        result[FeatureVectorKey.SYSTEM_BOOT_TIME.value] = canonicalize(systemBootTime.toString())
        result[FeatureVectorKey.SSH_KNOWN_HOSTS.value] = canonicalize(sshKnownHosts)

        // Calculate and add checksum
        result[FeatureVectorKey.CHECKSUM.value] = calculateChecksum(result)

        return result
    }
}

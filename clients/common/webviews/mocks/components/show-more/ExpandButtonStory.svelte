<script lang="ts">
  import ExpandButton from "$common-webviews/src/common/components/ExpandButton.svelte";
  import Fieldset from "$common-webviews/mocks/storybook/Fieldset.svelte";

  export let expanded: boolean = false;
  export let backgroundColor: string = "var(--user-theme-panel-background)";

  function handleClick() {
    expanded = !expanded;
  }
</script>

<div>
  <Fieldset title="Basic Usage">
    <div class="demo-container">
      <p class="description">
        The ExpandButton is a shared component used by both ShowMore and CodeblockContainer for
        consistent expand/collapse button styling.
      </p>

      <div class="button-container">
        <ExpandButton {expanded} {backgroundColor} onClick={handleClick} />
      </div>

      <div class="info">
        <p>Button state: <strong>{expanded ? "Expanded" : "Collapsed"}</strong></p>
      </div>
    </div>
  </Fieldset>
</div>

<style>
  .demo-container {
    max-width: 600px;
  }

  .description {
    margin-bottom: var(--ds-spacing-3);
    color: var(--ds-color-neutral-11);
    font-size: var(--ds-font-size-2);
  }

  .button-container {
    position: relative;
    height: 60px;
    background: var(--user-theme-sidebar-background);
    border: 1px solid var(--ds-color-neutral-6);
    border-radius: var(--ds-radius-2);
    margin-bottom: var(--ds-spacing-3);
  }

  .info {
    padding: var(--ds-spacing-2);
    background: var(--ds-color-neutral-2);
    border-radius: var(--ds-radius-1);
    font-size: var(--ds-font-size-1);
  }

  .info p {
    margin: 0;
    margin-bottom: var(--ds-spacing-1);
  }

  .info p:last-child {
    margin-bottom: 0;
  }
</style>

/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import component from "./ExpandButtonStory.svelte";

const meta = {
  title: "components/ExpandButton",
  component,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "A shared button component used by ShowMore and CodeblockContainer for consistent expand/collapse styling. Consolidates button styling while allowing customization for different use cases.",
      },
    },
  },
  argTypes: {
    expanded: {
      control: { type: "boolean" },
      description: "Whether the button is in expanded state",
      defaultValue: false,
    },
    backgroundColor: {
      control: { type: "color" },
      description: "Background color CSS value",
      defaultValue: "var(--user-theme-panel-background)",
    },
  },
  render(props) {
    return {
      Component: component,
      props,
    };
  },
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {};

export const DefaultCollapseText: Story = {
  args: {
    backgroundColor: "var(--user-theme-panel-background)",
  } as any,
};

export const CustomCollapseText: Story = {
  args: {
    collapseText: "Show less",
    backgroundColor: "var(--user-theme-panel-background)",
  } as any,
};

export const CustomBackground: Story = {
  args: {
    backgroundColor: "#ff6b6b",
    collapseText: "Hide content",
  } as any,
};

export const Expanded: Story = {
  args: {
    expanded: true,
    collapseText: "Collapse",
    backgroundColor: "var(--user-theme-panel-background)",
  } as any,
};

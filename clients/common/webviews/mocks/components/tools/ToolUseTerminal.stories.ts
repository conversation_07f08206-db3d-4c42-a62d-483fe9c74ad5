/* eslint-disable @typescript-eslint/naming-convention */
import type { <PERSON><PERSON>, StoryObj } from "@storybook/svelte";
import ToolUse from "$common-webviews/src/apps/chat/components/conversation/blocks/tools/ToolUse.svelte";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import mockSetupScript from "./mock-setup-script.json";
import mockScriptOutput from "./mock-script-output.json";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import { LONG_JS_FILE_CONTENT } from "./long_file_content";

// Long terminal command content for testing
const LONG_TERMINAL_COMMAND = `$ #!/bin/bash

# Long Bash Script Demonstration
echo "🚀 Starting Long Bash Script Demonstration..."
echo "================================================"

# Function definitions
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

create_test_files() {
    local dir=$1
    log_message "Creating test files in $dir"
    mkdir -p "$dir"
    for i in {1..5}; do
        echo "This is test file $i with some content" > "$dir/test_file_$i.txt"
        echo "Created on: $(date)" >> "$dir/test_file_$i.txt"
        echo "Random number: $RANDOM" >> "$dir/test_file_$i.txt"
    done
}

process_files() {
    local dir=$1
    log_message "Processing files in $dir"

    for file in "$dir"/*.txt; do
        if [[ -f "$file" ]]; then
            local word_count=$(wc -w < "$file")
            local line_count=$(wc -l < "$file")
            log_message "File $(basename "$file"): $line_count lines, $word_count words"
            sleep 0.5
        fi
    done
}

perform_calculations() {
    log_message "Performing mathematical calculations..."

    local sum=0
    for i in {1..50}; do
        sum=$((sum + i))
        if (( i % 10 == 0 )); then
            log_message "Progress: Calculated sum up to $i = $sum"
        fi
        sleep 0.1
    done

    log_message "Final sum of 1-50: $sum"
}`;

const meta = {
  title: "components/ToolUse/Terminal",
} satisfies Meta<typeof ToolUse>;

export default meta;

type Story = StoryObj<typeof ToolUse>;

export const SaveFile = {
  name: "ToolUse Save File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const SaveFileCancelled = {
  name: "Save file Cancelled",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: LocalToolType.saveFile,
      input_json: JSON.stringify({
        file_path: "/path/to/example-component.jsx",
        file_content: LONG_JS_FILE_CONTENT,
      }),
    },
    toolUseState: { phase: 8 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const SearchFile = {
  name: "ToolUse Search File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/example-component.jsx",
        type: "file",
        search_query_regex: "function.*Hello",
        case_sensitive: false,
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const ViewDirectory = {
  name: "ToolUse View Directory",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "directory-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/project/src",
        type: "directory",
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const SearchInFile = {
  name: "ToolUse Search in File with Regex",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "search-regex-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/example-component.jsx",
        type: "file",
        search_query_regex: "export.*Component",
        case_sensitive: true,
        context_lines_before: 3,
        context_lines_after: 3,
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const ViewFile = {
  name: "ToolUse View File",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "js-file-example",
      tool_name: SidecarToolType.view,
      input_json: JSON.stringify({
        path: "/path/to/example-component.jsx",
        type: "file",
      }),
    },
    toolUseState: { phase: 5 },
  } as any,
} satisfies Story;

export const ToolUseCli = {
  name: "ToolUse Shell CLI",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 3 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli1 = {
  name: "ToolUse Shell CLI - Loading - Phase 1",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 1 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli4 = {
  name: "ToolUse Shell CLI - Running - Phase 4",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 4 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli5 = {
  name: "ToolUse Shell CLI - Success - Phase 5",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCli6 = {
  name: "ToolUse Shell CLI - Error - Phase 6",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "2",
      tool_name: SidecarToolType.shell,
      input_json: '{"command": "ls"}',
    },
    toolUseState: { phase: 6 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCliLongCommand = {
  name: "ToolUse Shell CLI - Long Command",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "long-command",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command_type: "complex",
        complex_command: LONG_TERMINAL_COMMAND,
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCliLongArgs = {
  name: "ToolUse Shell CLI - Long Args",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "long-args-command",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command_type: "simple",
        simple_command: [
          "docker",
          "run",
          "--rm",
          "-it",
          "--volume",
          "/Users/<USER>/projects/my-awesome-project:/workspace",
          "--volume",
          "/Users/<USER>/.ssh:/root/.ssh:ro",
          "--volume",
          "/Users/<USER>/.gitconfig:/root/.gitconfig:ro",
          "--workdir",
          "/workspace",
          "--env",
          "NODE_ENV=development",
          "--env",
          "REDIS_URL=redis://localhost:6379/0",
          "--publish",
          "3000:3000",
          "--publish",
          "5432:5432",
          "--name",
          "my-development-container",
          "node:18-alpine",
          "sh",
          "-c",
          "npm install && npm run dev -- --host 0.0.0.0 --port 3000",
        ],
      }),
    },
    toolUseState: { phase: 5 },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseCliLongOutput = {
  name: "ToolUse Shell CLI - Long Command & Output",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: {
      tool_use_id: "long-output-command",
      tool_name: SidecarToolType.shell,
      input_json: JSON.stringify({
        command_type: "complex",
        complex_command: LONG_TERMINAL_COMMAND,
      }),
    },
    toolUseState: {
      phase: 5,
      result: {
        text: `Found 47 library files:
/usr/local/lib/libssl.3.dylib
/usr/local/lib/libcrypto.3.dylib
/usr/local/lib/libz.1.dylib
/usr/local/lib/libpng16.16.dylib
/usr/local/lib/libjpeg.8.dylib
/usr/local/lib/libfreetype.6.dylib
/usr/local/lib/libfontconfig.1.dylib
/usr/local/lib/libexpat.1.dylib
/usr/local/lib/libbrotlidec.1.dylib
/usr/local/lib/libbrotlienc.1.dylib
/usr/local/lib/libbrotlicommon.1.dylib
/usr/local/lib/libpcre2-8.0.dylib
/usr/local/lib/libgit2.1.7.dylib
/usr/local/lib/libssh2.1.dylib
/usr/local/lib/libcurl.4.dylib
/usr/local/lib/libnghttp2.14.dylib
/usr/local/lib/librtmp.1.dylib
/usr/local/lib/libidn2.0.dylib
/usr/local/lib/libunistring.5.dylib
/usr/local/lib/libpsl.5.dylib
/usr/local/lib/libzstd.1.dylib
/usr/local/lib/liblzma.5.dylib
/usr/local/lib/libbz2.1.0.dylib
/usr/local/lib/libreadline.8.dylib
/usr/local/lib/libhistory.8.dylib
/usr/local/lib/libncurses.6.dylib
/usr/local/lib/libform.6.dylib
/usr/local/lib/libmenu.6.dylib
/usr/local/lib/libpanel.6.dylib
/usr/local/lib/libsqlite3.0.dylib
/usr/local/lib/libxml2.2.dylib
/usr/local/lib/libxslt.1.dylib
/usr/local/lib/libexslt.0.dylib
/usr/local/lib/libyaml-0.2.dylib
/usr/local/lib/libffi.8.dylib
/usr/local/lib/libgmp.10.dylib
/usr/local/lib/libmpfr.6.dylib
/usr/local/lib/libmpc.3.dylib
/usr/local/lib/libisl.23.dylib
/usr/local/lib/libcloog-isl.4.dylib
/usr/local/lib/libgomp.1.dylib
/usr/local/lib/libgcc_s.1.dylib
/usr/local/lib/libstdc++.6.dylib
/usr/local/lib/libquadmath.0.dylib
/usr/local/lib/libgfortran.5.dylib
/usr/local/lib/libatomic.1.dylib
/usr/local/lib/libomp.dylib

Total size: 847.2 MB
Average file size: 18.0 MB
Largest file: libstdc++.6.dylib (156.3 MB)
Smallest file: libatomic.1.dylib (0.2 MB)

Library dependency analysis complete.
Scan completed in 2.34 seconds.`,
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

export const ToolUseSetupScript = {
  name: "ToolUse Setup Script",
  render(props) {
    return { Component: ToolUse, props } as any;
  },
  args: {
    toolUse: mockSetupScript,
    toolUseState: {
      phase: 5,
      requestId: "1",
      toolUseId: mockSetupScript.tool_use_id,
      result: {
        text: JSON.stringify(mockScriptOutput),
        isError: false,
      },
    },
    isLastTurn: true,
    requestId: "1",
  } as any,
} satisfies Story;

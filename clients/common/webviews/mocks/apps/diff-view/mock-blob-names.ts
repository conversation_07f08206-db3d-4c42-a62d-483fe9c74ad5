export const fileContents: string = `"""Blob name calculation."""

import dataclasses
import hashlib
import heapq
import typing
import structlog

from base.blob_names import blob_names_pb2

logger = structlog.get_logger()

BlobName = str
"""Type alias for blob name."""


def get_blob_name(path: str, contents: bytes | str) -> BlobName:
    """Calculate the blob name for the given path name and contents."""
    hasher = hashlib.sha256()
    hasher.update(path.encode("utf-8"))
    if isinstance(contents, str):
        contents = contents.encode("utf-8")
    hasher.update(contents)
    return hasher.hexdigest()


def is_sorted(blob_names: typing.Sequence[bytes]) -> bool:
    """Returns true if the given list of blob names is sorted."""
    return all(blob_names[i] <= blob_names[i + 1] for i in range(len(blob_names) - 1))


def encode_blob_name(blob_name_hex: BlobName) -> bytes:
    """Convert a blob name from hex to bytes."""
    return bytes.fromhex(blob_name_hex)


@dataclasses.dataclass
class Blobs:
    """Class containing the blob names in delta format.

    This class mirrors the Blobs proto definition.
    """

    baseline_checkpoint_id: typing.Optional[str] = None
    added: typing.Sequence[bytes] = ()
    deleted: typing.Sequence[bytes] = ()

    def is_empty(self) -> bool:
        """Returns true if the blobs object is empty."""
        return self.baseline_checkpoint_id is None and len(self.added) == 0

    @staticmethod
    def from_fake_blob_names(blob_names: typing.Sequence[str]) -> "Blobs":
        """Convenience function to convert a list of fake string blob_names to a Blobs object.

        Mainly used by tests to convert raw strings ("blob1", "foo") to hex
        encoded strings then call from_blob_names() below
        """
        blob_names_hex = [x.encode("utf-8").hex() for x in blob_names]
        return Blobs.from_blob_names(blob_names_hex)

    @staticmethod
    def from_blob_names(blob_names_hex: typing.Sequence[BlobName]) -> "Blobs":
        """Convenience function to convert a list of hex blob_names to a Blobs object.

        Mainly used to convert from the old blob_names hex format to the bytes
        format used by the Blobs object.
        """
        blob_names_hex = sorted(blob_names_hex)
        return Blobs(
            baseline_checkpoint_id=None,
            added=list(map(bytes.fromhex, blob_names_hex)),
            deleted=[],
        )

    @staticmethod
    def from_proto(proto: blob_names_pb2.Blobs) -> "Blobs":
        """Convenience function to convert a Blobs proto to a Blobs object."""
        if not is_sorted(proto.added):
            logger.error("Added blob names are not sorted")
            raise ValueError("Added blob names are not sorted")
        if not is_sorted(proto.deleted):
            logger.error("Deleted blob names are not sorted")
            raise ValueError("Deleted blob names are not sorted")

        # Make sure to check if baseline_checkpoint_id exists, since if it
        # is not set it will default to "". source:
        # https://protobuf.dev/getting-started/pythontutorial/#protocol-format
        baseline_checkpoint_id = (
            proto.baseline_checkpoint_id
            if proto.HasField("baseline_checkpoint_id")
            else None
        )
        return Blobs(
            baseline_checkpoint_id=baseline_checkpoint_id,
            added=proto.added,
            deleted=proto.deleted,
        )

    def to_proto(self) -> blob_names_pb2.Blobs:
        """Convenience function to convert a Blobs object to a Blobs proto."""
        if not is_sorted(self.added):
            logger.error("Added blob names are not sorted")
            raise ValueError("Added blob names are not sorted")
        if not is_sorted(self.deleted):
            logger.error("Deleted blob names are not sorted")
            raise ValueError("Deleted blob names are not sorted")

        return blob_names_pb2.Blobs(
            baseline_checkpoint_id=self.baseline_checkpoint_id,
            added=self.added,
            deleted=self.deleted,
        )


@dataclasses.dataclass
class BlobsDiff:
    """Representation of the difference between two Blobs objects that share the same
    baseline checkpoint. Relative to one of the Blobs objects, not to a baseline!
    """

    unique_to_lhs: typing.Sequence[bytes] = ()
    unique_to_rhs: typing.Sequence[bytes] = ()


class BlobsBaselineDiff:
    """Sentinel result when two Blobs objects have different baselines."""

    pass


def compute_diff(
    left: Blobs, right: Blobs
) -> typing.Union[BlobsDiff, BlobsBaselineDiff]:
    if left.baseline_checkpoint_id != right.baseline_checkpoint_id:
        return BlobsBaselineDiff()
    # Iterate each sequence at least once and at most twice, because merge(subtract, subtract) is easy to write
    rhs_only_added = _subtract(right.added, left.added)
    lhs_only_added = _subtract(left.added, right.added)
    rhs_only_deleted = _subtract(right.deleted, left.deleted)
    lhs_only_deleted = _subtract(left.deleted, right.deleted)
    return BlobsDiff(
        unique_to_lhs=list(heapq.merge(lhs_only_added, rhs_only_deleted)),
        unique_to_rhs=list(heapq.merge(rhs_only_added, lhs_only_deleted)),
    )


def _subtract(
    sorted_a: typing.Iterable[bytes], sorted_b: typing.Iterable[bytes]
) -> typing.Iterable[bytes]:
    iter_a = iter(sorted_a)
    try:
        a = next(iter_a)
    except StopIteration:
        return
    for b in sorted_b:
        while a <= b:
            if a < b:
                yield a
            try:
                a = next(iter_a)
            except StopIteration:
                return
    yield a
    yield from iter_a
`;

export const addDocstrings: any[] = [
  {
    data: { streamId: "768e88f2-5308-42db-8de1-6d542234f053" },
    type: "diff-view-diff-stream-started",
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 1,
        originalEndLine: 14,
        stagedStartLine: 1,
        stagedEndLine: 14,
      },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '"""' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\nBlob name calculation" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " an" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d management module" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\nThis module provides utilities" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " for calculating," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " encoding" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ", and managing blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\nwhich are unique" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " identifiers for file" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " contents" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " It" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " also includes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " classes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " for\nrepresenting collections" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " of bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs and computing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " differences" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " between them." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n"""' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\nimport dataclasses" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\nimport hashlib" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\nimport heap" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "q\nimport typing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\nimport structlog" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\nfrom base." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "blob_names import" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " blob_names_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "pb2\n\nlogger" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " = structlog." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "get_logger()" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\nBlobName" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' = str\n"""' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Type alias for blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " name," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " represente" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d as a hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "adecimal string" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '."""' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: { chunkEnd: true, streamId: "768e88f2-5308-42db-8de1-6d542234f053" },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 17,
        originalEndLine: 34,
        stagedStartLine: 23,
        stagedEndLine: 40,
      },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "def get" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_blob_name" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(path: str" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ", contents: bytes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " | str) ->" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " BlobName:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    Calculate the blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " name for the given" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " path name and contents" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n    This" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " function generates" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a unique identifier" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " (" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "blob name) by" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " hashing the file" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " path" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    and its" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " contents. The blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " name is returne" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d as a hexa" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "decimal string." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    Args:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        path (" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "str): The file" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " path.\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "contents (bytes |" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " str): The file" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " contents," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " either as bytes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " or a string." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    Returns:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        Blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Name: A" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "adecimal string" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " representing the calculate" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d blob name." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """\n    ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hasher = hash" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lib.sha256" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "()\n    has" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "her.update(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'path.encode("' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'utf-8"))' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    if isinstance" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(contents, str" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "):\n        contents" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " = contents.encode" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '("utf-8' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '")\n    has' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "her.update(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "contents)\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "return hasher." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hexdigest()" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n\ndef is_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "sorted(blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names: typing." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Sequence[bytes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "]) -> bool:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    Check" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " if the given list" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " of blob names is" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " sorted in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " ascending order." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    Args" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ":\n        blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_names (typing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".Sequence[" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bytes]): A" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " sequence of blob names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " as" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " bytes.\n\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Returns:\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bool: True if" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the sequence" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " is sorted, False" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " otherwise.\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '"""\n    return' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " all(blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names[i]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " <= blob_names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "[i + " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "1] for i" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " in range(len" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(blob_names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ") - 1" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "))\n\n\ndef encode" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_blob_name" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(blob_name" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_hex: B" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lobName) ->" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " bytes:\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '"""\n    Convert' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a blob name from" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "adecimal string" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " to bytes." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    Args:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        blob_name" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_hex (B" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lobName): The" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " blob name as a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " hexadecimal" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " string.\n\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Returns:\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bytes: The blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " name converte" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d to bytes." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """\n    ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "return bytes.from" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hex(blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_name_hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ")" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: { chunkEnd: true, streamId: "768e88f2-5308-42db-8de1-6d542234f053" },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 37,
        originalEndLine: 113,
        stagedStartLine: 71,
        stagedEndLine: 147,
      },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "@data" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "classes.data" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "class\nclass" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    Class representing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a collection of blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " delta format." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    This class mirrors" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " proto definition an" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d provides methods for" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    creating," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " converting" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ", and manip" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "ulating collections" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " of blob names." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    Attributes:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        baseline_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "checkpoint_id (" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Optional" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "[str]):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " The ID of the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " baseline checkpoint." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        added (" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Sequence[bytes]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Sequence of adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d blob names." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        deleted (" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Sequence[bytes]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "): Sequence of" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " deleted blob names." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    baseline_checkpoint" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_id: typing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".Optional[str" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "] = None" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    added: typing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".Sequence[" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bytes] = ()" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    deleted:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " typing.Sequence" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "[bytes] =" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " ()\n\n    def" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " is_empty(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "self) -> bool" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ':\n        """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        Check" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " if the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object is" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " empty.\n\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Returns:\n            " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bool: True if" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " there" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "'s" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " no baseline" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " checkpoint an" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d no added bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " False otherwise." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n        """\n        ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "return self.baseline" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_checkpoint_i" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d is None and len" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(self.adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d) == 0" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    @static" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "method\n    def" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " from_fake_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "blob_names(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "blob_names:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " typing.Sequence" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "[str]) ->" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' "Blobs' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '":\n        """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        Create" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " object from a list" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " of fake string blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names.\n\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "This" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " metho" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d is mainly" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " used in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " tests to convert raw" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " strings to" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " hex-" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "encoded\n        strings" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " an" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d then" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " create" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " object." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n        Args:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names (" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Sequence[str]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "): A sequence of" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " fake blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names as strings." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n        Returns:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs: A new" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " create" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d from the fake" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " blob names." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n        """\n        ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "blob_names_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hex = [x" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '.encode("utf' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '-8").hex' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "() for x in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " blob_names]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        return" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "from_blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names(blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names_hex)" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    @static" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "method\n    def" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " from_blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names(blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names_hex:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " typing.Sequence" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "[BlobName" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ']) -> "' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'Blobs":' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n        """\n        ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Create a Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs object from a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " list of hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "adecimal blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names.\n\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "This method is mainly" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " used to convert from" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the old blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names hex format" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        to the bytes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " format used by the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n        Args" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ":\n            blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_names_hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " (Sequence[" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "BlobName]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "): A sequence of" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names as hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " strings.\n\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Returns:\n            " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Blobs:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " A new Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs object with" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the provide" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d blob names adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'd.\n        """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names_hex =" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " sorted(blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names_hex)" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        return" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            baseline_checkpoint" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_id=None" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ",\n            adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d=list(map" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(bytes.from" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hex, blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_names_hex" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ")),\n            delete" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d=[],\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ")\n\n    @" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "staticmethod\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "def from_proto" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(proto: blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_names_pb" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "2.Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'obs) -> "' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'Blobs":' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n        """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        Create a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object from" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " prot" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obuf message" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n        This" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " method performs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " validation" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " checks" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " on the input" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " proto" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " message" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        and creates a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " new Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " object from" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " it" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n        Args" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ":\n            proto" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " (blob_names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_pb2." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Blobs):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " The" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs prot" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obuf message." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n        Returns:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs: A new" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " created from the prot" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obuf message." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n        Raises:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            ValueError:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " If the adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d or deleted blob names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the proto" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " are not sorted." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n        """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        if not is" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_sorted(proto" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".added):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            logger.error" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '("Added blob names' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' are not sorted")' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            raise ValueError" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '("Added blob names' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' are not sorted")' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        if not" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " is_sorted(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "proto.deleted):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            logger." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'error("Delete' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d blob names are not" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' sorted")\n            ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'raise ValueError("' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Deleted blob names are" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' not sorted")' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n        baseline" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_checkpoint_i" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d = (\n            " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "proto.baseline_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "checkpoint_id" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            if proto." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'HasField("baseline' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_checkpoint_i" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'd")\n            else' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " None\n        )" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        return" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            baseline_checkpoint" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_id=baseline" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_checkpoint_i" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d,\n            adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d=proto.adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d,\n            delete" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d=proto.delete" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d,\n        )" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    def to" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_proto(self" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ") -> blob_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "names_pb2" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ':\n        """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        Convert the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " to a Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs protobuf" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " message.\n\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "This method performs validation" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " checks on the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        before" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " creating" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " protobuf message" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n        Returns" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ":\n            blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_names_pb" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "2.Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs: The" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " converted to a prot" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obuf message." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n        Raises:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            ValueError:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " If the added or" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " deleted blob names are" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " not sorted." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n        """\n        ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "if not is_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "sorted(self." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "added):\n            " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'logger.error("' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Added blob names are" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' not sorted")' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n            raise ValueError("' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Added blob names are" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' not sorted")' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        if not is" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_sorted(self" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".deleted):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            logger.error" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '("Deleted blob' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names are not sorte" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: 'd")\n            raise' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' ValueError("Delete' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d blob names are not" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ' sorted")\n\n        ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "return blob_names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_pb2." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Blobs(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            baseline_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "checkpoint_id=" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "self.baseline_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "checkpoint_id," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            added=" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "self.added," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            deleted=" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "self.deleted," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        )" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: { chunkEnd: true, streamId: "768e88f2-5308-42db-8de1-6d542234f053" },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 116,
        originalEndLine: 165,
        stagedStartLine: 198,
        stagedEndLine: 247,
      },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "@data" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "classes.data" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "class\nclass B" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lobsDiff:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    Representation of" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the difference between two" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs objects" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " that share the same" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    baseline checkpoint" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    This" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " class stores" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names that are unique" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " to each" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " of" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the compare" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    objects." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " It's" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " important" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " to note that this" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " diff" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " is relative to one" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " of the Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs\n    objects" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ", not to a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " baseline." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    Attributes:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        unique_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "to_lhs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " (Sequence[" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bytes]):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names unique to the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " left-" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hand side" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs object." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        unique_to" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_rhs (" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Sequence[bytes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "]): Blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names unique to the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " right-hand side" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '.\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    unique_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "to_lhs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ": typing." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Sequence[bytes]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " = ()\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "unique_to_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "rhs: typing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".Sequence[" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bytes] = ()" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n\nclass Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "BaselineDiff" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ':\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    Sentinel" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " class" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " indicating" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " that two compare" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d Blobs objects" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " have different baselines" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n    This" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " class is used as" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a return" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " value in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the compute" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_diff function when" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    compare" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d Blobs objects" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " don" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "'t share the same" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " baseline checkpoint." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """\n\n    ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "pass\n\n\ndef compute" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_diff(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    left:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs, right" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ": Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n) -> typing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".Union[B" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lobsDiff," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " BlobsBase" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lineDiff]:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    Compute" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the difference between two" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs objects" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n    This" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " function calc" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "ulates the difference between" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " two Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " objects by comparing" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    their adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d and deleted blob names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ". If" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " Blobs objects have" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " different baseline" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    checkpoints" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ", a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " BlobsBase" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lineDiff object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " is returned instea" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d.\n\n    Args" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ":\n        left" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " (Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "): The left-" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hand side Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs object for" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " comparison" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n        right" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " (Blobs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "): The right-" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hand side Bl" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "obs object for comparison" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ".\n\n    Returns" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ":\n        Union" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "[BlobsD" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "iff, Blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "sBaselineD" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "iff]: A" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " BlobsDiff" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " object" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " representing the" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        difference if" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the bas" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "elines match" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ", or a B" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lobsBaselin" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "eDiff object if" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " they" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " don't." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """\n    ' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "if left.baseline" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_checkpoint_i" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d != right.baseline" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_checkpoint_i" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d:\n        return" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " BlobsBase" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "lineDiff()" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    r" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hs_only_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "added = _subtract" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(right.adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d, left.adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d)\n    l" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hs_only_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "added = _subtract" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(left.adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d, right.adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d)\n    r" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hs_only_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "deleted = _subtract" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(right.delete" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d, left.delete" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d)\n    l" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hs_only_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "deleted = _subtract" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(left.delete" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d, right.delete" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d)\n    return" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " BlobsDiff" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(\n        unique" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_to_l" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "hs=list(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "heapq." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "merge(lhs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_only_adde" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d, rhs_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "only_deleted))," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        unique_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "to_rhs" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "=list(he" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "apq.merge" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "(rhs_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "only_added," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " lhs_only" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "_deleted))," },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    )\n\n\ndef" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " _subtract(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    sorted_a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ": typing." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Iterable[bytes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "], sorted_b" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ": typing." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Iterable[bytes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "]\n) ->" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " typing.It" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "erable[bytes]:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Compute" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " the set" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " difference between two sorte" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d iterables of" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names.\n\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "This helper" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " function is" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " used to" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " fin" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names that are in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " sorte" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "a but not in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " sorted_b." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    It assumes" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " both" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " input" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " iterables are sorte" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d in" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " ascending order." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n\n    Args:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        sorted_a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " (Iterable" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "[bytes]):" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " The" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " first sorted iterable" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " of blob names." },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        sorted_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "b (It" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "erable[bytes]" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "): The second sorte" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d iterable of blob" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " names.\n\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Returns:\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "Iterable[" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "bytes]: An" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " it" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "erable of blob names" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " that are in sorte" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d_a but not" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " in sorted_b" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: '.\n    """' },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    iter_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "a = iter(" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "sorted_a)" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n    try:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n        a =" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " next(iter_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "a)\n    " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "except StopIt" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "eration:\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "return\n    for" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " b in sorted_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "b:\n        " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "while a <= b" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: ":\n            if" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " a < b:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n                yield a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n            try:" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "\n                a =" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: " next(iter_" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "a)\n            " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "except StopIt" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "eration:\n                " },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "return\n    yiel" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d a\n    yiel" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "d from iter_a" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: { newText: "" },
      streamId: "768e88f2-5308-42db-8de1-6d542234f053",
    },
  },
  {
    type: "diff-view-diff-stream-ended",
    data: { streamId: "768e88f2-5308-42db-8de1-6d542234f053" },
  },
];
export const rmDocstrings: any[] = [
  {
    data: {
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
    type: "diff-view-diff-stream-started",
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 1,
        stagedStartLine: 1,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "import",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " data",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "classes\nimport",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " hash",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lib\nimport he",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "apq\nimport",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " typing",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\nimport",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " struct",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "log\n\nfrom",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " base",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "blob_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " import blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "pb",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "2",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n\nlogger",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " = structlog.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "get_logger()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 1,
        originalEndLine: 12,
        stagedStartLine: 1,
        stagedEndLine: 12,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 13,
        stagedStartLine: 11,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "B",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lobName = str",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 13,
        originalEndLine: 15,
        stagedStartLine: 11,
        stagedEndLine: 13,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 17,
        stagedStartLine: 14,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "def",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " get_blob_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "name(path:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " str, contents:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " bytes | str)",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " -> BlobName",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ":\n    has",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "her = hashlib",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".sha256()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    hasher",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".update(path",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '.encode("',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'utf-8"))',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    if",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " isinstance(contents,",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " str",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "):\n        contents",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " = contents.encode",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '("utf-8',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '")\n    has',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "her.update(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "contents)\n    ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "return hasher.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hexdigest()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 17,
        originalEndLine: 25,
        stagedStartLine: 14,
        stagedEndLine: 22,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 27,
        stagedStartLine: 23,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "def is",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_sorted(blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names: typing",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".Sequence[",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "bytes]) -> bool",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ":\n    return",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " all(blob_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "names[i]",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " <= blob_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "[i + ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "1] for i",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " in range(len",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(blob_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ") - 1",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "))\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 27,
        originalEndLine: 30,
        stagedStartLine: 23,
        stagedEndLine: 26,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 32,
        stagedStartLine: 27,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "def encode",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_blob_name",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(blob_name",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_hex: B",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lobName) ->",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " bytes:\n    ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "return bytes.from",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hex(blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_name_hex",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ")\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 32,
        originalEndLine: 35,
        stagedStartLine: 27,
        stagedEndLine: 30,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 37,
        stagedStartLine: 31,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "@",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "dataclasses.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "dataclass\nclass",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " Blobs:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    baseline",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "checkpoint",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "id: typing",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".Optional[str",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "] = None",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    adde",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d: typing.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Sequence[bytes]",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " = ()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    deleted:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " typing.Sequence",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "[bytes] =",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " ()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n\n    def is",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_empty",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(self) ->",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " bool:\n        ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "return",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " self",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".baseline",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_checkpoint_i",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d is None and len",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(self.adde",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d) == 0",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n\n    @",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "static",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "method\n    def",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " from_fake",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_blob_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(blob_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ": typing.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Sequence[str",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ']) -> "',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'Blobs":',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hex",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " = [x",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '.encode("utf',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '-8").',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hex() for x",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " in blob_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "]\n        return",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " Blobs.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "from_blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names(blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names_hex",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ")\n\n    @",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "staticmethod\n    ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "def from_blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names(blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hex: typing.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Sequence[B",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lobName]) ->",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' "Blobs',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '":\n        blob',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hex",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " = sorte",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d(blob_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_hex)",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        return Bl",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "obs(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            baseline_checkpoint",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_id=None",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ",\n            adde",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d=list",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(map",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(bytes",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".fromhex",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ", blob_names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_hex)),",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            deleted=[",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "],",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        )",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n\n    @",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "static",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "method\n    def",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " from_proto",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(proto",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ": blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_names_pb",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "2.Bl",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'obs) -> "',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'Blobs":',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        if",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " not",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " is",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_sorted(proto",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".added):",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            logger",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".error",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '("Adde',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d blob names are not",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' sorted")\n            ',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "raise",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' ValueError("Added blob',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " names are not sorte",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'd")\n        if',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " not is_sorte",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d(proto.delete",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d):\n            logger",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '.error("',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Deleted blob names are",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' not sorted")',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '\n            raise ValueError("',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Deleted blob names",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' are not sorted")',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n\n        baseline",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_checkpoint_i",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d = (",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            proto.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "baseline_checkpoint_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "id",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            if proto.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Has",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'Field("baseline_',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'checkpoint_id")',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            else None",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        )",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        return",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " Blobs(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            baseline_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "checkpoint_id=",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "baseline_checkpoint_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "id,\n            ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "added=proto.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "added,\n            ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "deleted=proto.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "deleted,\n        ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ")\n\n    def",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " to",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_proto(self",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ") -> blob_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "names_pb2",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".Blobs",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ":\n        if",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " not",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " is",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_sorted(self",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".added):",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            logger.error",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '("Added blob names',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' are not sorted")',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            raise ValueError",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: '("Added blob names',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' are not sorted")',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        if not",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " is_sorted(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "self.deleted):",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            logger.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'error("Delete',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d blob names are not",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' sorted")\n            ',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: 'raise ValueError("',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Deleted blob names are",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ' not sorted")',
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n\n        return",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " blob_names_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "pb2.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Blobs(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            baseline_checkpoint",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_id=self",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".baseline_checkpoint",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_id,",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            added=self",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".added,",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            deleted=self",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".deleted,",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        )",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 37,
        originalEndLine: 114,
        stagedStartLine: 31,
        stagedEndLine: 108,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 116,
        stagedStartLine: 89,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "@datacl",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "asses.dataclass",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\nclass Blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "sDiff:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    unique_to",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_l",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hs: typing.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Sequence[bytes",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "] = ()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    unique_to",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_rhs:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " typing.Sequence",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "[bytes] =",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " ()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 116,
        originalEndLine: 124,
        stagedStartLine: 89,
        stagedEndLine: 97,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 126,
        stagedStartLine: 95,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "class",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " BlobsBase",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lineDiff:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    pass",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 126,
        originalEndLine: 130,
        stagedStartLine: 95,
        stagedEndLine: 99,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 132,
        stagedStartLine: 99,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "def",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " compute_diff(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    left:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " Blobs, right",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ": Blobs",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n) -> typing",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".Union[B",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lobsDiff,",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " BlobsBase",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lineDiff]:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    if",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " left.baseline_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "checkpoint_id !=",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " right.baseline_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "checkpoint_id:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        return B",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "lobs",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "BaselineDiff",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "()",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    r",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hs_only",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_added = _",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "subtract(right",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".added, left",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".added)",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    lhs_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "only_added =",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " _subtract(left",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".added, right",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".added)",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    rhs_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "only_deleted =",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " _subtract(right",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".deleted, left",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".deleted)",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    lhs_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "only_deleted =",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " _subtract(left",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".deleted, right",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".deleted)",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    return Blob",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "sDiff(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        unique_to",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_lhs=",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "list",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(heapq",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".merge(l",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hs_only_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "added, rhs",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_only_delete",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d)),\n        unique",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_to_r",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "hs=list(",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "heapq.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "merge(rhs",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_only_adde",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d, lhs_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "only_deleted)),",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    )",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 132,
        originalEndLine: 146,
        stagedStartLine: 99,
        stagedEndLine: 113,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      newChunkStart: {
        originalStartLine: 148,
        stagedStartLine: 114,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "def _subtract",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(\n    sorte",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d_a: typing",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".Iterable",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "[bytes], sorte",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d_b: typing",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ".Iterable",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "[bytes]",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n) -> typing.",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Iterable[",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "bytes]:\n    ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "iter",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_a = iter",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "(sorted_a",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ")\n    try",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: ":\n        a",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " = next(iter",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "_a)",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    except Stop",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "Iteration:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        return\n    ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "for b in sorte",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d_b:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n        while",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " a <= b:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            if",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " a < b:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n                yield a",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n            try:",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n                a =",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: " next(iter_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "a)\n            ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "except StopIt",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "eration:\n                ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "return\n    yiel",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "d a",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "\n    ",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "yield from iter_",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkContinue: {
        newText: "a\n",
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-chunk",
    data: {
      chunkEnd: {
        originalStartLine: 148,
        originalEndLine: 166,
        stagedStartLine: 114,
        stagedEndLine: 132,
      },
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
  {
    type: "diff-view-diff-stream-ended",
    data: {
      streamId: "17be54da-d864-4064-a53b-6e1f89520905",
    },
  },
];

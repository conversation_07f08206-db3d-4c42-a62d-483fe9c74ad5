/* eslint-disable @typescript-eslint/naming-convention */
import type { Meta, StoryObj } from "@storybook/svelte";
import "./chat-mock";
import component from "$common-webviews/src/apps/chat/ChatLoader.svelte";
import { withMonaco } from "$common-webviews/mocks/design-system/monaco-provider/monaco-decorator";

const meta = {
  title: "app/Chat",
  component,
} satisfies Meta<component>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Example: Story = {
  args: {
    initialFlags: {
      enableAgentMode: false,
    },
  },
};

export const EnableDebug: Story = {
  name: "Enable Debug Features",
  args: {
    initialFlags: {
      enableDebugFeatures: true,
      enableAgentMode: false,
    },
  },
  decorators: [withMonaco],
};

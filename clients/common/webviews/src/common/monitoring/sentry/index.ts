import * as Sentry from "@sentry/svelte";

// Track if Sentry has been initialized
let isSentryInitialized = false;
/**
 * Initialize Sentry monitoring in the webview
 *
 * This function initializes Sentry with configuration passed from the IntelliJ plugin
 * via the window.augmentFlags object.
 */
export function initializeSentry() {
  // Check if Sentry configuration is available in window.augmentFlags
  if (!window.augmentFlags?.sentry?.enabled) {
    return;
  }

  const sentryConfig = window.augmentFlags.sentry;
  if (!sentryConfig) {
    console.warn("Sentry configuration not found in window.augmentDeps");
    return;
  }
  if (isSentryInitialized) {
    console.warn("Sentry is already initialized, duplicate initialization attempt");
    return;
  }
  try {
    // Initialize Sentry with the configuration from IntelliJ
    Sentry.init({
      dsn: sentryConfig.dsn,
      release: sentryConfig.release,
      environment: sentryConfig.environment,
      tracesSampleRate: sentryConfig.tracesSampleRate || 0.0,
      replaysSessionSampleRate: sentryConfig.replaysSessionSampleRate || 0.0,
      replaysOnErrorSampleRate: sentryConfig.replaysOnErrorSampleRate || 0.0,
      sampleRate: sentryConfig.errorSampleRate || 0.0,
      sendDefaultPii:
        sentryConfig.sendDefaultPii === undefined ? false : sentryConfig.sendDefaultPii,
      // Add integrations if available
      integrations: (() => {
        const integrations = [];

        // Try to add browser tracing if available
        try {
          // Use type assertion to tell TypeScript these properties exist
          const browserTracing = (Sentry as any).BrowserTracing;
          if (typeof browserTracing === "function") {
            integrations.push(new browserTracing());
          }
        } catch (e) {
          console.warn("Sentry BrowserTracing not available:", e);
        }

        // Replay feature is disabled
        // No Replay integration will be added

        return integrations;
      })(),
      beforeSend: (event) => {
        // Filter sensitive data if needed
        return event;
      },
    });

    // Set additional tags
    if (sentryConfig.tags) {
      Object.entries(sentryConfig.tags).forEach(([key, value]) => {
        Sentry.setTag(key, String(value));
      });
    }
    isSentryInitialized = true;
  } catch (error) {
    console.error("Failed to initialize Sentry:", error);
  }
}

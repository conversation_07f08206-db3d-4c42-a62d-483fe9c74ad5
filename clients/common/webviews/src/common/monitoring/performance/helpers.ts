/**
 * Performance monitoring initialization for webviews.
 *
 * This module initializes monitoring for webviews based on feature flags.
 * It should be imported by the main entry points of the webviews.
 * It also ensures Sentry is initialized before performance monitoring starts.
 */

import { startMonitoringPerformance } from "./core";
// Default thresholds
const DEFAULT_LOW_FRAMERATE_THRESHOLD = 16; // fps
const DEFAULT_SLOW_INP_THRESHOLD = 200; // ms

export interface PerformanceMonitoringConfig {
  enabled: boolean;
  lowFramerateThreshold?: number;
  slowInpThreshold?: number;
}

/**
 * Check if monitoring is enabled via feature flags
 */
export function isPerformanceMonitoringEnabled(): boolean {
  // Feature flag name for enabling monitoring in webviews
  return window.augmentFlags?.enablePerformanceMonitoring ?? false;
}

/**
 * Initialize monitoring if enabled in the configuration.
 *
 * @param config Performance monitoring configuration
 */
export function setupPerformanceMonitoring(config: PerformanceMonitoringConfig): void {
  if (config.enabled) {
    startMonitoringPerformance({
      lowFramerateThreshold: config.lowFramerateThreshold || DEFAULT_LOW_FRAMERATE_THRESHOLD,
      slowInpThreshold: config.slowInpThreshold || DEFAULT_SLOW_INP_THRESHOLD,
    });
  }
}
/**
 * Initialize monitoring based on feature flags.
 * This function checks if the feature flag is enabled and initializes monitoring accordingly.
 * It also checks if Sentry is initialized before starting performance monitoring.
 */
export function initializePerformanceMonitoring(): void {
  // Check if Sentry is initialized before starting performance monitoring
  const isSentryInitialized = Boolean(window.augmentFlags?.sentry?.enabled);

  // Initialize monitoring
  setupPerformanceMonitoring({
    enabled: isPerformanceMonitoringEnabled(),
    lowFramerateThreshold: DEFAULT_LOW_FRAMERATE_THRESHOLD,
    slowInpThreshold: DEFAULT_SLOW_INP_THRESHOLD,
  });

  // Log Sentry initialization status
  if (isPerformanceMonitoringEnabled() && !isSentryInitialized) {
    console.warn(
      "[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.",
    );
  }
}

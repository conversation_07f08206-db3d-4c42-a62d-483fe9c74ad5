/**
 * Custom exception classes for performance issues in webviews.
 * These exceptions are used to capture performance issues in Sentry.
 */

/**
 * Base class for performance exceptions.
 */
export class PerformanceException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PerformanceException';
  }
}

/**
 * Exception for slow framerate issues.
 */
export class SlowFramerateException extends PerformanceException {
  readonly fps: number;
  readonly threshold: number;
  readonly avgFramerate: number;
  readonly webviewId: string;

  constructor(fps: number, threshold: number, avgFramerate: number, webviewId: string = 'unknown') {
    super(`Slow framerate detected: ${fps.toFixed(1)} fps`);
    this.name = 'SlowFramerateException';
    this.fps = fps;
    this.threshold = threshold;
    this.avgFramerate = avgFramerate;
    this.webviewId = webviewId;
  }
}

/**
 * Exception for slow Interaction to Next Paint (INP) issues.
 */
export class SlowINPException extends PerformanceException {
  readonly inp: number;
  readonly threshold: number;
  readonly target: string | null;
  readonly webviewId: string;

  constructor(inp: number, threshold: number, target: string | null = null, webviewId: string = 'unknown') {
    super(`Slow INP detected: ${inp.toFixed(1)} ms${target ? ` on target: ${target}` : ''}`);
    this.name = 'SlowINPException';
    this.inp = inp;
    this.threshold = threshold;
    this.target = target;
    this.webviewId = webviewId;
  }
}

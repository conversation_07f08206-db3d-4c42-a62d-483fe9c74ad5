/**
 * Performance monitoring for webviews.
 *
 * This module provides functionality to monitor framerate and Interaction to Next Paint (INP)
 * in webviews. It's designed to be initialized once and will log slow monitoring to the console.
 * It also reports performance issues to <PERSON><PERSON> for error tracking.
 */

import * as Sentry from "@sentry/svelte";
import { SlowFramerateException, SlowINPException } from "./exceptions";

export interface PerformanceMonitoringOptions {
  lowFramerateThreshold: number;
  slowInpThreshold: number;
}

/**
 * Initialize monitoring for webviews.
 * This should be called once when the webview is loaded.
 *
 * @param options Configuration options for monitoring
 */
export function startMonitoringPerformance(options: PerformanceMonitoringOptions): void {
  // Avoid initializing multiple times

  // We expose monitoring related methods and data for use in external logging system like Sentry
  // or for debugging purposes in devtools
  window.augmentPerformance = window.augmentPerformance || {};

  // Avoid initializing multiple times (double check)
  if (window.augmentPerformance.initialized) return;
  window.augmentPerformance.initialized = true;

  // Framerate monitoring
  let frameCount = 0;
  let lastFrameTime = performance.now();
  let framerate = 60; // Default assumption
  const framerateHistory: number[] = [];
  const HISTORY_SIZE = 10; // Keep track of last 10 measurements

  // INP monitoring
  let worstInpValue = 0;

  // Thresholds
  const LOW_FRAMERATE_THRESHOLD = options.lowFramerateThreshold;
  const SLOW_INP_THRESHOLD = options.slowInpThreshold;
  //Since there won't be any navigation, Sentry library wouldn't work
  //We measure INP manually
  const updateINP = (entries: PerformanceObserverEntryList): void => {
    const interactionEntries = entries.getEntries().filter(
      (entry) =>
        "interactionId" in entry &&
        "duration" in entry &&
        entry.startTime > 0 &&
        // guard against invalid entries, duration can contain timestamp as well
        entry.duration < 1_000_000,
    );
    if (interactionEntries.length === 0) return;

    // Sort by duration (descending)
    interactionEntries.sort((a, b) => b.duration - a.duration);

    // Get the 98th percentile entry (approximation)
    const percentileIndex = Math.floor(interactionEntries.length * 0.98);
    const newINP =
      interactionEntries[Math.min(percentileIndex, interactionEntries.length - 1)].duration;

    // Update INP if the new value is higher
    if (newINP > SLOW_INP_THRESHOLD) {
      // Log slow INP
      console.error(`[Augment Performance] Slow INP detected: ${newINP.toFixed(1)} ms`);

      // Get the interaction target if available
      let targetInfo = null;
      const slowestEntry = interactionEntries[0];
      if (slowestEntry && "target" in slowestEntry) {
        targetInfo = slowestEntry.target;
        console.error(`[Augment Performance] Slow interaction target:`, targetInfo, slowestEntry);
      }

      const exception = new SlowINPException(
        newINP,
        SLOW_INP_THRESHOLD,
        targetInfo ? String(targetInfo) : null,
        window.location.href,
      );

      Sentry.withScope((scope) => {
        // Set tags and context for the event
        scope.setTag("performance_issue", "slow_inp");
        scope.setTag("inp_value", newINP.toFixed(1));
        scope.setTag("webview_url", window.location.href);

        if (targetInfo) {
          scope.setTag("interaction_target", String(targetInfo));
        }

        // Set extra context data
        scope.setExtra("performance_data", {
          inp: newINP,
          threshold: SLOW_INP_THRESHOLD,
          target: targetInfo,
        });
        scope.setLevel("warning");
        // Capture the exception
        Sentry.captureException(exception);
      });

      // record the worst INP value
      if (newINP > worstInpValue) {
        worstInpValue = newINP;
      }
    }
  };
  // Framerate measurement using requestAnimationFrame
  function measureFramerate(timestamp: number): void {
    // Calculate time since last frame
    const elapsedMilliSeconds = timestamp - lastFrameTime;
    frameCount++;

    // Update framerate every second
    if (elapsedMilliSeconds > 1000) {
      // Calculate frames per second
      framerate = (frameCount * 1000) / elapsedMilliSeconds;
      frameCount = 0;
      lastFrameTime = timestamp;

      // Add to history and maintain history size
      framerateHistory.push(framerate);
      if (framerateHistory.length > HISTORY_SIZE) {
        framerateHistory.shift();
      }

      // Calculate average framerate from history
      const avgFramerate =
        framerateHistory.reduce((sum, fps) => sum + fps, 0) / framerateHistory.length;

      // Log slow framerate
      if (framerate < LOW_FRAMERATE_THRESHOLD) {
        console.error(`[Augment Performance] Slow framerate detected: ${framerate.toFixed(1)} fps`);
        console.error(
          `[Augment Performance] Avg framerate detected: ${avgFramerate.toFixed(1)} fps`,
        );

        const isCritical = framerate < 15; // Critical threshold from PerformanceMonitor.kt
        const exception = new SlowFramerateException(
          framerate,
          LOW_FRAMERATE_THRESHOLD,
          avgFramerate,
          window.location.href,
        );

        Sentry.withScope((scope) => {
          // Set tags and context for the event
          scope.setTag("performance_issue", "slow_framerate");
          scope.setTag("fps_value", framerate.toFixed(1));
          scope.setTag("avg_fps", avgFramerate.toFixed(1));
          scope.setTag("webview_url", window.location.href);

          // Set extra context data
          scope.setExtra("performance_data", {
            fps: framerate,
            avgFps: avgFramerate,
            threshold: LOW_FRAMERATE_THRESHOLD,
            isCritical: isCritical,
            framerateHistory: [...framerateHistory],
          });
          scope.setLevel("warning");
          // Capture the exception
          Sentry.captureException(exception);
        });
      }
    }

    // Continue measuring
    requestAnimationFrame(measureFramerate);
  }

  // Start measuring framerate
  requestAnimationFrame(measureFramerate);

  // INP measurement using PerformanceObserver
  if (PerformanceObserver.supportedEntryTypes.includes("event")) {
    try {
      // Function to calculate INP
      // Create observer for event timing entries
      const eventObserver = new PerformanceObserver((entryList) => {
        updateINP(entryList);
      });

      // Start observing event timing entries
      // we want longer than 2.5 frames (40ms)
      // https://github.com/GoogleChrome/web-vitals/blob/main/src/onINP.ts#L112
      eventObserver.observe({
        entryTypes: ["event", "first-input"],
        buffered: true,
      });
    } catch (e) {
      console.error("[Augment Performance] Error setting up INP monitoring:", e);
    }
  } else {
    console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");
  }

  // Expose metrics
  window.augmentPerformance.getFramerate = () => framerate;
  window.augmentPerformance.getWorstINP = () => worstInpValue;
}

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { startMonitoringPerformance } from "../core";
import { SlowFramerateException, SlowINPException } from "../exceptions";

/* eslint-disable @typescript-eslint/naming-convention */
// eslint doesn't like some of the property names in the mocks hence disabling

// Mock Sentry
vi.mock("@sentry/svelte", () => ({
  captureException: vi.fn(),
  withScope: vi.fn((callback) =>
    callback({
      setTag: vi.fn(),
      setExtra: vi.fn(),
      setLevel: vi.fn(),
    }),
  ),
  Severity: {
    Warning: "warning",
    Error: "error",
  },
}));

describe("Performance Monitoring with Sentry", () => {
  let mockPerformanceObserverCallback: any;
  let rafCallCount = 0;

  beforeEach(() => {
    // Use fake timers for deterministic testing - this is the Vitest best practice
    vi.useFakeTimers();

    // Clear mocks
    vi.clearAllMocks();
    rafCallCount = 0;

    // Mock window.augmentPerformance
    window.augmentPerformance = {
      initialized: false,
    };

    // Mock requestAnimationFrame with controlled recursion to prevent infinite loops
    // This approach is better than using real setTimeout delays
    vi.spyOn(window, "requestAnimationFrame").mockImplementation((cb) => {
      // Only execute the callback for the first few calls to prevent infinite recursion
      if (rafCallCount < 3) {
        rafCallCount++;
        // Use setTimeout with fake timers for controlled async behavior
        setTimeout(() => cb(performance.now() + 1000), 0);
      }
      return rafCallCount;
    });

    // Mock PerformanceObserver
    // @ts-expect-error - Mocking global
    global.PerformanceObserver = vi.fn().mockImplementation((callback) => {
      mockPerformanceObserverCallback = callback;
      return {
        observe: vi.fn(),
      };
    });
    // @ts-expect-error - Mocking global
    PerformanceObserver.supportedEntryTypes = ["event"];
  });

  afterEach(() => {
    // Restore real timers and mocks
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it("should capture slow framerate exception in Sentry", async () => {
    // Import Sentry to access the mocked functions
    const Sentry = await import("@sentry/svelte");

    // Arrange
    const options = {
      lowFramerateThreshold: 30,
      slowInpThreshold: 200,
    };

    // Act
    startMonitoringPerformance(options);

    // Get the callback function passed to requestAnimationFrame
    const rafSpy = vi.mocked(window.requestAnimationFrame);
    expect(rafSpy).toHaveBeenCalled();
    const framerateMeasurementFn = rafSpy.mock.calls[0][0];

    // Simulate multiple frames to trigger framerate calculation
    // First call to establish baseline
    framerateMeasurementFn(0);

    // Advance timers to process the setTimeout in our mock
    await vi.advanceTimersByTimeAsync(10);

    // Second call after 1000ms with only 1 frame (very low FPS)
    framerateMeasurementFn(1000);

    // Advance timers to process any remaining setTimeout calls
    await vi.advanceTimersByTimeAsync(10);

    // Assert
    expect(Sentry.withScope).toHaveBeenCalled();
    expect(Sentry.captureException).toHaveBeenCalledWith(expect.any(SlowFramerateException));
  });

  it("should capture slow INP exception in Sentry", async () => {
    // Import Sentry to access the mocked functions
    const Sentry = await import("@sentry/svelte");

    // Arrange
    const options = {
      lowFramerateThreshold: 30,
      slowInpThreshold: 200,
    };

    // Act
    startMonitoringPerformance(options);

    // Verify PerformanceObserver was created
    const observerConstructor = vi.mocked(PerformanceObserver);
    expect(observerConstructor).toHaveBeenCalled();
    expect(mockPerformanceObserverCallback).toBeDefined();

    // Create mock entry list with slow INP data
    const mockEntryList = {
      getEntries: vi.fn().mockReturnValue([
        {
          interactionId: "test-interaction",
          duration: 300,
          target: "button.submit",
          startTime: 100,
        },
      ]),
    };

    // Call the callback with slow INP data
    mockPerformanceObserverCallback(mockEntryList);

    // Assert
    expect(Sentry.withScope).toHaveBeenCalled();
    expect(Sentry.captureException).toHaveBeenCalledWith(expect.any(SlowINPException));
  });
});

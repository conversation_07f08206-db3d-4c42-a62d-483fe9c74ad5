import { render, fireEvent } from "@testing-library/svelte";
import { describe, test, expect, vi } from "vitest";
import InlineEditTextAugment from "./InlineEditTextAugment.svelte";

describe("InlineEditTextAugment", () => {
  test("should not start editing on double-click when isEditable is false", async () => {
    const onChange = vi.fn();
    const onStartEdit = vi.fn();
    
    const { container } = render(InlineEditTextAugment, {
      props: {
        value: "Test Value",
        isEditable: false,
        onChange,
        onStartEdit,
      },
    });

    const textElement = container.querySelector(".c-inline-edit-text-augment__text");
    expect(textElement).toBeTruthy();

    // Simulate double-click
    await fireEvent.click(textElement!, { detail: 2 });

    // Should not start editing
    expect(onStartEdit).not.toHaveBeenCalled();
    
    // Should not show input field
    const inputField = container.querySelector(".c-inline-edit-text-augment__input");
    expect(inputField).toBeFalsy();
  });

  test("should start editing on double-click when isEditable is true", async () => {
    const onChange = vi.fn();
    const onStartEdit = vi.fn();
    
    const { container } = render(InlineEditTextAugment, {
      props: {
        value: "Test Value",
        isEditable: true,
        onChange,
        onStartEdit,
      },
    });

    const textElement = container.querySelector(".c-inline-edit-text-augment__text");
    expect(textElement).toBeTruthy();

    // Simulate double-click
    await fireEvent.click(textElement!, { detail: 2 });

    // Should start editing
    expect(onStartEdit).toHaveBeenCalled();
    
    // Should show input field
    const inputField = container.querySelector(".c-inline-edit-text-augment__input");
    expect(inputField).toBeTruthy();
  });

  test("should not start editing on double-click when disabled is true", async () => {
    const onChange = vi.fn();
    const onStartEdit = vi.fn();

    const { container } = render(InlineEditTextAugment, {
      props: {
        value: "Test Value",
        isEditable: true,
        disabled: true,
        onChange,
        onStartEdit,
      },
    });

    const textElement = container.querySelector(".c-inline-edit-text-augment__text");
    expect(textElement).toBeTruthy();

    // Simulate double-click
    await fireEvent.click(textElement!, { detail: 2 });

    // Should not start editing
    expect(onStartEdit).not.toHaveBeenCalled();

    // Should not show input field
    const inputField = container.querySelector(".c-inline-edit-text-augment__input");
    expect(inputField).toBeFalsy();
  });

  test("should respect isEditable prop changes dynamically", async () => {
    const onChange = vi.fn();
    const onStartEdit = vi.fn();

    const { container, component } = render(InlineEditTextAugment, {
      props: {
        value: "Test Value",
        isEditable: true,
        onChange,
        onStartEdit,
      },
    });

    const textElement = container.querySelector(".c-inline-edit-text-augment__text");
    expect(textElement).toBeTruthy();

    // First, verify double-click works when editable
    await fireEvent.click(textElement!, { detail: 2 });
    expect(onStartEdit).toHaveBeenCalledTimes(1);

    // Reset the component to non-editing state
    await component.$set({ isEditing: false });
    onStartEdit.mockClear();

    // Change to not editable
    await component.$set({ isEditable: false });

    // Now double-click should not work
    await fireEvent.click(textElement!, { detail: 2 });
    expect(onStartEdit).not.toHaveBeenCalled();
  });
});

<script lang="ts">
  import ClipboardCopy from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/copy.svg?component";
  import type {
    ButtonSize,
    ButtonVariant,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import SuccessfulButton from "$common-webviews/src/apps/chat/components/buttons/SuccessfulButton.svelte";
  export let size: ButtonSize = 1;
  export let variant: ButtonVariant = "ghost-block";
  export let color: ButtonColor = "neutral";
  export let text: string | (() => string | Promise<string>) | undefined = undefined;
  export let tooltip = "Copy";
  export let stickyColor = false;
  export let onCopy = async () => {
    if (text === undefined) {
      return undefined;
    }
    loading = true;
    try {
      await Promise.all([
        navigator.clipboard.writeText(typeof text === "string" ? text : await text()),
        /** give the spinner a second to spin, so that the user knows something happened*/
        wait(500),
      ]);
    } finally {
      loading = false;
    }
    return "success";
  };
  export let tooltipNested: boolean | undefined = undefined;
  let loading = false;

  function wait<T = void>(ms: number, value?: T): Promise<T> {
    return new Promise((resolve) => setTimeout(resolve, ms, value));
  }
</script>

<span class="c-copy-button">
  <SuccessfulButton
    defaultColor={color}
    {size}
    {variant}
    {loading}
    {stickyColor}
    tooltip={{ neutral: tooltip, success: "Copied!" }}
    stateVariant={{ success: "soft" }}
    onClick={onCopy}
    icon={!$$slots.text}
    {tooltipNested}
  >
    <svelte:fragment slot="iconLeft">
      {#if $$slots.icon}
        <slot name="icon" />
      {:else}
        <ClipboardCopy />
      {/if}
    </svelte:fragment>
    <slot name="text" />
  </SuccessfulButton>
</span>

<style>
  .c-copy-button {
    display: contents;
  }
</style>

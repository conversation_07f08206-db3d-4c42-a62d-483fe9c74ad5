<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import ChevronUp from "$common-webviews/src/design-system/icons/chevron-up.svelte";

  export let expanded: boolean = false;
  export let backgroundColor: string = "var(--user-theme-panel-background)";
  export let onClick: () => void;

  let hoverBackgroundColor = `oklch(from ${backgroundColor} l c h / 0.85)`;

  let className = "";
  export { className as class };
</script>

<ButtonAugment
  variant="soft"
  color="neutral"
  size={1}
  on:click={onClick}
  class="c-expand-button {className} {expanded
    ? 'c-expand-button--expanded'
    : 'c-expand-button--collapsed'}"
  style="--base-btn-bg-color: {backgroundColor}; --base-btn-hover-bg-color: {hoverBackgroundColor};"
  aria-label={expanded ? "Collapse" : "Expand"}
>
  {#if expanded}
    <ChevronUp slot="iconLeft" />
  {:else}
    <ChevronDown slot="iconLeft" />
  {/if}
</ButtonAugment>

<style>
  :global(.c-expand-button.c-base-btn) {
    width: 100%;
    flex: 1;
    border: 1px solid var(--augment-border-color);
    position: relative;
    transition: background-color 0.2s ease;
  }

  :global(.c-expand-button) :global(.c-button--content) {
    padding: 0;
  }
</style>

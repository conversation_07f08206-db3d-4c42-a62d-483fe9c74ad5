<script lang="ts">
  let clazz: string = "";
  export { clazz as class };
  export let style: string = "";
</script>

<div class="c-tooltip-container" {style}>
  <div class={`c-tooltip ${clazz}`}>
    <slot />
  </div>
</div>

<style>
  .c-tooltip-container {
    flex: 1 0 auto;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  .c-tooltip {
    overflow: hidden;
    border-width: 1px;
    border-style: solid;
    border-radius: 2px;
    border-color: var(--augment-border-color);
    background-color: var(--user-theme-sidebar-background);
  }
</style>

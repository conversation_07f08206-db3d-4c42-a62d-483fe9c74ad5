import { render } from "@testing-library/svelte";
import Filespan from "./Filespan.svelte";
import { describe, it, expect } from "vitest";

describe("Filespan", () => {
  it("should render filename and directory with normalized path", () => {
    const { container } = render(Filespan, {
      props: {
        filepath: "path\\to\\file.txt",
      },
    });

    expect(container.textContent).toContain("file.txt");
    expect(container.textContent).toContain("path/to");
  });

  it("should render only filename when nopath is true", () => {
    const { container } = render(Filespan, {
      props: {
        filepath: "path\\to\\file.txt",
        nopath: true,
      },
    });

    expect(container.textContent).toContain("file.txt");
    expect(container.textContent).not.toContain("path/to");
  });

  it("should handle paths with mixed slashes", () => {
    const { container } = render(Filespan, {
      props: {
        filepath: "path\\to/file\\name.txt",
      },
    });

    expect(container.textContent).toContain("name.txt");
    expect(container.textContent).toContain("path/to/file");
  });
});

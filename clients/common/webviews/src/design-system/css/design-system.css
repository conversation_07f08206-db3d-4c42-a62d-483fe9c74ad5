/*

This file establishes all the colors and variables used by the
design system.

Vars ending in -augment should be considered usable inside and outside the
design system.

`--ds-internal-` vars are internal variables and should not be used outside
of this file.

*/

/* Internal variables */
@import "./_ds-colors.css";
@import "./_internal.css";
@import "./ds-spacing.css";
@import "./ds-radius.css";
@import "./ds-shadow.css";
@import "./ds-typography.css";
@import "./ds-icons.css";
@import "./user-theme.css";

/* Default Colors */
:root {
  --ds-default-white: #fff;
  --ds-default-black: #1c2024;
}

/* Token colors */
:root {
  --ds-panel-translucent: var(--ds-internal-vars-effects-translucent);

  --ds-panel-default: var(--ds-panel-translucent);

  --ds-surface: var(--ds-internal-vars-misc-white-to-dark-2);
  /* This should probably use the data-ds-color="accent" and --ds-color-a2
  value instead. */
  /* --ds-accent-surface: var(--ds-internal-accent-2a); */

  --ds-overlay: var(--ds-internal-vars-misc-backdrop);
  --ds-accent-contrast: var(--ds-white-contrast);
  --ds-white-contrast: var(--ds-default-white);
  --ds-black-contrast: var(--ds-default-black);
  --ds-text: var(--ds-internal-vars-misc-dark-to-white);
}

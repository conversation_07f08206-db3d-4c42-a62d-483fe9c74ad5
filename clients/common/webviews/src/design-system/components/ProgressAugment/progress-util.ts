export type ProgressVariant = "soft" | "surface" | "outline";
export type ProgressColor = "accent" | "neutral" | "error" | "success" | "warning" | "info";
export const PROGRESS_NAME = "ProgressAugment";
export const DEFAULT_MAX = 100;
export type ProgressState = "indeterminate" | "complete" | "loading";
export type ProgressSize = 1 | 2 | 3;

export function defaultGetValueLabel(value: number, max: number) {
  return `${Math.round((value / max) * 100)}%`;
}

export function getProgressState(
  value: number | undefined | null,
  maxValue: number,
): ProgressState {
  return value == null ? "indeterminate" : value === maxValue ? "complete" : "loading";
}

export function isNumber(value: unknown): value is number {
  return typeof value === "number";
}

export function isValidMaxNumber(max: unknown): max is number {
  // prettier-ignore
  return (
      isNumber(max) &&
      !isNaN(max) &&
      max > 0
    );
}

export function isValidValueNumber(value: unknown, max: number): value is number {
  // prettier-ignore
  return (
      isNumber(value) &&
      !isNaN(value) &&
      value <= max &&
      value >= 0
    );
}

// Split this out for clearer readability of the error message.
export function getInvalidMaxError(propValue: string, componentName: string) {
  return `Invalid prop \`max\` of value \`${propValue}\` supplied to \`${componentName}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${DEFAULT_MAX}\`.`;
}

export function getInvalidValueError(propValue: string, componentName: string) {
  return `Invalid prop \`value\` of value \`${propValue}\` supplied to \`${componentName}\`. The \`value\` prop must be:
    - a positive number
    - less than the value passed to \`max\` (or ${DEFAULT_MAX} if no \`max\` prop is set)
    - \`null\` or \`undefined\` if the progress is indeterminate.

  Defaulting to \`null\`.`;
}

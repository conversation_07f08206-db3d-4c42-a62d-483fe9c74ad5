<script lang="ts">
  import type {
    ButtonSize,
    ButtonVariant,
    ButtonColor,
    ButtonRadius,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import type { SplitButtonDisableState } from "./types";
  import ButtonAugment from "../ButtonAugment.svelte";
  import IconButtonAugment from "../IconButtonAugment.svelte";
  import DropdownMenuAugment from "../DropdownMenuAugment";
  import type { ContentSize } from "../DropdownMenuAugment/Content.svelte";
  import SeparatorAugment from "../SeparatorAugment.svelte";
  import ChevronDown from "../../icons/chevron-down.svelte";

  // Button styling props
  export let size: ButtonSize = 2;
  export let variant: ButtonVariant = "solid";
  export let color: ButtonColor = "neutral";
  export let radius: ButtonRadius = "medium";
  export let disabled: SplitButtonDisableState = {
    primaryDisabled: false,
    dropdownDisabled: false,
  };
  export let highContrast: boolean = false;

  // Compute individual disable states from the new type
  $: primaryDisabled = typeof disabled === "boolean" ? disabled : disabled.primaryDisabled;
  $: dropdownDisabled = typeof disabled === "boolean" ? disabled : disabled.dropdownDisabled;
  // Additional styling props
  let className: string = "";
  export { className as class };

  // Dropdown props
  export let showDropdown: boolean = true;
  export let dropdownSide: "top" | "bottom" | "left" | "right" = "bottom";
  export let dropdownAlign: "start" | "center" | "end" = "end";
  export let onDropdownOpenChange: ((open: boolean) => void) | undefined = undefined;
  export let onDropdownClickOutside: (() => void) | undefined = undefined;
  export let onDropdownEscapeKeyDown: (() => void) | undefined = undefined;

  // Dropdown icon props
  export let dropdownIcon: "chevron" | "custom" = "chevron";
  export let dropdownIconSize: ButtonSize = size;

  // Convert ButtonSize to ContentSize for dropdown content
  $: dropdownContentSize = (dropdownIconSize > 2 ? 2 : dropdownIconSize) as ContentSize;

  // Dropdown control reference
  let dropdownRoot: { requestClose: () => void; requestOpen: () => void } | undefined;

  // Public API for external controls
  export const requestCloseDropdown = () => dropdownRoot?.requestClose();
  export const requestOpenDropdown = () => dropdownRoot?.requestOpen();

  // Handle dropdown state changes
  function handleDropdownOpenChange(open: boolean) {
    if (onDropdownOpenChange) {
      onDropdownOpenChange(open);
    }
  }

  function handleDropdownClickOutside() {
    if (onDropdownClickOutside) {
      onDropdownClickOutside();
    }
  }

  function handleDropdownEscapeKeyDown() {
    if (onDropdownEscapeKeyDown) {
      onDropdownEscapeKeyDown();
    }
  }

  function handleDropdownContentClick() {
    requestCloseDropdown();
  }

  // Generate unique data attributes for styling
  const mainButtonDataAttr = "data-split-button-main";
  const dropdownButtonDataAttr = "data-split-button-dropdown";
</script>

<div
  class="c-split-button c-split-button--size-{size} {className}"
  class:c-split-button--has-dropdown={showDropdown}
>
  <!-- Main button slot -->
  <div class="c-split-button__main">
    <slot
      name="button"
      {mainButtonDataAttr}
      {size}
      {variant}
      {color}
      {radius}
      disabled={primaryDisabled}
      {highContrast}
    >
      <ButtonAugment
        {size}
        {variant}
        {color}
        {radius}
        disabled={primaryDisabled}
        {highContrast}
        data-split-button-main={showDropdown ? "true" : undefined}
        on:click
        on:keyup
        on:keydown
        on:mousedown
        on:mouseover
        on:focus
        on:mouseleave
        on:blur
        on:contextmenu
        {...$$restProps}
      >
        <slot name="iconLeft" slot="iconLeft" />
        <slot />
      </ButtonAugment>
    </slot>
  </div>

  <!-- Separator between button and dropdown -->
  {#if showDropdown}
    <div class="c-split-button__separator">
      <SeparatorAugment size={4} orientation="vertical" />
    </div>
  {/if}

  <!-- Dropdown section -->
  {#if showDropdown}
    <DropdownMenuAugment.Root bind:this={dropdownRoot} onOpenChange={handleDropdownOpenChange}>
      <DropdownMenuAugment.Trigger>
        <slot
          name="dropdown-trigger"
          {dropdownButtonDataAttr}
          {dropdownIconSize}
          {variant}
          {color}
          disabled={dropdownDisabled}
          {radius}
        >
          <IconButtonAugment
            size={dropdownIconSize}
            variant={primaryDisabled ? "soft" : variant}
            {color}
            disabled={dropdownDisabled}
            {radius}
            data-split-button-dropdown="true"
          >
            <slot name="dropdown-icon">
              {#if dropdownIcon === "chevron"}
                <ChevronDown />
              {:else}
                <slot name="custom-dropdown-icon" />
              {/if}
            </slot>
          </IconButtonAugment>
        </slot>
      </DropdownMenuAugment.Trigger>

      <DropdownMenuAugment.Content
        size={dropdownContentSize}
        side={dropdownSide}
        align={dropdownAlign}
        onClickOutside={handleDropdownClickOutside}
        onEscapeKeyDown={handleDropdownEscapeKeyDown}
      >
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <!-- svelte-ignore a11y-no-static-element-interactions -->
        <div on:click={handleDropdownContentClick} class="c-split-button__dropdown-contents">
          <slot name="dropdown-content" closeDropdown={requestCloseDropdown} />
        </div>
      </DropdownMenuAugment.Content>
    </DropdownMenuAugment.Root>
  {/if}
</div>

<style>
  .c-split-button {
    display: inline-flex;
    flex-direction: row;
    --split-button-height: var(--icon-btn-size);
  }

  .c-split-button__main {
    display: contents;
  }

  .c-split-button__separator {
    display: flex;
    align-items: center;
    align-self: stretch;
    margin: 0 -1px; /* Overlap borders to prevent double borders */
    z-index: 1;
  }

  /* Remove right border radius from main button when dropdown is present */
  .c-split-button--has-dropdown :global([data-split-button-main="true"]) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    /* Ensure main button matches dropdown button height */
    height: var(--split-button-height);
    min-height: var(--split-button-height);
  }

  /* Remove left border radius from dropdown button when dropdown is present */
  .c-split-button--has-dropdown :global([data-split-button-dropdown="true"]) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .c-split-button__dropdown-contents {
    display: contents;
  }

  /* Size-specific height variables to match IconButtonAugment */
  .c-split-button--size-1 {
    --icon-btn-size: var(--ds-spacing-5); /* 24px */
  }

  .c-split-button--size-2 {
    --icon-btn-size: var(--ds-spacing-6); /* 32px */
  }

  .c-split-button--size-3 {
    --icon-btn-size: var(--ds-spacing-7); /* 40px */
  }

  .c-split-button--size-4 {
    --icon-btn-size: var(--ds-spacing-8); /* 48px */
  }
</style>

<script lang="ts">
  import TextAugment from "./TextAugment.svelte";
  import type {
    ButtonColor,
    ButtonSize,
    ButtonVariant,
    ButtonAlignment,
    ButtonRadius,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import BaseButton from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";

  export let size: ButtonSize = 2;
  export let variant: ButtonVariant = "solid";
  export let color: ButtonColor = "neutral";
  export let highContrast: boolean = false;
  export let disabled: boolean = false;
  export let radius: ButtonRadius = "medium";
  export let loading: boolean = false;
  export let alignment: ButtonAlignment = "center";
</script>

<BaseButton
  {size}
  {variant}
  {color}
  {highContrast}
  {disabled}
  {loading}
  {alignment}
  {radius}
  on:click
  on:keyup
  on:keydown
  on:mousedown
  on:mouseover
  on:focus
  on:mouseleave
  on:blur
  on:contextmenu
  {...$$restProps}
>
  <div class={`c-button--content c-button--size-${size}`}>
    {#if $$slots.iconLeft}
      <div class="c-button--icon">
        <slot name="iconLeft" />
      </div>
    {/if}
    {#if $$slots.default}
      <div class="c-button--text">
        <TextAugment
          size={size === 0.5 ? 1 : size}
          weight={variant === "ghost" ? "regular" : "medium"}
        >
          <slot />
        </TextAugment>
      </div>
    {/if}
    {#if $$slots.iconRight}
      <div class="c-button--icon">
        <slot name="iconRight" />
      </div>
    {/if}
  </div>
</BaseButton>

<style>
  /* This is for the icon container */

  .c-button--icon :global(svg),
  .c-button--content :global(svg) {
    width: var(--button-icon-size);
    height: var(--button-icon-size);
    aspect-ratio: 1/1;
    display: block;
  }
  .c-button--icon :global(svg) {
    fill: var(--icon-color, currentColor);
  }

  .c-button--content {
    --button-icon-size: 16px;

    display: inline-flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    vertical-align: top;

    padding: var(--base-btn-padding-vertical) var(--base-btn-padding-horizontal);
    gap: var(--base-btn-gap);
  }

  .c-button--icon {
    display: flex;
    align-items: center;
    justify-content: center;

    /* Hide empty icon containers */
    &:not(:has(> *)) {
      display: contents;
    }
  }

  .c-button--text :global(.c-text) {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
  }

  .c-button--size-3 {
    --button-icon-size: 18px;
  }

  .c-button--size-4 {
    --button-icon-size: 20px;
  }
</style>

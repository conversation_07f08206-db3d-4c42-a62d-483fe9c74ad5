import { describe, it, expect, vi, beforeEach } from "vitest";
import { ExtensionClient } from "../../../apps/chat/extension-client";
import { ExchangeStatus } from "../../../apps/chat/types/chat-message";
import { ChatStreamWithRetry } from "../../../apps/chat/chat-stream-with-retry";

describe("ExtensionClient", () => {
  let mockHost: any;
  let mockAsyncMsgSender: any;
  let mockFlags: any;
  let extensionClient: ExtensionClient;

  beforeEach(() => {
    mockHost = {
      postMessage: vi.fn(),
    };

    mockAsyncMsgSender = {
      send: vi.fn().mockResolvedValue({
        data: {
          sources: [{ id: "doc1", name: "Documentation 1" }],
        },
      }),
      stream: vi.fn(),
    };

    mockFlags = {
      enableExternalSourcesInChat: true,
      enableAgentMode: false,
      enablePersonalities: true,
      enableRules: true,
    };

    extensionClient = new ExtensionClient(mockHost, mockAsyncMsgSender, mockFlags);
  });

  describe("getSuggestions", () => {
    it("should pass isAgentMode parameter to findExternalSources", async () => {
      // Spy on findExternalSources
      const findExternalSourcesSpy = vi.spyOn(extensionClient, "findExternalSources");

      // Mock other methods to return empty arrays
      extensionClient.findFiles = vi.fn().mockResolvedValue([]);
      extensionClient.findRecentlyOpenedFiles = vi.fn().mockResolvedValue([]);
      extensionClient.findFolders = vi.fn().mockResolvedValue([]);
      extensionClient.findRules = vi.fn().mockResolvedValue([]);

      // Call getSuggestions with isAgentMode = true
      await extensionClient.getSuggestions("query", true);

      // Verify findExternalSources was called with isAgentMode = true
      expect(findExternalSourcesSpy).toHaveBeenCalledWith("query", true);

      // Call getSuggestions with isAgentMode = false
      await extensionClient.getSuggestions("query", false);
    });

    it("should include personalities when query matches keywords in description", async () => {
      // Mock the required methods
      extensionClient.findFiles = vi.fn().mockResolvedValue([]);
      extensionClient.findRecentlyOpenedFiles = vi.fn().mockResolvedValue([]);
      extensionClient.findFolders = vi.fn().mockResolvedValue([]);
      extensionClient.findExternalSources = vi.fn().mockResolvedValue([]);
      extensionClient.findRules = vi.fn().mockResolvedValue([]);

      // Test with a keyword that should match the default personality
      const results1 = await extensionClient.getSuggestions("expert", false);
      expect(results1.some((item) => item.id === "auggie-personality-agent-default")).toBe(true);

      // Test with a keyword that should match the prototyper personality
      const results2 = await extensionClient.getSuggestions("prototype", false);
      expect(results2.some((item) => item.id === "auggie-personality-prototyper")).toBe(true);

      // Test with a keyword that should match both personalities
      const results3 = await extensionClient.getSuggestions("engineer", false);
      expect(
        results3.filter((item) => item.id.includes("auggie-personality")).length,
      ).toBeGreaterThan(0);

      // Test with a non-matching keyword
      const results4 = await extensionClient.getSuggestions("nonexistentkeyword", false);
      expect(results4.some((item) => item.id.includes("auggie-personality"))).toBe(false);
    });

    it("should filter personalities based on keyword matches", () => {
      // Test with empty query (should return all personalities)
      const results1 = extensionClient.getPersonalities("");
      expect(results1.length).toBeGreaterThan(0);

      // Test with a keyword that should match the default personality
      const results2 = extensionClient.getPersonalities("expert");
      expect(results2.some((item) => item.id === "auggie-personality-agent-default")).toBe(true);

      // Test with a keyword that should match the prototyper personality
      const results3 = extensionClient.getPersonalities("prototype");
      expect(results3.some((item) => item.id === "auggie-personality-prototyper")).toBe(true);

      // Test with a non-matching keyword
      const results4 = extensionClient.getPersonalities("nonexistentkeyword");
      expect(results4.length).toBe(0);
    });

    it("should not return personalities when enablePersonalities flag is false", async () => {
      // Create a new extension client with enablePersonalities set to false
      const mockFlagsWithoutPersonalities = {
        ...mockFlags,
        enablePersonalities: false,
      };
      const clientWithoutPersonalities = new ExtensionClient(
        mockHost,
        mockAsyncMsgSender,
        mockFlagsWithoutPersonalities,
      );

      // Mock the required methods
      clientWithoutPersonalities.findFiles = vi.fn().mockResolvedValue([]);
      clientWithoutPersonalities.findRecentlyOpenedFiles = vi.fn().mockResolvedValue([]);
      clientWithoutPersonalities.findFolders = vi.fn().mockResolvedValue([]);
      clientWithoutPersonalities.findExternalSources = vi.fn().mockResolvedValue([]);
      clientWithoutPersonalities.findRules = vi.fn().mockResolvedValue([]);

      // Test with a keyword that would normally match personalities
      const results = await clientWithoutPersonalities.getSuggestions("expert", false);
      expect(results.some((item) => item.id.includes("auggie-personality"))).toBe(false);

      // Test getPersonalities directly
      const personalities = clientWithoutPersonalities.getPersonalities("");
      expect(personalities.length).toBe(0);
    });
  });

  describe("findExternalSources", () => {
    it("should return external sources when enableExternalSourcesInChat is true and not in agent mode", async () => {
      mockFlags.enableExternalSourcesInChat = true;

      const result = await extensionClient.findExternalSources("query", false);

      expect(mockAsyncMsgSender.send).toHaveBeenCalled();
      expect(result).toEqual([{ id: "doc1", name: "Documentation 1" }]);
    });

    it("should return empty array when enableExternalSourcesInChat is false", async () => {
      mockFlags.enableExternalSourcesInChat = false;

      const result = await extensionClient.findExternalSources("query", false);

      expect(mockAsyncMsgSender.send).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it("should return empty array when in agent mode regardless of enableAgentMode flag", async () => {
      mockFlags.enableExternalSourcesInChat = true;
      mockFlags.enableAgentMode = false; // Even with the flag disabled

      const result = await extensionClient.findExternalSources("query", true);

      expect(mockAsyncMsgSender.send).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });

  describe("findRules", () => {
    it("should return rules when enableRules is true", async () => {
      mockFlags.enableRules = true;
      mockAsyncMsgSender.send.mockResolvedValue({
        type: "get-rules-list-response",
        data: [
          { path: ".augment/rules/test-rule.md", content: "# Test Rule", type: "MANUAL" },
          {
            path: ".augment/rules/another-rule.md",
            content: "# Another Rule",
            type: "ALWAYS_ATTACHED",
          },
        ],
      });

      const result = await extensionClient.findRules("test", 10);

      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        {
          type: "get-rules-list-request",
          data: { query: "test", maxResults: 10 },
        },
        5000,
      );
      expect(result).toEqual([
        { path: ".augment/rules/test-rule.md", content: "# Test Rule", type: "MANUAL" },
        {
          path: ".augment/rules/another-rule.md",
          content: "# Another Rule",
          type: "ALWAYS_ATTACHED",
        },
      ]);
    });

    it("should return empty array when enableRules is false", async () => {
      mockFlags.enableRules = false;

      const result = await extensionClient.findRules("test");

      expect(mockAsyncMsgSender.send).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it("should use default limit when not specified", async () => {
      mockFlags.enableRules = true;
      mockAsyncMsgSender.send.mockResolvedValue({
        type: "get-rules-list-response",
        data: [],
      });

      await extensionClient.findRules("test");

      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        {
          type: "get-rules-list-request",
          data: { query: "test", maxResults: 12 },
        },
        5000,
      );
    });
  });

  describe("startChatStreamWithRetry", () => {
    it("should create a retriable stream and store it in _activeRetryStreams", async () => {
      // Mock the startChatStream method to return a simple generator
      const mockGenerator = (async function* () {
        yield { status: ExchangeStatus.sent };
        yield { status: ExchangeStatus.success };
      })();
      const startChatStreamSpy = vi
        .spyOn(extensionClient, "startChatStream")
        .mockImplementation(() => mockGenerator);

      // Call the method
      const requestId = "test-request-id";
      const chatMessage = { message: "test message" } as any;
      const generator = extensionClient.startChatStreamWithRetry(requestId, chatMessage);

      // Get the first chunk to ensure the stream is created and stored
      const iterator = generator[Symbol.asyncIterator]();
      const firstChunk = await iterator.next();

      // Consume the rest of the generator
      const chunks = [firstChunk.value];
      for await (const chunk of { [Symbol.asyncIterator]: () => iterator }) {
        chunks.push(chunk);
      }

      // Verify we got chunks from the stream
      expect(chunks.length).toBeGreaterThan(0);

      // Verify at least one chunk has the sent status
      expect(chunks.some((chunk) => chunk.status === ExchangeStatus.sent)).toBe(true);

      // Verify the last chunk has the success status
      const lastChunk = chunks[chunks.length - 1];
      expect(lastChunk.status).toBe(ExchangeStatus.success);

      // Restore the spy
      startChatStreamSpy.mockRestore();
    });
  });

  describe("cancelChatStream", () => {
    it("should handle various cancellation scenarios correctly", async () => {
      // The strategy is to create two active streams, and then verify that:
      // * both streams are tracked and cleaned up properly
      // * an individual stream can be cancelled

      // Mock the startChatStream method to return a simple generator
      const mockGenerator = (async function* () {
        yield { status: ExchangeStatus.sent };
        yield { status: ExchangeStatus.success };
      })();
      const startChatStreamSpy = vi
        .spyOn(extensionClient, "startChatStream")
        .mockImplementation(() => mockGenerator);

      // Create two active streams.
      // We iterate once through the generaterators to ensure the streams are created and stored.
      const requestId1 = "request-id-1";
      const chatMessage1 = { message: "test message 1" } as any;
      const generator1 = extensionClient.startChatStreamWithRetry(requestId1, chatMessage1);
      const iterator1 = generator1[Symbol.asyncIterator]();
      await iterator1.next();

      const requestId2 = "request-id-2";
      const chatMessage2 = { message: "test message 2" } as any;
      const generator2 = extensionClient.startChatStreamWithRetry(requestId2, chatMessage2);
      const iterator2 = generator2[Symbol.asyncIterator]();
      await iterator2.next();

      // Access the ChatStreamWithRetry instances from the _activeRetryStreams map
      // This was the easiest way to find the internal streams, but does reach
      // into internal implementation details.
      // @ts-expect-error - Accessing private property for testing
      const chatStream1 = extensionClient._activeRetryStreams.get(requestId1);
      expect(chatStream1).toBeInstanceOf(ChatStreamWithRetry);
      // @ts-expect-error - Accessing private property for testing
      const chatStream2 = extensionClient._activeRetryStreams.get(requestId2);
      expect(chatStream2).toBeInstanceOf(ChatStreamWithRetry);

      // Spy on both cancel methods.
      const cancelSpy1 = vi.spyOn(chatStream1 as ChatStreamWithRetry, "cancel");
      const cancelSpy2 = vi.spyOn(chatStream2 as ChatStreamWithRetry, "cancel");

      // Test cancellation of the first active stream
      await extensionClient.cancelChatStream(requestId1);

      // Verify only the first stream's cancel method was called
      expect(cancelSpy1).toHaveBeenCalled();
      expect(cancelSpy2).not.toHaveBeenCalled();

      // Verify the cancellation message was sent with the correct request ID
      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: expect.any(String),
          data: { requestId: requestId1 },
        }),
        10000,
      );

      // Reset the mock to track new calls
      mockAsyncMsgSender.send.mockClear();
      cancelSpy1.mockClear();
      cancelSpy2.mockClear();

      // Test cancellation of a non-existent stream
      const nonExistentId = "non-existent-id";
      await extensionClient.cancelChatStream(nonExistentId);

      // Verify no cancel methods were called
      expect(cancelSpy1).not.toHaveBeenCalled();
      expect(cancelSpy2).not.toHaveBeenCalled();

      // Verify the cancellation message was still sent
      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: expect.any(String),
          data: { requestId: nonExistentId },
        }),
        10000,
      );

      // Reset the mock to track new calls
      mockAsyncMsgSender.send.mockClear();

      // Complete request1 and test if we can cancel it.
      for await (const _ of { [Symbol.asyncIterator]: () => iterator1 }) {
        void _;
      }
      await extensionClient.cancelChatStream(requestId1);

      expect(cancelSpy1).not.toHaveBeenCalled();
      expect(cancelSpy2).not.toHaveBeenCalled();
      expect(mockAsyncMsgSender.send).toHaveBeenCalledWith(
        expect.objectContaining({
          type: expect.any(String),
          data: { requestId: requestId1 },
        }),
        10000,
      );

      // Restore the spies
      startChatStreamSpy.mockRestore();
      cancelSpy1.mockRestore();
      cancelSpy2.mockRestore();

      // Complete request 2
      for await (const _ of { [Symbol.asyncIterator]: () => iterator2 }) {
        void _;
      }

      // Ensure the requests are no longer tracked.
      // @ts-expect-error - Accessing private property for testing
      expect(extensionClient._activeRetryStreams.has(requestId1)).toBe(false);
      // @ts-expect-error - Accessing private property for testing
      expect(extensionClient._activeRetryStreams.has(requestId2)).toBe(false);
    });
  });
});

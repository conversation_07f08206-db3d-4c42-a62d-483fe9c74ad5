<script lang="ts" context="module">
  export const NEW_THREADS_BUTTON_TEST_ID = "new-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_CHAT_TEST_ID = "new-chat-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_LOCAL_AGENT_MANUAL_TEST_ID =
    "new-local-agent-manual-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_LOCAL_AGENT_TEST_ID = "new-local-agent-thread-button";
  export const NEW_THREAD_TYPE_BUTTON_REMOTE_AGENT_TEST_ID = "new-remote-agent-thread-button";
</script>

<script lang="ts">
  import RegularChatIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message.svg?component";
  import ModeDisplayDropdownItem from "$common-webviews/src/apps/chat/components/buttons/ModeDisplayDropdownItem.svelte";
  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import SplitButtonAugment from "$common-webviews/src/design-system/components/SplitButtonAugment";
  import KeyboardShortcutHint from "$common-webviews/src/common/components/KeyboardShortcutHint.svelte";
  import { PlatformKeyIcon } from "$common-webviews/src/common/components/key-icons";
  import { getContext } from "svelte";
  import { getModeDisplayInfo } from "$common-webviews/src/apps/chat/components/buttons/mode-display-helper";
  import type { AgentConversationModel } from "$common-webviews/src/apps/chat/models/agent-conversation-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  // Get models from context
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);
  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");

  // Determine if background agents are enabled
  $: enableBackgroundAgents = chatModel ? $chatModel.flags.enableBackgroundAgents : false;
  $: enableAgentMode = chatModel ? $chatModel.flags.enableAgentMode : false;

  // Access store properties safely with proper reactive declarations
  $: isCurrConversationAgentic = agentConversationModel?.isCurrConversationAgentic;
  $: agentExecutionMode = chatModel?.agentExecutionMode;

  // Get current mode info for the main button icon with fallback values
  $: currentModeInfo = getModeDisplayInfo({
    isConversationAgentic: isCurrConversationAgentic ? $isCurrConversationAgentic : false,
    agentExecutionMode: agentExecutionMode ? $agentExecutionMode : undefined,
    isBackgroundAgent: remoteAgentsModel ? $remoteAgentsModel.isActive : false,
  });
  // Get tooltip text based on current mode
  $: tooltipText =
    currentModeInfo.type === "remoteAgent"
      ? "New Remote Agent"
      : currentModeInfo.type === "localAgent"
        ? "New Agent"
        : "New Chat";
</script>

{#if enableAgentMode || enableBackgroundAgents}
  <div class="new-thread-dropdown">
    <SplitButtonAugment.Root
      variant="soft"
      color="neutral"
      radius="medium"
      size={1}
      dropdownSide="bottom"
      dropdownAlign="end"
      class="c-agent-type-dropdown__button"
    >
      <!-- Manually create this tooltip that wraps a button and insert it into the "button" slot -->
      <TextTooltipAugment slot="button">
        <div slot="content" class="c-new-thread-dropdown__tooltip-content">
          <span>{tooltipText}</span>
          <KeyboardShortcutHint icons={[`${PlatformKeyIcon.cmdOrCtrl}`, "L"]} />
        </div>
        <ButtonAugment
          size={1}
          variant="soft"
          color="neutral"
          data-testid={NEW_THREADS_BUTTON_TEST_ID}
          data-split-button-main
          on:click={() => chatModeModel.createThreadOfCurrentType()}
        >
          <svelte:component this={currentModeInfo.icon} slot="iconLeft" />
          New
        </ButtonAugment>
      </TextTooltipAugment>

      <svelte:fragment slot="dropdown-content">
        <ModeDisplayDropdownItem
          onSelect={() => chatModeModel.createNewChatThread()}
          isConversationAgentic={false}
          tooltipSide="right"
          tooltipContent="Ask Augment"
          data-testid={NEW_THREAD_TYPE_BUTTON_CHAT_TEST_ID}
        />
        {#if enableAgentMode}
          <ModeDisplayDropdownItem
            onSelect={() => chatModeModel.createNewLocalAgentThread($agentExecutionMode)}
            isConversationAgentic={true}
            tooltipSide="right"
            tooltipContent="Require approval for some tools"
            data-testid={NEW_THREAD_TYPE_BUTTON_LOCAL_AGENT_MANUAL_TEST_ID}
          />
        {/if}
        {#if enableBackgroundAgents}
          <ModeDisplayDropdownItem
            onSelect={() => chatModeModel.createNewRemoteAgentThread()}
            isBackgroundAgent={true}
            tooltipSide="right"
            tooltipContent="Run background agents in the cloud"
            data-testid={NEW_THREAD_TYPE_BUTTON_REMOTE_AGENT_TEST_ID}
          />
        {/if}
      </svelte:fragment>
    </SplitButtonAugment.Root>
  </div>
{:else}
  <ButtonAugment
    size={1}
    variant="soft"
    color="neutral"
    data-testid={NEW_THREADS_BUTTON_TEST_ID}
    on:click={() => chatModeModel.createNewChatThread()}
  >
    <RegularChatIcon slot="iconLeft" />
    New
  </ButtonAugment>
{/if}

<style>
  .c-new-thread-dropdown__tooltip-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .c-new-thread-dropdown__tooltip-content :global(.c-keyboard-shortcut-hint__icon) {
    color: var(--gray-1);
    border-color: var(--gray-1);
  }
  .new-thread-dropdown :global(.new-thread-dropdown__button--localAgent svg) {
    /* make sure the lines are crisp */
    image-rendering: -webkit-optimize-contrast;
    width: 13px;
    margin-left: 1.5px;
    margin-right: 3px;
  }
  .new-thread-dropdown :global(.new-thread-dropdown__button--chat svg) {
    /* make sure the lines are crisp */
    image-rendering: -webkit-optimize-contrast;
    width: 13px;
    margin-right: 3px;
    margin-left: 1.5px;
  }

  .new-thread-dropdown :global(.new-thread-dropdown__button--remoteAgent svg) {
    /* make sure the lines are crisp */
    image-rendering: -webkit-optimize-contrast;
    width: 16px;
    margin-right: 1.5px;
  }

  /* Set icon size to 14px for new thread dropdown */
  :global(.c-agent-type-dropdown__button svg) {
    --icon-size: var(--ds-icon-size-0);
    --button-icon-size: var(--ds-icon-size-0);
  }
</style>

<script lang="ts">
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Terminal from "$common-webviews/src/design-system/icons/augment/terminal.svelte";
  import Branch from "$common-webviews/src/design-system/icons/branch.svelte";
  import RegularCloudIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud-arrow-up.svg?component";
  import Github from "$common-webviews/src/design-system/icons/github.svelte";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "../../../remote-agent-manager/models/remote-agents-model";
  import { getOrgRepoFromUrl } from "../../../remote-agent-manager/utils/repository-utils";
  import AgentOptionsMenu from "../menu/AgentOptionsMenu.svelte";
  import BaseThreadHeader from "./BaseThreadHeader.svelte";

  let windowHeight = 0;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  $: currentAgent = $remoteAgentsModel?.currentAgent;
  $: agentId = currentAgent?.remote_agent_id;
  $: repoUrl =
    currentAgent?.workspace_setup?.starting_files?.github_commit_ref?.repository_url || "";
  $: repoName = getOrgRepoFromUrl(repoUrl);
  $: title = currentAgent?.session_summary || "";
  $: isSetupScriptAgent = currentAgent?.is_setup_script_agent || false;
  $: isPinned = agentId ? $remoteAgentsModel?.pinnedAgents[agentId] || false : false;
  $: isRemoteAgentSshWindow = $remoteAgentsModel?.isRemoteAgentSshWindow;

  // Function to toggle the pinned state
  function togglePinned() {
    if (!agentId) return;
    remoteAgentsModel?.toggleAgentPinned(agentId, isPinned);
  }

  $: selectedBranch = currentAgent?.workspace_setup?.starting_files?.github_commit_ref?.git_ref;
</script>

<svelte:window bind:innerHeight={windowHeight} />

{#if currentAgent}
  <div
    class="c-agent-header"
    class:c-agent-header--setup-script={isSetupScriptAgent}
    class:is-in-remote={isRemoteAgentSshWindow}
  >
    <BaseThreadHeader
      title={isSetupScriptAgent ? "Generate a setup script" : title}
      isEditable={false}
    >
      <RegularCloudIcon slot="icon" />
      <svelte:fragment slot="banner">
        {#if isRemoteAgentSshWindow}
          <div class="c-agent-header-banner__with-icon">
            <RegularCloudIcon />
            <div class="c-agent-header-banner__stack">
              <div class="c-agent-header-banner__text">Running in the cloud</div>
              <div class="c-agent-header-banner__text c-agent-header-banner__text--secondary">
                Workspace opened via SSH in this VSCode window
              </div>
            </div>
          </div>
        {:else if currentAgent?.is_setup_script_agent}
          <span class="c-agent-header-banner__text">
            <div class="c-setup-script-badge">
              <Terminal />
            </div>
            Generate a setup script
          </span>
        {/if}
        <div class="c-agent-header__actions">
          {#if agentId && (isRemoteAgentSshWindow || currentAgent?.is_setup_script_agent)}
            <AgentOptionsMenu {agentId} {selectedBranch} {isPinned} {togglePinned} />
          {/if}
        </div>
      </svelte:fragment>

      <div slot="title">
        {#if isSetupScriptAgent}
          <div class="c-agent-title-content">
            <TextAugment size={1} truncate weight="medium">
              We've tasked an agent to generate a setup script for your repository.<span
                class="c-agent-title-content__long-text-alternative">..</span
              >
              <span class="c-agent-title-content__long-text">
                Once created, save it to use in a new remote agent, to install dependencies &
                configure the env before the agent starts.
              </span>
            </TextAugment>
          </div>
        {:else}
          <div class="c-agent-user-message" title={currentAgent?.session_summary || ""}>
            <TextAugment size={2}>{currentAgent?.session_summary}</TextAugment>
          </div>
        {/if}
      </div>

      <div slot="title-actions" class="c-agent-header__menu">
        {#if agentId && !(isRemoteAgentSshWindow || currentAgent?.is_setup_script_agent)}
          <AgentOptionsMenu {agentId} {selectedBranch} {isPinned} {togglePinned} />
        {/if}
      </div>

      <div slot="info-row" class="c-agent-info-row">
        <BadgeAugment.Root color="neutral" radius="full">
          <a href={repoUrl} class="c-agent-info-item">
            <Github />
            <TextTooltipAugment
              content={`Repository: ${repoUrl}`}
              triggerOn={[TooltipTriggerOn.Hover]}
            >
              <div class="c-agent-info-value">
                <TextAugment size={1}>
                  {repoName}
                </TextAugment>
              </div>
            </TextTooltipAugment>
          </a>
        </BadgeAugment.Root>
        <BadgeAugment.Root color="neutral" radius="full">
          <a href={`${repoUrl}/tree/${selectedBranch}`} class="c-agent-info-item">
            <Branch />
            <TextTooltipAugment
              content={`Started on branch: ${selectedBranch}`}
              triggerOn={[TooltipTriggerOn.Hover]}
            >
              <div class="c-agent-info-value">
                <TextAugment size={1}>
                  {selectedBranch}
                </TextAugment>
              </div>
            </TextTooltipAugment>
          </a>
        </BadgeAugment.Root>
      </div>
    </BaseThreadHeader>
  </div>
{/if}

<style lang="css">
  .c-agent-header {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .c-agent-header-banner__with-icon {
    display: flex;
    align-items: center;
    gap: 9px;
  }

  .is-in-remote :global(.c-thread-header-banner) {
    background-color: #0090ff;
    color: #fff;
  }

  .c-agent-header--setup-script :global(.c-thread-header-banner) {
    background-color: var(--ds-color-success-5);
    color: var(--ds-color-success-11);
  }

  .c-agent-header--setup-script .c-agent-title-content {
    color: var(--ds-color-success-11);
  }
  .c-agent-header--setup-script :global(.c-thread-title) {
    background-color: var(--ds-color-success-3);
    line-clamp: 5;
    -webkit-line-clamp: 5;
  }

  .c-agent-header--setup-script .c-agent-title-content__long-text {
    display: none;
    color: var(--ds-color-success-11);
    font-weight: 300;
  }
  .c-agent-header--setup-script .c-agent-title-content__long-text-alternative {
    display: inline;
  }
  .c-agent-header:hover .c-agent-title-content__long-text {
    display: inline;
  }
  .c-agent-header:hover .c-agent-title-content__long-text-alternative {
    display: none;
  }
  @media (min-height: 700px) {
    .c-agent-header--setup-script .c-agent-title-content__long-text {
      display: inline;
    }
    .c-agent-header--setup-script .c-agent-title-content__long-text-alternative {
      display: none;
    }
  }

  .c-agent-header-banner__stack {
    padding: 0.6em 0;
  }

  .c-agent-header-banner__text {
    line-height: 1.5em;
    display: flex;
    align-items: center;
    gap: 0.5em;
  }

  .c-agent-header-banner__text--secondary {
    color: var(--ds-color-accent-9);
  }

  @media (max-width: 440px) {
    .c-agent-header-banner__stack {
      display: flex;
      flex-direction: column;
    }
  }

  .c-agent-header__actions {
    display: flex;
    align-items: center;
  }

  .c-agent-header__actions :global(.c-agent-header__open-btn svg) {
    color: var(--vscode-descriptionForeground);
    width: 14px;
  }

  .c-agent-header__actions :global(.c-agent-header__pin-btn) {
    margin-right: 4px;
  }

  .c-agent-header__actions :global(.c-agent-header__pin-btn svg) {
    width: 12px;
    height: 12px;
    color: var(--vscode-descriptionForeground);
  }

  .c-agent-header__actions :global(.c-agent-header__pin-btn.is-pinned svg) {
    color: var(--ds-color-accent-9);
  }

  .c-setup-script-badge {
    display: flex;
    align-items: center;
    width: 15px;
    color: var(--ds-color-success-8);
    flex-shrink: 0;
  }

  .c-setup-script-badge :global(svg) {
    width: 15px;
    height: 15px;
  }

  .c-agent-info-item {
    --agent-info-item-gap: var(--ds-spacing-1);
    display: flex;
    align-items: center;
    gap: var(--agent-info-item-gap);
    min-width: 0;
    flex: 1;
    text-decoration: none;
    color: var(--ds-color-a11);
    max-width: calc(45vw - var(--agent-info-item-gap));
  }

  .c-agent-info-item :global(svg) {
    flex: none;
    width: 12px;
    height: 12px;
    min-width: 0;
  }

  .c-agent-info-item :global(.l-tooltip-trigger) {
    min-width: 0;
    flex: 1;
  }

  .c-agent-info-value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  @media (max-width: 250px) {
    .c-agent-info-row {
      flex-direction: column;
      gap: 2px;
    }

    .c-agent-info-item {
      /* Maximize width of item when in column */
      max-width: 80vw;
    }
  }

  .is-in-remote :global(.l-tooltip-trigger .c-base-btn) {
    --icon-color: #fff;
  }

  .c-agent-header__menu {
    margin-top: -3.5px;
    margin-bottom: -5px;
  }

  .c-agent-user-message {
    width: 100%;
    min-width: 0;
    overflow: hidden;
  }

  .c-agent-user-message :global(.c-text) {
    display: -webkit-box;
    -webkit-line-clamp: 1; /* Limit to 1 line */
    line-clamp: 1; /* For compatibility */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4em;
  }
</style>

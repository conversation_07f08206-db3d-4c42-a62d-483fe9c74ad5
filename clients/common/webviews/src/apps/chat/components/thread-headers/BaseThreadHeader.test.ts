import { render, fireEvent } from "@testing-library/svelte";
import { describe, test, expect, vi } from "vitest";
import BaseThreadHeader from "./BaseThreadHeader.svelte";

describe("BaseThreadHeader", () => {
  test("should disable all editing when isThreadEmpty is true", async () => {
    const onRename = vi.fn();
    
    const { container } = render(BaseThreadHeader, {
      props: {
        title: "New Thread",
        isEditable: true,
        isThreadEmpty: true,
        onRename,
      },
    });

    // Check that edit button is disabled
    const editButton = container.querySelector(".c-thread-header__title-icon");
    expect(editButton).toBeTruthy();
    expect(editButton?.getAttribute("disabled")).toBe("");

    // Check that double-click on title doesn't start editing
    const titleText = container.querySelector(".c-inline-edit-text-augment__text");
    expect(titleText).toBeTruthy();
    
    // Simulate double-click
    await fireEvent.click(titleText!, { detail: 2 });
    
    // Should not show input field
    const inputField = container.querySelector(".c-inline-edit-text-augment__input");
    expect(inputField).toBeFalsy();
    
    // onRename should not be called
    expect(onRename).not.toHaveBeenCalled();
  });

  test("should enable all editing when isThreadEmpty is false", async () => {
    const onRename = vi.fn();
    
    const { container } = render(BaseThreadHeader, {
      props: {
        title: "Thread with content",
        isEditable: true,
        isThreadEmpty: false,
        onRename,
      },
    });

    // Check that edit button is enabled
    const editButton = container.querySelector(".c-thread-header__title-icon");
    expect(editButton).toBeTruthy();
    expect(editButton?.hasAttribute("disabled")).toBe(false);

    // Check that double-click on title starts editing
    const titleText = container.querySelector(".c-inline-edit-text-augment__text");
    expect(titleText).toBeTruthy();
    
    // Simulate double-click
    await fireEvent.click(titleText!, { detail: 2 });
    
    // Should show input field
    const inputField = container.querySelector(".c-inline-edit-text-augment__input");
    expect(inputField).toBeTruthy();
  });

  test("should show helpful tooltip when thread is empty", async () => {
    const onRename = vi.fn();
    
    const { container } = render(BaseThreadHeader, {
      props: {
        title: "New Thread",
        isEditable: true,
        isThreadEmpty: true,
        onRename,
      },
    });

    // Check that edit button has helpful tooltip
    const editButton = container.querySelector(".c-thread-header__title-icon");
    expect(editButton).toBeTruthy();
    expect(editButton?.getAttribute("title")).toBe("Add content to edit thread title");
  });

  test("should show normal tooltip when thread is not empty", async () => {
    const onRename = vi.fn();
    
    const { container } = render(BaseThreadHeader, {
      props: {
        title: "Thread with content",
        isEditable: true,
        isThreadEmpty: false,
        onRename,
      },
    });

    // Check that edit button has normal tooltip
    const editButton = container.querySelector(".c-thread-header__title-icon");
    expect(editButton).toBeTruthy();
    expect(editButton?.getAttribute("title")).toBe("Edit Thread Title");
  });

  test("should respect isEditable=false regardless of isThreadEmpty", async () => {
    const onRename = vi.fn();
    
    const { container } = render(BaseThreadHeader, {
      props: {
        title: "Thread title",
        isEditable: false,
        isThreadEmpty: false,
        onRename,
      },
    });

    // Edit button should be disabled
    const editButton = container.querySelector(".c-thread-header__title-icon");
    expect(editButton).toBeTruthy();
    expect(editButton?.getAttribute("disabled")).toBe("");

    // Double-click should not work
    const titleText = container.querySelector(".c-inline-edit-text-augment__text");
    expect(titleText).toBeTruthy();
    
    await fireEvent.click(titleText!, { detail: 2 });
    
    const inputField = container.querySelector(".c-inline-edit-text-augment__input");
    expect(inputField).toBeFalsy();
  });
});

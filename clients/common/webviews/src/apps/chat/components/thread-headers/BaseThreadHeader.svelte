<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import InlineEditTextAugment from "$common-webviews/src/common/components/InlineEditTextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  // Props
  export let title: string = "";
  export let isSquished: boolean = false;
  export let isEditing: boolean = false;
  export let isEditable: boolean = true;
  export let isThreadEmpty: boolean = false;
  export let onRename = (_newValue: string) => {};

  // Computed editability - disable editing if thread is empty
  $: effectiveIsEditable = isEditable && !isThreadEmpty;

  // Expose window height binding to parent components
  let windowHeight = 0;

  $: effectiveIsSquished = isSquished || windowHeight < 700;

  function onEditClick() {
    isEditing = effectiveIsEditable && true;
  }
  //The node for which we will ignore click events i they happen.
  let outsideNode: HTMLElement | undefined;
</script>

<svelte:window bind:innerHeight={windowHeight} />

<div
  class="c-thread-header"
  class:c-thread-header--is-editing={effectiveIsEditable && isEditing}
  class:c-thread-header--is-editable={effectiveIsEditable}
  bind:this={outsideNode}
>
  <!-- Banner slot for status information (e.g., "Running in the cloud") -->
  <div class="c-thread-header__banner">
    <slot name="banner">
      <!-- Default banner content -->
    </slot>
  </div>
  <!-- Title area -->
  <div class="c-thread-header__title">
    <IconButtonAugment
      size={0}
      class="c-thread-header__title-icon"
      variant="ghost-block"
      disabled={!effectiveIsEditable}
      color="neutral"
      on:click={onEditClick}
      title={effectiveIsEditable ? "Edit Thread Title" : "Add content to edit thread title"}
    >
      <span class="c-thread-header__title-icon-content">
        <slot name="icon" />
      </span>
    </IconButtonAugment>

    <div class="c-thread-header__title-main">
      <slot name="title">
        <TextTooltipAugment
          content={"Rename"}
          nested={false}
          hasPointerEvents={false}
          class="c-thread-header__title-text"
        >
          <InlineEditTextAugment
            {outsideNode}
            size={1}
            weight="bold"
            value={title || "New Thread"}
            color="primary"
            isEditable={effectiveIsEditable}
            bind:isEditing
            showCommitButton={false}
            singleClickToEdit
            onChange={onRename}
          ></InlineEditTextAugment>
        </TextTooltipAugment>
      </slot>
    </div>
    {#if !isEditing}
      <slot name="title-actions" />
    {/if}
  </div>

  <!-- Info row for metadata (e.g., repository, branch) -->
  {#if !effectiveIsSquished}
    <div class="c-thread-header__info-row">
      <slot name="info-row">
        <!-- Default info row content -->
      </slot>
    </div>
  {/if}

  <!-- Status area for file changes summary or other status information -->
  <div class="c-thread-header__status">
    <slot name="status">
      <!-- Default status content -->
    </slot>
  </div>
</div>

<style>
  .c-thread-header {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-left: calc(0px - var(--chat-padding));
    margin-right: calc(0px - var(--chat-padding));
    border-top: 1px solid var(--augment-border-color);
    padding: var(--ds-spacing-1) var(--ds-spacing-3);
    width: 100dvw;
    transition: background-color 0.15s ease;
    flex: none;
  }
  /** hide edit icon when editing*/
  .c-thread-header--is-editing :global(.c-thread-header__title-icon) {
    display: none;
  }
  .c-thread-header__banner {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: var(--ds-color-neutral-2);
    padding: 0.5px var(--ds-spacing-3) 0.5px var(--ds-spacing-3);
    color: var(--vscode-descriptionForeground);
    font-size: 0.66rem;
    gap: var(--ds-spacing-2);
    transition: background-color 0.15s ease;
  }

  .c-thread-header__title {
    margin-bottom: 4px;
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-1);
    align-items: center;
    & .c-thread-header__title-icon-content {
      display: unset;
    }
    & .c-thread-header__title-icon-pencil {
      display: none;
    }
  }
  .c-thread-header--is-editable {
    & .c-thread-header__title-icon {
      cursor: pointer;
    }
  }
  .c-thread-header__title :global(.c-thread-header__title-text) {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    flex: 1;
    width: 100%;
    overflow: hidden;
  }

  .c-thread-header__title-main {
    flex: 1;
    min-width: 0;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .c-thread-header__info-row:not(:empty) {
    width: 100%;
    padding: 4px var(--ds-spacing-2) 0;
    font-size: 0.7rem;
    color: var(--vscode-descriptionForeground);
    margin-bottom: var(--ds-spacing-2);
  }

  .c-thread-header__status {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-thread-header__status:empty {
    margin-bottom: calc(0px - var(--ds-spacing-2));
  }

  .c-thread-header__status:not(:empty) {
    padding: 0 var(--ds-spacing-2);
  }
</style>

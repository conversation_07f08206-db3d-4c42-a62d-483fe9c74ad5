<script lang="ts">
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  import ChevronLeft from "$common-webviews/src/design-system/icons/chevron-left.svelte";
  import ChevronRight from "$common-webviews/src/design-system/icons/chevron-right.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import File from "$common-webviews/src/design-system/icons/vscode/file.svelte";
  import type {
    SetupScript,
    SetupScriptLocation,
  } from "$vscode/src/utils/remote-agent-setup/types";
  import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { getContext } from "svelte";
  import type { ChatModel } from "../../chat/models/chat-model";
  import { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
  import { ToolUsePhase } from "../types/tool-use-state";
  import SetupScriptCommandStatus from "./conversation/blocks/tools/components/local/SetupScriptCommandStatus.svelte";

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const chatModel = getContext<ChatModel>("chatModel");

  let scriptName = "setup.sh";
  let selectedLocation: SetupScriptLocation = "home";
  let isSaving = false;
  let errorMessage = "";

  // Extract setup script versions from chat history
  type ScriptVersion = {
    content: string;
    timestamp: number;
    status: ToolUsePhase;
    requestId: string;
    id: string;
  };
  let currentVersionIndex: number = 0; // New: Initialize currentVersionIndex

  $: scriptVersions = extractSetupScriptVersions(
    $remoteAgentsModel.currentConversation?.exchanges || [],
  );
  $: scriptVersionsCount = scriptVersions.length;
  const updateCurrentVersionIndex = () => {
    currentVersionIndex = scriptVersionsCount - 1;
  };
  $: scriptVersionsCount, updateCurrentVersionIndex();

  $: scriptVersion = scriptVersions[currentVersionIndex];

  const scrollToToolUseCard = (requestId: string) => {
    // setup script tool use cards have [data-tool-id={toolUse.tool_use_id}]
    const toolUseCard = document.querySelector(
      `[data-tool-id="${requestId}"]`,
    ) as HTMLElement | null;
    if (toolUseCard) {
      toolUseCard.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  function showDiffVersion(diffIndex = 1) {
    currentVersionIndex = Math.max(
      0,
      Math.min(scriptVersions.length - 1, currentVersionIndex + diffIndex),
    );

    const newScriptVersion = scriptVersions[currentVersionIndex];
    if (newScriptVersion?.id) {
      scrollToToolUseCard(newScriptVersion.id);
      // do it again after 0.5s because sometimes it gets stuck
      // I think from long collapse blocks
      setTimeout(() => {
        scrollToToolUseCard(newScriptVersion.id);
      }, 500);
    }
  }

  /**
   * Extract setup script versions from chat history
   * @param exchanges - The chat exchanges to extract from
   * @returns Array of setup script content strings
   */
  function extractSetupScriptVersions(exchanges: any[]): ScriptVersion[] {
    if (!exchanges || !Array.isArray(exchanges)) return [];

    return exchanges
      .flatMap(
        (
          exchange, // exchange here is the parent of response_nodes
        ) =>
          (exchange.exchange.response_nodes || [])
            .filter(
              (node: any) =>
                node.type === ChatResultNodeType.TOOL_USE &&
                node.tool_use?.tool_name === "setup-script" &&
                node.tool_use?.input_json,
            )
            .map((node: any) => {
              try {
                const inputJson =
                  typeof node.tool_use.input_json === "string"
                    ? JSON.parse(node.tool_use.input_json)
                    : node.tool_use.input_json;

                const state = $remoteAgentsModel.getToolUseState(node.tool_use.tool_use_id);
                // Ensure request_id is present on the exchange object
                const requestId = exchange.exchange?.request_id || exchange.request_id || "";
                if (!requestId) {
                  console.warn("SetupScriptSaveBar: Missing request_id for an exchange", exchange);
                }
                return {
                  content: inputJson.script_content || "",
                  timestamp: node.timestamp,
                  status: state.phase,
                  requestId: requestId,
                  id: node.tool_use.tool_use_id,
                };
              } catch (e) {
                console.error("Failed to parse setup script JSON:", e);
                return null;
              }
            }),
      )
      .filter((script): script is ScriptVersion => script !== null && script.content !== "");
  }

  async function saveAndUseScript(selectedLocation: SetupScriptLocation) {
    if (!scriptName || !selectedLocation) {
      errorMessage = "Please provide both script name and location";
      return;
    }

    if (!scriptVersion?.content) {
      errorMessage = "No script content to save";
      return;
    }

    try {
      isSaving = true;
      errorMessage = "";

      // Save the script
      // Ensure script name has .sh extension
      const finalScriptName = scriptName.endsWith(".sh") ? scriptName : `${scriptName}.sh`;

      const result = await remoteAgentsModel.saveSetupScript(
        finalScriptName,
        scriptVersion.content,
        selectedLocation,
      );

      let savedScript: SetupScript | undefined;

      if (result.success && result.path) {
        // Get the saved script details
        const scripts = await remoteAgentsModel.listSetupScripts();
        savedScript = scripts.find((script) => script.path === result.path);
      } else {
        errorMessage = result.error || "Failed to save setup script";
        return;
      }

      if (!savedScript) {
        errorMessage = "Failed to save script";
        return;
      }

      // Update the default setup script
      const currentAgent = $remoteAgentsModel?.currentAgent;
      const agentBranchRef =
        currentAgent?.workspace_setup?.starting_files?.github_commit_ref?.git_ref;
      const repoUrl =
        currentAgent?.workspace_setup?.starting_files?.github_commit_ref?.repository_url;
      remoteAgentsModel.saveLastRemoteAgentSetup(
        repoUrl || null,
        agentBranchRef || null,
        result.path,
      );

      // Clear the current agent to show the new agent flow
      remoteAgentsModel.setCurrentAgent(undefined);
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    } finally {
      isSaving = false;
    }
  }
</script>

{#if scriptVersions.length > 0}
  <div class="setup-script-save-bar">
    <div class="header-content">
      <div class="header-content__title">
        <TextAugment size={2} weight="medium">Setup script</TextAugment>
        <TextTooltipAugment content="View full script in editor" side="bottom">
          <ButtonAugment
            variant="ghost-block"
            color="neutral"
            size={1}
            on:click={() => {
              chatModel.extensionClient.openScratchFile(scriptVersion?.content, "shellscript");
            }}
          >
            <OpenInNewWindow slot="iconLeft" />
            View
          </ButtonAugment>
        </TextTooltipAugment>
      </div>
      <div class="header-content__title">
        {#if scriptVersions.length > 0}
          <div class="version-navigation">
            <TextTooltipAugment
              content={currentVersionIndex === 0 ? "" : "Switch to previous script"}
              triggerOn={[TooltipTriggerOn.Hover]}
            >
              <IconButtonAugment
                variant="ghost-block"
                size={1}
                on:click={() => showDiffVersion(-1)}
                disabled={currentVersionIndex === 0}
                title="Previous version"
              >
                <ChevronLeft />
              </IconButtonAugment>
            </TextTooltipAugment>
            <TextAugment size={1} color="secondary" class="header-content__version-count">
              {currentVersionIndex + 1} of {scriptVersions.length}
            </TextAugment>
            <TextTooltipAugment
              content={currentVersionIndex === scriptVersions.length - 1
                ? ""
                : "Switch to next script"}
              triggerOn={[TooltipTriggerOn.Hover]}
            >
              <IconButtonAugment
                variant="ghost-block"
                size={1}
                on:click={() => showDiffVersion(1)}
                disabled={currentVersionIndex === scriptVersions.length - 1}
                title="Next version"
              >
                <ChevronRight />
              </IconButtonAugment>
            </TextTooltipAugment>
          </div>
        {:else}
          <TextAugment size={1} color="secondary" class="header-content__version-count">
            No versions
          </TextAugment>
        {/if}
        {#if scriptVersion?.status}
          <div class="header-content__actions">
            <SetupScriptCommandStatus
              commandResult={scriptVersion.status === ToolUsePhase.completed
                ? "success"
                : scriptVersion.status === ToolUsePhase.error
                  ? "error"
                  : "loading"}
            />
          </div>
        {/if}
      </div>
    </div>

    <div class="save-bar-content">
      <TextFieldAugment
        bind:value={scriptName}
        on:blur={() => {
          if (!scriptName) {
            scriptName = "setup.sh";
          } else if (!scriptName.endsWith(".sh")) {
            scriptName += ".sh";
          }
        }}
        placeholder="Script name"
        size={2}
      >
        <File slot="iconLeft" />
      </TextFieldAugment>
      <div class="input-group location-buttons">
        <TextTooltipAugment
          content={"Save the setup script in your machine's home directory"}
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <ButtonAugment
            variant="solid"
            color="accent"
            disabled={isSaving || !scriptName || !selectedLocation}
            on:click={() => saveAndUseScript("home")}
          >
            Save locally
          </ButtonAugment>
        </TextTooltipAugment>
        <TextTooltipAugment
          content={"Save the setup script in the Git repository, for your team to use when you push your changes"}
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <ButtonAugment
            variant="solid"
            color="accent"
            disabled={isSaving || !scriptName}
            on:click={() => saveAndUseScript("git")}
          >
            Save in repo
          </ButtonAugment>
        </TextTooltipAugment>
      </div>

      {#if errorMessage}
        <div class="error-message">
          <TextAugment size={1} color="error">{errorMessage}</TextAugment>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .setup-script-save-bar {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-2);
    border: var(--augment-border);
    border-radius: var(--ds-radius-3);
    width: 100%;
    gap: var(--ds-spacing-2);
  }

  .header-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-1);
    width: 100%;
  }

  .header-content__title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
    gap: var(--ds-spacing-1);
  }

  .header-content__title :global(.c-text) {
    display: inline-block;
  }

  /* New styles for version navigation */
  .version-navigation {
    display: flex;
    align-items: center;
  }

  .version-navigation :global(.c-text) {
    display: flex;
    align-items: center;
  }

  @media (max-width: 400px) {
    .header-content :global(.header-content__version-count) {
      display: none;
    }
  }

  .save-bar-content {
    display: contents;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    width: 100%;
  }

  .input-group {
    width: 100%;
  }

  .error-message {
    margin-top: var(--ds-spacing-1);
  }

  .location-buttons {
    margin: var(--ds-spacing-1) 0;
    display: flex;
    gap: var(--ds-spacing-1);
  }

  .location-buttons :global(.l-tooltip-trigger) {
    flex: 1;
  }

  .location-buttons :global(.c-base-btn) {
    width: 100%;
    justify-content: center;
  }
</style>

import {
  type ToolUseState,
  ToolUsePhase,
} from "$common-webviews/src/apps/chat/types/tool-use-state";
import { type ICurrentConversationTaskStore } from "$common-webviews/src/apps/chat/models/task-store";

/**
 * Creates a reactive function that triggers task refresh when a tool reaches terminal state.
 * This function maintains its own state and should be called from a reactive statement.
 *
 * @param taskStore - The task store instance to call refresh on
 * @returns A function that should be called with the current toolUseState in a $: block
 */
export function createTaskRefreshOnCompletion(
  taskStore: ICurrentConversationTaskStore | undefined,
) {
  // Track previous phase to detect state transitions
  let previousPhase: ToolUsePhase | undefined = undefined;

  return (toolUseState: ToolUseState) => {
    const currentPhase = toolUseState.phase;
    const isTerminalState =
      currentPhase === ToolUsePhase.completed ||
      currentPhase === ToolUsePhase.error ||
      currentPhase === ToolUsePhase.cancelled;

    // Only trigger refresh if we've transitioned to a terminal state (not on initial load)
    if (
      isTerminalState &&
      previousPhase !== undefined &&
      previousPhase !== currentPhase &&
      taskStore
    ) {
      taskStore.refreshTasksThrottled();
    }

    previousPhase = currentPhase;
  };
}

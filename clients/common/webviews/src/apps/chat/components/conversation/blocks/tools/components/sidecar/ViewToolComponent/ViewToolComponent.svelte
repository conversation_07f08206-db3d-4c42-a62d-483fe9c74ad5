<script lang="ts" context="module">
  function viewRange(toolUseInput: Record<string, unknown>): { start: number; stop: number } {
    if (toolUseInput.view_range && Array.isArray(toolUseInput.view_range)) {
      return { start: toolUseInput.view_range[0], stop: toolUseInput.view_range[1] } as any;
    }
    return { start: 0, stop: 0 };
  }

  function getSearchQueryRegex(toolUseInput: Record<string, unknown>): string | undefined {
    return typeof toolUseInput.search_query_regex === "string"
      ? toolUseInput.search_query_regex
      : undefined;
  }

  function getCaseSensitive(toolUseInput: Record<string, unknown>): boolean {
    return typeof toolUseInput.case_sensitive === "boolean" ? toolUseInput.case_sensitive : false;
  }
</script>

<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import FileTextIcon from "$common-webviews/src/design-system/icons/file-text.svelte";
  import FolderIcon from "$common-webviews/src/design-system/icons/vscode/folder.svelte";
  import SearchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";
  import {
    stringOrDefault,
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ViewSecondary from "./ViewSecondary.svelte";
  import ViewToolUseDetails from "./ViewToolUseDetails.svelte";
  import ToolUseStatus from "../../../ToolUseStatus.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import ShellError from "../ShellError.svelte";
  import OpenFileButton from "../../OpenFileButton.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;
  let path: string;
  let isDirectory = false;
  let hasSearchQuery = false;

  $: {
    path = stringOrDefault(toolUseInput.path || toolUseInput.file_path, "");
    isDirectory = toolUseInput.type === "directory";
    hasSearchQuery = getSearchQueryRegex(toolUseInput) !== undefined;
  }
</script>

<BaseToolComponent bind:collapsed showToolOutput={hasSearchQuery}>
  <ToolUseHeader slot="header">
    <span slot="icon">
      {#if isDirectory}
        <FolderIcon />
      {:else if hasSearchQuery}
        <SearchIcon />
      {:else}
        <FileTextIcon />
      {/if}
    </span>
    <ViewSecondary
      slot="toolName"
      {...viewRange(toolUseInput)}
      {isDirectory}
      searchQueryRegex={getSearchQueryRegex(toolUseInput)}
      caseSensitive={getCaseSensitive(toolUseInput)}
    />
    <Filespan slot="secondary" filepath={path} />
    <span slot="toolAction">
      {#if !isDirectory}
        <OpenFileButton
          path={collapsed && toolUseState.phase === ToolUsePhase.completed ? path : undefined}
          {...viewRange(toolUseInput)}
        />
      {/if}
    </span>
    <div slot="toolStatus" class="c-view__tool-status">
      <ToolUseStatus />
    </div>
  </ToolUseHeader>
  <ViewToolUseDetails slot="details" {toolUseInput} />
  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-view__tool-status {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
    flex: 1;
    justify-content: flex-end;
    overflow: hidden;
  }
</style>

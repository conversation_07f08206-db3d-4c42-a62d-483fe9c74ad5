<script lang="ts">
  import { getChatModel } from "$common-webviews/src/apps/chat/chat-context";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import {
    type ButtonVariant,
    type ButtonColor,
    type ButtonSize,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import SuccessfulButton from "../../../../buttons/SuccessfulButton.svelte";
  import { getContext } from "svelte";
  export let path: string | undefined = undefined;
  export let start = 0;
  export let stop = 0;
  export let size: 0 | ButtonSize = 0;
  export let color: ButtonColor = "neutral";
  export let variant: ButtonVariant = "ghost-block";
  export let stickyColor = false;
  export let onOpenLocalFile = async function onOpenEditorFile(e: Event) {
    e?.stopPropagation?.();
    e?.preventDefault?.();
    if (!path) {
      return;
    }

    // Normal path resolution for other files
    const resolve = await chat?.extensionClient.resolvePath({
      rootPath: "",
      relPath: path,
    });
    chat?.extensionClient.openFile({
      repoRoot: resolve?.repoRoot ?? "",
      pathName: resolve?.pathName ?? "",
      range: {
        start: Math.max(start, 0),
        stop: Math.max(stop, 0),
      },
    });
    return "success";
  };

  const chat = getChatModel();

  const remoteAgentsModel = getContext<RemoteAgentsModel | undefined>(RemoteAgentsModel.key);
  $: isRemoteAgentSshWindow = $remoteAgentsModel?.isRemoteAgentSshWindow;
  $: isRemoteAgentMode = !!$remoteAgentsModel?.isActive;

  $: shouldShowOpenFileButton = !isRemoteAgentMode || isRemoteAgentSshWindow;
</script>

{#if shouldShowOpenFileButton}
  <span class="c-open-file-button-container c-open-file-button__size--{size}">
    <SuccessfulButton
      defaultColor={color}
      {stickyColor}
      {size}
      {variant}
      tooltip={{
        neutral: `Open File In Editor`,
        success: "Opening file...",
      }}
      stateVariant={{ success: "soft" }}
      onClick={onOpenLocalFile}
      icon={!$$slots.text}
    >
      <OpenInNewWindow slot="iconLeft" />
      <slot name="text" />
    </SuccessfulButton>
  </span>
{/if}

<style>
  .c-open-file-button-container {
    display: contents;
  }

  .c-open-file-button__size--0 :global(.c-open-file-button) {
    --base-btn-width: 16px;
    --base-btn-height: 16px;
    height: 16px;
    width: 16px;
  }
</style>

<!-- A component that displays checkpoint version with file changes similar to AgentEditList -->
<script lang="ts">
  // Import grey CSS variables
  import "@radix-ui/colors/gray.css";
  import "@radix-ui/colors/gray-dark.css";

  import { getContext } from "svelte";
  import {
    type AgenticRevertTargetInfo,
    isCheckpointRevert,
    type AgenticCheckpointDelimiter,
  } from "../../../types/chat-message";
  import type { CheckpointStore } from "../../../models/checkpoint-store";
  import { formatTimestamp } from "../../agent-edits/AgentEditList.svelte";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { ChatModel } from "../../../models/chat-model";
  import CheckpointHeader from "./CheckpointHeader.svelte";
  import AgentEditListItem from "../../agent-edits/AgentEditListItem.svelte";
  import type { ChatAgentEdit } from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
  import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import { visibilityObserverOnce } from "../../actions/trackOnScreen";

  export let turn: AgenticCheckpointDelimiter;

  const checkpointStore = getContext<CheckpointStore>("checkpointStore");
  const chatModel = getContext<ChatModel>("chatModel");
  const { targetCheckpointIdx, totalCheckpointCount, uuidToIdx } = checkpointStore;

  // State for collapsible UI. Starts out collapsed.
  let collapsed = true;

  /**
   * Determines if a checkpoint is the target checkpoint.
   * Returns true if the checkpoint matches target, or if no target is set and it's the latest checkpoint.
   * @param checkpointIdx - The index of the checkpoint to check
   * @param totalCheckpointCount - Total number of checkpoints
   * @param targetCheckpoint - Optional target checkpoint idx to compare against
   * @returns boolean indicating if this is the target checkpoint
   */
  function isTargetCheckpoint(
    checkpointIdx: number,
    totalCheckpointCount: number,
    targetCheckpoint?: number,
  ) {
    return (
      checkpointIdx === targetCheckpoint ||
      // When the target checkpoint is undefined, it defaults to the last checkpoint, which has
      // idx = totalCheckpointCount - 1
      (targetCheckpoint === undefined && checkpointIdx === totalCheckpointCount - 1)
    );
  }

  /**
   * If the checkpoint is the result of a revert, returns a message indicating the version it was reverted from.
   * @param revertTarget - The revert target, if any
   * @returns A message indicating the version it was reverted from, or undefined if not a revert
   */
  function getRevertMessage(
    turn: AgenticCheckpointDelimiter & AgenticRevertTargetInfo,
  ): string | undefined {
    if (turn.revertTarget?.uuid) {
      const targetIdx = $uuidToIdx.get(turn.revertTarget.uuid);
      if (targetIdx === undefined) {
        return undefined;
      }
      return `Reverted to Checkpoint ${targetIdx + 1}`;
    } else if (turn.revertTarget?.filePath) {
      return `Undid changes to ${turn.revertTarget.filePath.relPath}`;
    } else {
      return undefined;
    }
  }

  /**
   * Determines if we should dim all items after this checkpoint.
   * If we are on the target checkpoint, and the target is *not* the last checkpoint, we should dim.
   * @param checkpointIdx - The idx of the checkpoint to check
   * @param totalCheckpointCount - Total number of checkpoints
   * @param targetCheckpoint - Optional target checkpoint idx to compare against
   * @returns boolean indicating if this checkpoint is at or after the target
   */
  function dimDelimiter(
    checkpointIdx: number,
    totalCheckpointCount: number,
    targetCheckpoint?: number,
  ) {
    return (
      checkpointIdx === targetCheckpoint &&
      targetCheckpoint !== undefined &&
      targetCheckpoint < totalCheckpointCount - 1
    );
  }

  function handleFileClick(qualifiedPathName: IQualifiedPathName) {
    chatModel?.extensionClient.openFile({
      repoRoot: qualifiedPathName.rootPath,
      pathName: qualifiedPathName.relPath,
      allowOutOfWorkspace: true,
    });
  }

  function handleReviewFileClick(qualifiedPathName: IQualifiedPathName) {
    chatModel?.extensionClient.showAgentReview(
      qualifiedPathName,
      fromTimestamp,
      toTimestamp,
      false,
    );
  }

  function handleRevert() {
    void checkpointStore.revertToCheckpoint(turn.uuid);
  }

  $: checkpointIdx = $uuidToIdx.get(turn.uuid) ?? -1;
  $: toTimestamp = turn.toTimestamp;
  $: fromTimestamp = turn.fromTimestamp;

  $: isTarget = isTargetCheckpoint(checkpointIdx, $totalCheckpointCount, $targetCheckpointIdx);
  $: shouldDimAfter = dimDelimiter(checkpointIdx, $totalCheckpointCount, $targetCheckpointIdx);
  $: isLatest = checkpointIdx === $totalCheckpointCount - 1;
  $: displayCheckpointIdx = checkpointIdx + 1;

  // Revert info
  $: isRevert = isCheckpointRevert(turn);
  $: revertMessage = isCheckpointRevert(turn) ? getRevertMessage(turn) : undefined;

  // Get file changes for this checkpoint
  let checkpointSummary: ChatAgentEdit[] = [];
  $: filesWithEditsToShow = checkpointSummary.filter(
    (file) =>
      file.changesSummary &&
      (file.changesSummary.totalAddedLines > 0 || file.changesSummary.totalRemovedLines > 0),
  );
  $: diffSummary = filesWithEditsToShow.reduce(
    (acc, file) => {
      acc.totalAddedLines += file.changesSummary?.totalAddedLines ?? 0;
      acc.totalRemovedLines += file.changesSummary?.totalRemovedLines ?? 0;
      return acc;
    },
    { totalAddedLines: 0, totalRemovedLines: 0 },
  );
  $: hasChanges = filesWithEditsToShow.length > 0;

  // Load checkpoint summary when component is expanded AND stably scrolled into view AND
  // has not already been loaded
  let isVisibleOnScreen = false;
  let hasLoaded = false;
  $: if (isVisibleOnScreen && turn && !hasLoaded) {
    checkpointStore.getCheckpointSummary(turn).then((summary) => {
      checkpointSummary = summary;
      hasLoaded = true;
    });
  }
</script>

<!-- Don't render for the latest checkpoint, unless it's a revert -->
{#if !isLatest || isRevert}
  <div
    class="c-checkpoint-container"
    class:c-checkpoint-container--target-checkpoint={isTarget}
    class:c-checkpoint-container--dimmed-marker={shouldDimAfter}
    data-checkpoint-number={checkpointIdx}
    use:visibilityObserverOnce={{
      onVisible: () => (isVisibleOnScreen = true),
      scrollTarget: document.body,
    }}
  >
    <CollapsibleAugment class="c-checkpoint-collapsible" stickyHeader bind:collapsed>
      <CheckpointHeader
        slot="header"
        {displayCheckpointIdx}
        filesCount={filesWithEditsToShow.length}
        timestamp={formatTimestamp(toTimestamp)}
        {revertMessage}
        {diffSummary}
        {hasChanges}
        {isTarget}
        onRevertClick={handleRevert}
      />

      <!-- File changes list -->
      {#if hasChanges}
        <div class="c-edits-list">
          {#each filesWithEditsToShow as file}
            <AgentEditListItem
              qualifiedPathName={file.qualifiedPathName}
              lineChanges={file.changesSummary}
              onClickFile={() => handleFileClick(file.qualifiedPathName)}
              onClickReview={() => handleReviewFileClick(file.qualifiedPathName)}
            />
          {/each}
        </div>
      {:else if !collapsed}
        <div class="c-edits-list c-edits-list--empty">
          <TextAugment size={1} color="neutral">No changes to show</TextAugment>
        </div>
      {/if}
    </CollapsibleAugment>
  </div>
{/if}

<style>
  .c-checkpoint-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: var(--ds-spacing-1);

    & .c-checkpoint-collapsible {
      width: 100%;
    }
  }

  .c-edits-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-1);
  }

  .c-edits-list--empty {
    opacity: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-3);
    user-select: none;
  }
</style>

<script lang="ts">
  import { getContext } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import SplitButtonAugment from "$common-webviews/src/design-system/components/SplitButtonAugment";
  import type { SplitButtonDisableState } from "$common-webviews/src/design-system/components/SplitButtonAugment/types";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import type { SendMode, SendModeOption } from "../../types/send-mode";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../models/task-store";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";

  export let disabled: SplitButtonDisableState = false;
  export let onSelectModel: (modelId: string | null) => void;
  export let modelIdToDisplayName: { [modelId: string]: string };
  export let onSend: () => void;

  // New props for send mode selection
  export let modeOptions: SendModeOption[] = [];
  export let currentMode: SendMode;
  export let onSelectMode: (mode: SendMode) => void;

  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { canShowTaskList } = taskStore;

  // Show dropdown if there are multiple models OR multiple mode options
  $: hasMultipleModels = Object.keys(modelIdToDisplayName).length > 1;
  $: hasMultipleModes = modeOptions.length > 1;
  $: showDropdown =
    hasMultipleModels || ($canShowTaskList && hasMultipleModes && !$remoteAgentsModel?.isActive);

  $: primaryDisabled = typeof disabled === "boolean" ? disabled : disabled.primaryDisabled;
</script>

<SplitButtonAugment.Root
  size={1}
  variant="solid"
  color={primaryDisabled ? "neutral" : "accent"}
  {disabled}
  {showDropdown}
  dropdownSide="top"
  dropdownAlign="end"
  on:click={onSend}
  on:keyup={onKey("Enter", onSend)}
>
  <slot />

  <svelte:fragment slot="dropdown-content">
    <!-- Send Mode Options -->
    {#if hasMultipleModes}
      {#each modeOptions as modeOption}
        {@const isHighlighted = modeOption.id === currentMode}
        <DropdownMenuAugment.Item
          highlight={isHighlighted}
          onSelect={() => {
            onSelectMode(modeOption.id);
          }}
        >
          {modeOption.label}
        </DropdownMenuAugment.Item>
      {/each}

      <!-- Separator if we have both modes and models -->
      {#if hasMultipleModels}
        <DropdownMenuAugment.Separator />
      {/if}
    {/if}

    <!-- Model Options -->
    {#if hasMultipleModels}
      <DropdownMenuAugment.Label>Models</DropdownMenuAugment.Label>
      {#each Object.keys(modelIdToDisplayName) as modelId}
        {@const displayName = modelIdToDisplayName[modelId]}
        {@const isHighlighted = modelId === chatModel.currentConversationModel.selectedModelId}
        <DropdownMenuAugment.Item
          highlight={isHighlighted}
          onSelect={() => {
            // Close the model selector after selecting it
            onSelectModel(modelId);
          }}
        >
          {displayName}
        </DropdownMenuAugment.Item>
      {/each}
    {/if}
  </svelte:fragment>
</SplitButtonAugment.Root>

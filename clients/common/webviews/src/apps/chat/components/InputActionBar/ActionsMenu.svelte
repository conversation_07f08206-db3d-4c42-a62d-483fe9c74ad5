<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import { getContext, tick } from "svelte";
  import type { ChatModel } from "../../models/chat-model";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import {
    getVisibleSlashCommands,
    type ISlashCommandOptionData,
  } from "../../types/slash-command-list";
  import { type SlashCommandModel } from "../../models/slash-command-model";
  import SlashForward from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/slash-forward.svg?component";
  import CommandContent from "./CommandContent.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";

  export let onCloseDropdown: () => void = () => {};

  let dropdownRoot: DropdownMenuRoot | undefined;
  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
  const slashCommandModel = getContext<SlashCommandModel>("slashCommandModel");
  const flagsModel = $chatModel.flags;

  // Controls for dropdown
  async function onOpenChange(open: boolean) {
    if (open) {
      await tick();
      focusOnSearchBox();
    } else {
      onCloseDropdown();
      query = "";
    }
  }

  const onKeyDown = async (e: KeyboardEvent) => {
    // Special keys to not focus the input for
    switch (e.key) {
      case "ArrowLeft":
      case "ArrowRight":
      case "ArrowDown":
      case "ArrowUp":
      case "Enter":
        break;
      default:
        if (!e.defaultPrevented) {
          focusOnSearchBox();
        }
    }
  };

  function focusOnSearchBox() {
    dropdownRoot?.focusIdx(-1);
  }

  // Store query
  let query: string = "";
  $: filteredCommands = getActionsFromQuery(query, $flagsModel.enableGenerateCommitMessage);
  function getActionsFromQuery(
    query: string | undefined,
    enableGenerateCommitMessage: boolean,
  ): ISlashCommandOptionData[] {
    let newCommands = getVisibleSlashCommands();
    if (!enableGenerateCommitMessage) {
      newCommands = newCommands.filter((command) => command.id !== "generate-commit-message");
    }

    if (query === undefined) {
      return newCommands;
    }

    return newCommands.filter((command) =>
      command.label.toLowerCase().includes(query.toLowerCase()),
    );
  }

  function onSelectCommand(command: ISlashCommandOptionData) {
    slashCommandModel.setActiveCommand(command.id);
    dropdownRoot?.requestClose();
  }
</script>

<DropdownMenuAugment.Root bind:this={dropdownRoot} {onOpenChange} nested={false}>
  <DropdownMenuAugment.Trigger>
    <CommandContent>
      <IconButtonAugment size={1} variant="ghost-block" color="neutral">
        <SlashForward />
      </IconButtonAugment>
    </CommandContent>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content size={1} side="top" align="start" on:keydown={onKeyDown}>
    <div class="c-actions-menu__items">
      {#each filteredCommands as command}
        <DropdownMenuAugment.Item onSelect={() => onSelectCommand(command)}>
          /{command.label}
        </DropdownMenuAugment.Item>
      {/each}
    </div>
    <DropdownMenuAugment.Separator />
    <DropdownMenuAugment.TextFieldItem placeholder="Run an action" bind:value={query} />
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>

<style>
  .c-actions-menu__items {
    max-height: 50vh;
    overflow-y: scroll;

    display: flex;
    flex-direction: column;

    /* No scrollbar */
    scrollbar-width: none;
  }
</style>

<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let title: string = "Select an option";
  export let subtitle: string = "";
</script>

<div class="c-card-button__icon-left">
  <slot name="iconLeft" />
</div>
<div class="c-card-button__content">
  <div class="c-card-button__title">
    <slot name="title">
      <TextAugment size={2}>
        {title}
      </TextAugment>
    </slot>
  </div>
  {#if subtitle}
    <div class="c-card-button__subtitle">
      <slot name="subtitle">
        <TextAugment size={2}>{subtitle}</TextAugment>
      </slot>
    </div>
  {/if}
</div>
{#if $$slots.iconRight}
  <div class="c-card-button__icon-right">
    <slot name="iconRight" />
  </div>
{/if}

<style>
  .c-card-button__icon-left {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    padding: 6px 0 6px var(--ds-spacing-3);
    color: var(--ds-color-neutral-a11);
    border-radius: 5px 0 0 5px;
  }

  .c-card-button__icon-right {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-color-neutral-a10);
    padding: var(--ds-spacing-1) var(--ds-spacing-3);
  }

  .c-card-button__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
    gap: var(--ds-spacing-1);
  }

  .c-card-button__title {
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 2em;
    max-width: 100%;
    white-space: nowrap;
  }
  .c-card-button__title :global(.c-text) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }

  :global(.c-card-button__content:has(.c-card-button__subtitle)) .c-card-button__title {
    padding: var(--ds-spacing-2) var(--ds-spacing-3) 0;
  }

  .c-card-button__subtitle {
    padding: 0 var(--ds-spacing-3) var(--ds-spacing-2);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 2em;
    color: var(--ds-color-neutral-10);
    max-width: 100%;
  }
  .c-card-button__subtitle :global(.c-text) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
</style>

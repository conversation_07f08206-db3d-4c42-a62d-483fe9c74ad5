<script lang="ts">
  import { type ButtonColor } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import ChevronUp from "$common-webviews/src/design-system/icons/chevron-up.svelte";
  import { type AgentExecutionMode } from "../../models/chat-model";
  import { getModeDisplayInfo } from "./mode-display-helper";

  export let isDropdownOpen: boolean;
  export let isCurrConversationAgentic: boolean;
  export let agentExecutionMode: AgentExecutionMode | undefined = undefined;
  export let isBackgroundAgent: boolean | undefined;
  export let variant: "soft" | "ghost" = "ghost";
  export let color: ButtonColor = "neutral";
  export let disabled: boolean = false;
  $: modeInfo = getModeDisplayInfo({
    isConversationAgentic: isCurrConversationAgentic,
    agentExecutionMode,
    isBackgroundAgent: !!isBackgroundAgent,
  });
</script>

<ButtonAugment size={1} {variant} {color} {disabled}>
  <svelte:component this={modeInfo.icon} slot="iconLeft" />
  {modeInfo.primaryText}
  <svelte:fragment slot="iconRight">
    {#if isDropdownOpen}
      <ChevronUp />
    {:else}
      <ChevronDown />
    {/if}
  </svelte:fragment>
</ButtonAugment>

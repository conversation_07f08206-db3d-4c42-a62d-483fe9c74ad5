<script lang="ts">
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import TaskIcon from "./TaskIcon.svelte";
  import { getTaskStatusLabel } from "../utils/task-status-utils";

  // Props
  export let taskUuid: string;
  export let taskState: TaskState;
  export let taskStore: ICurrentConversationTaskStore;
  export let disabled: boolean = false;
  export let align: "start" | "center" | "end" = "end";
  export let side: "top" | "right" | "bottom" | "left" = "bottom";

  // Local state
  let requestCloseDropdown: (() => void) | undefined = undefined;
  $: dropdownOptions = Object.values(TaskState);
</script>

<DropdownMenu.Root bind:requestClose={requestCloseDropdown}>
  <DropdownMenu.Trigger>
    <slot />
  </DropdownMenu.Trigger>
  <DropdownMenu.Content size={1} {side} {align}>
    {#each dropdownOptions as state}
      <DropdownMenu.Item
        onSelect={() => {
          if (disabled || state === taskState) return;
          taskStore.updateTask(taskUuid, { state }, TaskUpdatedBy.USER);
          requestCloseDropdown?.(); // Close dropdown after selection
        }}
        highlight={state === taskState}
        disabled={disabled || state === taskState}
        class="c-task-dropdown-item c-task-dropdown-item--{state}"
      >
        <TaskIcon slot="iconLeft" taskState={state} size={1} />
        {getTaskStatusLabel(state)}
      </DropdownMenu.Item>
    {/each}
  </DropdownMenu.Content>
</DropdownMenu.Root>

<style>
  /* Dropdown item styling */
  :global(.c-task-dropdown-item) {
    display: flex;
    align-items: center;
    transition:
      background-color 0.2s ease,
      color 0.2s ease;
  }
</style>

import { FocusModel, type IFocusModel } from "$common-webviews/src/common/models/focus-model";
import { ChatMetricName } from "$vscode/src/metrics/types";
import type { SelectedCodeDetails } from "$vscode/src/utils/types";
import {
  type ChatUserMessageData,
  type IChatActiveContext,
} from "$vscode/src/webview-providers/webview-messages";
import { splitChatHistoryByCharLimit } from "@augment-internal/sidecar-libs/src/chat/chat-truncation";
import {
  ChatRequestNodeType,
  ChatResultNodeType,
  EditEventSource,
  ImageFormatType,
  PersonaType,
  type ChatRequestNode,
  type ChatResultNode,
  type Exchange,
  type MemoriesInfo,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { AgentRequestEventName } from "@augment-internal/sidecar-libs/src/metrics/types";
import type { JSONContent } from "@tiptap/core";
import type { Readable } from "svelte/store";
import {
  createThrottledCachedStore,
  type IThrottledCachedStore,
} from "../../../common/stores/throttled-cache-store";
import { type IExtensionClient } from "../extension-client";
import {
  ChatItemType,
  ExchangeStatus,
  isChatItemAgenticCheckpointDelimiter,
  isChatItemExchangeWithStatus,
  isChatItemHistorySummary,
  isChatItemSuccessfulExchange,
  SeenState,
  type ChatItem,
  type ExchangeWithStatus,
  type HistorySummaryMessage,
  type IChatItemSeenState,
} from "../types/chat-message";
import { FeedbackRating } from "../types/feedback-rating";
import type { FeedbackState } from "../types/feedback-state";
import { isItemPersonality, type IChatMentionable } from "../types/mention-option";
import {
  getToolUseKey,
  getToolUseKeyData,
  ToolUsePhase,
  type ToolUseState,
} from "../types/tool-use-state";
import { NEW_AGENT_KEY } from "./agent-constants";
import { filterByUserExchange, isUserExchange } from "./agent-conversation-utils";
import type { ChatFlagsModel } from "./chat-flags-model";
import type { SortableConversationFieldType } from "./chat-model";
import { type SpecialContextInputModel } from "./context-model";
import { getPersonalityPrompt } from "./personality-prompts";
import {
  isAgentConversation,
  isAutofixConversation,
  type IChatFlags,
  type IConversation,
  type StreamStateHandlerI,
} from "./types";
import { ChatStopReason } from "@augment-internal/sidecar-libs/src/api/types";
import { normalizeToolUse } from "../utils/tool-utils";

export const DEFAULT_CONVERSATION_NAME = "New Chat";
export const DEFAULT_AGENT_CONVERSATION_NAME = "New Agent";
export const DEFAULT_AUTOFIX_CONVERSATION_NAME = "Autofix Chat";

export class ConversationModel implements Readable<IConversation> {
  private _state: IConversation;
  private _subscribers: Set<(conversation: ConversationModel) => void> = new Set();
  private _focusModel: FocusModel<ExchangeWithStatus> = new FocusModel();
  private _onSendExchangeListeners: Array<(exchange: ExchangeWithStatus) => void> = [];
  private _onNewConversationListeners: Array<() => void> = [];
  private _onHistoryDeleteListeners: Array<(item: ChatItem) => void> = [];
  private _onBeforeChangeConversationListeners: Array<
    (current: IConversation, next: IConversation) => IConversation | undefined
  > = [];
  private _totalCharactersCacheThrottleMs = 1000;

  // Store for total characters count
  private _totalCharactersStore: IThrottledCachedStore<number>;

  constructor(
    private _extensionClient: IExtensionClient,
    private _chatFlagModel: ChatFlagsModel,
    private _specialContextInputModel: SpecialContextInputModel,
    private _saveConversation: (
      conversation: IConversation,
      doClearNewAgentThread?: boolean,
    ) => void,
  ) {
    this._state = {
      ...ConversationModel.create(),
    };

    // Initialize the total characters store
    this._totalCharactersStore = this._createTotalCharactersStore();
  }

  /**
   * Creates a store that tracks the total number of characters in a conversation
   *
   * @returns A store that provides the total character count
   */
  private _createTotalCharactersStore(): IThrottledCachedStore<number> {
    // Function to calculate the total characters
    const calculateTotalCharacters = (): number => {
      let totalChars = 0;

      // Count characters in chat history
      const chatHistory = this._state.chatHistory;
      const processedHistory = this._convertHistoryToExchanges(chatHistory);
      processedHistory.forEach((item) => {
        totalChars += JSON.stringify(item).length;
      });

      // Count characters in draft exchange if present
      if (this._state.draftExchange) {
        totalChars += JSON.stringify(this._state.draftExchange).length;
      }

      return totalChars;
    };

    // Use the generic throttled cached store
    return createThrottledCachedStore<number>(
      calculateTotalCharacters,
      0,
      this._totalCharactersCacheThrottleMs,
    );
  }

  /**
   * Initializes the persona type for a conversation thread.
   */
  public async decidePersonaType(): Promise<PersonaType> {
    try {
      const workspaceInfo = await this._extensionClient.getWorkspaceInfo();
      const totalTrackedFiles =
        workspaceInfo.trackedFileCount?.reduce((sum, count) => sum + count, 0) || 0;

      if (totalTrackedFiles <= 4) {
        return PersonaType.PROTOTYPER;
      } else {
        return PersonaType.DEFAULT;
      }
    } catch (error) {
      console.error("Error determining persona type:", error);
      return PersonaType.DEFAULT;
    }
  }

  public static create(initialOptions: Partial<IConversation> = {}): IConversation {
    const now = new Date().toISOString();
    const id = initialOptions.id || crypto.randomUUID();
    return {
      id,
      name: undefined,
      createdAtIso: now,
      lastInteractedAtIso: now,
      chatHistory: [],
      feedbackStates: {},
      toolUseStates: {},
      draftExchange: undefined,
      draftActiveContextIds: undefined,
      selectedModelId: undefined,
      requestIds: [],
      isPinned: false,
      lastUrl: undefined,
      isShareable: false,
      extraData: {},
      personaType: PersonaType.DEFAULT,
      ...initialOptions,
    };
  }

  public static toSentenceCase(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  public static getDisplayName(conversation: IConversation): string {
    const history = this._filterToExchanges(conversation);

    let defaultName;
    if (isAutofixConversation(conversation)) {
      defaultName = DEFAULT_AUTOFIX_CONVERSATION_NAME;
    } else if (isAgentConversation(conversation)) {
      defaultName = DEFAULT_AGENT_CONVERSATION_NAME;
    } else {
      defaultName = DEFAULT_CONVERSATION_NAME;
    }

    return ConversationModel.toSentenceCase(
      conversation.name || history[0]?.request_message || defaultName,
    );
  }

  private static _filterToExchanges(conversation: IConversation): Array<ExchangeWithStatus> {
    return conversation.chatHistory.filter((m): m is ExchangeWithStatus =>
      isChatItemExchangeWithStatus(m),
    );
  }

  /**
   * Determines if this conversation is a new agent thread that hasn't been sent yet
   * @returns true if this is a new agent thread, false otherwise
   */
  public static isNew(conversation: IConversation): boolean {
    return conversation.id === NEW_AGENT_KEY;
  }

  public static isEmpty(conversation: IConversation): boolean {
    // No chat history and no draft means an empty conversation
    const historicExchanges = conversation.chatHistory.filter((m): m is ExchangeWithStatus =>
      isChatItemExchangeWithStatus(m),
    );
    return historicExchanges.length === 0 && !conversation.draftExchange?.request_message;
  }

  public static isNamed(conversation: IConversation): boolean {
    return conversation.name !== undefined && conversation.name !== "";
  }

  /**
   * Determines if a thread is empty and should not allow title editing
   * A thread is considered empty if it has no messages and no draft content
   * @param thread - The thread object (can be ChatThread, LocalAgentThread, or RemoteAgentThread)
   * @returns true if the thread is empty and title editing should be disabled
   */
  public static isThreadEmpty(thread: { conversation?: IConversation; isNew?: boolean }): boolean {
    // If it's a new thread (using the special NEW_AGENT_KEY), it's always considered empty
    if (thread.isNew) {
      return true;
    }

    // If there's no conversation data, consider it empty
    if (!thread.conversation) {
      return true;
    }

    // Use the existing isEmpty logic
    return ConversationModel.isEmpty(thread.conversation);
  }

  public static getTime(conversation: IConversation, field: SortableConversationFieldType): Date {
    if (field === "lastMessageTimestamp") {
      return ConversationModel.lastMessageTimestamp(conversation);
    } else if (field === "lastInteractedAt") {
      return ConversationModel.lastInteractedAt(conversation);
    } else {
      // Default to createdAt if the field is not recognized
      return ConversationModel.createdAt(conversation);
    }
  }

  public static createdAt(conversation: IConversation): Date {
    return new Date(conversation.createdAtIso);
  }

  public static lastInteractedAt(conversation: IConversation): Date {
    return new Date(conversation.lastInteractedAtIso);
  }

  /**
   * Get the timestamp of the last message in the conversation as a Date object
   * @param conversation The conversation to get the timestamp from
   * @returns The timestamp of the last message as a Date, or the creation date if no messages exist
   */
  public static lastMessageTimestamp(conversation: IConversation): Date {
    const history = this._filterToExchanges(conversation);
    if (history.length === 0) {
      // If there are no messages, fall back to the creation date
      return this.createdAt(conversation);
    }
    const lastExchange = history[history.length - 1];
    return lastExchange.timestamp ? new Date(lastExchange.timestamp) : this.createdAt(conversation);
  }

  public static isValid(conversation: IConversation): boolean {
    return (
      conversation.id !== undefined &&
      (!ConversationModel.isEmpty(conversation) || ConversationModel.isNamed(conversation))
    );
  }

  subscribe = (sub: (conversation: ConversationModel) => void): (() => void) => {
    this._subscribers.add(sub);
    sub(this);
    return () => {
      this._subscribers.delete(sub);
    };
  };

  /**
   * Replace the current state with the given conversation.
   *
   * @param conversation - the new conversation to be set
   * @param preserveContext - if true, the current conversation's context will be preserved before it gets replaced
   */
  setConversation = (
    conversation: IConversation,
    preserveContext = true,
    shouldUpdateLastInteractedAt = true,
  ): boolean => {
    // Check if any listeners want to modify the conversation before it gets replaced
    const isDifferentConversation = conversation.id !== this._state.id;
    if (isDifferentConversation && shouldUpdateLastInteractedAt) {
      // When we swap to a different conversation, we want to populate the tool use states
      // with the new format.
      conversation.toolUseStates = Object.fromEntries(
        Object.entries(conversation.toolUseStates ?? {}).map(([key, state]) => {
          if (state.requestId && state.toolUseId) {
            // Validate
            const { requestId, toolUseId } = getToolUseKeyData(key);
            if (requestId !== state.requestId || toolUseId !== state.toolUseId) {
              console.warn(
                "Tool use state key does not match request and tool use IDs. Got key ",
                key,
                "but object has ",
                getToolUseKey(state),
              );
            }
            return [key, state];
          }

          return [key, { ...state, ...getToolUseKeyData(key) }];
        }),
      );
      conversation = this._notifyBeforeChangeConversation(this._state, conversation);
      conversation.lastInteractedAtIso = new Date().toISOString();
    }

    if (preserveContext && isDifferentConversation && this.isValid) {
      this.saveDraftActiveContextIds();
      this._unloadContextFromConversation(this._state);
    }

    // If a new conversation is being set, we want to copy over the draft
    // from the previous conversation.
    const isNewConversation = ConversationModel.isEmpty(conversation);
    if (isDifferentConversation && isNewConversation) {
      const draftExchange = this._state.draftExchange;
      if (draftExchange) {
        conversation.draftExchange = draftExchange;
      }
    }

    this._state = conversation;
    this._focusModel.setItems(this._state.chatHistory.filter(isChatItemExchangeWithStatus));
    this._focusModel.initFocusIdx(-1);
    this._subscribers.forEach((sub) => sub(this));
    this._saveConversation(this._state);

    if (isDifferentConversation) {
      this._loadContextFromConversation(conversation);
      this.loadDraftActiveContextIds();
      this._onNewConversationListeners.forEach((listener) => listener());
    }
    return true;
  };

  public onBeforeChangeConversation(
    listener: (current: IConversation, next: IConversation) => IConversation | undefined,
  ): () => void {
    this._onBeforeChangeConversationListeners.push(listener);
    return () => {
      this._onBeforeChangeConversationListeners = this._onBeforeChangeConversationListeners.filter(
        (l) => l !== listener,
      );
    };
  }

  /**
   * Notifies all before-new-conversation listeners and returns the potentially modified conversation
   */
  private _notifyBeforeChangeConversation(
    current: IConversation,
    next: IConversation,
  ): IConversation {
    let result = next;
    for (const listener of this._onBeforeChangeConversationListeners) {
      const listenerResult = listener(current, result);
      if (listenerResult !== undefined) {
        result = listenerResult;
      }
    }
    return result;
  }

  // update can take a partial conversation and merge it with the current state
  private update = (state: Partial<IConversation>) => {
    this.setConversation({ ...this._state, ...state });
    this._totalCharactersStore.updateStore(); // update the store on any conversation update
  };

  get extraData(): Record<string, unknown> | undefined {
    return this._state.extraData;
  }

  set extraData(extraData: Record<string, unknown>) {
    this.update({ extraData });
  }

  get focusModel(): IFocusModel<ExchangeWithStatus> {
    return this._focusModel;
  }

  get isValid(): boolean {
    return ConversationModel.isValid(this._state);
  }

  get id(): string {
    return this._state.id;
  }

  get name(): IConversation["name"] {
    return this._state.name;
  }

  get personaType(): PersonaType {
    return this._state.personaType ?? PersonaType.DEFAULT;
  }

  get rootTaskUuid(): string | undefined {
    return this._state.rootTaskUuid;
  }

  set rootTaskUuid(uuid: string | undefined) {
    this.update({ rootTaskUuid: uuid });
  }

  get displayName(): string {
    return ConversationModel.getDisplayName(this._state);
  }

  get createdAtIso(): IConversation["createdAtIso"] {
    return this._state.createdAtIso;
  }

  get createdAt(): Date {
    return ConversationModel.createdAt(this._state);
  }

  get chatHistory(): IConversation["chatHistory"] {
    return this._state.chatHistory;
  }

  get feedbackStates(): IConversation["feedbackStates"] {
    return this._state.feedbackStates;
  }

  get toolUseStates(): IConversation["toolUseStates"] {
    return this._state.toolUseStates;
  }

  get draftExchange(): IConversation["draftExchange"] {
    return this._state.draftExchange;
  }

  get selectedModelId(): IConversation["selectedModelId"] {
    return this._state.selectedModelId;
  }

  get isPinned(): boolean {
    return !!this._state.isPinned;
  }

  get extensionClient(): IExtensionClient {
    return this._extensionClient;
  }

  toggleIsPinned = () => {
    this.update({ isPinned: !this.isPinned });
  };

  setName = (name: string) => {
    this.update({ name });
  };

  setSelectedModelId = (modelId: string | null) => {
    this.update({ selectedModelId: modelId });
  };

  updateFeedback = (requestId: string, feedback: FeedbackState) => {
    this.update({
      feedbackStates: {
        ...this._state.feedbackStates,
        [requestId]: feedback,
      },
    });
  };

  updateToolUseState = (state: ToolUseState) => {
    this.update({
      toolUseStates: {
        ...this._state.toolUseStates,
        [getToolUseKey(state)]: state,
      },
    });
  };

  getToolUseState = (
    requestId: string | undefined,
    toolUseId: string | undefined,
  ): ToolUseState => {
    if (requestId === undefined || toolUseId === undefined || this.toolUseStates === undefined) {
      return {
        phase: ToolUsePhase.unknown,
        requestId: requestId ?? "",
        toolUseId: toolUseId ?? "",
      };
    }

    return (
      this.toolUseStates[getToolUseKey({ requestId, toolUseId })] || {
        phase: ToolUsePhase.new,
      }
    );
  };

  /**
   * Gets the tool use state of the last tool use node in the last exchange.
   *
   * @returns The tool use state of the last tool use node in the last exchange, or an object with phase: ToolUsePhase.unknown if there is no last exchange or no tool use nodes
   */
  public getLastToolUseState = () => {
    const lastExchange = this.lastExchange;
    if (!lastExchange) {
      return {
        phase: ToolUsePhase.unknown,
      };
    }
    const toolUseNode = toolOrToolStart(lastExchange?.structured_output_nodes);

    if (!toolUseNode) {
      return {
        phase: ToolUsePhase.unknown,
      };
    }
    return this.getToolUseState(lastExchange.request_id, toolUseNode.tool_use?.tool_use_id);
  };

  public addChatItem(item: ChatItem) {
    this.addExchange(item);
  }

  private addExchange = (exchange: ChatItem) => {
    const chatHistory = [...this._state.chatHistory, exchange];

    let feedbackStates: IConversation["feedbackStates"] | undefined = undefined;
    if (isChatItemExchangeWithStatus(exchange)) {
      feedbackStates = exchange.request_id
        ? {
            ...this._state.feedbackStates,
            [exchange.request_id]: {
              selectedRating: FeedbackRating.unset,
              feedbackNote: "",
            },
          }
        : undefined;
    }

    // Reset the last saved Url for the thread, since the thread is moving forward.
    this.update({
      chatHistory,
      ...(feedbackStates ? { feedbackStates } : {}),
      lastUrl: undefined,
    });
  };

  public resetShareUrl = () => {
    this.update({
      lastUrl: undefined,
    });
  };

  private updateExchangeById = (
    exchange: Partial<ExchangeWithStatus>,
    requestId: string,
    append: boolean = false,
  ): boolean => {
    // See if the exchange exists
    const exchangeWithId = this.exchangeWithRequestId(requestId);
    if (exchangeWithId === null) {
      console.warn("No exchange with this request ID found.");
      return false;
    }

    if (append && exchange.response_text !== undefined) {
      exchange.response_text =
        (exchangeWithId.response_text ?? "") + (exchange.response_text ?? "");
    }

    if (append) {
      exchange.structured_output_nodes = normalizeToolUse([
        ...(exchangeWithId.structured_output_nodes ?? []),
        ...(exchange.structured_output_nodes ?? []),
      ]);
    }
    //If we have tool we do not have stop_reason.
    if (
      exchange.stop_reason !== exchangeWithId.stop_reason &&
      exchangeWithId.stop_reason &&
      exchange.stop_reason === ChatStopReason.REASON_UNSPECIFIED
    ) {
      exchange.stop_reason = exchangeWithId.stop_reason;
    }

    if (append && exchange.workspace_file_chunks !== undefined) {
      exchange.workspace_file_chunks = [
        ...(exchangeWithId.workspace_file_chunks ?? []),
        ...(exchange.workspace_file_chunks ?? []),
      ];
    }

    // If we get a main text finished node that has content on it,
    // replace the displayed text with this updated context
    const mainTextFinishedContent = (exchange.structured_output_nodes || []).find(
      (node) => node.type === ChatResultNodeType.MAIN_TEXT_FINISHED,
    )?.content;
    if (mainTextFinishedContent && mainTextFinishedContent !== exchange.response_text) {
      exchange.response_text = mainTextFinishedContent;
    }

    let isShareable =
      this._state.isShareable || isChatItemSuccessfulExchange({ ...exchangeWithId, ...exchange });
    // If it exists, modify the message
    this.update({
      chatHistory: this.chatHistory.map((m: ChatItem): ChatItem => {
        if (m.request_id === requestId) {
          return { ...m, ...exchange } as ChatItem;
        }
        return m;
      }),
      isShareable,
    });
    return true;
  };

  clearMessagesFromHistory = (requestIds: Set<string>) => {
    this.update({
      // No request ID = don't filter it at all
      // If there is a request ID, filter it out if it is in the set
      chatHistory: this.chatHistory.filter((m) => !m.request_id || !requestIds.has(m.request_id)),
    });
    this._extensionClient.clearMetadataFor({
      requestIds: Array.from(requestIds),
    });
  };

  clearHistory = () => {
    this._extensionClient.clearMetadataFor({
      requestIds: this.requestIds,
    });
    this.update({ chatHistory: [] });
  };

  // Clears all messages including and after this request ID
  clearHistoryFrom = async (requestId: string, inclusive: boolean = true) => {
    // Get all the request Ids to clear
    const historyFrom = this.historyFrom(requestId, inclusive);
    const requestIdsToClear = historyFrom
      .map((m) => m.request_id)
      .filter((id) => id !== undefined) as string[];

    // First clear the actual chat state
    this.update({ chatHistory: this.historyTo(requestId, !inclusive) });
    // Once it is deleted, delete the metadata
    this._extensionClient.clearMetadataFor({ requestIds: requestIdsToClear });
    // Notify any listeners
    historyFrom.forEach((m) => {
      this._onHistoryDeleteListeners.forEach((listener) => listener(m));
    });
  };

  clearMessageFromHistory = (requestId: string) => {
    this.update({
      chatHistory: this.chatHistory.filter((m) => m.request_id !== requestId),
    });
    this._extensionClient.clearMetadataFor({ requestIds: [requestId] });
  };

  /**
   * Get all messages up to and maybe including the request ID
   *
   * @param requestId The ending request ID to get the history up to
   * @param inclusive Whether to include the request ID in the history
   * @returns The history up to and maybe including the request ID
   */
  historyTo = (requestId: string, inclusive: boolean = false): ChatItem[] => {
    const idx = this.chatHistory.findIndex((m) => m.request_id === requestId);
    if (idx === -1) {
      return [];
    }
    return this.chatHistory.slice(0, inclusive ? idx + 1 : idx);
  };

  /**
   * Get all messages after and maybe including the request ID
   *
   * @param requestId The starting request ID to get the history from
   * @param inclusive Whether to include the request ID in the history
   * @returns The history after and maybe including the request ID
   */
  historyFrom = (requestId: string, inclusive: boolean = true): ChatItem[] => {
    const idx = this.chatHistory.findIndex((m) => m.request_id === requestId);
    if (idx === -1) {
      return [];
    }
    return this.chatHistory.slice(inclusive ? idx : idx + 1);
  };

  resendLastExchange = async () => {
    const lastExchange = this.lastExchange;
    if (!lastExchange || this.awaitingReply) {
      return;
    }
    return this.resendTurn(lastExchange);
  };

  resendTurn = (turn: ExchangeWithStatus): Promise<void> => {
    if (this.awaitingReply) {
      return Promise.resolve();
    }
    this._removeTurn(turn);
    return this.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      chatItemType: turn.chatItemType,
      request_message: turn.request_message,
      rich_text_json_repr: turn.rich_text_json_repr,
      status: ExchangeStatus.draft,
      mentioned_items: turn.mentioned_items,
      structured_request_nodes: turn.structured_request_nodes,
      disableSelectedCodeDetails: turn.disableSelectedCodeDetails,
      chatHistory: turn.chatHistory,
      model_id: turn.model_id,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
  };

  private _removeTurn = (turn: ExchangeWithStatus) => {
    this.update({
      chatHistory: this.chatHistory.filter(
        (m) => m !== turn && (!turn.request_id || m.request_id !== turn.request_id),
      ),
    });
  };

  public exchangeWithRequestId = (requestId: string): ExchangeWithStatus | null => {
    return (
      this.chatHistory.find((m): m is ExchangeWithStatus => m.request_id === requestId) || null
    );
  };

  get requestIds(): string[] {
    return this._state.chatHistory
      .map((m) => m.request_id)
      .filter((id) => id !== undefined) as string[];
  }

  /**
   * Whether there is a draft message in the conversation
   */
  get hasDraft(): boolean {
    const msgText: string = this.draftExchange?.request_message ?? "";
    const hasText = msgText.trim() !== "";

    // Check if there are any images in the rich text JSON
    const hasImages = this.hasImagesInDraft();

    return hasText || hasImages;
  }

  /**
   * Checks if there are any images in the draft message
   */
  private hasImagesInDraft(): boolean {
    const json = this.draftExchange?.rich_text_json_repr;
    if (!json) {
      return false;
    }

    const hasImage = (node: JSONContent | JSONContent[]): boolean => {
      if (Array.isArray(node)) {
        return node.some(hasImage);
      }
      if (!node) {
        return false;
      }
      if (node.type === "image") {
        return true;
      }
      if (node.content && Array.isArray(node.content)) {
        return node.content.some(hasImage);
      }
      return false;
    };

    return hasImage(json);
  }

  /**
   * Whether the draft message can be sent, which requires the draft to be non-empty
   * and the existing conversation to be stopped.
   */
  get canSendDraft(): boolean {
    return this.hasDraft && !this.awaitingReply;
  }

  get canCancelMessage(): boolean {
    return this.awaitingReply;
  }

  get firstExchange(): ExchangeWithStatus | null {
    const history = ConversationModel._filterToExchanges(this);
    if (history.length === 0) {
      return null;
    }
    return history[0];
  }

  get lastExchange(): ExchangeWithStatus | null {
    const history = ConversationModel._filterToExchanges(this);
    if (history.length === 0) {
      return null;
    }
    return history[history.length - 1];
  }

  get canClearHistory(): boolean {
    return this._state.chatHistory.length !== 0 && !this.awaitingReply;
  }

  // Get all exchanges that can be recovered by requesting the extension
  // or the server for the response
  get recoverableExchanges(): ExchangeWithStatus[] {
    return this._state.chatHistory.filter(
      (m): m is ExchangeWithStatus =>
        isChatItemExchangeWithStatus(m) && m.status === ExchangeStatus.sent,
    );
  }

  get successfulMessages(): ChatItem[] {
    return this._state.chatHistory.filter(
      (m) =>
        isChatItemSuccessfulExchange(m) ||
        isChatItemAgenticCheckpointDelimiter(m) ||
        isChatItemHistorySummary(m),
    );
  }

  resetTotalCharactersCache = () => {
    this._totalCharactersStore.resetCache();
  };

  /**
   * Get a readable store for the total characters count
   * This store will update whenever the chat history changes
   */
  get totalCharactersStore(): IThrottledCachedStore<number> {
    return this._totalCharactersStore;
  }

  // The current version of the history summary.
  private historySummaryVersion: number = 1;

  /**
   * Processes the chat history to add checkpoint information as separate exchanges in history.
   *
   * @param history The chat history to process
   * @returns A new chat history with checkpoint information added to the appropriate exchanges
   */
  private _convertHistoryToExchanges(history: ChatItem[]): Exchange[] {
    if (history.length === 0) {
      return [];
    }
    // Remove any history summary nodes from previous versions.
    history = history.filter(
      (m) => !isChatItemHistorySummary(m) || m.summaryVersion === this.historySummaryVersion,
    );
    // Trim the history to the last summary node
    const lastSummaryIdx = history.findLastIndex((m) => isChatItemHistorySummary(m));
    if (this._chatFlagModel.useHistorySummary && lastSummaryIdx > 0) {
      // eslint-disable-next-line no-console
      console.info("Using history summary node found at index %d", lastSummaryIdx);
      history = history.slice(lastSummaryIdx);
    }

    // Create a combined array that preserves the original order
    const combinedHistory: Exchange[] = [];

    // Process each item in the original order
    for (const item of history) {
      if (isChatItemSuccessfulExchange(item)) {
        // Add regular exchanges as they are
        combinedHistory.push(formatHistory(item));
      } else if (
        isChatItemAgenticCheckpointDelimiter(item) &&
        item.fromTimestamp !== undefined &&
        item.toTimestamp !== undefined
      ) {
        // Only create new exchanges for reversion nodes
        if (item.revertTarget) {
          // Create a dehydrated checkpoint node
          const checkpointNode = createDehydratedCheckpointNode(item, 1);

          // Create a new exchange that only contains the checkpoint node
          const newExchange: Exchange = {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "", // Empty message since this is just a checkpoint
            response_text: "", // Empty response
            request_id: item.request_id || crypto.randomUUID(),
            request_nodes: [checkpointNode],
            response_nodes: [],
            /* eslint-enable @typescript-eslint/naming-convention */
          };

          // Add the checkpoint exchange in the same position as in the original history
          combinedHistory.push(newExchange);
        }
      } else if (this._chatFlagModel.useHistorySummary && isChatItemHistorySummary(item)) {
        // Create a new exchange that only contains the history summary
        combinedHistory.push(formatHistory(item));
      }
    }

    return combinedHistory;
  }

  get awaitingReply(): boolean {
    if (this.lastExchange === null) {
      return false;
    }
    return this.lastExchange.status === ExchangeStatus.sent;
  }

  get lastInteractedAtIso(): string {
    return this._state.lastInteractedAtIso;
  }

  markSeen = async (turn: IChatItemSeenState) => {
    if (!turn.request_id) {
      return;
    }

    const item = this.chatHistory.find(
      (m): m is ExchangeWithStatus => m.request_id === turn.request_id,
    );

    if (!item) {
      return;
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const newState = { seen_state: SeenState.seen };
    this.update({
      chatHistory: this.chatHistory.map((m: ChatItem): ChatItem => {
        if (m.request_id === turn.request_id) {
          return { ...m, ...newState } as ChatItem;
        }
        return m;
      }),
    });
  };

  createStructuredRequestNodes = (
    jsonRepresentation: JSONContent,
  ): ChatRequestNode[] | undefined => {
    return this._jsonToStructuredRequest(jsonRepresentation);
  };

  saveDraftMentions = (mentions: IChatMentionable[]) => {
    if (!this.draftExchange) {
      return;
    }

    // Filter out personality mentions from context tracking
    // This prevents them from appearing as empty context chips
    const filteredMentions = mentions.filter((item) => !item.personality);
    this.update({
      draftExchange: {
        /* eslint-disable @typescript-eslint/naming-convention */
        ...this.draftExchange,
        mentioned_items: filteredMentions,
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    });
  };

  get draftActiveContextIds(): string[] | undefined {
    return this._state.draftActiveContextIds;
  }

  private saveDraftActiveContextIds = () => {
    const draftActiveContextIds: string[] = this._specialContextInputModel.recentActiveItems.map(
      (i) => i.id,
    );
    this.update({ draftActiveContextIds });
  };

  private loadDraftActiveContextIds = () => {
    const activeContextIds = new Set(this.draftActiveContextIds ?? []);
    const shouldBeActive = this._specialContextInputModel.recentItems.filter(
      (i) => activeContextIds.has(i.id) || i.recentFile || i.selection || i.sourceFolder,
    );
    const shouldNotBeActive = this._specialContextInputModel.recentItems.filter(
      (i) => !activeContextIds.has(i.id) && !i.recentFile && !i.selection && !i.sourceFolder,
    );
    // Modification time should be in reverse order, since the oldest should be updated first
    // in order to preserve the order of the context menu
    this._specialContextInputModel.markItemsActive(shouldBeActive.reverse());
    this._specialContextInputModel.markItemsInactive(shouldNotBeActive.reverse());
  };

  saveDraftExchange = (draftText: string, draftJson?: JSONContent) => {
    const diffDraftText = draftText !== this.draftExchange?.request_message;
    const diffDraftJson = draftJson !== this.draftExchange?.rich_text_json_repr;
    if (!diffDraftText && !diffDraftJson) {
      return;
    }

    const mentionedItems = this.draftExchange?.mentioned_items;
    this.update({
      draftExchange: {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: draftText,
        rich_text_json_repr: draftJson,
        mentioned_items: mentionedItems,
        status: ExchangeStatus.draft,
        /* eslint-enable @typescript-eslint/naming-convention */
      },
    });
  };

  clearDraftExchange = (): ExchangeWithStatus | undefined => {
    const draftExchange = this.draftExchange;
    this.update({ draftExchange: undefined });
    return draftExchange;
  };

  sendDraftExchange = (): boolean => {
    this._extensionClient.triggerUsedChatMetric();
    if (!this.canSendDraft || !this.draftExchange) {
      return false;
    }
    const exchange = this.clearDraftExchange();
    if (!exchange) {
      return false;
    }

    const structuredRequestNodes =
      this._chatFlagModel.enableChatMultimodal && exchange.rich_text_json_repr
        ? this._jsonToStructuredRequest(exchange.rich_text_json_repr)
        : undefined;

    this.sendExchange({
      ...exchange,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      structured_request_nodes: structuredRequestNodes,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      model_id: this.selectedModelId ?? undefined,
    })
      .then(() => {
        // Send exchange in background
        // If this is the first completed exchange, generate a summary title
        const isFirstChatExchange =
          !this.name &&
          this.chatHistory.length === 1 &&
          this.firstExchange?.request_id === this.chatHistory[0].request_id;
        const isFirstAgentOnboardingExchange =
          isAgentConversation(this) &&
          this._state.extraData?.hasAgentOnboarded &&
          filterByUserExchange(this.chatHistory).length === 2;
        if (
          this._chatFlagModel.summaryTitles &&
          (isFirstChatExchange || isFirstAgentOnboardingExchange)
        ) {
          this.updateConversationTitle();
        }
      })
      .finally(() => {
        // Report the event if this is an agent conversation
        if (isAgentConversation(this)) {
          // Use the new AgentRequestEvent instead of AgentSessionEvent
          this._extensionClient.reportAgentRequestEvent({
            eventName: AgentRequestEventName.sentUserMessage,
            conversationId: this.id,
            requestId: this.lastExchange?.request_id ?? "UNKNOWN_REQUEST_ID",
            chatHistoryLength: this.chatHistory.length,
          });
        }
      });

    // Clear focus when we send a new exchange
    this.focusModel.setFocusIdx(undefined);
    return true;
  };

  cancelMessage = async (): Promise<void> => {
    if (!this.canCancelMessage || !this.lastExchange?.request_id) {
      return;
    }

    // Set the last exchange status to cancelled
    this.updateExchangeById(
      {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        status: ExchangeStatus.cancelled,
      },
      this.lastExchange.request_id,
    );
    await this._extensionClient.cancelChatStream(this.lastExchange.request_id);
  };

  public sendInstructionExchange = async (
    instruction: string,
    selectedCodeDetails: SelectedCodeDetails,
  ): Promise<void> => {
    let exchangeId = `temp-fe-${crypto.randomUUID()}`;

    const newExchange: ExchangeWithStatus = {
      /* eslint-disable @typescript-eslint/naming-convention */
      status: ExchangeStatus.sent,
      request_id: exchangeId,
      request_message: instruction,
      model_id: this.selectedModelId ?? undefined,
      structured_output_nodes: [],
      seen_state: SeenState.unseen,
      timestamp: new Date().toISOString(),
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    this.addExchange(newExchange);

    for await (const chunk of this._extensionClient.sendInstructionMessage(
      newExchange,
      selectedCodeDetails,
    )) {
      if (!this.updateExchangeById(chunk, exchangeId, true)) {
        return;
      }
      exchangeId = chunk.request_id || exchangeId;
    }
  };

  updateConversationTitle = async () => {
    const { responseText: name } = await this.sendSummaryExchange();
    this.update({ name });
  };

  private sendSummaryExchange = () => {
    const newExchange: ExchangeWithStatus = {
      /* eslint-disable @typescript-eslint/naming-convention */
      status: ExchangeStatus.sent,
      request_message:
        "Please provide a clear and concise summary of our " +
        "conversation so far. The summary must be less than 6 words long. " +
        "The summary must contain the key points of the conversation. The " +
        "summary must be in the form of a title which will represent the " +
        "conversation. The response should not include any additional " +
        "formatting such as wrapping the response with quotation marks.",
      model_id: this.selectedModelId ?? undefined,
      chatItemType: ChatItemType.summaryTitle,
      /* eslint-enable @typescript-eslint/naming-convention */
      disableRetrieval: true,
      disableSelectedCodeDetails: true, // Prevent selected text from being included in thread titles
    };
    return this.sendSilentExchange(newExchange);
  };

  generateCommitMessage = async (): Promise<void> => {
    let exchangeId = `temp-fe-${crypto.randomUUID()}`;
    const newExchange: ExchangeWithStatus = {
      /* eslint-disable @typescript-eslint/naming-convention */
      status: ExchangeStatus.sent,
      request_id: exchangeId,
      request_message:
        "Please generate a commit message based on the diff of my staged and unstaged changes.",
      model_id: this.selectedModelId ?? undefined,
      mentioned_items: [],
      seen_state: SeenState.unseen,
      chatItemType: ChatItemType.generateCommitMessage,
      disableSelectedCodeDetails: true,
      chatHistory: [],
      timestamp: new Date().toISOString(),
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    this.addExchange(newExchange);

    for await (const chunk of this._extensionClient.generateCommitMessage()) {
      if (!this.updateExchangeById(chunk, exchangeId, true)) {
        return;
      }
      exchangeId = chunk.request_id || exchangeId;
    }
  };

  /**
   * Sends a user message to the extension.
   *
   * @param exchange The exchange to send. This is a readonly exchange --
   *  the only thing that should be overridden before we send the request
   *  is the model ID, if the model does not exist in our list of models.
   *  Everything else should be copied over from the original exchange.
   */
  sendExchange = async (
    exchange: Readonly<ExchangeWithStatus>,
    silent: boolean = false,
  ): Promise<void> => {
    this.updateLastInteraction();
    let exchangeId = `temp-fe-${crypto.randomUUID()}`;

    // Check if the model ID being sent is available -- if not,
    // we should set the model ID to undefined and use the default.
    const isModelIdValid = this._chatFlagModel.isModelIdValid(exchange.model_id);
    let modelId = isModelIdValid ? exchange.model_id : undefined;

    // Check if this is a new agent thread that needs to be converted to a real conversation
    if (this._chatFlagModel.doUseNewDraftFunctionality && ConversationModel.isNew(this._state)) {
      // Generate a new real ID for this conversation
      const newId = crypto.randomUUID();
      const oldId = this._state.id;

      try {
        // Migrate any existing checkpoints from the old ID to the new ID
        await this._extensionClient.migrateConversationId(oldId, newId);
      } catch (error) {
        console.error("Failed to migrate conversation checkpoints:", error);
      }

      // Update the current state with the new ID
      this._state = {
        ...this._state,
        id: newId,
      };

      // Update the conversation in the parent chat model with the new ID
      this._saveConversation(this._state, true);

      // Update the current conversation in the extension client
      this._extensionClient.setCurrentConversation(newId);

      // Notify subscribers of the ID change
      this._subscribers.forEach((sub) => sub(this));
    }

    // Note: we defer adding IDE state node until after we add the exchange to the history,
    // as fetching the IDE state is async and the caller may be depending on this exchange
    // being added to the history synchronously; e.g. conditionally sending an exchange
    // based on some condition on the conversation (the conversation ID, the
    // current history, etc.).
    // See AU-10041 for one category of issues and future hardening.
    exchange = _ensureTextPresentAsNode(exchange);
    let newExchange: ExchangeWithStatus = {
      /* eslint-disable @typescript-eslint/naming-convention */
      status: ExchangeStatus.sent,
      request_id: exchangeId,
      request_message: exchange.request_message,
      rich_text_json_repr: exchange.rich_text_json_repr,
      model_id: modelId,
      mentioned_items: exchange.mentioned_items,
      structured_output_nodes: exchange.structured_output_nodes,
      seen_state: SeenState.unseen,
      chatItemType: exchange.chatItemType,
      disableSelectedCodeDetails: exchange.disableSelectedCodeDetails,
      chatHistory: exchange.chatHistory,
      structured_request_nodes: exchange.structured_request_nodes,
      timestamp: new Date().toISOString(),
      /* eslint-enable @typescript-eslint/naming-convention */
    };
    this.addExchange(newExchange);
    this._loadContextFromExchange(newExchange);

    this._onSendExchangeListeners.forEach((listener) => listener(newExchange));

    // Add a history summary node if the history is too long.
    if (this._chatFlagModel.useHistorySummary) {
      // Clear up stale history nodes -- we always check that they are the right version
      // but this makes sure the history isn't littered with older history nodes.
      // TODO(arun): We should move this out of the synchronous path, maybe triggering
      // it after we get a model response to hide some latency. However, this would
      // require some careful management of asynchrony.
      this._clearStaleHistorySummaryNodes();
      await this.maybeAddHistorySummaryNode();
    }
    // Now safe to go async to add the IDE state node
    newExchange = await this._addIdeStateNode(newExchange);
    this.updateExchangeById(
      // eslint-disable-next-line @typescript-eslint/naming-convention
      { structured_request_nodes: newExchange.structured_request_nodes },
      exchangeId,
      false,
    );

    for await (const chunk of this.sendUserMessage(exchangeId, newExchange, silent)) {
      // Check if the message is still in "sent". If not, we should not update it here,
      // since it has been cancelled/updated by another source.
      if (this.exchangeWithRequestId(exchangeId)?.status !== ExchangeStatus.sent) {
        return;
      }

      if (!this.updateExchangeById(chunk, exchangeId, true)) {
        return;
      }
      exchangeId = chunk.request_id || exchangeId;
    }
  };

  sendSuggestedQuestion = (question: string) => {
    void this.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: question,
      status: ExchangeStatus.draft,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
    // Trigger the metric for using chat
    this._extensionClient.triggerUsedChatMetric();
    this._extensionClient.reportWebviewClientEvent(ChatMetricName.chatUseSuggestedQuestion);
  };

  recoverAllExchanges = async (): Promise<void> => {
    await Promise.all(this.recoverableExchanges.map(this.recoverExchange));
  };

  // Attempt to recover an exchange that was not fully loaded by the extension. Must
  // have a request_id and have been sent in order to be recoverable.
  recoverExchange = async (exchange: ExchangeWithStatus): Promise<void> => {
    if (!exchange.request_id || exchange.status !== ExchangeStatus.sent) {
      return;
    }
    let requestId = exchange.request_id;
    /* "Clear" the output nodes; we have some nodes that are injected out-of-band
       (e.g. agent memories), rather than coming from the underlying chat stream,
       which should really live elsewhere in the Exchange. We don't clear those
       here, to keep the hack alive. As of time of writing, we're clearing
       RAW_RESPONSE, SUGGESTED_QUESTIONS, MAIN_TEXT_FINISHED, TOOL_USE
    */
    const emptyStructuredOutputNodes = exchange.structured_output_nodes?.filter(
      (node) => node.type === ChatResultNodeType.AGENT_MEMORY,
    );
    this.updateExchangeById(
      // eslint-disable-next-line @typescript-eslint/naming-convention
      {
        ...exchange,
        /* eslint-disable @typescript-eslint/naming-convention */
        response_text: "",
        structured_output_nodes: emptyStructuredOutputNodes ?? [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      requestId,
    );
    for await (const chunk of this.getChatStream(exchange)) {
      if (!this.updateExchangeById(chunk, requestId, true)) {
        return;
      }
      requestId = chunk.request_id || requestId;
    }
  };

  async sendSilentExchange(exchange: {
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: string;
    model_id?: string;
    /* eslint-enable @typescript-eslint/naming-convention */
    chatItemType?: ChatItemType;
    disableRetrieval?: boolean;
    disableSelectedCodeDetails?: boolean;
    chatHistory?: Exchange[];
    // Special type of message for memories
    memoriesInfo?: MemoriesInfo;
  }): Promise<{ responseText: string; requestId: string | undefined }> {
    const exchangeId = crypto.randomUUID();
    let responseText = "";
    let requestId: string | undefined = undefined;

    const newExchange = await this._addIdeStateNode(
      _ensureTextPresentAsNode({
        ...exchange,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        request_id: exchangeId,
        status: ExchangeStatus.sent,
        timestamp: new Date().toISOString(),
      }),
    );
    for await (const chunk of this.sendUserMessage(exchangeId, newExchange, true)) {
      if (chunk.response_text) {
        responseText += chunk.response_text;
      }
      if (chunk.request_id) {
        requestId = chunk.request_id;
      }
    }
    return { responseText, requestId };
  }

  private async *getChatStream(
    exchange: ExchangeWithStatus,
  ): AsyncGenerator<Partial<ExchangeWithStatus>> {
    if (!exchange.request_id) {
      return;
    }
    yield* this._extensionClient.getExistingChatStream(exchange, {
      flags: this._chatFlagModel,
    });
    return;
  }

  private _createStreamStateHandlers(
    _requestId: string,
    _chatMessage: ChatUserMessageData,
    _opts?: { flags: IChatFlags; maxRetries?: number },
  ): StreamStateHandlerI[] {
    // Note: Order matters here. The order specified here is the order in which the handlers will be called.
    return [];
  }

  /**
   * Sends a user message to the extension and returns an async generator
   * that yields the response chunks.
   *
   * @param exchange the exchange to send
   * @returns an async generator that yields the response chunks
   */
  private async *sendUserMessage(
    requestId: string,
    exchange: ExchangeWithStatus,
    silent: boolean,
  ): AsyncGenerator<Partial<ExchangeWithStatus>> {
    const messageContext: IChatActiveContext = this._specialContextInputModel.chatActiveContext;

    let processedHistory: Exchange[];
    if (exchange.chatHistory !== undefined) {
      processedHistory = exchange.chatHistory;
    } else {
      let messageHistory = this.successfulMessages;
      if (exchange.chatItemType === ChatItemType.summaryTitle) {
        // if this is a summary title message, do not include the agent onboarding welcome
        // message as a part of the summary
        const firstUserMessage = messageHistory.findIndex((exchangeItem) => {
          return (
            exchangeItem.chatItemType !== ChatItemType.agentOnboarding &&
            isUserExchange(exchangeItem)
          );
        });
        if (firstUserMessage !== -1) {
          messageHistory = messageHistory.slice(firstUserMessage);
        }
      }
      processedHistory = this._convertHistoryToExchanges(messageHistory);
    }

    // Check if there's a personality node in the structured request nodes
    let personaType = this.personaType; // Default to conversation-level persona

    if (exchange.structured_request_nodes) {
      const personalityNode = exchange.structured_request_nodes.find(
        (node) => node.type === ChatRequestNodeType.CHANGE_PERSONALITY,
      );

      if (personalityNode && personalityNode.change_personality_node) {
        personaType = personalityNode.change_personality_node.personality_type;
      }
    }

    const data: ChatUserMessageData = {
      text: exchange.request_message,
      chatHistory: processedHistory,
      silent,
      modelId: exchange.model_id,
      context: messageContext,
      userSpecifiedFiles: messageContext.userSpecifiedFiles,
      externalSourceIds: messageContext.externalSources?.map((s) => s.id),
      disableRetrieval: exchange.disableRetrieval ?? false,
      disableSelectedCodeDetails: exchange.disableSelectedCodeDetails ?? false,
      nodes: exchange.structured_request_nodes,
      memoriesInfo: exchange.memoriesInfo,
      personaType: personaType,
      conversationId: this.id,
      createdTimestamp: Date.now(),
    };

    const streamStateHandlers = this._createStreamStateHandlers(requestId, data, {
      flags: this._chatFlagModel,
    });

    // Use the retry-capable method instead of the regular one
    const chatStream = this._extensionClient.startChatStreamWithRetry(requestId, data, {
      flags: this._chatFlagModel,
    });
    for await (const chunk of chatStream) {
      let returnChunk = chunk;
      for (const handler of streamStateHandlers) {
        returnChunk = handler.handleChunk(returnChunk) ?? returnChunk;
      }
      yield returnChunk;
    }
    for (const handler of streamStateHandlers) {
      yield* handler.handleComplete();
    }
    return;
  }

  /**
   * Context Loading and Unloading
   *
   * The following methods handle the loading and unloading of context for conversations and exchanges.
   * This process is crucial for maintaining the state of the special context input model, which
   * keeps track of mentioned items (such as files, selections, or other contextual information)
   * throughout a conversation.
   *
   * Why is this necessary?
   * 1. State Management: As users switch between conversations or review past exchanges, we need
   *    to ensure that the context (mentioned items) is accurately reflected in the UI and available
   *    for future interactions.
   * 2. Performance: By loading context only when needed and unloading it when not in use, we can
   *    optimize memory usage and improve application performance.
   * 3. Consistency: This approach ensures that the context shown to the user always matches the
   *    current conversation or exchange being viewed.
   * 4. Feature Support: It enables features like context-aware suggestions and allows the AI to
   *    reference previously mentioned items accurately in its responses.
   *
   * The loading process adds items to the context, while the unloading process removes them.
   * This bidirectional flow allows for seamless transitions between different conversations
   * and exchanges while maintaining contextual integrity.
   */

  /**
   * Loads context from a conversation by iterating through its chat history
   * and loading context from each exchange.
   * @param conversation - The conversation to load context from
   */
  private _loadContextFromConversation = (conversation: IConversation) => {
    conversation.chatHistory.forEach((i: ChatItem) => {
      if (isChatItemExchangeWithStatus(i)) {
        this._loadContextFromExchange(i);
      }
    });
  };

  /**
   * Loads context from a single exchange by updating and marking items as active
   * in the special context input model.
   * @param exchange - The exchange to load context from
   */
  private _loadContextFromExchange = (exchange: ExchangeWithStatus): void => {
    if (!exchange.mentioned_items) {
      return;
    }
    this._specialContextInputModel.updateItems(exchange.mentioned_items, []);
    this._specialContextInputModel.markItemsActive(exchange.mentioned_items);
  };

  /**
   * Unloads context from a conversation by iterating through its chat history
   * and unloading context from each exchange.
   * @param conversation - The conversation to unload context from
   */
  private _unloadContextFromConversation = (conversation: IConversation) => {
    conversation.chatHistory.forEach((i: ChatItem) => {
      if (isChatItemExchangeWithStatus(i)) {
        this._unloadContextFromExchange(i);
      }
    });
  };

  /**
   * Unloads context from a single exchange by updating the special context input model
   * to remove the mentioned items.
   * @param exchange - The exchange to unload context from
   */
  private _unloadContextFromExchange = (exchange: ExchangeWithStatus): void => {
    if (!exchange.mentioned_items) {
      return;
    }
    this._specialContextInputModel.updateItems([], exchange.mentioned_items);
  };

  onSendExchange(fn: (exchange: ExchangeWithStatus) => void): () => void {
    this._onSendExchangeListeners.push(fn);
    return () => {
      this._onSendExchangeListeners = this._onSendExchangeListeners.filter(
        (listener) => listener !== fn,
      );
    };
  }

  onNewConversation(listener: () => void) {
    this._onNewConversationListeners.push(listener);
    return () => {
      this._onNewConversationListeners = this._onNewConversationListeners.filter(
        (l) => l !== listener,
      );
    };
  }

  onHistoryDelete(listener: (chatItem: ChatItem) => void) {
    this._onHistoryDeleteListeners.push(listener);
    return () => {
      this._onHistoryDeleteListeners = this._onHistoryDeleteListeners.filter((l) => l !== listener);
    };
  }

  /**
   * Updates a chat item with the given requestId with the provided partial item data
   *
   * @param requestId - The ID of the request to update
   * @param item - The partial item data to merge with the existing item
   * @returns true if the item was found and updated, false otherwise
   */
  updateChatItem(requestId: string, item: Partial<ChatItem>) {
    // See if the exchange exists
    const itemWithId = this.chatHistory.find((m) => m.request_id === requestId);
    if (itemWithId === null) {
      console.warn("No exchange with this request ID found.");
      return false;
    }

    // If it exists, modify the message
    this.update({
      chatHistory: this.chatHistory.map((m: ChatItem): ChatItem => {
        if (m.request_id === requestId) {
          return { ...m, ...item } as ChatItem;
        }
        return m;
      }),
    });
    return true;
  }

  private updateLastInteraction = () => {
    this.update({ lastInteractedAtIso: new Date().toISOString() });
  };

  /**
   * Convert a rich text JSON representation of the chat input into a structured chat request node
   *
   * Line breaks and white space are preserved. Multiple consecutive text nodes are combined into a single
   * text node.
   */
  private _jsonToStructuredRequest = (json: JSONContent): ChatRequestNode[] => {
    const nodes: ChatRequestNode[] = [];
    // append content to the last text node, or push a new node if there is no text node
    const appendContentOrPushNode = (content: string) => {
      const lastNode = nodes.at(-1);
      if (lastNode?.type === ChatRequestNodeType.TEXT) {
        const lastNodeContent = lastNode.text_node?.content ?? "";
        const newNode = {
          ...lastNode,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: { content: lastNodeContent + content },
        };
        nodes[nodes.length - 1] = newNode;
      } else {
        nodes.push({
          id: nodes.length,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: { content },
        });
      }
    };
    // traverse the JSON representation of the rich text editor tree
    const traverse = (node: JSONContent) => {
      if (node.type === "doc" || node.type === "paragraph") {
        for (const contentNode of node.content ?? []) {
          traverse(contentNode);
        }
      } else if (node.type === "hardBreak") {
        appendContentOrPushNode("\n");
      } else if (node.type === "text") {
        appendContentOrPushNode(node.text ?? "");
      } else if (node.type === "image") {
        // The image src is the file name stored in the user's asset manager
        if (typeof node.attrs?.src !== "string") {
          console.error("Image source is not a string: ", node.attrs?.src);
          return;
        }
        if (node.attrs.isLoading) {
          // If the node is in a loading state, do not send it
          return;
        }
        const fileName = node.attrs?.title;
        const imageFormat = this._fileNameToImageFormat(fileName);

        nodes.push({
          id: nodes.length,
          type: ChatRequestNodeType.IMAGE_ID,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          image_id_node: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            image_id: node.attrs.src,
            format: imageFormat,
          },
        });
      } else if (node.type === "mention") {
        const mentionData = node.attrs?.data;

        // Check if this is a personality mention
        if (mentionData && isItemPersonality(mentionData)) {
          // Push a new text node with the personality prompt
          // If we want this to actually change modes, we need to turn this
          // into a CHANGE_PERSONALITY node.
          nodes.push({
            id: nodes.length,
            type: ChatRequestNodeType.TEXT,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            text_node: {
              content: getPersonalityPrompt(this._chatFlagModel, mentionData.personality.type),
            },
          });
        } else {
          // Show the mention as a code span delimited by an @ beforehand
          const mentionPath = `@\`${mentionData?.name ?? mentionData?.id}\``;
          appendContentOrPushNode(mentionPath);
        }
      }
    };
    traverse(json);
    return nodes;
  };

  /**
   * Convert a file name to an ImageFormatType enum value
   *
   * @param fileName the file name including its extension
   */
  private _fileNameToImageFormat(fileName: string): ImageFormatType {
    const extension = fileName.split(".").at(-1)?.toLowerCase();
    switch (extension) {
      case "jpeg":
      case "jpg":
        return ImageFormatType.JPEG;
      case "png":
        return ImageFormatType.PNG;
      case "gif":
        return ImageFormatType.GIF;
      case "webp":
        return ImageFormatType.WEBP;
      default:
        return ImageFormatType.IMAGE_FORMAT_UNSPECIFIED;
    }
  }

  private async _addIdeStateNode(exchange: ExchangeWithStatus): Promise<ExchangeWithStatus> {
    // We'll replace any existing ide state node with a new one.
    let nodes = (exchange.structured_request_nodes ?? []).filter(
      (node) => node.type !== ChatRequestNodeType.IDE_STATE,
    );

    // Only add a new IDE state node if one doesn't already exist
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const ide_state_node = await this._extensionClient.getChatRequestIdeState();

    // NOTE(arun): This is some defensive programming because we're trying to address
    // an issue where we seem to send invalid chat requests to the backend.
    if (!ide_state_node) {
      return exchange;
    }

    nodes = [
      ...nodes,
      {
        id: getMaxNodeId(nodes) + 1,
        type: ChatRequestNodeType.IDE_STATE as number,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        ide_state_node,
      },
    ];
    // eslint-disable-next-line @typescript-eslint/naming-convention
    return { ...exchange, structured_request_nodes: nodes };
  }

  /**
   * Creates a history summary node by calling the chat model to summarize the provided
   * exchanges.
   * @returns A promise that resolves to a ChatRequestNode containing the summary
   */
  public async maybeAddHistorySummaryNode(): Promise<boolean> {
    const summarizationPrompt = this._chatFlagModel.historySummaryPrompt;
    if (!summarizationPrompt || summarizationPrompt.trim() === "") {
      return false;
    }

    const historyExchanges = this._convertHistoryToExchanges(this.chatHistory);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [rest, _] = splitChatHistoryByCharLimit(
      historyExchanges,
      this._chatFlagModel.historySummaryLowerChars,
      this._chatFlagModel.historySummaryMaxChars,
    );

    if (rest.length === 0) {
      // There's nothing to summarize here!
      return false;
    }

    // If rest ends with a tool use, then split that out so we can include in the
    // summarized request.
    let lastResponseNodes = rest.at(-1)?.response_nodes ?? [];
    let lastToolUses: ChatResultNode[] = lastResponseNodes.filter(
      (node) => node.type === ChatResultNodeType.TOOL_USE,
    );
    if (lastToolUses.length > 0) {
      rest.at(-1)!.response_nodes = lastResponseNodes.filter(
        (node) => node.type !== ChatResultNodeType.TOOL_USE,
      );
    }

    // eslint-disable-next-line no-console
    console.info("Summarizing %d turns of conversation history.", rest.length);

    // Call the model to generate the summary
    const { responseText, requestId } = await this.sendSilentExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: summarizationPrompt,
      // Make sure we send the message with history.
      disableRetrieval: true,
      disableSelectedCodeDetails: true,
      chatHistory: rest,
      // TODO(arun): also disable tool calling.
      /* eslint-enable @typescript-eslint/naming-convention */
    });

    const historySummaryItem: HistorySummaryMessage = {
      /* eslint-disable @typescript-eslint/naming-convention */
      chatItemType: ChatItemType.historySummary,
      summaryVersion: this.historySummaryVersion,
      // NOTE(arun): By using the history request id here, we can trace what summarized
      // in RI.
      request_id: requestId,
      request_message: summarizationPrompt,
      response_text: responseText,
      structured_output_nodes: [
        {
          id: lastToolUses.map((node) => node.id).reduce((acc, id) => Math.max(acc, id), -1) + 1,
          type: ChatResultNodeType.RAW_RESPONSE,
          content: responseText,
        },
        ...lastToolUses,
      ],
      status: ExchangeStatus.success,
      seen_state: SeenState.seen,
      timestamp: new Date().toISOString(),
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    // Insert the history summary node right after the last message in rest.
    const index = this.chatHistory.findIndex((m) => m.request_id === rest.at(-1)!.request_id) + 1;

    // eslint-disable-next-line no-console
    console.info("Adding a history summary node at index %d", index);

    // Splice in the new summary node into history.
    const chatHistory = [...this._state.chatHistory];
    chatHistory.splice(index, 0, historySummaryItem);
    this.update({ chatHistory });
    return true;
  }

  private _clearStaleHistorySummaryNodes() {
    this.update({
      chatHistory: this.chatHistory.filter(
        (m) => !isChatItemHistorySummary(m) || m.summaryVersion === this.historySummaryVersion,
      ),
    });
  }
}

/**
 * Creates a dehydrated checkpoint node from a checkpoint delimiter
 * This node can be hydrated on the extension-side with actual diff information
 *
 * @param checkpoint The checkpoint delimiter to convert to a node
 * @param id The node ID should be (typically the max ID of the existing nodes + 1)
 * @returns A ChatRequestNode of type CHECKPOINT_REF with timestamp information
 */
export function createDehydratedCheckpointNode(checkpoint: ChatItem, id: number): ChatRequestNode {
  // Get the timestamps from the checkpoint
  const fromTimestamp = isChatItemAgenticCheckpointDelimiter(checkpoint)
    ? checkpoint.fromTimestamp
    : (checkpoint as any).fromTimestamp;

  const toTimestamp = isChatItemAgenticCheckpointDelimiter(checkpoint)
    ? checkpoint.toTimestamp
    : (checkpoint as any).toTimestamp;

  // Check if this is a reversion checkpoint
  const isRevert =
    isChatItemAgenticCheckpointDelimiter(checkpoint) && checkpoint.revertTarget !== undefined;

  return {
    id,
    type: ChatRequestNodeType.CHECKPOINT_REF,
    /* eslint-disable @typescript-eslint/naming-convention */
    checkpoint_ref_node: {
      request_id: checkpoint.request_id || "",
      from_timestamp: fromTimestamp,
      to_timestamp: toTimestamp,
      source: isRevert ? EditEventSource.CHECKPOINT_REVERT : undefined,
    },
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/**
 * Adds checkpoint information as structured nodes to an exchange
 *
 * @param exchange The exchange to add checkpoint information to
 * @param checkpoints The checkpoints to add to the exchange
 * @returns A new exchange with checkpoint information added as structured nodes
 */
export function addCheckpointsToExchange(
  exchange: ExchangeWithStatus,
  checkpoints: ChatItem[],
): ExchangeWithStatus {
  if (!exchange || checkpoints.length === 0) {
    return exchange;
  }

  // Get existing request nodes
  let requestNodes = exchange.structured_request_nodes ?? [];

  // Add each checkpoint as a dehydrated node
  for (const checkpoint of checkpoints) {
    if (
      isChatItemAgenticCheckpointDelimiter(checkpoint) &&
      checkpoint.fromTimestamp !== undefined &&
      checkpoint.toTimestamp !== undefined
    ) {
      // Create a dehydrated checkpoint node
      const dehydratedCheckpointNode = createDehydratedCheckpointNode(
        checkpoint,
        getMaxNodeId(requestNodes) + 1,
      );

      // Add the dehydrated checkpoint node to the request nodes
      requestNodes = [...requestNodes, dehydratedCheckpointNode];
    }
  }

  // Return a new exchange with the updated request nodes
  return {
    ...exchange,
    // eslint-disable-next-line @typescript-eslint/naming-convention
    structured_request_nodes: requestNodes,
  };
}

export function formatHistory(m: ExchangeWithStatus): Exchange {
  const structuredOutputNodesForHistory = (m.structured_output_nodes ?? [])
    .filter(
      (node) =>
        node.type === ChatResultNodeType.RAW_RESPONSE ||
        node.type === ChatResultNodeType.TOOL_USE ||
        node.type === ChatResultNodeType.TOOL_USE_START,
    )
    .map((node) => {
      if (node.type === ChatResultNodeType.TOOL_USE_START) {
        return {
          /* eslint-disable @typescript-eslint/naming-convention */

          ...node,
          tool_use: {
            ...node.tool_use!,
            input_json: "{}",
          },
          type: ChatResultNodeType.TOOL_USE,
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      }
      return node;
    });

  return {
    /* eslint-disable @typescript-eslint/naming-convention */
    request_message: m.request_message,
    response_text: m.response_text ?? "",
    request_id: m.request_id || "",
    request_nodes: m.structured_request_nodes ?? [],
    response_nodes: structuredOutputNodesForHistory,
    /* eslint-enable @typescript-eslint/naming-convention */
  };
}

/* Get the max node id in the request nodes so far. */
function getMaxNodeId(nodes: ChatRequestNode[]) {
  return nodes.length > 0 ? Math.max(...nodes.map((n) => n.id)) : 0;
}

/**
 * Ensures that we convert any text in request message into a node.
 *
 * NOTE(arun): Typically for rich text inputs, we create nodes using
 * `_jsonToStructuredRequest`, but don't do so in other callsites. Ideally, we should
 * fix this upstream, making sure that all callers of `sendExchange` provide request
 * nodes. However, I don't understand this code well enough and was under too much time
 * pressure To Do It Right.
 */
function _ensureTextPresentAsNode(exchange: Readonly<ExchangeWithStatus>): ExchangeWithStatus {
  if (
    exchange.request_message.length > 0 &&
    !exchange.structured_request_nodes?.some((node) => node.type === ChatRequestNodeType.TEXT)
  ) {
    // Now that we are sending IDE_STATE nodes, we have to send any text messages as
    // TEXT nodes.
    let nodes = exchange.structured_request_nodes ?? [];
    nodes = [
      ...nodes,
      {
        id: getMaxNodeId(nodes) + 1,
        type: ChatRequestNodeType.TEXT,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        text_node: {
          content: exchange.request_message,
        },
      },
    ];
    // eslint-disable-next-line @typescript-eslint/naming-convention
    return { ...exchange, structured_request_nodes: nodes };
  }
  return exchange;
}

/**
 * The first tool wins.  Otherwise the first tool_use_start wins, otherwise its undefined.
 * @param nodes
 * @returns
 */

function toolOrToolStart(nodes: ChatResultNode[] = []): ChatResultNode | undefined {
  let toolStart: ChatResultNode | undefined;
  for (const node of nodes) {
    if (node.type === ChatResultNodeType.TOOL_USE) {
      return node;
    } else if (node.type === ChatResultNodeType.TOOL_USE_START) {
      toolStart = node;
    }
  }

  return toolStart;
}

<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import OpenFileButton from "../chat/components/conversation/blocks/tools/components/OpenFileButton.svelte";
  import SuccessfulButton from "../chat/components/buttons/SuccessfulButton.svelte";
  import ArrowRight from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-right.svg?component";

  import { ExtensionClient } from "../chat/extension-client";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ChatFlagsModel } from "../chat/models/chat-flags-model";
  import MarkdownEditor from "$common-webviews/src/design-system/components/MarkdownEditor.svelte";
  import {
    AgentSessionEventName,
    MemoriesMoveTarget,
  } from "@augment-internal/sidecar-libs/src/metrics/types";
  import type { Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import RulesDropdown from "./RulesDropdown.svelte";

  export let text: string;
  export let path: string;

  // Create extension client directly
  const msgBroker = new MessageBroker(host);
  const flagsModel = new ChatFlagsModel();
  const extensionClient = new ExtensionClient(host, msgBroker, flagsModel);

  // Initialize the flags when the component mounts
  async function initializeFlags() {
    try {
      const chatInitData = await extensionClient.getChatInitData();
      flagsModel.update({
        enableRules: chatInitData.enableRules ?? false,
        enableDebugFeatures: chatInitData.enableDebugFeatures ?? false,
      });
    } catch (error) {
      console.error("Failed to initialize flags:", error);
    }
  }

  // Call initialization when component mounts
  initializeFlags();

  $: enableRules = $flagsModel.enableRules;

  // Track text selection state
  let selectedText = "";
  let selectionStart = 0;
  let selectionEnd = 0;

  // Create a debounced save function to avoid excessive file writes
  const onSave = async () => {
    if (path) {
      // Use the client-workspaces/writeFile message type to save the file
      extensionClient.saveFile({ repoRoot: "", pathName: path, content: text });
    }
  };

  // Function to move selected text to a destination with confirmation
  async function moveSelectedText(
    destination: "userGuidelines" | "augmentGuidelines" | { type: "rule"; rule: Rule },
  ) {
    if (!selectedText) {
      return undefined;
    }

    // Determine confirmation message and target based on destination
    let confirmationTitle: string;
    let confirmationMessage: string;
    let moveTarget: MemoriesMoveTarget;
    const truncatedSelectedText = selectedText.slice(0, 20);

    if (destination === "userGuidelines") {
      confirmationTitle = "Move Content to User Guidelines";
      confirmationMessage = `Are you sure you want to move the selected content "${truncatedSelectedText}" to your user guidelines?`;
      moveTarget = MemoriesMoveTarget.userGuidelines;
    } else if (destination === "augmentGuidelines") {
      confirmationTitle = "Move Content to Workspace Guidelines";
      confirmationMessage = `Are you sure you want to move the selected content "${truncatedSelectedText}" to workspace guidelines?`;
      moveTarget = MemoriesMoveTarget.augmentGuidelines;
    } else {
      confirmationTitle = "Move Content to Rule";
      confirmationMessage = `Are you sure you want to move the selected content "${truncatedSelectedText}" to rule file "${destination.rule.path}"?`;
      moveTarget = MemoriesMoveTarget.rules;
    }

    // Show confirmation modal before moving
    const confirmed = await extensionClient.openConfirmationModal({
      title: confirmationTitle,
      message: confirmationMessage,
      confirmButtonText: "Move",
      cancelButtonText: "Cancel",
    });

    if (!confirmed) {
      // User cancelled, don't proceed with the move
      return undefined;
    }

    // Perform the move operation based on destination
    if (destination === "userGuidelines") {
      extensionClient.updateUserGuidelines(selectedText);
    } else if (destination === "augmentGuidelines") {
      extensionClient.updateWorkspaceGuidelines(selectedText);
    } else {
      extensionClient.updateRuleFile(destination.rule.path, selectedText);
    }

    // Remove the selected text from the current content
    const newText = text.substring(0, selectionStart) + text.substring(selectionEnd);
    text = newText;

    // Save the current memories file
    await onSave();

    // Report the metric
    extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.memoriesMove,
      conversationId: "",
      eventData: {
        memoriesMoveData: {
          target: moveTarget,
        },
      },
    });

    return "success";
  }

  // Function to handle rule selection from dropdown
  async function handleRuleSelection(rule: Rule) {
    await moveSelectedText({ type: "rule", rule });
  }
</script>

<MarkdownEditor
  bind:selectedText
  bind:selectionStart
  bind:selectionEnd
  bind:value={text}
  saveFunction={onSave}
  variant="surface"
  size={2}
  resize="vertical"
  class="markdown-editor"
>
  <div class="l-file-controls" slot="header">
    <div class="l-file-controls-left">
      <div class="c-move-text-btn">
        <SuccessfulButton
          tooltip={{
            neutral: "Move highlighted text to user guidelines",
            success: "Text moved to user guidelines",
          }}
          stateVariant={{ success: "solid", neutral: "soft" }}
          defaultColor="neutral"
          onClick={() => moveSelectedText("userGuidelines")}
          disabled={!selectedText}
          stickyColor={false}
          persistOnTooltipClose
          replaceIconOnSuccess
          size={1}
        >
          <div slot="iconLeft" class="c-move-text-btn__left_icon">
            <ArrowRight />
          </div>
          User Guidelines
        </SuccessfulButton>
      </div>
      {#if enableRules}
        <div class="c-move-text-btn">
          <RulesDropdown onRuleSelected={handleRuleSelection} disabled={!selectedText} />
        </div>
      {/if}
    </div>
    <div class="l-file-controls-right">
      <OpenFileButton
        size={1}
        {path}
        variant="soft"
        onOpenLocalFile={async () => {
          extensionClient.openFile({
            repoRoot: "",
            pathName: path,
          });
          return "success";
        }}
      >
        <TextAugment slot="text" size={1}>Open Memories</TextAugment>
      </OpenFileButton>
    </div>
  </div>
</MarkdownEditor>

<style>
  :global(.l-markdown-editor) {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .l-file-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-2);
    min-height: 32px;
    flex-shrink: 0; /* Prevent the controls from shrinking */
    padding-bottom: var(--ds-spacing-1);
    overflow: hidden; /* Hide overflow */
  }

  .l-file-controls-left {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
    overflow: hidden; /* Hide overflow */
    flex-shrink: 1; /* Allow left controls to shrink */
    min-width: 0; /* Allow flexbox to shrink below content size */
  }

  .l-file-controls-right {
    display: flex;
    flex-direction: row;
    flex-shrink: 0; /* Prevent right controls from shrinking */
  }
  .c-move-text-btn__left_icon {
    display: flex;
    gap: var(--ds-spacing-1);
    align-items: center;
    height: var(--button-icon-size);
  }

  .c-move-text-btn {
    flex-shrink: 1; /* Allow buttons to shrink */
    min-width: 0; /* Allow flexbox to shrink below content size */
    overflow: hidden;
  }

  .c-move-text-btn :global(.c-base-btn) {
    display: flex;
    flex-direction: column;
    width: 100%; /* Take full width of container */
  }

  .c-move-text-btn :global(.c-button--text) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
</style>

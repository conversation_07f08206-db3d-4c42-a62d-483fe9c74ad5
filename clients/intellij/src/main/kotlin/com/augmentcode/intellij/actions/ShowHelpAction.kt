package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware

class ShowHelpAction : AnAction(AugmentBundle.message("actions.help")), DumbAware {
  override fun actionPerformed(e: AnActionEvent) {
    BrowserUtil.browse("https://docs.augmentcode.com")
  }
}

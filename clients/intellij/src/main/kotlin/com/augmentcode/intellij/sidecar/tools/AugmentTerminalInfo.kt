package com.augmentcode.intellij.sidecar.tools

import com.intellij.execution.OutputListener
import com.intellij.execution.process.ProcessHandler
import com.intellij.execution.ui.RunContentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key

internal class AugmentTerminalInfo(
  val toolUseId: String,
  val terminalId: Long,
  val command: String,
  val workingDirectory: String,
) : OutputListener() {
  companion object {
    val TERMINAL_INFO_KEY = Key.create<AugmentTerminalInfo>("augment-terminal-info")

    fun findActiveTerminal(project: Project): AugmentTerminalInfo? {
      val runManager = RunContentManager.getInstance(project)
      // first check if there is an active terminal and if it's an augment terminal
      val selectedTerminal = runManager.selectedContent?.processHandler?.getUserData(TERMINAL_INFO_KEY)
      if (selectedTerminal != null) {
        return selectedTerminal
      }
      // otherwise, check if there is any augment terminal
      return runManager.allDescriptors.mapNotNull {
        it.processHandler?.getUserData(TERMINAL_INFO_KEY)
      }.firstOrNull()
    }
  }

  fun attach(processHandler: ProcessHandler) {
    TERMINAL_INFO_KEY.set(processHandler, this)
    processHandler.addProcessListener(this)
  }
}

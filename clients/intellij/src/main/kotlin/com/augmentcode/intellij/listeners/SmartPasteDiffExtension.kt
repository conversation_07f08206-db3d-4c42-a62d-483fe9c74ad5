package com.augmentcode.intellij.listeners

import com.augmentcode.api.SmartPasteResolution
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.chat.SMART_PASTE_RESOLUTION_KEY
import com.augmentcode.intellij.utils.metricTime
import com.intellij.diff.DiffContext
import com.intellij.diff.DiffExtension
import com.intellij.diff.FrameDiffTool.DiffViewer
import com.intellij.diff.requests.DiffRequest
import com.intellij.diff.tools.fragmented.UnifiedDiffViewer
import com.intellij.diff.tools.simple.SimpleDiffViewer
import com.intellij.diff.tools.util.base.DiffViewerBase
import com.intellij.diff.tools.util.base.DiffViewerListener
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.Key
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap

/**
 * A diff extension for listening to changes in the diff viewer
 * during a smart paste request.
 */
class SmartPasteDiffExtension : DiffExtension() {
  companion object {
    const val DISPOSAL_DELAY_MS = 500L
  }

  private val cs = CoroutineScope(Dispatchers.Default)
  private val pendingResolutions = ConcurrentHashMap<String, Job>()

  override fun onViewerCreated(
    viewer: DiffViewer,
    context: DiffContext,
    request: DiffRequest,
  ) {
    if (viewer !is DiffViewerBase) return
    // If we didn't create this diff viewer, then we don't care about it
    val resolution = request.getUserData(SMART_PASTE_RESOLUTION_KEY) ?: return

    // Cancel any pending resolution for this request
    pendingResolutions.remove(resolution.requestId)?.cancel()

    viewer.addListener(SmartPasteDiffListener(viewer, request, resolution))

    Disposer.register(viewer) {
      val disposalTimeMs = System.currentTimeMillis()
      val job =
        cs.launch {
          // When you toggle between unified and split diff views the diff viewer is disposed and recreated.
          // We use a delay to wait a short time to see if another viewer was created for this request. If not,
          // then we log the resolution.
          delay(DISPOSAL_DELAY_MS)
          if (pendingResolutions.remove(resolution.requestId) != null) {
            val (resolveSec, resolveNs) = metricTime(disposalTimeMs)
            resolution.resolveTimeSec = resolveSec
            resolution.resolveTimeNsec = resolveNs
            AugmentAPI.instance.reportSmartPasteResolution(resolution)
          }
        }
      pendingResolutions[resolution.requestId] = job
    }
  }
}

val CHUNKS_KEY = Key.create<Int>("augment.smart.paste.chunks.key")

class SmartPasteDiffListener(
  private val viewer: DiffViewer,
  private val request: DiffRequest,
  private val resolutionContainer: SmartPasteResolution,
) : DiffViewerListener() {
  private val logger = thisLogger()

  private fun getNumChunks(): Int =
    when (viewer) {
      is SimpleDiffViewer -> {
        viewer.diffChanges.size
      }
      is UnifiedDiffViewer -> {
        viewer.diffChanges?.size ?: 0
      }
      else -> {
        0
      }
    }

  override fun onAfterRediff() {
    super.onAfterRediff()
    val numChunks = getNumChunks()
    if (request.getUserData(CHUNKS_KEY) == null) {
      request.putUserData(CHUNKS_KEY, numChunks)
      resolutionContainer.isAcceptedChunks = BooleanArray(numChunks)
    } else {
      // This is a bit of a hack. It's hard to hook into the IntelliJ diff viewer
      // to accurately determine exactly which chunks are accepted vs rejected.
      // But what we really care about is percentage accepted.
      // So instead of adding complex logic to track accepted vs rejected diffChanges,
      // we just give a fake list that is accurate in the percentage accepted but not
      // the exact chunks that were accepted or rejected.
      resolutionContainer.isAcceptedChunks.fill(true)

      for (i in 0 until numChunks) {
        if (i >= resolutionContainer.isAcceptedChunks.size) {
          logger.warn("Attempted to access index $i but array size is ${resolutionContainer.isAcceptedChunks.size}")
          break
        }
        // If a chunk exists, then it has not yet been accepted
        resolutionContainer.isAcceptedChunks[i] = false
      }
    }
  }
}

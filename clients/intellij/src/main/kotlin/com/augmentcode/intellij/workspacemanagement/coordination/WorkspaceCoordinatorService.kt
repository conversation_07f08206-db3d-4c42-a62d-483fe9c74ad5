package com.augmentcode.intellij.workspacemanagement.coordination

import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.workspacemanagement.coordination.steps.BatchFileUploader
import com.augmentcode.intellij.workspacemanagement.coordination.steps.CoordinationFileDetails
import com.augmentcode.intellij.workspacemanagement.coordination.steps.FileFilterStep
import com.augmentcode.intellij.workspacemanagement.coordination.steps.FileToUpload
import com.augmentcode.intellij.workspacemanagement.indexing.WORKSPACE_INDEX_ID
import com.augmentcode.intellij.workspacemanagement.utils.RoughlySizedChannel
import com.augmentcode.intellij.workspacemanagement.utils.isV3IndexingEnabled
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import org.jetbrains.annotations.VisibleForTesting
import java.nio.file.Path

@Service(Service.Level.PROJECT)
class WorkspaceCoordinatorService(private val project: Project, scope: CoroutineScope) : Disposable {
  companion object {
    fun getInstance(project: Project): WorkspaceCoordinatorService {
      return project.service<WorkspaceCoordinatorService>()
    }

    private const val UPLOAD_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD = 10_000 // Placeholder. Tune this.
    private const val WAIT_FOR_INDEX_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD = 10_000 // Placeholder. Tune this.
  }

  private val logger = thisLogger()

  // These channels should be given to the processors as input/output channels.
  @VisibleForTesting
  internal val intakeChannel = RoughlySizedChannel<Path>(Channel(Channel.UNLIMITED))
  internal val filteredFilesChannel = RoughlySizedChannel<CoordinationFileDetails>(Channel(Channel.UNLIMITED))

  // todo: initialProbeChannel
  private val uploadChannel = RoughlySizedChannel<FileToUpload>(Channel(Channel.UNLIMITED))
  private val waitForIndexChannel = RoughlySizedChannel<AugmentBlobState>(Channel(Channel.UNLIMITED))
  // todo: checkpointChannel

  // Adding a channel to this config ensures the channel is monitored and disposed of.
  private val channelConfigs =
    listOf(
      ChannelMonitorConfig(
        "Intake",
        intakeChannel,
        null,
      ),
      ChannelMonitorConfig(
        "FilteredFiles",
        filteredFilesChannel,
        null,
      ),
      ChannelMonitorConfig(
        "Upload",
        uploadChannel,
        UPLOAD_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD,
      ),
      ChannelMonitorConfig(
        "WaitForIndex",
        waitForIndexChannel,
        WAIT_FOR_INDEX_CHANNEL_BACKPRESSURE_WARNING_THRESHOLD,
      ),
    )

  // Steps
  private val fileFilterStep = FileFilterStep(project, scope, intakeChannel, filteredFilesChannel)
  private val batchFileUploader: BatchFileUploader = BatchFileUploader(scope, uploadChannel, waitForIndexChannel)

  init {
    // Register the processing steps for disposal
    Disposer.register(this, fileFilterStep)
    Disposer.register(this, batchFileUploader)
    // todo: waitForIndex job
    // todo: checkpoint job

    // When the user signs in/out, we need to ensure we trigger a re-index
    PluginStateService.instance.subscribe(
      project.messageBus.connect(this),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext,
          state: PluginState,
        ) {
          if (state == PluginState.ENABLED) {
            syncWorkspace()
            return
          }
        }
      },
      triggerOnStateChange = true,
    )

    val channelMonitor = ChannelMonitor(scope, channelConfigs)
    Disposer.register(this, channelMonitor)
  }

  /**
   * When the service is disposed we should close all channels.
   */
  override fun dispose() {
    // Close the channels
    channelConfigs.forEach { it.channel.close() }
  }

  /**
   * Enqueues a file for processing. This is the intake for any files that we may want to upload and index.
   *
   * IMPORTANT: This method is called by the WorkspaceIndexer, so must be quick.
   */
  fun enqueueFileForProcessing(filePath: Path) {
    if (!isV3IndexingEnabled()) return

    val result = intakeChannel.trySend(filePath)
    if (result.isFailure) {
      logger.warn("Failed to enqueue file for processing: $filePath")
    }
  }

  fun syncWorkspace() {
    if (!isV3IndexingEnabled()) {
      logger.info("Skipping v3 indexing pipeline")
      return
    }

    // Start the processing steps
    fileFilterStep.startProcessing()

    logger.info("Syncing users workspace with Augment")
    FileBasedIndex.getInstance().requestRebuild(WORKSPACE_INDEX_ID)
  }
}

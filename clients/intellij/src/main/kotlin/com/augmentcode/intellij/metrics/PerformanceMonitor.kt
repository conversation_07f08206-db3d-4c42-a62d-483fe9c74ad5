package com.augmentcode.intellij.metrics

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.ProjectManager

/**
 * Monitors and reports performance metrics from webviews.
 *
 * This class handles performance monitoring data from webviews, including:
 * - Framerate (FPS) monitoring
 * - Interaction to Next Paint (INP) monitoring
 *
 * It logs performance issues and reports metrics for analysis.
 */
object PerformanceMonitor {
  private val logger = thisLogger()

  // Thresholds for performance metrics
  private const val CRITICAL_LOW_FRAMERATE = 15.0 // fps
  private const val CRITICAL_SLOW_INP = 500.0 // ms

  // Metric names for reporting
  private const val METRIC_SLOW_FRAMERATE = "slow_framerate"
  private const val METRIC_SLOW_INP = "slow_inp"

  /**
   * Logs and reports slow framerate detected in a webview.
   *
   * @param fps The measured frames per second
   */
  fun logSlowFramerate(fps: Double) {
    // Log the slow framerate
    logger.info("Slow framerate detected in webview: $fps fps")

    // Report more severe issues with higher visibility
    if (fps < CRITICAL_LOW_FRAMERATE) {
      logger.warn("Critical performance issue: Extremely low framerate ($fps fps)")
    }

    // Report metric to the metrics system
    reportMetric(METRIC_SLOW_FRAMERATE, fps.toLong())
  }

  /**
   * Logs and reports slow Interaction to Next Paint (INP) detected in a webview.
   *
   * @param inp The measured INP in milliseconds
   * @param target Optional information about the interaction target
   */
  fun logSlowINP(
    inp: Double,
    target: String? = null,
  ) {
    // Log the slow INP
    val targetInfo = target?.let { " on target: $it" } ?: ""
    logger.info("Slow interaction detected in webview: $inp ms$targetInfo")

    // Report more severe issues with higher visibility
    if (inp > CRITICAL_SLOW_INP) {
      logger.warn("Critical performance issue: Extremely slow interaction ($inp ms)$targetInfo")
    }

    // Report metric to the metrics system
    reportMetric(METRIC_SLOW_INP, inp.toLong())
  }

  /**
   * Reports a performance metric to the metrics system.
   *
   * @param metricName The name of the metric
   * @param value The value of the metric
   */
  private fun reportMetric(
    metricName: String,
    value: Long,
  ) {
    // Try to report to all open projects
    // This is a best-effort approach since we don't know which project the webview belongs to
    val projects = ProjectManager.getInstance().openProjects
    if (projects.isNotEmpty()) {
      // Report to the first open project
      val project = projects[0]
      if (!project.isDisposed) {
        val metricsReporter = ClientMetricsReporter.getInstance(project)
        metricsReporter.reportWebviewClientMetricAsync("performance", metricName, value)
      }
    }
  }
}

package com.augmentcode.intellij.api

import com.augmentcode.api.*
import com.augmentcode.rpc.ChatInstructionStreamResult
import com.augmentcode.rpc.ChatResult
import com.intellij.openapi.components.service
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.runBlocking

const val DEFAULT_PREFIX_SIZE = 4096
const val DEFAULT_SUFFIX_SIZE = 4096

interface AugmentAPI {
  companion object {
    val instance: AugmentAPI
      get() = service()

    const val CLIENT_NAME = "intellij"
  }

  suspend fun available(): Boolean

  fun nextSequenceId(): Int

  fun availableBlocking(): Boolean = runBlocking { available() }

  suspend fun batchUpload(uploadRequest: BatchUploadRequest): BatchUploadResponse?

  suspend fun findMissing(findRequest: FindMissingRequest): FindMissingResponse

  suspend fun complete(completionRequest: CompletionRequest): CompletionResult?

  suspend fun resolveCompletions(resolveRequest: ResolveCompletionsRequest)

  suspend fun token(
    tenantURL: String,
    tokenRequest: AuthTokenRequest,
  ): AuthTokenResult?

  suspend fun currentModel(): Model?

  fun getCachedModelInfo(): ModelConfig?

  suspend fun fetchModelInfo(): ModelConfig?

  suspend fun fetchModelInfoNoCache(): ModelConfig?

  suspend fun createCheckpoint(request: CheckpointBlobsRequest): CheckpointResult

  suspend fun chat(request: ChatRequest): Pair<String, Flow<ChatResult>>

  suspend fun chatRawResponse(request: ChatRequest): Pair<String, Flow<Result<String>>>

  suspend fun chatFeedback(request: ChatFeedbackRequest): ChatFeedbackResponse?

  suspend fun saveChat(request: SaveChatRequest): SaveChatResponse?

  suspend fun clientMetrics(metrics: List<ClientMetric>)

  suspend fun onboardingSessionEvents(sessionEvents: List<OnboardingSessionEvent>)

  suspend fun completionFeedback(request: CompletionFeedbackRequest): CompletionFeedbackResponse?

  suspend fun searchExternalSources(
    query: String,
    sourceTypes: List<String>,
  ): SearchExternalSourcesResponse?

  suspend fun smartPaste(request: ChatInstructionStreamPayload): Pair<String, Flow<ChatInstructionStreamResult>>

  suspend fun reportSmartPasteResolution(resolution: SmartPasteResolution)

  suspend fun reportError(request: ReportErrorRequest)

  suspend fun agentCodebaseRetrieval(request: AgentCodebaseRetrievalRequest): AgentCodebaseRetrievalResponse

  suspend fun listRemoteTools(remoteToolIDs: List<Int>): ListRemoteToolsResult

  suspend fun recordRequestEvents(
    requestId: String,
    request: RecordAgentRequestBody,
  )

  suspend fun recordSessionEvents(request: RecordAgentSessionBody)

  suspend fun recordPreferenceSample(sample: PreferenceSample)

  suspend fun runRemoteTool(
    requestId: String,
    toolName: String,
    toolInputJson: String,
    toolId: RemoteToolId,
    extraToolInput: ExtraToolInput? = null,
  ): RunRemoteToolResult

  suspend fun revokeToolAccess(toolId: RemoteToolId): RevokeToolAccessResult

  suspend fun checkToolSafety(
    toolId: Int,
    toolInputJson: String,
  ): CheckToolSafetyResponse

  suspend fun reportFeatureVector(featureVector: Map<String, Any>)
}

data class RequestRecord(
  val requestId: String,
  val timestamp: Long,
  val remoteMethod: String,
  val responseCode: Int,
) {
  val errored: Boolean
    get() = responseCode >= 500
}

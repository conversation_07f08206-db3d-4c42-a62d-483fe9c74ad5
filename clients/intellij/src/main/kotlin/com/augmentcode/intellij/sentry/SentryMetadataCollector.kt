package com.augmentcode.intellij.sentry

import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.AugmentWebviewStateStore
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.readAction
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.intellij.openapi.util.Disposer
import com.intellij.util.io.awaitExit
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import java.net.URI

/**
 * Data class to hold collected metadata.
 */
data class MetricsData(
  val memoryMetrics: MemoryMetrics? = null,
  val repositoryMetrics: RepositoryMetrics? = null,
  val webviewMetrics: WebviewMetrics? = null,
  val systemTags: Map<String, String> = emptyMap(),
)

/**
 * Memory-related metrics.
 */
data class MemoryMetrics(
  val heapAvailableMB: Long,
  val heapUtilizedMB: Long,
)

/**
 * Repository-related metrics for a specific project.
 */
data class RepositoryMetrics(
  val gitTrackedFiles: Int,
  val indexedFiles: Int,
)

/**
 * Webview-related metrics for a specific project.
 */
data class WebviewMetrics(
  val webviewChatStateSize: Long,
  val webviewSettingsStateSize: Long,
)

/**
 * Pure data collector for system health and performance metadata.
 *
 * This class periodically gathers metadata about:
 * - Memory usage (heap size, utilized memory)
 * - Repository information (Git-tracked files, indexed files)
 * - Webview state (chat state size, settings state size)
 * - System tags (tenant info, remote gateway detection)
 *
 * This class is now a pure data collector that reports collected metadata
 * back to a callback function instead of managing state internally.
 */
class SentryMetadataCollector(
  private val project: Project,
  private val coroutineScope: CoroutineScope,
  parentDisposable: Disposable,
  private val onMetricsCollected: (MetricsData) -> Unit,
) : Disposable {
  private val logger = thisLogger()
  private var collectionJob: Job? = null

  companion object {
    // Default collection interval: 60 seconds (1 minute)
    private const val DEFAULT_COLLECTION_INTERVAL_SECONDS = 60
  }

  init {
    Disposer.register(parentDisposable, this)
  }

  /**
   * Start periodic metadata collection.
   *
   * @param collectionIntervalSeconds The interval between metadata collections in seconds.
   */
  fun startCollection(collectionIntervalSeconds: Int = DEFAULT_COLLECTION_INTERVAL_SECONDS) {
    if (collectionJob?.isActive == true) {
      logger.warn("Metadata collection is already running")
      return
    }

    val collectionIntervalMs = collectionIntervalSeconds * 1000L

    collectionJob =
      coroutineScope.launch {
        logger.info("Starting Sentry metadata collection with interval: ${collectionIntervalSeconds}s")

        while (isActive) {
          try {
            collectAndReportMetrics()
          } catch (e: Throwable) {
            logger.error("Error collecting metadata", e)
          }

          delay(collectionIntervalMs)
        }
      }
  }

  /**
   * Stop metadata collection.
   */
  private fun stopCollection() {
    collectionJob?.cancel()
    collectionJob = null
    logger.info("Stopped Sentry metadata collection")
  }

  /**
   * Collect all metadata and report them via the callback.
   */
  private suspend fun collectAndReportMetrics() {
    logger.debug("Collecting Sentry metadata for project: ${project.name}")

    if (project.isDisposed) {
      logger.debug("Project is disposed, skipping metadata collection")
      return
    }

    val tags = collectSystemTags()
    val memoryMetrics = collectMemoryMetrics()
    val repositoryMetrics = collectRepositoryMetrics()
    val webviewMetrics = collectWebviewMetrics()

    // Create the collected metadata
    val metricsData =
      MetricsData(
        memoryMetrics = memoryMetrics,
        repositoryMetrics = repositoryMetrics,
        webviewMetrics = webviewMetrics,
        systemTags = tags,
      )

    // Report the collected metadata via callback
    onMetricsCollected(metricsData)

    logger.debug("Completed Sentry metadata collection for project: ${project.name}")
  }

  /**
   * Collect system-level tags (tenant info, remote gateway detection).
   */
  private suspend fun collectSystemTags(): Map<String, String> {
    val tags = mutableMapOf<String, String>()

    try {
      // Get tenant information from credentials
      val credentials = AugmentOAuthState.instance.getCredentials()
      if (credentials != null) {
        // Extract tenant name from URL (e.g., https://tenant-name.api.augmentcode.com)
        val tenantName = extractTenantNameFromUrl(credentials.tenantURL)
        if (tenantName != null) {
          tags["tenant_name"] = tenantName
        }

        // Detect if using JetBrains Gateway (remote development)
        val isRemoteGateway = detectRemoteGateway()
        tags["is_remote_gateway"] = isRemoteGateway.toString()
      }
    } catch (e: Exception) {
      logger.warn("Failed to collect system tags", e)
    }

    return tags
  }

  /**
   * Extract tenant name from tenant URL.
   */
  internal fun extractTenantNameFromUrl(tenantUrl: String): String? {
    return try {
      val uri = URI(tenantUrl)
      val host = uri.host ?: return null

      // Handle formats like "tenant-name.api.augmentcode.com" or "tenant-name.us-central.api.augmentcode.com"
      val parts = host.split(".")
      if (parts.size >= 3 && parts.contains("augmentcode")) {
        parts[0] // First part is typically the tenant name
      } else {
        host
      }
    } catch (e: Exception) {
      logger.warn("Failed to extract tenant name from URL: $tenantUrl", e)
      null
    }
  }

  /**
   * Detect if using JetBrains Gateway (remote development environment).
   *
   * JetBrains Gateway is a remote development solution where the IDE frontend
   * runs on a local machine but connects to a remote backend. This method uses
   * stable IntelliJ Platform APIs and system properties to detect Gateway usage.
   */
  internal fun detectRemoteGateway(): Boolean {
    return try {
      val applicationInfo = ApplicationInfo.getInstance()
      // TODO: https://github.com/augmentcode/augment/pull/26431/files#r2103017088
      // Method 1: Check application product code
      // Gateway typically has different product codes like "GW" or "JBC" (JetBrains Client)
      val productCode = applicationInfo.build.productCode
      val isGatewayProduct = productCode == "GW" || productCode == "JBC"

      // Method 2: Check system properties that indicate remote development
      val isRemoteProperty = System.getProperty("idea.is.remote.connection")?.toBoolean() ?: false
      val isGatewayProperty = System.getProperty("jetbrains.gateway.mode")?.toBoolean() ?: false
      val isClientProperty = System.getProperty("jetbrains.client.mode")?.toBoolean() ?: false

      // Method 3: Check application name patterns
      val appName = applicationInfo.versionName.lowercase()
      val isGatewayName = appName.contains("gateway") || appName.contains("client")

      // Method 4: Check for environment variables that might indicate Gateway usage
      val isRemoteEnv = System.getenv("JETBRAINS_REMOTE_RUN")?.toBoolean() ?: false
      val isGatewayEnv = System.getenv("JETBRAINS_GATEWAY")?.toBoolean() ?: false

      val isRemoteGateway =
        isGatewayProduct || isRemoteProperty || isGatewayProperty ||
          isClientProperty || isGatewayName || isRemoteEnv || isGatewayEnv

      // Log the detection results for debugging
      logger.debug(
        "Gateway detection: productCode=$productCode, isRemoteProperty=$isRemoteProperty, " +
          "isGatewayProperty=$isGatewayProperty, isClientProperty=$isClientProperty, " +
          "appName=$appName, isRemoteEnv=$isRemoteEnv, isGatewayEnv=$isGatewayEnv, " +
          "result=$isRemoteGateway",
      )

      isRemoteGateway
    } catch (e: Exception) {
      logger.warn("Failed to detect JetBrains Gateway usage", e)
      false
    }
  }

  /**
   * Collect memory usage metrics using JetBrains platform APIs.
   */
  private fun collectMemoryMetrics(): MemoryMetrics? {
    return try {
      val runtime = Runtime.getRuntime()

      val heapAvailableMB = runtime.maxMemory() / (1024 * 1024)
      val heapUtilizedMB = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)

      logger.debug("Collected memory metrics: available=${heapAvailableMB}MB, utilized=${heapUtilizedMB}MB")

      MemoryMetrics(
        heapAvailableMB = heapAvailableMB,
        heapUtilizedMB = heapUtilizedMB,
      )
    } catch (e: Exception) {
      logger.warn("Failed to collect memory metrics", e)
      null
    }
  }

  /**
   * Collect repository-related metadata for this project.
   */
  private suspend fun collectRepositoryMetrics(): RepositoryMetrics? {
    return try {
      // Collect Git-tracked files count
      val gitTrackedFiles = countGitTrackedFiles(project)
      logger.debug("Collected Git tracked files: $gitTrackedFiles")

      // Collect indexed files count
      val indexedFiles = countIndexedFiles(project)
      logger.debug("Collected indexed files: $indexedFiles")

      // Only return metadata if we have valid data
      if (gitTrackedFiles >= 0 && indexedFiles >= 0) {
        RepositoryMetrics(
          gitTrackedFiles = gitTrackedFiles,
          indexedFiles = indexedFiles,
        )
      } else {
        null
      }
    } catch (e: Exception) {
      logger.warn("Failed to collect repository metadata for project: ${project.name}", e)
      null
    }
  }

  /**
   * Count Git-tracked files in the project using git command and VirtualFile APIs.
   *
   * This method combines VirtualFile API logic for finding the git directory with
   * simplified streaming git ls-files execution, timeout handling, and safety limits.
   *
   * @param project The IntelliJ project
   * @param timeoutSeconds Timeout in seconds for the git command (default: 30)
   * @param maxFiles Maximum number of files to count before stopping (default: 1,000,000)
   * @return Number of tracked files, or -1 if error/timeout occurred
   */
  internal suspend fun countGitTrackedFiles(
    project: Project,
    timeoutSeconds: Int = 30,
    maxFiles: Int = 1_000_000,
  ): Int {
    return try {
      // Use AugmentRoot to find the project root directory (looks for .git or .augmentroot)
      val projectBaseDir =
        readAction {
          // We need a file to start the search from, so we'll use the project's base directory
          val projectDir = project.guessProjectDir()
          if (projectDir != null) {
            AugmentRoot.findRootForProjectFile(project, projectDir)
          } else {
            null
          }
        }

      if (projectBaseDir == null) {
        logger.debug("No project root directory found for project: ${project.name}")
        return -1
      }

      // Execute git ls-files with timeout
      withTimeout(timeoutSeconds * 1000L) {
        executeGitLsFilesCommand(projectBaseDir.path, maxFiles)
      }
    } catch (e: TimeoutCancellationException) {
      logger.warn("Git tracked files counting timed out after ${timeoutSeconds}s for project: ${project.name}")
      -1
    } catch (e: Exception) {
      logger.warn("Failed to count Git tracked files for project: ${project.name}", e)
      -1
    }
  }

  /**
   * Execute git ls-files command and count the results with streaming.
   */
  private suspend fun executeGitLsFilesCommand(
    projectBasePath: String,
    maxFiles: Int,
  ): Int {
    var process: Process? = null
    return try {
      process =
        ProcessBuilder("git", "ls-files", "--cached")
          .directory(java.io.File(projectBasePath))
          .redirectErrorStream(true)
          .start()

      var fileCount = 0
      process.inputStream.bufferedReader().use { reader ->
        reader.lineSequence().forEach { line ->
          if (line.trim().isNotEmpty()) {
            fileCount++
            if (fileCount >= maxFiles) {
              logger.warn("Git tracked files count reached maximum limit of $maxFiles, stopping count")
              return@use
            }
          }
        }
      }

      val exitCode = process.awaitExit()
      if (exitCode == 0) {
        logger.debug("Successfully counted $fileCount Git tracked files")
        fileCount
      } else {
        logger.warn("Git ls-files command failed with exit code: $exitCode")
        -1
      }
    } finally {
      cleanupProcess(process)
    }
  }

  /**
   * Clean up process resources.
   */
  private fun cleanupProcess(process: Process?) {
    process?.let { proc ->
      if (proc.isAlive) {
        logger.debug("Terminating git process")
        proc.destroyForcibly()
        try {
          proc.waitFor(5, java.util.concurrent.TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
          logger.warn("Interrupted while waiting for git process to terminate")
          Thread.currentThread().interrupt()
        }
      }
    }
  }

  /**
   * Count indexed files in the project.
   * Returns the same value as displayed in the Show Augment Status action.
   */
  private fun countIndexedFiles(project: Project): Int {
    return try {
      AugmentRemoteSyncingManager.getInstance(project).indexSize()
    } catch (e: Exception) {
      logger.warn("Failed to count indexed files", e)
      -1
    }
  }

  /**
   * Collect webview-related metrics for this project.
   */
  private fun collectWebviewMetrics(): WebviewMetrics? {
    return try {
      val webviewStateStore = AugmentWebviewStateStore.getInstance(project)

      val chatState = webviewStateStore.get(AugmentWebviewStateKey.CHAT_STATE)
      val chatStateSize = chatState?.length?.toLong() ?: 0L
      logger.debug("Collected chat state size: $chatStateSize characters")

      // Collect settings state size using string length instead of byte array conversion
      val settingsState = webviewStateStore.get(AugmentWebviewStateKey.SETTINGS_STATE)
      val settingsStateSize = settingsState?.length?.toLong() ?: 0L
      logger.debug("Collected settings state size: $settingsStateSize characters")

      WebviewMetrics(
        webviewChatStateSize = chatStateSize,
        webviewSettingsStateSize = settingsStateSize,
      )
    } catch (e: Exception) {
      logger.warn("Failed to collect webview metrics for project: ${project.name}", e)
      null
    }
  }

  override fun dispose() {
    stopCollection()
  }
}

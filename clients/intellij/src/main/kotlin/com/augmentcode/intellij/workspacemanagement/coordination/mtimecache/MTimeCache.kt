package com.augmentcode.intellij.workspacemanagement.coordination.mtimecache

import com.augmentcode.intellij.idea.AugmentFileStore
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.util.io.DataExternalizer
import com.intellij.util.io.EnumeratorStringDescriptor
import com.intellij.util.io.PersistentHashMap
import java.io.DataInput
import java.io.DataOutput

/**
 * This class is a service since the persistent hash map expected to be opened once
 * per project.
 */
@Service(Service.Level.PROJECT)
class MTimeCache(project: Project) : Disposable {
  private val mtimeCacheMap =
    PersistentHashMap(
      AugmentFileStore.getSubDirectoryForProject(project, "mtime-cache").resolve("mtime-cache.dat"),
      EnumeratorStringDescriptor.INSTANCE,
      KeyExternalizer(),
    )

  companion object {
    fun getInstance(project: Project): MTimeCache {
      return project.service<MTimeCache>()
    }
  }

  override fun dispose() {
    mtimeCacheMap.close()
  }

  fun get(absPath: String): MTimeCacheEntry? {
    return mtimeCacheMap[absPath]
  }

  fun put(
    absPath: String,
    value: MTimeCacheEntry,
  ) {
    mtimeCacheMap.put(absPath, value)
  }
}

private class KeyExternalizer : DataExternalizer<MTimeCacheEntry> {
  override fun save(
    out: DataOutput,
    value: MTimeCacheEntry,
  ) {
    out.writeLong(value.mtime)
    out.writeLong(value.size)
    out.writeUTF(value.blobName)
  }

  override fun read(`in`: DataInput): MTimeCacheEntry {
    val mtime = `in`.readLong()
    val size = `in`.readLong()
    val blobName = `in`.readUTF()
    return MTimeCacheEntry(mtime, size, blobName)
  }
}

data class MTimeCacheEntry(
  val mtime: Long,
  val size: Long,
  val blobName: String,
)

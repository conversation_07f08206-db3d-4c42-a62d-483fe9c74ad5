package com.augmentcode.intellij.syncing

import com.augmentcode.api.BlobsPayload
import com.augmentcode.api.FindMissingRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.CredentialsChangeListener
import com.augmentcode.intellij.auth.CredentialsMessageBusWrapper
import com.augmentcode.intellij.index.AugmentBlobState
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.settings.AugmentSettings
import com.google.common.annotations.VisibleForTesting
import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.io.DigestUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

class AugmentRemoteSyncingManagerImpl(
  private val project: Project,
  private val cs: CoroutineScope,
) : AugmentRemoteSyncingManager, Disposable {
  companion object {
    const val MAX_FIND_MISSING_BATCH_SIZE = 1000

    // 1Mb of 4 byte UTF-8 characters
    const val MAX_BATCH_CONTENT_SIZE_BYTES = 250_000

    const val SYNC_LOOP_INTERVAL_MS = 5_000L

    fun expectedBlobName(
      path: String,
      content: String,
    ): String {
      val bytes = path.toByteArray(Charsets.UTF_8) + content.toByteArray(Charsets.UTF_8)
      return DigestUtil.sha256Hex(bytes)
    }
  }

  // Define a data class to hold both pieces of data
  private data class QueueEntry(
    val state: AugmentBlobState,
    val fileReference: VirtualFile,
  )

  private val checkpointManager = AugmentCheckpointManager()

  private val syncFilter = SyncFilter(project)

  // .gitignore and .augmentignore processing
  private val pathFilterService = PathFilterService.getInstance(project)

  // absolute path to blob state mapping of blobs awaiting remote indexing
  private val syncQueue = ConcurrentHashMap<String, QueueEntry>()

  // absolute path to blob state mapping of blobs completed remote indexing
  private val syncedBlobs = ConcurrentHashMap<String, AugmentBlobState>()

  /**
   * Job for polling the server for blob indexing status.
   * Blobs that are known to be indexed are moved from [syncQueue] to [syncedBlobs].
   */
  private var pollJob: Job? = null

  /**
   * Job for periodically refreshing the checkpoint with the server when the blob delta is too big
   */
  private var refreshCheckpointJob: Job? = null

  private val credsMsgBus = CredentialsMessageBusWrapper(cs)

  init {
    AugmentBlobUploaderManager.getInstance(project).startUploadLoop()
    startSyncLoop()
    startCheckpointLoop()

    credsMsgBus.subscribe(
      project,
      object : CredentialsChangeListener {
        override fun onChange(credentials: AugmentCredentials?) {
          thisLogger().info("Credentials changed, resetting syncing state")
          // If the user signs out, reset the in-memory state since the
          // user may change tenants or namespaces.
          runBlocking {
            reset()
          }
        }
      },
    )
  }

  override fun syncedBlobs(): Collection<AugmentBlobState> = syncedBlobs.values

  override fun queuedBlobs(): Collection<AugmentBlobState> = syncQueue.values.map { it.state }

  override fun queueSize(): Int = syncQueue.size

  override fun indexSize(): Int = syncedBlobs.size

  override fun ratioIndexed(): Double {
    val totalSize = syncQueue.size + syncedBlobs.size
    if (totalSize == 0) {
      return 0.0
    }
    return indexSize().toDouble() / totalSize.toDouble()
  }

  override suspend fun notifyContentChanged(
    rootPath: String,
    relPath: String,
    expectedBlobName: String,
    blobFile: VirtualFile,
  ): AugmentBlobState {
    val uploadRequest = UploadRequest(rootPath, relPath, expectedBlobName, fileReference = blobFile)
    AugmentBlobUploaderManager.getInstance(project).enqueue(uploadRequest)
    val currentTimestamp = System.currentTimeMillis()
    val state =
      AugmentBlobState(
        uploadRequest.relPath,
        uploadRequest.expectedBlobName,
        uploadRequest.expectedBlobName,
        uploadRequest.rootPath,
        currentTimestamp,
      )

    val absolutePath = joinPath(uploadRequest.rootPath, uploadRequest.relPath)
    syncQueue[absolutePath] = QueueEntry(state, blobFile)
    return state
  }

  override suspend fun synchronizedBlobsPayload(): BlobsPayload {
    return checkpointManager.getCheckpoint(syncedBlobs.values.map { it.remoteName }.toSet())
  }

  override suspend fun reset() {
    syncQueue.clear()
    syncedBlobs.clear()
    checkpointManager.reset()
  }

  override fun dispose() {
    refreshCheckpointJob?.cancel()
    refreshCheckpointJob = null
  }

  @VisibleForTesting
  fun startSyncLoop() {
    if (pollJob !== null) {
      return
    }

    @OptIn(DelicateCoroutinesApi::class)
    pollJob =
      cs.launch(Dispatchers.IO) {
        while (isActive) {
          try {
            if (AugmentAPI.instance.available()) {
              syncLoop()
            }
          } catch (e: CancellationException) {
            // expected when shutting down
            throw e
          } catch (e: Exception) {
            thisLogger().warn("Failed to sync blobs", e)
          }
          delay(SYNC_LOOP_INTERVAL_MS)
        }
      }
  }

  fun startCheckpointLoop() {
    if (refreshCheckpointJob !== null) {
      return
    }
    refreshCheckpointJob =
      cs.launch(Dispatchers.IO) {
        while (isActive) {
          val syncedBlobNames = syncedBlobs.values.map { it.remoteName }.toSet()
          checkpointManager.refreshCheckpointLoop(syncedBlobNames)
        }
      }
  }

  private suspend fun syncLoop() {
    findUnsynced(syncQueue.values.map { it.state })
  }

  /**
   * Checks which blobs need to be indexed on the remote server and updates internal sync state.
   *
   * For each blob:
   * 1. Filters out files that should be ignored based on .gitignore and .augmentignore,
   *    removing them from the sync queue
   * 2. Batches requests to the server to check blob indexing status (respecting size limits)
   * 3. Updates internal state:
   *    - Moves successfully indexed blobs from [syncQueue] to [syncedBlobs]
   *    - Removes ignored files from [syncQueue]
   *
   * @param blobs Collection of blob states to check
   * @return Set of blob names that are unknown to the server (not yet uploaded or failed to index)
   */
  override suspend fun findUnsynced(blobs: Collection<AugmentBlobState>): Set<String> {
    if (blobs.isEmpty() || !AugmentAPI.instance.available()) {
      return emptySet()
    }
    val unknownBlobs = mutableSetOf<String>()
    val currentBatch = mutableListOf<AugmentBlobState>()
    var currentSize = 0

    for (blob in blobs) {
      val blobNameSize = blob.remoteName.length
      if (currentBatch.size >= MAX_FIND_MISSING_BATCH_SIZE ||
        currentSize + blobNameSize > MAX_BATCH_CONTENT_SIZE_BYTES
      ) {
        processBatch(currentBatch, unknownBlobs)
        currentBatch.clear()
        currentSize = 0
      }

      // Filter out files based on .gitignore and .augmentignore
      val absolutePath = joinPath(blob.rootPath, blob.relativePath)
      val queueEntry = syncQueue[absolutePath]
      // findUnsynced is called during AugmentValidateIndexActivity (a postStartupActivity)
      // syncQueue is empty at startup, therefore queueEntry will be null.
      // In that case, syncFilter#isAccepted needs to handle null virtualFile.
      if (syncFilter.isAccepted(blob.rootPath, blob.relativePath, queueEntry?.fileReference)) {
        currentBatch.add(blob)
        currentSize += blobNameSize
      } else {
        syncQueue.remove(absolutePath)
      }
    }

    // Process remaining batch if any
    if (currentBatch.isNotEmpty()) {
      processBatch(currentBatch, unknownBlobs)
    } else {
      thisLogger().info("No blobs to check indexing status for in current batch")
    }
    return unknownBlobs
  }

  override suspend fun notifyContentRemoved(absolutePath: String) {
    syncQueue.remove(absolutePath)
    syncedBlobs.remove(absolutePath)
  }

  override suspend fun notifyDirectoryRemoved(oldPath: String) {
    // Find and remove all entries in syncQueue and syncedBlobs that start with oldPath
    // This is more efficient than filtering and then removing individually

    // Count before removal for logging
    val queueSizeBefore = syncQueue.size
    val syncedSizeBefore = syncedBlobs.size

    // Remove all entries with keys starting with oldPath
    val oldPathWithSeparator = if (oldPath.endsWith("/")) oldPath else "$oldPath/"
    syncQueue.entries.removeIf { (key, _) -> key.startsWith(oldPathWithSeparator) }
    syncedBlobs.entries.removeIf { (key, _) -> key.startsWith(oldPathWithSeparator) }

    // Calculate how many entries were removed
    val queueRemoved = queueSizeBefore - syncQueue.size
    val syncedRemoved = syncedSizeBefore - syncedBlobs.size

    // No need to add entries with new paths - they will be indexed when accessed
    if (queueRemoved + syncedRemoved > 0) {
      thisLogger().info("Directory moved or deleted: $oldPath, removed ${queueRemoved + syncedRemoved} entries from sync maps")
    }
  }

  private suspend fun processBatch(
    batch: List<AugmentBlobState>,
    unknownBlobs: MutableSet<String>,
  ) {
    val blobStatesByRemoteName = batch.associateBy { it.remoteName }
    thisLogger().info(
      "Checking indexing status for ${batch.size} files",
    )
    val findMissingResponse =
      AugmentAPI.instance.findMissing(
        FindMissingRequest().apply {
          model = AugmentSettings.instance.modelName ?: ""
          memObjectNames = blobStatesByRemoteName.keys.toList()
        },
      )
    unknownBlobs.addAll(findMissingResponse.unknownMemoryNames)

    val knownBlobs =
      blobStatesByRemoteName.keys - findMissingResponse.nonindexedBlobNames - findMissingResponse.unknownMemoryNames

    if (findMissingResponse.unknownMemoryNames.isNotEmpty()) {
      val unknownBlobPaths = findMissingResponse.unknownMemoryNames.map { blobStatesByRemoteName[it]?.relativePath ?: "<unknown>" }
      thisLogger().info(
        "Found ${findMissingResponse.unknownMemoryNames.size} unknown blobs: [${unknownBlobPaths.joinToString(", ")}]",
      )
    }
    if (findMissingResponse.nonindexedBlobNames.isNotEmpty()) {
      val nonIndexedBlobPaths = findMissingResponse.nonindexedBlobNames.map { blobStatesByRemoteName[it]?.relativePath ?: "<unknown>" }
      thisLogger().info(
        "Found ${findMissingResponse.nonindexedBlobNames.size} non-indexed blobs: [${nonIndexedBlobPaths.joinToString(", ")}]",
      )
    }

    for (knownBlob in knownBlobs) {
      val blobState = blobStatesByRemoteName[knownBlob] ?: continue
      val absolutePath = joinPath(blobState.rootPath, blobState.relativePath)

      // Move blob from queue to synced state
      val queuedState = syncQueue.remove(absolutePath)?.state ?: blobState
      syncedBlobs.put(absolutePath, queuedState)
    }
  }

  private fun joinPath(
    rootPath: String,
    relativePath: String,
  ): String {
    return FileUtil.toSystemDependentName("$rootPath/$relativePath")
  }
}

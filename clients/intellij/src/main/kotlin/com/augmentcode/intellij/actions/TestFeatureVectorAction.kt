package com.augmentcode.intellij.actions

import com.augmentcode.intellij.featurevector.FeatureVectorReporter
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages

/**
 * Test action to manually trigger feature vector collection and reporting.
 * This is useful for testing the feature vector functionality.
 */
class TestFeatureVectorAction : AnAction() {
  
  override fun actionPerformed(e: AnActionEvent) {
    val project: Project = e.project ?: return
    
    try {
      val reporter = FeatureVectorReporter.getInstance(project)
      reporter.reportVector()
      
      Messages.showInfoMessage(
        project,
        "Feature vector collection started. Check logs for results.",
        "Feature Vector Test"
      )
    } catch (ex: Exception) {
      Messages.showErrorDialog(
        project,
        "Failed to start feature vector collection: ${ex.message}",
        "Feature Vector Test Error"
      )
    }
  }
  
  override fun update(e: AnActionEvent) {
    // Only enable if we have a project
    e.presentation.isEnabled = e.project != null
  }
}

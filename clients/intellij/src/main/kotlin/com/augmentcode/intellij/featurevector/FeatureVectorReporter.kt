package com.augmentcode.intellij.featurevector

import com.augmentcode.featurevector.ExtensionContext
import com.augmentcode.featurevector.FeatureVectorCollector
import com.augmentcode.featurevector.StorageUri
import com.augmentcode.featurevector.VscodeCollector
import com.augmentcode.featurevector.VscodeEnv
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.*

/**
 * Service responsible for collecting and reporting feature vectors to the API.
 * This mirrors the VSCode FeatureVectorReporter functionality.
 */
@Service(Service.Level.PROJECT)
class FeatureVectorReporter(
  private val project: Project,
  private val cs: CoroutineScope,
) {
  companion object {
    fun getInstance(project: Project): FeatureVectorReporter = project.getService(FeatureVectorReporter::class.java)
    
    private val logger = thisLogger()
  }

  /**
   * Report feature vector to the API.
   * This is the main entry point that collects system information and sends it to the backend.
   */
  fun reportVector() {
    cs.launch(Dispatchers.IO) {
      try {
        logger.info("Starting feature vector collection...")
        
        // Create VSCode collector data (IntelliJ equivalent)
        val vscode = VscodeCollector(
          version = getIntellijVersion(),
          env = VscodeEnv(machineId = getMachineId())
        )
        
        // Create extension context (IntelliJ equivalent)
        val extensionContext = ExtensionContext(
          globalStorageUri = StorageUri(getGlobalStoragePath()),
          storageUri = StorageUri(getProjectStoragePath())
        )
        
        // Collect features
        val features = FeatureVectorCollector.createFeatures(vscode, extensionContext)
        val featureVector = features.toVector()
        
        logger.info("Feature vector collected with ${featureVector.size} fields")
        
        // Report to API
        AugmentAPI.instance.reportFeatureVector(featureVector)
        
        logger.info("Feature vector reported successfully")
        
      } catch (e: Exception) {
        logger.warn("Failed to collect or report feature vector", e)
      }
    }
  }
  
  private fun getIntellijVersion(): String {
    return ApplicationInfo.getInstance().fullVersion
  }
  
  private fun getMachineId(): String {
    // Use a consistent machine ID based on system properties
    // This mirrors VSCode's env.machineId concept
    val systemProps = listOf(
      System.getProperty("user.name", ""),
      System.getProperty("os.name", ""),
      System.getProperty("os.arch", ""),
      System.getProperty("java.version", "")
    ).joinToString("-")
    
    return UUID.nameUUIDFromBytes(systemProps.toByteArray()).toString()
  }
  
  private fun getGlobalStoragePath(): String {
    // IntelliJ global storage path
    return System.getProperty("idea.system.path", System.getProperty("user.home") + "/.IntelliJIdea")
  }
  
  private fun getProjectStoragePath(): String {
    // Project-specific storage path
    return project.basePath ?: System.getProperty("user.dir")
  }
}

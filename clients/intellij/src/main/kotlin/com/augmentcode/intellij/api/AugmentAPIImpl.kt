package com.augmentcode.intellij.api

import com.augmentcode.api.*
import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.utils.SequenceId
import com.augmentcode.rpc.AugmentTypes
import com.augmentcode.rpc.ChatInstructionStreamResult
import com.augmentcode.rpc.ChatModelReplyError
import com.augmentcode.rpc.ChatResult
import com.github.benmanes.caffeine.cache.Caffeine
import com.google.gson.FieldNamingPolicy
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.protobuf.TypeRegistry
import com.google.protobuf.util.JsonFormat
import com.intellij.openapi.diagnostic.thisLogger
import com.jetbrains.rd.util.UUID
import io.ktor.client.statement.*
import io.ktor.utils.io.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import org.jetbrains.annotations.VisibleForTesting

data class ConnectionDetails(
  val baseURL: String?,
  val token: String?,
)

sealed class CheckpointResult {
  data class Success(val response: CheckpointBlobsResponse) : CheckpointResult()

  object NeedsReset : CheckpointResult()

  object OtherFailure : CheckpointResult()
}

internal const val DEFUALT_STREAM_TIMEOUT_MS = 30_000L
internal const val CHAT_STREAM_TIMEOUT_MS = 300_000L
internal const val MODEL_INFO_TTL_HOURS = 1L

internal class AugmentAPIImpl(
  private val cs: CoroutineScope,
) : AugmentAPI {
  private val httpClient = AugmentHttpClient()
  private val sequenceId = SequenceId()

  private val modelCache =
    Caffeine
      .newBuilder()
//      .expireAfterWrite(MODEL_INFO_TTL_HOURS, TimeUnit.HOURS)
      .build<String, Deferred<ModelConfig?>>()

  // use gson instead of Ktor's content negotiation because of conflicts in classpath
  // and not working serialization compiler plugin
  private val gson =
    GsonBuilder()
      .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
      .create()

  val jsonParser =
    JsonFormat
      .parser()
      .usingTypeRegistry(
        TypeRegistry
          .newBuilder()
          .add(AugmentTypes.getDescriptor().messageTypes)
          .build(),
      ).ignoringUnknownFields()

  override suspend fun available(): Boolean {
    if (!AugmentSettings.instance.apiToken.isNullOrBlank()) {
      return !AugmentSettings.instance.completionURL.isNullOrBlank()
    }
    val credentials = AugmentOAuthState.instance.getCredentials()
    return credentials != null
  }

  override fun nextSequenceId(): Int {
    return sequenceId.next()
  }

  @VisibleForTesting
  fun reset() {
    sequenceId.reset()
  }

  override suspend fun complete(completionRequest: CompletionRequest): CompletionResult? {
    val requestId = UUID.randomUUID().toString()
    val resp = httpClient.post("completion", completionRequest, requestID = requestId)
    if (resp.status.value in 200..299) {
      val completionBody = resp.bodyAsText()
      return gson.fromJson(completionBody, CompletionResult::class.java).also {
        it.requestId = requestId
      }
    }
    return null
  }

  override suspend fun batchUpload(uploadRequest: BatchUploadRequest): BatchUploadResponse? {
    if (uploadRequest.blobs.isEmpty()) {
      return BatchUploadResponse()
    }
    val resp = httpClient.post("batch-upload", uploadRequest)
    if (resp.status.value in 200..299) {
      val completionBody = resp.bodyAsText()
      return gson.fromJson(completionBody, BatchUploadResponse::class.java)
    }
    return null
  }

  override suspend fun findMissing(findRequest: FindMissingRequest): FindMissingResponse {
    val resp = httpClient.post("find-missing", findRequest)
    if (resp.status.value in 200..299) {
      val completionBody = resp.bodyAsText()
      return gson.fromJson(completionBody, FindMissingResponse::class.java)
    }
    return FindMissingResponse()
  }

  override suspend fun resolveCompletions(resolveRequest: ResolveCompletionsRequest) {
    httpClient.post("resolve-completions", resolveRequest)
  }

  override suspend fun token(
    tenantURL: String,
    tokenRequest: AuthTokenRequest,
  ): AuthTokenResult? {
    val resp = httpClient.post("token", tokenRequest, serverBaseURL = tenantURL)
    if (resp.status.value in 200..299) {
      val body = resp.bodyAsText()
      return gson.fromJson(body, AuthTokenResult::class.java)
    }
    return null
  }

  override suspend fun currentModel(): Model? {
    val modelInfo = fetchModelInfo()
    if (modelInfo == null) {
      logger.warn("Model info not found.")
      return null
    }

    var model = modelInfo.findModel(AugmentSettings.instance.modelName)
    if (model == null) {
      logger.warn("Model '${AugmentSettings.instance.modelName}' not found, using the default model.")
      model = modelInfo.findDefaultModel()
    }
    return model
  }

  @VisibleForTesting
  fun clearModelCache() {
    modelCache.invalidateAll()
  }

  /**
   * Returns the cached model config if it exists and is not expired.
   * Only waits up to 50 ms for the cache to be populated.
   * This is an easy way to allow some flags and other model information to be checked safely in the EDT.
   */
  override fun getCachedModelInfo(): ModelConfig? {
    // run fetchModelInfo in a timeout of 50 ms and return null if it times out
    return runBlocking {
      withTimeoutOrNull(50) {
        fetchModelInfo()
      }
    }
  }

  /**
   * Fetches the model config from the server if it is not already cached.
   * Multiple calls to this method while the network call is in flight will not result in
   * multiple network calls.
   */
  @OptIn(DelicateCoroutinesApi::class)
  override suspend fun fetchModelInfo(): ModelConfig? {
    val credentials = AugmentOAuthState.instance.getCredentials()
    val cacheKey =
      AugmentSettings.instance.let {
        listOfNotNull(it.completionURL, it.apiToken, credentials?.tenantURL, credentials?.accessToken).joinToString("-")
      }
    if (cacheKey.isBlank()) {
      return null
    }
    // there might a race here if a user changes the settings while this is running
    // but chances are so low that it's not worth the complexity
    val deferredResult =
      modelCache.get(cacheKey) {
        cs.async {
          try {
            modelInfoImpl().also {
              val model = it?.findDefaultModel()
              val debugInfo =
                listOf(
                  "model.default=${it?.defaultModelName}",
                  "model.default.context=${model?.suggestedPrefixCharCount}-${model?.suggestedSuffixCharCount}",
                  "features.intellijForceEditsMinVersion=${it?.featureFlags?.intellijForceCompletionMinVersion}",
                  "features.bypassLanguageFilter=${it?.featureFlags?.bypassLanguageFilter}",
                  "features.maxUploadSizeBytes=${it?.featureFlags?.maxUploadSizeBytes}",
                )
              logger.info("Model info refreshed: ${debugInfo.joinToString()}")
            }
          } catch (e: IllegalStateException) {
            logger.warn("Failed to fetch model info", e)
            null
          }
        }
      }
    return deferredResult.await().also {
      if (it == null) {
        @Suppress("DeferredResultUnused")
        modelCache.invalidate(cacheKey)
      }
    }
  }

  override suspend fun fetchModelInfoNoCache(): ModelConfig? {
    return modelInfoImpl()
  }

  override suspend fun createCheckpoint(request: CheckpointBlobsRequest): CheckpointResult {
    val resp = httpClient.post("checkpoint-blobs", request)
    return when (resp.status.value) {
      in 200..299 -> {
        val checkpointBody = resp.bodyAsText()
        CheckpointResult.Success(gson.fromJson(checkpointBody, CheckpointBlobsResponse::class.java))
      }
      400, 404 -> {
        logger.warn("Checkpoint not found or invalid, indicating reset needed")
        CheckpointResult.NeedsReset
      }
      else -> CheckpointResult.OtherFailure
    }
  }

  private suspend fun modelInfoImpl(): ModelConfig? {
    val emptyObject = object {}
    val resp = httpClient.post("get-models", emptyObject)
    if (resp.status.value !in 200..299) {
      return null
    }

    val body = resp.bodyAsText()
    try {
      return gson.fromJson(body, ModelConfig::class.java)
    } catch (e: JsonSyntaxException) {
      logger.warn("Failed to parse model config: ${e.message}")
      return null
    }
  }

  override suspend fun chat(request: ChatRequest): Pair<String, Flow<ChatResult>> {
    val (requestId, flowOfResults) = chatRawResponse(request)
    return requestId to
      flowOfResults.map { result ->
        if (result.isFailure) {
          val exception = result.exceptionOrNull()!!
          if (exception is AugmentAPIException) {
            if (exception.statusCode == 413) {
              val resultBuilder = ChatResult.newBuilder()
              resultBuilder.setError(
                ChatModelReplyError.newBuilder()
                  .setDisplayErrorMessage(AugmentBundle.message("chat.message.too.large"))
                  .build(),
              )
              return@map resultBuilder.build()
            } else if (exception.statusCode == 429 || (exception.statusCode >= 500 && exception.statusCode < 600)) {
              // If the status is 429 (resource exhausted) or an "unavailable" 5xx error, then
              // we want to show the error and make it as retriable.
              val resultBuilder = ChatResult.newBuilder()
              resultBuilder.setError(
                ChatModelReplyError.newBuilder()
                  .setDisplayErrorMessage(exception.message!!)
                  .setIsRetriable(true)
                  .build(),
              )
              return@map resultBuilder.build()
            }
          } else {
            // If none of the above apply, then it's a non-retriable error.
            val resultBuilder = ChatResult.newBuilder()
            resultBuilder.setError(
              ChatModelReplyError.newBuilder()
                .setDisplayErrorMessage(exception.message!!)
                .build(),
            )
            return@map resultBuilder.build()
          }
        }
        val resultBuilder = ChatResult.newBuilder()
        jsonParser.merge(result.getOrNull()!!, resultBuilder)
        return@map resultBuilder.build()
      }
  }

  override suspend fun chatRawResponse(request: ChatRequest): Pair<String, Flow<Result<String>>> {
    val (requestId, flowOfResults) = streamAndParse("chat-stream", request, CHAT_STREAM_TIMEOUT_MS)
    return requestId to flowOfResults
  }

  override suspend fun smartPaste(request: ChatInstructionStreamPayload): Pair<String, Flow<ChatInstructionStreamResult>> {
    val (requestId, flowOfResults) = streamAndParse("smart-paste-stream", request)
    return requestId to
      flowOfResults.map { result ->
        if (result.isFailure) {
          throw result.exceptionOrNull()!!
        }
        val resultBuilder = ChatInstructionStreamResult.newBuilder()
        jsonParser.merge(result.getOrNull(), resultBuilder)
        return@map resultBuilder.build()
      }
  }

  private suspend fun <R> streamAndParse(
    endpoint: String,
    request: R,
    timeoutMs: Long = DEFUALT_STREAM_TIMEOUT_MS,
  ): Pair<String, Flow<Result<String>>> {
    val requestID = UUID.randomUUID().toString()
    return requestID to
      flow {
        httpClient.stream(endpoint, request, requestID = requestID, timeoutMs = timeoutMs, handleStream = { resp ->
          when (resp.status.value) {
            in 200..299 -> Unit // continue
            else -> {
              emit(Result.failure(AugmentAPIException(endpoint, resp.status.value, resp.status.description)))
              return@stream
            }
          }

          val channel: ByteReadChannel = resp.bodyAsChannel()
          // await for the first few bytes and then start timing chunk receiving
          channel.awaitContent()
          while (!channel.isClosedForRead) {
            val line =
              try {
                withTimeout(timeoutMs) {
                  channel.readUTF8Line()
                }
              } catch (_: TimeoutCancellationException) {
                throw IllegalStateException("$endpoint experienced network issue")
              }
            if (!line.isNullOrBlank()) {
              emit(Result.success(line))
            }
          }
        })
      }
  }

  override suspend fun chatFeedback(request: ChatFeedbackRequest): ChatFeedbackResponse? {
    val resp = httpClient.post("chat-feedback", request)
    if (resp.status.value in 200..299) {
      val checkpointBody = resp.bodyAsText()
      return gson.fromJson(checkpointBody, ChatFeedbackResponse::class.java)
    }
    return null
  }

  override suspend fun completionFeedback(request: CompletionFeedbackRequest): CompletionFeedbackResponse? {
    val resp = httpClient.post("completion-feedback", request)
    if (resp.status.value in 200..299) {
      val checkpointBody = resp.bodyAsText()
      return gson.fromJson(checkpointBody, CompletionFeedbackResponse::class.java)
    } else {
      logger.warn("Failed to send completion feedback: ${resp.status.value} ${resp.status.description}")
    }
    return null
  }

  override suspend fun saveChat(request: SaveChatRequest): SaveChatResponse? {
    val resp = httpClient.post("save-chat", request)
    if (resp.status.value in 200..299) {
      val body = resp.bodyAsText()
      return gson.fromJson(body, SaveChatResponse::class.java)
    }
    return null
  }

  override suspend fun clientMetrics(metrics: List<ClientMetric>) {
    val request =
      ClientMetricsRequest().apply {
        this.metrics =
          metrics.map {
            ClientMetric().apply {
              this.clientMetric = it.clientMetric
              this.value = it.value
            }
          }
      }

    val resp = httpClient.post("client-metrics", request)
    if (resp.status.value in 200..299) {
      logger.info("Metrics sent successfully")
    } else {
      logger.warn("Failed to send metrics: ${resp.status.value} ${resp.status.description}")
    }
  }

  override suspend fun onboardingSessionEvents(sessionEvents: List<OnboardingSessionEvent>) {
    val request =
      object {
        // wrap request with object so that when it gets turned into JSON we have {events: [...]}
        val events =
          sessionEvents.map {
            OnboardingSessionEvent().apply {
              this.event_name = it.event_name
              this.event_time_sec = it.event_time_sec
              this.event_time_nsec = it.event_time_nsec
            }
          }
      }

    val resp = httpClient.post("record-onboarding-session-event", request)
    if (resp.status.value in 200..299) {
      logger.info("Onboarding session events sent successfully")
    } else {
      logger.warn("Failed to send onboarding session events: ${resp.status.value} ${resp.status.description}")
    }
  }

  override suspend fun searchExternalSources(
    query: String,
    sourceTypes: List<String>,
  ): SearchExternalSourcesResponse? {
    val request =
      SearchExternalSourcesRequest().apply {
        this.query = query
        this.sourceTypes = sourceTypes.toTypedArray()
      }
    val resp = httpClient.post("search-external-sources", request)
    if (resp.status.value in 200..299) {
      val sourcesBody = resp.bodyAsText()
      return gson.fromJson(sourcesBody, SearchExternalSourcesResponse::class.java)
    }
    return null
  }

  override suspend fun listRemoteTools(remoteToolIDs: List<Int>): ListRemoteToolsResult {
    val resp =
      httpClient.post(
        "agents/list-remote-tools",
        ListRemoteToolsRequest().apply {
          tool_id_list =
            ToolIDList().apply {
              tool_ids = remoteToolIDs.toSet()
            }
        },
      )
    if (resp.status.value in 200..299) {
      val toolsBody = resp.bodyAsText()
      return gson.fromJson(toolsBody, ListRemoteToolsResult::class.java)
    }
    return ListRemoteToolsResult()
  }

  override suspend fun runRemoteTool(
    requestId: String,
    toolName: String,
    toolInputJson: String,
    toolId: RemoteToolId,
    extraToolInput: ExtraToolInput?,
  ): RunRemoteToolResult {
    val toolRequest =
      RunRemoteToolRequest().apply {
        tool_name = toolName
        tool_input_json = toolInputJson
        tool_id = toolId.value
      }
    if (extraToolInput != null) {
      when (toolId) {
        RemoteToolId.Jira,
        RemoteToolId.Confluence,
        -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              atlassian_tool_extra_input = extraToolInput as AtlassianToolExtraInput
            }
        }
        RemoteToolId.Notion,
        -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              notion_tool_extra_input = extraToolInput as NotionToolExtraInput
            }
        }
        RemoteToolId.Linear,
        -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              linear_tool_extra_input = extraToolInput as LinearToolExtraInput
            }
        }
        RemoteToolId.GitHubApi -> {
          toolRequest.extra_tool_input =
            RemoteToolsInput().apply {
              github_tool_extra_input = extraToolInput as GitHubToolExtraInput
            }
        }
        else -> { /* do nothing */ }
      }
    }

    val resp =
      httpClient.post(
        "agents/run-remote-tool",
        toolRequest,
        requestID = requestId,
      )
    if (resp.status.value in 200..299) {
      val resultBody = resp.bodyAsText()
      return gson.fromJson(resultBody, RunRemoteToolResult::class.java)
    }
    throw IllegalStateException("Failed to run remote tool: ${resp.status.value} ${resp.status.description}")
  }

  override suspend fun revokeToolAccess(toolId: RemoteToolId): RevokeToolAccessResult {
    val resp = httpClient.post("agents/revoke-tool-access", RevokeToolAccessRequest(toolId))
    if (resp.status.value in 200..299) {
      logger.info("Tool revoked successfully")
      val resultBody = resp.bodyAsText()
      return gson.fromJson(resultBody, RevokeToolAccessResult::class.java)
    }
    logger.warn("Failed to revoke tool: ${resp.status.value} ${resp.status.description}")
    throw IllegalStateException("Failed to revoke tool: ${resp.status.value} ${resp.status.description}")
  }

  override suspend fun checkToolSafety(
    toolId: Int,
    toolInputJson: String,
  ): CheckToolSafetyResponse {
    val resp =
      httpClient.post(
        "agents/check-tool-safety",
        CheckToolSafety().apply {
          tool_id = toolId
          tool_input_json = toolInputJson
        },
      )
    if (resp.status.value in 200..299) {
      val resultBody = resp.bodyAsText()
      return gson.fromJson(resultBody, CheckToolSafetyResponse::class.java)
    }
    throw IllegalStateException("Failed to run remote tool: ${resp.status.value} ${resp.status.description}")
  }

  override suspend fun reportSmartPasteResolution(resolution: SmartPasteResolution) {
    val resp = httpClient.post("resolve-smart-paste", resolution)
    if (resp.status.value in 200..299) {
      logger.info("Smart paste resolution sent successfully")
    } else {
      logger.warn("Failed to send smart paste resolution: ${resp.status.value} ${resp.status.description}")
    }
  }

  override suspend fun reportError(request: ReportErrorRequest) {
    try {
      val resp = httpClient.post("report-error", request)
      if (resp.status.value !in 200..299) {
        logger.warn("Failed to report error: ${resp.status.value} ${resp.status.description}")
      }
    } catch (e: Exception) {
      logger.warn("Failed to report error: ${e.message}, dropping error report")
    }
  }

  override suspend fun agentCodebaseRetrieval(request: AgentCodebaseRetrievalRequest): AgentCodebaseRetrievalResponse {
    val resp = httpClient.post("agents/codebase-retrieval", request)
    if (resp.status.value in 200..299) {
      val sourcesBody = resp.bodyAsText()
      return gson.fromJson(sourcesBody, AgentCodebaseRetrievalResponse::class.java)
    }
    logger.warn("Failed to retrieve codebase: ${resp.status.value} ${resp.status.description}")
    throw IllegalStateException("Failed to retrieve codebase with status ${resp.status.value} ${resp.status.description}")
  }

  override suspend fun recordRequestEvents(
    requestId: String,
    request: RecordAgentRequestBody,
  ) {
    val resp = httpClient.post("record-request-events", request, requestID = requestId)
    if (resp.status.value in 200..299) {
      // NOOP
    } else {
      logger.warn("Failed to record request events: ${resp.status.value} ${resp.status.description}")
    }
  }

  override suspend fun recordSessionEvents(request: RecordAgentSessionBody) {
    val resp = httpClient.post("record-session-events", request)
    if (resp.status.value in 200..299) {
      // NOOP
    } else {
      logger.warn("Failed to record session events: ${resp.status.value} ${resp.status.description}")
    }
  }

  override suspend fun recordPreferenceSample(sample: PreferenceSample) {
    val resp = httpClient.post("record-preference-sample", sample)
    if (resp.status.value in 200..299) {
      // NOOP
    } else {
      logger.warn("Failed to record preference sample: ${resp.status.value} ${resp.status.description}")
    }
  }

  companion object {
    private val logger = thisLogger()
  }
}

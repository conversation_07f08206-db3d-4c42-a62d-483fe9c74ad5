package com.augmentcode.intellij.index

data class AugmentBlobState(
  /** Path relative to the root when the file was indexed */
  val relativePath: String,
  /** Blob name as known by the remote server */
  val remoteName: String,
  /**
   * Blob name computed from local file contents.
   * May be out of sync if file is actively being edited
   */
  val localName: String,
  /** Absolute path to the root directory when the file was indexed */
  val rootPath: String,
  /**
   * Timestamp of the last successful sync with remote server.
   * Represents when we last received the [remoteName]
   */
  val remoteSyncTimestamp: Long = System.currentTimeMillis(),
) {
  val syncedCopy: AugmentBlobState
    get() = this.copy(localName = remoteName)

  val synced: Boolean
    get() = remoteName == localName
}

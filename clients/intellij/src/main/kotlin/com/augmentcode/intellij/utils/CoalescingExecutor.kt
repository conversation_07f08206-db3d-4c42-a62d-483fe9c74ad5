package com.augmentcode.intellij.utils

import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.util.Disposer
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * CoalescingExecutor is a utility class that allows you to coalesce multiple requests into a single execution.
 * This is useful if you have a task that you want to run once, and any calls to it while it's running
 * should be coalesced into a single run once it's finished (i.e. once it's finished, run the function
 * once more).
 *
 * For example, lots events can result in the plugin state needing to change (i.e. auth changes, plugin
 * changes, etc.). We want to avoid running the state change task multiple times in parallel, and
 * instead run it once and then check if any other changes have been requested.
 */
class CoalescingExecutor(
  private val scope: CoroutineScope,
  parentDisposable: Disposable,
  private val task: suspend () -> Unit,
) : Disposable {
  private val mutex = Mutex()

  @Volatile
  private var isDisposed = false

  private val triggerChannel = Channel<Unit>(Channel.CONFLATED)
  private var job: Job? = null

  init {
    Disposer.register(parentDisposable, this)
    runLoop()
  }

  private fun runLoop() {
    job?.cancel()
    job =
      scope.launch {
        for (signal in triggerChannel) {
          if (isDisposed) break

          // Only one task can run at a time
          mutex.withLock {
            task()
          }
        }
      }
  }

  fun trigger() {
    if (isDisposed) {
      thisLogger().warn("CoalescingExecutor triggered after dispose")
      return
    }
    // Offer a signal to run; coalesces if one is already pending
    triggerChannel.trySend(Unit)
  }

  fun reset() {
    runLoop()
  }

  override fun dispose() {
    isDisposed = true
    job?.cancel()
  }
}

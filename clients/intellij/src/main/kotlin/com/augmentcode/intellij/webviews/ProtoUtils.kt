package com.augmentcode.intellij.webviews

import com.augmentcode.common.webviews.protos.CommonWebViewChatTypes
import com.augmentcode.common.webviews.protos.WebViewMsgDefTypes
import com.augmentcode.rpc.AugmentTypes
import com.augmentcode.rpc.ChatTypes
import com.augmentcode.rpc.PreferencesTypes
import com.augmentcode.rpc.SettingsTypes
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.databind.node.TextNode
import com.google.protobuf.Any
import com.google.protobuf.Descriptors.FileDescriptor
import com.google.protobuf.Message
import com.google.protobuf.TypeRegistry
import com.google.protobuf.util.JsonFormat

/**
 * Creates a mapping between webview message types and their corresponding protobuf type URLs.
 *
 * This is needed because to work with the WebviewMessage types defined in VSCode TS.
 *
 * @param descriptors One or more FileDescriptors containing message type definitions
 * @return Map of webview message type (e.g. "chat-model-reply") to protobuf type URL (e.g. "type.googleapis.com/com.augmentcode.rpc.ChatModelReply")
 */
private fun createTypeMapping(vararg descriptors: FileDescriptor): Map<String, String> {
  return descriptors.flatMap { descriptor ->
    descriptor.messageTypes.mapNotNull { type ->
      val augmentTypeOption = type.options.getExtension(AugmentTypes.webviewMessageType)
      val webViewMsgDefTypeOption = type.options.getExtension(WebViewMsgDefTypes.webviewMessageType)

      when {
        !augmentTypeOption.isNullOrBlank() -> {
          augmentTypeOption to "type.googleapis.com/${type.fullName}"
        }
        !webViewMsgDefTypeOption.isNullOrBlank() -> {
          webViewMsgDefTypeOption to "type.googleapis.com/${type.fullName}"
        }
        else -> null
      }
    }
  }.toMap() + mapOf("empty" to "type.googleapis.com/google.protobuf.Empty")
}

private val typeDescriptors =
  arrayOf(
    ChatTypes.getDescriptor(),
    AugmentTypes.getDescriptor(),
    SettingsTypes.getDescriptor(),
    PreferencesTypes.getDescriptor(),
    CommonWebViewChatTypes.getDescriptor(),
  )

private val messagesTypeRegistry =
  TypeRegistry.newBuilder()
    .add(typeDescriptors.flatMap { it.messageTypes })
    .build()

private enum class TypeConversionDirection {
  TO_PROTOBUF,
  TO_WEBVIEW,
}

private val typeMapping = createTypeMapping(*typeDescriptors)

private val reverseTypeMapping = typeMapping.map { (k, v) -> v to k }.toMap()

/**
 * Converts JSON text from Protobuf format to webview format.
 * ex. {"@type": "type.googleapis.com/com.augmentcode.rpc.ChatModelReply"} -> {"type": "chat-model-reply"}
 */
private fun convertToWebviewTypes(jsonText: String): String {
  val mapper = ObjectMapper()
  val rootNode = mapper.readTree(jsonText)

  replaceTypeFields(rootNode, TypeConversionDirection.TO_WEBVIEW)

  return mapper.writeValueAsString(rootNode)
}

/**
 * Converts JSON text from webview format to Protobuf format
 * ex. {"type": "chat-model-reply"} -> {"@type": "type.googleapis.com/com.augmentcode.rpc.ChatModelReply"}
 */
private fun convertToProtobufTypes(jsonText: String): String {
  val mapper = ObjectMapper()
  val rootNode = mapper.readTree(jsonText)

  replaceTypeFields(rootNode, TypeConversionDirection.TO_PROTOBUF)

  return mapper.writeValueAsString(rootNode)
}

/**
 * Converts type field names and their values between Protobuf and webview formats.
 *
 * Protobuf uses "@type" fields with full type URLs
 * (e.g., "type.googleapis.com/com.augmentcode.rpc.ChatModelReply"),
 * while the webview uses "type" fields with simpler identifiers (e.g., "chat-model-reply").
 *
 * @param node The JSON node to process recursively
 * @param direction The conversion direction:
 *   - TO_PROTOBUF: Converts "type" -> "@type" for protobuf deserialization
 *   - TO_WEBVIEW: Converts "@type" -> "type" for webview consumption
 *
 * The mappings to "type" are defined by the `webview_message_type` option in the proto files. @type maps
 * to the full type URL of the proto message.
 */
private fun replaceTypeFields(
  node: JsonNode,
  direction: TypeConversionDirection,
) {
  if (node.isObject) {
    val objectNode = node as ObjectNode
    val fields = objectNode.fields()

    val fieldsToReplace = mutableMapOf<String, Pair<String, JsonNode>>()

    while (fields.hasNext()) {
      val field = fields.next()
      val fieldName = field.key
      val fieldValue = field.value

      if (fieldName == "type" && direction == TypeConversionDirection.TO_PROTOBUF) {
        // If the type field matches a protobuf type, swap it out with @type
        // This allows properties with name "type" to remain
        if (typeMapping.containsKey(fieldValue.asText())) {
          val updatedFieldValue = TextNode(typeMapping[fieldValue.asText()]!!)
          fieldsToReplace[fieldName] = "@type" to updatedFieldValue
        }
      }

      if (fieldName == "@type" && direction == TypeConversionDirection.TO_WEBVIEW) {
        val updatedFieldValue = reverseTypeMapping[fieldValue.asText()]?.let { TextNode(it) } ?: fieldValue
        fieldsToReplace[fieldName] = "type" to updatedFieldValue
      }

      replaceTypeFields(fieldValue, direction)
    }

    for ((oldFieldName, replacement) in fieldsToReplace) {
      objectNode.set<JsonNode>(replacement.first, replacement.second)
      objectNode.remove(oldFieldName)
    }
  } else if (node.isArray) {
    for (item in node) {
      replaceTypeFields(item, direction)
    }
  }
}

fun serializeProtoToJson(message: Message): String =
  convertToWebviewTypes(
    JsonFormat
      .printer()
      .sortingMapKeys()
      .includingDefaultValueFields()
      .printingEnumsAsInts()
      .usingTypeRegistry(messagesTypeRegistry)
      .print(Any.pack(message)),
  )

fun parseJsonIntoProtoBuilder(
  jsonText: String,
  builder: Any.Builder,
) {
  JsonFormat.parser()
    .ignoringUnknownFields()
    .usingTypeRegistry(messagesTypeRegistry)
    .merge(convertToProtobufTypes(jsonText), builder)
}

fun parseJsonToProto(jsonText: String): Any {
  val messageBuilder: Any.Builder = Any.newBuilder()
  parseJsonIntoProtoBuilder(jsonText, messageBuilder)
  return messageBuilder.build()
}

package com.augmentcode.intellij.utils

import java.nio.file.Paths

/**
 * Attempts to correct a relative path that might be relative to a parent directory of the root.
 *
 * For example:
 * - rootPath: "/home/<USER>/augment/clients"
 * - overlappingRelPath: "clients/main.py"
 * - Returns: "main.py" (removing the overlapping "clients" portion)
 *
 * @param rootPath The absolute path of the root directory
 * @param overlappingRelPath The relative path that might start with segments that overlap with the root path
 * @return The corrected relative path, or null if no overlap is detected
 */
fun fixOverlappingRelPath(
  rootPath: String,
  overlappingRelPath: String,
): String? {
  val rootSegments = Paths.get(rootPath).normalize().map { it.toString() }
  val pathSegments = Paths.get(overlappingRelPath).normalize().map { it.toString() }

  if (pathSegments.isEmpty() || rootSegments.isEmpty()) {
    return null
  }

  // Check if the first segment(s) of the given path match the last segment(s) of the root
  for (overlapLength in 1..minOf(rootSegments.size, pathSegments.size)) {
    val rootSuffix = rootSegments.takeLast(overlapLength)
    val pathPrefix = pathSegments.take(overlapLength)

    if (rootSuffix == pathPrefix) {
      // Found overlap, return the remaining path segments
      val remainingSegments = pathSegments.drop(overlapLength)
      return Paths.get("", *remainingSegments.toTypedArray()).toString()
    }
  }

  return null
}

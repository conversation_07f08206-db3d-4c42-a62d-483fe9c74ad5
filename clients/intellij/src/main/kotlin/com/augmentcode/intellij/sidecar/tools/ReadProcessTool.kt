package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.intellij.sidecarrpc.convertStruct
import com.augmentcode.sidecar.rpc.ReadProcessInputSchema
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.tools.ToolCallResponse
import com.google.protobuf.Struct
import com.intellij.execution.ExecutionManager
import com.intellij.openapi.project.Project

class ReadProcessTool : IdeTool {
  override val name = "read-process"

  override val description =
    """
    Read output from a terminal.

    If `wait=true` and the process has not yet completed, waits for the terminal to complete up to `max_wait_seconds` seconds before returning its output.

    If `wait=false` or the process has already completed, returns immediately with the current output.
    """.trimIndent().trimStart()

  override val inputMessageDescriptor = ReadProcessInputSchema.getDescriptor()

  override suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    val killRequest = convertStruct(input) { ReadProcessInputSchema.newBuilder() }.build()

    val processHandler =
      ExecutionManager.getInstance(project).getRunningProcesses()
        .find { AugmentTerminalInfo.TERMINAL_INFO_KEY.get(it)?.terminalId == killRequest.terminalId }
    if (processHandler == null) {
      return ToolCallResponse.newBuilder()
        .setText("Terminal ${killRequest.terminalId} not found")
        .setIsError(true)
        .build()
    }

    val outputListener = AugmentTerminalInfo.TERMINAL_INFO_KEY.get(processHandler)

    val response = StringBuilder()
    val status = if (processHandler.exitCode != null) "completed" else "still running"
    response.append("Here is the output from terminal ${killRequest.terminalId} (status: $status):\n")
    response.append("<output>")
    response.append(outputListener?.output?.stdout ?: "")
    response.append(outputListener?.output?.stderr ?: "")
    response.append("</output>\n")
    if (processHandler.exitCode != null) {
      response.append("<return-code>\n")
      response.append(processHandler.exitCode)
      response.append("\n")
      response.append("</return-code>\n")
    }
    return ToolCallResponse.newBuilder()
      .setText(response.toString())
      .build()
  }
}

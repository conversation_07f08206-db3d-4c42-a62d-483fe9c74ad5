package com.augmentcode.intellij.idea

import com.augmentcode.intellij.featurevector.FeatureVectorReporter
import com.augmentcode.intellij.metrics.EdtFreezeDetector
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity

class AugmentPostStartupActivity : ProjectActivity {
  override suspend fun execute(project: Project) {
    AugmentFileStore.migrateFiles()
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      // Initialize plugin state service
      PluginStateService.instance

      // Initialize EDT freeze detector
      EdtFreezeDetector.instance

      // Initialize workspace manager service
      project.service<WorkspaceCoordinatorService>()
      // initialize sidecar services
      project.serviceOrNull<SidecarService>()?.startServer()

      // Initialize feature vector reporter and report on plugin enabled
      val featureVectorReporter = FeatureVectorReporter.getInstance(project)

      // Defer the PluginStateService subscription to avoid startup timing issues
      ApplicationManager.getApplication().invokeLater {
        // Report feature vector when plugin state changes to enabled
        PluginStateService.instance.subscribe(
          project.messageBus.connect(),
          object : com.augmentcode.intellij.pluginstate.PluginStateListener {
            override fun onStateChange(
              context: com.augmentcode.intellij.pluginstate.PluginContext,
              state: com.augmentcode.intellij.pluginstate.PluginState
            ) {
              if (state == com.augmentcode.intellij.pluginstate.PluginState.ENABLED && context.isSignedIn) {
                featureVectorReporter.reportVector()
              }
            }
          },
          triggerOnStateChange = true
        )
      }
    }
  }
}

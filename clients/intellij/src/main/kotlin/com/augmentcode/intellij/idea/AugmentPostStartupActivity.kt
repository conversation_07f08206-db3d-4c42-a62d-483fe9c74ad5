package com.augmentcode.intellij.idea

import com.augmentcode.intellij.featurevector.FeatureVectorReporter
import com.augmentcode.intellij.metrics.EdtFreezeDetector
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity

class AugmentPostStartupActivity : ProjectActivity {
  override suspend fun execute(project: Project) {
    AugmentFileStore.migrateFiles()
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      // Initialize plugin state service
      PluginStateService.instance

      // Initialize EDT freeze detector
      EdtFreezeDetector.instance

      // Initialize workspace manager service
      project.service<WorkspaceCoordinatorService>()
      // initialize sidecar services
      project.serviceOrNull<SidecarService>()?.startServer()

      // Initialize feature vector reporter
      val featureVectorReporter = FeatureVectorReporter.getInstance(project)

      // Report feature vector immediately if plugin is enabled and user is signed in
      // TODO: Add proper plugin state monitoring when PluginStateService constructor issue is resolved
      featureVectorReporter.reportVector()
    }
  }
}

package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class RenameFileSyncTest : SyncTestCase() {
  @Test
  fun testRenameFile() =
    runBlocking {
      val state = MockEngineState()
      val mockEngine = createMockEngine(state)
      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val tempDir = getVirtualFile(createTempDirectory())
      PsiTestUtil.addContentRoot(module, tempDir)

      // Add file
      val file =
        runWriteAction {
          tempDir.createChildData(this, "foo.txt").also {
            VfsUtil.saveText(it, "foo")
          }
        }
      val originalBlobState = AugmentBlobStateReader.read(file.toPsiFile(project))
      assertNotNull(originalBlobState)
      val originalBlobName = originalBlobState!!.remoteName

      // Wait for initial upload and checkpoint
      waitForRequests(mockEngine, "/batch-upload", 1)
      waitForRequests(mockEngine, "/checkpoint-blobs", 1)

      // Rename the file
      WriteCommandAction.runWriteCommandAction(project) {
        file.rename(this, "bar.txt")
      }

      // Get the new blob name and force indexing to occur
      val renamedBlobState = AugmentBlobStateReader.read(file.toPsiFile(project))
      assertNotNull(renamedBlobState)
      val renamedBlobName = renamedBlobState!!.remoteName

      // Wait for the new blob to be uploaded
      waitForRequests(mockEngine, "/batch-upload", 2)

      // Wait for sync manager to process the state changes with timeout
      val syncManager = AugmentRemoteSyncingManager.getInstance(project)
      waitForAssertion({
        runBlocking {
          val payload = syncManager.synchronizedBlobsPayload()

          assertEquals(payload.checkpointId, "checkpoint-1")
          assertEquals(payload.addedBlobs, setOf(renamedBlobName))
          assertEquals(payload.deletedBlobs, setOf(originalBlobName))
        }
      }, timeoutMs = 10000, pollIntervalMs = 250)
    }
}

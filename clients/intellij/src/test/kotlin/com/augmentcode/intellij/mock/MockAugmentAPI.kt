package com.augmentcode.intellij.mock

import com.augmentcode.api.*
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.CheckpointResult
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.rpc.ChatInstructionStreamResult
import com.augmentcode.rpc.ChatResult
import com.augmentcode.rpc.WorkspaceFileChunk
import io.ktor.util.collections.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow

@Deprecated("Use AugmentHttpClient.httpClientFactory with Ktor MockEngine.")
class MockAugmentAPI(
  private val mockResults: Map<String, CompletionResult> = emptyMap(),
  private val knownBlobNames: Set<String> = emptySet(),
  private val chatResponses: List<String> = emptyList(),
  private val isAvailable: Boolean = true,
  private val modelConfig: ModelConfig? = defaultModelConfigForTests(),
) : AugmentAPI {
  constructor(mockResult: CompletionResult) : this(mapOf("" to mockResult))

  constructor(flags: FeatureFlags = FeatureFlags(), isAvailable: Boolean = true) : this(
    isAvailable = isAvailable,
    modelConfig = defaultModelConfigForTests(flags),
  )

  private val remotelyIndexedBlobs: MutableSet<String> = ConcurrentSet()

  override suspend fun available(): Boolean = isAvailable

  override fun nextSequenceId(): Int {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  private val generations: MutableMap<String, Int> = mutableMapOf()

  val recordedCompletions: MutableList<CompletionResolution> = mutableListOf()
  var recorderLastCompletionRequest: CompletionRequest? = null

  var recorderLastChatRequest: ChatRequest? = null
  val recorderLastClientMetrics = mutableListOf<ClientMetric>()

  val recorderLastOnboardingEvents = mutableListOf<OnboardingSessionEvent>()

  override suspend fun batchUpload(uploadRequest: BatchUploadRequest): BatchUploadResponse {
    val result = BatchUploadResponse()
    result.blobNames =
      uploadRequest.blobs.map {
        // fake the hash by having a human readable string for testing
        generations[it.path] = generations.getOrDefault(it.path, 0) + 1
        "hash(${it.path}).v${generations[it.path]}"
      }
    // make it immediately indexed
    remotelyIndexedBlobs.addAll(result.blobNames)
    return result
  }

  override suspend fun findMissing(findRequest: FindMissingRequest): FindMissingResponse {
    return FindMissingResponse().also {
      it.unknownMemoryNames = findRequest.memObjectNames.subtract(knownBlobNames + remotelyIndexedBlobs)
      it.nonindexedBlobNames = findRequest.memObjectNames.subtract(remotelyIndexedBlobs)
    }
  }

  override suspend fun complete(completionRequest: CompletionRequest): CompletionResult? {
    recorderLastCompletionRequest = completionRequest
    return mockResults[completionRequest.model ?: ""]
  }

  override suspend fun resolveCompletions(resolveRequest: ResolveCompletionsRequest) {
    recordedCompletions.addAll(resolveRequest.resolutions)
  }

  override suspend fun token(
    tenantURL: String,
    tokenRequest: AuthTokenRequest,
  ): AuthTokenResult? {
    return null
  }

  override suspend fun currentModel(): Model? {
    return fetchModelInfo()?.findModel(AugmentSettings.instance.modelName)
  }

  override fun getCachedModelInfo(): ModelConfig? {
    return modelConfig
  }

  override suspend fun fetchModelInfo(): ModelConfig? {
    return modelConfig
  }

  override suspend fun createCheckpoint(request: CheckpointBlobsRequest): CheckpointResult {
    throw NotImplementedError()
  }

  override suspend fun chat(request: ChatRequest): Pair<String, Flow<ChatResult>> {
    recorderLastChatRequest = request
    return "mock-chat-exchange-id" to
      chatResponses.map { chatResponse ->
        ChatResult.newBuilder()
          .setText(chatResponse)
          .addWorkspaceFileChunks(
            WorkspaceFileChunk.newBuilder()
              .setCharStart(10)
              .setCharEnd(20)
              .setBlobName("hash(chat.txt).v1")
              .setFile(
                com.augmentcode.rpc.FileDetails.newBuilder()
                  .setRepoRoot("")
                  .setPathName("chat.txt")
                  .build(),
              )
              .build(),
          )
          .build()
      }.asFlow()
  }

  override suspend fun chatFeedback(request: ChatFeedbackRequest): ChatFeedbackResponse? {
    return ChatFeedbackResponse()
  }

  override suspend fun completionFeedback(request: CompletionFeedbackRequest): CompletionFeedbackResponse? {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun saveChat(request: SaveChatRequest): SaveChatResponse? {
    return SaveChatResponse().apply {
      uuid = "mock-uuid"
      url = "mock-url"
    }
  }

  override suspend fun smartPaste(request: ChatInstructionStreamPayload): Pair<String, Flow<ChatInstructionStreamResult>> {
    throw NotImplementedError()
  }

  override suspend fun clientMetrics(metrics: List<ClientMetric>) {
    recorderLastClientMetrics.addAll(metrics)
  }

  override suspend fun onboardingSessionEvents(sessionEvents: List<OnboardingSessionEvent>) {
    recorderLastOnboardingEvents.addAll(sessionEvents)
  }

  override suspend fun searchExternalSources(
    query: String,
    sourceTypes: List<String>,
  ): SearchExternalSourcesResponse? {
    return SearchExternalSourcesResponse()
  }

  override suspend fun reportSmartPasteResolution(resolution: SmartPasteResolution) {
    throw NotImplementedError()
  }

  override suspend fun reportError(request: ReportErrorRequest) {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun agentCodebaseRetrieval(request: AgentCodebaseRetrievalRequest): AgentCodebaseRetrievalResponse {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun listRemoteTools(remoteToolIDs: List<Int>): ListRemoteToolsResult {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun runRemoteTool(
    requestId: String,
    toolName: String,
    toolInputJson: String,
    toolId: RemoteToolId,
    extraToolInput: ExtraToolInput?,
  ): RunRemoteToolResult {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun revokeToolAccess(toolId: RemoteToolId): RevokeToolAccessResult {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun checkToolSafety(
    toolId: Int,
    toolInputJson: String,
  ): CheckToolSafetyResponse {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun recordRequestEvents(
    requestId: String,
    request: RecordAgentRequestBody,
  ) {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun recordSessionEvents(request: RecordAgentSessionBody) {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun recordPreferenceSample(sample: PreferenceSample) {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  override suspend fun chatRawResponse(request: ChatRequest): Pair<String, Flow<Result<String>>> {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }

  fun resetRemotelyIndexedBlobs() {
    remotelyIndexedBlobs.clear()
  }

  override suspend fun fetchModelInfoNoCache(): ModelConfig? {
    throw NotImplementedError("MockAugmentAPI is deprecated.")
  }
}

fun defaultModelConfigForTests(flags: FeatureFlags = FeatureFlags()): ModelConfig {
  return ModelConfig().apply {
    defaultModelName = "default-model"
    supportedLanguages =
      listOf(
        Language().apply {
          name = "Text"
          extensions = setOf(".txt")
        },
        Language().apply {
          name = "Go"
          extensions = setOf(".go")
        },
        Language().apply {
          name = "XML"
          extensions = setOf(".xml")
        },
        Language().apply {
          name = "Java"
          extensions = setOf(".java")
        },
      )
    availableModels =
      listOf(
        Model().apply {
          name = defaultModelName
          suggestedPrefixCharCount = 4096
          suggestedSuffixCharCount = 4096
        },
        Model().apply {
          name = "alternative-model"
          suggestedPrefixCharCount = 9192
          suggestedSuffixCharCount = 9192
        },
      )
    featureFlags = flags
  }
}

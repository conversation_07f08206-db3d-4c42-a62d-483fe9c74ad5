package com.augmentcode.intellij.webviews

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class RPCAdapterTest : AugmentBasePlatformTestCase() {
  @Test
  fun testBase64AutoDecode() {
    AugmentWebviewStateStore.getInstance(project).set(
      AugmentWebviewStateKey.CHAT_STATE,
      "eyJmb28iOiAiYmFyIn0=",
    )
    val actualState = RPCAdapter.getStoredState(project, AugmentWebviewStateKey.CHAT_STATE)
    assertEquals("{\"foo\": \"bar\"}", actualState)
  }
}

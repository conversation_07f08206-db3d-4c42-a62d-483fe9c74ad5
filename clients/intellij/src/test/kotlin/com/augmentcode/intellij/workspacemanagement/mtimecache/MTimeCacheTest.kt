package com.augmentcode.intellij.workspacemanagement.mtimecache

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCache
import com.augmentcode.intellij.workspacemanagement.coordination.mtimecache.MTimeCacheEntry
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class MTimeCacheTest : AugmentBasePlatformTestCase() {
  @Test
  fun testGetUnknownFile() {
    val cache = MTimeCache.getInstance(project)
    val result = cache.get("unknown")
    assertNull(result)
  }

  @Test
  fun testGetKnownFile() {
    val testFile = myFixture.createFile("test.txt", "test content")
    val cacheEntry = MTimeCacheEntry(testFile.modificationStamp, testFile.length, "abc123")

    val cache = MTimeCache.getInstance(project)
    cache.put(testFile.path, cacheEntry)

    val result = cache.get(testFile.path)
    assertEquals(cacheEntry, result)
  }
}

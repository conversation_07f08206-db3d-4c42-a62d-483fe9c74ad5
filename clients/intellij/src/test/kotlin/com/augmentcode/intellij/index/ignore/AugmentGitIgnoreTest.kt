package com.augmentcode.intellij.index.ignore

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.index.AugmentLocalIndex
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.FeatureFlagsTestUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.TestDataPath
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

/**
 * Tests for the reimplementation of gitignore parsing.
 * All tests copied from the original GitIgnoreAndAugmentIgnoreInteractionTest, but
 * with the new gitignore parser enabled.
 */
@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class GitIgnoreAndAugmentIgnoreInteractionTest2 : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/ignorerules"

  override fun setUp() {
    super.setUp()
    FeatureFlagsTestUtil.setupFeatureFlags(
      testRootDisposable,
      mapOf("enable_homespun_gitignore_min_version" to "0.0.0"),
    )
  }

  @Test
  fun testAugmentOverridesRejection1() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))

    val root = myFixture.copyDirectoryToProject("example-project", ".")
    myFixture.addFileToProject("./dir-1/.gitignore", "*.py")
    myFixture.addFileToProject("./.augmentignore", "!*.py")

    // Check the path filter and stacks are what we expect
    val filter =
      runBlocking {
        pathFilterService.getPathFilter(root)
      }
    val stacks = filter.getStacks()
    assertEquals(listOf("", "dir-1"), stacks.keys.toList())

    val rootStack = stacks[""]
    val rootStacks = rootStack!!.getIgnoreStacks()
    assertEquals(2, rootStacks.size)
    assertEquals(listOf(".gitignore", ".augmentignore"), rootStacks.map { it.ignoreFileName })
    val rootGitIgnore = rootStacks[0]
    val rootAugmentIgnore = rootStacks[1]
    assertEquals(rootGitIgnore.ignoreFile, null)

    val augmentIgnoreFile = myFixture.findFileInTempDir("./.augmentignore")
    assertNotNull(augmentIgnoreFile)
    assertEquals(rootAugmentIgnore.ignoreFile, augmentIgnoreFile)

    val dir1Stack = stacks["dir-1"]
    val dir1Stacks = dir1Stack!!.getIgnoreStacks()
    assertEquals(2, dir1Stacks.size)
    assertEquals(listOf(".gitignore", ".augmentignore"), dir1Stacks.map { it.ignoreFileName })
    val dir1GitIgnore = dir1Stacks[0]
    val dir1AugmentIgnore = dir1Stacks[1]
    assertEquals(dir1GitIgnore.ignoreFile, myFixture.findFileInTempDir("./dir-1/.gitignore"))
    assertEquals(dir1AugmentIgnore.ignoreFile, augmentIgnoreFile)

    // Test the path filter gives us the expected results when queried with files
    val tests =
      mapOf(
        "abc.py" to true,
        "dir-1/abc.py" to true,
        "dir-1/dir-2/abc.py" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testAugmentOverridesRejection2() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))

    val root = myFixture.copyDirectoryToProject("example-project", ".")
    myFixture.addFileToProject("./dir-1/.gitignore", "*.py")
    myFixture.addFileToProject("./dir-1/.augmentignore", "!*.py")

    // Check the path filter and stacks are what we expect
    val filter =
      runBlocking {
        pathFilterService.getPathFilter(root)
      }
    val stacks = filter.getStacks()
    assertEquals(listOf("", "dir-1"), stacks.keys.toList())

    val rootStack = stacks[""]
    val rootStacks = rootStack!!.getIgnoreStacks()
    assertEquals(2, rootStacks.size)
    assertEquals(listOf(".gitignore", ".augmentignore"), rootStacks.map { it.ignoreFileName })
    val rootGitIgnore = rootStacks[0]
    val rootAugmentIgnore = rootStacks[1]
    assertEquals(rootGitIgnore.ignoreFile, null)
    assertEquals(rootAugmentIgnore.ignoreFile, null)

    val dir1Stack = stacks["dir-1"]
    val dir1Stacks = dir1Stack!!.getIgnoreStacks()
    assertEquals(2, dir1Stacks.size)
    assertEquals(listOf(".gitignore", ".augmentignore"), dir1Stacks.map { it.ignoreFileName })
    val dir1GitIgnore = dir1Stacks[0]
    val dir1AugmentIgnore = dir1Stacks[1]

    val gitIgnoreFile = myFixture.findFileInTempDir("./dir-1/.gitignore")
    assertNotNull(gitIgnoreFile)
    assertEquals(dir1GitIgnore.ignoreFile, gitIgnoreFile)

    val augmentIgnoreFile = myFixture.findFileInTempDir("./dir-1/.augmentignore")
    assertNotNull(augmentIgnoreFile)
    assertEquals(dir1AugmentIgnore.ignoreFile, augmentIgnoreFile)

    // Test the path filter gives us the expected results when queried with files
    val tests =
      mapOf(
        "abc.py" to true,
        "dir-1/abc.py" to true,
        "dir-1/dir-2/abc.py" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testAugmentOverridesRejection3() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    val root = myFixture.copyDirectoryToProject("example-project", ".")
    myFixture.addFileToProject("./dir-1/.gitignore", "*.py")
    myFixture.addFileToProject("./dir-1/dir-2/.augmentignore", "!*.py")

    // Check the path filter and stacks are what we expect
    val filter =
      runBlocking {
        pathFilterService.getPathFilter(root)
      }
    val stacks = filter.getStacks()
    assertEquals(listOf("", "dir-1", "dir-1/dir-2"), stacks.keys.toList())

    val rootStack = stacks[""]
    val rootStacks = rootStack!!.getIgnoreStacks()
    assertEquals(2, rootStacks.size)
    assertEquals(listOf(".gitignore", ".augmentignore"), rootStacks.map { it.ignoreFileName })
    val rootGitIgnore = rootStacks[0]
    val rootAugmentIgnore = rootStacks[1]
    assertEquals(rootGitIgnore.ignoreFile, null)
    assertEquals(rootAugmentIgnore.ignoreFile, null)

    val dir1Stack = stacks["dir-1"]
    val dir1Stacks = dir1Stack!!.getIgnoreStacks()
    assertEquals(2, dir1Stacks.size)
    assertEquals(listOf(".gitignore", ".augmentignore"), dir1Stacks.map { it.ignoreFileName })
    val dir1GitIgnore = dir1Stacks[0]
    val dir1AugmentIgnore = dir1Stacks[1]
    val gitIgnoreFile = myFixture.findFileInTempDir("./dir-1/.gitignore")
    assertNotNull(gitIgnoreFile)
    assertEquals(dir1GitIgnore.ignoreFile, gitIgnoreFile)
    assertEquals(dir1AugmentIgnore.ignoreFile, null)

    val dir2Stack = stacks["dir-1/dir-2"]
    val dir2Stacks = dir2Stack!!.getIgnoreStacks()
    assertEquals(2, dir2Stacks.size)
    assertEquals(listOf(".gitignore", ".augmentignore"), dir2Stacks.map { it.ignoreFileName })
    val dir2GitIgnore = dir2Stacks[0]
    val dir2AugmentIgnore = dir2Stacks[1]
    assertEquals(dir2GitIgnore.ignoreFile, gitIgnoreFile)

    val augmentIgnoreFile = myFixture.findFileInTempDir("./dir-1/dir-2/.augmentignore")
    assertNotNull(augmentIgnoreFile)
    assertEquals(dir2AugmentIgnore.ignoreFile, augmentIgnoreFile)

    // Test the path filter gives us the expected results when queried with files
    val tests =
      mapOf(
        "abc.py" to true,
        "dir-1/abc.py" to false,
        "dir-1/dir-2/abc.py" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testAugmentOverridesAcceptance1() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.copyDirectoryToProject("example-project", ".")
    myFixture.addFileToProject("./dir-1/.gitignore", "!*.py")
    myFixture.addFileToProject("./.augmentignore", "*.py")

    val tests =
      mapOf(
        "abc.py" to false,
        "dir-1/abc.py" to false,
        "dir-1/dir-2/abc.py" to false,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testAugmentOverridesAcceptance2() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.copyDirectoryToProject("example-project", ".")
    myFixture.addFileToProject("./dir-1/.gitignore", "!*.py")
    myFixture.addFileToProject("./dir-1/.augmentignore", "*.py")

    val tests =
      mapOf(
        "abc.py" to true,
        "dir-1/abc.py" to false,
        "dir-1/dir-2/abc.py" to false,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testAugmentOverridesAcceptance3() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.copyDirectoryToProject("example-project", ".")
    myFixture.addFileToProject("./dir-1/.gitignore", "!*.py")
    myFixture.addFileToProject("./dir-1/dir-2/.augmentignore", "*.py")

    val tests =
      mapOf(
        "abc.py" to true,
        "dir-1/abc.py" to true,
        "dir-1/dir-2/abc.py" to false,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testBothAugmentAndGitIgnore() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.addFileToProject("./dir-1/.gitignore", "*.py")
    myFixture.addFileToProject("./dir-1/.augmentignore", "*.js")
    myFixture.addFileToProject("./dir-1/abc.cxx", "")
    myFixture.addFileToProject("./dir-1/def.py", "")
    myFixture.addFileToProject("./dir-1/def.js", "")

    val tests =
      mapOf(
        "dir-1/abc.cxx" to true,
        "dir-1/def.py" to false,
        "dir-1/def.js" to false,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      assertNotNull(file)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testGitIgnoreCancelsParentRule() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.addFileToProject("./dir-1/.gitignore", "node_modules")
    myFixture.addFileToProject("./dir-1/dir-2/.gitignore", "!node_modules")
    myFixture.addFileToProject("./dir-1/node_modules/a.py", "")
    myFixture.addFileToProject("./dir-1/dir-2/node_modules/b.py", "")
    myFixture.addFileToProject("./dir-1/dir-2/node_modules/slug/c.py", "")

    val tests =
      mapOf(
        "dir-1/node_modules/" to false,
        "dir-1/node_modules/a.py" to false,
        "dir-1/dir-2/node_modules/b.py" to true,
        "dir-1/dir-2/node_modules/slug/c.py" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      assertNotNull(file)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      println("$path: $actual; expected: $expected")
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testAugmentIgnoreCancelsParentRule() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.addFileToProject("./dir-1/.augmentignore", "node_modules")
    myFixture.addFileToProject("./dir-1/dir-2/.augmentignore", "!node_modules")
    myFixture.addFileToProject("./dir-1/node_modules/a.py", "")
    myFixture.addFileToProject("./dir-1/dir-2/node_modules/b.py", "")
    myFixture.addFileToProject("./dir-1/dir-2/node_modules/slug/c.py", "")

    val tests =
      mapOf(
        "dir-1/node_modules/" to false,
        "dir-1/node_modules/a.py" to false,
        "dir-1/dir-2/node_modules/b.py" to true,
        "dir-1/dir-2/node_modules/slug/c.py" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      assertNotNull(file)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      println("$path: $actual; expected: $expected")
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testDirSpecificIgnoreRules() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.addFileToProject("./dir-1/.gitignore", "abc/xyz.py")
    myFixture.addFileToProject("./dir-1/abc/xyz.py", "")
    myFixture.addFileToProject("./dir-1/abc/abc/xyz.py", "")
    myFixture.addFileToProject("./dir-1/abc/abc/abc/xyz.py", "")

    val tests =
      mapOf(
        "dir-1/abc/xyz.py" to false,
        "dir-1/abc/abc/xyz.py" to true,
        "dir-1/abc/abc/abc/xyz.py" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      assertNotNull(file)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      println("$path: $actual; expected: $expected")
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testAugmentDirRules() {
    val ignoreFile =
      "giraffe/manatee/a*\n" +
        "/giraffe/manatee/b*\n" +
        "manatee/c*\n" +
        "/manatee/d*\n" +
        "/e*/\n" +
        "f*/\n" +
        "/**/p*\n" +
        "/**/w*/"
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.addFileToProject("./.augmentignore", ignoreFile)

    val tests =
      mapOf(
        "aaa.py" to true,
        "bbb.py" to true,
        "ccc.py" to true,
        "ddd.py" to true,
        "eee.py" to true,
        "fff.py" to true,
        "/egret/xxx.py" to false, // absent because `/e*/` is ignored
        "/ferret/xxx.py" to false, // absent because `f*/` is ignored
        "/ppp.py" to false,
        "/www.py" to true,
        "/giraffe/aaa.py" to true,
        "/giraffe/bbb.py" to true,
        "/giraffe/ccc.py" to true,
        "/giraffe/ddd.py" to true,
        "/giraffe/eee.py" to true,
        "/giraffe/fff.py" to true,
        "/giraffe/ferret/xxx.py" to false, // absent because `f*/` is ignored
        "/giraffe/ppp.py" to false,
        "/giraffe/www.py" to true,
        "/giraffe/manatee/aaa.py" to false,
        "/giraffe/manatee/bbb.py" to false,
        "/giraffe/manatee/ccc.py" to true,
        "/giraffe/manatee/ddd.py" to true,
        "/giraffe/manatee/eee.py" to true,
        "/giraffe/manatee/fff.py" to true,
        "/manatee/aaa.py" to true,
        "/manatee/bbb.py" to true,
        "/manatee/ccc.py" to false,
        "/manatee/ddd.py" to false,
        "/manatee/eee.py" to true,
        "/manatee/fff.py" to true,
        "/porcupine/xxx.py" to false, // absent because `/**/p*/` is ignored
        "/slug/egret/xxx.py" to true,
        "/slug/ferret/xxx.py" to false, // absent because `f*/` is ignored
        "/slug/eee.py" to true,
        "/slug/fff.py" to true,
        "/walrus/aaa.py" to false, // absent because `/**/w*/` is ignored
      )
    for ((path, expected) in tests) {
      val file = myFixture.addFileToProject(path, "")
      val actual = runBlocking { pathFilterService.isAccepted(file.virtualFile) }
      println("$path: $actual; expected: $expected")
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testCaseSensitivity() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.addFileToProject("./.gitignore", "foo*\n")

    val tests =
      mapOf(
        "foo.py" to false,
        "Foo.py" to true,
        "FOO.py" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.addFileToProject(path, "")
      val actual = runBlocking { pathFilterService.isAccepted(file.virtualFile) }
      println("$path: $actual; expected: $expected")
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testMultipleIgnoreAndAcceptRules() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))
    myFixture.addFileToProject("./.gitignore", "node_modules")

    val ignoreFile =
      "!node_modules\n" +
        "node_modules/*\n" +
        "!node_modules/slug"
    myFixture.addFileToProject("./.augmentignore", ignoreFile)

    val tests =
      mapOf(
        "index.js" to true,
        "node_modules/slug/index.js" to true,
        "node_modules/slug/dir/index.js" to true,
        "node_modules/package.json" to false,
        "node_modules/other-slug/index.js" to false,
        "node_modules/other-slug/dir/index.js" to false,
      )
    for ((path, expected) in tests) {
      val file = myFixture.addFileToProject(path, "")
      val actual = runBlocking { pathFilterService.isAccepted(file.virtualFile) }
      println("$path: $actual; expected: $expected")
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testFileAndDirectories() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))

    val ignoreFile =
      "abc\n" +
        "def\n" +
        "ghi/"
    myFixture.addFileToProject("./.augmentignore", ignoreFile)

    val tests =
      mapOf(
        "abc" to false, // File matches 'abc'
        "def/example.js" to false, // Directory def/ matches 'def'
        "ghi" to true, // File does not match 'ghi/'
      )
    for ((path, expected) in tests) {
      val file = myFixture.addFileToProject(path, "")
      val actual = runBlocking { pathFilterService.isAccepted(file.virtualFile) }
      println("$path: $actual; expected: $expected")
      assertEquals(expected, actual)
    }
  }

  @Test
  fun testInvalidGitIgnoreRule() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))

    myFixture.copyDirectoryToProject("example-project", ".")
    myFixture.addFileToProject(
      ".gitignore",
      """
      # Valid rule
      *.txt
      # Invalid rule
      [a-
      # Another valid rule
      build/
      """.trimIndent(),
    )

    // Create some test files
    myFixture.addFileToProject("valid.txt", "")
    myFixture.addFileToProject("test.py", "")
    myFixture.addFileToProject("build/output.log", "")

    // Check if the files are accepted or ignored
    var file = myFixture.findFileInTempDir("valid.txt")
    assertFalse(runBlocking { pathFilterService.isAccepted(file) }) // ignored

    file = myFixture.findFileInTempDir("test.py")
    assertTrue(runBlocking { pathFilterService.isAccepted(file) }) // accepted

    file = myFixture.findFileInTempDir("build/output.log")
    assertFalse(runBlocking { pathFilterService.isAccepted(file) }) // ignored
  }

  @Test
  fun testGitignoreUpdate() =
    runBlocking {
      // Enable indexing by adding allowed languages with get-models
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      HttpUtil.registerMockHttpClient(HttpUtil.mockEngineGetModels(), testRootDisposable)

      val pathFilterService = PathFilterService.getInstance(project)

      // Add files to project structure
      val root = myFixture.addFileToProject(".gitignore", "*.py")
      val emuFile = myFixture.addFileToProject("emu.js", "")
      val walrusFile = myFixture.addFileToProject("walrus.py", "")

      // Force indexing
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()
      FileBasedIndex.getInstance().ensureUpToDate(AugmentLocalIndex.NAME, project, null)

      // Verify files were indexed
      assertNotNull(AugmentBlobStateReader.read(project, root.virtualFile))
      assertNotNull(AugmentBlobStateReader.read(project, emuFile.virtualFile))
      assertNotNull(AugmentBlobStateReader.read(project, walrusFile.virtualFile))

      // Original test assertions
      assertTrue(pathFilterService.isAccepted(root.virtualFile))
      assertTrue(pathFilterService.isAccepted(emuFile.virtualFile))
      assertFalse(pathFilterService.isAccepted(walrusFile.virtualFile))

      // update .gitignore to ignore both *.py and *.js
      myFixture.configureByText(".gitignore", "*.py\n*.js")

      // Force indexing
      PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()
      FileBasedIndex.getInstance().ensureUpToDate(AugmentLocalIndex.NAME, project, null)

      // We no longer accept js or py files
      assertFalse(pathFilterService.isAccepted(emuFile.virtualFile))
      assertFalse(pathFilterService.isAccepted(walrusFile.virtualFile))

      // Clean up
      AugmentSettings.instance.apiToken = null
      AugmentSettings.instance.completionURL = null
    }
}

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class EnumerateTypesAndAcceptancesTest2 : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    FeatureFlagsTestUtil.setupFeatureFlags(
      testRootDisposable,
      mapOf("enable_homespun_gitignore_min_version" to "0.0.0"),
    )
  }

  @Test
  fun testEnumerateTypesAndAcceptances() {
    val pathFilterService = PathFilterService(myFixture.project, augmentHelpers().createCoroutineScope(Dispatchers.IO))

    myFixture.addFileToProject("./dir-1/.gitignore", "*.py")
    myFixture.addFileToProject("./dir-1/emu.js", "")
    myFixture.addFileToProject("./dir-1/walrus.py", "")
    myFixture.addFileToProject("./dir-1/dir-2/dolphin.js", "")

    // Test the path filter gives us the expected results when queried with files
    val tests =
      mapOf(
        "dir-1/emu.js" to true,
        "dir-1/walrus.py" to false,
        "dir-1/dir-2/dolphin.js" to true,
        "dir-1/" to true,
        "dir-1/dir-2/" to true,
      )
    for ((path, expected) in tests) {
      val file = myFixture.findFileInTempDir(path)
      val actual = runBlocking { pathFilterService.isAccepted(file) }
      assertEquals(expected, actual)
    }
  }
}

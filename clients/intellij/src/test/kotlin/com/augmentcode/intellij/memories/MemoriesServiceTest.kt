
package com.augmentcode.intellij.memories

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.vfs.findDocument
import com.intellij.testFramework.PlatformTestUtil
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class MemoriesServiceTest : AugmentBasePlatformTestCase() {
  private lateinit var memoriesService: MemoriesService

  override fun setUp() {
    super.setUp()
    memoriesService = MemoriesService.getInstance(project)
  }

  @Test
  fun testOpenMemoriesOpensInEditor() {
    // Act - open memories
    memoriesService.openMemories()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Memories file should be opened in editor",
      openFiles.any { it.name == "augment-memories.md" },
    )
  }

  @Test
  fun testUpdateAndGetMemories() {
    // Act - update memories with "foo"
    memoriesService.updateMemories("foo")

    // Assert - check that getMemories returns "foo"
    val result = memoriesService.getMemories()
    assertEquals("getMemories should return the updated content", "foo", result)
  }

  @Test
  fun testDocumentChangeUpdatesMemories() {
    // Arrange - open memories to create the document
    memoriesService.openMemories()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Get the document from the temp file
    val document = FileEditorManager.getInstance(project).openFiles.find { it.name == "augment-memories.md" }!!.findDocument()
    assertNotNull("Document should be created", document)

    // Act - simulate editing the document
    runWriteAction {
      document!!.setText("foo")
    }

    // Assert - verify getMemories returns the updated content
    assertEquals("foo", memoriesService.getMemories())
  }

  @Test
  fun testUpdateMemoriesUpdatesEditorContent() {
    // Arrange - open memories in editor
    memoriesService.openMemories()

    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Act - update memories with "foo"
    memoriesService.updateMemories("foo")

    // Assert - check that the document in the editor has been updated
    val openFile =
      FileEditorManager.getInstance(project).openFiles
        .first { it.name == "augment-memories.md" }
    val document = openFile.findDocument()
    assertNotNull("Document should exist", document)
    assertEquals("Editor document should contain updated content", "foo", document!!.text)
  }
}

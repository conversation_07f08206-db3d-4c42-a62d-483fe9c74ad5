package com.augmentcode.intellij.completion

import com.augmentcode.api.OnboardingSessionEventName
import com.augmentcode.intellij.generateCompletionResult
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.testFramework.TestDataPath
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@Suppress("UnstableApiUsage")
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentCompletionElementManipulatorTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    augmentHelpers().setupTestCredentials()
  }

  @Test
  fun testCompletionReuse() {
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)

      val gson = GsonUtil.createApiGson()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = gson.toJson(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      typeChar('H')
      delay() // so everything is processed
      typeChar('e')
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")
    }
  }

  @Test
  fun testCompletionAcceptanceReporting() {
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)

      val gson = GsonUtil.createApiGson()
      var onboardingEventCalled = false
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/completion" -> {
              respond(
                content = gson.toJson(completionResult),
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/record-onboarding-session-event" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              if (requestBody.contains(OnboardingSessionEventName.AcceptedCompletion.apiName)) {
                onboardingEventCalled = true
              }
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay()
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      waitForAssertion({
        assertTrue(onboardingEventCalled)
      })
    }
  }
}

package com.augmentcode.intellij.history

import com.augmentcode.api.CompletionRequest
import com.augmentcode.api.CompletionResult
import com.augmentcode.api.FeedbackRating
import com.augmentcode.intellij.completion.AugmentCompletionProvider
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import io.ktor.client.engine.mock.*
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentHistoryModelTest : AugmentBasePlatformTestCase() {
  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    // Set up fake API settings
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  @Test
  fun testAddCompletion() {
    val model = AugmentHistoryModel(project, augmentHelpers().createCoroutineScope(Dispatchers.Default))
    val completion =
      AugmentHistoryEntry(
        CompletionRequest(),
        CompletionResult(),
        0,
      ).apply {
        result.requestId = "test-id"
      }

    model.addCompletionAsync(completion)
    waitForAssertion({
      assertEquals(listOf(completion), model.getCompletions())
    })
  }

  @Test
  fun testGetFeedbackForNonexistentRequest() {
    val model = AugmentHistoryModel(project, augmentHelpers().createCoroutineScope(Dispatchers.Default))
    val state = model.getFeedback("nonexistent-id")
    assertEquals(FeedbackRating.UNSET, state.selectedRating)
    assertEquals("", state.feedbackText)
    assertFalse(state.sent)
  }

  @Test
  fun testCompletionAddedAfterApiCall() {
    val model = AugmentHistoryModel.getInstance(project)
    model.clear()
    var completionCalled = false
    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/completion" -> {
            completionCalled = true
            respond(
              content = """{"completion_items":[{"text":"Hello World"}]}""",
              status = HttpStatusCode.OK,
              headers = headersOf("Content-Type", "application/json"),
            )
          }
          else -> error("Unexpected request to ${request.url}")
        }
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    myFixture.testInlineCompletion {
      myFixture.configureByText("test.txt", "")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // give time for the API call to complete
      insert()
      delay(100) // give time for the entry to be added to the history model
    }

    assertTrue("AugmentAPI.complete should have been called", completionCalled)
    val completions = model.getCompletions()
    assertTrue(completions.isNotEmpty())
    val historyEntry =
      completions.firstOrNull {
        it.result.completionItems?.any { item -> item.text == "Hello World" } == true
      }
    assertNotNull(historyEntry)
    assertEquals(
      "Hello World",
      historyEntry
        ?.result
        ?.completionItems
        ?.first()
        ?.text,
    )
    assertNotNull(historyEntry?.result?.requestId)
  }

  @Test
  fun testDispose() {
    val model = AugmentHistoryModel(project, augmentHelpers().createCoroutineScope(Dispatchers.Default))

    // Add some data
    val completion =
      AugmentHistoryEntry(
        CompletionRequest(),
        CompletionResult(),
        0,
      ).apply {
        result.requestId = "test-id"
      }
    model.addCompletionAsync(completion)
    runBlocking { delay(100) }
    model.setFeedback(
      "test-id",
      AugmentFeedbackState(
        selectedRating = FeedbackRating.POSITIVE,
        feedbackText = "Great!",
        sent = true,
      ),
    )

    // Dispose
    model.dispose()

    // Verify everything is cleared
    assertTrue(model.getCompletions().isEmpty())
    assertEquals(AugmentFeedbackState(), model.getFeedback("test-id"))
  }
}

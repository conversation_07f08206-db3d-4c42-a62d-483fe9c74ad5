package com.intellij.ide.scratch

import com.augmentcode.intellij.index.AugmentRoot
import com.intellij.lang.xml.XMLLanguage
import com.intellij.openapi.util.Computable
import com.intellij.openapi.util.IntellijInternalApi
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.intellij.util.application
import org.junit.Test

@OptIn(IntellijInternalApi::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class ScratchFilePathTest : BasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/indexing"

  @Test
  fun testScratchFile() {
    val scratchFile =
      application.runWriteAction(
        Computable {
          val context = ScratchFileCreationHelper.Context()
          context.language = XMLLanguage.INSTANCE
          ScratchFileActions.doCreateNewScratch(project, context)
        },
      )
    assertNotNull(scratchFile)

    // make sure we can find the relative path for scratch files in "hidden" roots
    // they are still part of the project
    assertNotNull(AugmentRoot.findRelativePath(project, scratchFile!!.virtualFile))
  }
}

"""Module containing prompt formatting logic for different code edit models."""

import logging
import typing

from base.prompt_format_edit.droid_prompt_formatter import DroidEdit<PERSON>romptFormatter
from base.prompt_format_edit.prompt_formatter import (
    EditPromptFormatter,
    EditTokenApportionment,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer
from base.tokenizers.tokenizer import Tokenizer


def get_code_edit_prompt_formatter_by_name(
    name: str,
    tokenizer: Tokenizer,
    token_apportionment: typing.Optional[EditTokenApportionment] = None,
) -> EditPromptFormatter:
    """Returns the prompt formatter by name.

    Args:
        name: name of the formatter
        tokenizer: instance of the tokenizer to use.
        token_apportionment: The exact token apportionment of tokens during the prompt formatting. If not set, a default is used.

    If there is no formatter with the given name, an exception is thrown.
    """
    if name == "droid":
        assert isinstance(tokenizer, DeepSeekCoderInstructTokenizer), type(tokenizer)
        return DroidEditPromptFormatter(tokenizer, token_apportionment)
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError("Invalid prompt formatter name")

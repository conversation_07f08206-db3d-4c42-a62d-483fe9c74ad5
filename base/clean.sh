#!/bin/bash -e
# Removes unused generated proto files from the workspace directory.
#
# Usage: `bazel run //tools/generate_proto_typestubs:clean`
#
# If you wish to only keep files required by base install
# Usage: `bazel run //base:clean`.
#
# By default, we do not remove files still used in bazel targets.
# If you want to remove all generated proto files, then run
# `bazel run //base:clean -- --all`.

# Parse command line arguments
all=false
while [[ $# -gt 0 ]]; do
	case $1 in
	--all)
		all=true
		shift
		;;
	*)
		echo "Unknown option: $1"
		exit 1
		;;
	esac
done

if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY not set (run with bazel)" >&2
	exit 1
fi

OUTDIR="$BUILD_WORKSPACE_DIRECTORY"

paths=(
	"$OUTDIR"
)

exclude=(
	"$OUTDIR/node_modules"
)

patterns=(
	'*_pb2.py'
	'*_pb2.pyi'
	'*_pb2_grpc.py'
	'*_pb2_grpc.pyi'
)

# Build the exclusion arguments
prune_args=()
for excl in "${exclude[@]}"; do
	if [ ${#prune_args[@]} -eq 0 ]; then
		prune_args=(-path "$excl")
	else
		prune_args+=(-o -path "$excl")
	fi
done

# Build the pattern matching arguments
find_args=()
for pattern in "${patterns[@]}"; do
	if [ ${#find_args[@]} -eq 0 ]; then
		find_args=(-name "$pattern")
	else
		find_args+=(-o -name "$pattern")
	fi
done

# Construct the full find command
find_cmd=("${paths[@]}")
if [ ${#exclude[@]} -gt 0 ]; then
	find_cmd+=(\( "${prune_args[@]}" \) -prune -o)
fi
# NOTE: -print is required so that we don't include the exclusion
# directories in the output
find_cmd+=(\( "${find_args[@]}" \) -type f -print)

echo -n "Running: "
printf '%q ' find "${find_cmd[@]}"
echo

proto_files=$(find "${find_cmd[@]}")

if [ -z "$proto_files" ]; then
	echo "No generated proto files found in $OUTDIR"
	exit 0
fi

# Filter out files that are still used
filtered_files=()
if [ "$all" = true ]; then
	# Use all found files if filtering is skipped
	filtered_files=($proto_files)
else
	for file in $proto_files; do
		rel_path=${file#"$OUTDIR/"}
		current_file="./$rel_path"
		if [ -f "$current_file" ]; then
			continue
		fi
		filtered_files+=("$file")
	done
fi

if [ ${#filtered_files[@]} -eq 0 ]; then
	echo "No files to remove - all files match bazel targets."
	exit 0
fi

echo "Found the following generated proto files to remove:"
printf '%s\n' "${filtered_files[@]}"
echo
echo "Are you sure you want to remove these files? [y/N] "
read -r response

if [ "$response" = "y" ] || [ "$response" = "Y" ]; then
	echo "Removing generated proto files."
	printf '%s\n' "${filtered_files[@]}" | xargs rm -f
	echo "Finish clean for base."
else
	echo "Operation cancelled."
	exit 0
fi

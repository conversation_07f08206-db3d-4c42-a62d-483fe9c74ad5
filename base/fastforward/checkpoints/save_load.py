"""Utils to save and load checkpoints."""

import json
import logging
from pathlib import Path
from typing import Optional, Sequence, Union

import torch

from base.fastforward.checkpoints.impl import save_load_v1, save_load_v2, sharding

DEFAULT_WEIGHT_DTYPE = torch.float16
Tensor = torch.Tensor
Device = Union[str, torch.device]
WeightFn = save_load_v1.WeightFn
default_weight_fn = save_load_v1.default_weight_fn


def try_get_version(path: Union[str, Path]) -> Optional[str]:
    """Try to get the version of the checkpoint at `path`.

    Args:
        path: where to load the weights.

    Returns:
        the version of the checkpoint. if the checkpoint does not exist,
        return None.
    """
    if isinstance(path, str):
        if path.startswith("gs://"):
            # At this time, only v2 manifests are supported in GCS; no need to load
            return "2"
        else:
            path = Path(path)

    root_manifest_path = path / save_load_v2.MANIFEST_FILE_NAME
    if root_manifest_path.exists():
        manifest_dict = json.loads(root_manifest_path.read_text())
        version_str = manifest_dict.get(
            "manifestVersion",
            "1",  # v1 checkpoints did not have a manifestVersion.
        )
        return version_str
    else:
        return None


def save_weights(
    path: Union[str, Path],
    weights: dict[str, torch.Tensor],
    max_workers: int = 16,
    detach_grad: bool = True,
    incremental: bool = False,
):
    """Save a dictionary of weight tensors to `path`.

    For each key `weight_name` in `weights`, this method stores
    `weights[weight_name]` at `path/weight_name`, in two files:

        path/{weight_name}/data.bin  | store the values in the weight tensor
        path/{weight_name}/info.json | store the metadata of the weights.

    We also store one extra file `path/info.json` which stores all the
    `weight_name`s and the hash of the all the weight hashes (sorted
    lexicographically). During loading, we use this hash to verify the
    checkpoint is loaded correctly.

    NOTE(hieu): currently, path/{weight_name}/info.json only stores the
        weight's hash We might want to store other values.

    Args:
        path: where to save the weights.
        weights: a dictionary mapping weight names (as str) to the weight torch.Tensors.
        max_workers: maximum degree of parallelism.
        detach_grad: we store the weights as serialized torch.Tensor objects.
            when this flag is set to True, we call weights.detach() before
            serializing. this prevents the awkward situations when we load weights
            from a saved checkpoint and still have a `weight.requires_grad` set to
            True, causing inefficient memory usage while loading.
        incremental: if True, we assume that the checkpoint exists. we will first
            check the file `{ckpt-path}/info.json`, exclude the weight_name that
            are already there, and save the rest in `weights` to their new path.
            we will also update `info.json` accordingly.
        manifest_version: the version of the manifest to use.
    """
    return save_load_v2.save_weights(
        path=path,
        weights=weights,
        max_workers=max_workers,
        detach_grad=detach_grad,
        incremental=incremental,
    )


def load_weights(
    path: Union[str, Path],
    dtype: Optional[torch.dtype] = None,
    device: Device = "cpu",
    weight_fn: WeightFn = save_load_v1.default_weight_fn,
    max_workers: int = 16,
    require_patterns: Sequence[str] = (),
    shard_load_args: sharding.ShardLoadArgsMap = {},
    target_sha256: Optional[str] = None,
    override_sha_check: bool = False,
) -> dict[str, torch.Tensor]:
    """Load a dictionary of weight tensors from `path`. Verify the loaded SHA-256.

    Args:
        path: where to load the weights.
        dtype: the dtype of the weights. if None, we will use the dtype of the
            weights in the checkpoint.
        device: the device to load the weights to.
        weight_fn: a function that takes in a weight name, a weight as bytes, and
            a device, and returns a weight tensor.
        max_workers: maximum degree of parallelism.
        require_patterns: a list of regex patterns. if not empty, we will load
            only those weights that match at least one of the patterns.
        target_sha256: the SHA-256 of the checkpoint must match the SHA-256 of
            the checkpoint.
        override_sha_check: if True, will skip the SHA-256 verification.

    Returns:
        a dictionary mapping weight names (as str) to the weight torch.Tensors.
    """
    manifest_version = try_get_version(path)
    if manifest_version is None:
        raise ValueError(
            f"The checkpoint at '{path}' does not have a manifest. "
            "This is an invalid checkpoint."
        )

    # Fallback to the original implementation if a newer version is not available.
    if manifest_version == "1":
        raise ValueError(
            f"The checkpoint at '{path}' is using checkpoints v1, an older version of model checkpointing. "
            "This behavior is deprecated and will be removed in the future. "
            "Falling back to v1 loading logic."
        )
    else:
        return save_load_v2.load_weights(
            path=path,
            target_sha256=target_sha256 or "",
            dtype=dtype,
            device=device,
            max_workers=max_workers,
            name_filters=require_patterns,
            name_to_shard_args=shard_load_args,
            skip_checking_checksum=override_sha_check,
        )

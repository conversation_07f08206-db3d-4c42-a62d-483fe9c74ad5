import logging
from base.prompt_format_postprocess.sentry_prompt_formatter import (
    SentryPromptFormatter,
    SentryTokenApportionment,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.tokenizers.tokenizer import Tokenizer


def get_postprocess_prompt_formatter_by_name(
    name: str,
    tokenizer: Tokenizer,
    apportionment_config: SentryTokenApportionment | None = None,
    docsets: list[str] | None = None,
) -> SentryPromptFormatter:
    """Returns the prompt formatter by name.

    Args:
        name: name of the formatter
        tokenizer: instance of the tokenizer to use.
        apportionment_config: Hints for the apportionment of tokens during the prompt formatting.
                            If not set, a default is used.
        prompt_formatter_config: optional additional configuration for the prompt formatter.

    If there is no formatter with the given name, an exception is thrown.
    """
    if name == "sentry_v1":
        assert isinstance(tokenizer, Qwen25CoderTokenizer), type(tokenizer)
        return SentryPromptFormatter(tokenizer, apportionment_config)
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError(f"Invalid prompt formatter name: {name}.")

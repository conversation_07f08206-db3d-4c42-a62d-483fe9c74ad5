package durationutil

import (
	"encoding/json"
	"errors"
	"time"
)

/*
 * JSONDuration is a custom type that can be used to marshal/unmarshal a
 * duration to/from JSON.
 */
type JSONDuration time.Duration

func (d JSONDuration) MarshalJSON() ([]byte, error) {
	return json.Marshal(time.Duration(d).String())
}

func (d *JSONDuration) UnmarshalJSON(b []byte) error {
	var v interface{}
	if err := json.Unmarshal(b, &v); err != nil {
		return err
	}
	switch value := v.(type) {
	case string:
		dur, err := time.ParseDuration(value)
		if err != nil {
			return err
		}
		*d = JSONDuration(dur)
		return nil
	default:
		return errors.New("invalid duration")
	}
}

func (d *JSONDuration) ToDuration() time.Duration {
	return time.Duration(*d)
}

"""Helper to write kubernetes based tests."""

import errno
import sys
import base64
import io
import json
import logging
import os
import pathlib
import socket
import subprocess
import tempfile
import time
import datetime
import typing
import secrets
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path

import grpc
import yaml
from kubernetes import client, config
from yaml.loader import SafeLoader

from base.cloud.k8s.kubectl import get_dev_namespace
from base.python.cloud import cloud as cloud_lib
from base.python.grpc.health_check import HealthChecker

from google.cloud import bigtable
from google.cloud.bigtable.row_filters import StripValueTransformerFilter
from google.cloud.bigtable import table as bigtable_table

from google.api_core import client_options

T = typing.TypeVar("T")
ContextManager = typing.Generator[T, None, None]


def _setup_kubernetes(kube_config: Path | None):
    if not kube_config:
        config.load_incluster_config()
    else:
        config.kube_config.load_kube_config(config_file=str(kube_config))


def is_local_port_used(port: int) -> bool:
    """Returns whether the given port is used."""
    try:
        s = socket.create_connection(("localhost", port), timeout=0.5)
        s.close()
        print(f"Port {port} is used", flush=True)
        return True
    except ConnectionRefusedError:
        return False
    except OSError as ex:
        if ex.errno == errno.EADDRNOTAVAIL:
            # We seem to get this when running in a container
            return False
        print(f"Port {port} is possibly used: ", ex, flush=True)
        return True


def wait_for_local_port(port: int, timeout: float):
    """Waits for the given port to be available."""
    start_time = time.monotonic()
    end_time = start_time + timeout
    while True:
        exception = None
        try:
            socket.create_connection(("localhost", port), timeout=timeout).close()
            return
        except OSError as ex:
            exception = ex

        time_left = end_time - time.monotonic()
        if time_left <= 0:
            print(
                f"Port {port} is not available after {timeout}s: {exception}",
                flush=True,
            )
            raise TimeoutError(f"Port {port} is not available after {timeout}s")
        time.sleep(min(time_left, 0.5))


def get_api_client(
    kube_config: Path | None, context: str | None
) -> client.ApiClient | None:
    """Return the api config for the given context."""
    if not kube_config:
        return None
    contexts, active_context = config.list_kube_config_contexts()
    assert contexts, "Cannot find any context in kube config"
    contexts = [context["name"] for context in contexts]  # type: ignore
    active_index = contexts.index(active_context["name"])
    if context:
        active_index = contexts.index(context)
        assert active_index >= 0

    return config.new_client_from_config(context=contexts[active_index])


def _run_kubectl(
    kube_config: Path | None,
    context: str | None,
    args: list[str],
    stdin: str | None = None,
    timeout: float = 60.0,
):
    cmd = [
        "../k8s_binary/file/kubectl",
    ]
    cmd.extend(args)
    if kube_config:
        cmd.append(
            f"--kubeconfig={kube_config}",
        )
    if context:
        cmd.append(
            f"--context={context}",
        )
    if stdin:
        with tempfile.NamedTemporaryFile(mode="w+") as stdin_file:
            stdin_file.write(stdin)
            stdin_file.flush()
            subprocess.check_call(
                cmd, stdin=Path(stdin_file.name).open(mode="rb"), timeout=timeout
            )
    else:
        subprocess.check_call(cmd, timeout=timeout)


def _get_kube_config_file():
    """Returns the kube config file to use or None if no kube config file was found."""
    if (
        "HOME" in os.environ
        and Path(os.environ["HOME"]).joinpath(".kube", "config").exists()
    ):
        return Path(os.environ["HOME"]).joinpath(".kube", "config")
    if (
        "USER" in os.environ
        and Path("/home", os.environ["USER"]).joinpath(".kube", "config").exists()
    ):
        return Path("/home", os.environ["USER"]).joinpath(".kube", "config")
    return None


def _resolve_kubecfg(
    kubecfg_bin: Path,
    cloud: str,
    kube_config_file: Path | None,
    kubecfg_extra_args: list[str] | None = None,
    namespace: str | None = None,
) -> str:
    """Resolve the kubecfg binary.

    Args:
        kubecfg_bin: The kubecfg binary to run.
        kube_config_file: The kube config file to use.
        kubecfg_extra_args: Extra arguments to pass to the kubecfg binary.

    Returns:
        The resolved kubecfg output.
    """
    assert kubecfg_bin.exists()
    args = [kubecfg_bin]

    if kubecfg_extra_args:
        args += kubecfg_extra_args
    args += ["--cloud", cloud]
    args += ["dump"]
    if kube_config_file:
        args += ["--kube-config-file", kube_config_file]
    else:
        args += ["--kube-config-file="]
    if namespace:
        args += ["--namespace", namespace]
    output = subprocess.check_output(args, encoding="utf-8")
    return output


def _parse_resolve_output(output) -> tuple[str | None, list[str]]:
    data = list(yaml.load_all(io.StringIO(output), Loader=SafeLoader))
    deployments_result = []
    found_namespace = None
    for item in data:
        if not item:
            continue
        if item.get("kind") == "Deployment":
            name = item["metadata"]["name"]
            namespace = item["metadata"].get("namespace")
            if namespace and not found_namespace:
                found_namespace = namespace
            deployments_result.append(name)
    for item in data:
        if not item:
            continue
        if "metadata" in item:
            namespace = item["metadata"].get("namespace")
            if namespace and not found_namespace:
                found_namespace = namespace
    return found_namespace, deployments_result


def _parse_k8s(resolve: str) -> tuple[str | None, list[str]]:
    return _parse_resolve_output(resolve)


class Kubectl:
    """Wrapper to call kubectl commands with the right kubeconfig file."""

    def __init__(
        self, kube_config_file: Path | None, context: str | None, namespace: str
    ):
        self.kube_config_file = kube_config_file
        self.namespace = namespace
        self.context = context
        self.api_client = get_api_client(kube_config=kube_config_file, context=context)

    @contextmanager
    def popen(
        self, args: list[str], **kwargs
    ) -> typing.Generator[subprocess.Popen[str], None, None]:
        """Creates the kubectl process with the given arguments."""
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.extend(args)
        cmd.append("-n")
        cmd.append(self.namespace)
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        with subprocess.Popen(cmd, **kwargs) as p:
            yield p

    def run(
        self,
        args: list[str],
        encoding: str = "utf-8",
        check=False,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        **kwargs,
    ) -> subprocess.CompletedProcess[str]:
        """Runs kubectl process with the given arguments."""
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.extend(args)
        cmd.append("-n")
        cmd.append(self.namespace)
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        return subprocess.run(
            cmd, check=check, encoding=encoding, stdout=stdout, stderr=stderr, **kwargs
        )

    def rollout(
        self,
        deployment: str,
        timeout: datetime.timedelta = datetime.timedelta(seconds=60),
    ):
        """Rollout the given deployment."""
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.append("rollout")
        cmd.append("restart")
        cmd.append("deployment")
        cmd.append(deployment)
        cmd.append("-n")
        cmd.append(self.namespace)
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        subprocess.check_call(cmd)

        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.append("rollout")
        cmd.append("status")
        cmd.append("deployment")
        cmd.append(deployment)
        cmd.append("-n")
        cmd.append(self.namespace)
        cmd.append("--watch=true")
        cmd.append(f"--timeout={int(timeout.total_seconds())}s")
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        subprocess.check_call(cmd)

    def apply_data(
        self,
        data: str,
        retry_count: int = 15,
        retry_sleep: int = 60,
        timeout: float | None = 120.0,
    ):
        """Apply a data string."""
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.append("apply")
        cmd.append("-f")
        cmd.append("-")
        cmd.append("-n")
        cmd.append(self.namespace)
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        print(f"Running {cmd=} [data]", flush=True)
        with tempfile.TemporaryFile(mode="w") as f:
            f.write(data)
            f.flush()
            f.seek(0)

            for _ in range(retry_count):
                r = subprocess.run(
                    cmd,
                    stdin=f,
                    capture_output=True,
                    check=False,
                    encoding="utf-8",
                    timeout=timeout,
                )
                print(r.stdout)
                print(r.stderr, file=sys.stderr)
                if "Warning: Detected changes" in r.stderr:
                    time.sleep(retry_sleep)
                    print("Retrying apply", flush=True)
                    f.seek(0)
                    continue
                if r.returncode:
                    raise ValueError(f"Failed to apply data: {r.stderr}")
                break

        print("kubectl apply finished", flush=True)

    def apply(
        self,
        data: typing.Any,
        retry_count: int = 15,
        retry_sleep: int = 60,
        timeout: float | None = 120.0,
    ):
        """Apply the data dictionary."""
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.append("apply")
        cmd.append("-f")
        cmd.append("-")
        cmd.append("-n")
        cmd.append(self.namespace)
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        print(f"Running {cmd=} {data=}", flush=True)
        with tempfile.TemporaryFile(mode="w") as f:
            yaml.safe_dump(data, f)
            f.seek(0)

            for _ in range(retry_count):
                r = subprocess.run(
                    cmd,
                    stdin=f,
                    capture_output=True,
                    check=True,
                    encoding="utf-8",
                    timeout=timeout,
                )
                print(r.stdout)
                print(r.stderr, file=sys.stderr)
                if "Warning: Detected changes" in r.stdout:
                    time.sleep(retry_sleep)
                    print("Retrying apply", flush=True)
                    f.seek(0)
                    continue
                break

        print("kubectl apply finished", flush=True)

    def get_object(self, kind: str, name: str, namespace: str) -> typing.Any | None:
        """Gets an object.

        Args:
          kind: The kind of the object.
          name: The name of the object.
          namespace: The namespace of the object.

        Returns:
          The object.
        """
        cmd = ["../k8s_binary/file/kubectl", "get", kind, "-o", "json", name]
        if namespace:
            cmd += ["-n", namespace]
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        o = subprocess.run(cmd, encoding="utf-8", capture_output=True, check=False)
        if o.returncode != 0:
            if "not found" in o.stderr:
                return None
            raise Exception("Failed to get object")
        return json.loads(o.stdout)

    def list(self, kind: str, namespace: str) -> typing.Iterable[typing.Any]:
        """Lists a kind of object.

        Args:
          kind: The kind of the objects to list.
          namespace: The namespace of the objects to list.

        Returns:
          A list of the objects of that kind in that namespace.
        """
        cmd = ["../k8s_binary/file/kubectl", "get", kind, "-o", "json"]
        if namespace:
            cmd += ["-n", namespace]
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        o = subprocess.run(cmd, encoding="utf-8", capture_output=True, check=False)
        if o.returncode != 0:
            if "not found" in o.stderr:
                return []
            raise Exception("Failed to get object")
        return json.loads(o.stdout).get("items", [])

    def delete(self, data: typing.Any, timeout: float = 300.0, ignore_not_found=False):
        """Delete the data dictionary."""
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.append("delete")
        cmd.append("-f")
        cmd.append("-")
        cmd.append("-n")
        cmd.append(self.namespace)
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        if ignore_not_found:
            cmd.append("--ignore-not-found=true")
        print(f"Running {cmd=} {data=}", flush=True)
        with tempfile.TemporaryFile(mode="w") as f:
            yaml.safe_dump(data, f)
            f.flush()
            f.seek(0)
            subprocess.check_call(cmd, stdin=f, timeout=timeout)
        print("kubectl delete finished", flush=True)

    def delete_data(self, data: str, timeout: float = 300.0, ignore_not_found=False):
        """Delete the data string."""
        cmd = [
            "../k8s_binary/file/kubectl",
        ]
        cmd.append("delete")
        cmd.append("-f")
        cmd.append("-")
        cmd.append("-n")
        cmd.append(self.namespace)
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        if ignore_not_found:
            cmd.append("--ignore-not-found=true")
        print(f"Running {cmd=} [data]", flush=True)
        with tempfile.TemporaryFile(mode="w") as f:
            f.write(data)
            f.flush()
            f.seek(0)
            subprocess.check_call(cmd, stdin=f, timeout=timeout)
        print("kubectl delete finished", flush=True)

    @contextmanager
    def port_forward(
        self, pod: str, remote_port: int, local_port: int
    ) -> ContextManager[int]:
        """Create a port forwarding to a given pod."""
        assert not is_local_port_used(local_port)

        with self.popen(
            [
                "port-forward",
                pod,
                f"{local_port}:{remote_port}",
            ]
        ) as p:
            try:
                try:
                    wait_for_local_port(local_port, timeout=5.0)
                except TimeoutError:
                    # Clients of port_forward have retry loops that don't handle
                    # exceptions. So just yield the port to them. They will do their
                    # own connectivity test and retry.
                    pass
                print(
                    f"Yielding {local_port} as local port for {pod}:{remote_port}",
                    flush=True,
                )

                yield local_port
            finally:
                p.kill()

    def load_grpc_credentials(self) -> grpc.ChannelCredentials | None:
        """Load the gRPC credentials from the namespace."""
        cmd = [
            "../k8s_binary/file/kubectl",
            "get",
            "secret",
            "support-certificate",
            "--namespace",
            self.namespace,
            "-o",
            "yaml",
        ]
        if self.kube_config_file:
            cmd.append(
                f"--kubeconfig={self.kube_config_file}",
            )
        if self.context:
            cmd.append(
                f"--context={self.context}",
            )
        r = subprocess.run(cmd, check=True, stdout=subprocess.PIPE)
        r = yaml.safe_load(r.stdout)
        ca = base64.b64decode(r["data"]["ca.crt"])
        key = base64.b64decode(r["data"]["tls.key"])
        tls = base64.b64decode(r["data"]["tls.crt"])
        return grpc.ssl_channel_credentials(
            root_certificates=ca, private_key=key, certificate_chain=tls
        )


@dataclass
class Resolve:
    """Information for a given resolve call."""

    resolve: str
    namespace: str
    deployment_names: list[str]

    def apply(self, kubectl: Kubectl):
        """Apply the resolved configuration."""
        kubectl.apply_data(self.resolve, timeout=120.0)

    def wait(self, kube_config_file: Path | None, context: str | None):
        """Wait for the deployments to roll-out."""
        for deployment_name in self.deployment_names:
            _run_kubectl(
                kube_config_file,
                context=context,
                args=[
                    "rollout",
                    "status",
                    "deployment",
                    deployment_name,
                    "-n",
                    self.namespace,
                    "--timeout",
                    "30m",
                ],
                timeout=30 * 60,
            )

    def _resolve_no_bigtable(self) -> str:
        """Resolve the configuration, but remove all bigtable resources.
        This is useful when we want to tear down the deployment, but not the bigtable
        resources, since creating and destroying the tables has a rate limit.
        """
        resources = list(yaml.load_all(io.StringIO(self.resolve), Loader=SafeLoader))
        filtered_resources = []
        for resource in resources:
            if resource.get("kind") in [
                "BigtableTable",
                "BigtableGCPolicy",
                "BigtableInstance",
            ] or (
                resource.get("kind") == "IAMPartialPolicy"
                and resource.get("spec", {}).get("resourceRef", {}).get("kind")
                == "BigtableTable"
            ):
                logging.info(
                    f"{resource.get('kind')} \"{resource.get('metadata', {}).get('name')}\" deletion SKIPPED"
                )
            else:
                filtered_resources.append(resource)

        return yaml.safe_dump_all(filtered_resources)

    def _resolve_filter_abondon(self, resolve: str) -> str:
        """Filter out objects marked for abandonment."""
        resources = list(yaml.load_all(io.StringIO(resolve), Loader=SafeLoader))
        filtered_resources = []
        for resource in resources:
            annocations = resource["metadata"].get("annotations", {})
            if not annocations:
                filtered_resources.append(resource)
                continue
            if (
                annocations.get("cnrm.cloud.google.com/deletion-policy") == "abandon"
            ) or (annocations.get("eng.augmentcode.com/deletion-policy") == "abandon"):
                logging.info(
                    f"{resource.get('kind')} \"{resource.get('metadata', {}).get('name')}\" deletion SKIPPED"
                )
            else:
                filtered_resources.append(resource)

        return yaml.safe_dump_all(filtered_resources)

    def remove(
        self,
        kubectl: Kubectl,
        teardown_bigtable: bool = False,
        timeout: float = 120.0,
    ):
        """Remove the configuration."""
        try:
            k8s_objects = (
                self.resolve if teardown_bigtable else self._resolve_no_bigtable()
            )
            k8s_objects = self._resolve_filter_abondon(k8s_objects)
            kubectl.delete_data(k8s_objects, ignore_not_found=True, timeout=timeout)
        except subprocess.TimeoutExpired:
            print("Timeout deleting resources")
            # we continue regardless


@dataclass
class DeployInfo:
    """Information about the deployments."""

    namespace: str
    resolves: list[Resolve]
    kubectl: Kubectl


def _resolve(
    kubecfg_binaries: list[Path],
    cloud: str,
    kube_config_file: Path | None,
    kubecfg_extra_args: list[str] | None = None,
    namespace: str | None = None,
) -> list[Resolve]:
    assert kubecfg_binaries
    result = []
    for kubecfg_bin in kubecfg_binaries:
        resolve = _resolve_kubecfg(
            kubecfg_bin,
            cloud=cloud,
            kube_config_file=kube_config_file,
            kubecfg_extra_args=kubecfg_extra_args,
            namespace=namespace,
        )
        namespace2, deployment_names = _parse_k8s(resolve)
        assert namespace is None or namespace2 == namespace
        if namespace2 is None:
            raise RuntimeError("no namespace detected in k8s objects, not supported")
        result.append(
            Resolve(
                resolve=resolve, namespace=namespace2, deployment_names=deployment_names
            )
        )
    return result


def _get_pods(
    namespace: str, kube_config: Path | None, context: str | None
) -> typing.Iterable[str]:
    v1 = client.CoreV1Api(get_api_client(kube_config=kube_config, context=context))  # type: ignore
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        yield name


def dump_state(kubectl: Kubectl, deploy_info: DeployInfo | None = None):
    """Dump the state of the test namespace to artifact files."""
    output_dir = os.environ.get("TEST_UNDECLARED_OUTPUTS_DIR")
    if not output_dir:
        return
    output_path = Path(output_dir)
    pod_list = kubectl.run(["get", "pods"])
    output_path.joinpath("pod_list.txt").write_text(pod_list.stdout, encoding="utf-8")

    all_list = kubectl.run(["get", "all", "-o", "yaml"])
    output_path.joinpath("all.yaml").write_text(all_list.stdout, encoding="utf-8")

    events_list = kubectl.run(["get", "events"])
    output_path.joinpath("events_list.txt").write_text(
        events_list.stdout, encoding="utf-8"
    )
    for pod_name in _get_pods(
        kubectl.namespace, kubectl.kube_config_file, context=kubectl.context
    ):
        desc = kubectl.run(["describe", "pod", pod_name])
        output_path.joinpath(f"describe_pod_{pod_name}.txt").write_text(
            desc.stdout, encoding="utf-8"
        )
        logs = kubectl.run(["logs", "--all-containers", "--ignore-errors", pod_name])
        output_path.joinpath(f"logs_pod_{pod_name}.txt").write_text(
            logs.stdout, encoding="utf-8"
        )

    if deploy_info:
        object_dir = output_path.joinpath("objects")
        object_dir.mkdir(exist_ok=True)
        for resolve in deploy_info.resolves:
            data = list(yaml.load_all(io.StringIO(resolve.resolve), Loader=SafeLoader))
            for item in data:
                kind = item.get("kind").lower()
                name = item.get("metadata", {}).get("name")
                if kind == "secret":
                    # we do not log secrets
                    continue
                if kind and name:
                    state = kubectl.run(["get", kind, name, "-o", "yaml"])
                    object_dir.joinpath(f"{kind}_{name}.yaml").write_text(
                        state.stdout, encoding="utf-8"
                    )


def get_kubectl(
    cloud: str,
    namespace: str,
) -> Kubectl:
    """Return a kubectl object."""
    kube_config_file = _get_kube_config_file()
    if config and not kube_config_file:
        # context is not used in in-cluster mode
        context = None
    else:
        context = cloud_lib.get_context(cloud)
        assert context
    _setup_kubernetes(kube_config=kube_config_file)
    kubectl = Kubectl(kube_config_file, namespace=namespace, context=context)
    return kubectl


@contextmanager
def deploy(
    skip_deploy: bool,
    kubecfg_binaries: list[Path],
    cloud: str,
    skip_deploy_check: bool = False,
    skip_deploy_check_teardown: bool = False,
    kubecfg_extra_args: list[str] | None = None,
    namespace: str | None = None,
    settle_time: float = 120.0,
    teardown_bigtable: bool = False,
) -> typing.Generator[DeployInfo, None, None]:
    """Deploys the kubernetes objects created by the given k8s_resolve binary.

    It waits for all deployments to settle and will tear down the objects at the end
    of the context.
    """
    resolves = None
    kube_config_file = _get_kube_config_file()
    if config and not kube_config_file:
        # context is not used in in-cluster mode
        context = None
    else:
        context = cloud_lib.get_context(cloud)
        assert context
    _setup_kubernetes(kube_config=kube_config_file)
    deploy_info = None
    kubectl = None
    if kubecfg_extra_args is None and is_running_in_test_infra():
        kubecfg_extra_args = [
            "--namespace-config-path={}".format(
                os.path.join(
                    os.getcwd(),
                    "deploy/tenants/namespace_configs/test-defaults.jsonnet",
                )
            )
        ]
    try:
        resolves = _resolve(
            kubecfg_binaries,
            cloud=cloud,
            kube_config_file=kube_config_file,
            kubecfg_extra_args=kubecfg_extra_args,
            namespace=namespace,
        )
        assert len(resolves) >= 1
        assert (
            len(set(r.namespace for r in resolves)) == 1
        ), "All targets should be in the same namespace"
        if namespace is not None:
            assert resolves[0].namespace == namespace
        else:
            namespace = resolves[0].namespace

        kubectl = Kubectl(kube_config_file, namespace=namespace, context=context)

        # If we raise an exception below, having deploy_info set will give us a
        # more detailed dump
        deploy_info = DeployInfo(
            namespace=namespace,
            resolves=resolves,
            kubectl=kubectl,
        )

        if not skip_deploy:
            for resolve in resolves:
                resolve.apply(kubectl)

            # some time is needed for the basic changes to settle.
            time.sleep(settle_time)

        # wait for the deployments to become ready.
        for resolve in resolves:
            if not skip_deploy_check:
                resolve.wait(kube_config_file, context=context)

        yield deploy_info
    finally:
        if not skip_deploy and not skip_deploy_check_teardown:
            # if we didn't get the namespace, we cannot dump the state
            # if the test is run manually and we do not clean up the state, there
            # is no need to dump it into files
            if namespace:
                assert kubectl
                dump_state(
                    kubectl=kubectl,
                    deploy_info=deploy_info,
                )
            if resolves:
                assert kubectl
                for resolve in resolves:
                    resolve.remove(kubectl, teardown_bigtable=teardown_bigtable)
                if not teardown_bigtable:
                    # Reset contents of all Bigtable tables instead of deleting the tables
                    bt_client = get_bigtable_client()
                    for resolve in resolves:
                        data = list(
                            yaml.load_all(
                                io.StringIO(resolve.resolve), Loader=SafeLoader
                            )
                        )
                        for item in data:
                            if item.get("kind") == "BigtableTable":
                                table_name = item["metadata"]["name"]
                                logging.info(
                                    f"Resetting contents of BigtableTable {table_name}"
                                )
                                instance = bt_client.instance(
                                    item["spec"]["instanceRef"]["name"]
                                )
                                table = instance.table(table_name)
                                clear_bigtable_table(table)
                    logging.info("All Bigtable tables reset")


def clear_bigtable_table(table: bigtable_table.Table, batch_size=1000):
    """Clear a Bigtable table without using drop_row_range.

    This method reads rows in batches (without values) and deletes them using mutate_rows.
    """

    # Track the last seen row key to paginate through the table
    last_seen_key = None

    # Create a filter to only return row keys
    strip_filter = StripValueTransformerFilter(True)

    while True:
        # If we have a last seen key, start after it
        if last_seen_key:
            rows = list(
                table.read_rows(
                    start_key=last_seen_key,
                    end_key=None,
                    filter_=strip_filter,
                    limit=batch_size,
                )
            )
            # Remove first row if it was last seen. If only there were a start_inclusive parameter to set to False...
            if rows and rows[0].row_key == last_seen_key:
                rows = rows[1:]
        else:
            rows = list(table.read_rows(limit=batch_size, filter_=strip_filter))

        if not rows:
            # No more rows to process
            break

        # Create delete mutations for each row
        mutations = []
        for row in rows:
            # Create a delete row mutation
            direct_row = table.direct_row(row.row_key)
            direct_row.delete()
            mutations.append(direct_row)

        # Delete the batch of rows
        if mutations:
            table.mutate_rows(mutations)
            logging.info(f"Deleted {len(mutations)} rows from table {table.table_id}")

        # Update the last seen key
        last_seen_key = rows[-1].row_key
    logging.info(f"Finished resetting table {table.table_id}")


def get_pod_for_deployment(
    api_client: client.ApiClient | None, namespace: str, deployment_prefix: str
) -> typing.Optional[str]:
    """Find the first pod for the given deployment prefix."""
    v1 = client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_prefix):
            return name


def next_available_port() -> int:
    """Return the next available port."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(("", 0))
        return s.getsockname()[1]


def check_health(
    url: str,
    service_name: str = "",
    credentials: grpc.ChannelCredentials | None = None,
    endpoint: str | None = None,
) -> bool:
    """Returns whether the service at `url` if provided is healthy.

    If `service_name` is provided, we check that the gRPC service with that name is
    healthy; otherwise we just check that the node is healthy.

    Args:
        url: The URL to check.
        service_name: The name of the service to check. If empty, no specific service is checked
        credentials: The credentials to use when connecting. If None, no credentials (TLS) is used.
        endpoint: The endpoint to use when verifying a TLS certificate

    Returns:
        True if the service is healthy.
    """
    try:
        logging.info("Checking health of %s", url)
        if endpoint:
            options: list[tuple[str, str]] = [
                ("grpc.ssl_target_name_override", endpoint)
            ]
        else:
            options = []
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


@contextmanager
def get_tunnel_for_deployment(
    deploy_info: DeployInfo,
    deployment_prefix: str,
    remote_port: int,
    service_name: str = "",
    credentials: grpc.ChannelCredentials | None = None,
    endpoint: str | None = None,
) -> ContextManager[str]:
    """Find the first pod for the given deployment and return a localhost url for it.

    If `service_name` is provided, we check that the gRPC service with that name is
    healthy; otherwise we just check that the node is healthy.
    """
    pod_name = get_pod_for_deployment(
        deploy_info.kubectl.api_client, deploy_info.namespace, deployment_prefix
    )
    assert pod_name, f"Failed to find pod for {deployment_prefix=}"
    print("Using pod", pod_name, flush=True)

    for _ in range(30):
        with deploy_info.kubectl.port_forward(
            pod_name,
            local_port=next_available_port(),
            remote_port=50051,
        ) as local_port:
            url = f"localhost:{local_port}"
            if check_health(
                url, service_name, credentials=credentials, endpoint=endpoint
            ):
                yield url
                break
            time.sleep(10)
    else:
        raise ValueError(
            f"Timeout waiting for pod {pod_name}:{remote_port} to be ready"
        )


def print_link_to_logs(namespace: str | None = None):
    """Prints a link to the GCP logging with a filter that matches the current test run."""
    namespace = namespace or get_dev_namespace()
    start_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S")
    end_time = (
        datetime.datetime.now(datetime.UTC) + datetime.timedelta(hours=1)
    ).strftime("%Y-%m-%dT%H:%M:%S")
    link = f"https://console.cloud.google.com/logs/query;query=resource.labels.namespace_name%3D%22{namespace}%22;cursorTimestamp={start_time}Z;startTime={start_time}Z;endTime={end_time}Z?project=system-services-dev"
    print()
    print(f"LOGS: {link}", flush=True)
    print()


def is_running_in_test_infra() -> bool:
    """Returns whether we are running in the test infra"""
    env_check = "TEST_RUNNER_JOB_ID" in os.environ
    r = env_check
    return r


@contextmanager
def new_tenant(
    application_shard_deploy: DeployInfo, tenant_name: str, *, wait_time: float = 10.0
):
    # generate a 128-bit random number as hex
    tenant_id = hex(secrets.randbits(128))[2:]
    print("Creating tenant", tenant_name, "with id", tenant_id)
    data = {
        "apiVersion": "eng.augmentcode.com/v1",
        "kind": "Tenant",
        "metadata": {
            "name": tenant_name,
            "namespace": application_shard_deploy.namespace,
        },
        "spec": {
            "cloud": "GCP_US_CENTRAL1_DEV",
            "config": {"support_access_control": False},
            "shard_namespace": application_shard_deploy.namespace,
            "tenant_id": tenant_id,
        },
    }
    application_shard_deploy.kubectl.delete(data, ignore_not_found=True)
    application_shard_deploy.kubectl.apply(data)
    # give the some time to learn about the new tenant
    time.sleep(wait_time)
    yield tenant_id

    application_shard_deploy.kubectl.delete(data, ignore_not_found=True)


def get_bigtable_client():
    """Returns a Bigtable client configured for either emulator or production.

    Returns:
        google.cloud.bigtable.client.Client: A configured Bigtable client
    """
    emulator_host = os.getenv("BIGTABLE_EMULATOR_HOST")

    if emulator_host:
        # Connect to emulator without authentication
        client = bigtable.Client(
            admin=True,
            project="local-test-project",  # Dummy project ID for emulator
            credentials=None,
            client_options=client_options.ClientOptions(api_endpoint=emulator_host),
        )
    else:
        # Connect to production Bigtable using default credentials
        client = bigtable.Client(admin=True)

    return client

"""Test for the signal_handler module."""

import os
import signal
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Optional


def _get_pid(path: Path) -> Optional[int]:
    content = path.read_text(encoding="utf-8")
    if not content:
        return None
    return int(content)


def start_and_kill_with_signal(signum):
    with tempfile.NamedTemporaryFile() as named_tmpfile:
        with subprocess.Popen(
            [
                "base/python/signal_handler/signal_handler_test_binary",
                named_tmpfile.name,
            ],
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            encoding="utf-8",
        ) as p:
            # wait for the file getting creating.
            # that means that the handler was setup correctly.
            for _ in range(30):
                pid = _get_pid(Path(named_tmpfile.name))
                if pid:
                    break
                time.sleep(1)
            print("Found pid", pid)
            os.kill(pid, signum)
            out, err = p.communicate()
            return out, err


def test_sigint():
    """Tests the behavior and logging of sigint with the stanard signal handler."""
    _, err = start_and_kill_with_signal(signal.SIGINT)
    assert "captured signal SIGINT" in err
    assert "signal_handler_test_binary.py" in err


def test_sigterm():
    """Tests the behavior and logging of sigterm with the stanard signal handler."""
    out, err = start_and_kill_with_signal(signal.SIGTERM)
    assert "captured signal SIGTERM" in out
    assert "signal_handler_test_binary.py" not in err
    assert "signal_handler_test_binary.py" not in out

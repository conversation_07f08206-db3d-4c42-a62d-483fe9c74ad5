"""A simple cache backed by an in-memory dict (Mapping).

This is useful in cases where the entire blob dataset fits in memory,
e.g., unit tests or small datasets. No eviction is performed.
"""

from __future__ import annotations

import typing
from threading import Lock

from base.caching import cache

K = typing.TypeVar("K", contravariant=True)
V = typing.TypeVar("V", covariant=True)


class DictCache(cache.Cache[K, V, []]):
    """An in-memory dict cache that supports batch operations.

    Only the public methods of this class are thread-safe.
    """

    def __init__(
        self,
        mapping: typing.Mapping[K, V | None],
    ):
        """Create a new dict cache from a read-only mapping.

        Args:
            mapping: the mapping to use as the backing store.
        """
        self._mapping = mapping

        # Listeners for metrics.
        self._insert_listener: cache.InsertListener | None = None
        self._lookup_listener: cache.LookupListener | None = None

        self._lock = Lock()

    def set_insert_listener(self, listener: cache.InsertListener | None = None):
        """Set a listener for when new items are inserted into the cache.

        If `listener` is None, any existing listener will be removed.
        """
        with self._lock:
            self._insert_listener = listener

    def set_lookup_listener(self, listener: cache.LookupListener | None = None):
        """Set a listener for when items are queried from the cache.

        If `listener` is None, any existing listener will be removed.
        """
        with self._lock:
            self._lookup_listener = listener

    def get(self, keys: typing.Iterable[K]) -> typing.Iterable[V | None]:
        """Retrieve content for a list of keys.

        Args:
            keys: a list of keys.

        Returns:
            a list of content corresponding to `keys` or None if content for that key
            could not be retrieved. The order of the elements returned must match those
            in `keys`.
        """

        with self._lock:
            values = [self._mapping.get(key) for key in keys]
            lookup_listener = self._lookup_listener
            insert_listener = self._insert_listener

        if lookup_listener:
            lookup_listener(cache.LookupStats(hits_count=len(values), misses_count=0))

        if insert_listener:
            insert_listener(
                cache.InsertStats(
                    insertion_count=0,
                    skip_count=0,
                    eviction_count=0,
                    cache_size=len(self._mapping),
                    entries=len(self._mapping),
                )
            )

        return values

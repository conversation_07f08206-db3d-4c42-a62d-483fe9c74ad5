package spanner_emulator

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"time"

	"cloud.google.com/go/spanner"
	dbadmin "cloud.google.com/go/spanner/admin/database/apiv1"
	"cloud.google.com/go/spanner/admin/database/apiv1/databasepb"
	instanceadmin "cloud.google.com/go/spanner/admin/instance/apiv1"
	"cloud.google.com/go/spanner/admin/instance/apiv1/instancepb"
	"github.com/augmentcode/augment/base/test_utils/process"
	"github.com/bazelbuild/rules_go/go/runfiles"
	"github.com/google/uuid"
)

const (
	instanceID       = "test-instance"
	fullProjectName  = "projects/test-project"
	fullInstanceName = "projects/test-project/instances/test-instance"
)

// In-memory Spanner emulator for use in unit tests. The recommend use by the emulator's authors is
// to spin up a single emulator instance and have every test construct its own database within the
// instance. Example usage:
//
//	emulator, err := New(context.Background())
//	if err != nil {
//	    // handle error
//	}
//	defer emulator.Close()
//
//	client, cleanup, err := emulator.NewDatabaseFromJson(context.Background(), "ddl.json")
//	if err != nil {
//	    // handle error
//	}
//	defer cleanup()
//
// client.Apply(...)  // Use client as you would any other spanner client.
type SpannerEmulator struct {
	// Admin client for creating databases.
	dbAdminClient *dbadmin.DatabaseAdminClient

	// Command running the emulator in the background.
	serverManager *process.ServerManager
}

// Construct a new emulator. This will start the emulator process and create a test instance.
func New(
	ctx context.Context,
) (*SpannerEmulator, error) {
	emulatorPath, err := runfiles.Rlocation("spanner_emulator_amd64/emulator_main")
	if err != nil {
		return nil, err
	}

	serverManager, err := process.NewServerManager(context.Background(), []string{emulatorPath, "--host_port", "127.0.0.1:0"}, true, nil)
	if err != nil {
		return nil, err
	}

	// Wait for the emulator to start.
	portRegex := regexp.MustCompile(`Server address: 127.0.0.1:(\d+)`)
	match, err := serverManager.WaitForLine(portRegex, os.Stdout, 30*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to wait for emulator to start: %w", err)
	}

	serverManager.DetachStdout(os.Stdout)

	hostPort := fmt.Sprintf("127.0.0.1:%s", match[1])

	err = os.Setenv("SPANNER_EMULATOR_HOST", hostPort)
	if err != nil {
		return nil, fmt.Errorf("failed to set SPANNER_EMULATOR_HOST: %w", err)
	}

	// Create an instance.
	instanceAdminClient, err := instanceadmin.NewInstanceAdminClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create instance admin client: %w", err)
	}
	defer instanceAdminClient.Close()
	op, err := instanceAdminClient.CreateInstance(ctx, &instancepb.CreateInstanceRequest{
		Parent:     fullProjectName,
		InstanceId: instanceID,
		Instance: &instancepb.Instance{
			Config:      "regional-us-central1",
			DisplayName: instanceID,
			NodeCount:   1,
			Edition:     instancepb.Instance_STANDARD,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create instance: %w", err)
	}
	_, err = op.Wait(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to wait for instance creation: %w", err)
	}

	// Set up the database admin client, which we will use to create databases.
	dbAdminClient, err := dbadmin.NewDatabaseAdminClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create admin client: %w", err)
	}

	return &SpannerEmulator{
		dbAdminClient: dbAdminClient,
		serverManager: serverManager,
	}, nil
}

func (s *SpannerEmulator) Close() {
	s.dbAdminClient.Close()
	s.serverManager.Stop()
}

// Get a Spanner client for a new database constructed with the provided DDL statements. Also
// provides a cleanup function that handles closing the client and dropping the database, which
// callers should use when they're done with the database.
func (s *SpannerEmulator) NewDatabase(
	ctx context.Context, ddlStatements []string,
) (client *spanner.Client, cleanup func(), err error) {
	// Create a new database with a unique name.
	dbName := "test_database_" + uuid.New().String()[:8]
	fullDbName := fmt.Sprintf("%s/databases/%s", fullInstanceName, dbName)
	op, err := s.dbAdminClient.CreateDatabase(ctx, &databasepb.CreateDatabaseRequest{
		Parent:          fullInstanceName,
		CreateStatement: "CREATE DATABASE `" + dbName + "`",
		ExtraStatements: ddlStatements,
	})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create database: %w", err)
	}
	if _, err := op.Wait(ctx); err != nil {
		return nil, nil, fmt.Errorf("failed to wait for database creation: %w", err)
	}

	// Create a client for the database.
	client, err = spanner.NewClient(ctx, fullDbName)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create client: %w", err)
	}

	cleanup = func() {
		client.Close()
		s.dbAdminClient.DropDatabase(ctx, &databasepb.DropDatabaseRequest{
			Database: fullDbName,
		})
	}

	return client, cleanup, nil
}

// Get a Spanner client for a new database constructed with the DDL statements in the provided JSON
// file. See `NewDatabase`.
func (s *SpannerEmulator) NewDatabaseFromJson(
	ctx context.Context, jsonPath string,
) (*spanner.Client, func(), error) {
	jsonBytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return nil, nil, err
	}
	var ddlStatements []string
	if err := json.Unmarshal(jsonBytes, &ddlStatements); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal DDL JSON: %w", err)
	}

	return s.NewDatabase(ctx, ddlStatements)
}

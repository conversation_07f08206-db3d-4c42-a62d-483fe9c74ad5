"""Utils to format chat history."""

import copy
import dataclasses
import structlog

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNodeType,
    Exchange,
    RequestMessage,
    ToolDefinition,
    try_get_request_message_as_text,
    is_request_message_tool_result,
    get_request_message_as_text_always,
    inject_as_first_text_node_in_request_message,
)
from base.prompt_format_chat.lib.ide_state_utils import (
    get_empty_ide_state,
    update_ide_state_info,
)
from base.prompt_format_chat.lib.system_prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IdeStateInfo
from base.prompt_format_chat.lib.token_counter import TokenCounter

logger = structlog.get_logger(__name__)


def format_chat_history(
    chat_history: list[Exchange],
    token_counter: TokenCounter,
    token_budget: int,
) -> tuple[list[Exchange], int]:
    """Formats and tokenizes the chat history.

    Args:
        chat_history: List of chat exchanges to process
        token_counter: Counter for tracking token usage
        token_budget: Maximum allowed tokens for history

    Returns:
        - chat_history: the formatted chat history
        - token_ct: the total tokens consumed
    """

    formatted_chat_history = []
    token_ct = 0

    for exchange in reversed(list(chat_history)):
        # Count tokens for this exchange
        request_tok_ct = token_counter.count_tokens_in_request(exchange.request_message)

        # We'll exclude the tool result token counts
        # Tool results will be pruned later separately in _apply_budget_to_tool_results
        request_tool_result_tok_ct = token_counter.count_tokens_in_request(
            exchange.request_message, node_type=ChatRequestNodeType.TOOL_RESULT
        )
        assert request_tok_ct >= request_tool_result_tok_ct

        # Add up the totals for this exchange
        exchange_tok_ct = (
            request_tok_ct - request_tool_result_tok_ct
        ) + token_counter.count_tokens_in_response(exchange.response_text)

        if token_ct + exchange_tok_ct > token_budget:
            break
        token_ct += exchange_tok_ct
        formatted_chat_history.append(exchange)

    formatted_chat_history.reverse()

    return formatted_chat_history, token_ct


def _replace_tool_result_content(
    exchange: Exchange, token_counter: TokenCounter, request_token_ct: int
) -> tuple[Exchange, int]:
    if not isinstance(exchange.request_message, list):
        return exchange, 0

    new_nodes = []
    for node in exchange.request_message:
        if (
            node.type == ChatRequestNodeType.TOOL_RESULT
            and node.tool_result_node is not None
        ):
            # Replace content with placeholder
            new_node = dataclasses.replace(
                node,
                tool_result_node=dataclasses.replace(
                    node.tool_result_node,
                    content="[Truncated...re-run tool if you need to see output again.]",
                    content_nodes=None,
                ),
            )
            new_nodes.append(new_node)
        else:
            new_nodes.append(node)

    new_exchange = dataclasses.replace(exchange, request_message=new_nodes)

    new_request_token_ct = token_counter.count_tokens_in_request(new_nodes)

    if new_request_token_ct > request_token_ct:
        return exchange, 0

    return new_exchange, request_token_ct - new_request_token_ct


def format_agent_history(
    chat_history: list[Exchange],
    token_counter: TokenCounter,
    token_budget: int,
    ide_state_prompt: IdeStateFormatter | None = None,
    tool_definitions: list[ToolDefinition] | None = None,
) -> tuple[list[Exchange], int]:
    """Formats and tokenizes the chat history for agent prompts.

    Uses special logic for truncating history.

    Args:
        chat_history: Input containing chat history
        token_counter: Counter for tracking token usage
        token_budget: Maximum allowed tokens for history
        ide_state_info: An initial ide state with tools.
        ide_state_prompt: Prompt template for IDE state

    Returns:
        - chat_history: the chat history
        - token_ct: the total tokens consumed
    """
    formatted_chat_history = chat_history.copy()
    token_ct = 0
    token_counts = []
    request_token_counts = []
    last_truncated_idx = 0

    # We track the token count for the IDE state message separately because we add the
    # message once per state change, even in cases where we truncate history.
    # The count here is intentionally an overestimate as we do not remove counts when
    # all turns with a particular state configuration are clipped from history. More
    # precise tracking is possible, but not worth the effort since the IDE state is
    # small anyway (~100-300 chars).
    estimated_ide_state_token_ct = 0

    ide_state_info = get_empty_ide_state(tool_definitions)

    for i, exchange in enumerate(formatted_chat_history):
        if ide_state_prompt is not None:
            ide_state_info = update_ide_state_info(exchange, ide_state_info)
            # Estimate the number of tokens for IDE state.
            # We do this once per time the state changes, even if one or more turns
            # are clipped from history.
            estimated_ide_state_token_ct += ide_state_prompt.format_and_count_tokens(
                ide_state_info
            )

        # Count tokens for this exchange
        request_token_ct = token_counter.count_tokens_in_request(
            exchange.request_message
        )
        request_token_counts.append(request_token_ct)

        response_token_ct = token_counter.count_tokens_in_response(
            exchange.response_text
        )
        exchange_tok_ct = request_token_ct + response_token_ct
        token_counts.append(exchange_tok_ct)

        token_ct += exchange_tok_ct

        # The if condition is not a bug
        if token_ct + estimated_ide_state_token_ct > token_budget:
            # start truncating up to AND INCLUDING i - 3, and add those savings to the budget
            while last_truncated_idx <= i - 3:
                truncated_exchange, savings = _replace_tool_result_content(
                    formatted_chat_history[last_truncated_idx],
                    token_counter,
                    request_token_counts[last_truncated_idx],
                )
                formatted_chat_history[last_truncated_idx] = truncated_exchange
                token_ct -= savings
                token_counts[last_truncated_idx] -= savings
                request_token_counts[last_truncated_idx] -= savings
                last_truncated_idx += 1
            # NOTE(arun): We don't subtract the savings from removing the IDE state
            # because it's a bit of a pain to do so and each IDE state is small anyway
            # (~100-300 chars).

    last_preserved_idx = 0
    while token_ct + estimated_ide_state_token_ct >= token_budget:
        token_ct -= token_counts[last_preserved_idx]
        last_preserved_idx += 1
    # del deletes up to but not including the idx
    del formatted_chat_history[:last_preserved_idx]

    return formatted_chat_history, token_ct


def inject_selected_code_into_chat_history(
    cur_message: RequestMessage,
    chat_history: list[Exchange],
    selected_code: str,
    context_code_exchange_request_id: str | None,
    selected_code_response_message: str,
) -> tuple[RequestMessage, list[Exchange]]:
    """Injects the selected code into the chat history.

    Returns
        - cur_message: the updated cur_message
        - chat_history: the updated chat history
    """
    if selected_code == "":
        # Nothing to inject!
        return cur_message, chat_history

    if context_code_exchange_request_id == "new":
        # `context_code_exchange_request_id` == "new" means that context code is brand new
        # (i.e. user selected it in the current conversation turn). So we include it in the message.

        # It is a tool result, we'll drop the selected code
        if is_request_message_tool_result(cur_message):
            return cur_message, chat_history
        else:
            # Inject selected code as the first text node in the request message
            updated_message = inject_as_first_text_node_in_request_message(
                cur_message, selected_code
            )
            return updated_message, chat_history

    # Context code is from previous exchange, so we try putting it in the history.
    chat_history = copy.copy(chat_history)
    if context_code_exchange_request_id is not None:
        # If context_code_exchange_request_id is None, then there is no message to
        # attach to. This could happen with legacy code or possibly other edge cases.
        for i in range(len(chat_history)):
            exchange = chat_history[i]
            if exchange.request_id == context_code_exchange_request_id:
                # It is a tool result, we'll drop the selected code
                if is_request_message_tool_result(exchange.request_message):
                    return cur_message, chat_history

                # Inject selected code as the first text node in the exchange's request message
                updated_request_message = inject_as_first_text_node_in_request_message(
                    exchange.request_message, selected_code
                )

                updated_exchange = dataclasses.replace(
                    exchange,
                    request_message=updated_request_message,
                )
                chat_history[i] = updated_exchange
                return cur_message, chat_history

    if is_request_message_tool_result(cur_message):
        return cur_message, chat_history
    else:
        # Inject selected code as the first text node in the request message
        updated_message = inject_as_first_text_node_in_request_message(
            cur_message, selected_code
        )
        return updated_message, chat_history


def sanitize_known_client_faults(
    chat_history: list[Exchange], cur_message: RequestMessage
) -> list[Exchange]:
    """Sanitizes known client faults in the chat history.

    Currently this includes:
        - Deduping identical RAW_OUTPUT and TOOL_USE nodes in response side of exchange (AU-9326 was insufficient fix)
        - If EditEvents node was inserted between a TOOL_USE and TOOL_RESULT, simply drop it (we don't currently
          render it, and splitting a TOOL_USE and TOOL_RESULT guarantees BadRequestError/400 response)

    Returns: updated chat history (cur_message is never modified for these issues)
    """
    tool_result_ids = set()
    for exchange in chat_history:
        tool_result_ids.update(exchange.tool_result_ids())
    tool_result_ids.update(
        Exchange(request_message=cur_message, response_text="").tool_result_ids()
    )

    nodes_to_keep = [
        ChatRequestNodeType.TEXT,
        ChatRequestNodeType.TOOL_RESULT,
        ChatRequestNodeType.IMAGE,
    ]

    def can_drop(exchange: Exchange):
        if not isinstance(exchange.request_message, list):
            return False
        if exchange.response_text:
            return False
        return not any(node.type in nodes_to_keep for node in exchange.request_message)

    duplicate_node_exchanges = set()
    tool_splitting_exchanges = set()
    new_chat_history = []
    for i, exchange in enumerate(chat_history):
        pending_exchange: Exchange | None = exchange
        if isinstance(pending_exchange.response_text, list):
            new_nodes = []
            for node in pending_exchange.response_text:
                if node in new_nodes:
                    duplicate_node_exchanges.add(pending_exchange.request_id)
                    continue
                new_nodes.append(node)
            if len(new_nodes) < len(pending_exchange.response_text):
                pending_exchange = dataclasses.replace(
                    pending_exchange, response_text=new_nodes
                )

        if new_chat_history:
            prev_tool_uses = new_chat_history[-1].tool_use_ids()
            curr_tool_results = pending_exchange.tool_result_ids()
            if any(
                tool_use_id in tool_result_ids and tool_use_id not in curr_tool_results
                for tool_use_id in prev_tool_uses
            ):
                # This conversation would result in BadRequestError if sent to some of our model providers' APIs
                # Try to save it if the offending intervening exchanges are OK to drop
                if can_drop(pending_exchange):
                    # We actually expect this request_id to be 'None' most of the time...
                    tool_splitting_exchanges.add(pending_exchange.request_id)
                    pending_exchange = None
        if pending_exchange is not None:
            new_chat_history.append(pending_exchange)

    if duplicate_node_exchanges:
        logger.warn(
            "Dropped duplicate nodes from %d exchanges",
            len(duplicate_node_exchanges),
            exchanges=list(duplicate_node_exchanges)[:5],
        )
    if tool_splitting_exchanges:
        logger.warn(
            "Dropped %d exchanges to avoid splitting tool use and result",
            len(tool_splitting_exchanges),
            exchanges=list(tool_splitting_exchanges)[:5],
        )
    return new_chat_history


def postprocess_chat_history_tool_use(
    chat_history: list[Exchange],
    cur_message: RequestMessage,
    token_counter: TokenCounter,
    tool_result_budget: int,
) -> list[Exchange]:
    """Postprocesses the chat history to ensure consistency between tool uses and tool results.

    If there are mismatches, it reconstructs the chat history to maintain consistency:
       - Removes tool results without corresponding tool uses.
       - Adds placeholder "Cancelled by user" results for tool uses without results.

    Returns:
        list[Exchange]: The postprocessed chat history with consistent tool use and result pairs.
    """
    tool_uses = set()
    tool_results = set()

    # Collect all tool use IDs and tool result IDs from the chat history and current message
    for exchange in chat_history:
        for node in exchange.request_nodes:
            if node.tool_result_node is not None:
                tool_results.add(node.tool_result_node.tool_use_id)

        for response_node in exchange.response_nodes:
            if (
                response_node.type == ChatResultNodeType.TOOL_USE
                and response_node.tool_use is not None
            ):
                tool_uses.add(response_node.tool_use.tool_use_id)

    if isinstance(cur_message, list):
        for node in cur_message:
            if node.tool_result_node is not None:
                tool_results.add(node.tool_result_node.tool_use_id)

    if tool_uses != tool_results:
        new_chat_history = _fix_tool_use_result_mismatches(
            chat_history,
            tool_uses,
            tool_results,
        )
    else:
        new_chat_history = chat_history

    return _apply_budget_to_tool_results(
        new_chat_history, token_counter, tool_result_budget
    )


def _fix_tool_use_result_mismatches(
    chat_history: list[Exchange],
    tool_uses: set[str],
    tool_results: set[str],
) -> list[Exchange]:
    """Fixes mismatches between tool uses and tool results in the chat history.

    Args:
        chat_history: List of chat exchanges to process
        tool_uses: Set of tool use IDs that have been seen
        tool_results: Set of tool result IDs that have been seen

    Returns:
        Updated chat history with fixed tool use/result pairs
    """
    new_chat_history = []

    for exchange in chat_history:
        if isinstance(exchange.request_message, list):
            for node in exchange.request_message:
                if (
                    node.tool_result_node is not None
                    and node.tool_result_node.tool_use_id not in tool_uses
                ):
                    # This is a tool result, but we haven't seen the tool use
                    # Clip the history to exclude the result
                    new_chat_history = []
                    exchange = dataclasses.replace(
                        exchange,
                        request_message=[],
                    )
                    break

        # handle the response -- i.e., the tool use
        tool_use_ids = []
        for response_node in exchange.response_nodes:
            if (
                response_node.type == ChatResultNodeType.TOOL_USE
                and response_node.tool_use is not None
            ):
                tool_use_ids.append(response_node.tool_use.tool_use_id)

        extra_nodes = []
        for tool_use_id in tool_use_ids:
            if tool_use_id not in tool_results:
                # This is a tool use, but we have no result for it
                extra_nodes.append(
                    ChatRequestNode(
                        id=1,
                        type=ChatRequestNodeType.TOOL_RESULT,
                        tool_result_node=ChatRequestToolResult(
                            tool_use_id=tool_use_id,
                            content="Cancelled by user.",
                            is_error=True,
                            request_id=None,
                            content_nodes=None,
                        ),
                        text_node=None,
                    )
                )

        new_chat_history.append(exchange)
        if extra_nodes:
            new_chat_history.append(Exchange(extra_nodes, "OK.", None))

    return new_chat_history


def _apply_budget_to_tool_results(
    chat_history: list[Exchange],
    token_counter: TokenCounter,
    tool_result_budget: int,
) -> list[Exchange]:
    """Process tool results in reverse order and apply token budget constraints.

    Args:
        chat_history: List of chat exchanges to process
        token_counter: Counter for tracking token usage
        tool_result_budget: Maximum allowed tokens for tool results

    Returns:
        Updated chat history with tool results processed according to budget
    """
    accumulated_tokens = 0
    for exchange in reversed(chat_history):
        if not isinstance(exchange.request_message, list):
            continue

        for node in exchange.request_message:
            if node.tool_result_node is None:
                continue

            # Count tokens for this tool result
            result_tokens = token_counter.count_tokens_in_request(
                [node], node_type=ChatRequestNodeType.TOOL_RESULT
            )

            if accumulated_tokens + result_tokens > tool_result_budget:
                # Modify the node in place if over budget
                node.tool_result_node.content = (
                    "Tool result omitted due to token budget constraints."
                )
                node.tool_result_node.is_error = True
                node.tool_result_node.content_nodes = None
            else:
                accumulated_tokens += result_tokens

    return chat_history

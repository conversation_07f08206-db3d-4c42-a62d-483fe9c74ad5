"""Tests for ide_state_utils module."""

import pytest
from anthropic.types import RawMessageStreamEvent

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ToolDefinition,
)
from base.prompt_format_chat.lib.ide_state_utils import (
    inject_as_first_text_node_in_request_message,
)
from base.third_party_clients.mock_anthropic_client import MockAnthropicClient


def _text(id: int, content: str) -> ChatRequestNode:
    """Helper to create a text ChatRequestNode."""
    return ChatRequestNode(
        id=id,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content=content),
        tool_result_node=None,
    )


def _tool_result(
    id: int, tool_use_id: str = "test-id", content: str = "test-content"
) -> ChatRequestNode:
    """Helper to create a tool result ChatRequestNode."""
    return ChatRequestNode(
        id=id,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id=tool_use_id,
            content=content,
            is_error=False,
        ),
    )


@pytest.mark.parametrize(
    "request_message,text_content,expected_result",
    [
        pytest.param("foo", "bar", "bar\nfoo", id="inject_string"),
        pytest.param("foo", None, "foo", id="inject_string_none"),
        pytest.param("foo", "", "foo", id="inject_string_empty"),
        pytest.param([], "bar", [_text(2, "bar")], id="inject_empty_nodes"),
        pytest.param([], None, [], id="inject_empty_nodes_none"),
        pytest.param(
            [_text(1, "foo")],
            "bar",
            [_text(2, "bar\n"), _text(1, "foo")],
            id="inject_nodes",
        ),
        pytest.param(
            [_tool_result(1)],
            "bar",
            [_tool_result(1), _text(2, "bar")],
            id="inject_nodes_with_tools",
        ),
        pytest.param(
            [_tool_result(1), _text(2, "bar"), _tool_result(3)],
            "qux",
            [_tool_result(1), _text(4, "qux\n"), _text(2, "bar"), _tool_result(3)],
            id="inject_nodes_middle",
        ),
    ],
)
def test_inject_as_first_text_node_in_request_message(
    request_message, text_content, expected_result
):
    """Test inject_as_first_text_node_in_request_message with various inputs."""
    result = inject_as_first_text_node_in_request_message(request_message, text_content)
    assert result == expected_result


@pytest.mark.parametrize(
    "request_message,text_content,expected_messages",
    [
        pytest.param("foo", "bar", "bar\nfoo", id="inject_string"),
        pytest.param([_text(1, "foo")], "bar", "bar\nfoo", id="inject_nodes"),
    ],
)
def test_inject_as_first_text_node_with_mock_anthropic_client(
    request_message, text_content, expected_messages
):
    """Test that inject_as_first_text_node_in_request_message works
    correctly with MockAnthropicClient final parameters.
    """
    mock_stream: list[RawMessageStreamEvent] = []
    mock_client = MockAnthropicClient(
        stream=mock_stream,
        model_name="fake-model-name",
        temperature=0.7,
        max_output_tokens=1024,
    )

    request_message = inject_as_first_text_node_in_request_message(
        request_message, text_content
    )

    # Generate response stream with yield_final_parameters=True
    responses = list(
        mock_client.generate_response_stream(
            model_caller="test",
            cur_message=request_message,
            yield_final_parameters=True,
        )
    )

    assert len(responses) == 1
    response = responses[0]

    assert response.final_parameters is not None
    final_params = response.final_parameters

    messages = final_params["messages"]
    assert len(messages) == 1
    assert messages[0]["role"] == "user"
    assert messages[0]["content"] == expected_messages

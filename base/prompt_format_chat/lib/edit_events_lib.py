"""Utils for IDE state."""

from collections.abc import Iterable
import dataclasses
from dataclasses import dataclass
import logging

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    Exchange,
    RequestMessage,
    EditEventSource,
)
from base.prompt_format_chat.lib.system_prompts import (
    EditEventInfo,
    EditEventsFormatter,
    EditEventsInfo,
)

from base.prompt_format_chat.lib.ide_state_utils import (
    inject_as_first_text_node_in_request_message,
)

from base.prompt_format_chat.lib.token_counter import TokenCounter

logger = logging.getLogger(__name__)


@dataclass
class EditEventsState:
    checkpoint_reverts: dict[str, EditEventInfo] = dataclasses.field(
        default_factory=dict
    )
    """List of checkpoint reverts."""

    num_additional_checkpoint_reverts: int = 0
    """Number of additional checkpoint reverts not shown."""

    user_edits: dict[str, EditEventInfo] = dataclasses.field(default_factory=dict)
    """List of user edits."""

    num_additional_user_edits: int = 0
    """Number of additional user edits not shown."""

    items_limit: int = 20
    """Maximum number of events to accumulate in checkpoint_reverts or user_edits."""

    per_event_char_limit: int = 200
    """Maximum number of characters in a single event to show."""

    def flush_edit_events_info(self) -> EditEventsInfo:
        """Flush the edit events info. This resets the state."""
        edit_events_info = EditEventsInfo(
            checkpoint_reverts=list(self.checkpoint_reverts.values()),
            num_additional_checkpoint_reverts=self.num_additional_checkpoint_reverts,
            user_edits=list(self.user_edits.values()),
            num_additional_user_edits=self.num_additional_user_edits,
        )
        self.checkpoint_reverts.clear()
        self.num_additional_checkpoint_reverts = 0
        self.user_edits.clear()
        self.num_additional_user_edits = 0
        return edit_events_info

    def extract_from_request_message(
        self, request_message: RequestMessage
    ) -> RequestMessage:
        """Updates the state and returns the message with edit events removed."""
        if isinstance(request_message, str):
            return request_message
        return self._extract_from(request_message)

    def extract_from_exchange(self, exchange: Exchange) -> Exchange | None:
        """Updates state and returns the exchange with edit events removed.

        Returns None if the exchange should be dropped.
        """
        if isinstance(exchange.request_message, str):
            return exchange
        nodes = self._extract_from(exchange.request_message)
        exchange = dataclasses.replace(exchange, request_message=nodes)
        if not exchange.request_message and not exchange.response_text:
            return None
        return exchange

    def _extract_from(self, nodes: Iterable[ChatRequestNode]) -> list[ChatRequestNode]:
        for node in nodes:
            if node.type != ChatRequestNodeType.EDIT_EVENTS:
                continue
            if node.edit_events_node is None:
                continue

            for file_edit in node.edit_events_node.edit_events:
                # TODO(jeff): Compute and add diff hunks
                if node.edit_events_node.source == EditEventSource.CHECKPOINT_REVERT:
                    if (
                        len(file_edit.path) > self.per_event_char_limit
                        or len(self.checkpoint_reverts) >= self.items_limit
                    ):
                        self.num_additional_checkpoint_reverts += 1
                        continue
                    self.checkpoint_reverts[file_edit.path] = EditEventInfo(
                        path=file_edit.path
                    )
                elif node.edit_events_node.source == EditEventSource.USER_EDIT:
                    if (
                        len(file_edit.path) > self.per_event_char_limit
                        or len(self.user_edits) >= self.items_limit
                    ):
                        self.num_additional_user_edits += 1
                        continue
                    self.user_edits[file_edit.path] = EditEventInfo(path=file_edit.path)
                # Skip other sources.

        return [node for node in nodes if node.type != ChatRequestNodeType.EDIT_EVENTS]


def is_user_message(exchange_or_message: Exchange | RequestMessage) -> bool:
    """Returns True if the given exchange or message is a user message.

    Specifically, there are no tool results, and at least one text or image node.
    """
    if isinstance(exchange_or_message, Exchange):
        return is_user_message(exchange_or_message.request_message)
    if isinstance(exchange_or_message, str):
        return len(exchange_or_message) > 0

    return all(
        node.type != ChatRequestNodeType.TOOL_RESULT for node in exchange_or_message
    ) and any(
        node.type == ChatRequestNodeType.TEXT or node.type == ChatRequestNodeType.IMAGE
        for node in exchange_or_message
    )


def inject_edit_events(
    chat_history: list[Exchange],
    request_message: RequestMessage,
    token_counter: TokenCounter,
    edit_events_prompt: EditEventsFormatter | None = None,
) -> tuple[list[Exchange], RequestMessage, int]:
    """Injects the edit events state into the chat history.

    Args:
        chat_history: List of chat exchanges to process
        edit_events_prompt: Prompt template for edit events. If None, just drop any edit
          events nodes.

    Returns:
        - Updated chat history with additional text nodes introduced whenever edit events
          are encountered. All other edit events nodes are removed.
        - Total tokens consumed by the edit events text nodes.
    """
    edit_events_state = EditEventsState()
    updated_history: list[Exchange] = []
    token_ct: int = 0
    for exchange in chat_history:
        exchange = edit_events_state.extract_from_exchange(exchange)
        if exchange is None:
            # The exchange is empty, drop the exchange.
            continue
        if not is_user_message(exchange) or not edit_events_prompt:
            updated_history.append(exchange)
            continue
        edit_events_info = edit_events_state.flush_edit_events_info()
        text_content = edit_events_prompt.format(edit_events_info)
        token_ct += token_counter.count_tokens(text_content)
        updated_history.append(
            dataclasses.replace(
                exchange,
                request_message=inject_as_first_text_node_in_request_message(
                    exchange.request_message, text_content
                ),
            )
        )

    # TODO(jeff): It's technically possible that request_message might be empty now,
    # but that would be invalid client behavior anyway.
    request_message = edit_events_state.extract_from_request_message(request_message)
    if is_user_message(request_message) and edit_events_prompt:
        edit_events_info = edit_events_state.flush_edit_events_info()
        text_content = edit_events_prompt.format(edit_events_info)
        token_ct += token_counter.count_tokens(text_content)
        request_message = inject_as_first_text_node_in_request_message(
            request_message, text_content
        )

    return updated_history, request_message, token_ct

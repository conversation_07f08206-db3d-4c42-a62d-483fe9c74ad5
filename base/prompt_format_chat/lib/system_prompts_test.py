import pytest

from base.prompt_format.common import TerminalInfo, WorkspaceFolderInfo
from base.prompt_format_chat.lib.system_prompts import (
    EditEventInfo,
    IdeStateInfo,
    EditEventsInfo,
    get_agent_ide_state_prompt_formatter_v1,
    get_edit_events_prompt_formatter_v1,
)
from base.third_party_clients.token_counter.token_counter import RoughTokenCounter


@pytest.mark.parametrize(
    "ide_state_info, expected_prompt",
    [
        # No workspace folders.
        (
            IdeStateInfo(
                first_message=True,
                workspace_folders=[],
                current_terminal=None,
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The user does not have any workspace directories open.\n"
            + "When the user mentions a path, it is probably relative to their home directory.\n"
            + "Their home directory will be the current working directory when launching processes using the `launch_process` tool with `wait=false`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # Workspace and terminal first message.
        (
            IdeStateInfo(
                first_message=True,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The user's workspace is opened at `/home/<USER>/augment/clients/vscode`.\n"
            + "When the user mentions a path, it is probably relative to the workspace directory.\n"
            + "The user's workspace is part of a repository that is currently rooted at `/home/<USER>/augment`.\n"
            + "Use the repository root directory to resolve relative paths supplied to the following tools: codebase_retrieval, str_replace_editor.\n"
            + "The repository root directory will be the current working directory when launching processes using the `launch_process` tool with `wait=false`.\n"
            + "The interactive terminal's current working directory is `/home/<USER>/augment/clients/vscode`.\n"
            + "This is the current working directory used when launching processes using the `launch_process` tool with `wait=true`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # Workspace and terminal first message -- no tool names.
        (
            IdeStateInfo(
                first_message=True,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="",
                repo_relative_tools=[],
            ),
            "<supervisor>\n"
            + "The user's workspace is opened at `/home/<USER>/augment/clients/vscode`.\n"
            + "When the user mentions a path, it is probably relative to the workspace directory.\n"
            + "The user's workspace is part of a repository that is currently rooted at `/home/<USER>/augment`.\n"
            + "The interactive terminal's current working directory is `/home/<USER>/augment/clients/vscode`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # No changes.
        (
            IdeStateInfo(
                first_message=False,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "",
        ),
        # Changed workspace.
        (
            IdeStateInfo(
                first_message=False,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=True,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The user changed workspace directories. It is now opened at `/home/<USER>/augment/clients/vscode`.\n"
            + "When the user mentions a path, it is probably relative to the workspace directory.\n"
            + "The user's workspace is part of a repository that is currently rooted at `/home/<USER>/augment`.\n"
            + "Use the repository root directory to resolve relative paths supplied to the following tools: codebase_retrieval, str_replace_editor.\n"
            + "The repository root directory will be the current working directory when launching processes using the `launch_process` tool with `wait=false`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # Changed terminal.
        (
            IdeStateInfo(
                first_message=False,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=True,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The interactive terminal's current working directory has changed and is now `/home/<USER>/augment/clients/vscode`.\n"
            + "This is the current working directory used when launching processes using the `launch_process` tool with `wait=true`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
    ],
)
def test_agent_ide_state_prompt_formatter_v1(
    ide_state_info: IdeStateInfo, expected_prompt: str
):
    formatter = get_agent_ide_state_prompt_formatter_v1(RoughTokenCounter())
    actual = formatter.format(ide_state_info)
    assert actual == expected_prompt


@pytest.mark.parametrize(
    "edit_events_info, expected_prompt",
    [
        pytest.param(
            EditEventsInfo(
                checkpoint_reverts=[],
                user_edits=[],
                num_additional_checkpoint_reverts=0,
                num_additional_user_edits=0,
            ),
            "",
            id="no_events",
        ),
        pytest.param(
            EditEventsInfo(
                checkpoint_reverts=[EditEventInfo(path="/home/<USER>/file1.py")],
                user_edits=[],
                num_additional_checkpoint_reverts=0,
                num_additional_user_edits=0,
            ),
            "<supervisor>\n"
            "The user reverted previously made changes to the following files:\n"
            "- /home/<USER>/file1.py\n"
            "Before editing these files again, make sure you read them to know what they look like now.\n"
            "\n"
            "This information may or may not be relevant to the user's current request.\n"
            "Don't repeat this information to the user.\n"
            "</supervisor>",
            id="only_checkpoint_revert",
        ),
        pytest.param(
            EditEventsInfo(
                checkpoint_reverts=[],
                user_edits=[EditEventInfo(path="/home/<USER>/file1.py")],
                num_additional_checkpoint_reverts=0,
                num_additional_user_edits=0,
            ),
            "<supervisor>\n"
            "The user manually made changes to the following files:\n"
            "- /home/<USER>/file1.py\n"
            "\n"
            "This information may or may not be relevant to the user's current request.\n"
            "Don't repeat this information to the user.\n"
            "</supervisor>",
            id="only_user_edit",
        ),
        pytest.param(
            EditEventsInfo(
                checkpoint_reverts=[EditEventInfo(path="/home/<USER>/file1.py")],
                user_edits=[EditEventInfo(path="/home/<USER>/file2.py")],
                num_additional_checkpoint_reverts=1,
                num_additional_user_edits=2,
            ),
            "<supervisor>\n"
            "The user reverted previously made changes to the following files:\n"
            "- /home/<USER>/file1.py\n"
            "... and 1 more files.\n"
            "Before editing these files again, make sure you read them to know what they look like now.\n"
            "The user manually made changes to the following files:\n"
            "- /home/<USER>/file2.py\n"
            "... and 2 more files.\n"
            "\n"
            "This information may or may not be relevant to the user's current request.\n"
            "Don't repeat this information to the user.\n"
            "</supervisor>",
            id="multiple_events",
        ),
    ],
)
def test_agent_edit_events_prompt_formatter_v1(
    edit_events_info: EditEventsInfo, expected_prompt: str
):
    formatter = get_edit_events_prompt_formatter_v1(RoughTokenCounter())
    actual = formatter.format(edit_events_info)
    assert actual == expected_prompt

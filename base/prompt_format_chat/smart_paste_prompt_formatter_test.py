"""
Sanity test for smart paste prompt formatter.
"""

import pytest
from base.prompt_format.common import Exchange
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON>ounter
from base.prompt_format_chat.prompt_formatter import ExceedContextLength
from base.prompt_format_chat.smart_paste_prompt_formatter import (
    SmartPastePromptFormatter,
    SmartPastePromptInput,
    SmartPasteTokenApportionment,
)

TEN_TOKEN_LINE = "This line should be just about 10 tokens!"
TEN_TOKEN_EXCHANGE = [
    Exchange(
        request_message="Exchange of about",
        response_text="10 tokens",
    )
]


def test_smart_paste_prompt_formatter_basic():
    """This is a simple sanity check to catch obvious bugs in smart paste prompt formatting."""
    token_counter = ClaudeTokenCounter()
    smart_paste_token_apportionment = SmartPasteTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=1024 * 4,
        suffix_len=1024,
        target_file_path_len=256,
        target_file_content_len=1024 * 4,
        max_prompt_len=1024 * 16,
    )
    prompt_formatter = SmartPastePromptFormatter(
        token_counter, smart_paste_token_apportionment
    )

    selected_code_input = "def quicksort(arr: List[int]) -> List[int]:\n    pass\n"

    prompt_input = SmartPastePromptInput(
        path="src/example_from.py",
        prefix="from typing import List\n\n",
        suffix="\ndef irrelevant():\n    pass\n",
        selected_code=selected_code_input,
        code_block="""\
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    else:
        pivot = arr[0]
        less = [x for x in arr[1:] if x <= pivot]
        greater = [x for x in arr[1:] if x > pivot]
        return quicksort(less) + [pivot] + quicksort(greater)
""",
        chat_history=[
            Exchange(
                request_message="Implement the selected function",
                response_text="""Certainly. I'll replace the selected code with an implemented quicksort function. Here's the modified code:


```
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    else:
        pivot = arr[0]
        less = [x for x in arr[1:] if x <= pivot]
        greater = [x for x in arr[1:] if x > pivot]
        return quicksort(less) + [pivot] + quicksort(greater)
```

This implementation uses the first element as the pivot and recursively sorts the subarrays of elements less than or equal to the pivot and greater than the pivot.""",
            )
        ],
        prefix_begin=0,
        suffix_end=999,
        retrieved_chunks=[],
        target_path="src/example_to.py",
        target_file_content="""\
from typing import List

def quicksort(arr: List[int]) -> List[int]:
    pass

def irrelevant():
    pass
""",
    )

    prompt_output = prompt_formatter.format_prompt(prompt_input, True)
    chat_history_output = list(prompt_output.chat_history)
    assert prompt_output.tools is not None
    assert list(prompt_output.tools) == []
    # With the new behavior, selected code is no longer injected into chat history
    # Instead, it's handled internally by the smart paste formatter
    assert len(chat_history_output) == 1
    # The chat history should contain the original exchange without selected code injection
    assert chat_history_output[0].request_message == "Implement the selected function"
    # Assert that chat history includes retrievals, selected code, prefix, suffix
    full_prompt = """Great! Now please, apply changes that you demonstrated in this codeblock:
<changes_to_apply>
```
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    else:
        pivot = arr[0]
        less = [x for x in arr[1:] if x <= pivot]
        greater = [x for x in arr[1:] if x > pivot]
        return quicksort(less) + [pivot] + quicksort(greater)

```
</changes_to_apply>

to this file :

<file path="src/example_to.py">
<line number=1>from typing import List</line number=1>
<line number=2></line number=2>
<line number=3>def quicksort(arr: List[int]) -> List[int]:</line number=3>
<line number=4>    pass</line number=4>
<line number=5></line number=5>
<line number=6>def irrelevant():</line number=6>
<line number=7>    pass</line number=7>
</file>

To modify the file, please use git conflict markers format. I.e. return single or multiple git conflict markers in the following format:
<<<<<<< original BRIEFLY
...
======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]
...
>>>>>>> updated FULL

When you need to insert new class(es) or function(s) after line X use:
<<<<<<< original BRIEFLY
...
======= [after line(s) <line number=X>...content of line X...</line number=X>, <line number=X+1>...content of line X+1...</line number=X+1>,...,<line number=X+k>...content of line X+k...</line number=X+k>]
...
>>>>>>> updated FULL

- `updated` section should contain modified `original` code.
- Return only git conflict markers and nothing else.
- Split large changes into multiple smaller ones, and merge changes if they are close.
- Make sure to always use disjoint line ranges for every conflict marker.
- Line numbers in markers should ALWAYS be as in file, WITHOUT accounting for markers happened above.
- For middle line use EXACTLY this format: `======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]`. Exactly 2 lines (X and Y) have to be specified here. Or `======= [after line(s) <line number=X>...content of line X...</line number=X>, <line number=X+1>...content of line X+1...</line number=X+1>,...,<line number=X+k>...content of line X+k...</line number=X+k>]`.
- Always preserve indentation style of the original file. If indentation style of file and codeblock differ, always prefer file indentation style.
- To save time, please write code in `original` schematically and VERY briefly, use inline comments to indicate skipped and omitted parts.
- But ALWAYS write `updated` as the FULL range X - Y from original file. Expand any shortenings that are in `original`, NEVER copy them.
- `updated` section should rewrite PRECISELY lines from X to Y.
"""
    assert prompt_output.message == full_prompt
    assert (
        prompt_output.prefill
        == "Here are the changes applied using git conflict markers:\n\n\n<<<<<<< original BRIEFLY"
    )


def test_smart_paste_prompt_formatter_basic_wo_pure_additions():
    """This is a simple sanity check to catch obvious bugs in smart paste prompt formatting."""
    token_counter = ClaudeTokenCounter()
    smart_paste_token_apportionment = SmartPasteTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=1024 * 4,
        suffix_len=1024,
        target_file_path_len=256,
        target_file_content_len=1024 * 4,
        max_prompt_len=1024 * 16,
    )
    prompt_formatter = SmartPastePromptFormatter(
        token_counter, smart_paste_token_apportionment
    )

    selected_code_input = "def quicksort(arr: List[int]) -> List[int]:\n    pass\n"

    prompt_input = SmartPastePromptInput(
        path="src/example_from.py",
        prefix="from typing import List\n\n",
        suffix="\ndef irrelevant():\n    pass\n",
        selected_code=selected_code_input,
        code_block="""\
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    else:
        pivot = arr[0]
        less = [x for x in arr[1:] if x <= pivot]
        greater = [x for x in arr[1:] if x > pivot]
        return quicksort(less) + [pivot] + quicksort(greater)
""",
        chat_history=[
            Exchange(
                request_message="Implement the selected function",
                response_text="""Certainly. I'll replace the selected code with an implemented quicksort function. Here's the modified code:


```
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    else:
        pivot = arr[0]
        less = [x for x in arr[1:] if x <= pivot]
        greater = [x for x in arr[1:] if x > pivot]
        return quicksort(less) + [pivot] + quicksort(greater)
```

This implementation uses the first element as the pivot and recursively sorts the subarrays of elements less than or equal to the pivot and greater than the pivot.""",
            )
        ],
        prefix_begin=0,
        suffix_end=999,
        retrieved_chunks=[],
        target_path="src/example_to.py",
        target_file_content="""\
from typing import List

def quicksort(arr: List[int]) -> List[int]:
    pass

def irrelevant():
    pass
""",
    )

    prompt_output = prompt_formatter.format_prompt(prompt_input, False)
    chat_history_output = list(prompt_output.chat_history)
    assert prompt_output.tools is not None
    assert list(prompt_output.tools) == []
    # With the new behavior, selected code is no longer injected into chat history
    # Instead, it's handled internally by the smart paste formatter
    assert len(chat_history_output) == 1
    # The chat history should contain the original exchange without selected code injection
    assert chat_history_output[0].request_message == "Implement the selected function"
    # Assert that chat history includes retrievals, selected code, prefix, suffix
    full_prompt = """Great! Now please, apply changes that you demonstrated in this codeblock:
<changes_to_apply>
```
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    else:
        pivot = arr[0]
        less = [x for x in arr[1:] if x <= pivot]
        greater = [x for x in arr[1:] if x > pivot]
        return quicksort(less) + [pivot] + quicksort(greater)

```
</changes_to_apply>

to this file :

<file path="src/example_to.py">
<line number=1>from typing import List</line number=1>
<line number=2></line number=2>
<line number=3>def quicksort(arr: List[int]) -> List[int]:</line number=3>
<line number=4>    pass</line number=4>
<line number=5></line number=5>
<line number=6>def irrelevant():</line number=6>
<line number=7>    pass</line number=7>
</file>

To modify the file, please use git conflict markers format. I.e. return single or multiple git conflict markers in the following format:
<<<<<<< original BRIEFLY
...
======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]
...
>>>>>>> updated FULL

- `updated` section should contain modified `original` code.
- Return only git conflict markers and nothing else.
- Split large changes into multiple smaller ones and write them in top-to-bottom order.
- Make sure to always use disjoint line ranges for every conflict marker.
- For middle line use EXACTLY this format: `======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]`. Exactly 2 lines (X and Y) have to be specified here.
- Always preserve indentation style of the original file. If indentation style of file and codeblock differ, always prefer file indentation style.
- To save time, please write code in `original` schematically and VERY briefly, use inline comments to indicate skipped and omitted parts.
- But ALWAYS write `updated` as the FULL range X - Y from original file. Expand any shortenings that are in `original`, NEVER copy them.
- `updated` section should rewrite PRECISELY lines from X to Y.
"""
    assert prompt_output.message == full_prompt
    assert (
        prompt_output.prefill
        == "Here are the changes applied using git conflict markers:\n\n\n<<<<<<< original BRIEFLY"
    )


def test_smart_paste_prompt_formatter_above_max_prompt_len():
    """Token budget is enforced"""
    token_counter = ClaudeTokenCounter()
    smart_paste_token_apportionment = SmartPasteTokenApportionment(
        path_len=256,
        prefix_len=256,
        chat_history_len=256,
        suffix_len=256,
        target_file_path_len=256,
        target_file_content_len=256,
        max_prompt_len=1024 * 2,
    )
    prompt_formatter = SmartPastePromptFormatter(
        token_counter, smart_paste_token_apportionment
    )
    prompt_input = SmartPastePromptInput(
        path=TEN_TOKEN_LINE * 20,
        prefix=TEN_TOKEN_LINE * 20,
        suffix=TEN_TOKEN_LINE * 20,
        selected_code=TEN_TOKEN_LINE * 30,
        code_block=TEN_TOKEN_LINE * 30,
        chat_history=TEN_TOKEN_EXCHANGE * 20,
        prefix_begin=0,
        suffix_end=999,
        retrieved_chunks=[],
        target_path=TEN_TOKEN_LINE * 20,
        target_file_content=TEN_TOKEN_LINE * 20,
    )
    with pytest.raises(ExceedContextLength, match="exceeds maximum"):
        prompt_formatter.format_prompt(prompt_input)


def test_smart_paste_prompt_formatter_file_content_budget():
    """Token budget is enforced"""
    token_counter = ClaudeTokenCounter()
    smart_paste_token_apportionment = SmartPasteTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=1024,
        suffix_len=1024,
        target_file_path_len=256,
        target_file_content_len=128,
        max_prompt_len=1024 * 16,
    )
    prompt_formatter = SmartPastePromptFormatter(
        token_counter, smart_paste_token_apportionment
    )
    prompt_input = SmartPastePromptInput(
        path="src/example_from.py",
        prefix="from typing import List\n\n",
        suffix="\ndef irrelevant():\n    pass\n",
        selected_code="def quicksort(arr: List[int]) -> List[int]:\n    pass\n",
        code_block="""\
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    else:
        pivot = arr[0]
        less = [x for x in arr[1:] if x <= pivot]
        greater = [x for x in arr[1:] if x > pivot]
        return quicksort(less) + [pivot] + quicksort(greater)
""",
        chat_history=[],
        prefix_begin=0,
        suffix_end=999,
        retrieved_chunks=[],
        target_path="src/example_to.py",
        target_file_content=TEN_TOKEN_LINE * 20,
    )
    with pytest.raises(ExceedContextLength, match="Target file content length"):
        prompt_formatter.format_prompt(prompt_input)

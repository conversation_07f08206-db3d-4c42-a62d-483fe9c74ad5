# chunking utilities

This directory contains shared infrastructure for chunking of enriched text info including

- html to markdown conversion
- notebook processing.
- html dom structure based analysis and chunking

These conversion and analysis have to happen during chunking because some metadata or
structural information necessary to create chunks are lost if we do a plain conversion
ahead of time before chunking, so chunking and conversion to model readable text
usually need to be done together.

Things that might go into this folder in the future:
- Handling PDF etc
- Third party docs (google docs, notion pages etc)
- Schema metadata, data tables, sheets, ...
- Other structured data like email, messages, tickets, ...

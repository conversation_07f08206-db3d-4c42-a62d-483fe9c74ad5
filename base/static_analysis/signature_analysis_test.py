"""Tests the signature interface implementation."""

import dataclasses
from pathlib import Path

import pytest

from base.languages import LanguageId
from base.static_analysis import signature_analysis
from base.static_analysis.common import assert_eq

_TEST_ROOT = Path(__file__).parent / "testdata"


def _load_file(path: Path) -> str:
    return (_TEST_ROOT / path).read_text()


@dataclasses.dataclass
class TestCase:
    """A test case for the signature analysis."""

    path: Path
    language: LanguageId
    definition_names: list[str]
    usage_names: list[str]


CASES = [
    TestCase(
        path=Path("people.py"),
        language="Python",
        definition_names=[
            "Person",
            ".name",
            ".age",
            ".profession",
        ],
        usage_names=[
            "@dataclasses",
            "@dataclasses",
            "dataclass",
            "str",
            "int",
            "str",
        ],
    ),
    TestCase(
        path=Path("sort_people.py"),
        language="Python",
        definition_names=["sort_by_age", "test_sort_by_age"],
        usage_names=[
            "@people",
            "@people/Person",
            "Person",
            ".sort",
            ".age",
            "@people/Person",
            "Person",
            "@people/Person",
            "Person",
            "@people/Person",
            "Person",
            "sort_by_age",
        ],
    ),
]


@pytest.mark.parametrize("test_case", CASES)
def test_get_summary(test_case: TestCase):
    summary = signature_analysis.SignatureAnalysis.create(
        _load_file(test_case.path), test_case.path, test_case.language
    ).get_summary()
    # Just get the names for us to test with.
    definition_names = [definition.name for definition in summary.definitions]
    usage_names = [
        u.name
        for u in sorted(
            summary.local_analysis.name_usages, key=lambda x: (x.use_site, x.name)
        )
    ]

    assert_eq(summary.path, test_case.path)
    assert_eq(definition_names, test_case.definition_names)
    assert_eq(usage_names, test_case.usage_names)


@pytest.mark.parametrize("test_case", CASES)
def test_get_summary_with_signatures(test_case: TestCase):
    summary_signatures = signature_analysis.SignatureAnalysis.create(
        _load_file(test_case.path), test_case.path, test_case.language
    ).get_summary_with_signatures()
    summary = summary_signatures.summary
    # Just get the names for us to test with.
    definition_names = [definition.name for definition in summary.definitions]
    usage_names = [
        u.name
        for u in sorted(
            summary.local_analysis.name_usages, key=lambda x: (x.use_site, x.name)
        )
    ]

    assert_eq(summary.path, test_case.path)
    assert_eq(definition_names, test_case.definition_names)
    assert_eq(usage_names, test_case.usage_names)

    # Test that all global definitions should appear in the module signature
    signatures = summary_signatures.signature_info
    global_names = [
        name for name in test_case.definition_names if not name.startswith(".")
    ]
    for i, definition_name in enumerate(global_names):
        assert definition_name in signatures.module_signature.text
        def_id = summary.definitions[i].id
        assert definition_name in signatures.symbol_signatures[def_id].text

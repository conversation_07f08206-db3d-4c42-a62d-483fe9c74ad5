"""Implements a Range type that represents the interval [start, stop)."""

# NOTE: from __future__ import annotations does not play nice with dataclasses_json.

from dataclasses import dataclass
from typing import Collection, Iterable, Optional

import dataclasses_json
from typing_extensions import Self


@dataclasses_json.dataclass_json
@dataclass(frozen=True, order=True)
class IntRange:
    """A continuous range of integers from [start, stop).

    For example [0, 2) would include the integers 0 and 1. This range could be
    used to represent the first two characters of a document.

    In addition, IntRange supports points. Going back to our use case of
    character positions in a document, these are places we could insert
    additional characters. For the range [0, 2), we can insert a character
    before 0, before 1, and before 2.

    So, we can think of IntRange as a set containing both character positions
    and insertion points. For example, [0, 2) = {character position 0, character
    position 1, insertion point 0, insertion point 1, insertion point 2}. The
    set [1, 1) is {insertion point 1}.

    Some notes on operations:
    - Contains:  both [0, 2) and [2, 4) contain [2, 2) because both sets contain
      {insertion point 2}
    - Intersection: behaves pretty much as you would expect. [0, 2) ∩ [2, 4) =
      [2, 2) because both sets include {insertion point 2}. [0, 2) ∩ [10, 40) =
      None because the insersection is the Empty set.
    - Length: We define the length to be the number of characters (and not
      insertion points) in the range. E.g., |[0, 5)| = 5, but |[5, 5)| = 0. It's
      important to keep length and __iter__ consistent and __iter__ iterates
      over character positions.
    - We say that two ranges overlap iff they share characters (e.g. [0, 2) and
      [1, 3) share character 1). If they only share insertion points (e.g. [0,
      2) and [2, 3)) then they do not overlap.
    - Finally, the distance between two non-overlapping ranges is the minimum
      distance between their endpoints; both overlapping and adjacent ranges
      have 0 distance. Distance is measured as the number of insertion points
      between the two ranges.

    "IntRange" can be converted to a slice (`.to_slice()`) or a tuple
    (`.to_tuple()`).
    """

    start: int
    """The start of the range."""
    stop: int
    """The exclusive end of the range."""

    def __post_init__(self):
        if self.stop < self.start:
            raise ValueError(f"A valid range must have {self.start=} <= {self.stop=}.")

    # Set operations and mathematical definition.
    @classmethod
    def point(cls, start: int) -> Self:
        """Creates a range for a given point."""
        return cls(start, start)

    def is_point(self) -> bool:
        """Check if this is a point range."""
        return self.start == self.stop

    def points(self) -> Iterable[int]:
        """Return the points of the range (includes the otherwise exclusive end).

        See description above for more details for why this is consistent."""
        return range(self.start, self.stop + 1)

    def contains(self, other: "IntRange") -> bool:
        """Check if this range fully contains another range.

        For point ranges, this operation returns True even if the point touches the
        exclusive end.
        """
        return self.start <= other.start and self.stop >= other.stop

    def __len__(self) -> int:
        """Return the length of the range; the length of a point range is always 0."""
        return self.stop - self.start

    def intersect(self, other: "IntRange") -> Optional["IntRange"]:
        """Find the largest range contained in both ranges.

        For example, the intersection of:
            - [1, 3) ∩ [2, 4) is [2, 3).

        Notes:
        * the intersection of two adjacent ranges will results in a point range:
            - [1, 2) ∩ [2, 3) is [2, 2).
        * the intersection of a point range and another range containing is a point
          range:
            - [1, 10) ∩ [3, 3) is [3, 3).
        * the intersection of two non-adjacent disjoint ranges will return None.
            - [1, 2) ∩ [3, 4) is None.
        """
        start = max(self.start, other.start)
        stop = min(self.stop, other.stop)
        if stop >= start:
            return IntRange(start, stop)
        else:
            return None

    def overlaps(self, other: "IntRange") -> bool:
        """Check if the two ranges have a non-zero intersection.

        Always returns False for point ranges.
        """
        return max(self.start, other.start) < min(self.stop, other.stop)

    def touches(self, other: "IntRange") -> bool:
        """Check if the two ranges share any character *or* insertion points.

        This is true iff a.overlaps(b), a.contains(b), b.contains(a), or a.adjoins(b).
        and includes such cases where a or b are point ranges.
        """
        return max(self.start, other.start) <= min(self.stop, other.stop)

    def adjoins(self, other: "IntRange") -> bool:
        """Check if the two ranges are adjacent but non-overlapping."""
        return self.start == other.stop or self.stop == other.start

    def difference(self, other: "IntRange") -> Iterable["IntRange"]:
        """Find the difference between two ranges.

        Note that this may be zero, one, or two ranges.

        For example, the difference of:
            - [1, 3) - [2, 4) is [[1, 2)].
            - [1, 3) - [1, 4) is [].
            - [1, 4) - [2, 3) is [[1, 2), [3, 4)].
        """
        if self.start < other.start:
            yield IntRange(self.start, min(self.stop, other.start))
        if self.stop > other.stop:
            yield IntRange(max(self.start, other.stop), self.stop)

    # Other operations.
    def merge(self, other: "IntRange") -> "IntRange":
        """Find the smallest range that contains both ranges.

        For example, the union of:
            - [1, 2) + [2, 3) is [1, 3).
            - [1, 2) + [3, 4) is [1, 4).

        Note that the union of two point ranges extends between the two points:
            - [1, 1) + [3, 3) is [1, 3).
        """
        return IntRange(min(self.start, other.start), max(self.stop, other.stop))

    def split_by_range(
        self, splitter: "IntRange"
    ) -> tuple[Optional["IntRange"], Optional["IntRange"]]:
        """Split this range into two parts, one before `splitter` and the other after.

        For example, if the range is [1, 9) and
            - the splitter is [5, 5), then return [1, 5), [5, 9).
            - the splitter is [1, 5), then return [1, 1), [5, 9).
            - the splitter is [0, 5), then return {} (empty), [5, 9).
            - the splitter is [5, 9), then return [1, 5), [9, 9).
            - the splitter is [5, 10), then return [1, 5), {} (empty).
            - the splitter is [0, 10), then return {} (empty), {} (empty).
            - the splitter is [0, 1), then return {} (empty), [1, 9).
            - the splitter is [9, 10), then return [1, 9), {} (empty).

        Returns:
            the left and right parts of the split.
            if the splitter extends past the left of the range, the left range will be
                None.
            if the splitter extends past the right of the range, the right range will be
                None.
        """
        left = (
            IntRange(self.start, min(splitter.start, self.stop))
            if splitter.start >= self.start
            else None
        )
        right = (
            IntRange(max(self.start, splitter.stop), self.stop)
            if splitter.stop <= self.stop
            else None
        )
        return left, right

    def shifted(self, offset: int) -> "IntRange":
        """Return the range shifted by the offset amount."""
        return IntRange(self.start + offset, self.stop + offset)

    def distance(self, other: "IntRange") -> int:
        """Return the distance between this and another range.

        Distance is defined as the min distance between their endpoints; both
        overlapping and adjacent ranges have 0 distance.

        For example, the distance between:
            - [1, 3) and [2, 3) is 0 (overlapping).
            - [1, 3) and [3, 4) or [3, 3) is 0 (adjacent).
            - [1, 3) and [4, 5) or [4, 4) is 1.
        """
        return -min(0, min(self.stop, other.stop) - max(self.start, other.start))

    @staticmethod
    def any_overlaps(ranges: Iterable["IntRange"]) -> bool:
        """Return whether any two ranges provided overlap."""
        if not ranges:
            return False
        ranges = sorted(ranges)
        last_stop = ranges[0].stop
        for r in ranges[1:]:
            if r.start < last_stop:
                return True
            last_stop = r.stop
        return False

    @staticmethod
    def merge_touching(ranges: Collection["IntRange"]) -> list["IntRange"]:
        """Merge collection of touching ranges into a list of non-touching ranges."""
        # nothing to do if we have one or fewer ranges
        if len(ranges) < 2:
            return list(ranges)

        # sort the blocked ranges by start position
        sorted_ranges = sorted(ranges)

        # merge starting from the first range
        merged_ranges = sorted_ranges[:1]

        for next_blocked_range in sorted_ranges[1:]:
            last_merged_range = merged_ranges[-1]

            # the >= comparison is consistent with the "touches" method
            if last_merged_range.stop >= next_blocked_range.start:
                merged_ranges[-1] = last_merged_range.merge(next_blocked_range)
            else:
                merged_ranges.append(next_blocked_range)

        return merged_ranges

    # Conversions.
    def to_slice(self) -> slice:
        """Convert this into a Python slice object."""
        return slice(self.start, self.stop)

    def to_tuple(self) -> tuple[int, int]:
        """Return (self.start, self.stop)."""
        return (self.start, self.stop)

    # Python dunders
    def __str__(self) -> str:
        return f"{self.start}:{self.stop}"

    def __repr__(self) -> str:
        return f"IntRange({self.start}, {self.stop})"

    def __bool__(self) -> bool:
        """Returns true if this is a non-zero range."""
        return self.stop > self.start

    def __iter__(self):
        """Iterate over the indices in this range."""
        return iter(range(self.start, self.stop))

    def __contains__(self, i: int) -> bool:
        """Return whether this range contains the index i."""
        return self.start <= i < self.stop


# Commonly used aliases for IntRange. These are defined as separate types to prevent
#   users from unintentionally swapping one for the other; unfortunately this will not
#   be enforced by the type checker.
ByteRange = IntRange
"""References a range of bytes in file."""

CharRange = IntRange
"""References a range of characters in file."""

LineRange = IntRange
"""References a range of lines in file."""

TokenRange = IntRange
"""References a range of tokens in file."""

from functools import lru_cache
from typing import Any, Callable, Generator, Iterator, Optional

import structlog
import vertexai
from typing_extensions import override
from vertexai.generative_models import (
    Content,
    GenerationConfig,
    GenerativeModel,
    HarmBlockThreshold,
    HarmCategory,
    Part,
)
from vertexai.preview.tokenization import get_tokenizer_for_model

from base.prompt_format.common import (
    Exchange,
    RequestMessage,
    StopReason,
    get_request_message_as_text,
    get_response_message_as_text,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ModelAPICall,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
)

SAFETY_SETTINGS = {
    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
    HarmCategory.HARM_CATEGORY_UNSPECIFIED: HarmBlockThreshold.BLOCK_NONE,
}

logger = structlog.get_logger("VertexAiClient")


class VertexAiClient(ThirdPartyModelClient):
    """
    A class to interact with the VertexAI API for generating responses.
    """

    def __init__(
        self,
        project_id,
        region,
        model_name,
        temperature,
        max_output_tokens,
    ):
        """
        Initialize the VertexAI API with project details and model parameters.

        Args:
            project_id (str): The ID of the Google Cloud project.
            region (str): The region of the Google Cloud project.
            model_name (str): The name of the model to use for generating responses.
            temperature (float): The temperature parameter for controlling the randomness of the responses.
            max_output_tokens (int): The maximum number of tokens to generate in the response.
        """
        vertexai.init(project=project_id, location=region)
        self.generation_config = GenerationConfig(
            candidate_count=1,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )
        self.model_name = model_name
        if model_name in [
            "gemini-pro-experimental",
            "gemini-flash-experimental",
            "gemini-2.0-flash-exp",
            "gemini-2.0-flash-001",
            "gemini-2.5-pro-exp-03-25",
            "gemini-2.5-pro-preview-03-25",
            "gemini-2.5-flash-preview-04-17",
        ]:
            # No tokenizer for experimental models
            self.tokenizer = get_tokenizer_for_model("gemini-1.5-flash-001")
        else:
            self.tokenizer = get_tokenizer_for_model(model_name)
        logger.info("VertexAiClient initialized for model %s", model_name)

    @lru_cache(maxsize=32)
    def _get_model(self, system_prompt: str) -> GenerativeModel:
        """
        Get the model for the client.

        Args:
            system_prompt (str): The system prompt to use for the model.

        Returns:
            GenerativeModel: The model for the client.
        """
        return GenerativeModel(
            model_name=self.model_name,
            generation_config=self.generation_config,
            safety_settings=SAFETY_SETTINGS,
            system_instruction=system_prompt,
        )

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        code_instructions_streaming_v2: bool = False,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """
        Generate a response based on the given message.

        Args:
            messages: The messages to generate a response for in the format (user_m, assistant_m)
                where the last tuple only has the user_message. Deprecated in favor of `chat_history`.
            system_prompt: The system prompt to use for the model.
            cur_message: The current message to generate a response for.
            chat_history: A structured format of `messages`.
            tools: List of available tools.
            tool_definitions: List of tool definitions.
            tool_choice: The selected tool choice.
            temperature: Temperature parameter for response generation.
            max_output_tokens: Maximum number of tokens in the response.
            prefill: Prefilled text to start the response with.
            code_instructions_streaming_v2: Whether to use code instructions streaming v2.
            use_caching: Whether to use response caching.
            request_context: Optional request context containing information about the request, including session ID.

        Returns:
            Generator for the generated response text.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        assert not tool_choice, "VertexAiClient does not support tool_choice"
        assert not prefill, "VertexAiClient does not support prefill"
        assert (
            not code_instructions_streaming_v2
        ), "VertexAiClient does not support code_instructions_streaming_v2"
        assert not use_caching, "VertexAiClient does not support use_caching"
        assert (
            not yield_final_parameters
        ), "VertexAiClient does not support yield_final_parameters"
        logger.info("VertexAiClient generating response for %s messages", len(messages))
        if len(tools) > 0 or len(tool_definitions) > 0:
            logger.warning(
                "VertexAiClient does not currently support tools, but got %s.",
                tools + [tool_def.name for tool_def in tool_definitions],
            )
            tools = []
            tool_definitions = []

        if chat_history is None:
            chat_history = [
                Exchange(request_message=message[0], response_text=message[1])
                for message in messages
            ]

        history: list[Content] = []
        for message in chat_history:
            history.append(
                Content(
                    role="user",
                    parts=[
                        Part.from_text(
                            get_request_message_as_text(message.request_message)
                        )
                    ],
                )
            )
            history.append(
                Content(
                    role="assistant",
                    parts=[
                        Part.from_text(
                            get_response_message_as_text(message.response_text)
                        )
                    ],
                )
            )
        model = self._get_model(system_prompt or "")

        # temperature and max_output_tokens can only be overridden per-chat for ChatModel, not GenerativeModel
        assert temperature is None and max_output_tokens is None
        chat = model.start_chat(
            history=history,
            response_validation=False,
        )
        response_iterator = chat.send_message(
            get_request_message_as_text(cur_message),
            stream=True,
            generation_config=self.generation_config,
        )
        assert isinstance(response_iterator, Iterator)
        for response in response_iterator:
            yield ThirdPartyModelResponse(response.text)
        yield ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(
                stop_reason=StopReason.END_TURN,
            ),
        )

    @override
    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: The number of tokens in the given message.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        count_token_result = self.tokenizer.count_tokens(message)
        return count_token_result.total_tokens

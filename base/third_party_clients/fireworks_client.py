"""Fireworks AI client."""

from typing import Any, Call<PERSON>, Generator, Iterator, Optional

import structlog
from fireworks.client import Fireworks
from typing_extensions import override

from base.prompt_format.common import (
    Exchange,
    RequestMessage,
    StopReason,
)
from base.prompt_format_chat import get_token_counter_by_prompt_formatter_name
from base.third_party_clients.openai_client import (
    format_request_message_as_nodes,
    format_response_message_as_node,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ModelAPICall,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolDefinition,
)
from base.third_party_clients.token_counter.token_counter import TokenCounter

logger = structlog.get_logger("FireworksClient")


class FireworksClient(ThirdPartyModelClient):
    """
    A class to interact with OpenAI for generating responses.
    """

    def __init__(
        self,
        api_key: str,
        model_name: str,
        temperature: float,
        max_output_tokens: int,
    ):
        """Initialize the Fireworks AI client.

        Args:
            api_key: The API key for accessing Fireworks AI services.
            model_name: The name of the model to use for generating responses.
            temperature: Controls randomness in the model's output (0.0 to 1.0).
            max_output_tokens: Maximum number of tokens to generate in the response.
        """
        self.api_key = api_key
        self.model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.client = Fireworks(api_key=self.api_key)
        self._token_counter = get_token_counter_by_prompt_formatter_name("binks-openai")

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: dict[str, str] | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """Generate a streaming response from the Fireworks AI model.

        Args:
            messages: Deprecated. List of (user, assistant) message tuples. Use chat_history instead.
            system_prompt: Optional system prompt to guide model behavior.
            cur_message: Current user message to generate a response for.
            chat_history: List of previous Exchange objects containing request/response pairs.
            tools: List of tool names (not supported, will raise NotImplementedError).
            tool_definitions: List of tool definitions (not supported, will raise NotImplementedError).
            tool_choice: Tool selection parameters (unused).
            prefill: Optional text to prefill response (unused).
            use_caching: Whether to use response caching (unused).
            request_context: Optional request context containing information about the request, including session ID.
            yield_final_parameters: Whether to yield a response with the final parameters used for the request.

        Returns:
            Generator of ThirdPartyModelResponse objects containing response text chunks.

        Raises:
            NotImplementedError: If tools or tool_definitions are provided.
            openai.BadRequestError: If the API request fails.
        """
        assert (
            tool_choice is None
        ), "FireworksClient does not currently support tool_choice."
        assert prefill is None, "FireworksClient does not currently support prefill."
        assert not use_caching, "FireworksClient does not currently support caching."

        if tools or tool_definitions:
            tools = []
            tool_definitions = []
            logger.warning("Disabling tools. FireworksClient does not support tools.")

        logger.info(
            "FireworksClient generating response for %s messages", len(messages)
        )

        formatted_messages = []
        if system_prompt is not None:
            formatted_messages.append(
                {
                    "role": "system",
                    "content": system_prompt,
                }
            )
        if chat_history is None:
            chat_history = [
                Exchange(request_message=message[0], response_text=message[1])
                for message in messages
            ]

        for message in chat_history:
            formatted_messages += format_request_message_as_nodes(
                message.request_message
            )
            formatted_messages.append(
                format_response_message_as_node(message.response_text)
            )

        # Add cur message
        formatted_messages += format_request_message_as_nodes(cur_message)

        if yield_final_parameters:
            yield ThirdPartyModelResponse(
                text="",
                final_parameters={
                    "model": self.model_name,
                    "messages": formatted_messages,
                    "temperature": self.temperature,
                    "max_completion_tokens": self.max_output_tokens,
                    "stream": True,
                },
            )

        response_iterator = self.client.chat.completions.create(  # type: ignore
            model=self.model_name,
            messages=formatted_messages,  # type: ignore
            temperature=self.temperature,
            max_completion_tokens=self.max_output_tokens,
            stream=True,
        )
        assert isinstance(response_iterator, Iterator)
        for chunk in response_iterator:
            if chunk.choices and chunk.choices[0].delta.content:
                yield ThirdPartyModelResponse(chunk.choices[0].delta.content)
        yield ThirdPartyModelResponse(
            text="",
            end_of_stream=EndOfStream(stop_reason=StopReason.REASON_UNSPECIFIED),
        )

    @override
    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: An estimate of the number of tokens in the given message.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        return self._token_counter.count_tokens(message)

    @override
    def token_counter(self) -> TokenCounter:
        return self._token_counter

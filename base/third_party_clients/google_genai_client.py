import base64
import hashlib
import json
import secrets
import time
import uuid
from typing import Any, Callable, Generator, Optional

import grpc
import prometheus_client
import pydantic
import structlog
from google.genai import Client as genai_Client
from google.genai.types import (
    Content,
    FunctionCall,
    FunctionDeclaration,
    FunctionResponse,
    GenerateContentConfig,
    GenerateContentResponseUsageMetadata,
    HarmBlockThreshold,
    HarmCategory,
    JSONSchema,
    Part,
    SafetySetting,
    Schema,
    Tool,
    Type,
)
from typing_extensions import override
from vertexai.preview.tokenization import get_tokenizer_for_model

from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNodeType,
    Exchange,
    ImageFormatType,
    RequestMessage,
    ResponseMessage,
    StopReason,
    try_get_request_message_as_text,
)
from base.third_party_clients.common import (
    InvalidArgumentRpcError,
    UnavailableRpcError,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ModelAPICall,
    PromptCacheUsage,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
    ToolUseResponse,
)
from base.third_party_clients.utils.google_genai_schema_adapter_utils import (
    json_schema_str_to_genai_schema,
)

SAFETY_SETTINGS = [
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_UNSPECIFIED,
        threshold=HarmBlockThreshold.BLOCK_NONE,
    ),
]

_logger = structlog.get_logger("GoogleGenaiClient")

# Note that this metric can be inaccurate, because it depends on the client
# closing the generator stream as soon as it is done consuming results.
GOOGLE_GENAI_RESPONSE_LATENCY = prometheus_client.Histogram(
    "au_google_genai_response_latency",
    "Latency of Google GenAI API responses in seconds",
    ["model"],
    buckets=[
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
        20.0,
        50.0,
        100.0,
        200.0,
        float("inf"),
    ],
)

GOOGLE_GENAI_FIRST_TOKEN_LATENCY = prometheus_client.Histogram(
    "au_google_genai_first_token_latency",
    "Latency of first token of Google GenAI API responses in seconds",
    ["model"],
    buckets=[
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
        20.0,
        50.0,
        100.0,
        200.0,
        float("inf"),
    ],
)

_token_count_calls = prometheus_client.Counter(
    "au_google_genai_token_count_calls",
    "Number of token counting calls made to Google GenAI client",
)

_output_text_chars_counter = prometheus_client.Counter(
    "au_google_genai_output_text_size",
    "Size of output text from Google GenAI client",
    ["model"],
)

_error_counter = prometheus_client.Counter(
    "au_google_genai_error_count",
    "Number of errors from Google GenAI client",
    ["model", "error_type"],
)

FIRST_TOKEN_HIGH_LATENCY_THRESHOLD = 10  # seconds


def get_function_declaration(tool_name: str) -> FunctionDeclaration:
    tool_name_to_params = {
        "replace_text": FunctionDeclaration(
            name="replace_text",
            description="Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.",
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "old_text": Schema(
                        type=Type.STRING,
                        description="The original text.",
                    ),
                    "start_line_number": Schema(
                        type=Type.INTEGER,
                        description="The line number where the original text starts, inclusive.",
                    ),
                    "end_line_number": Schema(
                        type=Type.INTEGER,
                        description="The line number where the original text ends, inclusive.",
                    ),
                    "replacement_text": Schema(
                        type=Type.STRING,
                        description="The new text.",
                    ),
                },
                required=[
                    "old_text",
                    "start_line_number",
                    "end_line_number",
                    "replacement_text",
                ],
            ),
        ),
        "check_command_code_related": FunctionDeclaration(
            name="check_command_code_related",
            description="Check if the given command executed any code related checks.",
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "result": Schema(
                        type=Type.BOOLEAN,
                        description="Boolean indicating whether the command executed any code related checks.",
                    ),
                    "desc": Schema(
                        type=Type.STRING,
                        description="Short and concise one line explanation of the result.",
                    ),
                },
                required=["result", "desc"],
            ),
        ),
        "command_output_contain_errors": FunctionDeclaration(
            name="command_output_contain_errors",
            description="Report if the given command output contains errors.",
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "result": Schema(
                        type=Type.BOOLEAN,
                        description="Boolean indicating whether the command output contains errors.",
                    ),
                    "desc": Schema(
                        type=Type.STRING,
                        description="Short and concise one line explanation.",
                    ),
                },
                required=["result", "desc"],
            ),
        ),
        "glean_search": FunctionDeclaration(
            name="glean_search",
            description="Search in company's internal documentation including Notion, Slack, and Jira using Glean. Use this tool only when necessary to find relevant information not available in the codebase. This tool returns a list of search queries to retrieve relevant documents from Glean, complementing codebase searches. Use sparingly for additional context beyond the codebase.",
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "queries": Schema(
                        type=Type.ARRAY,
                        description="List of 1-3 concise search queries.",
                        items=Schema(
                            type=Type.STRING,
                        ),
                    ),
                },
                required=["queries"],
            ),
        ),
        "codebase-retrieval": FunctionDeclaration(
            name="codebase-retrieval",
            description="Use this tool to request information from the codebase. It will return relevant snippets for the requested information. This tool is not longer available to use. DO NOT CALL IT.",
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "information_request": Schema(
                        type=Type.STRING,
                        description="A description of the information you need.",
                    ),
                },
                required=["information_request"],
            ),
        ),
        "dummy_tool": FunctionDeclaration(
            name="dummy_tool",
            description="A placeholder tool. DO NOT CALL IT.",
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "input": Schema(
                        type=Type.STRING,
                        description="The input to the dummy tool.",
                    ),
                },
                required=["input"],
            ),
        ),
    }

    if tool_name not in tool_name_to_params:
        raise NotImplementedError(f"Tool {tool_name} not implemented")
    return tool_name_to_params[tool_name]


def convert_to_schema(input_schema_json: str, identifier: str) -> Schema:
    try:
        return json_schema_str_to_genai_schema(input_schema_json)
    except Exception as e:
        raise InvalidArgumentRpcError(
            f"Tool {identifier} failed input schema validation (jsonschema 2020-12)"
        ) from e


def loggable_tool_name(tool_name: str) -> str:
    if tool_name in [
        "save-file",
        "str-replace-editor",
        "remember",
        "launch-process",
        "write-process",
        "web-search",
        "notion-search",
        "notion-page",
    ]:
        return tool_name
    return ""


def _parse_usage_metadata_to_prompt_cache_usage(
    usage_metadata: GenerateContentResponseUsageMetadata,
) -> PromptCacheUsage:
    """Converts Google GenAI UsageMetadata to PromptCacheUsage."""

    candidates_tokens = usage_metadata.candidates_token_count or 0
    thoughts_tokens = getattr(usage_metadata, "thoughts_token_count", 0) or 0
    prompt_token_count = usage_metadata.prompt_token_count or 0
    cached_content_token_count = usage_metadata.cached_content_token_count or 0

    return PromptCacheUsage(
        input_tokens=prompt_token_count - cached_content_token_count,
        cache_creation_input_tokens=0,  # Not directly available because we use implicit caching.
        cache_read_input_tokens=cached_content_token_count,
        text_input_tokens=0,  # TODO: Not directly available, will have to figure out later
        tool_input_tokens=0,  # TODO: Not directly available, will have to figure out later
        text_output_tokens=candidates_tokens + thoughts_tokens,
        tool_output_tokens=0,  # thoughts_token_count is not tool output
    )


class GoogleGenaiClient(ThirdPartyModelClient):
    """
    A class to interact with the Google GenAI API for generating responses using google.genai library.
    """

    client_type = "google_genai"

    # Tool use ID templates for different model families. We currently only have vertex.
    TOOL_USE_ID_TEMPLATES = {
        "gemini": "toolu_aug_vrtx_gemini_{uuid}",
    }

    def __init__(
        self,
        project_id,
        region,
        model_name,
        temperature,
        max_output_tokens,
    ):
        """
        Initialize the Google GenAI API with project details and model parameters.

        Args:
            project_id (str): The ID of the Google Cloud project.
            region (str): The region of the Google Cloud project.
            model_name (str): The name of the model to use for generating responses.
            temperature (float): The temperature parameter for controlling the randomness of the responses.
            max_output_tokens (int): The maximum number of tokens to generate in the response.
        """
        self.client = genai_Client(vertexai=True, project=project_id, location=region)
        self.model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens

        # Set the tool use ID template based on the model name
        if "gemini" in model_name:
            self.tool_use_id_template = self.TOOL_USE_ID_TEMPLATES["gemini"]
        else:
            raise GoogleGenAiClientStartupError(
                f"Unknown model name: {model_name}. Cannot set tool use ID template."
            )

        if model_name in [
            "gemini-pro-experimental",
            "gemini-flash-experimental",
            "gemini-2.0-flash-exp",
            "gemini-2.0-flash-001",
            "gemini-2.5-pro-exp-03-25",
            "gemini-2.5-pro-preview-03-25",
            "gemini-2.5-pro-preview-05-06",
            "gemini-2.5-flash-preview-04-17",
            "gemini-2.5-flash-preview-05-20",
        ]:
            # No tokenizer for experimental models
            self.tokenizer = get_tokenizer_for_model("gemini-1.5-flash-001")
        else:
            self.tokenizer = get_tokenizer_for_model(model_name)
        _logger.info(
            "Google GenAI Client initialized",
            model_name=model_name,
            tool_use_id_template=self.tool_use_id_template,
        )

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """
        Generate a response based on the given message.

        Args:
            model_caller: A field passed in by the caller for metrics purposes.
            messages: The messages to generate a response for in the format (user_m, assistant_m)
                where the last tuple only has the user_message. Deprecated in favor of `chat_history`.
            system_prompt: The system prompt to use for the model.
            cur_message: The current message to generate a response for.
            chat_history: A structured format of `messages`.
            tools: List of available tools.
            tool_definitions: List of tool definitions.
            tool_choice: The selected tool choice.
            temperature: Temperature parameter for response generation.
            max_output_tokens: Maximum number of tokens in the response.
            prefill: Prefilled text to start the response with.
            use_caching: Whether to use response caching.
            request_context: Optional request context containing information about the request, including session ID.
            yield_final_parameters: Whether to yield the final parameters used for the request.

        Returns:
            Generator for the generated response text.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        _logger.info("generating response for %s messages", len(messages))

        if tool_choice:
            _logger.warning("GoogleGenaiClient does not currently support tool_choice")
        if use_caching:
            _logger.warning("GoogleGenaiClient does not currently support use_caching")

        if chat_history is None:
            chat_history = [
                Exchange(request_message=message[0], response_text=message[1])
                for message in messages
            ]

        formatted_messages = []
        # Create a hashmap to store tool use IDs and their corresponding names
        tool_use_id_to_name = {}

        try:
            for exchange in chat_history:
                request_content = format_request_message_content(
                    exchange.request_message, tool_use_id_to_name
                )
                if request_content:
                    formatted_messages.append(request_content)

                response_content = format_response_message_content(
                    exchange.response_text, tool_use_id_to_name
                )
                if response_content:
                    formatted_messages.append(response_content)

            cur_message_content = format_request_message_content(
                cur_message, tool_use_id_to_name
            )
            if cur_message_content:
                formatted_messages.append(cur_message_content)
        except ValueError as e:
            # Convert ValueError into a proper gRPC error with INVALID_ARGUMENT status
            raise InvalidArgumentRpcError(str(e))

        # Prefilling the response
        if prefill:
            _logger.info(
                "Prefilling response with length %d characters",
                len(prefill),
                model_name=self.model_name,
                client_type=self.client_type,
            )
            formatted_messages.append(
                Content(
                    role="model",
                    parts=[Part.from_text(text=prefill)],
                )
            )
        else:
            prefill = None
        temp_param = temperature if temperature is not None else self.temperature
        tokens_param = (
            max_output_tokens
            if max_output_tokens is not None
            else self.max_output_tokens
        )

        # `tools` are `str`s which `get_function_declaration()` maps to a pre-set
        # definition. `tool_definitions` are frontend-supplied definitions.
        function_declarations: list[FunctionDeclaration] = [
            get_function_declaration(tool_name) for tool_name in tools
        ] + [
            FunctionDeclaration(
                name=tool_def.name,
                description=tool_def.description,
                parameters=convert_to_schema(tool_def.input_schema_json, tool_def.name),
            )
            for i, tool_def in enumerate(tool_definitions)
        ]
        request_id = request_context.request_id if request_context else "unknown-id"
        _logger.info("Pre-defined tools (%s): %s", request_id, tools)
        _logger.info(
            "Custom tool definitions (%s): %s",
            request_id,
            [tool_def.name for tool_def in tool_definitions],
        )

        gemini_config = GenerateContentConfig(
            system_instruction=system_prompt,
            max_output_tokens=tokens_param,
            temperature=temp_param,
            safety_settings=SAFETY_SETTINGS,
            tools=[
                Tool(function_declarations=[declaration])
                for declaration in function_declarations
            ],
        )

        start_time = time.time()
        with GOOGLE_GENAI_RESPONSE_LATENCY.labels(
            model=self.model_name,
        ).time():
            try:
                first_message_time = None
                latest_prompt_cache_usage: PromptCacheUsage | None = None
                if yield_final_parameters:
                    yield ThirdPartyModelResponse(
                        text="",
                        final_parameters={
                            "model": self.model_name,
                            "contents": formatted_messages,
                            "config": gemini_config,
                        },
                    )

                response = self.client.models.generate_content_stream(
                    model=self.model_name,
                    contents=formatted_messages,
                    config=gemini_config,
                )

                # Stream the response
                assert response is not None
                # Set this flag to ensure that we only ever collect one tool call per generation.
                # It would be nice to revisit this in the future when we can support parallel tool calling.
                collected_tool_use = False
                for chunk in response:
                    if chunk.usage_metadata:
                        latest_prompt_cache_usage = (
                            _parse_usage_metadata_to_prompt_cache_usage(
                                chunk.usage_metadata
                            )
                        )

                    if first_message_time is None:
                        first_message_time = time.time()
                        first_latency = first_message_time - start_time
                        GOOGLE_GENAI_FIRST_TOKEN_LATENCY.labels(
                            model=self.model_name,
                        ).observe(first_latency)
                        if first_latency > FIRST_TOKEN_HIGH_LATENCY_THRESHOLD:
                            _logger.warning(
                                "First token took %.2f seconds to arrive",
                                first_latency,
                            )

                    if (
                        not chunk.candidates
                        or not chunk.candidates[0].content
                        or not chunk.candidates[0].content.parts
                    ):
                        continue

                    if len(chunk.candidates) > 1:
                        _logger.warning(
                            "Multiple candidates detected. Only the first candidate will be used."
                        )

                    content = chunk.candidates[0].content
                    for part in content.parts or []:
                        if part.text:
                            _output_text_chars_counter.labels(
                                model=self.model_name,
                            ).inc(len(part.text))
                            yield ThirdPartyModelResponse(text=part.text)
                        if part.function_call and not collected_tool_use:
                            collected_tool_use = True
                            assert part.function_call.name is not None
                            tool_name = part.function_call.name
                            tool_input = part.function_call.args or {}
                            # Use the provided ID if available, otherwise generate one
                            tool_use_id = part.function_call.id or ""
                            if not tool_use_id:
                                tool_use_id = self.generate_tool_use_id()

                            yield ThirdPartyModelResponse(
                                text="",
                                tool_use=ToolUseResponse(
                                    tool_name=tool_name,
                                    input=tool_input,
                                    tool_use_id=tool_use_id,
                                ),
                            )
                        elif part.function_call and collected_tool_use:
                            _logger.warning("Dropping a parallel tool call")

                yield ThirdPartyModelResponse(
                    text="",
                    end_of_stream=EndOfStream(
                        stop_reason=StopReason.END_TURN,  # TODO: Extract from response
                        prompt_cache_usage=latest_prompt_cache_usage,
                    ),
                )
            except Exception as e:
                _logger.error("Google GenAI error: %s", str(e))
                _error_counter.labels(
                    model=self.model_name,
                    error_type="api_error",
                ).inc()
                raise UnavailableRpcError(f"Google GenAI API error: {str(e)}")

    def generate_tool_use_id(self) -> str:
        """
        Generate a tool use ID using the template defined for this model.

        Returns:
            A string containing a high-entropy, URL-safe base64 encoded ID in the format specified by the template
        """
        # Generate a high-entropy, URL-safe base64 encoded string
        random_id = secrets.token_urlsafe(
            18
        )  # 18 bytes produces 24 characters in base64

        # Format the ID using the template for this model
        return self.tool_use_id_template.format(uuid=random_id)

    @override
    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: The number of tokens in the given message.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        _token_count_calls.inc()
        count_token_result = self.tokenizer.count_tokens(message)
        return count_token_result.total_tokens


def format_request_message_content(
    request_message: RequestMessage, tool_use_id_to_name: dict[str, str]
) -> Content | None:
    message_text = try_get_request_message_as_text(request_message)
    if message_text is not None:
        if message_text.strip() == "":
            _logger.error("Empty message text")
            return None
        return Content(role="user", parts=[Part(text=message_text)])
    if isinstance(request_message, str):
        raise ValueError("request_message must not be str by this point.")

    # Assert that request_message is iterable
    # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
    assert iter(request_message) is not None

    formatted_parts = []
    tool_added = False
    for node in request_message:
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            assert node.tool_result_node is None
            # Detect and remove empty or whitespace-only text nodes. Gemini API rejects
            # requests with empty or whitespace-only text nodes with a 400 Bad Request.
            if node.text_node.content.strip() == "":
                _logger.error("Empty text node detected - may cause API rejection")
            else:
                formatted_parts.append(Part(text=node.text_node.content))
        elif node.type == ChatRequestNodeType.TOOL_RESULT:
            assert node.tool_result_node is not None
            assert node.text_node is None
            if tool_added:
                continue
            tool_added = True
            # Look up the tool name in our hashmap
            if (
                tool_use_id_to_name
                and node.tool_result_node.tool_use_id in tool_use_id_to_name
            ):
                tool_name = tool_use_id_to_name[node.tool_result_node.tool_use_id]
            else:
                tool_name = "function-called-in-previous-round"

            formatted_parts.append(
                Part(
                    function_response=FunctionResponse(
                        id=node.tool_result_node.tool_use_id,
                        name=tool_name,
                        response={"output": node.tool_result_node.content},
                    )
                )
            )
            if node.tool_result_node.is_error:
                # TODO: Handle error responses
                _logger.warning(
                    f"Tool result for use {node.tool_result_node.tool_use_id} is error."
                    f" But Google GenAI API does not support error responses. Response:"
                    f" {node.tool_result_node.content}"
                )
        elif node.type == ChatRequestNodeType.IMAGE:
            assert node.image_node is not None
            if node.image_node.format == ImageFormatType.IMAGE_FORMAT_UNSPECIFIED:
                supported_formats = [
                    format_type.name
                    for format_type in ImageFormatType
                    if format_type != ImageFormatType.IMAGE_FORMAT_UNSPECIFIED
                ]
                raise ValueError(
                    f"Image format must be specified. Supported formats: {', '.join(supported_formats)}"
                )

            media_type = {
                ImageFormatType.PNG: "image/png",
                ImageFormatType.JPEG: "image/jpeg",
            }.get(node.image_node.format)

            if media_type is None:
                if isinstance(node.image_node.format, ImageFormatType):
                    name = node.image_node.format.name
                else:
                    name = "<unknown>"
                raise ValueError(f"Unsupported image format: {name}")

            formatted_parts.append(
                Part.from_bytes(
                    data=base64.b64decode(node.image_node.image_data),
                    mime_type=media_type,
                )
            )

    if not formatted_parts:
        return None
    return Content(role="user", parts=formatted_parts)


def format_response_message_content(
    response_text: ResponseMessage, tool_use_id_to_name: dict[str, str]
) -> Content | None:
    if isinstance(response_text, str):
        if response_text.strip() == "":
            _logger.error("Empty response text")
            return None
        return Content(role="model", parts=[Part(text=response_text)])

    if isinstance(response_text, list):
        response_nodes = response_text
        formatted_parts = []
        tool_added = False
        for node in response_nodes:
            if node.type == ChatResultNodeType.RAW_RESPONSE:
                is_whitespace_only = node.content.strip() == ""
                if not is_whitespace_only:
                    assert node.tool_use is None
                    formatted_parts.append(Part(text=node.content))
            elif node.type == ChatResultNodeType.TOOL_USE:
                assert node.tool_use is not None
                if tool_added:
                    continue
                tool_added = True

                # Make sure we have a valid tool use ID
                tool_use_id = node.tool_use.tool_use_id

                # If the tool use ID somehow already exists in the map that's a client error
                if tool_use_id in tool_use_id_to_name:
                    raise InvalidArgumentRpcError(
                        "Multiple tool_use with same tool_use_id"
                    )

                # Store the tool use ID and name in our hashmap for future lookups
                tool_use_id_to_name[tool_use_id] = node.tool_use.name

                formatted_parts.append(
                    Part(
                        function_call=FunctionCall(
                            id=tool_use_id,
                            name=node.tool_use.name,
                            args=node.tool_use.input,
                        )
                    )
                )
        if not formatted_parts:
            return None
        return Content(role="model", parts=formatted_parts)


def formatted_messages_contain_tools(formatted_messages: list[Content]) -> bool:
    for message in formatted_messages:
        if message.parts:
            for node in message.parts:
                if node.function_call is not None or node.function_response is not None:
                    return True
    return False


class GoogleGenAiClientStartupError(Exception):
    """Exception for errors during Google GenAI client startup."""

    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)

    def __str__(self):
        return f"GoogleGenAiClientStartupError: {self.message}"

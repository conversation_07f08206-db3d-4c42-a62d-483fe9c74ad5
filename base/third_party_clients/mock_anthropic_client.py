"""A mock client for Anthropic models for testing purposes."""

from unittest.mock import <PERSON><PERSON>ock

from anthropic.types import RawMessageStreamEvent
from base.third_party_clients import anthropic_direct_client


class MockAnthropicClient(anthropic_direct_client.AnthropicDirectClient):
    """A mock client that returns no response.

    Can be useful for testing and debugging to just inspect the final parameters.
    """

    client_type = "anthropic_mock"

    def __init__(
        self,
        stream: list[RawMessageStreamEvent],
        model_name: str,
        temperature: float,
        max_output_tokens: int,
    ):
        """
        Initialize the mock Anthropic client with model parameters.

        Args:
            model_name (str): The name of the model to use for generating responses.
            temperature (float): The temperature parameter for controlling the randomness of the responses.
            max_output_tokens (int): The maximum number of tokens to generate in the response.
        """
        super().__init__(
            api_key="fake-api-key",  # pragma: allowlist secret
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )

        self.client = MagicMock()
        self.count_tokens_client = MagicMock()

        mock_messages = MagicMock()
        mock_stream = MagicMock()
        mock_stream.return_value.__enter__.return_value = stream
        mock_stream.return_value.__exit__.return_value = None

        mock_messages.stream = mock_stream

        self.client.messages = mock_messages

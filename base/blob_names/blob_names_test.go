package blob_names

import (
	"bytes"
	"os"
	"strings"
	"testing"
)

func TestDecodeHexBlobName(t *testing.T) {
	tests := []struct {
		name      string
		hexStr    string
		wantBytes []byte
		wantErr   bool
	}{
		{
			name:      "Valid SHA-256 hex string",
			hexStr:    "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
			wantBytes: []byte{0xe3, 0xb0, 0xc4, 0x42, 0x98, 0xfc, 0x1c, 0x14, 0x9a, 0xfb, 0xf4, 0xc8, 0x99, 0x6f, 0xb9, 0x24, 0x27, 0xae, 0x41, 0xe4, 0x64, 0x9b, 0x93, 0x4c, 0xa4, 0x95, 0x99, 0x1b, 0x78, 0x52, 0xb8, 0x55},
			wantErr:   false,
		},
		{
			name:    "Invalid hex string (not hex characters)",
			hexStr:  "invalid_hex_string",
			wantErr: true,
		},
		{
			name:    "Invalid hex string (too short)",
			hexStr:  "e3b0c442",
			wantErr: true,
		},
		{
			name:    "Invalid hex string (too long)",
			hexStr:  "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
			wantErr: true,
		},
		{
			name:      "Valid SHA-256 hex string with mixed case",
			hexStr:    "E3B0C44298FC1C149AFBF4C8996FB92427AE41E4649B934CA495991B7852B855",
			wantBytes: []byte{0xe3, 0xb0, 0xc4, 0x42, 0x98, 0xfc, 0x1c, 0x14, 0x9a, 0xfb, 0xf4, 0xc8, 0x99, 0x6f, 0xb9, 0x24, 0x27, 0xae, 0x41, 0xe4, 0x64, 0x9b, 0x93, 0x4c, 0xa4, 0x95, 0x99, 0x1b, 0x78, 0x52, 0xb8, 0x55},
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotBytes, err := DecodeHexBlobName(BlobName(tt.hexStr))
			if (err != nil) != tt.wantErr {
				t.Errorf("DecodeHexBlobName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !bytes.Equal(gotBytes, tt.wantBytes) {
				t.Errorf("DecodeHexBlobName() = %x, want %x", gotBytes, tt.wantBytes)
			}
		})
	}
}

func TestGetBlobName(t *testing.T) {
	content, err := os.ReadFile("test_data/blob-0")
	if err != nil {
		t.Errorf("io.ReadAll() error = %v", err)
	}

	expectedBlobName, err := os.ReadFile("test_data/blob-0.name")
	if err != nil {
		t.Errorf("io.ReadAll() error = %v", err)
	}
	expectedBlobNameStr := BlobName(strings.TrimSpace(string(expectedBlobName)))

	blobName := GetBlobName("base/blob_names/test_data/blob-0", content)
	if blobName != expectedBlobNameStr {
		t.Errorf("GetBlobName() = %v, want %v", blobName, expectedBlobNameStr)
	}

	blobName = GetBlobName("file.txt", []byte("contents"))
	if blobName != "11506d0b5d2b16a4bbd0630ef25eb7cccc020f63aae758de2a7e6d1a7de8bba3" {
		t.Errorf("GetBlobName() = %v, want %v", blobName, "11506d0b5d2b16a4bbd0630ef25eb7cccc020f63aae758de2a7e6d1a7de8bba3")
	}

	blobName = GetBlobName("", []byte("contents"))
	if blobName != "d1b2a59fbea7e20077af9f91b27e95e865061b270be03ff539ab3b73587882e8" {
		t.Errorf("GetBlobName() = %v, want %v", blobName, "d1b2a59fbea7e20077af9f91b27e95e865061b270be03ff539ab3b73587882e8")
	}
}

func TestNewBlobNameProto(t *testing.T) {
	blobName := BlobName("e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855")
	proto, err := NewBlobNameProto(blobName, false)
	if err != nil {
		t.Errorf("NewBlobNameProto() error = %v", err)
	}
	if proto.GetNameHex() != string(blobName) {
		t.Errorf("NewBlobNameProto() = %v, want %v", proto.GetNameHex(), blobName)
	}
	blobName2, err := FromBlobNameProto(proto)
	if err != nil {
		t.Errorf("FromBlobNameProto() error = %v", err)
	}
	if blobName2 != blobName {
		t.Errorf("FromBlobNameProto() = %v, want %v", blobName2, blobName)
	}

	proto, err = NewBlobNameProto(blobName, true)
	if err != nil {
		t.Errorf("NewBlobNameProto() error = %v", err)
	}
	blobName2, err = FromBlobNameProto(proto)
	if err != nil {
		t.Errorf("FromBlobNameProto() error = %v", err)
	}
	if blobName2 != blobName {
		t.Errorf("FromBlobNameProto() = %v, want %v", blobName2, blobName)
	}
}

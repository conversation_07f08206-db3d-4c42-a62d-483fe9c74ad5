"""Prompt formatter for localization changes."""

from dataclasses import dataclass, field

from dataclasses_json import DataClassJsonMixin
from typing_extensions import override

from base.diff_utils.diff_formatter import (
    format_file_changes_with_ranges,
    format_tokenized_hunks,
    tokenize_diff_hunks,
)
from base.prompt_format.common import PromptFormatterOutput
from base.prompt_format_next_edit.common import NextEditPromptInput
from base.prompt_format_retrieve.prompt_formatter import (
    RetrieverPromptFormatter,
)
from base.tokenizers.tokenizer import (
    NextEditGenSpecialTokens,
    RetrievalSpecialTokens,
    Tokenizer,
)


@dataclass(frozen=True)
class LocalizationNextEditPromptInput(NextEditPromptInput):
    """Input to the LocalizationNextEditPromptInput."""

    command_output: str | None = None
    """The output from the command that generated the change."""


# TODO(arun): reconcile with the common `NextEditFormatterConfig`.
@dataclass
class NextEditLocationQueryFormatter(
    RetrieverPromptFormatter[LocalizationNextEditPromptInput]
):
    """The query formatter for next edit location."""

    @dataclass
    class Config(DataClassJsonMixin):
        """The configuration for the NextEditLocationQueryFormatter."""

        diff_context_lines: int = 5
        """The number of hunk context lines to show in the diff section."""

        ignore_whitespace_changes: bool = True
        """Whether to ignore whitespace changes in the input diff."""

        max_prompt_tokens: int = 4096
        """The maximum number of tokens used by the input prompt."""

        max_instruction_tokens: int = 512
        """The maximum number of tokens used by the instruction."""

        # TODO(arun): change to true once we've trained a new location model.
        deduplicate_identical_paths: bool = False
        """Whether to filter out duplicate file paths in the diff."""

        # TODO(arun): change to true once we've trained a new location model.
        use_smart_header: bool = False
        """Whether to use a smart header in the diff."""

        truncate_instructions_tail: bool = True
        """If true, truncate from the tail of instructions."""

        max_diff_tokens: int | None = None
        """The maximum number of tokens used by the diff section."""

        max_command_output_tokens: int | None = None
        """The maximum number of tokens used by the command output."""

        def __post_init__(self):
            # +2 for the end of the instruction token and end of query token
            if self.max_prompt_tokens < self.max_instruction_tokens + 2:
                raise ValueError(
                    f"{self.max_prompt_tokens=} must be more than "
                    f"{self.max_instruction_tokens=} + 2"
                )

            assert self.max_diff_tokens is None or self.max_diff_tokens >= 0

    input_type = LocalizationNextEditPromptInput
    """The type of the input for the formatter."""

    tokenizer: Tokenizer
    """The tokenizer used to tokenize the prompt."""

    config: Config = field(default_factory=Config)

    def __post_init__(self):
        assert isinstance(self.tokenizer.special_tokens, RetrievalSpecialTokens)
        assert isinstance(self.tokenizer.special_tokens, NextEditGenSpecialTokens)
        self.special_tokens = self.tokenizer.special_tokens

        # TODO (c-flaherty): Temporary hack - will fix once it's fixed in model training pipeline
        self.command_output_token = self.special_tokens.far_prefix

    @override
    def format_prompt(
        self, prompt_input: LocalizationNextEditPromptInput
    ) -> PromptFormatterOutput:
        """Format the input into a token sequence."""
        ret = list[int]()

        if self.config.max_instruction_tokens > 0 and prompt_input.instruction:
            instruction_toks = self.tokenizer.tokenize_safe(prompt_input.instruction)
            ret += (
                instruction_toks[: self.config.max_instruction_tokens]
                if self.config.truncate_instructions_tail
                else instruction_toks[-self.config.max_instruction_tokens :]
            )
            ret.append(self.special_tokens.instruction)

        if self.config.max_command_output_tokens and prompt_input.command_output:
            command_output_toks = self.tokenizer.tokenize_safe(
                prompt_input.command_output
            )
            ret += command_output_toks[-self.config.max_command_output_tokens :]
            ret.append(self.command_output_token)

        # -1 for the end of query token
        remaining_tokens = self.config.max_prompt_tokens - len(ret) - 1
        if self.config.max_diff_tokens is not None:
            remaining_tokens = min(remaining_tokens, self.config.max_diff_tokens)
        diff_hunks = format_file_changes_with_ranges(
            changes=prompt_input.recent_changes,
            diff_context_lines=self.config.diff_context_lines,
            use_smart_header=self.config.use_smart_header,
            # TODO(arun): Support "ignore_whitespace" = True in diff_utils
        )
        t_hunks = tokenize_diff_hunks(
            diff_hunks,
            remaining_tokens,
            self.tokenizer,
            deduplicate_identical_paths=self.config.deduplicate_identical_paths,
        )
        ret += format_tokenized_hunks(t_hunks)
        ret.append(self.special_tokens.end_of_query)

        return PromptFormatterOutput([ret])

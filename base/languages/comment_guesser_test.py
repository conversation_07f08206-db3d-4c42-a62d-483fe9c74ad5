"""Module to guess whether a file is a comment.

NOTE: We don't test every language, but test all the major cases.
To see how this method performs against tree sitter, see `guesser_inspect.ipynb`.
"""

import pytest

from pathlib import Path

from base.languages.comment_guesser import (
    default_comment_guesser,
    guess_is_comment,
    guess_is_multi_line_comment,
    guess_is_single_line_comment,
)
from base.languages.language_guesser import LanguageId


def test_default_comment_guesser():
    """Test the default comment guesser."""
    assert default_comment_guesser() is not None


@pytest.mark.parametrize(
    "prefix, suffix, path, expected_single, expected_multi",
    [
        # C++: Normal cases
        ("// foo", " bar\n", "x.cpp", True, False),
        ("\n// foo", "bar\n", "x.cpp", True, False),
        ("\n// foo\n", "bar\n", "x.cpp", False, False),
        ("", "// bar", "x.cpp", False, False),
        ("/* foo\n", "bar */\n", "x.cpp", False, True),
        ("/* foo\n", "bar\n", "x.cpp", False, True),
        ("/* // foo", "bar\n", "x.cpp", True, True),
        ("foo/****\n * bar\n", "*/\n", "x.cpp", False, True),
        # HTML: Test multiple types of multi-line
        ("// foo", " bar\n", "x.html", True, False),
        ("// foo\n", " bar\n", "x.html", False, False),
        ("/* foo\n", "bar */\n", "x.html", False, True),
        ("/* foo\n", "bar\n", "x.html", False, True),
        ("<!-- foo\n", "bar -->\n", "x.html", False, True),
        ("<!------ foo\n", "bar\n", "x.html", False, True),
        # Python: Test when multi-line comment delimiters are the same.
        ("# foo", "bar\n", "x.py", True, False),
        ("# foo\n", "bar\n", "x.py", False, False),
        ('"""\n', '"""\n', "x.py", False, True),
        ('"""foo\n', "bar\n", "x.py", False, True),
        ('"""foo"""\n', '""""\n', "x.py", False, False),
        ('"""foo"""\n"""bar', '""""\n', "x.py", False, True),
        ('""""""', '""""\n', "x.py", False, False),
        ('"""""""', '""""\n', "x.py", False, False),
        ("'''\n", "bar\n", "x.py", False, True),
        # Test when prefix might be truncated.
        ("x=10\n" * 1000 + '"""\n', "bar\n", "x.py", False, True),
        ('foo\n"""' + "x=10\n" * 1000 + '"""\n', "bar\n", "x.py", False, True),
        # None: When language is unknown, should always be False
        ("// foo", "bar", "x.foo", False, False),
        ("/* foo\n", "bar */\n", None, False, False),
    ],
)
def test_guess_is_comment(
    prefix: str,
    suffix: str,
    path: Path | str | None,
    expected_single: bool,
    expected_multi: bool,
):
    """Test the guess_is_comment functions."""
    assert guess_is_single_line_comment(prefix, suffix, path) == expected_single
    assert guess_is_multi_line_comment(prefix, suffix, path) == expected_multi
    assert guess_is_comment(prefix, suffix, path) == (expected_single or expected_multi)

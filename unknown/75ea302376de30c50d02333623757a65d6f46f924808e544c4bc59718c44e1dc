determined:
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/codestral.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 32770
  eot_token_id: 2
  pad_token_id: 2
  gradient_accumulation_steps: 16
  batch_size: 2
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 250
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/codestral/Codestral-22B-v0.1/converted
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/codestral_eth6_4m_morelang3_fpref1kretsuf0k5npref0k5_aug05rdrop03/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/codestral_eth6_4m_morelang3_fpref1kretsuf0k5npref0k5_aug05rdrop03/validation_dataset
  checkpoint_optimizer_state: False

  tokenizer_name: CodestralTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: codestral
  wandb_project: rogue

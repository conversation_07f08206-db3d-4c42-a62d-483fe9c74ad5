load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load(
    "//tools/bzl:python.bzl",
    "py_binary",
    "py_library",
    "py_oci_image",
    "py_proto_library",
)
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "embeddings_indexer_server",
    srcs = [
        "embeddings_indexer_server.py",
    ],
    deps = [
        ":chunker_factory",
        ":handler",
        "//base/executor:submit_blocking_executor",
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format_retrieve",
        "//base/python/grpc:client_options",
        "//base/tokenizers",
        "//models/retrieval/chunking",
        "//services/content_manager/client",
        "//services/embedder_host/client",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/token_exchange/client:client_py",
        requirement("numpy"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("protobuf"),
        requirement("prometheus-client"),
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
    ],
)

py_library(
    name = "handler",
    srcs = [
        "handler.py",
    ],
    deps = [
        ":chunk_py_proto",
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format_retrieve",
        "//base/python/numpy_utils",
        "//base/tokenizers",
        "//models/retrieval/chunking",
        "//services/content_manager/client",
        "//services/embedder_host/client",
        "//services/embedder_host/client:multiplex",
        requirement("numpy"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("prometheus-client"),
        requirement("protobuf"),
        "//base/logging:struct_logging",
    ],
)

pytest_test(
    name = "handler_test",
    srcs = ["handler_test.py"],
    data = ["test_data/pre_chunked.jsonl"],
    deps = [
        ":handler",
        "//base/prompt_format_retrieve",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":embeddings_indexer_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    trivy_allow_list = [],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//services/deploy:endpoints",
    ],
)

proto_library(
    name = "chunk_proto",
    srcs = ["chunk.proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

py_proto_library(
    name = "chunk_py_proto",
    protos = [":chunk_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

py_library(
    name = "chunker_factory",
    srcs = ["chunker_factory.py"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        ":line_rpc",
        "//models/retrieval/chunking",
        requirement("dataclasses_json"),
        requirement("grpcio"),
    ],
)

pytest_test(
    name = "chunker_factory_test",
    srcs = ["chunker_factory_test.py"],
    deps = [
        ":chunker_factory",
    ],
)

py_library(
    name = "line_rpc",
    srcs = ["line_rpc.py"],
    visibility = [
        "//services:__subpackages__",
    ],
)

pytest_test(
    name = "line_rpc_test",
    srcs = ["line_rpc_test.py"],
    deps = [
        ":line_rpc",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":monitoring_kubecfg",
    ],
)

"""Utilities for diffing kubecfg configs."""

import logging
import re


def _is_meaningful_line(line: str) -> bool:
    """Returns whether the line is meaningful."""
    # filter out lines not starting with +/-
    if not line.startswith("+") and not line.startswith("-"):
        return False
    # filter out lines with three +++ or ---
    if line.startswith("+++") or line.startswith("---"):
        return False

    # filter out lines that have the version in them
    if re.match(r"^.*app.kubernetes.io/version:.*", line):
        return False

    # filter out lines that deploy info
    if re.match(r"^.*augmentcode.com/deployed-by:.*", line):
        return False

    # filter out lines with only a time change
    if re.match(r"^[+-]\s+time:.*", line):
        return False

    # filter out generation
    if re.match(r"^.*generation:.*", line):
        return False
    return True


def is_meaningful_diff(diff: str) -> bool:
    """Returns whether the diff is meaningful.

    Meaningful means that it contains lines that are not just trivial
    changes to the time and generation. The changes should be applied
    to the cluster.

    Args:
      diff: The diff to check.

    Returns:
      Whether the diff is meaningful.
    """
    lines = diff.splitlines()
    lines = list(filter(_is_meaningful_line, lines))
    if lines:
        logging.info("Found meaningful diff:")
        for line in lines:
            logging.info("%s", line)
    return len(lines) > 0


VERSION_RE = re.compile(r"^-.*app.kubernetes.io/version: ([a-z0-9-]+)")


def extract_git_versions(diff: str) -> set[str]:
    """Extracts the git version from the diff."""
    lines = diff.splitlines()
    versions = set()
    for line in lines:
        m = VERSION_RE.match(line)
        if m:
            versions.add(m.group(1))
    return versions

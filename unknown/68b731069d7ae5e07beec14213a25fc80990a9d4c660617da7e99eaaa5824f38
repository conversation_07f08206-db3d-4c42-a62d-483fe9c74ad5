#!/bin/bash -x

#
set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

# Install golang: https://go.dev/doc/install
# NOTE: please keep the version here in sync with WORKSPACE
GO_TAR=go1.23.5.linux-amd64.tar.gz
wget https://dl.google.com/go/$GO_TAR
sha256sum -c <<SUM
cbcad4a6482107c7c7926df1608106c189417163428200ce357695cc7e01d091 $GO_TAR
SUM
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf $GO_TAR
rm $GO_TAR

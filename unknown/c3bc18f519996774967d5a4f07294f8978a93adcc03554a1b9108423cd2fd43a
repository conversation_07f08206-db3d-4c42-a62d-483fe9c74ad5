import { MockCommand } from "../__mocks__/mock-augment-command";
import { generateMockWorkspaceConfig } from "../__mocks__/mock-augment-config";
import { commands, mockWorkspaceConfigChange, resetMockWorkspace } from "../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../augment-config-listener";
import { CommandManager, CommandType } from "../command-manager";

describe("command-manager", () => {
    beforeEach(() => {
        resetMockWorkspace();
    });

    afterEach(() => {
        resetMockWorkspace();
    });

    test("register", async () => {
        const manager = new CommandManager(new AugmentConfigListener());
        manager.register([]);
        expect(manager.allCommands).toEqual([]);

        const cmd1 = new MockCommand("vscode-example.test-1");
        const cmd2 = new MockCommand("vscode-example.test-2");
        const cmd3 = new MockCommand("vscode-example.test-3");
        const cmd4 = new MockCommand("vscode-example.test-4");

        manager.register([cmd1]);
        expect(manager.allCommands).toEqual([cmd1]);

        // Ensure registered names are ignored
        manager.register([new MockCommand("vscode-example.test-1"), cmd2]);
        expect(manager.allCommands).toEqual([cmd1, cmd2]);

        // Ensure all new commands are registered
        manager.register([cmd3, cmd4]);
        expect(manager.allCommands).toEqual([cmd1, cmd2, cmd3, cmd4]);

        await commands.executeCommand("vscode-example.test-1", "Example Arg 1", "Example Arg 2");
        const cmd1Mock = (cmd1.run as jest.Mock).mock;
        expect(cmd1Mock.calls.length).toEqual(1);
        expect(cmd1Mock.calls[0]).toEqual(["Example Arg 1", "Example Arg 2"]);
    });

    test("availableCommands", () => {
        const config = new AugmentConfigListener();
        const manager = new CommandManager(config);

        const cmd1 = new MockCommand("vscode-example.test-1", { type: CommandType.public });
        const cmd2 = new MockCommand("vscode-example.test-2", {
            type: CommandType.debug,
            canRun: false,
        });
        manager.register([cmd1, cmd2]);

        // Should filter out debug commands
        expect(manager.availableCommands).toEqual([cmd1]);

        // Should include all commands
        cmd2.options.canRun = true;
        expect(manager.availableCommands).toEqual([cmd1, cmd2]);
    });

    test("availableGroups", () => {
        const config = new AugmentConfigListener();
        const manager = new CommandManager(config);

        const cmd1 = new MockCommand("vscode-example.test-1", { type: CommandType.public });
        const cmd2 = new MockCommand("vscode-example.test-2", {
            type: CommandType.debug,
            canRun: false,
        });
        const cmd3 = new MockCommand("vscode-example.test-3", {
            type: CommandType.public,
            canRun: true,
        });
        const cmd4 = new MockCommand("vscode-example.test-4", {
            type: CommandType.public,
            canRun: true,
        });
        manager.registerGroup("1", [cmd1]);
        manager.registerGroup("1", [cmd2]);
        manager.registerGroup("2", [cmd3]);
        manager.registerGroup("1", [cmd4]);

        // Should filter out debug commands and merge first two "1" groups
        expect(manager.availableCommands).toEqual([cmd1, cmd3, cmd4]);
        expect(manager.availableCommandGroups).toEqual([
            { name: "1", commands: [cmd1] },
            { name: "2", commands: [cmd3] },
            { name: "1", commands: [cmd4] },
        ]);

        // Should include all commands
        cmd2.options.canRun = true;
        expect(manager.availableCommands).toEqual([cmd1, cmd2, cmd3, cmd4]);
        expect(manager.availableCommandGroups).toEqual([
            { name: "1", commands: [cmd1, cmd2] },
            { name: "2", commands: [cmd3] },
            { name: "1", commands: [cmd4] },
        ]);

        // Should merge all three "1" groups
        cmd3.options.canRun = false;
        expect(manager.availableCommands).toEqual([cmd1, cmd2, cmd4]);
        expect(manager.availableCommandGroups).toEqual([
            { name: "1", commands: [cmd1, cmd2, cmd4] },
        ]);
    });

    test("toggling debug commands", () => {
        const registerSpy = jest.spyOn(commands, "registerCommand");

        const config = new AugmentConfigListener();
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                advanced: {
                    enableDebugFeatures: false,
                },
            })
        );
        const manager = new CommandManager(config);

        const cmd1 = new MockCommand("vscode-example.test-1", { type: CommandType.public });
        const cmd2 = new MockCommand("vscode-example.test-2", {
            type: CommandType.debug,
            canRun: true,
        });
        const cmd3 = new MockCommand("vscode-example.test-3", {
            type: CommandType.public,
            canRun: true,
        });
        const cmd4 = new MockCommand("vscode-example.test-4", {
            type: CommandType.private,
            canRun: true,
        });
        manager.registerGroup("1", [cmd1]);
        manager.registerGroup("1", [cmd2]);
        manager.registerGroup("2", [cmd3]);
        manager.registerGroup("1", [cmd4]);

        // Should filter out debug commands and merge first two "1" groups
        expect(manager.availableCommands).toEqual([cmd1, cmd3, cmd4]);
        expect(manager.availableCommandGroups).toEqual([
            { name: "1", commands: [cmd1] },
            { name: "2", commands: [cmd3] },
            { name: "1", commands: [cmd4] },
        ]);
        expect(registerSpy).toHaveBeenCalledTimes(3);
        expect(registerSpy.mock.calls).toEqual([
            [cmd1.commandID, expect.any(Function)],
            [cmd3.commandID, expect.any(Function)],
            [cmd4.commandID, expect.any(Function)],
        ]);

        // Should include all commands
        registerSpy.mockClear();
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                advanced: {
                    enableDebugFeatures: true,
                },
            })
        );
        expect(manager.availableCommands).toEqual([cmd1, cmd2, cmd3, cmd4]);
        expect(manager.availableCommandGroups).toEqual([
            { name: "1", commands: [cmd1, cmd2] },
            { name: "2", commands: [cmd3] },
            { name: "1", commands: [cmd4] },
        ]);

        // Should merge all three "1" groups
        cmd3.options.canRun = false;
        expect(manager.availableCommands).toEqual([cmd1, cmd2, cmd4]);
        expect(manager.availableCommandGroups).toEqual([
            { name: "1", commands: [cmd1, cmd2, cmd4] },
        ]);

        expect(registerSpy).toHaveBeenCalledTimes(1);
        expect(registerSpy.mock.calls).toEqual([[cmd2.commandID, expect.any(Function)]]);

        // Should unregister when we switch back
        registerSpy.mockClear();
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                advanced: {
                    enableDebugFeatures: false,
                },
            })
        );
        cmd3.options.canRun = true;
        expect(manager.availableCommands).toEqual([cmd1, cmd3, cmd4]);
        expect(manager.availableCommandGroups).toEqual([
            { name: "1", commands: [cmd1] },
            { name: "2", commands: [cmd3] },
            { name: "1", commands: [cmd4] },
        ]);
        expect(registerSpy).not.toHaveBeenCalled();
    });

    test("runCommand", () => {
        const registerSpy = jest.spyOn(commands, "registerCommand");
        const config = new AugmentConfigListener();
        const manager = new CommandManager(config);

        const cmd1 = new MockCommand("vscode-example.test1", {
            type: CommandType.public,
            canRun: false,
        });
        const cmd1Spy = jest.spyOn(cmd1, "run");

        // Should handle a command that cannot be run
        manager.register([cmd1]);
        expect(registerSpy).toHaveBeenCalledTimes(1);
        const debugRunFn = registerSpy.mock.calls[0][1];

        debugRunFn();
        expect(cmd1Spy).not.toHaveBeenCalled();

        // Should handle a command that can be run
        cmd1.options.canRun = true;
        debugRunFn();
        expect(cmd1Spy).toHaveBeenCalled();
    });

    test("dispose", () => {
        const registerSpy = jest.spyOn(commands, "registerCommand");
        const config = new AugmentConfigListener();
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                advanced: {
                    enableDebugFeatures: false,
                },
            })
        );

        const manager = new CommandManager(config);
        const cmd1 = new MockCommand("vscode-example.test-1", { type: CommandType.public });
        const cmd2 = new MockCommand("vscode-example.test-2", { type: CommandType.public });
        const cmd3 = new MockCommand("vscode-example.test-3", { type: CommandType.debug });
        manager.register([cmd1, cmd2, cmd3]);

        expect(manager.availableCommands).toEqual([cmd1, cmd2]);
        expect(registerSpy).toHaveBeenCalledTimes(2);

        manager.dispose();
        expect(manager.availableCommands).toEqual([]);
    });
});

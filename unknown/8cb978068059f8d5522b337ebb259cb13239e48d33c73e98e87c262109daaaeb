#!/bin/bash

set -e

if [[ -z $EMAIL_NAME ]]; then
	if [[ -f ~/.augment/user.json ]]; then
		EMAIL_NAME=$(cat ~/.augment/user.json | jq -r .name)
	fi
fi
if [[ -z $EMAIL_NAME ]] || [[ $EMAIL_NAME == "augment" ]] || [[ $EMAIL_NAME == "github" ]] || [[ $EMAIL_NAME == "root" ]]; then
	echo "Set \$EMAIL_NAME to your username and run again"
	exit 1
fi

kernel=$(uname -s)
if [[ $kernel == "Darwin" ]]; then
	curl -L "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
	echo
	echo "You may need to enter your local password for sudo"
	echo
	sudo installer -pkg AWSCLIV2.pkg -target /
	rm -f AWSCLIV2.pkg
else
	hw=$(uname -m)
	if [[ $hw == "x86_64" ]]; then
		URL="https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip"
	else
		URL="https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip"
	fi
	curl -L $URL -o "awscliv2.zip"
	unzip -o awscliv2.zip
	rm -f awscliv2.zip
	echo
	echo "You may need to enter your local password for sudo"
	echo
	# The return value from this is not useful
	yes | sudo ./aws/install --update || true
fi

aws configure

mkdir -p ~/.augment
jq --null-input --arg name $EMAIL_NAME '{"name": $name}' >~/.augment/user.json

<script lang="ts" generics="TOption extends IMentionable">
  import { type Readable } from "svelte/store";
  import type { Instance } from "tippy.js";

  import Tooltip from "../../tooltips/Tooltip.svelte";
  import type { IMentionable } from "../types";
  import type { IMentionChipData } from "./store";

  export let chipData: Readable<IMentionChipData<TOption> | null>;

  // Parse out data from the store
  $: option = $chipData?.data;
  $: activeClientRect = $chipData?.clientRect;

  // Show status is based on whether there is a current active filepath.
  // However, for visual stability, the tooltip should always show contents of the last valid filepath.
  let lastValidOption: TOption | undefined = option;
  let lastValidActiveClientRect: DOMRect = new DOMRect();
  $: lastValidOption = option ?? lastValidOption;
  $: lastValidActiveClientRect = activeClientRect ?? lastValidActiveClientRect;

  let tooltip: Instance | undefined;
  $: {
    if (option) {
      tooltip?.enable();
      tooltip?.show();
    } else {
      tooltip?.hide();
      tooltip?.disable();
    }
  }
</script>

<Tooltip
  bind:tooltip
  tippyProps={{
    delay: 0,
    duration: 0,
    trigger: "manual",
    getReferenceClientRect: () => lastValidActiveClientRect,
    placement: "top",
    showOnCreate: false,
    popperOptions: {
      strategy: "fixed",
    },
  }}
>
  <div slot="trigger" />
  <slot
    name="tooltipContents"
    slot="tooltipContents"
    let:tooltip
    {tooltip}
    option={lastValidOption}
  />
</Tooltip>

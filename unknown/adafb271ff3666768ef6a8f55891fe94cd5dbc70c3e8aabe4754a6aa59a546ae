<script lang="ts">
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";
  import CodeInsertButton from "$common-webviews/src/common/components/CodeInsertButton.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import OpenFileButton from "../../conversation/blocks/tools/components/OpenFileButton.svelte";
  import type { ICodeblockActionButton } from "../utils";
  import { CodeblockActionType } from "./types";

  export let primaryButton: ICodeblockActionButton;

  export const onClickForSuccessButton = async () => {
    primaryButton.onClick();
    return "success" as const;
  };
</script>

{#if primaryButton.codeblockActionType === CodeblockActionType.copy}
  <CopyButton stickyColor size={1} onCopy={onClickForSuccessButton}>
    <TextAugment slot="text" size={1}>{primaryButton.buttonText}</TextAugment>
  </CopyButton>
{:else if primaryButton.codeblockActionType === CodeblockActionType.goTo}
  <OpenFileButton stickyColor size={1} onOpenLocalFile={onClickForSuccessButton}>
    <TextAugment slot="text" size={1}>{primaryButton.buttonText}</TextAugment>
  </OpenFileButton>
{:else}
  <CodeInsertButton
    codeblockActionType={primaryButton.codeblockActionType}
    size={1}
    onClick={onClickForSuccessButton}
    successText={primaryButton.onSuccessMessage}
  >
    <TextAugment slot="text" size={1}>{primaryButton.buttonText}</TextAugment>
  </CodeInsertButton>
{/if}

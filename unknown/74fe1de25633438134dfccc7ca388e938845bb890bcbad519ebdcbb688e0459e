{
  kind: 'SealedSecret',
  apiVersion: 'bitnami.com/v1alpha1',
  metadata: {
    name: 'annotation-exporter-webhook-auth',
    namespace: 'dev-aswin',
    creationTimestamp: null,
  },
  spec: {
    template: {
      metadata: {
        name: 'annotation-exporter-webhook-auth',
        namespace: 'dev-aswin',
        creationTimestamp: null,
      },
    },
    encryptedData: {
      'credentials.json': 'AgCKRCyfKaTjexbjmqEdrsu0KwHqFTGvOCazLa2Ec4EikEPe94941U0bzik/ECr0orhZM7BMSUS1fWMf5gWBjyCSZAHDFJEEQbMWgADohdv8QLqOg6KdGpSeUcCv2Bg53he4vEMxejNwYC42TFhqTCmkrtS/lyTLKP2J2Dyj26kQaXQNjLr8k4S5w/A2Ggxh91wqyu+yxiBWJE8aYst1O2USSvzSYBWKAtiFT0I9b+2MbT+7oW5fHOsyPOeJxOKlclD0giPpxf/KIsg7r6XVWqO26MifqqtX1NunHEaXn1dnLFG3MwfqcyC0duThmY+NyA6R7cPgyAIkPFk0tXqrucVUV98MDPnlCSBclvlFolXEKX876Ff7+cZm2jKgjgddQqIuKOO16hYFpFL6Y6U3tuF4B4RMdT7dF4w/XevRqdlLW2qvVCWM5zNroN9PTqxPiuHMwMutf4xioBt9wQ6k2rRPIDG8y4rz0KKDkQ0GoH9379rP86k+IYpXL55GKZA2CIkdVWXDdb9XyyCpDfrR4qBMFRwBkGeedto8LDuRZNIC1sRxL04rVDzhFXVM9TMMUZO/IP70io9JSMhBtNiV83S6wim57jTlkugNh22lr7m1usDa7sio8y17/HnfrtKxGmf6uOA7Kky4bvigb6Yna5rGmK/gAS9qSotZD5yax0eZew/3/rfztWkA6hiqbBwgJtCl3A==',
    },
  },
}

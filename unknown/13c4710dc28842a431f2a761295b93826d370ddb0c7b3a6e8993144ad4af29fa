## Dealing with tiktoken errors

When you see this kind of error related to tiktoken:
```
        from base.tokenizers.core_bpe import CoreBPE
    base/tokenizers/core_bpe.py:5: in <module>
        from base.tokenizers.tiktoken import CoreBPE as _CoreBPE
    E   ModuleNotFoundError: No module named 'base.tokenizers.tiktoken'
```

It means tiktoken was not installed. Try running this command to fix it:
```
bazel run //base:install
```

<script lang="ts">
  import type { ConfigBlock } from "../models/settings-model";
  import ConfigCompact from "../components/ConfigCompact.svelte";
  import SettingsCategory from "./SettingsCategory.svelte";

  export let title: string;
  export let tools: ConfigBlock[] = [];
  export let onAuthenticate: (url: string) => void;
  export let onRevokeAccess: (config: ConfigBlock) => void;
</script>

<SettingsCategory {title} loading={tools.length === 0}>
  <div class="tool-category-list">
    {#each tools as config}
      <ConfigCompact {config} {onAuthenticate} {onRevokeAccess} />
    {/each}
  </div>
</SettingsCategory>

<style>
  .tool-category-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-2) 0;
  }
</style>

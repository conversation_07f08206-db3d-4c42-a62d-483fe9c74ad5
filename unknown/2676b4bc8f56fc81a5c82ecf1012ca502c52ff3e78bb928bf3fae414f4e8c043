package com.augmentcode.intellij.actions

import com.augmentcode.api.SmartPasteResolution
import com.augmentcode.intellij.AugmentBundle
import com.intellij.icons.AllIcons
import com.intellij.openapi.vfs.VirtualFile

/**
 * An action that applies all changes from the right side of the diff viewer to the left side.
 * We add this to the Smart Paste diff viewer toolbar.
 */
class ApplyAllChangesAction(
  replacementText: String,
  targetFile: VirtualFile,
  resolutionContainer: SmartPasteResolution,
) : BaseDiffAction(
    AugmentBundle.message("actions.accept.all"),
    AugmentBundle.message("actions.accept.all.description"),
    AllIcons.General.InspectionsOK,
    targetFile,
    replacementText,
    resolutionContainer,
  )

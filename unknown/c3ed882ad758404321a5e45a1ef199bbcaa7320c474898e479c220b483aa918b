#!/bin/bash

TIMEOUT=30s
# CI pods - 12 hrs
now=$(date -u +%s)
maxage=$(( 60 * 60 * 12 ))
while IFS= read -r line; do
  ts=$(date +%s -d $(echo $line | awk '{print $NF}'));
  dt=$(echo $line | awk '{print $NF}');
  pod=$(echo $line | awk '{print $1}');
  age=$(( $now - $ts ))
  if [[ $age -gt $maxage ]]; then
    echo "deleting pod $pod $dt ($age > $maxage)"
    timeout $TIMEOUT kubectl delete pod $pod
  fi
done < <(kubectl get pods --no-headers -o custom-columns='NAME:.metadata.name,AGE:.metadata.creationTimestamp' -l GITHUB_PR)

# Hydra pods - 1 hour
maxage=$(( 60 * 60 * 1 ))
while IFS= read -r line; do
  ts=$(date +%s -d $(echo $line | awk '{print $NF}'));
  dt=$(echo $line | awk '{print $NF}');
  pod=$(echo $line | awk '{print $1}');
  age=$(( $now - $ts ))
  if [[ $age -gt $maxage ]]; then
    echo "deleting pod $pod $dt ($age > $maxage)"
    timeout $TIMEOUT kubectl delete pod $pod
  fi
done < <(kubectl get pods --no-headers -o custom-columns='NAME:.metadata.name,AGE:.metadata.creationTimestamp' -l hydra-block-resource-internet-access --field-selector 'status.phase=Succeeded')

while IFS= read -r line; do
  ts=$(date +%s -d $(echo $line | awk '{print $NF}'));
  dt=$(echo $line | awk '{print $NF}');
  pod=$(echo $line | awk '{print $1}');
  age=$(( $now - $ts ))
  if [[ $age -gt $maxage ]]; then
    echo "deleting pod $pod $dt ($age > $maxage)"
    timeout $TIMEOUT kubectl delete pod $pod
  fi
done < <(kubectl get pods --no-headers -o custom-columns='NAME:.metadata.name,AGE:.metadata.creationTimestamp' -l hydra-block-resource-internet-access --field-selector 'status.phase=Failed')

# hydra configmaps - 24 hours
maxage=$(( 60 * 60 * 24 * 1 ))
while IFS= read -r line; do
  ts=$(date +%s -d $(echo $line | awk '{print $NF}'));
  dt=$(echo $line | awk '{print $NF}');
  map=$(echo $line | awk '{print $1}');
  age=$(( $now - $ts ))
  if [[ $age -gt $maxage ]]; then
    echo "deleting configmap $map $dt ($age > $maxage)"
    timeout $TIMEOUT kubectl delete configmap $map
  fi
done < <(kubectl get configmaps --no-headers -o custom-columns='NAME:.metadata.name,AGE:.metadata.creationTimestamp' | grep hydra)

# Other pods - 2 days
maxage=$(( 60 * 60 * 24 * 2 ))
while IFS= read -r line; do
  ts=$(date +%s -d $(echo $line | awk '{print $NF}'));
  dt=$(echo $line | awk '{print $NF}');
  pod=$(echo $line | awk '{print $1}');
  age=$(( $now - $ts ))
  if [[ $age -gt $maxage ]]; then
    echo "deleting pod $pod $dt ($age > $maxage)"
    timeout $TIMEOUT kubectl delete pod $pod
  fi
done < <(kubectl get pods --no-headers -o custom-columns='NAME:.metadata.name,AGE:.metadata.creationTimestamp' --field-selector 'status.phase=Succeeded')

while IFS= read -r line; do
  ts=$(date +%s -d $(echo $line | awk '{print $NF}'));
  dt=$(echo $line | awk '{print $NF}');
  pod=$(echo $line | awk '{print $1}');
  age=$(( $now - $ts ))
  if [[ $age -gt $maxage ]]; then
    echo "deleting pod $pod $dt ($age > $maxage)"
    timeout $TIMEOUT kubectl delete pod $pod
  fi
done < <(kubectl get pods --no-headers -o custom-columns='NAME:.metadata.name,AGE:.metadata.creationTimestamp' --field-selector 'status.phase=Failed')

# Spark configmaps - 10 days
maxage=$(( 60 * 60 * 24 * 10 ))
while IFS= read -r line; do
  ts=$(date +%s -d $(echo $line | awk '{print $NF}'));
  dt=$(echo $line | awk '{print $NF}');
  configmap=$(echo $line | awk '{print $1}');
  age=$(( $now - $ts ))
  if [[ $age -gt $maxage ]]; then
    echo "deleting configmap $configmap $dt ($age > $maxage)"
    timeout $TIMEOUT kubectl delete configmap $configmap
  fi
done < <(kubectl get configmaps --no-headers -o custom-columns='NAME:.metadata.name,AGE:.metadata.creationTimestamp' | grep spark-exec-)

# Testmon data files
for datafile in $(find /mnt/efs/augment/jobs/testmon/ -type f -mtime +30); do
  ls -l $datafile
  echo "Removing $datafile"
  rm -f $datafile
done

# empty testmon data dirs
for datadir in $(find /mnt/efs/augment/jobs/testmon/ -type d -mtime +5 -empty); do
  ls -dl $datadir
  echo "Removing $datadir"
  rmdir $datadir
done

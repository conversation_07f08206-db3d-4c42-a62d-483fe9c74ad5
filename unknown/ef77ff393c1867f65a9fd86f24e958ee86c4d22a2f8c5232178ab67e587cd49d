import { parse } from "../git-diff-parser";

describe("git-diff-parser", () => {
    describe("parse", () => {
        it("should parse git diff with Added, Deleted, Modified, and Renamed", async () => {
            const text = [
                ["M", "foo.js"],
                ["A", "foo bar.txt"],
                ["D", "src/old.js"],
                ["R100", "src/main.js", "src/index.js"],
            ]
                .map((line) => line.join("\t"))
                .join("\n");
            const result = parse(text);
            expect(result).toMatchSnapshot();
        });

        it("should handle files with a space", async () => {
            const text = [["A", "foo bar.txt"]].map((line) => line.join("\t")).join("\n");
            const result = parse(text);
            expect(result[0].afterPath).toEqual("foo bar.txt");
        });

        it("should handle an empty line property", async () => {
            const text = [["M", "foo.js"], [""]].map((line) => line.join("\t")).join("\n");
            const result = parse(text);
            expect(result.length).toEqual(1);
        });
    });
});

syntax = "proto3";

package com.augmentcode.common.webviews.protos;

import "clients/common/webviews/protos/webview_message_def.proto";
import "clients/sidecar/node-process/protos/chat.proto";

// This package contains proto version of chat WebView messages defined in webview-messages.ts.
// Ultimately we'll move everything to proto buffers.

// Add these options to generate Java classes
option java_multiple_files = true;
option java_outer_classname = "CommonWebViewChatTypes";
option java_package = "com.augmentcode.common.webviews.protos";

message SaveChatRequest {
  option (webview_message_type) = "save-chat";
  SaveChatRequestData data = 1;
}

message SaveChatRequestData {
  string conversationId = 1;
  repeated com.augmentcode.sidecar.rpc.chat.ChatHistoryItem chatHistory = 2;
  string title = 3;
}

import json
import time

from thefuzz import fuzz
from tqdm import tqdm

from base.prompt_format_chat.lib.token_counter_claude import Claude<PERSON><PERSON>Counter
from base.prompt_format_chat.prompt_formatter import (
    Chat<PERSON>romptFormatter,
    ChatPromptInput,
    ChatTokenApportionment,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredStraightThroughPromptFormatter,
)
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from experimental.zhuoran.commit_msgs.git_history import GitHistory
from research.core.chat_prompt_input import ResearchChatPromptInput

SYSTEM_PROMPT = """You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:
- Strictly synthesizes meaningful information from the provided code diff
- Utilizes any additional user-provided context to comprehend the rationale behind the code changes
- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions
- Avoids unnecessary phrases such as "this commit", "this change", and the like
- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes
- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.
- Most importantly emphasizes the 'why' of the change, its benefits, or the problem it addresses rather than only the 'what' that changed
Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up!"""
REGION = "us-east5"
PROJECT_ID = "augment-387916"
MODEL_NAME = "claude-3-5-sonnet@20240620"
TEMPERAURE = 0
MAX_OUTPUT_TOKENS = 1024 * 8

ANTHROPIC_CLIENT = AnthropicVertexAiClient(
    project_id=PROJECT_ID,
    region=REGION,
    model_name=MODEL_NAME,
    temperature=TEMPERAURE,
    max_output_tokens=MAX_OUTPUT_TOKENS,
)


def run_claude(
    chat_prompt_input: ChatPromptInput,
    prompt_formatter: ChatPromptFormatter,
    tools=None,
):
    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
    messages = []
    for exchange in prompt_output.chat_history:
        messages.extend(
            [
                {"role": "user", "content": exchange.request_message},
                {"role": "assistant", "content": exchange.response_text},
            ]
        )
    messages.append({"role": "user", "content": prompt_output.message})
    if tools is None:
        response = ANTHROPIC_CLIENT.client.messages.create(
            model=MODEL_NAME,
            max_tokens=MAX_OUTPUT_TOKENS,
            messages=messages,
            system=prompt_output.system_prompt,
            temperature=TEMPERAURE,
        )
    else:
        response = ANTHROPIC_CLIENT.client.messages.create(
            model=MODEL_NAME,
            max_tokens=MAX_OUTPUT_TOKENS,
            messages=messages,
            system=prompt_output.system_prompt,
            temperature=TEMPERAURE,
            tools=tools,
        )
    return response.content[0].text, response, prompt_output


def make_message_only_input(message):
    return ResearchChatPromptInput(
        path="",
        prefix="",
        selected_code="",
        suffix="",
        message=message,
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
        doc_ids=[],
        user_guided_blobs=[],
        context_code_exchange_request_id=None,
    )


git_history = GitHistory.from_json(
    "/home/<USER>/zhuoran/commit_msgs/1000_commits.json"
)
token_apportionment = ChatTokenApportionment(
    prefix_len=1024 * 2,
    suffix_len=1024 * 2,
    path_len=256,
    message_len=-1,  # Deprecated field
    selected_code_len=-1,  # Deprecated field
    chat_history_len=1024 * 4,
    retrieval_len_per_each_user_guided_file=0,  # 2000,
    retrieval_len_for_user_guided=0,  # 3000,
    retrieval_len=0,  # -1,  # Fill the rest of the input prompt with retrievals
    max_prompt_len=1024 * 12,  # 12k for prompt
)

token_counter = ClaudeTokenCounter()
# Initialize variables
scores = []
top_overlaps = []  # Will store tuples of (score, commit_id)
straight_through_prompt_formatter = StructuredStraightThroughPromptFormatter.create(
    token_counter=token_counter,
    system_prompt_factory=SYSTEM_PROMPT,
    token_apportionment=token_apportionment,
)
# Process every 10th commit
results = []
for i in tqdm(range(0, git_history.get_length(), 10)):
    # Get the prompt for the current commit
    prompt = git_history.get_prompt(i)
    no_diff_prompt = git_history.get_prompt(i, diff=False)

    # Call Claude API
    input_ = make_message_only_input(prompt)
    claude_response = run_claude(input_, straight_through_prompt_formatter)

    # Calculate fuzzy match score
    score = fuzz.ratio(no_diff_prompt, claude_response[0])
    scores.append(score)

    # Create a result dictionary
    result = {
        "commit_id": i,
        "score": score,
    }

    results.append(result)

    # Write the result to the JSON file
    with open("/mnt/efs/augment/user/zhuoran/commit_msgs/overlaps.json", "w") as f:
        json.dump(results, f, indent=4)

    time.sleep(5)

import { Button, Form, Input } from "antd";
import { LayoutComponent } from "../lib/layout";
import { useNavigate } from "react-router-dom";

type DeploymentIdSearchForm = {
  deployId: string;
};

function DeploymentIdSearchComponent() {
  const [runIdForm] = Form.useForm();
  const navigate = useNavigate();

  const onRunIdFormFinish = (values: DeploymentIdSearchForm) => {
    console.log("Success:", values);

    navigate(`/deployment/${values.deployId}`);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <Form
      name="deploy_id"
      form={runIdForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ maxWidth: 600 }}
      initialValues={{ remember: true }}
      onFinish={onRunIdFormFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
    >
      <Form.Item
        label="Deploy Id"
        name="deployId"
        rules={[{ required: true, message: "Please input the Deploy Id" }]}
      >
        <Input />
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit">
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
}

function DeploymentsSearchPageComponent() {
  const items = <DeploymentIdSearchComponent />;

  return (
    <LayoutComponent
      children={items}
      selectedMenuKey={"deployments"}
      breadcrumbs={[
        { label: "Search Deployments", link: "/deployment/search" },
      ]}
    />
  );
}

export default DeploymentsSearchPageComponent;

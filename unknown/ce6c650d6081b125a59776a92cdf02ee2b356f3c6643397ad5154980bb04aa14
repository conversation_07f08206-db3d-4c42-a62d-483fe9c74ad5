/* eslint-disable @typescript-eslint/naming-convention */
import AutofixDetailsLog from "$common-webviews/src/apps/autofix/components/AutofixDetailsLog.svelte";
import {
  AutofixIterationStage,
  type IConversationAutofixExtraData,
} from "$vscode/src/autofix/autofix-state";
import type { Meta, StoryObj } from "@storybook/svelte";
const meta = {
  title: "app/Autofix/components/AutofixDetailsLog",
  component: AutofixDetailsLog,
  tags: ["autodocs"],
  argTypes: {},
} satisfies Meta<AutofixDetailsLog>;

export default meta;

const iterationId: string = "mock-iteration-id";
const autofixData: IConversationAutofixExtraData = {
  isAutofix: true,
  autofixIterations: [
    {
      id: iterationId,
      isFirstIteration: true,
      currentStage: AutofixIterationStage.runTest,
      commandFailed: false,
      commandOutput: "Test error\nTest line2",
    },
  ],
};

type Story = StoryObj<typeof meta>;

export const Normal: Story = {
  args: {
    iterationId,
    autofixData,
  },
};

import copy
import dataclasses
from textwrap import dedent
from typing import Optional

from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    TokenizedChatPromptFormatter,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.tokenized_llama_prompt_formatter import (
    StructToTokensLlama3PromptFormatter,
)
from base.prompt_format_chat.tokenized_string_formatter import TokenizedStringFormatter
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer

BUFFER_SIZE_FOR_SPEC_TOKENS = 100


class TokenizedLlama3BinksPromptFormatter(TokenizedChatPromptFormatter):
    """The class formats tokenized prompts for the Binks Llama3-based chat model.

    For prompt structure of the Llama3 models, see:
    https://llama.meta.com/docs/model-cards-and-prompt-formats/meta-llama-3

    Under the hood, it uses base/prompt_format_chat/structured_binks_prompt_formatter.py
    to format the prompt, and then tokenizes it, adding special token delimiters where needed.

    Here is how we tokenize the prompt:

    <|begin_of_text|><|start_header_id|>system<|end_header_id|>

    [insert system prompt]<|eot_id|>\
    [for conversation turn:]
        <|start_header_id|>user<|end_header_id|>

        [insert user message]<|eot_id|>\
        <|start_header_id|>assistant<|end_header_id|>

        [insert assistant message]<|eot_id|>\
    <|start_header_id|>user<|end_header_id|>

    [insert user message]<|eot_id|>\
    <|start_header_id|>assistant<|end_header_id|>

    """

    def __init__(
        self,
        tokenizer: Llama3InstructTokenizer,
        token_apportionment: Optional[ChatTokenApportionment] = None,
    ):
        self.tokenizer = tokenizer
        self.token_counter = TokenizerBasedTokenCounter(tokenizer)
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = ChatTokenApportionment(
                path_len=256,
                prefix_len=1024,
                chat_history_len=2048,
                suffix_len=1024,
                retrieval_len=-1,  # unlimited retrieval
                max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
                # Fields unused by default
                retrieval_len_per_each_user_guided_file=0,
                retrieval_len_for_user_guided=0,
                recent_changes_len=0,
                # Deprecated fields
                message_len=-1,
                selected_code_len=-1,
            )

        assert (
            token_apportionment.message_len == -1
        ), "The message length should be -1 because it is a deprecate field not used in this formatter."
        assert (
            token_apportionment.selected_code_len == -1
        ), "The selected code length should be -1 because it is a deprecate field not used in this formatter."

        self.tokenized_prompt_formatter = StructToTokensLlama3PromptFormatter(tokenizer)
        # Add a buffer of BUFFER_SIZE_FOR_SPEC_TOKENS tokens for spec
        # tokens. It's okay if we go a little over it.
        structured_token_apportionment = copy.deepcopy(token_apportionment)
        structured_token_apportionment = dataclasses.replace(
            structured_token_apportionment,
            max_prompt_len=token_apportionment.max_prompt_len
            - self.tokenized_prompt_formatter.reserved_token_budget,
        )
        # Required for the interface even if we don't use it.
        self.token_apportionment = token_apportionment

        self.structured_prompt_formatter = StructuredBinksPromptFormatter.create(
            self.token_counter, token_apportionment
        )

    def format_prompt(self, prompt_input: ChatPromptInput) -> TokenizedChatPromptOutput:
        """Format prompt for Binks Llama3-based code chat model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """
        structured_prompt = self.structured_prompt_formatter.format_prompt(prompt_input)
        return self.tokenized_prompt_formatter.format_prompt(structured_prompt)

package com.augmentcode.intellij.webviews

import com.intellij.ide.ui.UISettings
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.awt.Color
import java.util.Locale

@RunWith(JUnit4::class)
class StylesManagerTest : BasePlatformTestCase() {
  companion object {
    private val defaultLocale: Locale = Locale.getDefault()
  }

  @After
  fun after() {
    Locale.setDefault(defaultLocale)
    unmockkAll()
  }

  @Test
  fun testColorWebRgba() {
    Locale.setDefault(defaultLocale)
    val color: Color = Color(255, 0, 0, 128)
    assertEquals("rgba(255, 0, 0, 0.50)", color.webRgba())
  }

  @Test
  fun testColorWebRgbaSweden() {
    Locale.setDefault(Locale.forLanguageTag("sv-SE"))
    val color: Color = Color(255, 0, 0, 128)
    assertEquals("rgba(255, 0, 0, 0.50)", color.webRgba())
  }

  @Test
  fun testColorWebRgbaUsa() {
    Locale.setDefault(Locale.forLanguageTag("en-US"))
    val color: Color = Color(255, 0, 0, 128)
    assertEquals("rgba(255, 0, 0, 0.50)", color.webRgba())
  }

  @Test
  fun testColorWebRgbaIran() {
    Locale.setDefault(Locale.forLanguageTag("fa-IR"))
    val color: Color = Color(255, 0, 0, 128)
    assertEquals("rgba(255, 0, 0, 0.50)", color.webRgba())
  }

  @Test
  fun testColorWebRgbaSaudiArabia() {
    Locale.setDefault(Locale.forLanguageTag("ar-SA"))
    val color: Color = Color(255, 0, 0, 128)
    assertEquals("rgba(255, 0, 0, 0.50)", color.webRgba())
  }

  @Test
  fun testEnsureFontSizeIsSet() {
    // We use the UISettings to get the font size since it's the
    // original font-size in the settings that the webview can use.
    val mockUISettings = mockk<UISettings>()
    every { mockUISettings.fontSize } returns 13
    every { mockUISettings.ideScale } returns 1.0F

    val css = StylesManager.getCssFromUIDefaults(mockUISettings)
    assertTrue(css.contains("--intellij-ui-font-size: 13px;"))
  }
}

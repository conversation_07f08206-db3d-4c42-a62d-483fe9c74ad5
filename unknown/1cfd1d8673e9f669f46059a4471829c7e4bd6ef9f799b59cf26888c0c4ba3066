import axios, { AxiosError } from "axios";

export type Condition = {
  lastTransitionTime: string;
  lastUpdateTime: string;
  status: string;
  type: string;
  reason: string;
  message: string;
};

export type KubernetesData = {
  name: string;
  namespace: string;
  cloud: string;
  deployment_target?: string;
  deployed_by?: string;
  version?: string;
  app?: string;
  condition?: Condition;
};

export type Namespace = {
  name: string;
  cloud: string;
};

export async function getNamespaces(): Promise<Namespace[]> {
  const { data: response }: { data: Namespace[] } = await axios.get(
    `/api/kubernetes/namespaces`,
  );
  return response;
}

export async function getClouds(): Promise<string[]> {
  const { data: response }: { data: string[] } = await axios.get(
    `/api/kubernetes/clouds`,
  );
  return response;
}

export async function getKubernetesState(
  cloud: string,
  continue_token: string | null,
): Promise<{ deployments: KubernetesData[]; continue_token?: string }> {
  const { data: response }: { data: any } = await axios.get(
    `/api/kubernetes/state`,
    {
      params: {
        cloud: cloud,
        continue_token: continue_token,
      },
    },
  );
  return response;
}

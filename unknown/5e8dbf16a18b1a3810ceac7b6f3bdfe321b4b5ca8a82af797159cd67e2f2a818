<script lang="ts">
  import { getContext } from "svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";

  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
</script>

<div class="c-agent-auto-mode-warning">
  <CalloutAugment color="warning" variant="soft" size={1}>
    <ExclamationTriangle slot="icon" />
    <div class="c-agent-auto-mode-warning__content">
      <TextAugment size={2}>
        Agent Auto mode allows Augment to run commands automatically on your behalf without your
        explicit approval. Changes can be destructive -- proceed with caution.
      </TextAugment>
      <div class="c-agent-auto-mode-warning__buttons">
        <ButtonAugment
          size={1}
          variant="solid"
          color="accent"
          on:click={() => agentConversationModel.acceptAutoMode()}
          class="c-agent-auto-mode-warning__button"
        >
          Accept
        </ButtonAugment>
        <ButtonAugment
          size={1}
          variant="solid"
          color="neutral"
          on:click={() => agentConversationModel.rejectAutoMode()}
          class="c-agent-auto-mode-warning__button"
        >
          Back to Manual Mode
        </ButtonAugment>
      </div>
    </div>
  </CalloutAugment>
</div>

<style>
  .c-agent-auto-mode-warning {
    & .c-callout-body {
      width: 100%;
    }

    & .c-agent-auto-mode-warning__content {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: var(--ds-spacing-2);
    }

    & .c-agent-auto-mode-warning__buttons {
      display: flex;
      gap: var(--ds-spacing-1);
    }

    & .c-agent-auto-mode-warning__button {
      flex: 1;
    }
  }
</style>

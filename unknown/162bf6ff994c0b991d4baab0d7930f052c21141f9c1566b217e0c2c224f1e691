"""Configuration for the Ethanol6 version 16 retriever."""

config = {
    "scorer": {
        "name": "dense_scorer_v2_ffwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/ethanol/smart/stareth_smart_128doc_2000s/global_step2000",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    "query_formatter": {
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
}

load("//tools/bzl:go.bzl", "go_binary", "go_library")

go_library(
    name = "kubeseal_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/k8s/kubeseal",
    visibility = ["//visibility:private"],
    deps = [
        "//base/cloud/k8s:k8s_go",
        "//base/logging:logging_go",
        "@com_github_rs_zerolog//log",
    ],
    data = [
        "@com_github_bitnami_labs_sealed_secrets//cmd/kubeseal:kubeseal"
    ]
)

go_binary(
    name = "kubeseal",
    embed = [":kubeseal_lib"],
    visibility = ["//visibility:public"],
)

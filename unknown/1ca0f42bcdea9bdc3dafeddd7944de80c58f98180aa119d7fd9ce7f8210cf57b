import { expect, describe, test, beforeEach, afterEach, vi } from "vitest";
import { MentionableMenuContext } from "./mentionable-menu";
import { get } from "svelte/store";
import { type SuggestionProps } from "@tiptap/suggestion";

type TestMentionable = { id: string; label: string; name: string };
let mentionableMenuContext: MentionableMenuContext<TestMentionable>;
function getFakeMentionables(query: string): TestMentionable[] {
  return [
    { id: "1", label: `${query} 1 - label`, name: `${query} 1 - name` },
    { id: "2", label: `${query} 2 - label`, name: `${query} 2 - name` },
    { id: "3", label: `${query} 3 - label`, name: `${query} 3 - name` },
  ];
}

beforeEach(() => {
  vi.useFakeTimers();
  mentionableMenuContext = new MentionableMenuContext();
});

afterEach(() => {
  vi.useRealTimers();
  vi.clearAllMocks();
});

const mockClientRect = { top: 0, left: 0, width: 100, height: 50 } as DOMRect;
const mockTipTapNotify = (props: SuggestionProps<TestMentionable>) => {
  mentionableMenuContext.onUpdateSuggestion(props);
};

describe("MentionableMenuContext", () => {
  test("initial state is correct", () => {
    expect(get(mentionableMenuContext.query)).toBeUndefined();
    expect(get(mentionableMenuContext.activeIdx)).toBeUndefined();
    expect(get(mentionableMenuContext.referenceClientRect)).toBeUndefined();
    expect(get(mentionableMenuContext.tiptapExtensionProps)).toBeUndefined();
    expect(get(mentionableMenuContext.isMenuActive)).toBe(false);
  });

  test("onUpdateMentionable updates downstream state", () => {
    const mockProps = {
      query: "test",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>;

    mockTipTapNotify(mockProps);
    mentionableMenuContext.updateMentionables(getFakeMentionables("test"));

    // Check if the downstream state has been updated correctly
    expect(get(mentionableMenuContext.query)).toBe("test");
    expect(get(mentionableMenuContext.activeIdx)).toBe(0);
    expect(get(mentionableMenuContext.referenceClientRect)).toBe(mockClientRect);
    expect(get(mentionableMenuContext.tiptapExtensionProps)).toBe(mockProps);
    expect(get(mentionableMenuContext.isMenuActive)).toBe(true);
  });

  test("onExitMentionable clears downstream state", () => {
    // Set some initial state
    const mockProps = {
      query: "test",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>;
    mockTipTapNotify(mockProps);

    // Expect state to be set
    expect(get(mentionableMenuContext.query)).toBe("test");
    expect(get(mentionableMenuContext.referenceClientRect)).toBe(mockClientRect);
    expect(get(mentionableMenuContext.tiptapExtensionProps)).toBe(mockProps);
    expect(get(mentionableMenuContext.isMenuActive)).toBe(true);

    // Call the onExitMentionable method
    mentionableMenuContext.exitMenu();

    // Check if the downstream state has been cleared
    expect(get(mentionableMenuContext.query)).toBeUndefined();
    expect(get(mentionableMenuContext.activeIdx)).toBeUndefined();
    expect(get(mentionableMenuContext.referenceClientRect)).toBeUndefined();
    expect(get(mentionableMenuContext.tiptapExtensionProps)).toBeUndefined();
    expect(get(mentionableMenuContext.isMenuActive)).toBe(false);
  });

  test("active index methods do nothing if uninitialized", () => {
    mentionableMenuContext["_incrementActiveIdx"]();
    expect(get(mentionableMenuContext.activeIdx)).toBeUndefined();
    mentionableMenuContext["_decrementActiveIdx"]();
    expect(get(mentionableMenuContext.activeIdx)).toBeUndefined();
    mentionableMenuContext["_setActiveIdx"](5);
    expect(get(mentionableMenuContext.activeIdx)).toBeUndefined();
  });

  test("active index methods work correctly if initialized", () => {
    mockTipTapNotify({
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mentionableMenuContext.updateMentionables(getFakeMentionables("test"));

    // Test increment and decrement
    mentionableMenuContext["_incrementActiveIdx"]();
    expect(get(mentionableMenuContext.activeIdx)).toBe(1);
    mentionableMenuContext["_decrementActiveIdx"]();
    expect(get(mentionableMenuContext.activeIdx)).toBe(0);

    // Test setActiveIdx
    mentionableMenuContext["_setActiveIdx"](5);
    // 5 % 3 = 2
    expect(get(mentionableMenuContext.activeIdx)).toBe(2);
  });

  test("selectItem calls the command function", () => {
    const mockItem = { id: "1", label: "test", name: "test" };
    const mockCommand = vi.fn();
    mockTipTapNotify({
      command: mockCommand,
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mentionableMenuContext.replaceQueryWithMentionNode(mockItem);
    expect(mockCommand).toHaveBeenCalledWith(mockItem);
  });

  test("activeItem is undefined if no mentionables", () => {
    const mockCommand = vi.fn();
    mockTipTapNotify({
      command: mockCommand,
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mentionableMenuContext.updateMentionables([]);
    expect(get(mentionableMenuContext.activeItem)).toBeUndefined();
  });

  test("activeItem is undefined if no activeIdx", () => {
    const mockItem = { id: "1", label: "test", name: "test" };
    mentionableMenuContext.updateMentionables([mockItem]);
    expect(get(mentionableMenuContext.activeItem)).toBeUndefined();
  });

  test("activeItem is correct if activeIdx is set", () => {
    const mockItem = { id: "1", label: "test", name: "test" };
    const mockCommand = vi.fn();
    mockTipTapNotify({
      command: mockCommand,
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mentionableMenuContext.updateMentionables([mockItem]);
    mentionableMenuContext["_setActiveIdx"](0);
    expect(get(mentionableMenuContext.activeItem)).toBe(mockItem);
  });

  test("onArrowUp and onArrowDown set correct activeItem", () => {
    const mockItems: TestMentionable[] = [
      { id: "1", label: "test 1 - label", name: "test 1 - name" },
      { id: "2", label: "test 2 - label", name: "test 2 - name" },
      { id: "3", label: "test 3 - label", name: "test 3 - name" },
    ];
    const mockCommand = vi.fn();
    mockTipTapNotify({
      command: mockCommand,
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mentionableMenuContext.updateMentionables(mockItems);

    mentionableMenuContext["_setActiveIdx"](0);
    // Down 2 times, go from 0 => 2 => 1
    expect(get(mentionableMenuContext.activeItem)).toBe(mockItems[0]);
    mentionableMenuContext.onArrowUp();
    expect(get(mentionableMenuContext.activeItem)).toBe(mockItems[2]);
    mentionableMenuContext.onArrowUp();
    // Up 2 times, go from 1 => 2 => 0
    expect(get(mentionableMenuContext.activeItem)).toBe(mockItems[1]);
    mentionableMenuContext.onArrowDown();
    expect(get(mentionableMenuContext.activeItem)).toBe(mockItems[2]);
    mentionableMenuContext.onArrowDown();
    expect(get(mentionableMenuContext.activeItem)).toBe(mockItems[0]);
  });

  test("updateMentionables maintains activeIdx within bounds when items length changes", () => {
    mockTipTapNotify({
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);

    // Start with 3 items and select last one
    mentionableMenuContext.updateMentionables(getFakeMentionables("test"));
    mentionableMenuContext["_setActiveIdx"](2);
    expect(get(mentionableMenuContext.activeIdx)).toBe(2);

    // Update with fewer items
    mentionableMenuContext.updateMentionables([{ id: "1", label: "test 1", name: "test 1" }]);
    expect(get(mentionableMenuContext.activeIdx)).toBe(0);
  });

  test("replaceQueryWithMentionNode does nothing if no command available", () => {
    const mockItem = { id: "1", label: "test", name: "test" };
    mockTipTapNotify({
      items: [],
      query: "",
      clientRect: () => mockClientRect,
      command: undefined,
    } as unknown as SuggestionProps<TestMentionable>);

    mentionableMenuContext.replaceQueryWithMentionNode(mockItem);
    // Should not throw and should maintain existing state
    expect(get(mentionableMenuContext.isMenuActive)).toBe(true);
  });

  test("replaceQueryWithMentionNode calls command if available", () => {
    const mockItem = { id: "1", label: "test", name: "test" };

    // Mock the editor closing the menu when a command is run
    const mockCommand = vi.fn(mentionableMenuContext.exitMenu);
    mockTipTapNotify({
      command: mockCommand,
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mentionableMenuContext.replaceQueryWithMentionNode(mockItem);
    expect(mockCommand).toHaveBeenCalledWith(mockItem);
    // Should have closed the menu
    expect(get(mentionableMenuContext.isMenuActive)).toBe(false);
  });

  test("concurrent updates handle state correctly", async () => {
    const mockCommand = vi.fn();
    mockTipTapNotify({
      command: mockCommand,
      items: [],
      query: "first",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mockTipTapNotify({
      command: mockCommand,
      items: [],
      query: "second",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);

    // Simulate rapid updates
    mentionableMenuContext.updateMentionables(getFakeMentionables("first"));
    mentionableMenuContext.updateMentionables(getFakeMentionables("second"));

    expect(get(mentionableMenuContext.query)).toBe("second");
    expect(get(mentionableMenuContext.activeIdx)).toBe(0);
  });

  test("empty query still shows menu", () => {
    mockTipTapNotify({
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);
    mentionableMenuContext.updateMentionables(getFakeMentionables(""));

    expect(get(mentionableMenuContext.query)).toBe("");
    expect(get(mentionableMenuContext.isMenuActive)).toBe(true);
  });

  test("handles null clientRect by not showing menu", () => {
    mockTipTapNotify({
      query: "test",
      clientRect: () => null,
    } as unknown as SuggestionProps<TestMentionable>);

    expect(get(mentionableMenuContext.referenceClientRect)).toBeUndefined();
    expect(get(mentionableMenuContext.isMenuActive)).toBe(false);
  });

  test("maintains active index when updating with same length mentionables", () => {
    mockTipTapNotify({
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);

    mentionableMenuContext.updateMentionables(getFakeMentionables("first"));
    mentionableMenuContext["_setActiveIdx"](1);
    expect(get(mentionableMenuContext.activeIdx)).toBe(1);

    // Update with new items but same length
    mentionableMenuContext.updateMentionables(getFakeMentionables("second"));
    expect(get(mentionableMenuContext.activeIdx)).toBe(1);
  });

  test("arrow navigation wraps correctly at edges with single item", () => {
    const singleItem = [{ id: "1", label: "test", name: "test" }];
    mockTipTapNotify({
      items: [],
      query: "",
      clientRect: () => mockClientRect,
    } as unknown as SuggestionProps<TestMentionable>);

    mentionableMenuContext.updateMentionables(singleItem);
    expect(get(mentionableMenuContext.activeIdx)).toBe(0);

    mentionableMenuContext.onArrowDown();
    expect(get(mentionableMenuContext.activeIdx)).toBe(0);

    mentionableMenuContext.onArrowUp();
    expect(get(mentionableMenuContext.activeIdx)).toBe(0);
  });
});

"""Test for LRUCache."""

from __future__ import annotations

import dataclasses
import sys
import threading
import time
import typing
from unittest import mock

import pytest

from base.caching.cache import InsertStats, LookupStats
from base.caching.lru_cache import LRUCache, measure_bytes, measure_count

# NOTE(arun): Did you know that Python uses 32 *bytes* to store the smallest int?
ENTRY_SIZE = measure_bytes("key1", 1)

# An number that is larger than INT_SIZE (~44 bytes).
BIGINT = 2**127


@dataclasses.dataclass
class Stats:
    """Object that tracks stats for testing purposes."""

    insertion_stats: InsertStats | None = None
    lookup_stats: LookupStats | None = None

    def on_insert(self, stats: InsertStats):
        self.insertion_stats = stats

    def on_lookup(self, stats: LookupStats):
        self.lookup_stats = stats

    def reset(self):
        self.insertion_stats = None
        self.lookup_stats = None


CONTENT = {
    "key1": 1,
    "key2": 2,
    "key3": 3,
    # This key is too big to fit in the cache.
    "big": BIGINT,
}


@pytest.fixture
def cache() -> LRUCache[str, int, ...]:
    return LRUCache(
        get_missing_fn=lambda keys: [CONTENT.get(key) for key in keys],
        max_size=2 * ENTRY_SIZE,
        max_elem_size=ENTRY_SIZE,
    )


@pytest.fixture
def cache_with_missing_keys() -> LRUCache[str, int, ...]:
    return LRUCache(
        get_missing_fn=lambda keys: [CONTENT.get(key) for key in keys],
        max_size=2 * ENTRY_SIZE,
        max_elem_size=ENTRY_SIZE,
        cache_missing_keys=True,
    )


def test_get_works(cache: LRUCache[str, int, ...]):
    assert cache.get(["key1"]) == [1]
    assert cache.get(["missing"]) == [None]
    assert cache.get(["key1", "key2", "key3"]) == [
        1,
        2,
        3,
    ]
    assert cache.get(["key1", "missing", "key3"]) == [1, None, 3]
    # Even if we don't store the big key in our cache, we still return it.
    assert cache.get(["key1", "big", "key3"]) == [
        1,
        BIGINT,
        3,
    ]


def test_duplicate_keys(cache: LRUCache[str, int, ...]):
    """Test that the cache correctly handles duplicate keys."""
    assert cache.get(["key1", "key1", "key1"]) == [1, 1, 1]
    assert cache.get(["key1", "key2", "key3"]) == [1, 2, 3]
    assert cache.get(["key2", "key3", "key3", "key1", "key1"]) == [2, 3, 3, 1, 1]
    assert cache.get(["key3", "key2", "key1"]) == [3, 2, 1]


def test_get_calls_manager():
    get_missing = mock.Mock()
    get_missing.side_effect = lambda keys, context: [CONTENT.get(key) for key in keys]

    cache = LRUCache(
        get_missing_fn=get_missing,
        max_size=2 * ENTRY_SIZE,
        max_elem_size=ENTRY_SIZE,
    )

    # 1. Get a key.
    get_missing.reset_mock()
    assert cache.get(["key1"], "context_1") == [1]
    get_missing.assert_called_once_with(["key1"], "context_1")

    # 2. Try to get the key again.
    get_missing.reset_mock()
    assert cache.get(["key1"], "context_2") == [1]
    get_missing.assert_not_called()

    # 3. Get more keys to evict the first key out of the cache.
    get_missing.reset_mock()
    assert cache.get(["key2", "key3"], "context_3") == [2, 3]
    get_missing.assert_called_once_with(["key2", "key3"], "context_3")

    # 4. Get first key: assert that first key is downaloded again.
    get_missing.reset_mock()
    assert cache.get(["key1", "big"], "context_4") == [1, BIGINT]
    get_missing.assert_called_once_with(["key1", "big"], "context_4")


def test_get_calls_listeners(cache: cache.LRUCache[str, int, ...]):
    stats = Stats()
    cache.set_insert_listener(stats.on_insert)
    cache.set_lookup_listener(stats.on_lookup)

    # 1. Get a key.
    stats.reset()
    assert cache.get(["key1"]) == [1]
    assert stats.lookup_stats is not None
    assert stats.lookup_stats.hits_count == 0
    assert stats.lookup_stats.misses_count == 1
    assert stats.insertion_stats is not None
    assert stats.insertion_stats.insertion_count == 1
    assert stats.insertion_stats.skip_count == 0
    assert stats.insertion_stats.eviction_count == 0
    assert stats.insertion_stats.cache_size == 1 * ENTRY_SIZE
    assert stats.insertion_stats.entries == 1

    # 2. Try to get the key again.
    stats.reset()
    assert cache.get(["key1"]) == [1]
    assert stats.lookup_stats is not None
    assert stats.lookup_stats.hits_count == 1
    assert stats.lookup_stats.misses_count == 0
    assert stats.insertion_stats is None

    # 3. Get more keys to fill the cache. Assert that the cache is full.
    stats.reset()
    assert cache.get(["key2", "key3"]) == [2, 3]
    assert stats.lookup_stats is not None
    assert stats.lookup_stats.hits_count == 0
    assert stats.lookup_stats.misses_count == 2
    assert stats.insertion_stats is not None
    assert stats.insertion_stats.insertion_count == 2
    assert stats.insertion_stats.skip_count == 0
    assert stats.insertion_stats.eviction_count == 1
    assert stats.insertion_stats.cache_size == 2 * ENTRY_SIZE
    assert stats.insertion_stats.entries == 2

    # 4. Get a big key: assert that stats show we skipped its insertion.
    stats.reset()
    # We still return the big object, but we skip its insertion.
    assert cache.get(["key2", "big"]) == [2, BIGINT]
    assert stats.lookup_stats is not None
    assert stats.lookup_stats.hits_count == 1
    assert stats.lookup_stats.misses_count == 1
    assert stats.insertion_stats is not None
    assert stats.insertion_stats.insertion_count == 0
    assert stats.insertion_stats.skip_count == 1
    assert stats.insertion_stats.eviction_count == 0
    assert stats.insertion_stats.cache_size == 2 * ENTRY_SIZE
    assert stats.insertion_stats.entries == 2


def test_get_works_with_multithreading():
    # Implementing a custom content manager to test threading.
    def get_missing(
        keys: typing.Iterable[str],
        request_context: str,
    ) -> typing.Iterable[int | None]:
        # This gets Python's thread switching interval.
        switch_s = sys.getswitchinterval()
        time.sleep(switch_s)
        return map(CONTENT.get, keys)

    cache = LRUCache(
        get_missing_fn=get_missing,
        max_size=2 * ENTRY_SIZE,
        max_elem_size=ENTRY_SIZE,
    )

    def run():
        test_get_works(cache)

    threads = [threading.Thread(target=run) for _ in range(32)]
    for thread in threads:
        thread.start()

    for thread in threads:
        thread.join()


def test_lookup_stats_are_valid(cache: LRUCache[str, int, ...]):
    stats = Stats()
    cache.set_lookup_listener(stats.on_lookup)

    stats.reset()
    assert cache.get(["key1", "missing1", "missing2"]) == [1, None, None]
    assert stats.lookup_stats
    assert stats.lookup_stats.hits_count == 0
    assert stats.lookup_stats.misses_count == 3

    stats.reset()
    assert cache.get(["key1", "missing1", "missing2"]) == [1, None, None]
    assert stats.lookup_stats
    assert stats.lookup_stats.hits_count == 1
    assert stats.lookup_stats.misses_count == 2


def test_lookup_stats_when_cache_missing_keys_are_valid(
    cache_with_missing_keys: LRUCache[str, int, ...],
):
    cache = cache_with_missing_keys

    stats = Stats()
    cache.set_lookup_listener(stats.on_lookup)

    stats.reset()
    assert cache.get(["key1", "missing1", "missing2"]) == [1, None, None]
    assert stats.lookup_stats
    assert stats.lookup_stats.hits_count == 0
    assert stats.lookup_stats.misses_count == 3

    stats.reset()
    assert cache.get(["key1", "missing1", "missing2"]) == [1, None, None]
    assert stats.lookup_stats
    # We can only cache 2 keys, so we're unable to cache the 3rd key.
    assert stats.lookup_stats.hits_count == 2
    assert stats.lookup_stats.misses_count == 1


def test_insertion_stats_are_valid(cache: LRUCache[str, int, ...]):
    stats = Stats()
    cache.set_insert_listener(stats.on_insert)

    stats.reset()
    assert cache.get(["key1", "key2", "big"]) == [1, 2, BIGINT]
    assert stats.insertion_stats
    assert stats.insertion_stats.insertion_count == 2
    # We are skipping the big key.
    assert stats.insertion_stats.skip_count == 1
    assert stats.insertion_stats.eviction_count == 0
    assert stats.insertion_stats.cache_size == 2 * ENTRY_SIZE
    assert stats.insertion_stats.entries == 2

    stats.reset()
    assert cache.get(["key3", "key1"]) == [3, 1]
    assert stats.insertion_stats
    # We'll insert key 3.
    assert stats.insertion_stats.insertion_count == 1
    assert stats.insertion_stats.skip_count == 0
    # We'll have to evict key 2.
    assert stats.insertion_stats.eviction_count == 1
    assert stats.insertion_stats.cache_size == 2 * ENTRY_SIZE
    assert stats.insertion_stats.entries == 2


def test_get_works_custom_size():
    cache = LRUCache(
        get_missing_fn=lambda keys: [CONTENT.get(key) for key in keys],
        max_size=3,
        size_fn=measure_count,
    )

    stats = Stats()
    cache.set_insert_listener(stats.on_insert)
    cache.set_lookup_listener(stats.on_lookup)

    # Get some keys.
    stats.reset()
    assert cache.get(["key1", "key2", "big", "key3"]) == [1, 2, BIGINT, 3]
    assert stats.lookup_stats is not None
    assert stats.lookup_stats.hits_count == 0
    assert stats.lookup_stats.misses_count == 4
    assert stats.insertion_stats is not None
    assert stats.insertion_stats.insertion_count == 3
    assert stats.insertion_stats.skip_count == 1
    assert stats.insertion_stats.eviction_count == 0
    assert stats.insertion_stats.cache_size == 3
    assert stats.insertion_stats.entries == 3

    # Get some more keys to see if we evict correctly.
    stats.reset()
    assert cache.get(["key1", "key2", "key3", "big"]) == [1, 2, 3, BIGINT]
    assert stats.lookup_stats is not None
    assert stats.lookup_stats.hits_count == 3
    assert stats.lookup_stats.misses_count == 1
    assert stats.insertion_stats is not None
    assert stats.insertion_stats.insertion_count == 1
    assert stats.insertion_stats.skip_count == 0
    assert stats.insertion_stats.eviction_count == 1
    assert stats.insertion_stats.cache_size == 3
    assert stats.insertion_stats.entries == 3


def test_extra_context():
    """Test that we can use extra params as a context."""
    contexts = []

    def get_missing(keys, context):
        contexts.append(context)
        return [CONTENT.get(key) for key in keys]

    cache = LRUCache[str, int, ...](
        get_missing_fn=get_missing,
        max_size=2 * ENTRY_SIZE,
        max_elem_size=ENTRY_SIZE,
    )

    assert cache.get(["key1"], "context_1") == [1]
    assert "context_1" in contexts

import json
from uuid import UUI<PERSON>
from itertools import islice

import google.protobuf.json_format as json_format
from research.llm_apis.llm_client import (
    LLMClient,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
    LLMMessages,
    AssistantContentBlock,
)
from base.augment_client.client import AugmentClient, Retry
from services.api_proxy import public_api_pb2
from typing import Tuple, Any, cast


def llm_messages_to_chat_dialog(
    messages: LLMMessages,
) -> Tuple[list[public_api_pb2.Exchange], list[public_api_pb2.ChatRequestNode]]:
    user_messages: list[list[public_api_pb2.ChatRequestNode]] = []
    for message_list in islice(messages, 0, None, 2):
        request_nodes: list[public_api_pb2.ChatRequestNode] = []
        for message in message_list:
            if str(type(message)) == str(TextPrompt):
                message = cast(TextPrompt, message)
                request_nodes.append(
                    public_api_pb2.ChatRequestNode(
                        type=public_api_pb2.ChatRequestNodeType.TEXT,
                        text_node=public_api_pb2.ChatRequestText(content=message.text),
                    )
                )
            elif str(type(message)) == str(ToolFormattedResult):
                message = cast(ToolFormattedResult, message)
                request_nodes.append(
                    public_api_pb2.ChatRequestNode(
                        type=public_api_pb2.ChatRequestNodeType.TOOL_RESULT,
                        tool_result_node=public_api_pb2.ChatRequestToolResult(
                            tool_use_id=message.tool_call_id,
                            content=message.tool_output,
                            is_error=False,
                            request_id=None,
                        ),
                    )
                )
            else:
                raise ValueError(f"Invalid user message type: {type(message)}")
        user_messages.append(request_nodes)

    assistant_messages: list[list[public_api_pb2.ChatResultNode]] = []
    for message_list in islice(messages, 1, None, 2):
        result_nodes: list[public_api_pb2.ChatResultNode] = []
        for message in message_list:
            if str(type(message)) == str(TextResult):
                message = cast(TextResult, message)
                result_nodes.append(
                    public_api_pb2.ChatResultNode(
                        type=public_api_pb2.ChatResultNodeType.RAW_RESPONSE,
                        content=message.text,
                    )
                )
            elif str(type(message)) == str(ToolCall):
                message = cast(ToolCall, message)
                result_nodes.append(
                    public_api_pb2.ChatResultNode(
                        type=public_api_pb2.ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=public_api_pb2.ChatResultToolUse(
                            tool_use_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            input_json=json.dumps(message.tool_input),
                        ),
                    )
                )
            else:
                raise ValueError(f"Invalid assistant message type: {type(message)}")
        assistant_messages.append(result_nodes)

    exchanges = [
        public_api_pb2.Exchange(request_nodes=user, response_nodes=assistant)
        for user, assistant in zip(user_messages, assistant_messages)
    ]
    current_message = user_messages[-1] if len(messages) % 2 == 1 else []
    return exchanges, current_message


class AugmentLLMClient(LLMClient):
    """Route LLM requests through Augment's backend. Available model(s) depend
    upon what is deployed on the backend.

    Currently relies on grpc-debug/prototyping client. Once plumbed through
    api-proxy, replace with a public API client.
    """

    def __init__(
        self,
        augment_client: AugmentClient,
        max_retries=2,
    ):
        self.client = augment_client
        self.retry = (
            Retry(retry_count=max_retries, retry_sleep=5) if max_retries > 0 else None
        )

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.
            thinking_tokens: The number of tokens to generate before stopping (unused)

        Returns:
            A generated response.
        """

        assert (
            len(messages) % 2 == 1
        ), "Dialog must end with user message for LLM generation call"
        exchanges, current_message = llm_messages_to_chat_dialog(messages)

        choice: public_api_pb2.ToolChoice | None = None
        if tool_choice is not None:
            if tool_choice["type"] == "any":
                choice = public_api_pb2.ToolChoice(
                    type=public_api_pb2.ToolChoiceType.ANY
                )
            elif tool_choice["type"] == "auto":
                choice = public_api_pb2.ToolChoice(
                    type=public_api_pb2.ToolChoiceType.AUTO
                )
            elif tool_choice["type"] == "tool":
                choice = public_api_pb2.ToolChoice(
                    type=public_api_pb2.ToolChoiceType.TOOL, name=tool_choice["name"]
                )
            else:
                raise ValueError(f"Unknown tool_choice type: {tool_choice['type']}")

        tool_defs: list[public_api_pb2.ToolDefinition] = []
        for tool in tools:
            tool_defs.append(
                public_api_pb2.ToolDefinition(
                    name=tool.name,
                    description=tool.description,
                    input_schema_json=json.dumps(tool.input_schema),
                )
            )

        request_message = public_api_pb2.LLMGenerateRequest(
            model_name="",  # whatever is deployed
            user_message=current_message,
            dialog=exchanges,
            max_tokens=max_tokens,
            system_prompt=system_prompt or "",
            temperature=temperature,
            tool_definitions=tool_defs,
            tool_choice=choice,
        )
        response, request_id = self.client.post_proto(
            "/agents/llm-generate",
            request_message,
            public_api_pb2.LLMGenerateResponse(),
            retry_policy=self.retry,
        )

        # Convert messages back to Augment format
        result_messages: list[AssistantContentBlock] = []
        assert response is not None
        for node in response.response_nodes:
            if node.type == public_api_pb2.ChatResultNodeType.RAW_RESPONSE:
                result_messages.append(TextResult(text=node.content))
            elif node.type == public_api_pb2.ChatResultNodeType.TOOL_USE:
                result_messages.append(
                    ToolCall(
                        tool_call_id=node.tool_use.tool_use_id,
                        tool_name=node.tool_use.tool_name,
                        tool_input=json.loads(node.tool_use.input_json),
                    )
                )
            else:
                raise ValueError(f"Unhandled message type: {node.type}")

        # What message metadata do we want from the remote API? Can/should we expose these
        # through api-proxy? Return empty for now
        return result_messages, {"request_id": request_id}

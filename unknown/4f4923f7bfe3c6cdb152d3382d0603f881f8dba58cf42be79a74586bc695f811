# Remote agents service

Warning: This service is highly experimental.

This service manages remote agent lifecycles and saves metadata about the agent.

## Lifecycle

The lifecycle of an agent is as follows (mermaid diagram):
stateDiagram
    [*] --> Starting: User starts new agent
    Starting --> Running: Agent is created
    Running --> Idle: Finished task, waiting for user input, or interrupted by user
    Running --> Error: Unrecoverable error
    Idle --> Running: User sends chat request
    Idle --> [*]: User destroys agent and artifacts
    Error --> [*]: User destroys agent and artifacts


## State Management

The remote agents service uses BigTable as its primary storage mechanism for maintaining agent state, chat history, and pending updates.

### Storage Structure

The remote agents service uses the following BigTable structure for data storage:

### Row Key Patterns and Column Families
As of May 2025, we can only run transactions on the Status column family, because of bigtable & bigtable proxy has constraints on the read filter size.

| Row Key Pattern | Column Families | Description |
|-----------------|-----------------|-------------|
| `RemoteAgent#{agentID}` | Config | Contains the user defined config for the agent, like the workspace setup, starting prompt, SSH config, etc. |
| `RemoteAgent#{agentID}` | Status | Contains runtime info about the agent, like the status, container name etc. All changes to this column should be run as transactions. *All transations we intend to run on runtime configurations shoul be run on this column.*|
| `UserAgentMapping#{userID}` | UserMapping | Maintains a list of agent IDs associated with each user |
| `ExchangeHistory#{agentID}#{sequenceID}` | Output | Contains chat dialog and exchanges with proper ordering |
| `UpdateSequenceID#{agentID}` | Output | Tracks the latest sequence ID for agent updates |
| `PendingUpdates#{agentID}#{sequenceID}` | Output | Stores updates waiting to be processed by the agent |

### Key Data Entities

1. **Agent Configuration** (`entitiesproto.AgentConfig`):
   - Stores the user-defined configuration, user ID, and creation timestamp
   - Can be changed if the user modifies configuration

2. **Agent Status** (`entitiesproto.AgentStatus`):
   - Tracks the current state of the agent (STARTING, RUNNING, WAITING, etc.)
   - Updated as the agent's lifecycle progresses

3. **User-Agent Mapping** (`entitiesproto.UserAgentMapping`):
   - Maintains a list of agent IDs associated with each user
   - Enables efficient listing of a user's agents

4. **Chat History** (`remoteagentsproto.ChatHistoryExchange`):
   - Stores conversation exchanges between users and agents
   - Organized by sequence IDs for proper ordering

5. **Pending Updates** (`entitiesproto.PendingAgentUpdate`):
   - Manages updates waiting to be processed by the agent
   - Includes sequence IDs for ordering and deduplication

### Key Operations

1. **Agent Creation**:
   - Atomically creates a new agent if one with the same ID doesn't exist
   - Uses CheckAndMutateRow for concurrency safety
   - Initializes the agent with STARTING status
   - Updates user-to-agent mapping
   - Sets initial update sequence ID

2. **Status Updates**:
   - Updates the agent's status as it progresses through its lifecycle

3. **Chat History Management**:
   - Writes chat exchanges with sequence IDs for ordering
   - Reads and returns ordered chat history

4. **Pending Updates**:
   - Manages updates like chat requests and interrupts
   - Service orders updates from clients
   - Agent polls for updates and processes them in order

from typing import Any, List
from dataclasses_json import DataClassJsonMixin  # type: ignore
from dataclasses import dataclass, field
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_sample import (
    EditAgentEvalSample,
)
from experimental.vpas.agent.edit_agent_eval.stats import calc_dialog_stats
from research.agents.tools import ToolImplOutput, LoggedToolCall
from research.llm_apis.llm_client import GeneralContentBlock


@dataclass
class EditAgentEvalOutput(DataClassJsonMixin):
    """Output of an evaluation of an edit agent call."""

    sample: EditAgentEvalSample
    # output of the agent that was evaluated
    tool_impl_output: ToolImplOutput
    modified_file_content: str
    diff_against_original: str
    diff_against_expected: str
    correct: bool | None
    success: bool | None
    tool_call_logs: List[LoggedToolCall]
    lang: str | None
    ast_comparison: bool | None
    ast_parsing_succeeded: bool | None

    dialog_messages: list[list[GeneralContentBlock]] = field(default_factory=list)

    def get_all_metrics(self) -> list[tuple[str, Any]]:
        # use list to preserve order
        metrics = []

        metrics.append(("uuid", self.sample.uuid))
        metrics.append(("correct", self.correct))
        metrics.append(("success", self.success))
        metrics.append(("path", self.sample.file_path))
        metrics.append(("category", self.sample.category))
        metrics.append(("is_empty_edit_in_logs", self.sample.is_empty_edit))
        metrics.append(("lang", self.lang))
        metrics.append(("ast_comparison", self.ast_comparison))
        metrics.append(("ast_parsing_succeeded", self.ast_parsing_succeeded))

        for key, value in self.tool_impl_output.auxiliary_data.items():
            if key != "udiff_results":  # too big to store
                metrics.append((key, value))

        metrics.append(
            ("num_original_edit_tool_calls", len(self.sample.edit_file_calls))
        )

        return metrics


@dataclass
class EditAgentEvalSummary(DataClassJsonMixin):
    """Summary of an evaluation of an edit agent call."""

    dataset_name: str
    anthropic_model: str
    agent_name: str

    # sample uuid to output
    outputs: dict[str, EditAgentEvalOutput]

    num_samples: int
    num_samples_with_label: int
    num_correct: int
    num_success: int
    num_errors: int
    accuracy: float
    success_rate: float

    aux_data: dict[str, Any]

    def _add_final_review_metrics(
        self, metrics: list[tuple[str, Any]], passed_key: str, prefix: str = ""
    ):
        # Calculate precision and recall for final_review_stage_result_review_passed
        # here positive means the error was found
        # so true positive is when review failed and is_correct is False
        true_positives = 0
        false_positives = 0
        false_negatives = 0

        total_review_passed = 0

        for output in self.outputs.values():
            output_metrics = dict(output.get_all_metrics())
            review_passed = output_metrics.get(passed_key, False)
            is_correct = output.correct

            if not review_passed and not is_correct:
                true_positives += 1
            elif not review_passed and is_correct:
                false_positives += 1
            elif review_passed and not is_correct:
                false_negatives += 1

            if review_passed:
                total_review_passed += 1

        precision = (
            true_positives / (true_positives + false_positives)
            if (true_positives + false_positives) > 0
            else 0
        )
        recall = (
            true_positives / (true_positives + false_negatives)
            if (true_positives + false_negatives) > 0
            else 0
        )

        metrics.append((f"{prefix}_fail_detection_precision", precision))
        metrics.append((f"{prefix}_fail_detection_recall", recall))
        metrics.append((f"{prefix}_fail_detection_true_positives", true_positives))
        metrics.append((f"{prefix}_fail_detection_false_positives", false_positives))
        metrics.append((f"{prefix}_fail_detection_false_negatives", false_negatives))
        metrics.append((f"{prefix}_total_review_passed", total_review_passed))
        metrics.append(
            (
                f"{prefix}_total_review_passed_rate",
                total_review_passed / len(self.outputs),
            )
        )

    def get_all_metrics(self) -> list[tuple[str, Any]]:
        each_output_metrics = [
            dict(output.get_all_metrics()) for output in self.outputs.values()
        ]

        # use list to preserve order
        metrics = []

        metrics.append(("num_samples", self.num_samples))
        metrics.append(("num_samples_with_label", self.num_samples_with_label))
        metrics.append(("num_correct", self.num_correct))
        metrics.append(("accuracy", self.accuracy))
        metrics.append(("num_success", self.num_success))
        metrics.append(("success_rate", self.success_rate))
        metrics.append(("num_errors", self.num_errors))

        self._add_final_review_metrics(
            metrics,
            passed_key="final_review_stage_result_review_passed",
            prefix="final_review",
        )
        self._add_final_review_metrics(
            metrics,
            passed_key="final_review_stage_result_strict_review_passed",
            prefix="final_review_strict",
        )

        # for key, value in self.aux_data.items():
        #     metrics.append((key, value))

        to_avg = [
            "num_requests",
            "num_input_tokens",
            "num_output_tokens",
            "theoretical_edit_latency",
            "edit_latency",
            "num_total_edits",
            "num_wrong_tool_calls",
            "num_wrong_tool_inputs",
            "num_failed_edits",
            "num_successful_edits",
            "num_verify_passed",
            "num_verify_failed",
            "num_str_replace_calls",
            "num_successful_str_replace_calls",
            "num_view_calls",
            "num_insert_calls",
            "total_str_replace_entries",
        ]
        totals = {key: 0.0 for key in to_avg}
        for output_metrics in each_output_metrics:
            for key, value in output_metrics.items():
                if key in to_avg and value is not None:
                    assert isinstance(value, (int, float))
                    totals[key] += value
        for key, value in totals.items():
            metrics.append((f"avg_{key}", value / len(self.outputs)))

        to_count = [
            "input_review_result_review_passed",
        ]
        counts = {key: 0 for key in to_count}
        for output_metrics in each_output_metrics:
            for key, value in output_metrics.items():
                if key in to_count and value is not None and value:
                    counts[key] += 1
        for key, value in counts.items():
            metrics.append((f"num_{key}", value))

        num_total_edits_histogram = {}
        num_total_edits_success_histogram = {}
        num_total_edits_correct_histogram = {}
        lang_histogram = {}
        for output_metrics in each_output_metrics:
            for key, value in output_metrics.items():
                if key == "num_total_edits" and value is not None:
                    num_total_edits_histogram[value] = (
                        num_total_edits_histogram.get(value, 0) + 1
                    )
                    if output_metrics["success"]:
                        num_total_edits_success_histogram[value] = (
                            num_total_edits_success_histogram.get(value, 0) + 1
                        )
                    if output_metrics["correct"]:
                        num_total_edits_correct_histogram[value] = (
                            num_total_edits_correct_histogram.get(value, 0) + 1
                        )
                if key == "lang":
                    value = str(value)
                    lang_histogram[value] = lang_histogram.get(value, 0) + 1

        metrics.append(("num_total_edits_histogram", num_total_edits_histogram))
        metrics.append(
            ("num_total_edits_success_histogram", num_total_edits_success_histogram)
        )
        metrics.append(
            ("num_total_edits_correct_histogram", num_total_edits_correct_histogram)
        )
        metrics.append(("lang_histogram", lang_histogram))

        total_str_replace_calls = 0
        total_str_replace_entries = 0
        for output_metrics in each_output_metrics:
            total_str_replace_calls += output_metrics.get(
                "num_successful_str_replace_calls", 0
            )
            total_str_replace_entries += output_metrics.get(
                "total_str_replace_entries", 0
            )
        metrics.append(
            (
                "avg_str_replace_entries_per_call",
                total_str_replace_entries / total_str_replace_calls
                if total_str_replace_calls > 0
                else 0,
            )
        )

        return metrics

package k8s

import (
	"bytes"
	"fmt"
	"io"
	"net/http"

	//nolint:staticcheck // SA1019 Keep using module since it's still being maintained and the api of google.golang.org/protobuf/proto differs
	"github.com/golang/protobuf/proto"
	openapiv2 "github.com/google/gnostic-models/openapiv2"
)

// openApiV2WokaroundMiddleware works around duplicate OpenAPIv2 definitions caused
// by pre v0.7.2 metrics-server.
type openApiV2WokaroundMiddleware struct {
	up http.RoundTripper
}

func (f *openApiV2WokaroundMiddleware) RoundTrip(r *http.Request) (*http.Response, error) {
	resp, err := f.up.RoundTrip(r)
	if err != nil || r.URL.Path != "/openapi/v2" {
		return resp, err
	}

	deny := map[string]bool{
		"io.k8s.apimachinery.pkg.apis.meta.v1.APIResourceList_v2": true,
		"io.k8s.apimachinery.pkg.apis.meta.v1.APIResource_v2":     true,
	}

	/// Read the original response body.

	buf, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("openApiV2WokaroundMiddleware response io error: %w", err)
	}
	resp.Body.Close()

	/// Unmarshal the original response body.

	doc := &openapiv2.Document{}
	if err := proto.Unmarshal(buf, doc); err != nil {
		return nil, fmt.Errorf("openApiV2WokaroundMiddleware response unmarshal error: %w", err)
	}

	/// Filter out the above deny list.

	fltr := []*openapiv2.NamedSchema{}
	for _, ns := range doc.GetDefinitions().GetAdditionalProperties() {
		if !deny[ns.GetName()] {
			fltr = append(fltr, ns)
		}
	}
	doc.GetDefinitions().AdditionalProperties = fltr

	/// Re-marshal the updated response body.

	buf2, err := proto.Marshal(doc)
	if err != nil {
		return nil, fmt.Errorf("openApiV2WokaroundMiddleware middleware re-marshal error: %w", err)
	}
	resp.Body = io.NopCloser(bytes.NewReader(buf2))

	/// Return the updated response.

	return resp, nil
}

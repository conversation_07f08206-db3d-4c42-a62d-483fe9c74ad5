import * as vscode from "vscode";

import { ScanCodeChord } from "../../third-party/microsoft/vscode/src/vs/base/common/keybindings";
import { KeyCode } from "../../third-party/microsoft/vscode/src/vs/base/common/keyCodes";
import { KeybindingWatcher } from "../keybindings";
import { getKeyboardIconFontChar } from "../keyboard-icons";

export function getKeybindingDecor(
    commandId: string,
    keybindingWatcher: KeybindingWatcher,
    _context: vscode.ExtensionContext
): Array<vscode.DecorationInstanceRenderOptions> {
    const keybinding = KeybindingWatcher.getStructuredKeybinding(
        keybindingWatcher.getKeybindingForCommand(commandId)
    );
    if (!keybinding || keybinding.chords.length < 1) {
        return [];
    }

    if (keybinding.chords.length > 1 || keybinding.chords[0] instanceof ScanCodeChord) {
        // Don't support multi-chord keybindings because they can get long.
        // Don't support ScanCodeChords because the ScanCodes that are immutable
        //   across keyboard layouts don't have pretty single-char representations.
        return [
            {
                after: {
                    contentText: keybinding
                        ? `[${keybinding.toPrettyString(keybindingWatcher.getSimplifiedPlatform())}]`
                        : "",
                },
            },
        ];
    } else {
        // keybinding.chords.length == 1 now
        const chord = keybinding.chords[0];
        const iconChars: Array<string | null> = [];
        const addImageForKey = (key: KeyCode) => {
            iconChars.push(getKeyboardIconFontChar(key, keybindingWatcher.getSimplifiedPlatform()));
        };
        if (chord.ctrlKey) {
            addImageForKey(KeyCode.Ctrl);
        }
        if (chord.shiftKey) {
            addImageForKey(KeyCode.Shift);
        }
        if (chord.altKey) {
            addImageForKey(KeyCode.Alt);
        }
        if (chord.metaKey) {
            addImageForKey(KeyCode.Meta);
        }
        if (chord.keyCode) {
            addImageForKey(chord.keyCode);
        }
        // if any icon is missing we fall back to text.
        if (iconChars.some((i) => i == null)) {
            return [
                {
                    after: {
                        contentText: keybinding
                            ? `[${keybinding.toPrettyString(keybindingWatcher.getSimplifiedPlatform())}]`
                            : "",
                    },
                },
            ];
        }
        // now everything in images is not null
        // Note use of verticalAlign below -- it actually is not part of the API and it looks like it should not be available
        // however it looks like it gets copied over here:
        // https://github.com/microsoft/vscode/blob/release/1.94/src/vs/editor/browser/services/abstractCodeEditorService.ts#L782
        const decorBase = {
            fontFamily: '"Augment.vscode-augment/augment-kb-icon-font.woff"',
            verticalAlign: "bottom",
        };
        return iconChars.map((i) => ({
            light: {
                after: {
                    ...decorBase,
                    contentText: i!,
                },
            },
            dark: {
                after: {
                    ...decorBase,
                    contentText: i!,
                },
            },
        }));
    }
}

# script provided by GCP support for Support Case 49863372
# see https://console.cloud.google.com/support/cases/detail/v2/49863372?project=system-services-prod
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: nvidia-driver-installer-image-keeper
  namespace: kube-system
  labels:
    k8s-app: nvidia-driver-installer-image-keeper
spec:
  selector:
    matchLabels:
      k8s-app: nvidia-driver-installer-image-keeper
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        name: nvidia-driver-installer-image-keeper
        k8s-app: nvidia-driver-installer-image-keeper
    spec:
      priorityClassName: system-node-critical
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: cloud.google.com/gke-accelerator
                    operator: Exists
      tolerations:
        - operator: "Exists"
      hostPID: true
      initContainers:
        - name: installer-puller
          image: "gcr.io/cos-cloud/cos-gpu-installer:v2.0.38"
          securityContext:
            privileged: true
          command:
            - nsenter
            - -at
            - "1"
            - --
            - sh
            - -c
            - "/usr/bin/ctr -n k8s.io images pull $(/usr/bin/cos-extensions list -- --gpu-installer) &&
              /usr/bin/ctr -n k8s.io images tag $(/usr/bin/cos-extensions list -- --gpu-installer) docker.io/library/cos-nvidia-installer:fixed
              2>&1 && echo 'Installer container loaded successfully' || echo 'Installer container image
              already exists'"
      containers:
        - image: "cos-nvidia-installer:fixed"
          name: nvidia-driver-installer-image-keeper
          imagePullPolicy: Never
          command: ["sleep", "inf"]

package main

import (
	"testing"
	"time"
)

func passingConfig() *Config {
	return &Config{
		Project:                  "foo",
		Instance:                 "bar",
		TargetClusterId:          "baz",
		StartEarlyMinutes:        1,
		TableGroupBackupPolicies: []TableGroupBackupPolicy{},
	}
}

func TestSanityCheckHappyPath(t *testing.T) {
	config := passingConfig()

	err := sanityCheck(config)
	if err != nil {
		t.Fatal("Expected no error")
	}
}

func TestSanityCheckCatchesInvalidRegex(t *testing.T) {
	config := passingConfig()

	config.TableGroupBackupPolicies = []TableGroupBackupPolicy{
		{
			TableNameRegex: "(",
		},
	}

	if err := sanityCheck(config); err == nil {
		t.Fatal("Expected error")
	}
}

func TestSanityCheckCatchesInvalidRetentionTime(t *testing.T) {
	config := passingConfig()

	config.TableGroupBackupPolicies = []TableGroupBackupPolicy{
		{
			TableNameRegex: "foo",
			Policies: []BackupPolicy{
				{
					PeriodHours:    2,
					RetentionHours: 1,
				},
			},
		},
	}

	if err := sanityCheck(config); err == nil {
		t.Fatal("Expected error")
	}
}

func TestSanityCheckCatchesInvalidPeriodOrder(t *testing.T) {
	config := passingConfig()

	config.TableGroupBackupPolicies = []TableGroupBackupPolicy{
		{
			TableNameRegex: "foo",
			Policies: []BackupPolicy{
				{
					PeriodHours:    1,
					RetentionHours: 3,
				},
				{
					PeriodHours:    2,
					RetentionHours: 3,
				},
			},
		},
	}

	if err := sanityCheck(config); err == nil {
		t.Fatal("Expected error")
	}
}

func TestSanityCheckCatchesPeriodSmallerThanStartEarly(t *testing.T) {
	config := passingConfig()

	config.StartEarlyMinutes = 120
	config.TableGroupBackupPolicies = []TableGroupBackupPolicy{
		{
			TableNameRegex: "foo",
			Policies: []BackupPolicy{
				{
					PeriodHours:    1,
					RetentionHours: 3,
				},
			},
		},
	}

	if err := sanityCheck(config); err == nil {
		t.Fatal("Expected error")
	}
}

func TestMatchTableGroupCatchesTableMatchingTwoRegexes(t *testing.T) {
	_, err := matchTableGroup([]string{"foo"}, []TableGroupBackupPolicy{
		{
			TableNameRegex: "f",
		},
		{
			TableNameRegex: "fo",
		},
	})

	if err == nil {
		t.Fatal("Expected error")
	}
}

func TestMatchTableGroupHappyPath(t *testing.T) {
	m, _ := matchTableGroup([]string{"foo", "bar", "baz"}, []TableGroupBackupPolicy{
		{
			TableNameRegex: "^foo$",
		},
		{
			TableNameRegex: "^bar$",
		},
	})
	if len(m) != 2 {
		t.Fatal("Expected 2 tables")
	}
	if _, ok := m["foo"]; !ok {
		t.Fatal("Expected foo to match")
	}
	if _, ok := m["bar"]; !ok {
		t.Fatal("Expected bar to match")
	}
}

type TestBackupInfo struct {
	Name       string
	StartTime  time.Time
	ExpireTime time.Time
}

func (b *TestBackupInfo) GetName() string {
	return b.Name
}

func (b *TestBackupInfo) GetStartTime() time.Time {
	return b.StartTime
}

func (b *TestBackupInfo) GetExpireTime() time.Time {
	return b.ExpireTime
}

func fakeNow(t *testing.T) time.Time {
	now, err := time.Parse(time.RFC3339, "2021-01-01T00:00:00Z")
	if err != nil {
		t.Fatal("Failed to parse time: ", err)
	}

	return now
}

func TestCalculateBackupTodoHappyPath(t *testing.T) {
	now := fakeNow(t)

	backupByTable := map[string][]BackupInfoInterface{
		"foo": {
			&TestBackupInfo{
				Name:       "foo-1",
				StartTime:  now.Add(-2 * time.Hour),
				ExpireTime: now.Add(1 * time.Hour),
			},
		},
		"bar": {
			&TestBackupInfo{
				Name:       "bar-1",
				StartTime:  now.Add(-2 * time.Hour),
				ExpireTime: now.Add(1 * time.Hour),
			},
		},
	}

	policyByTable := TableGroupBackupPolicyMap{
		"foo": &TableGroupBackupPolicy{
			Policies: []BackupPolicy{
				{
					PeriodHours:    1,
					RetentionHours: 2,
				},
			},
		},
		"bar": &TableGroupBackupPolicy{
			Policies: []BackupPolicy{
				{
					PeriodHours:    1,
					RetentionHours: 2,
				},
			},
		},
	}

	todos := calculateBackupTodo(now, 0, backupByTable, policyByTable)
	if len(todos) != 2 {
		t.Fatal("Expected 2 todos")
	}
}

func TestCalculateBackupIgnoreRecentWithShortRetention(t *testing.T) {
	now := fakeNow(t)

	backupByTable := map[string][]BackupInfoInterface{
		"foo": {
			&TestBackupInfo{
				Name:       "foo-1",
				StartTime:  now.Add(-1 * time.Hour / 2),
				ExpireTime: now.Add(1 * time.Hour),
			},
		},
	}

	policyByTable := TableGroupBackupPolicyMap{
		"foo": &TableGroupBackupPolicy{
			Policies: []BackupPolicy{
				{
					PeriodHours:    1,
					RetentionHours: 2,
				},
			},
		},
	}

	todos := calculateBackupTodo(now, 0, backupByTable, policyByTable)
	if len(todos) != 1 {
		t.Fatal("Expected 1 todos, found ", len(todos))
	}
}

func TestCalculateBackupSkipBecauseRecentBackup(t *testing.T) {
	now := fakeNow(t)

	backupByTable := map[string][]BackupInfoInterface{
		"foo": {
			&TestBackupInfo{
				Name:       "foo-1",
				StartTime:  now.Add(-1 * time.Hour),
				ExpireTime: now.Add(5 * time.Hour),
			},
		},
	}

	policyByTable := TableGroupBackupPolicyMap{
		"foo": &TableGroupBackupPolicy{
			Policies: []BackupPolicy{
				{
					PeriodHours:    4,
					RetentionHours: 5,
				},
			},
		},
	}

	todos := calculateBackupTodo(now, 0, backupByTable, policyByTable)
	if len(todos) != 0 {
		t.Fatal("Expected 0 todos, found ", len(todos))
	}
}

func TestCalculateBackupUsesSecondPolicy(t *testing.T) {
	now := fakeNow(t)

	backupByTable := map[string][]BackupInfoInterface{
		"foo": {
			&TestBackupInfo{
				Name:       "foo-1",
				StartTime:  now.Add(-2 * time.Hour),
				ExpireTime: now.Add(100 * time.Hour),
			},
		},
	}

	policyByTable := TableGroupBackupPolicyMap{
		"foo": &TableGroupBackupPolicy{
			Policies: []BackupPolicy{
				{
					PeriodHours:    90,
					RetentionHours: 100,
				},
				{
					PeriodHours:    1,
					RetentionHours: 2,
				},
			},
		},
	}

	todos := calculateBackupTodo(now, 0, backupByTable, policyByTable)
	if len(todos) != 1 {
		t.Fatal("Expected 1 todos, found ", len(todos))
	}
}

func TestCalculateBackupsNoBackup(t *testing.T) {
	now := fakeNow(t)

	backupByTable := map[string][]BackupInfoInterface{}

	policyByTable := TableGroupBackupPolicyMap{
		"foo": &TableGroupBackupPolicy{
			Policies: []BackupPolicy{
				{
					PeriodHours:    1,
					RetentionHours: 2,
				},
			},
		},
	}

	todos := calculateBackupTodo(now, 0, backupByTable, policyByTable)
	if len(todos) != 1 {
		t.Fatal("Expected 1 todos, found ", len(todos))
	}
}

func TestCalculateBackupsStartEarly(t *testing.T) {
	now := fakeNow(t)

	backupByTable := map[string][]BackupInfoInterface{
		"foo": {
			&TestBackupInfo{
				Name:       "foo-1",
				StartTime:  now.Add(-time.Hour * 3 / 4),
				ExpireTime: now.Add(1 * time.Hour),
			},
		},
	}

	policyByTable := TableGroupBackupPolicyMap{
		"foo": &TableGroupBackupPolicy{
			Policies: []BackupPolicy{
				{
					PeriodHours:    1,
					RetentionHours: 1,
				},
			},
		},
	}

	todos := calculateBackupTodo(now, time.Duration(30)*time.Minute, backupByTable, policyByTable)
	if len(todos) != 1 {
		t.Fatal("Expected 1 todos, found ", len(todos))
	}
}

func TestMatchTableWithExcludePattern(t *testing.T) {
	tables := []string{
		"dev-auth-central",
		"test-auth-central",
		"prod-auth-central",
		"dev-github",
		"test-github",
		"prod-github",
	}

	policies := []TableGroupBackupPolicy{
		{
			TableNameExcludeRegex: `^(dev|test)-`,
			TableNameRegex:        `.*-(auth-central|genie-main$|github|settings|share)`,
		},
	}

	m, err := matchTableGroup(tables, policies)
	if err != nil {
		t.Fatal("Expected no error, got:", err)
	}

	// Should only match prod tables
	expectedMatches := []string{"prod-auth-central", "prod-github"}
	if len(m) != len(expectedMatches) {
		t.Fatalf("Expected %d matches, got %d", len(expectedMatches), len(m))
	}

	for _, expected := range expectedMatches {
		if _, ok := m[expected]; !ok {
			t.Errorf("Expected %s to match", expected)
		}
	}

	// Verify dev/test tables don't match
	excludedTables := []string{"dev-auth-central", "test-auth-central", "dev-github", "test-github"}
	for _, excluded := range excludedTables {
		if _, ok := m[excluded]; ok {
			t.Errorf("Table %s should not match but did", excluded)
		}
	}
}

import { EventEmitter, Uri, workspace } from "vscode";

import { getLogger } from "../../logging";
import { DisposableService } from "../../utils/disposable-service";

const emitter = new EventEmitter<StashEvent>();

export const onDidChange = emitter.event;

export type StashEvent = {
    repoId: number;
};

/**
 * @description - listens to stash changes on VCS (Currently specific for GIT) and notifies.
 * @event onDidChange - when stash changes
 *
 * <code>
 * const watcher = new StashWatcher();
 * watcher.listenForChanges(vcsDetails);
 * ...
 * watcher.dispose();
 * </code>
 *
 * Consuming events example:
 * <code>
 * const stashWatcher = require('./stash-watcher');
 * stashWatcher.onDidChange(() => { ... })
 * </code>
 */
export class StashWatcher extends DisposableService {
    private _logger;
    private listening = false;

    constructor(
        private _repoRoot: Uri,
        private _folderName: string,
        private _repoId: number
    ) {
        super();
        this._logger = getLogger(`StashWatcher[${this._folderName}]`);
    }

    public listenForChanges() {
        if (this.listening) {
            return;
        }

        const stashPath = Uri.joinPath(this._repoRoot, ".git", "refs", "stash");
        const fileSystemWatcher = workspace.createFileSystemWatcher(stashPath.fsPath);

        this.addDisposables(
            fileSystemWatcher,
            fileSystemWatcher.onDidCreate(() => {
                this._logger.debug("Stash created");
                emitter.fire({ repoId: this._repoId });
            }),
            fileSystemWatcher.onDidChange(() => {
                this._logger.debug("Stash changed");
                emitter.fire({ repoId: this._repoId });
            }),
            fileSystemWatcher.onDidDelete(() => {
                this._logger.debug("Stash deleted");
                emitter.fire({ repoId: this._repoId });
            })
        );
        this.listening = true;
        this._logger.debug("Listening for stash changes.");
    }
}

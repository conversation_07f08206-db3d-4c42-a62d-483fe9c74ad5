<script lang="ts">
  import { getContext, onD<PERSON>roy } from "svelte";
  import type { Tokens } from "marked";
  import debounce from "lodash.debounce";
  import throttle from "lodash.throttle";

  import Codespan from "$common-webviews/src/common/components/markdown/Codespan.svelte";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import type { ChatModel } from "../../models/chat-model";
  import type {
    FileDetails,
    FindSymbolResponseData,
  } from "$vscode/src/webview-providers/webview-messages";
  import type { Writable } from "svelte/store";
  import { filterUndefined } from "$common-webviews/src/utils/filters";
  import { visibilityObserver } from "$common-webviews/src/apps/chat/components/actions/trackOnScreen";
  import { ChatMetricName } from "$vscode/src/metrics/types";
  import { FileType } from "$vscode/src/utils/types";
  import File from "$common-webviews/src/design-system/icons/vscode/file.svelte";
  import FolderOpened from "$common-webviews/src/design-system/icons/vscode/folder-opened.svelte";
  import SymbolNamespace from "$common-webviews/src/design-system/icons/vscode/symbol-namespace.svelte";
  import CodespanPresentation from "$common-webviews/src/apps/chat/components/markdown-ext/CodespanPresentation.svelte";

  export let token: Tokens.Codespan;

  // Used to access the chat model and issue requests for clickable links into the workspace
  const chatModel: ChatModel | undefined = getContext("chatModel");
  const requestIdStore: Writable<string> = getContext("requestId");
  if (!chatModel) {
    console.warn("ChatModel not found in context -- should you be using this component?");
  }

  // We cannot open directories, only files
  $: linkable =
    (path !== undefined && (path.fileType === undefined || path.fileType === FileType.file)) ||
    (symbols !== undefined && symbols.length > 0);

  // Opens a file in the editor
  const onOpenLocalFile = (details: FileDetails) => {
    if (!linkable) {
      return;
    }
    chatModel?.extensionClient.reportWebviewClientEvent(ChatMetricName.chatCodespanGoToFile);
    chatModel?.extensionClient.openFile(details);
  };

  const onGoToSymbol = (details: FileDetails) => {
    chatModel?.extensionClient.reportWebviewClientEvent(ChatMetricName.chatCodespanGoToSymbol);
    chatModel?.extensionClient.openFile(details);
  };

  // Each request ID has a set of "sources" used to generate it. We use this to restrict our search
  // for clickable file links to a specific set of files and ranges in those files. This makes
  // the search space for clickable links much smaller, making it much faster.
  $: requestId = $requestIdStore;
  $: conversationModel = $chatModel?.currentConversationModel;
  $: currentExchange = $conversationModel?.exchangeWithRequestId?.(requestId);
  $: maybeFilesToSearch = currentExchange?.workspace_file_chunks?.map((chunk) => chunk.file) ?? [];
  $: filesToSearch = maybeFilesToSearch.filter(filterUndefined<FileDetails>).map(
    (file): FileDetails => ({
      repoRoot: file.repoRoot,
      pathName: file.pathName,
    }),
  );

  // Keep track of the set of last seen files. If it changes, we need to refresh the symbol search.
  // We do so by updating lastSeenFiles whenever it changes.
  let lastSeenFiles = new Set<string>();
  $: updateLastSeenFiles(filesToSearch);
  const updateLastSeenFiles = throttle(
    (filesToSearch: FileDetails[]): void => {
      const newFiles = new Set<string>(filesToSearch.map((file) => file.pathName));
      if (newFiles.size !== lastSeenFiles.size) {
        lastSeenFiles = newFiles;
      } else if (new Set([...lastSeenFiles, ...newFiles]).size !== lastSeenFiles.size) {
        lastSeenFiles = newFiles;
      }
    },
    1000,
    { leading: false, trailing: true },
  );

  // Possibly resolve the contents of this codespan to a file
  let path: FileDetails | undefined = undefined;
  async function resolvePath(query: string): Promise<void> {
    const newPath = await chatModel?.extensionClient.resolvePath?.(
      {
        rootPath: "",
        relPath: query,
      },
      { files: filesToSearch },
    );
    // Don't update if contents have changed since query was executed
    if (query === codespanContents) {
      path = newPath;
    }
  }

  // Possibly resolve the contents of this codespan to a symbol
  let symbols: FindSymbolResponseData[] | undefined = undefined;
  async function resolveSymbol(query: string): Promise<FindSymbolResponseData[]> {
    const newSymbols =
      (await chatModel?.extensionClient.resolveSymbols(query, {
        files: filesToSearch,
      })) ?? [];
    // Don't update if contents have changed since query was executed
    if (query === codespanContents) {
      symbols = newSymbols;
    }
    return newSymbols;
  }

  const refreshSymbol = debounce(
    async (query: string) => {
      if (query.length <= 1 || filesToSearch.length === 0) {
        return;
      }
      void resolvePath(query);
      void resolveSymbol(query);
    },
    500,
    { leading: false, trailing: true },
  );

  $: codespanContents = token.raw.slice(1, token.raw.length - 1);

  // Only refresh symbol if we have "seen" the codespan for the first time
  // "Seen" means the user has stopped scrolling for at least 250ms (default)
  // with the codespan over 25% (default) visible
  $: hasSeenFirstTime && lastSeenFiles && refreshSymbol(codespanContents);

  // Purpose:
  // This code sets up a visibility observer to track when the codespan element becomes visible in the viewport.
  // It's used to optimize performance by only triggering certain actions (like symbol resolution)
  // when the element has actually been seen by the user.

  // Why it's important:
  // 1. Performance optimization: By only processing visible elements, we reduce unnecessary computations for off-screen content.
  // 2. User experience: The above means users will experience less latency for codespans that are actually visible.
  // 3. Resource management: Helps in managing resources efficiently, especially in long conversations with many codespans.
  let element: HTMLSpanElement | undefined;
  let observer: ReturnType<typeof visibilityObserver> | undefined;
  let hasSeenFirstTime = false;
  $: if (element) {
    observer?.destroy();
    observer = visibilityObserver(element, {
      onVisible: () => (hasSeenFirstTime = true),
      scrollTarget: document.body,
    });
  }
  onDestroy(() => {
    observer?.destroy();
  });
</script>

{#if path !== undefined}
  <!-- Render filepaths before symbols. If both exist, filepath will be rendered -->
  {@const definedPath = path}
  <CodespanPresentation
    {linkable}
    {token}
    bind:element
    on:click={() => onOpenLocalFile(definedPath)}
    on:keydown={onKey("Enter", () => onOpenLocalFile(definedPath))}
    on:cut={() => navigator.clipboard.writeText(codespanContents)}
    on:paste={(e) => e.preventDefault()}
  >
    <svelte:fragment slot="icon">
      {#if definedPath.fileType === FileType.file}
        <File />
      {:else if definedPath.fileType === FileType.directory}
        <FolderOpened />
      {/if}
    </svelte:fragment>
    {codespanContents}
  </CodespanPresentation>
{:else if symbols !== undefined && symbols.length > 0}
  <!-- Render symbols if no filepath -->
  {@const symbol = symbols[0]}
  <CodespanPresentation
    {linkable}
    {token}
    bind:element
    on:click={async () => {
      // On click, wait for the refresh the symbol to make sure we have the latest results
      // in case the cache has been updated
      const newSymbols = await resolveSymbol(codespanContents);
      const _symbol = newSymbols && newSymbols.length > 0 ? newSymbols[0] : symbol;
      onGoToSymbol(_symbol.file);
    }}
    on:keydown={onKey("Enter", () => onGoToSymbol(symbol.file))}
    on:cut={() => navigator.clipboard.writeText(codespanContents)}
    on:paste={(e) => e.preventDefault()}
  >
    <SymbolNamespace slot="icon" />
    {codespanContents}
  </CodespanPresentation>
{:else}
  <!-- Render normal codespan -->
  <Codespan bind:element {token}>{codespanContents}</Codespan>
{/if}

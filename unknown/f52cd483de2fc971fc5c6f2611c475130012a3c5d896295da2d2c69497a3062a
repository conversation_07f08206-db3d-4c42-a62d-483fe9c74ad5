local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local errorsAtLoadBalancerSpec = {
    displayName: 'Tenant Manager Webhook 5xx measured at load balancer',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*tenmgr-webhook.*",response_code_class="500"}[5m])) > 3
      |||,
    },
  };

  local errorsAtServiceSpec = {
    displayName: 'Tenant Manager webhook errors measured at service',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(au_tenant_manager_webhook_requests{status=~"^40[013]|50[0-9]$", pod=~"tenmgr-webhook-.*"}[5m])) > 3
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(cloud, errorsAtLoadBalancerSpec, 'tenant-manager-webhook-5xx-errors', '5xx errors measured at load balancer'),
    monitoringLib.alertPolicy(cloud, errorsAtServiceSpec, 'tenant-manager-webhook-service-errors', 'Errors measured at service'),
  ]

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

config_setting(
    name = "opt_build",
    values = {"compilation_mode": "opt"},
)

OPT_RUST_FLAGS = [
    "-C",
    "opt-level=3",
    "-C",
    "target-cpu=cascadelake",  # chosen to maintain compatibility with n2 family
    "-C",
    "target-feature=+avx2,+avx,+sse2,+avx512f",
]

rust_library(
    name = "numpy",
    srcs = glob(["*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    rustc_flags = select({
        ":opt_build": OPT_RUST_FLAGS,
        "//conditions:default": [],
    }),
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ),
)

rust_test(
    name = "numpy_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":numpy",
    data = glob(["test_data/*.npy"]),
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

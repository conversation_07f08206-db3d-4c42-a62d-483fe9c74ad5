local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(env, cloud, namespace, namespace_config)
  local appName = 'expiration-watcher';
  local serviceAccount = {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
    metadata: {
      name: 'expiration-watcher-sa',
      namespace: namespace,
      labels: {
        app: 'expiration-watcher',
      },
    },
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local roleBindings = if env == 'DEV' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: 'expiration-watcher-role-binding',
        namespace: namespace,
        labels: {
          app: 'expiration-watcher',
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: 'expiration-watcher-sa',
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'expiration-watcher-cluster-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ] else [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'expiration-watcher-role-binding',
        labels: {
          app: 'expiration-watcher',
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: 'expiration-watcher-sa',
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'expiration-watcher-cluster-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ];

  local expirationWatcherPod = {
    serviceAccountName: 'expiration-watcher-sa',
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    containers: [
      {
        name: 'expiration-watcher',
        target: {
          name: '//tools/genie/expiration:expiration_watcher_image',
          dst: 'expiration_watcher',
        },
        resources: {
          limits: {
            // TODO: These are way too much
            cpu: 0.1,
            memory: '512Mi',
          },
        },
      },
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'expiration-watcher',
      namespace: namespace,
      labels: {
        app: 'expiration-watcher',
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: 0,
      // There should only ever be a single pod making kubernetes state changes.
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          // Don't allow surge to ensure that there is always only a single processor pod up.
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      selector: {
        matchLabels: {
          app: 'expiration-watcher',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'expiration-watcher',
          },
        },
        spec: expirationWatcherPod,
      },
    },
  };

  lib.flatten([
    serviceAccount,
    deployment,
    roleBindings,
  ])

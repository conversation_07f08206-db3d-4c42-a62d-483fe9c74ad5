#!/bin/bash
bazel run //tools/load_test:load_test -- \
 -target="https://dev-edvin.us-central.api.augmentcode.com" \
 -config-file="/home/<USER>/augment/tools/load_test/configs/next_edit_test_configs/nextEditHostRequests_2024-11-18T21:00:00-2024-11-18T22:00:00.json" \
 -auth-token-file="/home/<USER>/.augment/api_token" \
 -src-blobs-bucket="augment-blob-exporter-staging-shard-0-staging" \
 -verbose
#  -stop-on-first-user \
#  -max-requests=-1 \

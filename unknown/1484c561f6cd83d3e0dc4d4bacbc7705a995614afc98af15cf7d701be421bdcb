<script lang="ts">
  import { SuggestionState, type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    buildCodeActions,
    stop,
  } from "$common-webviews/src/common/components/code-roll/code-roll-util";
  import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
  import { type OnCodeAction } from "$common-webviews/src/common/components/code-roll/types";
  import Drawer from "$common-webviews/src/common/components/drawer/Drawer.svelte";
  import SuggestionTree from "$common-webviews/src/common/components/suggestion-tree/SuggestionTree.svelte";
  import { createMediaQueryStore } from "./mediaStore";
  import CodeRollItem from "$common-webviews/src/common/components/code-roll/CodeRollItem.svelte";
  import {
    getCurrentSuggestion,
    getLevel,
    setCodeRollSelection,
  } from "$common-webviews/src/common/components/code-roll/code-roll-context";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import { hasSuggestion } from "$common-webviews/src/common/components/code-roll/code-roll-util";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import {
    areSameSuggestion,
    atIndexSuggestion,
    sortAndFilterSuggestions,
  } from "$common-webviews/src/common/components/suggestion-tree/navigation-utils";
  import { createAsyncMessageFileReader } from "$common-webviews/src/common/utils/file-reader";
  import { onMount } from "svelte";
  import { addEventListener, collectFns } from "$common-webviews/src/common/utils/functional";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  const asyncMsgSender = new AsyncMsgSender(host.postMessage, 3000);
  const readFile = createAsyncMessageFileReader(asyncMsgSender);
  const query = createMediaQueryStore("(min-width: 500px)");
  const ctx = setCodeRollSelection({});
  const codeActions = buildCodeActions("active", "|", "reject", "accept");
  const STALE_ACTIONS = buildCodeActions("active", "|", "reject", "undo");
  let sortedPathSuggestionsMap = new Map<string, IEditSuggestion[]>();
  let suggestions: IEditSuggestion[] = [];
  let loading = true;
  let minimized = false;
  let codeRollScrollContainerRef: HTMLElement;

  function handleMessage(e: MessageEvent<WebViewMessage>) {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.nextEditPreviewActive:
        $ctx = {
          ...$ctx,
          activeSuggestion: msg.data,
          nextSuggestion: msg.data,
        };
        break;
      case WebViewMessageType.nextEditDismiss:
        $ctx = {
          ...$ctx,
          activeSuggestion: undefined,
        };
        break;
      case WebViewMessageType.nextEditActiveSuggestionChanged:
        $ctx.activeSuggestion = msg.data;
        break;
      case WebViewMessageType.nextEditToggleSuggestionTree:
        minimized = !minimized;
        break;
      case WebViewMessageType.nextEditRefreshStarted:
        loading = true;
        break;
      case WebViewMessageType.nextEditRefreshFinished:
        loading = false;
        break;
      case WebViewMessageType.nextEditSuggestionsChanged: {
        loading = false;
        sortedPathSuggestionsMap = new Map(sortAndFilterSuggestions(msg.data.suggestions ?? []));
        suggestions = [...sortedPathSuggestionsMap.values()].flat();

        if (!hasSuggestion(suggestions, $ctx.nextSuggestion)) {
          $ctx = { ...$ctx, nextSuggestion: undefined };
        }

        if (!hasSuggestion(suggestions, $ctx.activeSuggestion)) {
          $ctx = { ...$ctx, activeSuggestion: undefined };
        }

        break;
      }
      case WebViewMessageType.nextEditNextSuggestionChanged:
        $ctx = { ...$ctx, nextSuggestion: msg.data };
        break;
      case WebViewMessageType.nextEditPanelFocus:
        if (containerRef) {
          containerRef.focus();
        }
        break;
    }
  }

  function getNextSelectedSuggestionIndex(suggestionIndex: number): number {
    if (suggestionIndex === -1) {
      return -1;
    }
    let currentIndex = suggestionIndex;
    // search before
    do {
      currentIndex--;
    } while (currentIndex >= 0 && suggestions[suggestionIndex].state === "stale");
    if (currentIndex !== -1) {
      return currentIndex;
    }
    // search after
    currentIndex = suggestionIndex;
    do {
      currentIndex++;
    } while (currentIndex < suggestions.length && suggestions[suggestionIndex].state === "stale");
    if (currentIndex === suggestions.length) {
      return -1;
    }
    return currentIndex;
  }
  function getNextSelectedSuggestion(suggestion: IEditSuggestion): IEditSuggestion | undefined {
    const suggestionIndex = suggestions.findIndex(areSameSuggestion.bind(null, suggestion));
    const nextSelectedSuggestionIndex = getNextSelectedSuggestionIndex(suggestionIndex);
    return atIndexSuggestion(sortedPathSuggestionsMap, nextSelectedSuggestionIndex);
  }

  const onCodeAction: OnCodeAction = (action, suggestion) => {
    switch (action) {
      case "acceptAllInFile":
        if (!Array.isArray(suggestion)) {
          return;
        }
        host.postMessage({
          type: WebViewMessageType.nextEditSuggestionsAction,
          data: {
            acceptAllInFile: suggestion,
          },
        });
        return;
      case "rejectAllInFile":
        if (!Array.isArray(suggestion)) {
          return;
        }
        host.postMessage({
          type: WebViewMessageType.nextEditSuggestionsAction,
          data: {
            rejectAllInFile: suggestion,
          },
        });
        return;
      case "undoAllInFile":
        if (!Array.isArray(suggestion)) {
          return;
        }
        host.postMessage({
          type: WebViewMessageType.nextEditSuggestionsAction,
          data: {
            undoAllInFile: suggestion,
          },
        });
        return;
      case "refresh":
        loading = true;
        host.postMessage({
          type: WebViewMessageType.nextEditRefreshStarted,
          data: "refresh",
        });
        return;
      case "accept":
        if (!suggestion || Array.isArray(suggestion)) {
          return;
        }

        $ctx.selectedSuggestion = getNextSelectedSuggestion(suggestion);
        host.postMessage({
          type: WebViewMessageType.nextEditSuggestionsAction,
          data: {
            accept: suggestion,
          },
        });
        return;
      case "reject":
        if (!suggestion || Array.isArray(suggestion)) {
          return;
        }
        host.postMessage({
          type: WebViewMessageType.nextEditSuggestionsAction,
          data: {
            reject: suggestion,
          },
        });
        return;
      case "active":
        if (!suggestion || Array.isArray(suggestion)) {
          return;
        }
        host.postMessage({
          type: WebViewMessageType.nextEditOpenSuggestion,
          data: suggestion,
        });
        $ctx = {
          ...$ctx,
          activeSuggestion: suggestion,
          selectedSuggestion: suggestion,
        };
        return;
      case "select":
        if (!suggestion || Array.isArray(suggestion)) {
          return;
        }
        $ctx = {
          ...$ctx,
          activeSuggestion: undefined,
          selectedSuggestion: suggestion,
        };
        return;
      case "dismiss":
        if (getLevel($ctx) === "active") {
          $ctx = {
            ...$ctx,
            activeSuggestion: undefined,
          };
          host.postMessage({ type: WebViewMessageType.nextEditDismiss });
          return;
        }
        return;
      case "undo":
        if (!suggestion || Array.isArray(suggestion)) {
          return;
        }
        host.postMessage({
          type: WebViewMessageType.nextEditSuggestionsAction,
          data: {
            undo: suggestion,
          },
        });
        return;
    }
  };

  function handleWindowFocus(_event: Event) {
    isPanelFocused = true;
  }

  function handleWindowBlur(_event: Event) {
    $ctx.selectedSuggestion = undefined;
    isPanelFocused = false;
  }

  onMount(() => {
    host.postMessage({
      type: WebViewMessageType.nextEditLoaded,
    });

    return collectFns(
      addEventListener(window, "focus", handleWindowFocus),
      addEventListener(window, "blur", handleWindowBlur),
    );
  });

  let containerRef: HTMLElement;
  let isPanelFocused = false;

  // Focus the container when it's created
  $: if (containerRef) {
    containerRef.focus();
  }
  $: suggestion = getCurrentSuggestion($ctx);
  $: if (
    isPanelFocused &&
    $ctx.nextSuggestion &&
    $ctx.selectedSuggestion === undefined &&
    $ctx.nextSuggestion !== $ctx.selectedSuggestion
  ) {
    $ctx.selectedSuggestion = $ctx.nextSuggestion;
  }
</script>

<svelte:window on:message={handleMessage} />
<MonacoProvider.Root>
  <main class="c-next-edit-suggestions" class:c-next-edit-suggestions__narrow={!$query}>
    <div
      class="c-next-edit-suggestions__container"
      bind:this={containerRef}
      tabindex="0"
      role="button"
    >
      {#if suggestions.length === 0}
        <div class="c-next-edit-suggestions--empty">
          <p>No Suggestions</p>
          <ButtonAugment {loading} on:click={stop(onCodeAction, "refresh")}>Refresh</ButtonAugment>
        </div>
      {:else if loading}
        <div class="c-next-edit-suggestions--empty">
          <SpinnerAugment />
        </div>
      {:else}
        <Drawer
          bind:minimized
          showButton={false}
          class="c-next-edit-suggestions__drawer"
          initialWidth={300}
          expandedMinWidth={150}
          deadzone={50}
          minimizedWidth={40}
        >
          <div slot="left" class="c-next-edit-suggestions__left">
            <SuggestionTree {sortedPathSuggestionsMap} {onCodeAction} {minimized} />
          </div>
          <div
            class="c-next-edit-suggestions__right"
            slot="right"
            bind:this={codeRollScrollContainerRef}
          >
            {#if suggestion}
              <CodeRollItem
                filepath={suggestion.qualifiedPathName}
                suggestions={[suggestion]}
                {onCodeAction}
                codeActions={suggestion.state === SuggestionState.accepted
                  ? STALE_ACTIONS
                  : codeActions}
                {readFile}
                expandable={false}
                scrollContainer={codeRollScrollContainerRef}
              />
            {:else}
              <div class="c-next-edit-suggestions__right--empty"></div>
            {/if}
          </div>
        </Drawer>
      {/if}
    </div>
  </main>
</MonacoProvider.Root>

<style>
  .c-next-edit-suggestions {
    /** Somewhere we are getting an overflow and an outline on focus, this is meant to make that go away*/
    padding-left: 2px;
    --augment-code-roll-item-border: var(--ds-color-neutral-a6);
    --augment-code-roll-item-background: transparent;
  }
  .c-next-edit-suggestions,
  .c-next-edit-suggestions__container {
    display: flex;
    flex-direction: column;
    justify-content: start;
    width: 100%;
    height: 100%;
    min-height: 0;
    flex: 1;
    position: relative;
    overflow: hidden;
  }
  .c-next-edit-suggestions__container :global(.c-drawer__right) {
    overflow: hidden;
  }
  .c-next-edit-suggestions__left,
  .c-next-edit-suggestions__right {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex: 1;
    padding-left: 1px;
  }
  .c-next-edit-suggestions__right.c-next-edit-suggestions__right {
    padding: var(--ds-spacing-2);
    flex: 1 0;
    overflow: auto;
  }
  .c-next-edit-suggestions > :global(.c-next-edit-suggestions__drawer) {
    overflow: hidden;
  }
  .c-next-edit-suggestions.c-next-edit-suggestions__narrow {
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .c-next-edit-suggestions__narrow .c-next-edit-suggestions__left,
  .c-next-edit-suggestions__narrow .c-next-edit-suggestions__right {
    overflow: hidden;
    flex: 1;
  }
  .c-next-edit-suggestions--empty,
  .c-next-edit-suggestions__right--empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
  }
</style>

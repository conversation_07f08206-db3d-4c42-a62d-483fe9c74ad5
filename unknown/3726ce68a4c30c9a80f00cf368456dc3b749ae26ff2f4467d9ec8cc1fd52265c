"""Protocol module for tokenizers."""

from typing import Callable, Mapping, Protocol, Sequence, runtime_checkable, TypeVar

from base.registry import Registry


@runtime_checkable
class SpecialTokens(Protocol):
    """Special tokens for a tokenizer."""

    padding: int
    """Padding token."""

    eos: int
    """End of sequence token."""


# === Special token interfaces. ===
# The classes below are data protocols (i.e. they are *interfaces* not implementations)
# that describe required fields for a special tokens to be used for e.g. FIM or
# retrieval. Please define additional interfaces as needed below. Note that it is
# important to subclass `Protocol` or Python will create an implementation.


@runtime_checkable
class FimSpecialTokens(SpecialTokens, Protocol):
    """Special tokens for a FIM model."""

    fim_prefix: int
    """The token that preceeds the FIM prefix."""

    fim_middle: int
    """The token that preceeds the FIM middle span."""

    fim_suffix: int
    """The token that preceeds the FIM suffix."""


@runtime_checkable
class RagSpecialTokens(FimSpecialTokens, Protocol):
    """Special tokens for a the retrieval-augmented code completion model."""

    retrieval_section: int
    """Denotes the start of a retrieval section."""

    ret_start: int
    """Denotes the start of a retrieved chunk."""

    ret_body: int
    """Denotes the body of a retrieved chunk."""

    prefix_body: int
    """Denotes the body of a prefix chunk."""

    nearby_prefix: int
    """Denotes the nearby prefix ."""

    nearby_suffix: int
    """Denotes the nearby suffix."""

    sig_begin: int
    """Denotes the start of the signature prompt part."""

    sig_end: int
    """Denotes the end of the signature prompt part."""

    signature_section: int
    """Denotes the start of the signature section."""

    sig_lookup: int
    """Denotes the start of the signature lookup."""

    far_prefix: int
    """Denotes the far prefix."""

    far_suffix: int
    """Denotes the far suffix."""

    filename: int
    """Denotes the filename."""

    skip: int
    """Denotes the skip token."""

    pause: int
    """Denotes the pause token."""

    newline: int
    """Denotes the newline token."""

    begin_sequence: tuple[int, ...]
    """The tokens that should always appear at the start of any token sequence. Can be empty for tokenizers/models that don't have these tokens."""


@runtime_checkable
class NextEditGenSpecialTokens(RagSpecialTokens, Protocol):
    """Special tokens for the next-edit generation models."""

    instruction: int
    """Denotes the start of the instruction."""

    selected_code: int
    """Denotes the start of the selected code."""

    diff_section: int
    """Denotes the start of the diff section."""

    diff_hunk: int
    """Denotes the start of a diff hunk."""

    has_change: int
    """Denotes there is no change in the output."""

    no_change: int
    """Denotes there is a change in the output."""


@runtime_checkable
class RetrievalSpecialTokens(FimSpecialTokens, Protocol):
    """Special tokens for retrieval models.

    NOTE(arun): For historical reasons, retrieval prompt formatters rely on the FIM
    special tokens, and hence the retrieval special tokens are a superset of FIM.
    """

    start_of_key: int
    """Denotes the start of a document/key."""

    end_of_query: int
    """Denotes the end of a query; used to extract query embeddings."""

    end_of_key: int
    """Denotes the end of a document/key; used to extract document embeddings."""

    begin_sequence: tuple[int, ...]
    """The tokens that should always appear at the start of any token sequence. Can be empty for tokenizers/models that don't have these tokens."""


@runtime_checkable
class RerankerSpecialTokens(RetrievalSpecialTokens, Protocol):
    """Special tokens for rerankers."""

    chunk_prediction: int
    """Denotes the end of a chunk prediction."""


SpecialTokensT = TypeVar("SpecialTokensT", bound=SpecialTokens, covariant=True)


@runtime_checkable
class Tokenizer(Protocol[SpecialTokensT]):
    """Converts a sequence of bytes into a sequence of ints and back.

    While the underyling tokenizers use bytes, the interface uses the Python builtin
    str type instead. This restricts us to text that is valid UTF-8.
    This can be fixed by adding additional methods to the interface in a
    future version.

    Note: The vocab itself is an implementation detail of
    the tokenizer so that two different tokenizers implementing
    the same tokenizer/detokenizer behavior might have a different
    vocab representation.
    """

    @property
    def vocab_size(self) -> int:
        """Number of tokens in tokenizer's vocabulary, including special tokens."""
        raise NotImplementedError()

    @property
    def vocab(self) -> Mapping[bytes, int]:
        """Vocabulary of the tokenizer."""
        raise NotImplementedError()

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens.

        Special tokens in the vocab are not considered when tokenizing, e.g.
        <|endoftext|> is tokenized as [27, 91, 437, 1659, 5239, 91, 29] instead [50256].

        This function should be used for user input.
        """
        raise NotImplementedError()

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens.

        Special tokens are considered when tokenizing, e.g.
        <|endoftext|> is tokenized as [50256].

        User input should never be tokenized with tokenize_unsafe.
        """
        raise NotImplementedError()

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string.

        Tokens that do not correspond to UTF-8 characters are returned as \ufffd.
        """
        raise NotImplementedError()

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with their offsets.

        Tokens that do not correspond to UTF-8 characters are returned as \ufffd.

        Args:
            token_ids: A list of token IDs.

        Returns:
            A tuple of the detokenized string and a list of offsets.
            Each entry in the list of offsets corresponds to the start character within
            the string. For tokens that represent part of a UTF-8 character, the
            offset is the start of that character.
        """
        raise NotImplementedError()

    @property
    def special_tokens(self) -> SpecialTokensT:
        """Return an object with attributes for special tokens."""
        raise NotImplementedError()


TokenizerFactory = Callable[[], Tokenizer]

REGISTRY: Registry[TokenizerFactory] = Registry()

import structlog

from tools.feature_flags.launchdarkly_diff_builder import LaunchDarklyDiffBuilder
from tools.feature_flags.config import LaunchDarklyConfig
from tools.feature_flags.launchdarkly_client import LaunchDarklyClient
from tools.feature_flags.launchdarkly_types import LaunchDarklyDiff

log = structlog.get_logger()


def sync(launchdarkly_api_key, repo_dir, apply_changes):
    config = LaunchDarklyConfig(launchdarkly_api_key=launchdarkly_api_key)
    ld_client = LaunchDarklyClient(config)
    ld_diff_builder = LaunchDarklyDiffBuilder(config, repo_dir)

    new_flags, keys_to_sync = ld_diff_builder.get_new_flags()
    new_flags_sync = [flag for flag in new_flags if flag.key in keys_to_sync]
    new_flags_no_sync = [flag for flag in new_flags if flag.key not in keys_to_sync]

    log.info("New flags no-sync: %s", [flag.key for flag in new_flags_no_sync])
    log.info("New flags sync: %s", [flag.key for flag in new_flags_sync])
    if apply_changes:
        ld_client.create_new_flags(new_flags_sync)

    diff, keys_to_sync = ld_diff_builder.get_diff()
    if diff:
        diff_sync = LaunchDarklyDiff(
            {key: diff.patch_flags[key] for key in keys_to_sync}
        )
        diff_no_sync = LaunchDarklyDiff(
            {
                key: diff.patch_flags[key]
                for key in diff.patch_flags
                if key not in keys_to_sync
            }
        )
        log.info("Flags diff basic no-sync: %s", diff_no_sync.to_dict())
        log.info("Flags diff basic sync: %s", diff_sync.to_dict())
        if apply_changes:
            ld_client.apply_diff(diff_sync)
    else:
        log.info("No basic Feature Flag diff to apply")

    diff, keys_to_sync = ld_diff_builder.get_diff(is_envs=True)
    if diff:
        diff_sync = LaunchDarklyDiff(
            {key: diff.patch_flags[key] for key in keys_to_sync}
        )
        diff_no_sync = LaunchDarklyDiff(
            {
                key: diff.patch_flags[key]
                for key in diff.patch_flags
                if key not in keys_to_sync
            }
        )
        log.info("Flags diff envs no-sync: %s", diff_no_sync.to_dict())
        log.info("Flags diff envs sync: %s", diff_sync.to_dict())
        if apply_changes:
            ld_client.apply_diff(diff_sync)
    else:
        log.info("No envs Feature Flag diff to apply")


if __name__ == "__main__":
    """
    Run for a manual sync
    """
    import os
    import argparse

    launchdarkly_api_key = os.environ["LAUNCHDARKLY_API_KEY"]
    base_dir = (
        os.environ["BUILD_WORKSPACE_DIRECTORY"]
        if "BUILD_WORKSPACE_DIRECTORY" in os.environ
        else "."
    )
    parser = argparse.ArgumentParser()
    parser.add_argument("--apply", default=False)
    args = parser.parse_args()
    if args.apply:
        apply_changes = True
    else:
        print("Dry run - to apply changes use --apply as the first argument\n")
        apply_changes = False
    sync(launchdarkly_api_key, base_dir, apply_changes)

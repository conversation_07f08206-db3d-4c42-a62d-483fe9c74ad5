package com.augmentcode.intellij.index

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.application.smartReadAction
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.IndexNotReadyException
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiFile
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.util.CommonProcessors
import com.intellij.util.indexing.FileBasedIndex

object AugmentBlobStateReader {
  val FORCE_UPLOAD: Key<Boolean> = Key.create("augment.force.upload")

  fun read(file: PsiFile?): AugmentBlobState? {
    if (file == null) {
      return null
    }
    return read(file.project, file.virtualFile)
  }

  /**
   * Reads the latest state of a file from the index. The file might need to be .gitignored or .augmentignored.
   * This method already handles the necessary runReadAction internally, so callers do not need to wrap this in their own read action.
   *
   * @return null if indexing is not ready yet or if there are any errors accessing the index
   */
  fun read(
    project: Project,
    file: VirtualFile?,
  ): AugmentBlobState? {
    if (file == null) {
      return null
    }
    return runReadAction {
      try {
        FileBasedIndex.getInstance().getFileData(AugmentLocalIndex.NAME, file, project)
          .keys.maxByOrNull { it.remoteSyncTimestamp }
      } catch (e: IndexNotReadyException) {
        null
      } catch (e: IllegalStateException) {
        null
      } catch (e: AssertionError) {
        // there might be a race condition where while indexing we request reindexing
        thisLogger().warn("Failed to read state for file ${file.path}", e)
        null
      }
    }
  }

  /**
   * Returns all locally indexed blobs, including blobs that should be .gitignored or .augmentignored.
   *
   * NOTE: For most use cases, prefer [com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager.syncedBlobs],
   * which properly handles filtering of .gitignored and .augmentignored files. Use this method only when you
   * specifically need access to locally indexed blobs without filtering.
   *
   * The caller MUST filter out .gitignored and .augmentignored blobs before uploading or referencing in any server calls,
   * such as checkpoints, find_missing, completions requests, and chat requests.
   *
   * This method already handles the necessary runReadAction internally,
   * so callers do not need to wrap this in their own read action.
   *
   * @return all locally indexed blobs or null if indexing is not ready yet
   */
  fun allUnfilteredIndexes(project: Project): Collection<AugmentBlobState>? =
    runReadAction {
      try {
        FileBasedIndex.getInstance().getAllKeys(AugmentLocalIndex.NAME, project)
      } catch (e: IndexNotReadyException) {
        null
      } catch (e: IllegalStateException) {
        null
      }
    }

  suspend fun requestInvalidation(
    project: Project,
    remoteNamesToInvalidate: Set<String>,
  ) {
    if (remoteNamesToInvalidate.isEmpty()) {
      return
    }
    val filesToInvalidate =
      findFilesByState(project) { blobState -> remoteNamesToInvalidate.contains(blobState.remoteName) }
    if (filesToInvalidate.isEmpty()) {
      thisLogger().warn(
        "Did not find files to invalidate for ${remoteNamesToInvalidate.size} remote names: ${remoteNamesToInvalidate.joinToString()}",
      )
      return
    }

    filesToInvalidate.forEach { file ->
      // Invoke later from the EDT thread (this is used instead of coroutines to make testing possible)
      ApplicationManager.getApplication().invokeLater {
        // Require the write lock for as short a time as possible.
        runWriteAction {
          file.putUserData(FORCE_UPLOAD, true)
        }
        // Data is set so we can request reindexing.
        FileBasedIndex.getInstance().requestReindex(file)
      }
    }
  }

  /**
   * Finds all files in the project that match the given condition, including blobs that should be .gitignored or .augmentignored.
   *
   * The caller MUST filter out .gitignored and .augmentignored blobs before uploading or referencing in any server calls,
   * such as checkpoints, find_missing, completions requests, and chat requests.
   *
   * This method already handles the necessary runReadAction internally,
   * so callers do not need to wrap this in their own read action.
   *
   * @return Collection of VirtualFiles that match the condition
   */
  suspend fun findFilesByState(
    project: Project,
    condition: (AugmentBlobState) -> Boolean,
  ): Collection<VirtualFile> {
    val collectProcessor = CommonProcessors.CollectProcessor<VirtualFile>()
    smartReadAction(project) {
      val blobs = allUnfilteredIndexes(project) ?: return@smartReadAction
      val keys =
        blobs
          .filter(condition)
          .toSet()
      FileBasedIndex.getInstance()
        .processFilesContainingAnyKey(
          AugmentLocalIndex.NAME,
          keys,
          GlobalSearchScope.everythingScope(project),
          null,
          null,
          collectProcessor,
        )
    }
    return collectProcessor.results
  }
}

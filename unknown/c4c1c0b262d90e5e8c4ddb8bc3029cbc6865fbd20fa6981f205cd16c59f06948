"""Unit tests for ffw_dsv2."""

import copy

import flash_attn
import pytest
import torch

from base.fastforward import cached_attention
from base.fastforward.deepseek_v2 import fwd_dsv2, model_specs


def test_group_limited_greedy():
    """Simple test of the group-limited greedy topk algorithm."""
    n_scores = 12
    n_groups = 3
    k_scores = 4
    k_groups = 2

    # We will prepare test data with topk indexes sorted
    scores_dat = []
    expected_topk = []

    # Top scores actually all come from a single group
    scores_dat.append(list(range(n_scores)))
    expected_topk.append({11, 10, 9, 8})

    # Groups 0 and 2 have highest 2 scores; group 1 has next highest
    # scores but due to greedy group selection, nothing from group 1 is selected
    scores_dat.append([0, 100, 2, 3, 40, 50, 60, 70, 8, 9, 10, 110])
    expected_topk.append({11, 1, 10, 9})

    scores = torch.tensor(scores_dat)
    _, topk_idx = fwd_dsv2.DeepSeekV2MoE.group_limited_greedy(
        scores, n_groups, k_groups, k_scores
    )
    for i in range(len(topk_idx)):
        assert set(map(int, topk_idx[i])) == expected_topk[i]


def test_parameters_lite(deepseek_coder_v2_lite_bf16_fake_fixture):
    """Ensure that the large model can initialize."""
    model = deepseek_coder_v2_lite_bf16_fake_fixture
    total = 0
    for p in model.parameters():
        total += p.numel()
    assert total == 15_706_484_224


def _test_nano_model(ms, step_fn, attn_factory):
    assert (
        ms.name == "deepseek-coder-v2-nano"
    ), "this test only runs for deepseek-coder-v2-nano."
    attn = attn_factory(128)
    attn.reset()

    # These tokens represent:
    # <｜begin▁of▁sentence｜>User: write a quick sort algorithm in python.
    #
    # Assistant:
    # fmt: off
    inp = [100000, 5726, 25, 3708, 245, 3399, 3734, 6712, 279, 9934, 13, 185, 185, 77398, 25]
    # fmt: on

    output_tokens = []
    for _ in range(5):
        logits = step_fn(list(inp), attn).checked_cast(torch.Tensor)
        next_tok = int(logits[-1].argmax().item())
        output_tokens.append(next_tok)
        inp = [next_tok]
    # This is collected by running this nano model offline.
    # The weights of this nano model is randomly generated but we then fix the weights for unit test
    # to make sure nothing breaks.
    assert output_tokens == [75526, 58751, 33332, 680, 75141]


def test_generate_wo_cuda_graph_no_mqa(deepseek_coder_v2_nano_bf16_fixture):
    """Ensure that the model generates the correct outputs."""
    ms, step_fn, attn_factory = deepseek_coder_v2_nano_bf16_fixture
    _test_nano_model(ms, step_fn, attn_factory)


@pytest.mark.xfail(
    reason="This test is expected to fail. The MQA version has an unidentified numerical error. See comment on test_attention_mla_mqa below."
)
def test_generate_wo_cuda_graph_mqa(deepseek_coder_v2_mqa_for_mla_nano_bf16_fixture):
    """Ensure that the model generates the correct outputs."""
    ms, step_fn, attn_factory = deepseek_coder_v2_mqa_for_mla_nano_bf16_fixture
    _test_nano_model(ms, step_fn, attn_factory)


# Test that a reference implementation of mqa-for-mla is equivalent to the original implementation.
# WARNING: The equivalence is not exact! We've observed an undiagnosed numerical drift in the
# mqa-for-mla implementation that causes this test to fail if the threshold in increased by a few
# orders of magnitude. Some imprecision is expected due to the mqa-for-mla implementation running
# its No-PE calculations in the full LoRA dimension rather than the head dimension, but this is
# more severe than we'd expect from that and substantially degrades results in practice.
@pytest.mark.parametrize("seq_len", [3, 10, 21, 37, 42])
def test_attention_mla_mqa(seq_len: int):
    torch.manual_seed(31415)
    torch.set_printoptions(precision=3, sci_mode=False, linewidth=250)

    ms_1 = model_specs.get_model_spec(model_name="deepseek-coder-v2-nano")
    ms_1.num_layers = 1

    ms_2 = model_specs.get_model_spec(model_name="deepseek-coder-v2-nano-mqa")
    ms_2.num_layers = 1
    ms_2.attention = copy.copy(ms_1.attention)
    ms_2.attention.use_mqa_for_mla = True

    dtype = torch.float16

    attn_v1 = fwd_dsv2.DeepSeekV2AttentionImpl1(
        config=ms_1.attention, dtype=dtype, device="cuda"
    )
    attn_v2 = fwd_dsv2.DeepSeekV2AttentionImpl2(
        config=ms_2.attention, dtype=dtype, device="cuda"
    )
    attn_cache_1 = fwd_dsv2.DeepSeekCoderV2AttentionFactory(ms=ms_1, dtype=dtype)(128)
    attn_cache_2 = fwd_dsv2.DeepSeekCoderV2AttentionFactory(ms=ms_2, dtype=dtype)(128)
    config = ms_1.attention

    init_range = 0.1

    with torch.no_grad():
        # initialize values
        for weight in attn_v1.parameters():
            weight.data.uniform_(-init_range, init_range)

        for weight in attn_v2.parameters():
            weight.data.uniform_(-init_range, init_range)

        attn_v1_weights = {name: weight for name, weight in attn_v1.named_parameters()}
        attn_v2_weights = {name: weight for name, weight in attn_v2.named_parameters()}

        print("-" * 80, flush=True)
        print(ms_1.attention)
        for name, weight in attn_v1_weights.items():
            print(f"{name:<50} {weight.size()}", flush=True)

        print("-" * 80, flush=True)
        print(ms_2.attention)
        for name, weight in attn_v2_weights.items():
            print(f"{name:<50} {weight.size()}", flush=True)

        a, b = torch.split(
            attn_v1_weights["kv_a_proj_with_mqa.weight"],
            [config.kv_lora_rank, config.qk_rope_head_dim],
            dim=0,
        )
        attn_v2_weights["kv_a_proj_with_mqa.weight"].data.copy_(
            torch.cat([b, a], dim=0)
        )

        attn_v2_weights["kv_a_layernorm.weight"].data.copy_(
            attn_v1_weights["kv_a_layernorm.weight"]
        )

        attn_v2_weights["o_proj.weight"].data.copy_(attn_v1_weights["o_proj.weight"])

        attn_v2_weights["v_proj"].data.view(
            config.num_heads,
            config.kv_lora_rank,
            config.v_head_dim,
        ).copy_(
            attn_v1_weights["kv_b_proj.weight"]
            .view(
                config.num_heads,
                config.qk_nope_head_dim + config.v_head_dim,
                config.kv_lora_rank,
            )[:, config.qk_nope_head_dim :, :]
            .transpose(1, 2)
            .contiguous()
        )

        attn_v2_weights["q_proj.weight"].view(
            config.num_heads,
            config.qk_rope_head_dim + config.kv_lora_rank,
            config.hidden_dim,
        )[:, : config.qk_rope_head_dim, :].copy_(
            attn_v1_weights["q_proj.weight"].view(
                config.num_heads,
                config.qk_nope_head_dim + config.qk_rope_head_dim,
                config.hidden_dim,
            )[:, config.qk_nope_head_dim :, :]
        )

        attn_v2_weights["q_proj.weight"].view(
            config.num_heads,
            config.qk_rope_head_dim + config.kv_lora_rank,
            config.hidden_dim,
        )[:, config.qk_rope_head_dim :, :].copy_(
            torch.bmm(
                attn_v1_weights["kv_b_proj.weight"]
                .view(
                    config.num_heads,
                    config.qk_nope_head_dim + config.v_head_dim,
                    config.kv_lora_rank,
                )[:, : config.qk_nope_head_dim]
                .transpose(1, 2),
                attn_v1_weights["q_proj.weight"].view(
                    config.num_heads,
                    config.qk_nope_head_dim + config.qk_rope_head_dim,
                    config.hidden_dim,
                )[:, : config.qk_nope_head_dim, :],
            )
        )

    x = torch.zeros(seq_len, ms_1.attention.hidden_dim, dtype=dtype, device="cuda")
    x_idxs = torch.arange(seq_len, dtype=torch.int32, device="cuda")
    x.uniform_(-init_range, init_range)

    attn_cache_1.reset()
    attn_cache_2.reset()

    attn_cache_1.register_tokens_get_positions(x_idxs, process_idx=0)
    ref = attn_v1(x, attn=attn_cache_1, layer_idx=0)

    attn_cache_2.register_tokens_get_positions(x_idxs, process_idx=0)
    out = attn_v2(x, attn=attn_cache_2, layer_idx=0)

    torch.testing.assert_close(out, ref, atol=5e-3, rtol=1e-3)


# Test that update_weights_for_mqa_for_mla is equivalent to the reference implementation above
def test_attention_weights_equivalence():
    torch.manual_seed(31415)
    torch.set_printoptions(precision=3, sci_mode=False, linewidth=250)

    ms_1 = model_specs.get_model_spec(model_name="deepseek-coder-v2-nano")
    ms_1.num_layers = 1

    ms_2 = model_specs.get_model_spec(model_name="deepseek-coder-v2-nano-mqa")
    ms_2.num_layers = 1
    ms_2.attention = copy.copy(ms_1.attention)
    ms_2.attention.use_mqa_for_mla = True

    dtype = torch.float16

    attn_v1 = fwd_dsv2.DeepSeekV2AttentionImpl1(
        config=ms_1.attention, dtype=dtype, device="cuda"
    )
    attn_v2 = fwd_dsv2.DeepSeekV2AttentionImpl2(
        config=ms_2.attention, dtype=dtype, device="cuda"
    )
    config = ms_1.attention

    init_range = 0.5

    with torch.no_grad():
        # initialize values
        for weight in attn_v1.parameters():
            weight.data.uniform_(-init_range, init_range)

        for weight in attn_v2.parameters():
            weight.data.uniform_(-init_range, init_range)

        attn_v1_weights = {
            f"layers.0.attn.{name}": weight
            for name, weight in attn_v1.named_parameters()
        }
        attn_v2_weights = {
            f"layers.0.attn.{name}": weight
            for name, weight in attn_v2.named_parameters()
        }

        print("-" * 80, flush=True)
        print(ms_1.attention)
        for name, weight in attn_v1_weights.items():
            print(f"{name:<50} {weight.size()}", flush=True)

        print("-" * 80, flush=True)
        print(ms_2.attention)
        for name, weight in attn_v2_weights.items():
            print(f"{name:<50} {weight.size()}", flush=True)

        a, b = torch.split(
            attn_v1_weights["layers.0.attn.kv_a_proj_with_mqa.weight"],
            [config.kv_lora_rank, config.qk_rope_head_dim],
            dim=0,
        )
        attn_v2_weights["layers.0.attn.kv_a_proj_with_mqa.weight"].data.copy_(
            torch.cat([b, a], dim=0)
        )

        attn_v2_weights["layers.0.attn.kv_a_layernorm.weight"].data.copy_(
            attn_v1_weights["layers.0.attn.kv_a_layernorm.weight"]
        )

        attn_v2_weights["layers.0.attn.o_proj.weight"].data.copy_(
            attn_v1_weights["layers.0.attn.o_proj.weight"]
        )

        attn_v2_weights["layers.0.attn.v_proj"].data.view(
            config.num_heads,
            config.kv_lora_rank,
            config.v_head_dim,
        ).copy_(
            attn_v1_weights["layers.0.attn.kv_b_proj.weight"]
            .view(
                config.num_heads,
                config.qk_nope_head_dim + config.v_head_dim,
                config.kv_lora_rank,
            )[:, config.qk_nope_head_dim :, :]
            .transpose(1, 2)
            .contiguous()
        )

        attn_v2_weights["layers.0.attn.q_proj.weight"].view(
            config.num_heads,
            config.qk_rope_head_dim + config.kv_lora_rank,
            config.hidden_dim,
        )[:, : config.qk_rope_head_dim, :].copy_(
            attn_v1_weights["layers.0.attn.q_proj.weight"].view(
                config.num_heads,
                config.qk_nope_head_dim + config.qk_rope_head_dim,
                config.hidden_dim,
            )[:, config.qk_nope_head_dim :, :]
        )

        attn_v2_weights["layers.0.attn.q_proj.weight"].view(
            config.num_heads,
            config.qk_rope_head_dim + config.kv_lora_rank,
            config.hidden_dim,
        )[:, config.qk_rope_head_dim :, :].copy_(
            torch.bmm(
                attn_v1_weights["layers.0.attn.kv_b_proj.weight"]
                .view(
                    config.num_heads,
                    config.qk_nope_head_dim + config.v_head_dim,
                    config.kv_lora_rank,
                )[:, : config.qk_nope_head_dim]
                .transpose(1, 2),
                attn_v1_weights["layers.0.attn.q_proj.weight"].view(
                    config.num_heads,
                    config.qk_nope_head_dim + config.qk_rope_head_dim,
                    config.hidden_dim,
                )[:, : config.qk_nope_head_dim, :],
            )
        )

        fwd_dsv2.update_weights_for_mqa_for_mla(
            attn_v1_weights, layer_idxs=[0], attention=config
        )

        for name in attn_v1_weights.keys():
            torch.testing.assert_close(
                attn_v1_weights[name], attn_v2_weights[name], atol=5e-7, rtol=1e-7
            )
            print(f"{name:<50} {attn_v1_weights[name].size()}", flush=True)


@pytest.mark.parametrize("seq_len", [3])
@pytest.mark.parametrize("num_heads_q", [2, 4, 8])
@pytest.mark.parametrize("num_heads_kv", [1])
@pytest.mark.parametrize("head_dim", [128])
@pytest.mark.parametrize("num_caches", [16, 32, 192])
def test_slow_attention(
    seq_len: int,
    num_heads_q: int,
    num_heads_kv: int,
    head_dim: int,
    num_caches: int,
):
    torch.manual_seed(31415)
    torch.set_printoptions(precision=3, sci_mode=False, linewidth=250)

    dtype = torch.float16

    q = torch.empty(
        seq_len,
        num_heads_kv,
        num_heads_q // num_heads_kv,
        head_dim,
        device="cuda",
        dtype=dtype,
    )
    k_cache = torch.empty(
        num_caches + 1, seq_len, num_heads_kv, head_dim, device="cuda", dtype=dtype
    )
    v_cache = torch.empty(
        num_caches + 1, seq_len, num_heads_kv, head_dim, device="cuda", dtype=dtype
    )

    init_range = 0.2
    q.uniform_(-init_range, init_range)
    k_cache.uniform_(-init_range, init_range)
    v_cache.uniform_(-init_range, init_range)

    cache_idxs = torch.zeros(seq_len, dtype=torch.int32, device="cuda")
    cache_pos = torch.arange(seq_len, dtype=torch.int32, device="cuda")

    out = cached_attention._causal_attention_no_flash(
        q=q,
        k=k_cache,
        v=v_cache,
        cache_idxs=cache_idxs,
        cache_pos=cache_pos,
        softmax_scale=1.0,
    )

    ref = flash_attn.flash_attn_with_kvcache(
        q=q.reshape(seq_len, 1, num_heads_q, head_dim),
        k_cache=k_cache,
        v_cache=v_cache,
        k=None,
        v=None,
        cache_seqlens=cache_pos + 1,
        cache_batch_idx=cache_idxs,
        softmax_scale=1.0,
        causal=True,
    )

    torch.testing.assert_close(out, ref, atol=1e-3, rtol=1e-3)

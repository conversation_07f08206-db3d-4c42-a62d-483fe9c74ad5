# A script to generate diffs for all the parquet files under a directory in parallel
from pathlib import Path
from experimental.vaibhav.next_edit_descriptions.generate_diffs import (
    get_truncated_tuple,
)
from research.data.spark.pipelines.utils import map_parquet
import pickle
import pandas as pd
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import get_session


def get_all_diffs(
    input_path: str,
    output_path: str,
    spark,
    task_info_location: str | Path | None = None,
):
    def get_diffs_from_bytes(pickled_results: bytes) -> pd.Series:
        problems = pickle.loads(pickled_results)
        diffs: list[tuple[str, str, str]] = get_truncated_tuple(problems)
        return pd.Series(
            {"num_problems": len(diffs), "pickled_results": pickle.dumps(diffs)}
        )

    return map_parquet.apply(
        spark,
        get_diffs_from_bytes,
        input_path,
        output_path,
        input_columns=["pickled_results"],
        task_info_location=task_info_location,
        batch_size=1,
        ignore_error=True,
        row_size_limit_mb=100,
    )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_path",
        type=str,
        required=True,
        help="The input path to the parquet files.",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        required=True,
        help="Directory in which to save the parquet files.",
    )
    args = parser.parse_args()
    output_path = Path(args.output_path).expanduser()
    output_path.mkdir(parents=True, exist_ok=True)

    with get_session(
        use_gpu=False,
        name=str(output_path),
        max_workers=128,
    ) as spark:
        task_info_location = output_path.with_name(f"{output_path.name}.logs")
        get_all_diffs(
            map_parquet.to_gcp_path(args.input_path),
            map_parquet.to_gcp_path(str(output_path)),
            spark,
            task_info_location,
        )

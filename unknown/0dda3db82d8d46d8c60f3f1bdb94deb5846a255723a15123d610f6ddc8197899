"""Test for the component registry module."""

import abc
from dataclasses import dataclass
from typing import Iterable, Optional

import pytest
from dataclasses_json import DataClassJsonMixin

from base.component_registry import (
    ComponentConfig,
    ComponentMap,
    ComponentRegistry,
    create_components,
    ref_field,
)
from base.component_registry.component_registry import (
    ComponentValidationError,
    serialize_component_configs,
)

ExampleRegistry = ComponentRegistry()


class Widget(abc.ABC):
    """A widget base class.

    This widget is used to test the component registry, and returns a list of its
    parameters.
    """

    @abc.abstractmethod
    def params(self) -> Iterable[str]:
        """Return the parameters of the widget."""
        raise NotImplementedError()


class SimpleWidget(Widget):
    """A simple widget with a single parameter."""

    def __init__(self, param: str):
        self.param = param

    def params(self) -> Iterable[str]:
        return [self.param]

    def create_config(self):
        return {"param": self.param}

    # Implementing eq (and hash) for tests below.
    def __eq__(self, other):
        return isinstance(other, SimpleWidget) and self.param == other.param

    def __hash__(self):
        return hash(self.param)


@dataclass
class SimpleConfig(ComponentConfig):
    """Configuration for SimpleWidget."""

    param: str


@ExampleRegistry.register_component(SimpleConfig, SimpleWidget)
def create_widget(cfg: SimpleConfig, _) -> SimpleWidget:
    return SimpleWidget(cfg.param)


class ComplexWidget(Widget):
    """A complex widget exhibiting all the different reference types."""

    @dataclass
    class Config(ComponentConfig):
        """Configuration for ComplexWidget."""

        a_widget_ref: str = ref_field(Widget)
        maybe_widget_ref: Optional[str] = ref_field(Widget, allow_unset=True)
        widget_refs: list[str] = ref_field(list[Widget], default=[])
        widget_map_refs: dict[str, str] = ref_field(dict[str, Widget], default={})

    def __init__(
        self,
        a_widget: Widget,
        maybe_widget: Optional[Widget],
        widgets: list[Widget],
        widget_map: dict[str, Widget],
    ):
        self.a_widget = a_widget
        self.maybe_widget = maybe_widget
        self.widgets = widgets
        self.widget_map = widget_map

    def params(self) -> Iterable[str]:
        yield from self.a_widget.params()
        if self.maybe_widget is not None:
            yield from self.maybe_widget.params()
        for widget in self.widgets:
            yield from widget.params()
        for widget in self.widget_map.values():
            yield from widget.params()

    def create_config(self):
        return {
            "a_widget": self.a_widget,
            "maybe_widget": self.maybe_widget,
            "widgets": self.widgets,
            "widget_map": self.widget_map,
        }

    # Implementing eq (and hash) for tests below.
    def __eq__(self, other):
        return (
            isinstance(other, ComplexWidget)
            and self.a_widget == other.a_widget
            and self.maybe_widget == other.maybe_widget
            and self.widgets == other.widgets
            and self.widget_map == other.widget_map
        )

    def __hash__(self):
        return hash(
            (
                self.a_widget,
                self.maybe_widget,
                tuple(self.widgets),
                tuple(self.widget_map.items()),
            )
        )


@ExampleRegistry.register_component(ComplexWidget.Config, ComplexWidget)
def create_complex_widget(
    cfg: ComplexWidget.Config, components: ComponentMap
) -> ComplexWidget:
    return ComplexWidget(
        a_widget=components.get_with_type(cfg.a_widget_ref, Widget),
        maybe_widget=(
            components.get_with_type(cfg.maybe_widget_ref, Widget)
            if cfg.maybe_widget_ref is not None
            else None
        ),
        widgets=[components.get_with_type(name, Widget) for name in cfg.widget_refs],
        widget_map={
            key: components.get_with_type(name, Widget)
            for key, name in cfg.widget_map_refs.items()
        },
    )


class NotAWidget:
    """Example of something that isn't `Widget` to test type validation."""

    @dataclass
    class Config(ComponentConfig):
        """Configuration for NotAWidget."""

        pass


@ExampleRegistry.register_component(NotAWidget.Config, NotAWidget)
def create_not_a_widget(cfg: NotAWidget.Config, _) -> NotAWidget:
    del cfg
    return NotAWidget()


# Tests begin here.
def test_components_registered():
    """Test that the components are registered in the registry."""
    assert SimpleWidget in ExampleRegistry
    assert ComplexWidget in ExampleRegistry
    assert NotAWidget in ExampleRegistry


def test_create_components():
    configs = {
        "simple1": {"$component_name": "SimpleWidget", "param": "foo"},
        "simple2": {"$component_name": "SimpleWidget", "param": "bar"},
        "main_widget": {
            "$component_name": "ComplexWidget",
            "a_widget_ref": "$simple1",
            "maybe_widget_ref": "$simple2",
            "widget_refs": ["$simple1", "$simple2"],
            "widget_map_refs": {"foo": "$simple1", "bar": "$simple2"},
        },
    }

    components = create_components(configs, [ExampleRegistry])
    widget = components.get_with_type("main_widget", ComplexWidget)

    assert list(widget.params()) == ["foo", "bar", "foo", "bar", "foo", "bar"]


def test_create_components_legacy_syntax():
    configs = {
        "simple1": {"component_name": "SimpleWidget", "param": "foo"},
        "simple2": {"component_name": "SimpleWidget", "param": "bar"},
        "main_widget": {
            "component_name": "ComplexWidget",
            "a_widget_ref": "simple1",
            "maybe_widget_ref": "simple2",
            "widget_refs": ["simple1", "simple2"],
            "widget_map_refs": {"foo": "simple1", "bar": "simple2"},
        },
    }

    components = create_components(configs, [ExampleRegistry])
    widget = components.get_with_type("main_widget", ComplexWidget)

    assert list(widget.params()) == ["foo", "bar", "foo", "bar", "foo", "bar"]


def test_create_components_with_existing_components():
    configs = {
        "main_widget": {
            "$component_name": "ComplexWidget",
            "a_widget_ref": "$simple1",
        },
    }

    components = create_components(
        configs,
        [ExampleRegistry],
        ComponentMap(
            {
                "simple1": SimpleWidget("foo"),
            }
        ),
    )
    widget = components.get_with_type("main_widget", ComplexWidget)

    assert list(widget.params()) == ["foo"]


def test_missing_components():
    configs = {
        "main_widget": {
            "$component_name": "ComplexWidget",
            "a_widget_ref": "$missing",
        },
    }

    with pytest.raises(ComponentValidationError):
        create_components(configs, [ExampleRegistry])


def test_mistyped_components():
    configs = {
        "mistyped": {"$component_name": "NotAWidget"},
        "main_widget": {
            "$component_name": "ComplexWidget",
            "a_widget_ref": "$mistyped",
        },
    }

    with pytest.raises(ComponentValidationError):
        create_components(configs, [ExampleRegistry])


def test_cyclic_dependencies():
    configs = {
        "complex1": {
            "$component_name": "ComplexWidget",
            "a_widget_ref": "$complex2",
        },
        "complex2": {
            "$component_name": "ComplexWidget",
            "a_widget_ref": "$complex1",
        },
    }

    with pytest.raises(ComponentValidationError):
        create_components(configs, [ExampleRegistry])


def test_mistyped_existing_components():
    class NotAWidget:
        pass

    configs = {
        "main_widget": {
            "$component_name": "ComplexWidget",
            "a_widget_ref": "$simple1",
        },
    }

    with pytest.raises(ComponentValidationError):
        create_components(
            configs,
            [ExampleRegistry],
            existing_components=ComponentMap(
                {
                    "simple1": NotAWidget(),
                }
            ),
        )


def test_autoregister_simple():
    AutoRegistry = ComponentRegistry()
    AutoRegistry.autoregister(SimpleWidget)

    configs = {
        "simple1": {"$component_name": "SimpleWidget", "param": "foo"},
        "simple2": {"$component_name": "SimpleWidget", "param": "bar"},
    }

    components = create_components(configs, [AutoRegistry])
    assert components.get_with_type("simple1", SimpleWidget).param == "foo"
    assert components.get_with_type("simple2", SimpleWidget).param == "bar"


def test_autoregister_complex():
    AutoRegistry = ComponentRegistry()
    AutoRegistry.autoregister(SimpleWidget)
    AutoRegistry.autoregister(ComplexWidget)

    configs = {
        "simple1": {"$component_name": "SimpleWidget", "param": "foo"},
        "simple2": {"$component_name": "SimpleWidget", "param": "bar"},
        "main_widget": {
            "$component_name": "ComplexWidget",
            "a_widget": "$simple1",
            "maybe_widget": "$simple2",
            "widgets": ["$simple1", "$simple2"],
            "widget_map": {"foo": "$simple1", "bar": "$simple2"},
        },
    }

    components = create_components(configs, [AutoRegistry])
    widget = components.get_with_type("main_widget", ComplexWidget)

    assert list(widget.params()) == ["foo", "bar", "foo", "bar", "foo", "bar"]


def test_autoregister_all_types():
    @dataclass
    class TestWidget:
        @dataclass
        class Config(DataClassJsonMixin):
            """A configuration dataclass for testing."""

            field: str

        a_str: str
        a_int: int
        a_float: float
        a_bool: bool
        a_list: list[str]
        a_dict: dict[str, str]
        a_config: Config
        a_config_list: list[Config]
        a_config_dict: dict[str, Config]
        a_widget: SimpleWidget
        a_widget_list: list[SimpleWidget]
        a_widget_dict: dict[str, SimpleWidget]

    AutoRegistry = ComponentRegistry()
    AutoRegistry.autoregister(TestWidget)
    AutoRegistry.autoregister(SimpleWidget)

    configs = {
        "main_widget": {
            "$component_name": "TestWidget",
            "a_str": "foo",
            "a_int": 42,
            "a_float": 0.5,
            "a_bool": True,
            "a_list": ["foo", "bar"],
            "a_dict": {"foo": "bar"},
            "a_config": {"field": "foo"},
            "a_config_list": [{"field": "foo"}, {"field": "bar"}],
            "a_config_dict": {"foo": {"field": "foo"}, "bar": {"field": "bar"}},
            "a_widget": "$simple_foo",
            "a_widget_list": ["$simple_foo", "$simple_bar"],
            "a_widget_dict": {
                "foo": "$simple_foo",
                "bar": "$simple_bar",
            },
        },
        "simple_foo": {"$component_name": "SimpleWidget", "param": "foo"},
        "simple_bar": {"$component_name": "SimpleWidget", "param": "bar"},
    }

    components = create_components(configs, [AutoRegistry])
    widget = components.get_with_type("main_widget", TestWidget)

    simple_foo = SimpleWidget("foo")
    simple_bar = SimpleWidget("bar")

    assert widget == TestWidget(
        a_str="foo",
        a_int=42,
        a_float=0.5,
        a_bool=True,
        a_list=["foo", "bar"],
        a_dict={"foo": "bar"},
        a_config=TestWidget.Config(field="foo"),
        a_config_list=[
            TestWidget.Config(field="foo"),
            TestWidget.Config(field="bar"),
        ],
        a_config_dict={
            "foo": TestWidget.Config(field="foo"),
            "bar": TestWidget.Config(field="bar"),
        },
        a_widget=simple_foo,
        a_widget_list=[simple_foo, simple_bar],
        a_widget_dict={"foo": simple_foo, "bar": simple_bar},
    )


def test_autoregister_all_types_as_fn():
    @dataclass
    class Config(DataClassJsonMixin):
        """A configuration dataclass for testing."""

        field: str

    def make_widget_dict(
        a_str: str,
        a_int: int,
        a_float: float,
        a_bool: bool,
        a_list: list[str],
        a_dict: dict[str, str],
        a_config: Config,
        a_config_list: list[Config],
        a_config_dict: dict[str, Config],
        a_widget: SimpleWidget,
        a_widget_list: list[SimpleWidget],
        a_widget_dict: dict[str, SimpleWidget],
    ) -> dict:
        return {
            "a_str": a_str,
            "a_int": a_int,
            "a_float": a_float,
            "a_bool": a_bool,
            "a_list": a_list,
            "a_dict": a_dict,
            "a_config": a_config,
            "a_config_list": a_config_list,
            "a_config_dict": a_config_dict,
            "a_widget": a_widget,
            "a_widget_list": a_widget_list,
            "a_widget_dict": a_widget_dict,
        }

    AutoRegistry = ComponentRegistry()
    AutoRegistry.autoregister(make_widget_dict)
    AutoRegistry.autoregister(SimpleWidget)

    configs = {
        "main": {
            "$component_name": "make_widget_dict",
            "a_str": "foo",
            "a_int": 42,
            "a_float": 0.5,
            "a_bool": True,
            "a_list": ["foo", "bar"],
            "a_dict": {"foo": "bar"},
            "a_config": {"field": "foo"},
            "a_config_list": [{"field": "foo"}, {"field": "bar"}],
            "a_config_dict": {"foo": {"field": "foo"}, "bar": {"field": "bar"}},
            "a_widget": "$simple_foo",
            "a_widget_list": ["$simple_foo", "$simple_bar"],
            "a_widget_dict": {
                "foo": "$simple_foo",
                "bar": "$simple_bar",
            },
        },
        "simple_foo": {"$component_name": "SimpleWidget", "param": "foo"},
        "simple_bar": {"$component_name": "SimpleWidget", "param": "bar"},
    }

    components = create_components(configs, [AutoRegistry])
    widget = components.get_with_type("main", dict)

    simple_foo = SimpleWidget("foo")
    simple_bar = SimpleWidget("bar")

    assert widget == dict(
        a_str="foo",
        a_int=42,
        a_float=0.5,
        a_bool=True,
        a_list=["foo", "bar"],
        a_dict={"foo": "bar"},
        a_config=Config(field="foo"),
        a_config_list=[
            Config(field="foo"),
            Config(field="bar"),
        ],
        a_config_dict={
            "foo": Config(field="foo"),
            "bar": Config(field="bar"),
        },
        a_widget=simple_foo,
        a_widget_list=[simple_foo, simple_bar],
        a_widget_dict={"foo": simple_foo, "bar": simple_bar},
    )


def test_autoregister_unregistered_type():
    class Unregistered:
        def __init__(self, param: str):
            self.param = param

    @dataclass
    class TestWidget:
        unregistered: Unregistered

    AutoRegistry = ComponentRegistry()
    AutoRegistry.autoregister(TestWidget)

    configs = {
        "main_widget": {
            "$component_name": "TestWidget",
            "unregistered": "$foo",
        },
    }

    # Works with an existing component
    existing_foo = Unregistered("foo")
    components = create_components(
        configs, [AutoRegistry], ComponentMap({"foo": existing_foo})
    )
    widget = components.get_with_type("main_widget", TestWidget)
    assert widget == TestWidget(unregistered=existing_foo)

    # But fails without an existing component, because we don't know how to create it.
    with pytest.raises(ComponentValidationError):
        create_components(configs, [AutoRegistry])


def test_autoregister_fails_without_type_hints():
    class TestWidget:
        def __init__(self, unknown):
            self.unknown = unknown

    AutoRegistry = ComponentRegistry()
    with pytest.raises(ValueError):
        AutoRegistry.autoregister(TestWidget)


def test_serialize_simple():
    config = serialize_component_configs({"simple1": SimpleWidget("foo")})
    assert config == {
        "simple1": {
            "$component_name": f"{__name__}.SimpleWidget",
            "param": "foo",
        },
    }


def test_serialize_complex():
    simple1 = SimpleWidget("foo")
    simple2 = SimpleWidget("bar")
    complex = ComplexWidget(
        simple1, simple2, [simple1, simple2], {"foo": simple1, "bar": simple2}
    )

    config = serialize_component_configs(
        {
            "simple1": simple1,
            "complex": complex,
        }
    )
    assert config == {
        "simple1": {
            "$component_name": f"{__name__}.SimpleWidget",
            "param": "foo",
        },
        # simple2
        "complex/maybe_widget": {
            "$component_name": f"{__name__}.SimpleWidget",
            "param": "bar",
        },
        "complex": {
            "$component_name": f"{__name__}.ComplexWidget",
            "a_widget": "$simple1",
            "maybe_widget": "$complex/maybe_widget",
            "widgets": ["$simple1", "$complex/maybe_widget"],
            "widget_map": {"foo": "$simple1", "bar": "$complex/maybe_widget"},
        },
    }


def test_serialize_with_unserializable_type_raises():
    class Unserializable:
        pass

    class TestWidget:
        def create_config(self):
            return {"obj": Unserializable()}

    with pytest.raises(ValueError):
        serialize_component_configs(
            {
                "unserializable": TestWidget(),
            }
        )


def test_serialize_with_missing_field_raises():
    class ClassWithMissingField:
        def __init__(self, a_param: str, b_param: int):
            self.a_param = a_param
            self.b_param = b_param

        def create_config(self):
            # a is missing from config
            return {"a_param": self.a_param}

    with pytest.raises(ValueError):
        serialize_component_configs(
            {
                "missing_field": ClassWithMissingField("foo", 42),
            }
        )


def test_serialize_with_mistyped_field_raises():
    class ClassWithMisTypedField:
        def __init__(self, a_param: str, b_params: list[int]):
            self.a_param = a_param
            self.b_params = b_params

        def create_config(self):
            # a is missing from config
            return {
                "a_param": self.a_param,
                # Intentionally saving `a_param` which has the wrong type.
                "b_params": [self.a_param],
            }

    with pytest.raises(ValueError):
        serialize_component_configs(
            {
                "mistyped": ClassWithMisTypedField("foo", [42]),
            }
        )


def test_reconstruct_with_registry():
    Registry = ComponentRegistry()
    Registry.autoregister(SimpleWidget)
    Registry.autoregister(ComplexWidget)

    simple1 = SimpleWidget("foo")
    simple2 = SimpleWidget("bar")
    complex = ComplexWidget(
        simple1, simple2, [simple1, simple2], {"foo": simple1, "bar": simple2}
    )

    configs = serialize_component_configs(
        {
            "simple1": simple1,
            "simple2": simple2,
            "complex": complex,
        }
    )
    # Now try reconstructing them with the registry.
    components = create_components(configs, [Registry])
    assert components["simple1"] == simple1
    assert components["simple2"] == simple2
    assert components["complex"] == complex


def test_autoregister_with_optional():
    @dataclass
    class OptionalWidget:
        a_str: str | None = None

    Registry = ComponentRegistry()
    Registry.autoregister(OptionalWidget)

    configs = {
        "optional_widget": {
            "$component_name": "OptionalWidget",
            "a_str": "foobar",
        },
    }

    components = create_components(configs, [Registry])
    assert components["optional_widget"].a_str == "foobar"

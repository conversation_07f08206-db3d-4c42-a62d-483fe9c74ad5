import { describe, it, expect } from "vitest";
import { normalizeFilePath, getFileDirectory, getFilename } from "./file-paths";

describe("file-paths utilities", () => {
  describe("normalizeFilePath", () => {
    it("should convert backslashes to forward slashes", () => {
      expect(normalizeFilePath("path\\to\\file.txt")).toBe("path/to/file.txt");
    });

    it("should leave forward slashes unchanged", () => {
      expect(normalizeFilePath("path/to/file.txt")).toBe("path/to/file.txt");
    });

    it("should handle mixed slashes", () => {
      expect(normalizeFilePath("path\\to/file\\name.txt")).toBe("path/to/file/name.txt");
    });

    it("should handle empty string", () => {
      expect(normalizeFilePath("")).toBe("");
    });
  });

  describe("path component extraction", () => {
    it("should correctly extract filename from normalized path", () => {
      const path = "path/to/file.txt";
      expect(getFilename(normalizeFilePath(path))).toBe("file.txt");
    });

    it("should correctly extract directory from normalized path", () => {
      const path = "path/to/file.txt";
      expect(getFileDirectory(normalizeFilePath(path))).toBe("path/to");
    });

    it("should handle paths with backslashes", () => {
      const path = "path\\to\\file.txt";
      const normalized = normalizeFilePath(path);
      expect(getFilename(normalized)).toBe("file.txt");
      expect(getFileDirectory(normalized)).toBe("path/to");
    });
  });
});

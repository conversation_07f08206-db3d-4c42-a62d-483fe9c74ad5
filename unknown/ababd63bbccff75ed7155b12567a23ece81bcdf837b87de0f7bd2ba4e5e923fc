"""Tests for StarCoder2."""

from base.fastforward import cached_attention, fwd_model_test_utils


def test_generate_fp8(starcoder2_100m_fp8_fixture, starcoder2_test_data):
    step_fn, attn_factory, max_round_size = starcoder2_100m_fp8_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=starcoder2_test_data["inputs"],
        target_sequence=starcoder2_test_data["outputs"],
        extra_kv_len=max_round_size,  # for padding
    )


def test_generate_graphed(starcoder2_100m_fp8_graphed_fixture, starcoder2_test_data):
    step_fn, attn_factory, max_round_size = starcoder2_100m_fp8_graphed_fixture
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        given_prefix=starcoder2_test_data["inputs"],
        target_sequence=starcoder2_test_data["outputs"],
        extra_kv_len=max_round_size,  # for graph capturing
    )


def test_logits_are_close_to_bf16(
    starcoder2_100m_fp8_fixture, starcoder2_100m_bf16_fixture, starcoder2_test_data
):
    step_fn_e4m3, attn_factory_e4m3, max_round_size = starcoder2_100m_fp8_fixture
    step_fn_bf16, attn_factory_bf16 = starcoder2_100m_bf16_fixture

    if (
        attn_factory_e4m3._attention_impl
        == cached_attention.AttentionImpl.MULTI_REQUEST_FLASH_V3_FP8
    ):
        allowed_mismatches = 1
    else:
        allowed_mismatches = 0

    fwd_model_test_utils.check_logits_are_close(
        step_fn_1=step_fn_e4m3,
        step_fn_2=step_fn_bf16,
        attn_factory_1=attn_factory_e4m3,
        attn_factory_2=attn_factory_bf16,
        test_tokens=starcoder2_test_data["inputs"][:128],
        extra_kv_len=max_round_size,  # for padding
        allowed_mismatches=allowed_mismatches,
    )

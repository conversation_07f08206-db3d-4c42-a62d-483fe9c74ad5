#!/bin/bash
export PYTHONPATH=${PYTHONPATH}:${HOME}/augment:${HOME}/augment/research/gpt-neox/
export TOKENIZERS_PARALLELISM=false

# chdir to the main pipeline directory
cd "$(dirname "$0")"
cd ..

zip -q -r /tmp/stages.zip stages

cd ~/augment/research/gpt-neox
zip -q -r /tmp/megatron.zip megatron
cd - > /dev/null

time python3.9 tokenize_language_by_language.py \
    --config configs/tokenize_the_stack.yaml \
    --py_file /tmp/stages.zip \
    --py_file /tmp/megatron.zip \
    |& tee log.txt

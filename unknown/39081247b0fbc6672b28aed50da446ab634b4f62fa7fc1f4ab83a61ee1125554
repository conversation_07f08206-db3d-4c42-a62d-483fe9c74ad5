# Test Viewer UI

This package contains the test viewer web UI, e.g. to browse test results.

# Local Development

- Deploy development setup
- Forward test-viewer-prc pod's rpc port with `kubectl port-forward [TEST-RPC-POD-IN-DEV-NAMESPACE] 50051:50051` or `click`
- Run `bazel run //tools/bazel_runner/web/backend`
- Goto `tools/bazel_runner/web/frontend` and run `npm run start`.
- Forward port `3000` from AWS dev VM to Laptop via the vscode port-forwarding
- Visit `http://127.0.0.1:3000/`

Any changes in the backend Python code as well as the Typescript frontend code are automatically reflected.

# Kubecfg

A custom `kubecfg`-like executable for applying our `*.jsonnet` deployment files
to Kubernetes. This is the tool that's used by the `kubecfg` Bazel macro.

NOTE: this `kubecfg` does *NOT* have the same interface as the open source
`kubecfg` tool you'll find on the internet. For example: one notable difference
is that the open source `kubecfg` only accepts jsonnet files where the top-level
expression is an object. This `kubecfg` accepts `*.jsonnet` files where the
top-level expression is a function, handled by invoking the `jsonnet`
interpreter with additional top-level arguments.

Another is that this implementation allows Kubernetes `Container` resource
definitions to specify a `target` key instead of an `image` key. This is used to
specify a Bazel target that builds the container image to be used.

## Utils

The utils are CLI applications that can be used to interact with kubecfg targets.

- kubecfg_util: CLI application for kubecfg targets.
- kubecfg_deletion_util: CLI application for kubecfg tombstoning.

In development, these utils can be used.

In production, one should not use these CLI directly and use the CI/CD pipeline instead.
During emergencies, e.g. active outages, the utils can in some cases be used directly.

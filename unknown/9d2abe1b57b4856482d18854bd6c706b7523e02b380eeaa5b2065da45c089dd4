"""Prompt formatter for next edit diff descriptions."""

from dataclasses import dataclass
from typing import (
    Protocol,
)

from dataclasses_json import dataclass_json

from base.diff_utils.diff_utils import File, compute_file_diff
from base.prompt_format_chat.lib.token_counter import (
    TokenizerBasedTokenCounter,
)
from base.prompt_format_chat.lib.truncation_utils import head_n_lines
from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput
from base.prompt_format_next_edit.common import NextEditPromptInput
from base.tokenizers import Tokenizer


@dataclass(frozen=True)
class EditDescriptionPromptInput(NextEditPromptInput):
    replacement_text: str
    """The generated text to replace the selected region."""


class EditDescriptionPromptFormatter(Protocol):
    """Protocol for edit description prompt formatters."""

    tokenizer: Tokenizer
    """The tokenizer used to tokenize the prompt."""

    def format_input(
        self,
        prompt_input: EditDescriptionPromptInput,
    ) -> StructuredChatPromptOutput:
        """Format the input into a structured chat prompt."""
        raise NotImplementedError()


@dataclass
class RavenDescribePromptFormatter(EditDescriptionPromptFormatter):
    """Prompt formatter for edit generation."""

    @dataclass_json
    @dataclass
    class Config:
        max_prompt_tokens: int = 2048
        """The maximum number of tokens to be used by the input prompt."""

        diff_context_lines: int = 5
        """The number of hunk context lines to show in the diff section."""

        use_descriptions_v2_model: bool = False
        """Whether to use the newly trained model. Temporary variable."""

    tokenizer: Tokenizer
    config: Config

    system_prompt = """\
Your task: Create a concise, imperative description of the code change below, such that a developer can use the summary to decide whether to review the change in detail.

Guidelines:
- Start with an action verb (Add, Update, Remove, Fix, Refactor, Format, etc.)
- Maximum 10 words in a single line only
- Focus on WHAT is changing, not WHERE in the codebase
- Look out for comments changes and just give a simple description in such a case.
- For formatting changes just say: "Reformat code"
- Summarize the most important details if there is space otherwise focus on the high level change
"""
    message = """\
Here is the code change delimited by triple backticks:
```
{diff_str}
```
ONLY respond with the description.
"""

    def __post_init__(self):
        assert self.config.max_prompt_tokens > 0
        self._token_counter = TokenizerBasedTokenCounter(self.tokenizer)

    def format_input_from_diff_str(self, diff_str: str) -> StructuredChatPromptOutput:
        """Format the input into a structured chat prompt."""
        return StructuredChatPromptOutput(
            system_prompt=self.system_prompt,
            message=self.message.format(diff_str=diff_str),
            chat_history=[],
            retrieved_chunks_in_prompt=[],
        )

    def format_input(
        self, prompt_input: EditDescriptionPromptInput
    ) -> StructuredChatPromptOutput:
        """Format the input into a structured chat prompt."""

        before_file = File(
            path=prompt_input.current_path,
            contents=prompt_input.current_code,
        )
        after_file = File(
            path=prompt_input.current_path,
            contents=(
                prompt_input.prefix
                + prompt_input.replacement_text
                + prompt_input.suffix
            ),
        )

        diff_str = compute_file_diff(
            before_file=before_file,
            after_file=after_file,
            use_smart_header=True,
            num_context_lines=self.config.diff_context_lines,
        )

        budget = self.config.max_prompt_tokens
        budget -= self._token_counter.count_tokens(self.system_prompt)
        # We'll use an empty diff_str to budget for the template.
        budget -= self._token_counter.count_tokens(self.message.format(diff_str=""))

        # TODO(arun): Include "... n more lines." when truncating.
        diff_str = head_n_lines(
            diff_str,
            max_toks=budget,
            token_counter=self._token_counter,
        )

        return self.format_input_from_diff_str(diff_str)

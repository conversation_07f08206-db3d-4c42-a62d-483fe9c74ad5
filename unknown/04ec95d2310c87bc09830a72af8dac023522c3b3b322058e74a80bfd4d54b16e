"""Unit tests for the chunker factory."""

import pytest

from models.retrieval.chunking import chunking, docset_chunkers
from models.retrieval.chunking.line_level_chunker import LineLevelChunker
from models.retrieval.chunking.signature_chunker import SignatureChunker
from models.retrieval.chunking.smart_line_chunker import SmartLineChunker
from models.retrieval.chunking.transform_chunker import TransformChunker
from services.embeddings_indexer import chunker_factory


def get_inner_chunker(chunker: chunking.Chunker) -> chunking.Chunker:
    """Get the inner chunker from a chunker."""
    if isinstance(chunker, docset_chunkers.DocsetPreservingChunker):
        return chunker.inner_chunker
    elif isinstance(chunker, docset_chunkers.DocsetSkippingChunker):
        return chunker.inner_chunker
    else:
        return chunker


def test_create_line_level_chunker():
    """Test that we can create a line level chunker."""
    config = chunker_factory.ChunkerConfig(
        name="line_level",
        config={
            "max_lines_per_chunk": 5,
            "max_chunk_size": 1024,
        },
    )
    chunker = chunker_factory.create_chunker(config)
    chunker = get_inner_chunker(chunker)
    assert isinstance(chunker, LineLevelChunker)
    assert chunker.max_lines_per_chunk == 5  # type: ignore
    assert chunker.max_chunk_size == 1024  # type: ignore


@pytest.mark.parametrize(
    "values, expected",
    [
        ({}, {"max_docstr_chars": 0, "show_private_members": False}),
        (
            {"max_docstr_chars": 100, "show_private_members": True},
            {"max_docstr_chars": 100, "show_private_members": True},
        ),
    ],
)
def test_create_signature_chunker(values, expected):
    """Test that we can create a signature chunker."""
    config = chunker_factory.ChunkerConfig(
        name="signature_raw",
        config=values,
    )
    chunker = chunker_factory.create_chunker(config)
    chunker = get_inner_chunker(chunker)
    assert isinstance(chunker, SignatureChunker)
    assert chunker.max_docstr_chars == expected["max_docstr_chars"]
    assert chunker.show_private_members is expected["show_private_members"]


def test_create_smart_line_level_chunker():
    """Test that we can create a smart line level chunker."""
    config = chunker_factory.ChunkerConfig(
        name="smart_line_level",
        config={
            "max_chunk_chars": 1024,
            "max_headers": 3,
        },
    )
    chunker = chunker_factory.create_chunker(config)
    chunker = get_inner_chunker(chunker)
    assert isinstance(chunker, SmartLineChunker)
    assert chunker._internal_chunker.max_chunk_chars == 1024  # type: ignore
    assert chunker._internal_chunker.max_headers == 3  # type: ignore


def test_create_transform_line_level_chunker():
    """Test that we can create a line level chunker."""
    config = chunker_factory.ChunkerConfig(
        name="transform_line_level",
        config={
            "max_lines_per_chunk": 5,
            "max_chunk_size": 1024,
        },
    )
    chunker = chunker_factory.create_chunker(config)
    chunker = get_inner_chunker(chunker)
    assert isinstance(chunker, TransformChunker)
    assert chunker.chunker.max_lines_per_chunk == 5  # type: ignore
    assert chunker.chunker.max_chunk_size == 1024  # type: ignore


def test_create_transform_smart_line_level_chunker():
    """Test that we can create a smart line level chunker."""
    config = chunker_factory.ChunkerConfig(
        name="transform_smart_line_level",
        config={
            "max_chunk_chars": 1024,
            "max_headers": 3,
        },
    )
    chunker = chunker_factory.create_chunker(config)
    chunker = get_inner_chunker(chunker)
    assert isinstance(chunker, TransformChunker)
    assert chunker.chunker._internal_chunker.max_chunk_chars == 1024  # type: ignore
    assert chunker.chunker._internal_chunker.max_headers == 3  # type: ignore


def test_create_unknown_chunker():
    """Test that we fail on an unknown value."""
    config = chunker_factory.ChunkerConfig(
        name="unknown",
        config={},
    )
    with pytest.raises(ValueError):
        chunker_factory.create_chunker(config)

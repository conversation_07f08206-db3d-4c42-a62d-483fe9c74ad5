#!/usr/bin/env python

import argparse
import subprocess
import sys
from pathlib import Path

import yaml

AUGMENT = "/home/<USER>/.local/bin/augment"


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--json",
        type=str,
        default="-",
        required=False,
        help="Path to the json file with the pyright output; omit for stdin",
    )
    parser.add_argument(
        "--model",
        type=str,
        default="",
        required=False,
        help="The name of the model to use",
    )
    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="Whether to overwrite the input file.",
    )
    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="Just show the commands to run.",
    )

    return parser.parse_args()


def main(args: argparse.Namespace) -> None:
    if args.json == "-":
        data = yaml.safe_load(sys.stdin)
    else:
        with open(args.json, "r") as f:
            data = yaml.safe_load(f)

    git_tl = subprocess.check_output(
        ["git", "rev-parse", "--show-toplevel"], text=True
    ).strip()
    summary = data["summary"]
    print(
        f"Attempting to fix {summary['errorCount']} errors in {summary['filesAnalyzed']} files"
    )
    for error in data["generalDiagnostics"]:
        file = Path(error["file"]).relative_to(git_tl)
        message = error["message"]
        line = error["range"]["start"]["line"]

        cmd = [
            AUGMENT,
            "--file-line",
            f"{file}:{line}",
        ]
        if args.overwrite:
            cmd.extend(["--output", "overwrite"])
        if args.model:
            cmd.extend(["--model", args.model])

        # This ordering makes the output easier to read
        cmd.append(f"Fix the following error: {message}")
        print(f"Running '{' '.join(cmd)}'")
        if not args.dry_run:
            subprocess.run(cmd, check=True)
        print("\n\n")
    print(
        f"Attempting to fix {summary['errorCount']} errors in {summary['filesAnalyzed']} files"
    )


if __name__ == "__main__":
    args = parse_args()
    main(args)

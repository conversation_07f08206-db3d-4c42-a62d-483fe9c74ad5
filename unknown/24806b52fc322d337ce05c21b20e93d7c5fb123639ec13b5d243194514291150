<script lang="ts">
  import { onMount } from "svelte";
  import type { IEditorView } from "$common-webviews/src/common/components/inputs/types";
  export let view: IEditorView;

  let element: HTMLDivElement | undefined;
  onMount(() => {
    // Element will always exist on mount
    element!.appendChild(view.domElement);
    return () => element?.removeChild(view.domElement);
  });
</script>

<!-- Container for positioning rich text components inside the input -->
<div class="c-rich-text-input" data-testid="rich-text-input" bind:this={element} />

<style>
  .c-rich-text-input,
  .c-rich-text-input > :global(*) {
    position: relative;

    /* No horizontal scroll */
    width: 100%;
    height: 100%;
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  /* Remove top margin from first child for better scrolling behavior */
  .c-rich-text-input :global(.tiptap > :first-child) {
    margin-top: 0;
  }
  /* Remove bottom margin from last child for better scrolling behavior */
  .c-rich-text-input :global(.tiptap > :last-child) {
    margin-bottom: 0;
  }

  /* Needed for placeholder support. See
    https://tiptap.dev/docs/editor/api/extensions/placeholder
    for more details */
  .c-rich-text-input :global(.tiptap p.is-editor-empty:first-child::before) {
    opacity: 0.5;
    color: var(--augment-foreground);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
</style>

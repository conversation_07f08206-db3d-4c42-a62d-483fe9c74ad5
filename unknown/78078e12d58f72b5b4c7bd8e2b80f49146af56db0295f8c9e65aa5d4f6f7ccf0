"""Tests for base.fastforward.fwd_utils."""

from unittest.mock import MagicMock

import pytest
import torch

from base.fastforward import fwd_torch
from base.fastforward import cached_attention
from base.fastforward import fwd_utils
from base.fastforward.llama import fwd_llama
from base.fastforward.llama import conftest as llama_conftest


@pytest.mark.parametrize(
    "round_sizes",
    [
        [1],
        [1, 2],
        [2],
        [3],
        [16],
        [4, 8, 16],
    ],
)
def test_pad_and_step(round_sizes: list[int]):
    """Test that pad_and_step works as expected."""
    _, step_fn, attn_factory = llama_conftest.create_llama_350m()
    attn = attn_factory(128)
    padded_step = fwd_utils.pad_and_step(step_fn, round_sizes=round_sizes)
    tokens = fwd_llama.LLAMA_350M_PROMPT_TOKS
    # to make sure  that anyone who changes the llama standard prompts gets
    # notified that this test depends on the prompt length:
    assert len(tokens) == 11
    logits = padded_step(tokens, attn).checked_cast(torch.Tensor)
    assert logits.shape[0] == 11  # ensure we return the unpadded length
    greedy_tokens = torch.argmax(logits, dim=-1).tolist()
    expected_token = fwd_llama.LLAMA_350M_OUTPUT_TOKS[0]
    assert greedy_tokens[-1] == expected_token


@pytest.mark.parametrize(
    "logits, targets, expected_ppl",
    [
        ([[-1000.0, 1.0]], [1], 0),
        ([[-1000.0, 1.0], [-1000.0, 0.0]], [1, 1], 0),
        ([[1.0, 1.0]], [1], 0.693147),  # -ln(0.5)
        # two steps are averaged:
        ([[1.0, 1.0], [1.0, 1.0]], [1, 1], 0.693147),
        ([[1.0, 1.0], [1.0, 1.0]], [1, 0], 0.693147),
        ([[0.0, 1.0]], [1], 0.3132617),  # lower than -ln(0.5)
    ],
)
def test_log_perplexity(
    logits: list[list[float]], targets: list[int], expected_ppl: float
):
    # Mock the input so we get logits from step.
    inputs = [0] * (len(logits) - len(targets) + 1)
    attn = MagicMock(cached_attention.Attention)

    def step(_, __):
        return fwd_torch.TorchLogits2D(torch.tensor(logits, device="cuda"))

    log_ppl = fwd_utils.log_perplexity(step, attn, inputs + targets)
    assert log_ppl == pytest.approx(expected_ppl)


@pytest.mark.parametrize(
    "logits, targets, expected_ppl",
    [
        ([[-1000.0, 1.0]], [1], 0),
        ([[-1000.0, 1.0], [-1000.0, 0.0]], [1, 1], 0),
        ([[1.0, 1.0]], [1], 0.693147),  # -ln(0.5)
        # two steps are averaged:
        ([[1.0, 1.0], [1.0, 1.0]], [1, 1], 0.693147),
        ([[1.0, 1.0], [1.0, 1.0]], [1, 0], 0.693147),
        ([[0.0, 1.0]], [1], 0.3132617),  # lower than -ln(0.5)
    ],
)
def test_log_perplexity_with_continuation(
    logits: list[list[float]], targets: list[int], expected_ppl: float
):
    # Mock the input so we get logits from step.
    inputs = [0] * (len(logits) - len(targets) + 1)
    attn = MagicMock(cached_attention.Attention)

    def step(_, __):
        return fwd_torch.TorchLogits2D(torch.tensor(logits, device="cuda"))

    log_ppl = fwd_utils.log_perplexity_continuation(step, attn, inputs, targets)
    assert log_ppl == pytest.approx(expected_ppl)

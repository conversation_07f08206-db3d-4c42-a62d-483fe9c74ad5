import { type AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { type MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { type IRemoteAgentDiffPanelOptions } from "$vscode/src/remote-agent-manager/types";
import {
  type RemoteAgentDiffPanelSetOptsMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { type Readable } from "svelte/motion";

/**
 * Model for the state management of the remote agents diff panel.
 *
 * The diff functionality is implemented in the RemoteAgentDiffOpsModel.
 */
export class RemoteAgentDiffModel implements Readable<RemoteAgentDiffModel>, MessageConsumer {
  public static key = "remoteAgentDiffModel"; // for svelte context
  private _opts: IRemoteAgentDiffPanelOptions | null = null;

  private _subscribers = new Set<(value: RemoteAgentDiffModel) => void>();
  subscribe(run: (value: RemoteAgentDiffModel) => void): () => void {
    this._subscribers.add(run);
    run(this);

    return () => {
      this._subscribers.delete(run);
    };
  }
  private notifySubscribers() {
    this._subscribers.forEach((sub) => sub(this));
  }

  constructor(private readonly _asyncMsgSender: AsyncMsgSender) {}

  public get opts() {
    return this._opts;
  }

  public updateOpts(opts: IRemoteAgentDiffPanelOptions | null) {
    this._opts = opts;
    this.notifySubscribers();
  }

  async onPanelLoaded() {
    try {
      this.updateOpts(null);
      const response: RemoteAgentDiffPanelSetOptsMessage = await this._asyncMsgSender.send({
        type: WebViewMessageType.remoteAgentDiffPanelLoaded,
      });
      this.updateOpts(response.data);
    } catch (err) {
      console.error("Failed to load diff panel:", err);
      this.updateOpts(null);
    }
  }

  handleMessageFromExtension(msg: any): boolean {
    const message = msg.data;
    if (!message || !message.type) {
      return false;
    }
    switch (message.type) {
      case WebViewMessageType.remoteAgentDiffPanelSetOpts:
        this.updateOpts(message.data);
        return true;
      default:
        return false;
    }
  }
}

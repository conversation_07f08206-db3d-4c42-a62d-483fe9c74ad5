package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

type Config struct {
	DryRun           bool     `json:"dry_run"`
	Bucket           string   `json:"bucket"`
	PrefixesToDelete []string `json:"prefixes_to_delete"`
	Directory        string   `json:"directory"`
}

func (c *Config) Validate() error {
	if c.Bucket == "" {
		return fmt.Errorf("bucket is required")
	}
	if c.Directory == "" {
		return fmt.Errorf("directory is required")
	}
	return nil
}

type Run interface {
	Run(dir string, args ...string) error
}

type ProcessRun struct {
}

func (r *ProcessRun) Run(dir string, args ...string) error {
	log.Info().Msgf("Running %v", args)
	c := exec.Command(args[0], args[1:]...)
	c.Dir = dir
	c.Stdout = os.Stdout
	c.Stderr = os.Stderr
	err := c.Run()
	if err != nil {
		return err
	}
	return nil
}

func logDiskSpace(runner Run, directory string) error {
	log.Info().Msg("Checking disk space...")
	return runner.Run(directory, "df", "-h")
}

func SyncCheckpoints(config Config, runner Run) error {
	// Log initial disk space
	if err := logDiskSpace(runner, config.Directory); err != nil {
		log.Warn().Err(err).Msg("Failed to check initial disk space")
	}

	base_args := []string{"/usr/local/google-cloud-sdk/bin/gsutil", "-m", "rsync", "-r", "-x", ".*\\/code\\/.*"}
	if config.DryRun {
		base_args = append(base_args, "-n")
	}

	// Sync bucket
	log.Info().Msgf("Syncing %s to %s", config.Bucket, config.Directory)
	args := append(base_args, config.Bucket, ".")
	err := runner.Run(config.Directory, args...)
	if err != nil {
		return err
	}

	log.Info().Msg("Finished syncing bucket")

	// Delete prefixes
	for _, prefix := range config.PrefixesToDelete {
		if filepath.IsAbs(prefix) {
			log.Warn().Msgf("Skipping absolute path in PrefixesToDelete: %s", prefix)
			continue
		}
		fullPath := filepath.Join(config.Directory, prefix)

		// Check if the directory exists before attempting to delete
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			log.Info().Msgf("Directory %s does not exist, skipping deletion", fullPath)
			continue
		}

		log.Info().Msgf("Deleting %s", fullPath)
		if !config.DryRun {
			err := os.RemoveAll(fullPath)
			if err != nil {
				return fmt.Errorf("error deleting %s: %v", fullPath, err)
			}
			log.Info().Msgf("Successfully deleted %s", fullPath)
		} else {
			log.Info().Msgf("Dry run: would delete %s", fullPath)
		}
	}

	log.Info().Msgf("Finished deleting %d prefixes", len(config.PrefixesToDelete))

	// Log final disk space after all operations
	if err := logDiskSpace(runner, config.Directory); err != nil {
		log.Warn().Err(err).Msg("Failed to check final disk space")
	}

	return nil
}

func main() {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	var configPath string
	flag.StringVar(&configPath, "config", "", "Path to the config file")
	flag.Parse()

	if configPath == "" {
		log.Fatal().Msg("Missing config file")
	}

	// Load configuration
	configFile, err := os.Open(configPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer configFile.Close()

	configData, err := io.ReadAll(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading config file")
	}

	var config Config
	if err := json.Unmarshal(configData, &config); err != nil {
		log.Fatal().Err(err).Msg("Error unmarshalling config file")
	}
	log.Info().Msgf("Config: %v", config)

	err = config.Validate()
	if err != nil {
		log.Fatal().Err(err).Msg("Error in config file")
	}

	runner := &ProcessRun{}
	err = SyncCheckpoints(config, runner)
	if err != nil {
		log.Fatal().Err(err).Msg("Error syncing checkpoints")
	}

}

#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      # name: codegen-16b-indiana
      # name: codegen-2b-indiana
      # checkpoint_path: indiana-2b-001
      # name: starcoderbase
      name: codegen-350m-multi
      # name: codegen-2B-multi
      # name: codegen-6B-multi
      # name: codegen-16b-multi
      prompt:
        max_prefix_tokens: 1920
        max_suffix_tokens: 0
        max_prompt_tokens: 1920

        # starcoder
        # max_prefix_tokens: 8064
        # max_suffix_tokens: 0
        # max_prompt_tokens: 8064

        # retrieval_layout_style: comment
        # Starcoder doesn't like this
        # fill_to_context_window: True
        # retrieval_layout_style: comment
        # max_retrieved_chunk_tokens: 884
      # Model does validation of inputs so only want arguments the model recognizes
      # neox_args:
      #   # model_parallel_size: 1
    generation_options:
      temperature: 0
      # best for pass@1
      # temperature: 0.2
      # top_p: 0.95
      # top_k: 0
      max_generated_tokens: 128
    retriever:
      name: null
      chunker: line_level
      max_chunk: 20
    # retriever:
    #   name: bm25
    #   chunker: line_level
    #   max_chunk: 20
    # retriever:
    #   name: bm25
    #   chunker: scope_aware
    #   max_chunk: 20
    experimental:
      # remove_suffix: True
      retriever_top_k: 25
      #trim_on_dedent: True
      #trim_on_max_lines: null
      # trim_trailing_newline_on_prefix: True


# Tasks
#   specify the evaluation tasks for each checkpoint
#
tasks:
  # - name: hydra
  #   # dataset: function
  #   limit: 10
  #   exec: False
  # - name: humaneval
  - name: mbpp
    # dataset: function
    # limit: 10
    iterations: 1
    exec: True

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml
# podspec: 1xA100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: MBPP - Codegen 350M, Temp 0
  workspace: Dev
  project: rich
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

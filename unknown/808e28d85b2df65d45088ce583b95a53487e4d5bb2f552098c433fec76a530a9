"""Module containing the config for the deploy pod."""

from dataclasses import dataclass
from typing import Optional
import pathlib

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class PubSubConfig:
    """Configuration for the pubsub."""

    project_id: str
    topic_name: str


@dataclass_json
@dataclass
class Config:
    """Configuration for the server."""

    # unique id of the deployment
    deploy_id: str

    # the base directory to use
    base_directory: str

    # the repo to use
    repo_owner: str

    # the repo to use
    repo_name: str

    # the branch to deploy
    # this is mutually exclusive with pr
    branch: Optional[str]

    # the pull request to deploy
    # this is mutually exclusive with branch
    pull_request_number: Optional[str]

    # the path to the github app. The directory should contain
    # the app id, installation id, and the private key.
    #
    # if this is set to the empty string, the deploy job will not use github and
    # the current checkout will be used for deployment
    github_app_path: str

    # the number of minutes to pause after staging
    pause_after_staging_minutes: int

    # filters targets by cloud name
    target_clouds: list[str]

    # filters targets by namespace
    target_namespaces: list[str]

    # filters targets by environment
    target_envs: list[str]

    # filters the target by name.
    #
    # The name has to be an exact match. If it is set to None, no name-based
    # filtering will be done
    target_names: list[str]

    allow_rollback: bool

    # the deployment schedule name to filter with.
    # none means no filtering. In this case, target_names should be set
    deployment_schedule_name: str | None

    num_parallel_targets: int = 1

    # extra arguments to pass to the deployer
    extra_args: str = ""
    extra_startup_args: str = ""

    # if set to true, will not actually deploy
    dry_run: bool = False

    # the name of the cloud the job is running in
    cloud: str = ""

    # The amount of RAM to limit builds
    ram_limit_gb: Optional[int] = None

    # The number of CPUs to limit builds
    cpu_limit: Optional[int] = None

    # the git reference to deploy
    ref: Optional[str] = None

    pubsub: Optional[PubSubConfig] = None

    minimal_free_disk_gb: int = 100

    abort_on_deploy_gate_failure: bool = False

    @classmethod
    def load_config_from_str(cls, config: str) -> "Config":
        """Loads the configuration from a string."""
        return cls.schema().loads(config)  # pylint: disable=no-member # type: ignore

    @classmethod
    def load_config(cls, config_path: pathlib.Path) -> "Config":
        """Loads the configuration from a string."""
        config = config_path.read_text()
        return cls.schema().loads(config)  # pylint: disable=no-member # type: ignore

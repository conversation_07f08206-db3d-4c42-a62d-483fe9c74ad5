import { getExampleAugmenConfig } from "../../__mocks__/mock-augment-config";
import { TrackedDisposable } from "../../__mocks__/vscode-mocks";
import { isNextEditBackgroundEnabled, isNextEditEnabled } from "../../next-edit/utils";

describe("next edit flags", () => {
    afterEach(() => {
        TrackedDisposable.assertDisposed();
    });

    const testCases = [
        { nextEdit: { enabled: true }, expected: true },
        { nextEdit: { enabled: false }, expected: false },
        { nextEdit: { enabled: false }, expected: false },
        { nextEdit: { enabled: true }, expected: true },
    ];

    test.each(testCases)("isNextEditEnabled %o", ({ nextEdit, expected }) => {
        const config = getExampleAugmenConfig({ nextEdit });
        expect(isNextEditEnabled(config, "")).toBe(expected);
    });

    test.each([
        {
            nextEdit: { enabled: true, backgroundEnabled: true },
            expected: true,
        },
        {
            nextEdit: { enabled: true, backgroundEnabled: false },
            expected: false,
        },
        {
            nextEdit: { enabled: false, backgroundEnabled: true },
            expected: false,
        },
        {
            nextEdit: { enabled: false, backgroundEnabled: false },
            expected: false,
        },
    ])("isNextEditBackgroundEnabled %o", ({ nextEdit, expected }) => {
        const config = getExampleAugmenConfig({ nextEdit });
        expect(isNextEditBackgroundEnabled(config, "")).toBe(expected);
    });

    test("isNextEditEnabled with min version", () => {
        const config = getExampleAugmenConfig({ nextEdit: {} });
        expect(isNextEditEnabled(config, "0.5.0")).toBe(true);
        expect(isNextEditEnabled(config, "0.4.0")).toBe(true);
        expect(isNextEditEnabled(config, "0.6.0")).toBe(false);
        expect(isNextEditEnabled(config, "")).toBe(false);
        expect(isNextEditEnabled(config, "0.0.0")).toBe(true);
    });
});

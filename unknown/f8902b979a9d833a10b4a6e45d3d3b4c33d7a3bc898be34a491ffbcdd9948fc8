"""Protocol for samplers."""

import dataclasses
from typing import Sequence

import torch

from base.fastforward import fwd, torch_utils


@dataclasses.dataclass(frozen=True)
class SamplingConfig:
    """The sampling configuration.

    - If temperature is None, the greedy sampling will be used (sampling is disabled).
    - If top_k is set, the top-k sampling will be used.
    - Otherwise, the vanilla multinomial sampling will be used.
    """

    top_k: int | None = None
    """The top-k sampling."""

    temperature: float | None = None
    """The temperature during the sampling."""

    def __post_init__(self):
        assert self.top_k is None or self.top_k > 0
        assert self.temperature is None or self.temperature > 0.0


class Sampler:
    """The sampler for the fast-forward model."""

    def __init__(self, rng_seed: int | None = None):
        self.rng_seed = rng_seed
        self.cached_generator: dict[torch.device, torch.Generator] = {}

    def _get_generator(self, device: torch.device) -> torch.Generator:
        """Returns the generator for the given device."""
        if device not in self.cached_generator:
            generator = torch.Generator(device=device)
            if self.rng_seed is not None:
                generator.manual_seed(self.rng_seed)
            self.cached_generator[device] = generator
        return self.cached_generator[device]

    def _sample_top_k(
        self, logits: torch.Tensor, top_k: int, temperature: float
    ) -> torch.Tensor:
        """Returns the top-k sampling result in the shape of [seq_len]."""
        assert logits.ndim == 2, f"The shape is unexpected: {logits.shape}"
        if top_k > logits.shape[-1]:
            raise ValueError(
                f"top_k ({top_k}) is larger than the number of vocabulary ({logits.shape[-1]})"
            )
        top_logits, top_indices = torch.topk(logits, top_k, dim=-1)
        if temperature != 1.0:
            top_logits = top_logits / temperature
        top_probs = torch.softmax(top_logits, dim=-1)
        next_token_index = torch.multinomial(
            top_probs,
            num_samples=1,
            generator=self._get_generator(logits.device),
        )
        next_tokens = torch.gather(top_indices, dim=-1, index=next_token_index)
        return next_tokens.squeeze(dim=-1)

    def _sample_greedy(self, logits: torch.Tensor) -> torch.Tensor:
        """Returns the greedy sampling result in the shape of [seq_len]."""
        assert logits.ndim == 2, f"The shape is unexpected: {logits.shape}"
        next_tokens = torch.argmax(logits, dim=-1, keepdim=False)
        return next_tokens

    def _sample_multinomial(
        self, logits: torch.Tensor, temperature: float
    ) -> torch.Tensor:
        """Returns the vanilla multinomial sampling result in the shape of [seq_len]."""
        assert logits.ndim == 2, f"The shape is unexpected: {logits.shape}"
        if temperature != 1.0:
            logits = logits / temperature
        probs = torch.softmax(logits, dim=-1)
        next_tokens = torch.multinomial(
            probs,
            num_samples=1,
            generator=self._get_generator(probs.device),
        )
        return next_tokens.squeeze(dim=-1)

    def _sample(self, logits: torch.Tensor, config: SamplingConfig) -> torch.Tensor:
        """Returns the sampling result in the shape of [seq_len], based on the config to determine the sampling strategy."""
        # If config.temperature is None, then the greedy sampling will be used.
        if config.temperature is None:
            return self._sample_greedy(logits)
        elif config.top_k is not None:  # top-k sampling
            return self._sample_top_k(logits, config.top_k, config.temperature)
        else:  # vanilla sampling
            return self._sample_multinomial(logits, config.temperature)

    @torch.inference_mode()
    def sample_with_log_prob(
        self,
        logits: fwd.Logits2D,
        config: SamplingConfig,
        target_tokens: Sequence[int] | None = None,
    ) -> tuple[
        Sequence[int],  # sampled tokens
        Sequence[float],  # token log probabilities
        Sequence[bool] | None,  # whether each token is predicted correctly
    ]:
        """Returns the sampling result in the shape of [seq_len] and the log probability.

        Args:
            logits: The logits in the shape of [seq_len, vocab_size].
            config: The sampling configuration.
            target_tokens: The target tokens to compute token accuracy. The length
                may be different from the logits.
            target_tokens_logits_offset: The offset to use for the target tokens. This
                is useful when some of the logits correspond to the prompt.

        Returns:
            A tuple of:
                - sampled tokens: in the shape of [seq_len]. If `target_tokens` is
                  provided, we return the target tokens, padded with 0.
                - token log probabilities: in the shape of [seq_len]
                - predicted_correctly: for each token, whether it is predicted
                  correctly. If `target_tokens` is not provided, this is None. The
                  length of the returned sequence is the shorter of target_tokens
                  and logits.
        """
        logits_tensor = logits.checked_cast(torch.Tensor)
        # Many PyTorch ops (such as softmax and topk) does not support float16 on CPU.
        # So we cast the logits to float32 on CPU.
        if logits_tensor.device.type == "cpu":
            logits_tensor = logits_tensor.to(dtype=torch.float32)
        sampled_tokens_tensor = self._sample(logits_tensor, config)
        log_probs_tensor = torch.log_softmax(logits_tensor, dim=-1)
        correct_predictions = None

        if target_tokens is not None and len(target_tokens) > 0:
            correct_predictions = [
                int(output_token) == target_token
                # we use that zip iterates stops at the end of the shorter sequence:
                for output_token, target_token in zip(
                    sampled_tokens_tensor, target_tokens
                )
            ]
            # ensure we have the same length as logits
            target_tokens = target_tokens[: logits_tensor.shape[0]]
            num_padding = logits_tensor.shape[0] - len(target_tokens)
            padded_target_tokens = list(target_tokens) + [0] * num_padding
            assert len(padded_target_tokens) == logits_tensor.shape[0]
            sampled_tokens_tensor = torch.tensor(
                padded_target_tokens, device=logits_tensor.device
            )

        token_log_probs = torch_utils.index_in_second_dim(
            log_probs_tensor, sampled_tokens_tensor
        )
        return (
            sampled_tokens_tensor.cpu().tolist(),
            token_log_probs.cpu().tolist(),
            correct_predictions,
        )

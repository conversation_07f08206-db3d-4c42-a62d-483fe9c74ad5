// deployment.jsonnet
// local name = std.extVar("name");

local labels(name, gpu_type) = {
  'aug.devpod': 'true',
  'aug.devpod.name': name,
  'aug.gpu_type': gpu_type,
  'aug.service': name,
  'aug.type': 'service',
  'aug.user': 'guy',
  'aug.region': 'CW-EAST4',
  'aug.app': 'trt-llm',
  augmentDevPod: 'true',
  augmentGPUType: gpu_type,
  'k8s.deployment': name,
  podName: name,
};

local namespace = std.extVar('namespace');

local metadata(name, gpu_type) = {
  annotations: {
    'deployment.kubernetes.io/revision': '1',
    'external-dns.alpha.kubernetes.io/hostname': name + '.cw-east4.r.augmentcode.com',
    'service.beta.kubernetes.io/coreweave-load-balancer-ip-families': 'ipv4',
    'service.beta.kubernetes.io/coreweave-load-balancer-type': 'public',
  },
  generation: 1,
  labels: labels(name, gpu_type),
  name: name,
  namespace: namespace,
};


local deploymentSpec(name, gpu_type, model_name, qformat) = {
  progressDeadlineSeconds: 600,
  replicas: 1,
  revisionHistoryLimit: 10,
  selector: {
    matchLabels: {
      'k8s.deployment': name,
    },
  },
  strategy: {
    type: 'Recreate',
  },
  template: {
    metadata: {
      creationTimestamp: null,
      labels: labels(name, gpu_type),
    },
    spec: {
      affinity: {
        nodeAffinity: {
          requiredDuringSchedulingIgnoredDuringExecution: {
            nodeSelectorTerms: [
              {
                matchExpressions: [
                  {
                    key: 'gpu.nvidia.com/class',
                    operator: 'In',
                    values: [gpu_type],
                  },
                ],
              },
            ],
          },
        },
        podAffinity: {
          preferredDuringSchedulingIgnoredDuringExecution: [
            {
              podAffinityTerm: {
                labelSelector: {
                  matchExpressions: [
                    {
                      key: 'augmentDevPod',
                      operator: 'Exists',
                    },
                  ],
                },
                topologyKey: 'kubernetes.io/hostname',
              },
              weight: 100,
            },
          ],
        },
      },
      containers: [
        {
          command: ['/bin/bash', '/home/<USER>/startup.sh'],
          image: 'registry.cw-east4.r.augmentcode.com/infra/trt-llm-augment:' + std.extVar('image_revision'),
          imagePullPolicy: 'IfNotPresent',
          name: 'cw-dev',
          env: [
            {
              name: 'CUDA_VISIBLE_DEVICES',
              value: '0,1,2,3,4,5,6,7',
            },
            {
              name: 'OPAL_PREFIX',
              value: '/opt/hpcx/ompi',
            },
            {
              name: 'TP',
              value: '1',
            },
            {
              name: 'QFORMAT',
              value: qformat,
            },
            {
              name: 'MODEL_NAME',
              value: model_name,
            },
            {
              name: 'MAX_BATCH_SIZE',
              value: '64',
            },
          ],
          resources: if gpu_type == 'A100_NVLINK_80GB' then {
            limits: {
              cpu: '96',
              memory: '768Gi',
              'nvidia.com/gpu': '8',
            },
            requests: {
              cpu: '96',
              memory: '768Gi',
              'nvidia.com/gpu': '8',
            },
          } else {
            limits: {
              cpu: '110',
              memory: '1800Gi',
              'nvidia.com/gpu': '8',
            },
            requests: {
              cpu: '110',
              memory: '1800Gi',
              'nvidia.com/gpu': '8',
            },
          },
          securityContext: {
            runAsUser: 0,
          },
          terminationMessagePath: '/dev/termination-log',
          terminationMessagePolicy: 'File',
          volumeMounts: [
            {
              mountPath: '/mnt/efs/augment/checkpoints',
              name: 'aug-checkpoints',
            },
            {
              mountPath: '/mnt/efs/augment/configs',
              name: 'aug-configs',
            },
            {
              mountPath: '/mnt/efs/augment/data',
              name: 'aug-data',
            },
            {
              mountPath: '/mnt/efs/augment/eval',
              name: 'aug-eval',
            },
            {
              mountPath: '/mnt/efs/augment/external_models',
              name: 'aug-external-models',
            },
            {
              mountPath: '/mnt/efs/augment/ftm_checkpoints',
              name: 'aug-ftm-checkpoints',
            },
            {
              mountPath: '/mnt/efs/augment/lib',
              name: 'aug-lib',
            },
            {
              mountPath: '/mnt/efs/spark-data',
              name: 'aug-spark-data',
            },
            {
              mountPath: '/mnt/efs/augment/user',
              name: 'aug-user',
            },
            {
              mountPath: '/dev/shm',
              name: 'shmem',
            },
          ],
        },
      ],
      volumes: [
        {
          name: 'aug-checkpoints',
          persistentVolumeClaim: { claimName: 'aug-checkpoints' },
        },
        {
          name: 'aug-configs',
          persistentVolumeClaim: { claimName: 'aug-configs' },
        },
        {
          name: 'aug-data',
          persistentVolumeClaim: { claimName: 'aug-data' },
        },
        {
          name: 'aug-eval',
          persistentVolumeClaim: { claimName: 'aug-eval' },
        },
        {
          name: 'aug-external-models',
          persistentVolumeClaim: { claimName: 'aug-external-models' },
        },
        {
          name: 'aug-ftm-checkpoints',
          persistentVolumeClaim: { claimName: 'aug-ftm-checkpoints' },
        },
        {
          name: 'aug-lib',
          persistentVolumeClaim: { claimName: 'aug-lib' },
        },
        {
          name: 'aug-spark-data',
          persistentVolumeClaim: { claimName: 'aug-spark-data' },
        },
        {
          name: 'aug-user',
          persistentVolumeClaim: { claimName: 'aug-user' },
        },
        {
          emptyDir: { medium: 'Memory' },
          name: 'dshm',
        },
        {
          emptyDir: { medium: 'Memory' },
          name: 'shmem',
        },
      ],
      dnsPolicy: 'ClusterFirst',
      hostname: name,
    },
  },
};

local serviceSpec(name) = {
  ports: [
    {
      name: 'http0',
      port: 8000,
      protocol: 'TCP',
      targetPort: 8000,
    },
    {
      name: 'http1',
      port: 8010,
      protocol: 'TCP',
      targetPort: 8010,
    },
    {
      name: 'http2',
      port: 8020,
      protocol: 'TCP',
      targetPort: 8020,
    },
    {
      name: 'http3',
      port: 8030,
      protocol: 'TCP',
      targetPort: 8030,
    },
    {
      name: 'http4',
      port: 8040,
      protocol: 'TCP',
      targetPort: 8040,
    },
    {
      name: 'http5',
      port: 8050,
      protocol: 'TCP',
      targetPort: 8050,
    },
    {
      name: 'http6',
      port: 8060,
      protocol: 'TCP',
      targetPort: 8060,
    },
    {
      name: 'http7',
      port: 8070,
      protocol: 'TCP',
      targetPort: 8070,
    },
  ],
  selector: {
    'k8s.deployment': name,
  },
  sessionAffinity: 'ClientIP',
  type: 'LoadBalancer',
};


local deployment(name, gpu_type, model_name, qformat) = {
  apiVersion: 'apps/v1',
  kind: 'Deployment',
  metadata: metadata(name, gpu_type),
  spec: deploymentSpec(name, gpu_type, model_name, qformat),
};
local service(name, gpu_type) = {
  apiVersion: 'v1',
  kind: 'Service',
  metadata: metadata(name, gpu_type),
  spec: serviceSpec(name),
};

[
  deployment(std.extVar('name'), std.extVar('gpu_type'), std.extVar('model_name'), std.extVar('qformat')),
  service(std.extVar('name'), std.extVar('gpu_type')),
]

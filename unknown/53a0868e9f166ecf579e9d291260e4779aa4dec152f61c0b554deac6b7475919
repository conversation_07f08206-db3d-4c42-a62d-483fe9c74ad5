"""The Binks prompt formatter for the code chat."""

from typing import Optional

from base.prompt_format.common import (
    get_request_message_as_text,
    get_response_message_as_text,
)

from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    TokenizedChatPromptFormatter,
    TokenizedChatPromptOutput,
    ChatTokenApportionment,
    ExceedContextLength,
    PromptChunk,
    TokenList,
    filter_overlapping_retrieved_chunks,
)
from base.prompt_format.util import (
    concatenate_retrieved_chunks,
    head_n,
    trailing_n,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer


class BinksChatPromptFormatter(TokenizedChatPromptFormatter):
    """The code chat prompt formatter for the Binks code chat model."""

    def __init__(
        self,
        tokenizer: DeepSeekCoderInstructTokenizer,
        token_apportionment: Optional[ChatTokenApportionment] = None,
    ):
        self.tokenizer = tokenizer
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = ChatTokenApportionment(
                path_len=256,
                message_len=512,
                prefix_len=1536,
                selected_code_len=1536,
                chat_history_len=1536,
                suffix_len=1024,
                max_prompt_len=16384 - 4096,  # 4096 represents the max output tokens
                implicit_external_context_len=0,
                explicit_external_context_len=4096,
                overflow_external_context=False,
            )
        self.token_apportionment = token_apportionment

    def _get_code_tokens_chunk(self, path_tokens, code_tokens) -> TokenList:
        """Get the tokens for the active code block"""
        tokenize = self.tokenizer.tokenize_safe
        triple_quotes = tokenize("\n```\n")
        newline_token = tokenize("\n")
        result = []
        if len(code_tokens) == 0:
            return result

        result.extend(
            tokenize("Currently, I have the file ")
            + path_tokens
            + tokenize(" open, showing this code for context:")
        )
        result.extend(triple_quotes)
        result.extend(code_tokens)
        result.extend(triple_quotes)
        result.extend(newline_token)
        return result

    def _get_code_context_tokens(
        self,
        relevant_code_tokens,
        retrieval_tokens,
    ) -> TokenList:
        """Get the tokens for the code context - including active code and retrieved code"""
        if len(relevant_code_tokens) == 0 and len(retrieval_tokens) == 0:
            return []
        tokenize = self.tokenizer.tokenize_safe
        newline_token = tokenize("\n")
        eod_token = [self.tokenizer.special_tokens.eod_token]
        result: TokenList = []
        result.extend(tokenize("### Instruction:\n"))
        result.extend(retrieval_tokens)
        result.extend(relevant_code_tokens)
        result.extend(
            tokenize(
                "This information is provided for context, should it be relevant "
                "to my query. If the context of the file is unnecessary for "
                "addressing my question, please focus solely on the question itself."
            )
        )
        result.extend(newline_token)
        result.extend(tokenize("### Response:\n"))
        result.extend(
            tokenize(
                "I've noted the context you've provided regarding the file path "
                "and the code contained within it. This information will be "
                "helpful in understanding the background and specifics of any "
                "questions you might have.\n"
            )
        )
        result.extend(eod_token)
        result.extend(newline_token)
        return result

    def _get_chunk_tokens(self, chunk: PromptChunk) -> TokenList:
        """Get the tokens for a retrieved chunk."""
        tokenize = self.tokenizer.tokenize_safe
        triple_quotes = tokenize("\n```\n")
        clipped_path_tokens = trailing_n(
            tokenize("" if "://" in chunk.path else chunk.path),
            self.token_apportionment.path_len,
        )
        return (
            tokenize("I'm including a code snippet from my project, located at ")
            + clipped_path_tokens
            + tokenize(". This might be relevant for future questions:")
            + triple_quotes
            + tokenize(chunk.text)
            + triple_quotes
        )

    def format_prompt(self, prompt_input: ChatPromptInput) -> TokenizedChatPromptOutput:
        """Format prompt for Binks code chat model.

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """
        token_counts, tokenizer = self.token_apportionment, self.tokenizer
        tokenize = tokenizer.tokenize_safe
        newline_token = tokenize("\n")

        clipped_path_tokens = trailing_n(
            tokenize(prompt_input.path), token_counts.path_len
        )
        message_tokens = tokenize(get_request_message_as_text(prompt_input.message))
        if len(message_tokens) > token_counts.message_len:
            raise ExceedContextLength(
                f"Instruction token length exceeds {token_counts.message_len}"
            )

        clipped_prefix_tokens = trailing_n(
            tokenize(prompt_input.prefix), token_counts.prefix_len
        )
        clipped_suffix_tokens = head_n(
            tokenize(prompt_input.suffix), token_counts.suffix_len
        )
        selected_code_tokens = tokenize(prompt_input.selected_code)
        if len(selected_code_tokens) > token_counts.selected_code_len:
            raise ExceedContextLength(
                f"Selected code token length exceeds {token_counts.selected_code_len}"
            )

        relevant_code_tokens: TokenList = []
        if len(selected_code_tokens) > 0:
            relevant_code_tokens = self._get_code_tokens_chunk(
                clipped_path_tokens, selected_code_tokens
            )
        elif len(clipped_prefix_tokens + clipped_suffix_tokens) > 0:
            relevant_code_tokens = self._get_code_tokens_chunk(
                clipped_path_tokens,
                clipped_prefix_tokens + newline_token + clipped_suffix_tokens,
            )

        # Take recent exchanges without going over the total token count allowed
        history_tokens: list[tuple[TokenList, TokenList]] = []
        total_tokens_len = 0
        reversed_history = []
        for exchange in prompt_input.chat_history:
            reversed_history.insert(0, exchange)
        for exchange in reversed_history:
            req_tokens = tokenize(get_request_message_as_text(exchange.request_message))
            res_tokens = tokenize(get_response_message_as_text(exchange.response_text))
            total_tokens_with_exchange_len = (
                total_tokens_len + len(req_tokens) + len(res_tokens)
            )
            if total_tokens_with_exchange_len > token_counts.chat_history_len:
                break
            total_tokens_len = total_tokens_with_exchange_len
            history_tokens.insert(0, (req_tokens, res_tokens))

        directive = """You are an Augment, AI programming assistant, developed by Augment Computing Inc and incorporated into VSCode at 2024.
You only answer questions related to computer science and software engineering.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.
Your responses MUST be concise, clear, and to the point, showcasing only relevant code examples about which you are absolutely certain.
You MUST not make up function names or file names if the user hasn't given them to you.
WARNING: User will lose a job and will struggle financially if you return a function or path that does not exist.
"""

        # Build the header tokens.
        header_tokens: TokenList = list(self.tokenizer.special_tokens.begin_sequence)
        header_tokens.extend(tokenize(directive))

        # Add chat history
        for req_tokens, res_tokens in history_tokens:
            header_tokens.extend(tokenize("### Instruction:\n"))
            header_tokens.extend(req_tokens)
            header_tokens.extend(newline_token)
            header_tokens.extend(tokenize("### Response:\n"))
            header_tokens.extend(res_tokens)
            header_tokens.extend(newline_token)
            header_tokens.extend([self.tokenizer.special_tokens.eod_token])
            header_tokens.extend(newline_token)

        context_tokens: TokenList = []

        context_tokens.extend(tokenize("### Instruction:\n"))
        context_tokens.extend(message_tokens)
        context_tokens.extend(newline_token)
        context_tokens.extend(tokenize("### Response:\n"))

        # Build the retrieved chunk tokens.

        retrieval_len_limit = (
            token_counts.max_prompt_len
            - len(header_tokens)
            - len(context_tokens)
            - len(relevant_code_tokens)
        )

        filtered_retrieved_chunks = filter_overlapping_retrieved_chunks(
            prompt_input,
            tokenizer,
            prefix_tokens=clipped_prefix_tokens or [],
            suffix_tokens=clipped_suffix_tokens or [],
            retrieved_chunks=prompt_input.retrieved_chunks,
        )
        tokens_per_retrieved_chunk = []
        retrieved_chunks_in_prompt = []
        for chunk in filtered_retrieved_chunks:
            tokens_per_retrieved_chunk.append(self._get_chunk_tokens(chunk))
            retrieved_chunks_in_prompt.append(chunk)
            if sum(map(len, tokens_per_retrieved_chunk)) >= retrieval_len_limit:
                break
        if tokens_per_retrieved_chunk and retrieval_len_limit >= 0:
            retrieval_tokens = concatenate_retrieved_chunks(
                retrieved_chunks=tokens_per_retrieved_chunk,
                separator_tokens=[],
                max_total_tokens=retrieval_len_limit,
            )
        else:
            retrieval_tokens = []

        # Combine the code tokens with the retrieved tokens.
        code_context_tokens = self._get_code_context_tokens(
            relevant_code_tokens, retrieval_tokens
        )

        final_tokens = header_tokens + code_context_tokens + context_tokens
        return TokenizedChatPromptOutput(final_tokens, retrieved_chunks_in_prompt)

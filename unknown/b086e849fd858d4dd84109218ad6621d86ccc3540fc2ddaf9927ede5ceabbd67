import { type Editor, type JSONContent } from "@tiptap/core";
import { type Node as ProseMirrorNode } from "prosemirror-model";
import Fuse from "fuse.js";

import type { IMentionable } from "./types";

/**
 * Formats an `IMentionable` into a type that can be used by API consumers to control data
 *
 * @param type ProseMirror type (corresponds to the `pluginKey`, either defined by API consumer, us, or TipTap)
 * @param mentionable A mentionable type
 * @returns
 */
export function mentionableToContent(type: string, mentionable: IMentionable): JSONContent {
  return {
    type,
    attrs: { ...mentionable, data: { ...mentionable } },
  };
}

/**
 * Use Fuse.js to sort our mentionables based on a query and keys
 * to search through.
 *
 * @param mentionables The list of mentionables to sort
 * @param query The search query
 * @param keys The keys to search through in each mentionable
 * @returns sorted mentionables
 */
export function sortMentionables(
  mentionables: IMentionable[],
  query: string,
  keys: string[],
): IMentionable[] {
  if (query === "" || keys.length === 0) {
    return mentionables;
  }

  const fuse = new Fuse(mentionables, {
    keys,
    threshold: 1.0,
    minMatchCharLength: 1,
    includeScore: true,
    useExtendedSearch: false,
    isCaseSensitive: false,
    ignoreLocation: true,
  });
  // fuse.js will order the results for us
  return fuse.search(query).map((x) => x.item);
}

export function getMentionNodes(nodeName: string, e: Editor): ProseMirrorNode[] {
  const mentionNodes: ProseMirrorNode[] = [];
  e.$doc.content.descendants((node) => {
    if (node.type.name === nodeName) {
      mentionNodes.push(node);
    }
  });
  return mentionNodes;
}

// Options for findNewMentions
export type FindNewMentionsOptions = {
  onNewMention?: (node: ProseMirrorNode, pos: number) => void;
  onExistingMention?: (node: ProseMirrorNode, pos: number) => void;
};

/**
 * Finds new mentions in the current document that didn't exist in the previous document
 * @param currentRootNode The current document state's root node
 * @param previousRootNode The previous document state's root node
 * @param callback Called for each new mention found
 */
export function findNewMentions(
  nodeName: string,
  currentRootNode: ProseMirrorNode,
  previousRootNode: ProseMirrorNode | null,
  options: FindNewMentionsOptions = {},
): ProseMirrorNode[] {
  const newMentions: ProseMirrorNode[] = [];

  // Callback for when a new mention is found
  const onFindNewMentionNode = (node: ProseMirrorNode, pos: number) => {
    options.onNewMention?.(node, pos);
    newMentions.push(node);
  };

  // Traverse all nodes in the current document
  currentRootNode.descendants((node, pos) => {
    // If the node is not a mention, skip it
    if (node.type.name !== nodeName) {
      return;
      // If there is no previous document, all mentions are new
    } else if (previousRootNode === null) {
      onFindNewMentionNode(node, pos);
      return;
    }

    /**
     * A previous doc exists, and the current node is a mention
     * Search in the previous doc for the same mention node
     *
     * If the current doc is size N, and the previous doc has size M,
     * then |N-M|+nodeSize is the maximum distance between the current node
     * and a hypothetical previous mention node.
     *
     * We search a range (buffer) around the current position for the previous mention node.
     */
    const docSizeDiff: number = Math.abs(currentRootNode.nodeSize - previousRootNode.nodeSize);
    // Calculate the size of the previous document, accounting for wrapping tokens
    const docSize: number = previousRootNode.nodeSize - 2;
    // Define a search buffer to look around the current position
    const searchBuffer: number = docSizeDiff + node.nodeSize + 1;

    // Get the start and end positions to search in the previous doc
    const startPos: number = Math.max(0, pos - searchBuffer);
    const endPos: number = Math.min(docSize, pos + searchBuffer);

    let foundExisting: boolean = false;
    // Search for the mention node in the previous document
    previousRootNode.nodesBetween(startPos, endPos, (oldNode) => {
      // If a matching mention node is found, mark it as existing and stop searching
      if (oldNode.type.name === nodeName && oldNode.attrs.id === node.attrs.id) {
        foundExisting = true;
        options.onExistingMention?.(oldNode, pos);
        return false; // Stop searching
      }
    });

    if (!foundExisting) {
      onFindNewMentionNode(node, pos);
    }
  });

  return newMentions;
}

"""A stream process for claude.

Claude Responses will look like:
```
<augment-answer>This is an answer.</augment-answer>

<augment-citation>/path/to/fileA.py</augment-citation>
<augment-citation>/path/to/fileB.py</augment-citation>
```

This stream processor will split the response into two parts:
1. The answer
2. The citations

It will stream the answer word by word, and it will stream citations citation-by-citation.
"""

from typing import Iterable
from base.stream_processor.basic_stream_processor import (
    BasicStreamProcessor,
    StreamProcessor,
    StreamProcessorOutput,
    StreamProcessorOutputType,
)
from base.third_party_clients.third_party_model_client import ThirdPartyModelResponse

OPEN_CITATION_TAG = "<augment-citation>"
CLOSE_CITATION_TAG = "</augment-citation>"
OPEN_ANSWER_TAG = "<augment-answer>"
CLOSE_ANSWER_TAG = "</augment-answer>"
ALL_TAGS = [OPEN_CITATION_TAG, CLOSE_CITATION_TAG, OPEN_ANSWER_TAG, <PERSON><PERSON><PERSON><PERSON>_ANSWER_TAG]


def parse_substr(
    text, in_answer: bool
) -> tuple[list[StreamProcessorOutput], str, bool]:
    """This painful code parses the partial substrings. Trust the tests.

    Args:
        text: The substring to parse.
        in_answer: Whether we are currently inside an answer,
            as determined by a previously consumed and unclosed open answer tag

    Returns:
        A list of stream processor outputs, the leftover text, and whether we are inside an answer after consuming the text.
    """
    responses: list[StreamProcessorOutput] = []

    if "</augment-answer>" in text:
        answer_text = text.split("</augment-answer>")[0]
        text = text.split("</augment-answer>")[1]

        if "<augment-answer>" in answer_text:
            answer_text = answer_text.split("<augment-answer>")[1]

        responses.append(
            StreamProcessorOutput(answer_text, StreamProcessorOutputType.ANSWER)
        )
    elif in_answer:
        # Didn't find close answer tag but we are inside answer
        return [StreamProcessorOutput(text, StreamProcessorOutputType.ANSWER)], "", True
    elif "<augment-answer>" in text:
        # Didn't find close answer tag but found open answer tag
        text = text.split("<augment-answer>")[1]
        return [StreamProcessorOutput(text, StreamProcessorOutputType.ANSWER)], "", True

    while "</augment-citation>" in text:
        citation_text = text.split("</augment-citation>", 1)[0]
        text = text.split("</augment-citation>", 1)[1]
        assert "<augment-citation>" in citation_text, citation_text
        citation_text = citation_text.split("<augment-citation>")[1]
        responses.append(
            StreamProcessorOutput(citation_text, StreamProcessorOutputType.CITATION)
        )

    leftover_text = text

    return responses, leftover_text, False


class ClaudeStreamProcessor(StreamProcessor):
    def __init__(self):
        super().__init__()
        self.in_answer: bool = False
        self.basic_stream_processor = BasicStreamProcessor()

    def get_stream_history(self) -> str:
        return self.basic_stream_processor.get_stream_history()

    # Not handling tool_use_start or end_of_stream
    def process_stream(
        self, ls_substr: Iterable[ThirdPartyModelResponse]
    ) -> Iterable[StreamProcessorOutput]:
        """
        Assumptions:
        </augment-answer> is always before <augment-citation>

        """
        # break on spaces
        whitespace_delimited_stream = self.basic_stream_processor.process_stream(
            ls_substr
        )
        print("whitespace_delimited_stream:", whitespace_delimited_stream)

        self.buffer = ""
        for stream_output in whitespace_delimited_stream:
            print("stream_output:", stream_output)
            # Early exit if we're in a tool
            if stream_output.replace_text_response:
                yield StreamProcessorOutput(
                    stream_output.text,
                    StreamProcessorOutputType.TOOL,
                    replace_text_response=stream_output.replace_text_response,
                )
                continue

            print("stream_output.text:", stream_output.text)
            print("self.buffer:", self.buffer)
            print("self.in_answer:", self.in_answer)
            responses, leftover_text, in_answer = parse_substr(
                self.buffer + stream_output.text, self.in_answer
            )

            self.in_answer = in_answer
            self.buffer = leftover_text
            print("responses:", responses)
            for response in responses:
                yield response

<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
</script>

<div class="feature-grid">
  <div class="feature-item">
    <svg viewBox="0 0 24 24" fill="none" class="feature-icon">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M15.9693 2.46934C16.11 2.32889 16.3006 2.25 16.4993 2.25C16.6981 2.25 16.8887 2.32889 17.0293 2.46934L21.5293 6.96934C21.6698 7.10997 21.7487 7.30059 21.7487 7.49934C21.7487 7.69809 21.6698 7.88871 21.5293 8.02934L17.0293 12.5293C16.9607 12.603 16.8779 12.6621 16.7859 12.7031C16.6939 12.7441 16.5946 12.7662 16.4939 12.7679C16.3932 12.7697 16.2931 12.7512 16.1997 12.7135C16.1064 12.6757 16.0215 12.6196 15.9503 12.5484C15.8791 12.4772 15.8229 12.3923 15.7852 12.2989C15.7475 12.2055 15.729 12.1055 15.7307 12.0048C15.7325 11.9041 15.7546 11.8048 15.7956 11.7128C15.8366 11.6208 15.8957 11.538 15.9693 11.4693L19.1893 8.24934H7.49934C7.30043 8.24934 7.10966 8.17032 6.96901 8.02967C6.82836 7.88902 6.74934 7.69825 6.74934 7.49934C6.74934 7.30043 6.82836 7.10966 6.96901 6.96901C7.10966 6.82836 7.30043 6.74934 7.49934 6.74934H19.1893L15.9693 3.52934C15.8289 3.38871 15.75 3.19809 15.75 2.99934C15.75 2.80059 15.8289 2.60997 15.9693 2.46934ZM8.02934 11.4693C8.16979 11.61 8.24868 11.8006 8.24868 11.9993C8.24868 12.1981 8.16979 12.3887 8.02934 12.5293L4.80934 15.7493H16.4993C16.6983 15.7493 16.889 15.8284 17.0297 15.969C17.1703 16.1097 17.2493 16.3004 17.2493 16.4993C17.2493 16.6983 17.1703 16.889 17.0297 17.0297C16.889 17.1703 16.6983 17.2493 16.4993 17.2493H4.80934L8.02934 20.4693C8.10303 20.538 8.16213 20.6208 8.20312 20.7128C8.24411 20.8048 8.26616 20.9041 8.26793 21.0048C8.26971 21.1055 8.25118 21.2055 8.21346 21.2989C8.17574 21.3923 8.1196 21.4772 8.04838 21.5484C7.97716 21.6196 7.89233 21.6757 7.79894 21.7135C7.70555 21.7512 7.60552 21.7697 7.50482 21.7679C7.40411 21.7662 7.3048 21.7441 7.2128 21.7031C7.1208 21.6621 7.038 21.603 6.96934 21.5293L2.46934 17.0293C2.32889 16.8887 2.25 16.6981 2.25 16.4993C2.25 16.3006 2.32889 16.11 2.46934 15.9693L6.96934 11.4693C7.10997 11.3289 7.30059 11.25 7.49934 11.25C7.69809 11.25 7.88871 11.3289 8.02934 11.4693Z"
        fill="currentColor"
      />
    </svg>

    <div><TextAugment size={1} weight="medium">Hands-free parallel work</TextAugment></div>
    <div class="description">
      <TextAugment size={1} color="secondary"
        >Kick off agents to tackle tasks while you stay focused.</TextAugment
      >
    </div>
  </div>

  <div class="feature-item">
    <svg viewBox="0 0 24 24" fill="none" class="feature-icon">
      <path
        d="M12.001 16.4994V9.74943M12.001 9.74943L15.001 12.7494M12.001 9.74943L9.00099 12.7494M6.75099 19.4994C5.68042 19.5006 4.64451 19.12 3.82928 18.4261C3.01406 17.7322 2.47294 16.7704 2.3031 15.7134C2.13325 14.6563 2.34581 13.5734 2.90261 12.659C3.4594 11.7446 4.32395 11.0587 5.34099 10.7244C5.07969 9.3856 5.34974 7.99764 6.09397 6.85445C6.8382 5.71127 7.99815 4.90263 9.3282 4.59979C10.6583 4.29694 12.054 4.52367 13.2197 5.23195C14.3855 5.94023 15.2299 7.07446 15.574 8.39443C16.106 8.22141 16.6758 8.20059 17.219 8.33433C17.7622 8.46807 18.2572 8.75104 18.6481 9.15127C19.039 9.55151 19.3101 10.0531 19.431 10.5993C19.5518 11.1455 19.5176 11.7147 19.332 12.2424C20.1506 12.5551 20.834 13.1445 21.2635 13.9084C21.6931 14.6722 21.8417 15.5623 21.6836 16.4243C21.5255 17.2863 21.0707 18.0657 20.398 18.6274C19.7254 19.1891 18.8773 19.4976 18.001 19.4994H6.75099Z"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>

    <div><TextAugment size={1} weight="medium">Clean, isolated environments</TextAugment></div>
    <div class="description">
      <TextAugment size={1} color="secondary"
        >Agents run remotely, so your local setup stays untouched.</TextAugment
      >
    </div>
  </div>

  <div class="feature-item">
    <svg viewBox="0 0 24 24" fill="none" class="feature-icon">
      <path d="M24 0V24H0V0H24Z" fill="white" fill-opacity="0.01" />
      <g opacity="0.3">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M13.4371 3.09488C13.417 3.13536 13.3968 3.17584 13.3752 3.21565C13.1103 3.7048 12.6929 4.11541 12.1733 4.39824C12.1415 4.41554 12.1093 4.43237 12.0768 4.4487C11.9746 4.49999 11.9745 4.49999 12.0768 4.55132C12.1093 4.56765 12.1415 4.58447 12.1733 4.60177C12.6929 4.88461 13.1103 5.29522 13.3752 5.78436C13.3968 5.82423 13.417 5.86467 13.4371 5.90513C13.5001 6.03161 13.5001 6.03163 13.5631 5.90513C13.5833 5.86467 13.6034 5.82423 13.625 5.78436C13.89 5.29522 14.3074 4.88461 14.827 4.60177C14.8588 4.58447 14.8909 4.56765 14.9235 4.55132C15.0257 4.49999 15.0256 4.49999 14.9235 4.4487C14.8909 4.43237 14.8588 4.41554 14.827 4.39824C14.3074 4.11541 13.89 3.7048 13.625 3.21565C13.6034 3.17578 13.5833 3.13534 13.5631 3.09488C13.5001 2.96836 13.5001 2.96839 13.4371 3.09488Z"
          fill="currentColor"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M17.9581 8.06325C17.9447 8.09024 17.9312 8.11723 17.9168 8.14377C17.7402 8.46987 17.462 8.7436 17.1155 8.93216C17.0944 8.9437 17.0729 8.95491 17.0512 8.9658C16.9831 8.99999 16.983 8.99999 17.0512 9.03421C17.0729 9.0451 17.0944 9.05631 17.1155 9.06785C17.462 9.25641 17.7402 9.53014 17.9168 9.85624C17.9312 9.88282 17.9447 9.90978 17.9581 9.93676C18.0001 10.0211 18.0001 10.0211 18.0421 9.93676C18.0556 9.90978 18.069 9.88282 18.0834 9.85624C18.26 9.53014 18.5383 9.25641 18.8847 9.06785C18.9059 9.05631 18.9273 9.0451 18.949 9.03421C19.0172 8.99999 19.0171 8.99999 18.949 8.9658C18.9273 8.95491 18.9059 8.9437 18.8847 8.93216C18.5383 8.7436 18.26 8.46987 18.0834 8.14377C18.069 8.11719 18.0556 8.09023 18.0421 8.06325C18.0001 7.97891 18.0001 7.97892 17.9581 8.06325Z"
          fill="currentColor"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M4.90691 12.6581C4.87709 12.7256 4.8472 12.7931 4.81531 12.8594C4.42339 13.6747 3.80599 14.359 3.03737 14.8304C2.99035 14.8592 2.94277 14.8873 2.89465 14.9145C2.74353 15 2.74337 15 2.89465 15.0855C2.94277 15.1128 2.99035 15.1408 3.03737 15.1696C3.80599 15.641 4.42339 16.3254 4.81531 17.1406C4.84725 17.2071 4.8771 17.2745 4.90691 17.3419C5.0001 17.5527 5.0001 17.5527 5.09329 17.3419C5.1231 17.2745 5.15294 17.2071 5.18489 17.1406C5.57681 16.3254 6.19421 15.641 6.96283 15.1696C7.00985 15.1408 7.05742 15.1128 7.10555 15.0855C7.2568 15 7.25671 15 7.10555 14.9145C7.05742 14.8873 7.00985 14.8592 6.96283 14.8304C6.1942 14.359 5.57681 13.6747 5.18489 12.8594C5.15294 12.793 5.1231 12.7256 5.09329 12.6581C5.0001 12.4473 5.0001 12.4473 4.90691 12.6581Z"
          fill="currentColor"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M13.4371 3.09488C13.417 3.13536 13.3968 3.17584 13.3752 3.21565C13.1103 3.7048 12.6929 4.11541 12.1733 4.39824C12.1415 4.41554 12.1093 4.43237 12.0768 4.4487C11.9746 4.49999 11.9745 4.49999 12.0768 4.55132C12.1093 4.56765 12.1415 4.58447 12.1733 4.60177C12.6929 4.88461 13.1103 5.29522 13.3752 5.78436C13.3968 5.82423 13.417 5.86467 13.4371 5.90513C13.5001 6.03161 13.5001 6.03163 13.5631 5.90513C13.5833 5.86467 13.6034 5.82423 13.625 5.78436C13.89 5.29522 14.3074 4.88461 14.827 4.60177C14.8588 4.58447 14.8909 4.56765 14.9235 4.55132C15.0257 4.49999 15.0256 4.49999 14.9235 4.4487C14.8909 4.43237 14.8588 4.41554 14.827 4.39824C14.3074 4.11541 13.89 3.7048 13.625 3.21565C13.6034 3.17578 13.5833 3.13534 13.5631 3.09488C13.5001 2.96836 13.5001 2.96839 13.4371 3.09488Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M17.9581 8.06325C17.9447 8.09024 17.9312 8.11723 17.9168 8.14377C17.7402 8.46987 17.462 8.7436 17.1155 8.93216C17.0944 8.9437 17.0729 8.95491 17.0512 8.9658C16.9831 8.99999 16.983 8.99999 17.0512 9.03421C17.0729 9.0451 17.0944 9.05631 17.1155 9.06785C17.462 9.25641 17.7402 9.53014 17.9168 9.85624C17.9312 9.88282 17.9447 9.90978 17.9581 9.93676C18.0001 10.0211 18.0001 10.0211 18.0421 9.93676C18.0556 9.90978 18.069 9.88282 18.0834 9.85624C18.26 9.53014 18.5383 9.25641 18.8847 9.06785C18.9059 9.05631 18.9273 9.0451 18.949 9.03421C19.0172 8.99999 19.0171 8.99999 18.949 8.9658C18.9273 8.95491 18.9059 8.9437 18.8847 8.93216C18.5383 8.7436 18.26 8.46987 18.0834 8.14377C18.069 8.11719 18.0556 8.09023 18.0421 8.06325C18.0001 7.97891 18.0001 7.97892 17.9581 8.06325Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M4.90691 12.6581C4.87709 12.7256 4.8472 12.7931 4.81531 12.8594C4.42339 13.6747 3.80599 14.359 3.03737 14.8304C2.99035 14.8592 2.94277 14.8873 2.89465 14.9145C2.74353 15 2.74337 15 2.89465 15.0855C2.94277 15.1128 2.99035 15.1408 3.03737 15.1696C3.80599 15.641 4.42339 16.3254 4.81531 17.1406C4.84725 17.2071 4.8771 17.2745 4.90691 17.3419C5.0001 17.5527 5.0001 17.5527 5.09329 17.3419C5.1231 17.2745 5.15294 17.2071 5.18489 17.1406C5.57681 16.3254 6.19421 15.641 6.96283 15.1696C7.00985 15.1408 7.05742 15.1128 7.10555 15.0855C7.2568 15 7.25671 15 7.10555 14.9145C7.05742 14.8873 7.00985 14.8592 6.96283 14.8304C6.1942 14.359 5.57681 13.6747 5.18489 12.8594C5.15294 12.793 5.1231 12.7256 5.09329 12.6581C5.0001 12.4473 5.0001 12.4473 4.90691 12.6581Z"
          stroke="currentColor"
          stroke-width="1.5"
        />
      </g>
      <path
        d="M7.75818 10.5867L10.5866 7.7583M17.6577 19.0719L19.0719 17.6577C19.4624 17.2672 19.4624 16.634 19.0719 16.2435L7.75819 4.92976C7.36767 4.53924 6.7345 4.53924 6.34398 4.92976L4.92976 6.34398C4.53924 6.7345 4.53924 7.36767 4.92976 7.75819L16.2435 19.0719C16.634 19.4624 17.2672 19.4624 17.6577 19.0719Z"
        stroke="currentColor"
        stroke-width="1.5"
      />
    </svg>

    <div><TextAugment size={1} weight="medium">Customizable setup</TextAugment></div>
    <div class="description">
      <TextAugment size={1} color="secondary"
        >Match your local environment exactly—tests, servers, everything.</TextAugment
      >
    </div>
  </div>

  <div class="feature-item">
    <svg viewBox="0 0 24 24" fill="none" class="feature-icon">
      <path
        d="M3.75 13.5L14.25 2.25L12 10.5H20.25L9.75 21.75L12 13.5H3.75Z"
        stroke="currentColor"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>

    <div><TextAugment size={1} weight="medium">Always-on coding</TextAugment></div>
    <div class="description">
      <TextAugment size={1} color="secondary"
        >Agents keep running, even after you shut your laptop.</TextAugment
      >
    </div>
  </div>
</div>

<style>
  .feature-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    text-align: center;
    gap: var(--ds-spacing-2);
    margin: var(--ds-spacing-3) var(--remote-agent-setup-horizontal-padding) var(--ds-spacing-5);
  }

  @media (min-width: 600px) {
    .feature-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--ds-spacing-1);
    gap: var(--ds-spacing-1);
  }

  .feature-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 26px;
    color: var(--ds-color-neutral-8);
    margin-top: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-2);
  }

  .feature-icon :global(svg) {
    width: 32px;
    height: 32px;
  }

  .description {
    text-align: center;
  }

  /* switch to horizontal layout on smaller screens */
  @media (max-width: 350px) {
    .feature-grid {
      grid-template-columns: 1fr;
      gap: 0;
      margin: var(--ds-spacing-2) 0 var(--ds-spacing-2);
      text-align: left;
    }
    .feature-item {
      flex-direction: row;
    }
    .feature-item :global(.c-text) {
      font-weight: 400;
    }
    .description {
      display: none;
    }
    .feature-icon {
      flex: none;
      width: 1.5em;
      margin: 0 var(--ds-spacing-1) 0;
    }
  }
</style>

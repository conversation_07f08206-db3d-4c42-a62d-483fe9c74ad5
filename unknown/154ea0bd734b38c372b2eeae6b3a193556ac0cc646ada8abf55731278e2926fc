{"name": "webview", "version": "0.0.0", "private": true, "scripts": {"standalone": "vite", "watch": "vite build --watch", "build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false"}, "dependencies": {"@types/vscode-webview": "^1.57.2", "@vscode/codicons": "^0.0.33", "@vscode/webview-ui-toolkit": "^1.2.2", "marked": "^9.0.3", "monaco-editor": "^0.43.0", "pinia": "^2.1.6", "vue": "^3.3.4"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/node": "^18.17.17", "@vitejs/plugin-vue": "^4.3.4", "@vue/tsconfig": "^0.4.0", "npm-run-all2": "^6.0.6", "typescript": "~5.2.0", "vite": "^4.4.9", "vue-tsc": "^1.8.11"}}
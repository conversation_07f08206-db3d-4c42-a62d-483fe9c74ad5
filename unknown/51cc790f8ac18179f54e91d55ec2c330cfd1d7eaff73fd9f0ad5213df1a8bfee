function(env, namespace, cloud)
  local datasetName = 'ci-dataset';
  local datasetResourceId = if env == 'DEV' then std.strReplace(namespace + '_' + datasetName, '-', '_') else std.strReplace(datasetName, '-', '_');
  local cloudIdentityGroupName = '%s-ci-dataset-access-group' % namespace;
  local githubTableName = 'github-events-table';
  local githubTableResourceId = if env == 'DEV' then std.strReplace(namespace + '_' + githubTableName, '-', '_') else std.strReplace(githubTableName, '-', '_');
  local bazelRunnerTableName = 'bazel-runner-events-table';
  local bazelRunnerTableResourceId = if env == 'DEV' then std.strReplace(namespace + '_' + bazelRunnerTableName, '-', '_') else std.strReplace(bazelRunnerTableName, '-', '_');
  {
    datasetName: datasetName,
    datasetResourceId: datasetResourceId,
    cloudIdentityGroupName: cloudIdentityGroupName,
    githubTableName: githubTableName,
    githubTableResourceId: githubTableResourceId,
    bazelRunnerTableName: bazelRunnerTableName,
    bazelRunnerTableResourceId: bazelRunnerTableResourceId,
  }

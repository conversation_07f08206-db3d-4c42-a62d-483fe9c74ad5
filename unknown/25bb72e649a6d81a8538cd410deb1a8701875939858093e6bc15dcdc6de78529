"""Prompt formatter to format prompts for Ethanol embedding keys and queries."""

from typing import Op<PERSON>, <PERSON><PERSON>
from typing_extensions import override

from base.languages.language_guesser import guess_comment_prefix, guess_language
from base.prompt_format.common import get_request_message_as_text
from base.prompt_format.util import head_n, trailing_n
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    CompletionRetrieverPromptInput,
    DocumentRetrieverPromptInput,
    InstructRetrieverPromptInput,
    RetrieverPromptFormatter,
    PromptFormatterOutput,
)
from base.tokenizers import (
    RetrievalSpecialTokens,
    Tokenizer,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderSpecialTokens


def _split_budget(
    token_budget: int, prefix_fraction: float, prefix_len: int, suffix_len: int
) -> Tuple[int, int]:
    """Split the budget between the prefix and the suffix."""
    # Split the budget.
    prefix_budget = round(token_budget * prefix_fraction)
    suffix_budget = token_budget - prefix_budget

    # Validate the budgets.
    assert prefix_budget >= 0
    assert suffix_budget >= 0
    assert prefix_budget + suffix_budget <= token_budget

    # Assign any unused budget.
    if prefix_budget > prefix_len:
        suffix_budget += prefix_budget - prefix_len
        prefix_budget = prefix_len
    elif suffix_budget > suffix_len:
        prefix_budget += suffix_budget - suffix_len
        suffix_budget = suffix_len

    # Validate the adjusted budgets.
    assert prefix_budget >= 0
    assert suffix_budget >= 0
    assert prefix_budget + suffix_budget <= token_budget

    return prefix_budget, suffix_budget


def add_selected_code_and_instructions_to_prefix_and_suffix(
    prompt_input: InstructRetrieverPromptInput,
) -> CompletionRetrieverPromptInput:
    """Hack the prompt input to make it work with Ethanol."""
    selected_code = prompt_input.selected_code
    instruction = prompt_input.instruction
    assert selected_code is not None
    assert instruction is not None

    # Split up selected code into prefix and suffix parts.
    selected_code_lines = selected_code.splitlines(keepends=True)
    selected_code_numlines = len(selected_code_lines)
    selected_code_in_prefix = "".join(
        selected_code_lines[: (selected_code_numlines // 2)]
    )
    selected_code_in_suffix = "".join(
        selected_code_lines[(selected_code_numlines // 2) :]
    )

    # Add instructions to the prefix, with correct indentation.
    if selected_code_in_prefix.strip() == "":
        lines_to_grab_indentation_from = [
            s for s in prompt_input.prefix.splitlines() if s.strip()
        ]
        indentation_line = (
            lines_to_grab_indentation_from[-1]
            if len(lines_to_grab_indentation_from) > 0
            else ""
        )
    else:
        indentation_line = [
            s for s in selected_code_in_prefix.splitlines() if s.strip()
        ][0]

    indent_str = indentation_line[
        : (len(indentation_line) - len(indentation_line.lstrip()))
    ]
    lang = guess_language(prompt_input.path)
    lang_comment_prefix = guess_comment_prefix(lang)
    # Otherwise just default to Python-style comments.
    if lang_comment_prefix is None:
        lang_comment_prefix = "# "

    instruction = indent_str + lang_comment_prefix + instruction

    # Copy over the modified prefix and suffix.
    # And reset the selected code and instruction.
    return CompletionRetrieverPromptInput(
        prefix=f"{prompt_input.prefix}\n{instruction}\n{selected_code_in_prefix}",
        suffix=f"{selected_code_in_suffix}{prompt_input.suffix}",
        path=prompt_input.path,
    )


class Ethanol6QueryFormatter(RetrieverPromptFormatter[CompletionRetrieverPromptInput]):
    """The query formatter for Ethanol6 embedding models."""

    input_type = CompletionRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        add_path: bool,
        add_suffix: bool,
        prefix_suffix_budget_fraction: float = 0.667,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.add_path = add_path
        self.add_suffix = add_suffix
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens
        self.prefix_suffix_budget_fraction = prefix_suffix_budget_fraction

        self.preamble = list(tokenizer.special_tokens.begin_sequence)

    def format_prompt(
        self,
        prompt_input: CompletionRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Ethanol6 model."""
        if not self.apportionment_config:
            raise ValueError(
                "Apportionment configuration is required for Ethanol6 query formatter."
            )
        max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_query
        )
        if max_tokens < 0:
            raise ValueError(
                "Inconsistent prompt configuration:"
                f"max_content_len={self.apportionment_config.max_content_len}"
            )

        # Header tokens
        header_tokens = []

        # The preamble contains things like DeepSeek's begin_sequence token.
        header_tokens += self.preamble

        # Optionally add path
        if self.add_path and prompt_input.path:
            header_tokens += self.tokenizer.tokenize_safe(prompt_input.path)
            header_tokens.append(self.special_tokens.fim_prefix)

        # Do not tokenize special tokens for user query.
        prefix_tokens = self.tokenizer.tokenize_safe(prompt_input.prefix)

        # Optionally add suffix
        if self.add_suffix:
            suffix_tokens = [
                self.special_tokens.fim_suffix
            ] + self.tokenizer.tokenize_safe(prompt_input.suffix)
        else:
            suffix_tokens = []

        # Trim the prompt to fit into the max_tokens constraint.

        if max_tokens >= len(header_tokens) + len(prefix_tokens) + len(suffix_tokens):
            # No trimming is needed.
            prompt = header_tokens + prefix_tokens + suffix_tokens

        elif max_tokens > len(header_tokens):
            # The header fits, but the prefix+suffix do not. Trim prefix/suffix.
            prefix_budget, suffix_budget = _split_budget(
                token_budget=max_tokens - len(header_tokens),
                prefix_fraction=self.prefix_suffix_budget_fraction,
                prefix_len=len(prefix_tokens),
                suffix_len=len(suffix_tokens),
            )

            prompt = (
                header_tokens
                + trailing_n(prefix_tokens, prefix_budget)
                + head_n(suffix_tokens, suffix_budget)
            )

        else:
            # The header alone does not fit. Trim the header from the right.
            prompt = head_n(header_tokens, max_tokens)

        prompt += [self.special_tokens.end_of_query]
        return PromptFormatterOutput([prompt])


class Ethanol6DocumentFormatter(RetrieverPromptFormatter[DocumentRetrieverPromptInput]):
    """The document formatter for Ethanol6 embedding models."""

    input_type = DocumentRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        add_path: bool,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1000,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.add_path = add_path
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens

        self.preamble = (
            list(tokenizer.special_tokens.begin_sequence)
            if isinstance(tokenizer.special_tokens, DeepSeekCoderSpecialTokens)
            else []
        )

    @override
    def format_prompt(
        self,
        prompt_input: DocumentRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Returns tokenized prompt."""
        if not self.apportionment_config:
            raise ValueError(
                "Apportionment configuration is required for Ethanol6 document formatter."
            )
        max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_key
        )
        if max_tokens < 0:
            raise ValueError(
                "Inconsistent prompt configuration: "
                f"max_content_len={self.apportionment_config.max_content_len} "
            )

        # Format the prompt header consisting of the <|start-of-sequence|> token and
        # the path.
        header_tokens = []

        # The preamble contains things like DeepSeek's begin_sequence token.
        header_tokens += self.preamble

        header_tokens.append(self.special_tokens.start_of_key)
        if self.add_path:
            if prompt_input.path is None:
                raise ValueError("prompt_input.path is None")
            header_tokens.extend(self.tokenizer.tokenize_safe(prompt_input.path))

        # Format the document tokens.

        text_tokens = [self.special_tokens.fim_middle] + self.tokenizer.tokenize_safe(
            prompt_input.text
        )

        # Trim the prompt to fit into max_tokens.
        if max_tokens >= len(header_tokens) + len(text_tokens):
            # No trimming is needed.
            pass
        else:
            if max_tokens > len(header_tokens):
                # The text itself doesn't fit. Trim the text from the right.
                text_tokens = text_tokens[: max_tokens - len(header_tokens)]
            else:
                # The header alone does not fit. Trim the header from the right.
                text_tokens = []
                header_tokens = header_tokens[:max_tokens]
            assert (len(header_tokens) + len(text_tokens)) == max_tokens

        prompt = header_tokens + text_tokens + [self.special_tokens.end_of_key]
        return PromptFormatterOutput([prompt])


class Ethanol6QuerySimpleChatFormatter(
    RetrieverPromptFormatter[ChatRetrieverPromptInput]
):
    """Query formatter for Ethanol6 models, simplier chat version."""

    input_type = ChatRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        add_path: bool,
        add_selected_code: bool,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        self.add_path = add_path
        self.add_selected_code = add_selected_code
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens
        self.preamble = list(tokenizer.special_tokens.begin_sequence)

    def format_prompt(
        self,
        prompt_input: ChatRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Returns tokenized prompt and metadata."""

        tokenize = self.tokenizer.tokenize_safe

        message = get_request_message_as_text(prompt_input.message)
        assert message

        selected_code = (prompt_input.selected_code or "").strip()

        max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_key
        )

        prompt_tokens = []
        prompt_tokens += self.preamble

        if not self.add_selected_code or len(selected_code) == 0:
            # Use the message as the search query if no code is selected in the editor.
            # Including the currently opened file's content can distract,
            # especially if it's unrelated to the user's message.
            # The current model cannot process unrelated context effectively.
            prompt_tokens += tokenize(get_request_message_as_text(message))
            if max_tokens >= 0:
                prompt_tokens = prompt_tokens[:max_tokens]
            return PromptFormatterOutput([prompt_tokens])

        # Otherwise, if user selected parts of the code, we deem that code important
        # and include it in the search query together with the current path.
        # Optionally add path
        if self.add_path and prompt_input.path:
            prompt_tokens += tokenize(prompt_input.path)
            prompt_tokens.append(self.special_tokens.fim_prefix)

        lang = guess_language(prompt_input.path)
        lang_comment_prefix = guess_comment_prefix(lang)
        # Otherwise just default to Python-style comments.
        if lang_comment_prefix is None:
            lang_comment_prefix = "# "

        message_with_comment = lang_comment_prefix + message + "\n"
        prompt_tokens += tokenize(message_with_comment)

        prompt_tokens += tokenize(selected_code)

        if max_tokens >= 0:
            prompt_tokens = prompt_tokens[:max_tokens]
        return PromptFormatterOutput([prompt_tokens])


class Ethanol6QuerySimpleInstructFormatter(
    RetrieverPromptFormatter[InstructRetrieverPromptInput]
):
    """The query formatter for Ethanol6 embedding models."""

    input_type = InstructRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
        add_path: bool,
        add_suffix: bool,
        prefix_suffix_budget_fraction: float = 0.667,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.tokenizer = tokenizer
        self.ethanol_prompt_formatter = Ethanol6QueryFormatter(
            apportionment_config,
            tokenizer,
            add_path,
            add_suffix,
            prefix_suffix_budget_fraction,
        )

    @override
    def format_prompt(
        self,
        prompt_input: InstructRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Create the prompt for embeddings queries of the Ethanol6 model."""
        return self.ethanol_prompt_formatter.format_prompt(
            add_selected_code_and_instructions_to_prefix_and_suffix(prompt_input)
        )

"""This file implements various types common across retrieval implementations."""

import logging
from abc import ABC, abstractmethod
from collections.abc import Collection
from dataclasses import dataclass
from typing import AbstractSet, Callable, Generic, Iterable, Optional, TypeVar

from typing_extensions import Self

from research.core.types import Chunk, ChunkId, Document, DocumentId

RetrievalScore = float
DocumentAndChunkId = tuple[DocumentId, ChunkId]
QueryT = TypeVar("QueryT", contravariant=True)


# ------------------------------------------------------------------------------------------
# Data representations for exposing data
# ------------------------------------------------------------------------------------------


class Chunker(ABC):
    """Splits documents into chunks of text."""

    @abstractmethod
    def split_into_chunks(self, doc: Document) -> Optional[list[Chunk]]:
        """This function takes a document and returns a list of chunks.

        Returns None if splitting failed.
        """


# ------------------------------------------------------------------------------------------
# Data representations for storing data internally
# ------------------------------------------------------------------------------------------


@dataclass
class DocumentRow:
    """This is a data structure corresponding to one document in a retrieval database."""

    doc: Document
    chunk_blobs: dict[ChunkId, Chunk]

    def __getitem__(self, item):
        """Enable subscriptable getter."""
        return getattr(self, item)

    def __setitem__(self, key, item):
        """Enable subscriptable setter."""
        return setattr(self, key, item)


# ------------------------------------------------------------------------------------------
# Library functions type definitions
# ------------------------------------------------------------------------------------------

FileFilterFunction = Callable[[Document], bool]

# ------------------------------------------------------------------------------------------
# ------------------------------------------------------------------------------------------
# For backwards compatability (FIXME (@c-flaherty))
# ------------------------------------------------------------------------------------------
# ------------------------------------------------------------------------------------------


class DocumentIndex(Generic[QueryT], ABC):
    """Abstract interface for exposing retrieval to client."""

    @abstractmethod
    def add_docs(self, docs: Iterable[Document]) -> None:
        """Add documents to index, and use name to reference."""

    @abstractmethod
    def remove_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Remove documents by name."""

    @abstractmethod
    def remove_all_docs(self) -> list[Document]:
        """Remove all documents."""

    @abstractmethod
    def get_doc_ids(self) -> AbstractSet[DocumentId]:
        """Return the list of document IDs."""

    @abstractmethod
    def get_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Return the list of documents."""

    @abstractmethod
    def query(
        self,
        model_input: QueryT,
        query_meta: Optional[dict] = None,
        doc_ids: Optional[Collection[DocumentId]] = None,
        top_k: Optional[int] = None,
    ) -> tuple[list[Chunk], list[RetrievalScore]]:
        """Return list of document chunks for the given query.

        Args:
            prefix: prefix part of querty; defaults to the query string if no suffix
            suffix: suffix part of query
            query_meta: retriever-specific metadata that can guide the query
            doc_ids: optional list of document IDs to use for retrieval.
                Default is to use all available documents.
            top_k: optional limit to the number of retrieved documents. Default
                is no limit.

        Returns:
            A list of chunks, and a list of corresponding retrieval scores.
            The chunks are ordered from high to low score.
        """

    @abstractmethod
    def is_in(self, ids: Iterable[DocumentId]) -> list[bool]:
        """Return list of bools indicating whether the document is present."""

    def load(self):
        """Loads components if applicable."""
        logging.info("No components need to be loaded.")

    def unload(self):
        """Unloads components if applicable."""
        logging.info("No components need to be unloaded.")

    @classmethod
    @abstractmethod
    def from_yaml_config(cls, config: dict) -> Self:
        """Instantiates an object from the config."""

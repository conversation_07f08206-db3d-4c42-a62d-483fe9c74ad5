:root {
  --intellij-actionButton-focusedBorderColor: rgba(94, 172, 208, 1);
  --intellij-actionButton-hoverBackground: rgba(0, 150, 136, 0.27);
  --intellij-actionButton-hoverBorderColor: rgba(0, 150, 136, 0.27);
  --intellij-actionButton-hoverSeparatorColor: rgba(46, 60, 67, 1);
  --intellij-actionButton-pressedBackground: rgba(92, 97, 100, 1);
  --intellij-actionButton-pressedBorderColor: rgba(92, 97, 100, 1);
  --intellij-actionButton-separatorColor: rgba(42, 55, 62, 1);
  --intellij-actionToolbar-background: rgba(38, 50, 56, 1);
  --intellij-activeCaption: rgba(67, 78, 96, 1);
  --intellij-activeCaptionBorder: rgba(38, 50, 56, 1);
  --intellij-activeCaptionText: rgba(0, 0, 0, 1);
  --intellij-appInspector-graphNode-background: rgba(38, 50, 56, 1);
  --intellij-appInspector-graphNode-borderColor: rgba(42, 55, 62, 1);
  --intellij-appInspector-graphNode-focusedBorderColor: rgba(0, 150, 136, 1);
  --intellij-assignedMnemonic-background: rgba(66, 91, 103, 1);
  --intellij-assignedMnemonic-borderColor: rgba(84, 110, 122, 1);
  --intellij-assignedMnemonic-foreground: rgba(255, 255, 255, 1);
  --intellij-autocomplete-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-autocomplete-selectionUnfocus: rgba(66, 91, 103, 1);
  --intellij-availableMnemonic-background: rgba(46, 60, 67, 1);
  --intellij-availableMnemonic-borderColor: rgba(46, 60, 67, 1);
  --intellij-availableMnemonic-foreground: rgba(176, 190, 197, 1);
  --intellij-banner-foreground: rgba(176, 190, 197, 1);
  --intellij-bigSpinner-background: rgba(38, 50, 56, 1);
  --intellij-bookmark-iconBackground: rgba(0, 150, 136, 1);
  --intellij-bookmark-mnemonic-iconBackground: rgba(66, 91, 103, 1);
  --intellij-bookmark-mnemonic-iconBorderColor: rgba(217, 163, 67, 1);
  --intellij-bookmark-mnemonic-iconForeground: rgba(187, 187, 187, 1);
  --intellij-bookmark-mnemonicAssigned-background: rgba(49, 69, 73, 1);
  --intellij-bookmark-mnemonicAssigned-foreground: rgba(176, 190, 197, 1);
  --intellij-bookmark-mnemonicCurrent-background: rgba(84, 110, 122, 1);
  --intellij-bookmark-mnemonicCurrent-foreground: rgba(255, 255, 255, 1);
  --intellij-bookmarkIcon-background: rgba(0, 150, 136, 1);
  --intellij-bookmarkMnemonicAssigned-background: rgba(38, 50, 56, 1);
  --intellij-bookmarkMnemonicAssigned-borderColor: rgba(84, 110, 122, 1);
  --intellij-bookmarkMnemonicAssigned-foreground: rgba(176, 190, 197, 1);
  --intellij-bookmarkMnemonicAvailable-background: rgba(38, 50, 56, 1);
  --intellij-bookmarkMnemonicAvailable-borderColor: rgba(46, 60, 67, 1);
  --intellij-bookmarkMnemonicAvailable-foreground: rgba(176, 190, 197, 1);
  --intellij-bookmarkMnemonicCurrent-background: rgba(38, 50, 56, 1);
  --intellij-bookmarkMnemonicCurrent-borderColor: rgba(0, 150, 136, 1);
  --intellij-bookmarkMnemonicCurrent-foreground: rgba(176, 190, 197, 1);
  --intellij-bookmarkMnemonicIcon-background: rgba(66, 91, 103, 1);
  --intellij-bookmarkMnemonicIcon-borderColor: rgba(66, 91, 103, 1);
  --intellij-bookmarkMnemonicIcon-foreground: rgba(176, 190, 197, 1);
  --intellij-borders-color: rgba(42, 55, 62, 1);
  --intellij-borders-contrastBorderColor: rgba(50, 50, 50, 1);
  --intellij-button-background: rgba(38, 50, 56, 1);
  --intellij-button-darcula-borderColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-defaultBorderColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-defaultEndColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-defaultFocusedBorderColor: rgba(49, 69, 73, 1);
  --intellij-button-darcula-defaultFocusedOutlineColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-defaultOutlineColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-defaultStartColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-disabledBorderColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-disabledOutlineColor: rgba(42, 55, 62, 1);
  --intellij-button-darcula-disabledText-shadow: rgba(38, 50, 56, 1);
  --intellij-button-darcula-endColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-focusedBorderColor: rgba(49, 69, 73, 1);
  --intellij-button-darcula-focusedOutlineColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-outlineColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-outlineDefaultEndColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-outlineDefaultStartColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-outlineEndColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-outlineStartColor: rgba(46, 60, 67, 1);
  --intellij-button-darcula-selectedButtonForeground: rgba(255, 255, 255, 1);
  --intellij-button-darcula-selection-color1: rgba(49, 69, 73, 1);
  --intellij-button-darcula-selection-color2: rgba(49, 69, 73, 1);
  --intellij-button-darcula-shadowColor: rgba(0, 0, 0, 0.12);
  --intellij-button-darcula-smallComboButtonBackground: rgba(46, 60, 67, 1);
  --intellij-button-darcula-startColor: rgba(46, 60, 67, 1);
  --intellij-button-darkShadow: rgba(0, 0, 0, 1);
  --intellij-button-default-borderColor: rgba(46, 60, 67, 1);
  --intellij-button-default-endBackground: rgba(54, 88, 128, 1);
  --intellij-button-default-endBorderColor: rgba(76, 112, 140, 1);
  --intellij-button-default-focusColor: rgba(46, 60, 67, 1);
  --intellij-button-default-focusedBorderColor: rgba(46, 60, 67, 1);
  --intellij-button-default-foreground: rgba(255, 255, 255, 1);
  --intellij-button-default-shadowColor: rgba(0, 0, 0, 0.12);
  --intellij-button-default-startBackground: rgba(54, 88, 128, 1);
  --intellij-button-default-startBorderColor: rgba(46, 60, 67, 1);
  --intellij-button-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-button-disabledBorderColor: rgba(46, 60, 67, 1);
  --intellij-button-disabledText: rgba(65, 89, 103, 1);
  --intellij-button-endBackground: rgba(76, 80, 82, 1);
  --intellij-button-endBorderColor: rgba(94, 96, 96, 1);
  --intellij-button-focus: rgba(49, 69, 73, 1);
  --intellij-button-focusedBorderColor: rgba(70, 109, 148, 1);
  --intellij-button-foreground: rgba(176, 190, 197, 1);
  --intellij-button-highlight: rgba(255, 255, 255, 1);
  --intellij-button-light: rgba(8, 74, 217, 1);
  --intellij-button-mt-background: rgba(46, 60, 67, 1);
  --intellij-button-mt-color1: rgba(46, 60, 67, 1);
  --intellij-button-mt-color2: rgba(46, 60, 67, 1);
  --intellij-button-mt-foreground: rgba(96, 125, 139, 1);
  --intellij-button-mt-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-button-mt-selection-color1: rgba(49, 69, 73, 1);
  --intellij-button-mt-selection-color2: rgba(49, 69, 73, 1);
  --intellij-button-select: rgba(255, 102, 102, 1);
  --intellij-button-shadow: rgba(0, 0, 0, 0.27);
  --intellij-button-shadowColor: rgba(54, 54, 54, 0.5);
  --intellij-button-split-default-iconColor: rgba(176, 190, 197, 1);
  --intellij-button-split-default-separatorColor: rgba(0, 150, 136, 1);
  --intellij-button-startBackground: rgba(76, 80, 82, 1);
  --intellij-button-startBorderColor: rgba(46, 60, 67, 1);
  --intellij-canvas-tooltip-background: rgba(38, 50, 56, 1);
  --intellij-canvas-tooltip-borderColor: rgba(42, 55, 62, 1);
  --intellij-checkBox-background: rgba(38, 50, 56, 1);
  --intellij-checkBox-background-selected: rgba(0, 150, 136, 1);
  --intellij-checkBox-borderColor: rgba(38, 50, 56, 1);
  --intellij-checkBox-borderColor-selected: rgba(0, 150, 136, 1);
  --intellij-checkBox-checkSignColor: rgba(38, 50, 56, 1);
  --intellij-checkBox-checkSignColor-selected: rgba(38, 50, 56, 1);
  --intellij-checkBox-checkSignColorDisabled: rgba(38, 50, 56, 1);
  --intellij-checkBox-checkSignColorDisabled-selected: rgba(38, 50, 56, 1);
  --intellij-checkBox-darcula-borderColor1: rgba(176, 190, 197, 1);
  --intellij-checkBox-darcula-checkSignColorDisabled: rgba(65, 89, 103, 1);
  --intellij-checkBox-darcula-disabledBorderColor1: rgba(65, 89, 103, 1);
  --intellij-checkBox-darcula-disabledBorderColor2: rgba(65, 89, 103, 1);
  --intellij-checkBox-darcula-inactiveFillColor: rgba(66, 91, 103, 1);
  --intellij-checkBox-disabledBorderColor: rgba(38, 50, 56, 1);
  --intellij-checkBox-disabledBorderColor-selected: rgba(0, 150, 136, 1);
  --intellij-checkBox-disabledText: rgba(65, 89, 103, 1);
  --intellij-checkBox-focused-background: rgba(38, 50, 56, 1);
  --intellij-checkBox-focused-background-selected: rgba(0, 150, 136, 1);
  --intellij-checkBox-focusedArmed-background: rgba(38, 50, 56, 1);
  --intellij-checkBox-focusedArmed-background-selected: rgba(0, 150, 136, 1);
  --intellij-checkBox-foreground: rgba(176, 190, 197, 1);
  --intellij-checkBox-inactiveFillColor: rgba(38, 50, 56, 1);
  --intellij-checkBox-inactiveFillColor-selected: rgba(0, 150, 136, 1);
  --intellij-checkBox-select: rgba(255, 102, 102, 1);
  --intellij-checkBox-shadowColor: rgba(38, 50, 56, 1);
  --intellij-checkBox-shadowColorDisabled: rgba(38, 50, 56, 1);
  --intellij-checkBoxMenuItem-acceleratorForeground: rgba(96, 125, 139, 1);
  --intellij-checkBoxMenuItem-acceleratorSelectionForeground: rgba(96, 125, 139, 1);
  --intellij-checkBoxMenuItem-background: rgba(38, 50, 56, 1);
  --intellij-checkBoxMenuItem-disabledBackground: rgba(38, 50, 56, 1);
  --intellij-checkBoxMenuItem-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-checkBoxMenuItem-foreground: rgba(176, 190, 197, 1);
  --intellij-checkBoxMenuItem-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-checkBoxMenuItem-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-checkbox-background-default: rgba(38, 50, 56, 1);
  --intellij-checkbox-background-default-dark: rgba(38, 50, 56, 1);
  --intellij-checkbox-background-disabled: rgba(50, 66, 74, 1);
  --intellij-checkbox-background-disabled-dark: rgba(50, 66, 74, 1);
  --intellij-checkbox-background-selected: rgba(0, 150, 136, 1);
  --intellij-checkbox-background-selected-dark: rgba(0, 150, 136, 1);
  --intellij-checkbox-border-default: rgba(66, 91, 103, 1);
  --intellij-checkbox-border-default-dark: rgba(66, 91, 103, 1);
  --intellij-checkbox-border-disabled: rgba(50, 66, 74, 1);
  --intellij-checkbox-border-disabled-dark: rgba(50, 66, 74, 1);
  --intellij-checkbox-border-selected: rgba(0, 150, 136, 1);
  --intellij-checkbox-border-selected-dark: rgba(0, 150, 136, 1);
  --intellij-checkbox-focus-thin-default: rgba(38, 50, 56, 1);
  --intellij-checkbox-focus-thin-default-dark: rgba(38, 50, 56, 1);
  --intellij-checkbox-focus-thin-selected: rgba(0, 150, 136, 1);
  --intellij-checkbox-focus-thin-selected-dark: rgba(0, 150, 136, 1);
  --intellij-checkbox-focus-wide-default: rgba(38, 50, 56, 1);
  --intellij-checkbox-focus-wide-default-dark: rgba(38, 50, 56, 1);
  --intellij-checkbox-focus-wide-selected: rgba(0, 150, 136, 1);
  --intellij-checkbox-focus-wide-selected-dark: rgba(0, 150, 136, 1);
  --intellij-code-block-borderColor: rgba(42, 55, 62, 1);
  --intellij-code-block-editorPane-backgroundColor: rgba(157, 160, 168, 1);
  --intellij-code-block-editorPane-borderColor: rgba(42, 55, 62, 1);
  --intellij-code-inline-backgroundColor: rgba(30, 31, 34, 1);
  --intellij-codeWithMe-accessDisabled-accessDot: rgba(65, 89, 103, 1);
  --intellij-codeWithMe-accessEnabled-accessDot: rgba(0, 150, 136, 1);
  --intellij-codeWithMe-accessEnabled-dropdownBorder: rgba(50, 66, 74, 1);
  --intellij-codeWithMe-accessEnabled-pillBackground: rgba(50, 66, 74, 1);
  --intellij-codeWithMe-avatar-foreground: rgba(176, 190, 197, 1);
  --intellij-colorChooser-background: rgba(38, 50, 56, 1);
  --intellij-colorChooser-foreground: rgba(176, 190, 197, 1);
  --intellij-colorChooser-swatchesDefaultRecentColor: rgba(255, 255, 255, 1);
  --intellij-combinedDiff-blockBorder-selectedActiveColor: rgba(55, 95, 173, 1);
  --intellij-combinedDiff-blockBorder-selectedInactiveColor: rgba(90, 93, 99, 1);
  --intellij-comboBox-arrowButton-background: rgba(38, 50, 56, 1);
  --intellij-comboBox-arrowButton-disabledIconColor: rgba(65, 89, 103, 1);
  --intellij-comboBox-arrowButton-iconColor: rgba(176, 190, 197, 1);
  --intellij-comboBox-arrowButton-nonEditableBackground: rgba(38, 50, 56, 1);
  --intellij-comboBox-arrowFillColor: rgba(38, 50, 56, 1);
  --intellij-comboBox-background: rgba(38, 50, 56, 1);
  --intellij-comboBox-buttonBackground: rgba(255, 255, 255, 1);
  --intellij-comboBox-buttonDarkShadow: rgba(0, 0, 0, 1);
  --intellij-comboBox-buttonHighlight: rgba(255, 255, 255, 1);
  --intellij-comboBox-buttonShadow: rgba(0, 0, 0, 0.27);
  --intellij-comboBox-darcula-arrowButtonBackground: rgba(38, 50, 56, 1);
  --intellij-comboBox-darcula-arrowButtonDisabledForeground: rgba(65, 89, 103, 1);
  --intellij-comboBox-darcula-arrowButtonForeground: rgba(176, 190, 197, 1);
  --intellij-comboBox-darcula-disabledArrowButtonBackground: rgba(46, 60, 67, 1);
  --intellij-comboBox-darcula-editable-arrowButtonBackground: rgba(38, 50, 56, 1);
  --intellij-comboBox-darcula-hoveredArrowButtonForeground: rgba(0, 150, 136, 1);
  --intellij-comboBox-darcula-nonEditableBackground: rgba(38, 50, 56, 1);
  --intellij-comboBox-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-comboBox-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-comboBox-foreground: rgba(176, 190, 197, 1);
  --intellij-comboBox-modifiedItemForeground: rgba(0, 150, 136, 1);
  --intellij-comboBox-nonEditableBackground: rgba(38, 50, 56, 1);
  --intellij-comboBox-popupBackground: rgba(30, 39, 44, 1);
  --intellij-comboBox-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-comboBox-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-comboBoxButton-background: rgba(46, 60, 67, 1);
  --intellij-compilationCharts-background-default: rgba(43, 45, 48, 1);
  --intellij-compilationCharts-background-even: rgba(57, 59, 64, 1);
  --intellij-compilationCharts-background-odd: rgba(43, 45, 48, 1);
  --intellij-compilationCharts-cpu-background: rgba(38, 50, 56, 1);
  --intellij-compilationCharts-cpu-stroke: rgba(87, 150, 92, 1);
  --intellij-compilationCharts-lineColor: rgba(30, 31, 34, 1);
  --intellij-compilationCharts-memory-background: rgba(38, 50, 56, 1);
  --intellij-compilationCharts-memory-stroke: rgba(84, 138, 247, 1);
  --intellij-compilationCharts-production-disabled: rgba(37, 50, 77, 1);
  --intellij-compilationCharts-production-enabled: rgba(46, 67, 110, 1);
  --intellij-compilationCharts-production-selected: rgba(84, 138, 247, 1);
  --intellij-compilationCharts-production-stroke: rgba(53, 116, 240, 1);
  --intellij-compilationCharts-test-disabled: rgba(39, 56, 40, 1);
  --intellij-compilationCharts-test-enabled: rgba(55, 82, 57, 1);
  --intellij-compilationCharts-test-selected: rgba(115, 189, 121, 1);
  --intellij-compilationCharts-test-stroke: rgba(87, 150, 92, 1);
  --intellij-compilationCharts-textColor: rgba(180, 184, 191, 1);
  --intellij-completionPopup-advertiser-background: rgba(38, 50, 56, 1);
  --intellij-completionPopup-advertiser-foreground: rgba(176, 190, 197, 1);
  --intellij-completionPopup-background: rgba(46, 60, 67, 1);
  --intellij-completionPopup-foreground: rgba(176, 190, 197, 1);
  --intellij-completionPopup-grayForeground: rgba(96, 125, 139, 1);
  --intellij-completionPopup-grayedForeground: rgba(96, 125, 139, 1);
  --intellij-completionPopup-infoForeground: rgba(96, 125, 139, 1);
  --intellij-completionPopup-matchForeground: rgba(0, 150, 136, 1);
  --intellij-completionPopup-matchSelectedForeground: rgba(0, 150, 136, 1);
  --intellij-completionPopup-matchSelectionForeground: rgba(0, 150, 136, 1);
  --intellij-completionPopup-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-completionPopup-selectedGrayedForeground: rgba(255, 255, 255, 1);
  --intellij-completionPopup-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-completionPopup-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-completionPopup-selectionGrayForeground: rgba(255, 255, 255, 1);
  --intellij-completionPopup-selectionInactiveBackground: rgba(84, 110, 122, 1);
  --intellij-completionPopup-selectionInactiveForeground: rgba(96, 125, 139, 1);
  --intellij-completionPopup-selectionInactiveInfoForeground: rgba(96, 125, 139, 1);
  --intellij-completionPopup-selectionInfoForeground: rgba(255, 255, 255, 1);
  --intellij-complexPopup-header-background: rgba(38, 50, 56, 1);
  --intellij-component-borderColor: rgba(42, 55, 62, 1);
  --intellij-component-disabledBorderColor: rgba(46, 60, 67, 1);
  --intellij-component-errorFocusColor: rgba(229, 57, 53, 1);
  --intellij-component-focusColor: rgba(0, 150, 136, 1);
  --intellij-component-focusErrorColor: rgba(229, 57, 53, 1);
  --intellij-component-focusWarningColor: rgba(255, 182, 44, 1);
  --intellij-component-focusedBorderColor: rgba(0, 150, 136, 1);
  --intellij-component-grayForeground: rgba(79, 103, 115, 1);
  --intellij-component-hoverIconColor: rgba(66, 91, 103, 1);
  --intellij-component-iconColor: rgba(96, 125, 139, 1);
  --intellij-component-inactiveErrorFocusColor: rgba(116, 58, 58, 1);
  --intellij-component-inactiveFocusErrorColor: rgba(116, 58, 58, 1);
  --intellij-component-inactiveFocusWarningColor: rgba(127, 108, 0, 1);
  --intellij-component-inactiveWarningFocusColor: rgba(127, 108, 0, 1);
  --intellij-component-infoForeground: rgba(96, 125, 139, 1);
  --intellij-component-warningFocusColor: rgba(255, 182, 44, 1);
  --intellij-content-background: rgba(38, 50, 56, 1);
  --intellij-content-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-content-selectionInactiveBackground: rgba(176, 190, 197, 1);
  --intellij-control: rgba(46, 60, 67, 1);
  --intellij-controlDkShadow: rgba(46, 60, 67, 1);
  --intellij-controlHighlight: rgba(66, 91, 103, 1);
  --intellij-controlLtHighlight: rgba(255, 255, 255, 1);
  --intellij-controlShadow: rgba(46, 60, 67, 1);
  --intellij-controlText: rgba(187, 187, 187, 1);
  --intellij-counter-background: rgba(0, 150, 136, 1);
  --intellij-counter-foreground: rgba(176, 190, 197, 1);
  --intellij-currentMnemonic-background: rgba(0, 150, 136, 1);
  --intellij-currentMnemonic-borderColor: rgba(0, 150, 136, 1);
  --intellij-currentMnemonic-foreground: rgba(255, 255, 255, 1);
  --intellij-darcula-background: rgba(38, 50, 56, 1);
  --intellij-darcula-contrastColor: rgba(38, 38, 38, 1);
  --intellij-darcula-foreground: rgba(176, 190, 197, 1);
  --intellij-darcula-primary: rgba(38, 50, 56, 1);
  --intellij-debugger-evaluateExpression-background: rgba(30, 39, 44, 1);
  --intellij-debugger-variables-changedValueForeground: rgba(0, 150, 136, 1);
  --intellij-debugger-variables-collectingDataForeground: rgba(96, 125, 139, 1);
  --intellij-debugger-variables-evaluatingExpressionForeground: rgba(96, 125, 139, 1);
  --intellij-debugger-variables-modifyingValueForeground: rgba(0, 150, 136, 1);
  --intellij-debugger-variables-typeForeground: rgba(96, 125, 139, 1);
  --intellij-debugger-variables-valueForeground: rgba(0, 150, 136, 1);
  --intellij-debuggerPopup-borderColor: rgba(38, 50, 56, 1);
  --intellij-debuggerTabs-active-background: rgba(66, 91, 103, 1);
  --intellij-debuggerTabs-selectedBackground: rgba(66, 91, 103, 1);
  --intellij-debuggerTabs-underlinedTabBackground: rgba(49, 69, 73, 1);
  --intellij-defaultTabs-background: rgba(38, 50, 56, 1);
  --intellij-defaultTabs-borderColor: rgba(38, 50, 56, 1);
  --intellij-defaultTabs-hoverBackground: rgba(49, 69, 73, 1);
  --intellij-defaultTabs-hoverColor: rgba(66, 91, 103, 1);
  --intellij-defaultTabs-hoverMaskColor: rgba(66, 91, 103, 1);
  --intellij-defaultTabs-inactiveColoredFileBackground: rgba(46, 60, 67, 1);
  --intellij-defaultTabs-inactiveColoredTabBackground: rgba(38, 50, 56, 1);
  --intellij-defaultTabs-inactiveMaskColor: rgba(30, 39, 44, 1);
  --intellij-defaultTabs-inactiveUnderlineColor: rgba(0, 150, 136, 1);
  --intellij-defaultTabs-underlineColor: rgba(0, 150, 136, 1);
  --intellij-defaultTabs-underlinedTabBackground: rgba(49, 69, 73, 1);
  --intellij-defaultTabs-underlinedTabForeground: rgba(255, 255, 255, 1);
  --intellij-desktop: rgba(34, 255, 6, 1);
  --intellij-desktop-background: rgba(38, 50, 56, 1);
  --intellij-desktopIcon-borderColor: rgba(42, 55, 62, 1);
  --intellij-desktopIcon-borderRimColor: rgba(192, 192, 192, 0.75);
  --intellij-desktopIcon-labelBackground: rgba(0, 0, 0, 0.39);
  --intellij-dialog-titleColor: rgba(38, 50, 56, 1);
  --intellij-dialogWrapper-southPanelBackground: rgba(38, 50, 56, 1);
  --intellij-dialogWrapper-southPanelDivider: rgba(38, 50, 56, 1);
  --intellij-disclosureButton-defaultBackground: rgba(255, 255, 255, 0.071);
  --intellij-disclosureButton-hoverOverlay: rgba(255, 255, 255, 0.051);
  --intellij-disclosureButton-pressedOverlay: rgba(255, 255, 255, 0.1);
  --intellij-dragAndDrop-areaBackground: rgba(84, 110, 122, 0.6);
  --intellij-dragAndDrop-areaBorderColor: rgba(38, 50, 56, 1);
  --intellij-dragAndDrop-areaForeground: rgba(186, 186, 186, 1);
  --intellij-dragAndDrop-backgroundBorderColor: rgba(38, 50, 56, 1);
  --intellij-dragAndDrop-backgroundColor: rgba(38, 50, 56, 1);
  --intellij-dragAndDrop-borderColor: rgba(0, 150, 136, 1);
  --intellij-dragAndDrop-foregroundColor: rgba(176, 190, 197, 1);
  --intellij-dragAndDrop-rowBackground: rgba(0, 150, 136, 0.27);
  --intellij-dropArea-base: rgba(0, 150, 136, 1);
  --intellij-editor-background: rgba(38, 50, 56, 1);
  --intellij-editor-foreground: rgba(176, 190, 197, 1);
  --intellij-editor-searchField-background: rgba(38, 50, 56, 1);
  --intellij-editor-shortcutForeground: rgba(96, 125, 139, 1);
  --intellij-editor-toolTip-background: rgba(38, 50, 56, 1);
  --intellij-editor-toolTip-border: rgba(42, 55, 62, 1);
  --intellij-editor-toolTip-foreground: rgba(176, 190, 197, 1);
  --intellij-editor-toolTip-iconHoverBackground: rgba(49, 69, 73, 1);
  --intellij-editor-toolTip-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-editor-toolbar-borderColor: rgba(42, 55, 62, 1);
  --intellij-editorGroupsTabs-background: rgba(38, 50, 56, 1);
  --intellij-editorGroupsTabs-borderColor: rgba(42, 55, 62, 1);
  --intellij-editorGroupsTabs-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-editorGroupsTabs-hoverColor: rgba(66, 91, 103, 1);
  --intellij-editorGroupsTabs-inactiveUnderlineColor: rgba(0, 150, 136, 1);
  --intellij-editorGroupsTabs-underlineColor: rgba(0, 150, 136, 1);
  --intellij-editorGroupsTabs-underlinedTabBackground: rgba(49, 69, 73, 1);
  --intellij-editorGroupsTabs-underlinedTabForeground: rgba(176, 190, 197, 1);
  --intellij-editorPane-background: rgba(38, 50, 56, 1);
  --intellij-editorPane-caretForeground: rgba(0, 150, 136, 1);
  --intellij-editorPane-font-family: "Dialog", monospace;
  --intellij-editorPane-font-size: 13px;
  --intellij-editorPane-foreground: rgba(176, 190, 197, 1);
  --intellij-editorPane-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-editorPane-inactiveForeground: rgba(176, 190, 197, 1);
  --intellij-editorPane-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-editorPane-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-editorPane-splitBorder: rgba(42, 55, 62, 1);
  --intellij-editorTabs-active-background: rgba(49, 69, 73, 1);
  --intellij-editorTabs-active-foreground: rgba(176, 190, 197, 1);
  --intellij-editorTabs-active-underlineColor: rgba(0, 150, 136, 1);
  --intellij-editorTabs-background: rgba(38, 50, 56, 1);
  --intellij-editorTabs-borderColor: rgba(42, 55, 62, 1);
  --intellij-editorTabs-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-editorTabs-hoverColor: rgba(66, 91, 103, 1);
  --intellij-editorTabs-hoverMaskColor: rgba(66, 91, 103, 1);
  --intellij-editorTabs-inactive-maskColor: rgba(38, 50, 56, 1);
  --intellij-editorTabs-inactiveColoredFileBackground: rgba(50, 66, 74, 0.5);
  --intellij-editorTabs-inactiveMaskColor: rgba(38, 50, 56, 1);
  --intellij-editorTabs-inactiveUnderlineColor: rgba(0, 150, 136, 1);
  --intellij-editorTabs-selectedBackground: rgba(49, 69, 73, 1);
  --intellij-editorTabs-selectedForeground: rgba(176, 190, 197, 1);
  --intellij-editorTabs-underTabsBorderColor: rgba(46, 60, 67, 1);
  --intellij-editorTabs-underlineColor: rgba(0, 150, 136, 1);
  --intellij-editorTabs-underlinedTabBackground: rgba(49, 69, 73, 1);
  --intellij-editorTabs-underlinedTabForeground: rgba(255, 255, 255, 1);
  --intellij-fileColor-blue: rgba(23, 26, 52, 1);
  --intellij-fileColor-excluded: rgba(46, 60, 67, 1);
  --intellij-fileColor-gray: rgba(46, 60, 67, 1);
  --intellij-fileColor-green: rgba(22, 44, 22, 1);
  --intellij-fileColor-orange: rgba(79, 45, 18, 1);
  --intellij-fileColor-rose: rgba(79, 6, 13, 1);
  --intellij-fileColor-violet: rgba(49, 19, 51, 1);
  --intellij-fileColor-yellow: rgba(63, 55, 27, 1);
  --intellij-flameGraph-jVMBackground: rgba(137, 221, 247, 1);
  --intellij-flameGraph-jVMFocusBackground: rgba(130, 170, 255, 1);
  --intellij-flameGraph-jVMFocusSearchNotMatchedBackground: rgba(171, 121, 103, 1);
  --intellij-flameGraph-jVMSearchNotMatchedBackground: rgba(255, 83, 112, 1);
  --intellij-flameGraph-nativeBackground: rgba(255, 203, 107, 1);
  --intellij-flameGraph-nativeFocusBackground: rgba(247, 140, 108, 1);
  --intellij-flameGraph-nativeFocusSearchNotMatchedBackground: rgba(187, 128, 179, 1);
  --intellij-flameGraph-nativeSearchNotMatchedBackground: rgba(199, 146, 234, 1);
  --intellij-flameGraph-tooltip-foreground: rgba(176, 190, 197, 1);
  --intellij-flameGraph-tooltip-scaleBackground: rgba(54, 56, 57, 1);
  --intellij-flameGraph-tooltip-scaleColor: rgba(54, 88, 128, 1);
  --intellij-focus-activeErrorBorderColor: rgba(229, 57, 53, 1);
  --intellij-focus-activeWarningBorderColor: rgba(255, 182, 44, 1);
  --intellij-focus-borderColor: rgba(0, 150, 136, 0.27);
  --intellij-focus-color: rgba(0, 150, 136, 0.27);
  --intellij-focus-defaultButtonBorderColor: rgba(0, 150, 136, 1);
  --intellij-focus-inactiveErrorBorderColor: rgba(116, 58, 58, 1);
  --intellij-focus-inactiveWarningBorderColor: rgba(127, 108, 0, 1);
  --intellij-formattedTextField-background: rgba(38, 50, 56, 1);
  --intellij-formattedTextField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-formattedTextField-foreground: rgba(176, 190, 197, 1);
  --intellij-formattedTextField-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-formattedTextField-inactiveForeground: rgba(176, 190, 197, 1);
  --intellij-formattedTextField-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-formattedTextField-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-git-log-ref-localBranch: rgba(0, 150, 136, 1);
  --intellij-git-log-ref-other: rgba(96, 125, 139, 1);
  --intellij-git-log-ref-remoteBranch: rgba(176, 190, 197, 1);
  --intellij-git-log-ref-tag: rgba(96, 125, 139, 1);
  --intellij-github-list-tallRow-foreground: rgba(176, 190, 197, 1);
  --intellij-github-list-tallRow-secondary-foreground: rgba(96, 125, 139, 1);
  --intellij-github-list-tallRow-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-github-list-tallRow-selectionBackground-unfocused: rgba(66, 91, 103, 1);
  --intellij-github-list-tallRow-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-github-list-tallRow-selectionForeground-unfocused: rgba(255, 255, 255, 1);
  --intellij-gotItTooltip-animationBackground: rgba(30, 39, 44, 1);
  --intellij-gotItTooltip-background: rgba(38, 50, 56, 1);
  --intellij-gotItTooltip-borderColor: rgba(30, 39, 44, 1);
  --intellij-gotItTooltip-button-contrastBackground: rgba(30, 39, 44, 1);
  --intellij-gotItTooltip-button-endBackground: rgba(46, 60, 67, 1);
  --intellij-gotItTooltip-button-foreground: rgba(176, 190, 197, 1);
  --intellij-gotItTooltip-button-startBackground: rgba(46, 60, 67, 1);
  --intellij-gotItTooltip-codeBackground: rgba(76, 80, 82, 1);
  --intellij-gotItTooltip-codeBorderColor: rgba(108, 112, 126, 1);
  --intellij-gotItTooltip-codeForeground: rgba(176, 190, 197, 1);
  --intellij-gotItTooltip-endBackground: rgba(46, 60, 67, 1);
  --intellij-gotItTooltip-endBorderColor: rgba(46, 60, 67, 1);
  --intellij-gotItTooltip-foreground: rgba(176, 190, 197, 1);
  --intellij-gotItTooltip-header-foreground: rgba(176, 190, 197, 1);
  --intellij-gotItTooltip-imageBorderColor: rgba(30, 39, 44, 1);
  --intellij-gotItTooltip-linkForeground: rgba(0, 150, 136, 1);
  --intellij-gotItTooltip-linkUnderlineHoveredColor: rgba(0, 150, 136, 1);
  --intellij-gotItTooltip-secondaryActionForeground: rgba(96, 125, 139, 1);
  --intellij-gotItTooltip-shortcutBackground: rgba(30, 39, 44, 1);
  --intellij-gotItTooltip-shortcutBorderColor: rgba(42, 55, 62, 1);
  --intellij-gotItTooltip-shortcutForeground: rgba(96, 125, 139, 1);
  --intellij-gotItTooltip-startBackground: rgba(46, 60, 67, 1);
  --intellij-gotItTooltip-startBorderColor: rgba(46, 60, 67, 1);
  --intellij-gotItTooltip-stepForeground: rgba(96, 125, 139, 1);
  --intellij-group-disabledSeparatorColor: rgba(42, 55, 62, 1);
  --intellij-group-separatorColor: rgba(42, 55, 62, 1);
  --intellij-gutterTooltip-borderColor: rgba(38, 50, 56, 1);
  --intellij-gutterTooltip-infoForeground: rgba(96, 125, 139, 1);
  --intellij-gutterTooltip-lineSeparatorColor: rgba(38, 50, 56, 1);
  --intellij-headerColor-active: rgba(38, 50, 56, 1);
  --intellij-headerColor-inactive: rgba(30, 39, 44, 1);
  --intellij-helpBrowser-aiEditor-background: rgba(38, 50, 56, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-background: rgba(38, 50, 56, 1);
  --intellij-helpBrowser-helpBrowserMessage-snippet-moreLines-foreground: rgba(176, 190, 197, 1);
  --intellij-helpBrowser-userMessage-background: rgba(38, 50, 56, 1);
  --intellij-helpBrowser-userMessage-snippet-moreLines-foreground: rgba(176, 190, 197, 1);
  --intellij-helpTooltip-background: rgba(38, 50, 56, 1);
  --intellij-helpTooltip-backgroundColor: rgba(38, 50, 56, 1);
  --intellij-helpTooltip-borderColor: rgba(42, 55, 62, 1);
  --intellij-helpTooltip-foreground: rgba(176, 190, 197, 1);
  --intellij-helpTooltip-infoForeground: rgba(96, 125, 139, 1);
  --intellij-helpTooltip-shortcutForeground: rgba(96, 125, 139, 1);
  --intellij-helpTooltip-shortcutTextColor: rgba(96, 125, 139, 1);
  --intellij-helpTooltip-textColor: rgba(176, 190, 197, 1);
  --intellij-hg-log-ref-branch: rgba(0, 150, 136, 1);
  --intellij-hg-log-ref-closedBranch: rgba(176, 190, 197, 1);
  --intellij-hg-log-ref-localTag: rgba(96, 125, 139, 1);
  --intellij-hg-log-ref-mqTag: rgba(96, 125, 139, 1);
  --intellij-hg-log-ref-tag: rgba(96, 125, 139, 1);
  --intellij-hyperlink-linkColor: rgba(0, 150, 136, 1);
  --intellij-iconBadge-infoBackground: rgba(0, 150, 136, 1);
  --intellij-iconBadge-newUiBackground: rgba(0, 150, 136, 1);
  --intellij-ide-shadow-bottom0Color: rgba(30, 39, 44, 0);
  --intellij-ide-shadow-bottom1Color: rgba(30, 39, 44, 0.098);
  --intellij-ide-shadow-bottomLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomLeft1Color: rgba(30, 39, 44, 0.098);
  --intellij-ide-shadow-bottomRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-bottomRight1Color: rgba(30, 39, 44, 0.098);
  --intellij-ide-shadow-left0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-left1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-right0Color: rgba(30, 39, 44, 0);
  --intellij-ide-shadow-right1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-top1Color: rgba(0, 0, 0, 0.071);
  --intellij-ide-shadow-topLeft0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topLeft1Color: rgba(30, 39, 44, 0.098);
  --intellij-ide-shadow-topRight0Color: rgba(0, 0, 0, 0);
  --intellij-ide-shadow-topRight1Color: rgba(30, 39, 44, 0.098);
  --intellij-inactiveCaption: rgba(50, 66, 74, 1);
  --intellij-inactiveCaptionBorder: rgba(108, 108, 108, 1);
  --intellij-inactiveCaptionText: rgba(96, 125, 139, 1);
  --intellij-info: rgba(255, 255, 255, 1);
  --intellij-infoPanelForeground: rgba(96, 125, 139, 1);
  --intellij-infoText: rgba(96, 125, 139, 1);
  --intellij-informationHint-borderColor: rgba(42, 55, 62, 1);
  --intellij-inlineBanner-hoverBackground: rgba(0, 150, 136, 0.27);
  --intellij-inlineBanner-pressedBackground: rgba(0, 150, 136, 0.27);
  --intellij-inplaceRefactoringPopup-borderColor: rgba(38, 50, 56, 1);
  --intellij-intellijlaf-background: rgba(38, 50, 56, 1);
  --intellij-intellijlaf-foreground: rgba(176, 190, 197, 1);
  --intellij-internalFrame-activeTitleBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-activeTitleForeground: rgba(176, 190, 197, 1);
  --intellij-internalFrame-background: rgba(38, 50, 56, 1);
  --intellij-internalFrame-borderColor: rgba(42, 55, 62, 1);
  --intellij-internalFrame-borderDarkShadow: rgba(0, 255, 0, 1);
  --intellij-internalFrame-borderHighlight: rgba(0, 0, 255, 1);
  --intellij-internalFrame-borderLight: rgba(255, 255, 0, 1);
  --intellij-internalFrame-borderShadow: rgba(255, 0, 0, 1);
  --intellij-internalFrame-inactiveTitleBackground: rgba(38, 50, 56, 1);
  --intellij-internalFrame-inactiveTitleForeground: rgba(96, 125, 139, 1);
  --intellij-internalFrame-optionDialogBackground: rgba(238, 238, 238, 1);
  --intellij-internalFrame-paletteBackground: rgba(38, 50, 56, 1);
  --intellij-label-background: rgba(38, 50, 56, 1);
  --intellij-label-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-label-disabledForegroundColor: rgba(65, 89, 103, 1);
  --intellij-label-disabledShadow: rgba(64, 64, 64, 1);
  --intellij-label-disabledText: rgba(65, 89, 103, 1);
  --intellij-label-errorForeground: rgba(0, 150, 136, 1);
  --intellij-label-foreground: rgba(176, 190, 197, 1);
  --intellij-label-grayForeground: rgba(96, 125, 139, 1);
  --intellij-label-infoForeground: rgba(96, 125, 139, 1);
  --intellij-label-selectedDisabledForeground: rgba(176, 190, 197, 1);
  --intellij-label-selectedForeground: rgba(187, 187, 187, 1);
  --intellij-label-textForeground: rgba(96, 125, 139, 1);
  --intellij-lesson-badge-newLessonBackground: rgba(73, 156, 84, 1);
  --intellij-lesson-badge-newLessonForeground: rgba(254, 254, 254, 1);
  --intellij-lesson-shortcutBackground: rgba(50, 66, 74, 1);
  --intellij-lesson-stepNumberForeground: rgba(254, 254, 254, 1);
  --intellij-lesson-tooltip-background: rgba(30, 39, 44, 1);
  --intellij-lesson-tooltip-borderColor: rgba(30, 39, 44, 1);
  --intellij-lesson-tooltip-foreground: rgba(176, 190, 197, 1);
  --intellij-lesson-tooltip-spanBackground: rgba(46, 60, 67, 1);
  --intellij-lesson-tooltip-spanForeground: rgba(176, 190, 197, 1);
  --intellij-lesson-tooltip-stepNumberForeground: rgba(96, 125, 139, 1);
  --intellij-lineProfiler-hotLine-foreground: rgba(176, 190, 197, 1);
  --intellij-lineProfiler-hotLine-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-lineProfiler-hotLine-labelBackground: rgba(89, 61, 65, 1);
  --intellij-lineProfiler-ignoredLine-foreground: rgba(176, 190, 197, 1);
  --intellij-lineProfiler-ignoredLine-labelBackground: rgba(67, 71, 74, 1);
  --intellij-lineProfiler-line-foreground: rgba(176, 190, 197, 1);
  --intellij-lineProfiler-line-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-lineProfiler-line-labelBackground: rgba(67, 71, 74, 1);
  --intellij-link: rgba(0, 150, 136, 1);
  --intellij-link-activeForeground: rgba(88, 157, 246, 1);
  --intellij-link-background: rgba(38, 50, 56, 1);
  --intellij-link-focusedBorderColor: rgba(0, 150, 136, 1);
  --intellij-link-foreground: rgba(0, 150, 136, 1);
  --intellij-link-hover-foreground: rgba(0, 150, 136, 1);
  --intellij-link-hoverForeground: rgba(88, 157, 246, 1);
  --intellij-link-pressed-foreground: rgba(0, 150, 136, 1);
  --intellij-link-pressedForeground: rgba(0, 150, 136, 1);
  --intellij-link-secondaryForeground: rgba(96, 125, 139, 1);
  --intellij-link-tag-background: rgba(38, 50, 56, 1);
  --intellij-link-tag-foreground: rgba(176, 190, 197, 1);
  --intellij-link-visited-foreground: rgba(0, 150, 136, 1);
  --intellij-link-visitedForeground: rgba(88, 157, 246, 1);
  --intellij-list-background: rgba(38, 50, 56, 1);
  --intellij-list-button-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-list-button-separatorColor: rgba(42, 55, 62, 1);
  --intellij-list-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-list-foreground: rgba(176, 190, 197, 1);
  --intellij-list-hoverBackground: rgba(48, 69, 77, 1);
  --intellij-list-hoverInactiveBackground: rgba(49, 69, 73, 1);
  --intellij-list-line-hoverBackground: rgba(84, 110, 122, 1);
  --intellij-list-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-list-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-list-selectionInactiveBackground: rgba(84, 110, 122, 1);
  --intellij-list-selectionInactiveForeground: rgba(176, 190, 197, 1);
  --intellij-list-tag-background: rgba(46, 60, 67, 1);
  --intellij-list-tag-foreground: rgba(176, 190, 197, 1);
  --intellij-liveIndicator-color: rgba(0, 150, 136, 1);
  --intellij-mainMenu-background: rgba(38, 50, 56, 1);
  --intellij-mainMenu-foreground: rgba(176, 190, 197, 1);
  --intellij-mainMenu-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-mainMenu-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-mainMenu-transparentSelectionBackground: rgba(48, 69, 77, 1);
  --intellij-mainToolbar-background: rgba(38, 50, 56, 1);
  --intellij-mainToolbar-dropdown-background: rgba(38, 50, 56, 1);
  --intellij-mainToolbar-dropdown-foreground: rgba(176, 190, 197, 1);
  --intellij-mainToolbar-dropdown-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-mainToolbar-dropdown-pressedBackground: rgba(76, 80, 82, 1);
  --intellij-mainToolbar-dropdown-transparentHoverBackground: rgba(0, 150, 136, 0.27);
  --intellij-mainToolbar-foreground: rgba(176, 190, 197, 1);
  --intellij-mainToolbar-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-mainToolbar-icon-background: rgba(38, 50, 56, 1);
  --intellij-mainToolbar-icon-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-mainToolbar-icon-pressedBackground: rgba(49, 69, 73, 1);
  --intellij-mainToolbar-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-mainToolbar-pressedBackground: rgba(49, 69, 73, 1);
  --intellij-mainToolbar-separatorColor: rgba(38, 50, 56, 1);
  --intellij-mainWindow-fullScreeControl-background: rgba(66, 91, 103, 1);
  --intellij-mainWindow-tab-background: rgba(38, 50, 56, 1);
  --intellij-mainWindow-tab-borderColor: rgba(42, 55, 62, 1);
  --intellij-mainWindow-tab-foreground: rgba(176, 190, 197, 1);
  --intellij-mainWindow-tab-hoverBackground: rgba(84, 110, 122, 0.6);
  --intellij-mainWindow-tab-hoverForeground: rgba(206, 208, 214, 1);
  --intellij-mainWindow-tab-selectedBackground: rgba(38, 50, 56, 1);
  --intellij-mainWindow-tab-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-mainWindow-tab-selectedInactiveBackground: rgba(31, 41, 46, 1);
  --intellij-mainWindow-tab-separatorColor: rgba(42, 55, 62, 1);
  --intellij-managedIdeBadgeBackground: rgba(38, 50, 56, 1);
  --intellij-managedIdeBadgeBackgroundHover: rgba(0, 150, 136, 0.27);
  --intellij-managedIdeBadgeBorder: rgba(42, 55, 62, 1);
  --intellij-managedIdeMenuItemHover: rgba(84, 110, 122, 1);
  --intellij-material-background: rgba(38, 50, 56, 1);
  --intellij-material-branchColor: rgba(176, 190, 197, 1);
  --intellij-material-contrast: rgba(30, 39, 44, 1);
  --intellij-material-foreground: rgba(176, 190, 197, 1);
  --intellij-material-mergeCommits: rgba(46, 60, 67, 1);
  --intellij-material-primaryColor: rgba(96, 125, 139, 1);
  --intellij-material-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-material-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-material-tab-backgroundColor: rgba(38, 50, 56, 1);
  --intellij-material-tab-borderColor: rgba(0, 150, 136, 1);
  --intellij-material-tagColor: rgba(96, 125, 139, 1);
  --intellij-memoryIndicator-allocatedBackground: rgba(50, 66, 74, 1);
  --intellij-memoryIndicator-unusedColor: rgba(50, 66, 74, 1);
  --intellij-memoryIndicator-usedBackground: rgba(66, 91, 103, 1);
  --intellij-memoryIndicator-usedColor: rgba(66, 91, 103, 1);
  --intellij-menu: rgba(38, 50, 56, 1);
  --intellij-menu-acceleratorForeground: rgba(96, 125, 139, 1);
  --intellij-menu-acceleratorSelectionForeground: rgba(0, 0, 0, 1);
  --intellij-menu-background: rgba(38, 50, 56, 1);
  --intellij-menu-borderColor: rgba(42, 55, 62, 1);
  --intellij-menu-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-menu-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-menu-foreground: rgba(176, 190, 197, 1);
  --intellij-menu-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-menu-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-menu-separatorColor: rgba(42, 55, 62, 1);
  --intellij-menuBar-background: rgba(38, 50, 56, 1);
  --intellij-menuBar-borderColor: rgba(38, 50, 56, 1);
  --intellij-menuBar-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-menuBar-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-menuBar-foreground: rgba(176, 190, 197, 1);
  --intellij-menuBar-highlight: rgba(38, 50, 56, 1);
  --intellij-menuBar-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-menuBar-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-menuBar-shadow: rgba(38, 50, 56, 1);
  --intellij-menuItem-acceleratorForeground: rgba(96, 125, 139, 1);
  --intellij-menuItem-acceleratorSelectionForeground: rgba(187, 187, 187, 1);
  --intellij-menuItem-background: rgba(38, 50, 56, 1);
  --intellij-menuItem-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-menuItem-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-menuItem-foreground: rgba(176, 190, 197, 1);
  --intellij-menuItem-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-menuItem-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-menuText: rgba(176, 190, 197, 1);
  --intellij-mlModelBinding-viewer-codeEditor-background: rgba(38, 50, 56, 1);
  --intellij-mnemonicIcon-background: rgba(66, 91, 103, 1);
  --intellij-mnemonicIcon-borderColor: rgba(66, 91, 103, 1);
  --intellij-mnemonicIcon-foreground: rgba(176, 190, 197, 1);
  --intellij-navBar-arrowColor: rgba(176, 190, 197, 1);
  --intellij-navBar-borderColor: rgba(42, 55, 62, 1);
  --intellij-navBar-selectedColor: rgba(0, 150, 136, 1);
  --intellij-newClass-panel-background: rgba(38, 50, 56, 1);
  --intellij-newClass-searchField-background: rgba(30, 39, 44, 1);
  --intellij-newPSD-warning: rgba(0, 150, 136, 1);
  --intellij-newUiOnboarding-dialog-background: rgba(50, 66, 74, 1);
  --intellij-notification-background: rgba(30, 39, 44, 1);
  --intellij-notification-borderColor: rgba(42, 55, 62, 1);
  --intellij-notification-error-foreground: rgba(0, 150, 136, 1);
  --intellij-notification-errorForeground: rgba(0, 150, 136, 1);
  --intellij-notification-foreground: rgba(176, 190, 197, 1);
  --intellij-notification-link-foreground: rgba(0, 150, 136, 1);
  --intellij-notification-linkForeground: rgba(0, 150, 136, 1);
  --intellij-notification-moreButton-background: rgba(46, 60, 67, 1);
  --intellij-notification-moreButton-foreground: rgba(176, 190, 197, 1);
  --intellij-notification-moreButton-innerBorderColor: rgba(46, 60, 67, 1);
  --intellij-notification-shadow-bottom0Color: rgba(30, 39, 44, 0);
  --intellij-notification-shadow-bottom1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-bottomLeft0Color: rgba(30, 39, 44, 0);
  --intellij-notification-shadow-bottomLeft1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-shadow-bottomRight0Color: rgba(30, 39, 44, 0);
  --intellij-notification-shadow-bottomRight1Color: rgba(30, 39, 44, 0.098);
  --intellij-notification-shadow-left0Color: rgba(30, 39, 44, 0);
  --intellij-notification-shadow-left1Color: rgba(30, 39, 44, 0.098);
  --intellij-notification-shadow-right0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-right1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-top0Color: rgba(0, 0, 0, 0);
  --intellij-notification-shadow-top1Color: rgba(0, 0, 0, 0.063);
  --intellij-notification-shadow-topLeft0Color: rgba(30, 39, 44, 0);
  --intellij-notification-shadow-topLeft1Color: rgba(30, 39, 44, 0.098);
  --intellij-notification-shadow-topRight0Color: rgba(30, 39, 44, 0);
  --intellij-notification-shadow-topRight1Color: rgba(0, 0, 0, 0.035);
  --intellij-notification-toolWindow-errorBackground: rgba(183, 28, 28, 1);
  --intellij-notification-toolWindow-errorBorderColor: rgba(183, 28, 28, 1);
  --intellij-notification-toolWindow-errorForeground: rgba(176, 190, 197, 1);
  --intellij-notification-toolWindow-infoBackground: rgba(30, 39, 44, 1);
  --intellij-notification-toolWindow-infoBorderColor: rgba(30, 39, 44, 1);
  --intellij-notification-toolWindow-infoForeground: rgba(176, 190, 197, 1);
  --intellij-notification-toolWindow-informativeBackground: rgba(30, 39, 44, 1);
  --intellij-notification-toolWindow-informativeBorderColor: rgba(30, 39, 44, 1);
  --intellij-notification-toolWindow-informativeForeground: rgba(176, 190, 197, 1);
  --intellij-notification-toolWindow-warningBackground: rgba(93, 64, 55, 1);
  --intellij-notification-toolWindow-warningBorderColor: rgba(93, 64, 55, 1);
  --intellij-notification-toolWindow-warningForeground: rgba(176, 190, 197, 1);
  --intellij-notification-toolWindowError-background: rgba(183, 28, 28, 1);
  --intellij-notification-toolWindowError-borderColor: rgba(183, 28, 28, 1);
  --intellij-notification-toolWindowError-foreground: rgba(176, 190, 197, 1);
  --intellij-notification-toolWindowInfo-background: rgba(30, 39, 44, 1);
  --intellij-notification-toolWindowInfo-borderColor: rgba(30, 39, 44, 1);
  --intellij-notification-toolWindowInfo-foreground: rgba(176, 190, 197, 1);
  --intellij-notification-toolWindowWarning-background: rgba(93, 64, 55, 1);
  --intellij-notification-toolWindowWarning-borderColor: rgba(93, 64, 55, 1);
  --intellij-notification-toolWindowWarning-foreground: rgba(176, 190, 197, 1);
  --intellij-notification-welcomeScreen-separatorColor: rgba(42, 55, 62, 1);
  --intellij-notifications-background: rgba(30, 39, 44, 1);
  --intellij-notifications-borderColor: rgba(42, 55, 62, 1);
  --intellij-notificationsToolwindow-newNotification-background: rgba(30, 39, 44, 1);
  --intellij-notificationsToolwindow-newNotification-hoverBackground: rgba(48, 69, 77, 1);
  --intellij-notificationsToolwindow-notification-hoverBackground: rgba(48, 69, 77, 1);
  --intellij-onePixelDivider-background: rgba(42, 55, 62, 1);
  --intellij-optionButton-default-separatorColor: rgba(49, 69, 73, 1);
  --intellij-optionButton-separatorColor: rgba(46, 60, 67, 1);
  --intellij-optionPane-background: rgba(38, 50, 56, 1);
  --intellij-optionPane-foreground: rgba(176, 190, 197, 1);
  --intellij-optionPane-messageForeground: rgba(187, 187, 187, 1);
  --intellij-outline-color: rgba(46, 60, 67, 1);
  --intellij-outline-disabledColor: rgba(65, 89, 103, 1);
  --intellij-outline-focusedColor: rgba(0, 150, 136, 1);
  --intellij-packageDetails-border: rgba(0, 150, 136, 1);
  --intellij-packageDetails-infoBanner: rgba(0, 150, 136, 1);
  --intellij-packageSearch-packageTag-background: rgba(38, 50, 56, 1);
  --intellij-packageSearch-packageTag-foreground: rgba(176, 190, 197, 1);
  --intellij-packageSearch-packageTag-hoverBackground: rgba(84, 110, 122, 0.8);
  --intellij-packageSearch-packageTag-selectedBackground: rgba(47, 101, 202, 1);
  --intellij-packageSearch-packageTag-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-packageSearch-packageTagSelected-background: rgba(38, 50, 56, 1);
  --intellij-packageSearch-packageTagSelected-foreground: rgba(176, 190, 197, 1);
  --intellij-packageSearch-searchResult-background: rgba(38, 50, 56, 1);
  --intellij-packageSearch-searchResult-hoverBackground: rgba(84, 110, 122, 0.8);
  --intellij-packageSearch-searchResult-packageTag-background: rgba(38, 50, 56, 1);
  --intellij-packageSearch-searchResult-packageTag-foreground: rgba(176, 190, 197, 1);
  --intellij-packageSearch-searchResult-packageTag-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-packageSearch-searchResult-packageTag-selectedBackground: rgba(47, 101, 202, 1);
  --intellij-packageSearch-searchResult-packageTag-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-panel-background: rgba(38, 50, 56, 1);
  --intellij-panel-foreground: rgba(176, 190, 197, 1);
  --intellij-panel-mouseShortcutBackground: rgba(38, 50, 56, 1);
  --intellij-parameterInfo-background: rgba(38, 50, 56, 1);
  --intellij-parameterInfo-borderColor: rgba(49, 69, 73, 1);
  --intellij-parameterInfo-contextHelp-foreground: rgba(96, 125, 139, 1);
  --intellij-parameterInfo-currentOverloadBackground: rgba(66, 91, 103, 1);
  --intellij-parameterInfo-currentParameterForeground: rgba(0, 150, 136, 1);
  --intellij-parameterInfo-disabledColor: rgba(65, 89, 103, 1);
  --intellij-parameterInfo-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-parameterInfo-foreground: rgba(176, 190, 197, 1);
  --intellij-parameterInfo-highlightedColor: rgba(0, 150, 136, 1);
  --intellij-parameterInfo-infoForeground: rgba(96, 125, 139, 1);
  --intellij-parameterInfo-lineSeparatorColor: rgba(49, 69, 73, 1);
  --intellij-passwordField-background: rgba(38, 50, 56, 1);
  --intellij-passwordField-capsLockIconColor: rgba(0, 0, 0, 0.39);
  --intellij-passwordField-caretForeground: rgba(0, 150, 136, 1);
  --intellij-passwordField-font-family: "Operator Mono", system-ui, sans-serif;
  --intellij-passwordField-font-size: 13px;
  --intellij-passwordField-foreground: rgba(176, 190, 197, 1);
  --intellij-passwordField-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-passwordField-inactiveForeground: rgba(176, 190, 197, 1);
  --intellij-passwordField-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-passwordField-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-plugins-background: rgba(38, 50, 56, 1);
  --intellij-plugins-borderColor: rgba(42, 55, 62, 1);
  --intellij-plugins-button-installBackground: rgba(46, 60, 67, 1);
  --intellij-plugins-button-installBorderColor: rgba(46, 60, 67, 1);
  --intellij-plugins-button-installFillBackground: rgba(46, 60, 67, 1);
  --intellij-plugins-button-installFillForeground: rgba(65, 89, 103, 1);
  --intellij-plugins-button-installFocusedBackground: rgba(66, 91, 103, 1);
  --intellij-plugins-button-installForeground: rgba(176, 190, 197, 1);
  --intellij-plugins-button-updateBackground: rgba(46, 60, 67, 1);
  --intellij-plugins-button-updateBorderColor: rgba(46, 60, 67, 1);
  --intellij-plugins-button-updateForeground: rgba(176, 190, 197, 1);
  --intellij-plugins-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-plugins-eapTagBackground: rgba(66, 91, 103, 1);
  --intellij-plugins-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-plugins-lightSelectionBackground: rgba(49, 69, 73, 1);
  --intellij-plugins-paidTagBackground: rgba(66, 91, 103, 1);
  --intellij-plugins-screenshotPagination-currentImage-fillColor: rgba(0, 150, 136, 1);
  --intellij-plugins-searchField-background: rgba(38, 50, 56, 1);
  --intellij-plugins-searchField-borderColor: rgba(42, 55, 62, 1);
  --intellij-plugins-sectionHeader-background: rgba(38, 50, 56, 1);
  --intellij-plugins-sectionHeader-foreground: rgba(176, 190, 197, 1);
  --intellij-plugins-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-plugins-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-plugins-suggestedLabelBackground: rgba(46, 60, 67, 1);
  --intellij-plugins-tab-active-background: rgba(49, 69, 73, 1);
  --intellij-plugins-tab-active-foreground: rgba(255, 255, 255, 1);
  --intellij-plugins-tab-hover-background: rgba(49, 69, 73, 1);
  --intellij-plugins-tab-hoverBackground: rgba(49, 69, 73, 1);
  --intellij-plugins-tab-selectedBackground: rgba(49, 69, 73, 1);
  --intellij-plugins-tab-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-plugins-tagBackground: rgba(66, 91, 103, 1);
  --intellij-plugins-tagForeground: rgba(0, 150, 136, 1);
  --intellij-plugins-trialTagBackground: rgba(66, 91, 103, 1);
  --intellij-popup-advertiser-background: rgba(38, 50, 56, 1);
  --intellij-popup-advertiser-borderColor: rgba(42, 55, 62, 1);
  --intellij-popup-advertiser-foreground: rgba(96, 125, 139, 1);
  --intellij-popup-background: rgba(38, 50, 56, 1);
  --intellij-popup-border-color: rgba(30, 39, 44, 1);
  --intellij-popup-border-inactiveColor: rgba(38, 50, 56, 1);
  --intellij-popup-borderColor: rgba(42, 55, 62, 1);
  --intellij-popup-header-activeBackground: rgba(74, 78, 82, 1);
  --intellij-popup-header-activeForeground: rgba(176, 190, 197, 1);
  --intellij-popup-header-inactiveBackground: rgba(30, 39, 44, 1);
  --intellij-popup-header-inactiveForeground: rgba(65, 89, 103, 1);
  --intellij-popup-inactiveBorderColor: rgba(86, 86, 86, 1);
  --intellij-popup-mnemonicForeground: rgba(96, 125, 139, 1);
  --intellij-popup-preferences-background: rgba(38, 50, 56, 1);
  --intellij-popup-preferences-borderColor: rgba(38, 50, 56, 1);
  --intellij-popup-separator-color: rgba(42, 55, 62, 1);
  --intellij-popup-separator-foreground: rgba(176, 190, 197, 1);
  --intellij-popup-separatorColor: rgba(50, 66, 74, 1);
  --intellij-popup-separatorForeground: rgba(176, 190, 197, 1);
  --intellij-popup-toolbar-background: rgba(30, 39, 44, 1);
  --intellij-popup-toolbar-border-color: rgba(30, 39, 44, 1);
  --intellij-popup-toolbar-borderColor: rgba(42, 55, 62, 1);
  --intellij-popup-toolbar-floating-background: rgba(30, 39, 44, 1);
  --intellij-popupMenu-background: rgba(38, 50, 56, 1);
  --intellij-popupMenu-foreground: rgba(176, 190, 197, 1);
  --intellij-popupMenu-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-popupMenu-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-popupMenu-translucentBackground: rgba(38, 50, 56, 1);
  --intellij-presentationAssistant-bright-keymapLabel: rgba(96, 125, 139, 1);
  --intellij-presentationAssistant-bright-popup-border: rgba(42, 55, 62, 1);
  --intellij-presentationAssistant-bright-popup-foreground: rgba(176, 190, 197, 1);
  --intellij-presentationAssistant-bright-popupBackground: rgba(30, 39, 44, 1);
  --intellij-presentationAssistant-pale-keymapLabel: rgba(96, 125, 139, 1);
  --intellij-presentationAssistant-pale-popup-border: rgba(42, 55, 62, 1);
  --intellij-presentationAssistant-pale-popup-foreground: rgba(176, 190, 197, 1);
  --intellij-presentationAssistant-pale-popupBackground: rgba(30, 39, 44, 1);
  --intellij-profiler-chartSlider-foreground: rgba(176, 190, 197, 1);
  --intellij-profiler-chartSlider-lineColor: rgba(154, 167, 176, 1);
  --intellij-profiler-cpuChart-background: rgba(38, 50, 56, 1);
  --intellij-profiler-cpuChart-borderColor: rgba(42, 55, 62, 1);
  --intellij-profiler-cpuChart-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-profiler-cpuChart-inactiveBorderColor: rgba(79, 106, 75, 1);
  --intellij-profiler-cpuChart-pointBackground: rgba(98, 150, 37, 1);
  --intellij-profiler-cpuChart-pointBorderColor: rgba(60, 63, 65, 1);
  --intellij-profiler-liveChart-horizontalAxisColor: rgba(209, 209, 209, 1);
  --intellij-profiler-memoryChart-background: rgba(38, 50, 56, 1);
  --intellij-profiler-memoryChart-borderColor: rgba(42, 55, 62, 1);
  --intellij-profiler-memoryChart-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-profiler-memoryChart-inactiveBorderColor: rgba(71, 101, 137, 1);
  --intellij-profiler-memoryChart-pointBackground: rgba(88, 157, 246, 1);
  --intellij-profiler-memoryChart-pointBorderColor: rgba(60, 63, 65, 1);
  --intellij-profiler-neutralLifecycleEvent: rgba(50, 66, 74, 1);
  --intellij-profiler-timer-background: rgba(38, 50, 56, 1);
  --intellij-profiler-timer-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-profiler-timer-foreground: rgba(176, 190, 197, 1);
  --intellij-profiler-trackBackground: rgba(46, 60, 67, 1);
  --intellij-progressBar-background: rgba(38, 50, 56, 1);
  --intellij-progressBar-failedColor: rgba(231, 72, 72, 1);
  --intellij-progressBar-failedEndColor: rgba(244, 162, 160, 1);
  --intellij-progressBar-foreground: rgba(176, 190, 197, 1);
  --intellij-progressBar-halfColor: rgba(66, 91, 103, 1);
  --intellij-progressBar-indeterminateEndColor: rgba(131, 131, 131, 1);
  --intellij-progressBar-indeterminateStartColor: rgba(105, 105, 105, 1);
  --intellij-progressBar-passedColor: rgba(0, 143, 80, 1);
  --intellij-progressBar-passedEndColor: rgba(93, 196, 143, 1);
  --intellij-progressBar-progressColor: rgba(160, 160, 160, 1);
  --intellij-progressBar-selectionBackground: rgba(66, 91, 103, 1);
  --intellij-progressBar-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-progressBar-trackColor: rgba(66, 91, 103, 1);
  --intellij-progressIcon-color: rgba(0, 150, 136, 1);
  --intellij-psiViewer-referenceHighlightColor: rgba(0, 150, 136, 1);
  --intellij-radioButton-background: rgba(38, 50, 56, 1);
  --intellij-radioButton-darcula-borderColor1: rgba(176, 190, 197, 1);
  --intellij-radioButton-darcula-selectionDisabledColor: rgba(38, 50, 56, 1);
  --intellij-radioButton-darcula-selectionDisabledShadowColor: rgba(60, 60, 60, 1);
  --intellij-radioButton-darcula-selectionEnabledColor: rgba(170, 170, 170, 1);
  --intellij-radioButton-darcula-selectionEnabledShadowColor: rgba(0, 150, 136, 1);
  --intellij-radioButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-radioButton-disabledText: rgba(65, 89, 103, 1);
  --intellij-radioButton-focusColor: rgba(0, 150, 136, 1);
  --intellij-radioButton-foreground: rgba(176, 190, 197, 1);
  --intellij-radioButton-highlight: rgba(255, 255, 255, 1);
  --intellij-radioButton-light: rgba(8, 74, 217, 1);
  --intellij-radioButton-select: rgba(255, 102, 102, 1);
  --intellij-radioButton-selectionDisabledColor: rgba(38, 50, 56, 1);
  --intellij-radioButton-selectionDisabledShadowColor: rgba(0, 150, 136, 1);
  --intellij-radioButton-selectionEnabledColor: rgba(0, 150, 136, 1);
  --intellij-radioButton-selectionEnabledShadowColor: rgba(0, 150, 136, 1);
  --intellij-radioButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-radioButtonMenuItem-acceleratorForeground: rgba(96, 125, 139, 1);
  --intellij-radioButtonMenuItem-acceleratorSelectionForeground: rgba(0, 0, 0, 1);
  --intellij-radioButtonMenuItem-background: rgba(38, 50, 56, 1);
  --intellij-radioButtonMenuItem-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-radioButtonMenuItem-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-radioButtonMenuItem-foreground: rgba(176, 190, 197, 1);
  --intellij-radioButtonMenuItem-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-radioButtonMenuItem-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-recentProject-color1-avatar-end: rgba(233, 128, 111, 1);
  --intellij-recentProject-color1-avatar-start: rgba(224, 136, 85, 1);
  --intellij-recentProject-color1-mainToolbarGradientStart: rgba(101, 75, 64, 1);
  --intellij-recentProject-color2-avatar-end: rgba(187, 127, 25, 1);
  --intellij-recentProject-color2-avatar-start: rgba(176, 139, 20, 1);
  --intellij-recentProject-color2-mainToolbarGradientStart: rgba(83, 76, 51, 1);
  --intellij-recentProject-color3-avatar-end: rgba(135, 170, 89, 1);
  --intellij-recentProject-color3-avatar-start: rgba(161, 163, 89, 1);
  --intellij-recentProject-color3-mainToolbarGradientStart: rgba(69, 80, 56, 1);
  --intellij-recentProject-color4-avatar-end: rgba(97, 131, 236, 1);
  --intellij-recentProject-color4-avatar-start: rgba(59, 146, 184, 1);
  --intellij-recentProject-color4-mainToolbarGradientStart: rgba(49, 81, 95, 1);
  --intellij-recentProject-color5-avatar-end: rgba(122, 100, 240, 1);
  --intellij-recentProject-color5-avatar-start: rgba(53, 116, 240, 1);
  --intellij-recentProject-color5-mainToolbarGradientStart: rgba(52, 76, 125, 1);
  --intellij-recentProject-color6-avatar-end: rgba(169, 86, 207, 1);
  --intellij-recentProject-color6-avatar-start: rgba(200, 77, 143, 1);
  --intellij-recentProject-color6-mainToolbarGradientStart: rgba(93, 53, 74, 1);
  --intellij-recentProject-color7-avatar-end: rgba(168, 77, 224, 1);
  --intellij-recentProject-color7-avatar-start: rgba(149, 90, 224, 1);
  --intellij-recentProject-color7-mainToolbarGradientStart: rgba(79, 62, 101, 1);
  --intellij-recentProject-color8-avatar-end: rgba(39, 156, 205, 1);
  --intellij-recentProject-color8-avatar-start: rgba(36, 163, 148, 1);
  --intellij-recentProject-color8-mainToolbarGradientStart: rgba(29, 71, 68, 1);
  --intellij-recentProject-color9-avatar-end: rgba(61, 150, 139, 1);
  --intellij-recentProject-color9-avatar-start: rgba(95, 173, 101, 1);
  --intellij-recentProject-color9-mainToolbarGradientStart: rgba(62, 85, 64, 1);
  --intellij-review-avatar-border-status-accepted: rgba(0, 150, 136, 1);
  --intellij-review-avatar-border-status-needReview: rgba(66, 91, 103, 1);
  --intellij-review-branch-background: rgba(46, 60, 67, 1);
  --intellij-review-branch-background-hover: rgba(66, 91, 103, 1);
  --intellij-review-chatItem-bubblePanel-border: rgba(46, 60, 67, 1);
  --intellij-review-chatItem-hover: rgba(49, 69, 73, 1);
  --intellij-review-editor-line-status-marker: rgba(0, 150, 136, 1);
  --intellij-review-metaInfo-statusLine-blue: rgba(0, 150, 136, 1);
  --intellij-review-metaInfo-statusLine-gray: rgba(46, 60, 67, 1);
  --intellij-review-state-background: rgba(50, 66, 74, 1);
  --intellij-review-state-foreground: rgba(140, 140, 140, 1);
  --intellij-review-timeline-thread-diff-anchorLine: rgba(49, 69, 73, 1);
  --intellij-reviewList-state-background: rgba(50, 66, 74, 1);
  --intellij-reviewList-state-foreground: rgba(176, 190, 197, 1);
  --intellij-runToolbar-debug-activeBackground: rgba(66, 91, 103, 1);
  --intellij-runToolbar-profile-activeBackground: rgba(66, 91, 103, 1);
  --intellij-runToolbar-run-activeBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-background: rgba(46, 60, 67, 1);
  --intellij-runWidget-debug-activeBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-foreground: rgba(255, 255, 255, 1);
  --intellij-runWidget-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-iconColor: rgba(176, 190, 197, 1);
  --intellij-runWidget-leftHoverBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-leftPressedBackground: rgba(49, 69, 73, 1);
  --intellij-runWidget-pressedBackground: rgba(49, 69, 73, 1);
  --intellij-runWidget-profile-activeBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-run-activeBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-runIconColor: rgba(0, 150, 136, 1);
  --intellij-runWidget-running-background: rgba(66, 91, 103, 1);
  --intellij-runWidget-running-foreground: rgba(255, 255, 255, 1);
  --intellij-runWidget-running-leftHoverBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-running-leftPressedBackground: rgba(49, 69, 73, 1);
  --intellij-runWidget-running-separatorColor: rgba(42, 55, 62, 1);
  --intellij-runWidget-runningBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-runningForeground: rgba(255, 255, 255, 1);
  --intellij-runWidget-separatorColor: rgba(42, 55, 62, 1);
  --intellij-runWidget-stopBackground: rgba(50, 66, 74, 1);
  --intellij-runWidget-stopButton-background: rgba(46, 60, 67, 1);
  --intellij-runWidget-stopButton-foreground: rgba(176, 190, 197, 1);
  --intellij-runWidget-stopButton-leftHoverBackground: rgba(66, 91, 103, 1);
  --intellij-runWidget-stopButton-leftPressedBackground: rgba(49, 69, 73, 1);
  --intellij-screenView-borderColor: rgba(42, 55, 62, 1);
  --intellij-screenView-defaultBorderColor: rgba(0, 0, 0, 1);
  --intellij-screenView-hoveredBorderColor: rgba(66, 91, 103, 1);
  --intellij-screenView-selectedBorderColor: rgba(84, 110, 122, 1);
  --intellij-scrollBar-background: rgba(38, 50, 56, 1);
  --intellij-scrollBar-foreground: rgba(176, 190, 197, 1);
  --intellij-scrollBar-hoverThumbBorderColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-hoverThumbColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-hoverTrackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollBar-mac-hoverThumbBorderColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-mac-hoverThumbColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-mac-hoverTrackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollBar-mac-thumbBorderColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-mac-thumbColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-mac-trackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollBar-mac-transparent-hoverThumbBorderColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-mac-transparent-hoverThumbColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-mac-transparent-hoverTrackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollBar-mac-transparent-thumbBorderColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-mac-transparent-thumbColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-mac-transparent-trackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollBar-thumb: rgba(30, 39, 44, 1);
  --intellij-scrollBar-thumb-hovered-background: rgba(0, 150, 136, 1);
  --intellij-scrollBar-thumb-nonOpaque-hovered-background: rgba(0, 150, 136, 1);
  --intellij-scrollBar-thumbBorderColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-thumbColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-thumbDarkShadow: rgba(0, 0, 0, 1);
  --intellij-scrollBar-thumbHighlight: rgba(255, 255, 255, 1);
  --intellij-scrollBar-thumbShadow: rgba(0, 0, 0, 0.27);
  --intellij-scrollBar-track: rgba(154, 154, 154, 1);
  --intellij-scrollBar-trackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollBar-trackHighlight: rgba(0, 0, 0, 1);
  --intellij-scrollBar-transparent-hoverThumbBorderColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-transparent-hoverThumbColor: rgba(0, 150, 136, 0.29);
  --intellij-scrollBar-transparent-hoverTrackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollBar-transparent-thumbBorderColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-transparent-thumbColor: rgba(0, 150, 136, 0.2);
  --intellij-scrollBar-transparent-trackColor: rgba(38, 50, 56, 0.3);
  --intellij-scrollPane-background: rgba(38, 50, 56, 1);
  --intellij-scrollPane-foreground: rgba(176, 190, 197, 1);
  --intellij-scrollbar: rgba(154, 154, 154, 1);
  --intellij-searchEverywhere-advertiser-background: rgba(30, 39, 44, 1);
  --intellij-searchEverywhere-advertiser-foreground: rgba(176, 190, 197, 1);
  --intellij-searchEverywhere-background: rgba(38, 50, 56, 1);
  --intellij-searchEverywhere-dialog-background: rgba(38, 50, 56, 1);
  --intellij-searchEverywhere-dialog-settingsBackground: rgba(38, 50, 56, 1);
  --intellij-searchEverywhere-foreground: rgba(176, 190, 197, 1);
  --intellij-searchEverywhere-header-background: rgba(38, 50, 56, 1);
  --intellij-searchEverywhere-list-separator-color: rgba(42, 55, 62, 1);
  --intellij-searchEverywhere-list-separator-foreground: rgba(42, 55, 62, 1);
  --intellij-searchEverywhere-list-separatorColor: rgba(42, 55, 62, 1);
  --intellij-searchEverywhere-list-separatorForeground: rgba(96, 125, 139, 1);
  --intellij-searchEverywhere-list-settingsBackground: rgba(38, 50, 56, 1);
  --intellij-searchEverywhere-searchField-background: rgba(38, 50, 56, 1);
  --intellij-searchEverywhere-searchField-border-color: rgba(66, 91, 103, 1);
  --intellij-searchEverywhere-searchField-borderColor: rgba(42, 55, 62, 1);
  --intellij-searchEverywhere-searchField-grayForeground: rgba(65, 89, 103, 1);
  --intellij-searchEverywhere-searchField-infoForeground: rgba(65, 89, 103, 1);
  --intellij-searchEverywhere-shortcutForeground: rgba(96, 125, 139, 1);
  --intellij-searchEverywhere-tab-active-background: rgba(66, 91, 103, 1);
  --intellij-searchEverywhere-tab-active-foreground: rgba(255, 255, 255, 1);
  --intellij-searchEverywhere-tab-selected-background: rgba(66, 91, 103, 1);
  --intellij-searchEverywhere-tab-selected-foreground: rgba(255, 255, 255, 1);
  --intellij-searchEverywhere-tab-selectedBackground: rgba(85, 90, 94, 1);
  --intellij-searchEverywhere-tab-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-searchField-errorBackground: rgba(30, 39, 44, 1);
  --intellij-searchFieldWithExtension-background: rgba(38, 50, 56, 1);
  --intellij-searchMatch-endBackground: rgba(0, 150, 136, 1);
  --intellij-searchMatch-endColor: rgba(0, 150, 136, 1);
  --intellij-searchMatch-startBackground: rgba(0, 150, 136, 1);
  --intellij-searchMatch-startColor: rgba(0, 150, 136, 1);
  --intellij-searchOption-selectedBackground: rgba(49, 69, 73, 1);
  --intellij-searchOption-selectedHoveredBackground: rgba(66, 91, 103, 1);
  --intellij-searchOption-selectedPressedBackground: rgba(66, 91, 103, 1);
  --intellij-searchResults-ordinal-file-foreground: rgba(96, 125, 139, 1);
  --intellij-searchResults-repeated-file-foreground: rgba(176, 190, 197, 1);
  --intellij-segmentedButton-focusedSelectedButtonColor: rgba(61, 75, 92, 1);
  --intellij-segmentedButton-selectedButtonColor: rgba(46, 60, 67, 1);
  --intellij-segmentedButton-selectedEndBorderColor: rgba(46, 60, 67, 1);
  --intellij-segmentedButton-selectedStartBorderColor: rgba(94, 96, 96, 1);
  --intellij-separator-background: rgba(50, 66, 74, 1);
  --intellij-separator-foreground: rgba(176, 190, 197, 1);
  --intellij-separator-highlight: rgba(255, 255, 255, 1);
  --intellij-separator-separatorColor: rgba(42, 55, 62, 1);
  --intellij-separator-shadow: rgba(0, 0, 0, 0.27);
  --intellij-settings-spotlight-borderColor: rgba(0, 150, 136, 1);
  --intellij-shortcut-borderColor: rgba(42, 55, 62, 1);
  --intellij-shortcutForeground: rgba(96, 125, 139, 1);
  --intellij-sidePanel-background: rgba(38, 50, 56, 1);
  --intellij-slider-background: rgba(38, 50, 56, 1);
  --intellij-slider-buttonBorderColor: rgba(0, 150, 136, 1);
  --intellij-slider-buttonColor: rgba(0, 150, 136, 1);
  --intellij-slider-focus: rgba(0, 0, 0, 1);
  --intellij-slider-foreground: rgba(176, 190, 197, 1);
  --intellij-slider-highlight: rgba(255, 255, 255, 1);
  --intellij-slider-shadow: rgba(0, 0, 0, 0.27);
  --intellij-slider-thumb: rgba(0, 150, 136, 1);
  --intellij-slider-tickColor: rgba(50, 66, 74, 1);
  --intellij-slider-track: rgba(49, 69, 73, 1);
  --intellij-slider-trackColor: rgba(49, 69, 73, 1);
  --intellij-slider-trackDisabled: rgba(66, 91, 103, 1);
  --intellij-space-review-acceptedOutline: rgba(84, 181, 118, 1);
  --intellij-space-review-diffAnchorBackground: rgba(50, 66, 74, 1);
  --intellij-space-review-waitForResponseOutline: rgba(140, 211, 236, 1);
  --intellij-space-review-workingOutline: rgba(255, 148, 102, 1);
  --intellij-speedSearch-background: rgba(38, 50, 56, 1);
  --intellij-speedSearch-borderColor: rgba(42, 55, 62, 1);
  --intellij-speedSearch-errorForeground: rgba(255, 100, 100, 1);
  --intellij-speedSearch-foreground: rgba(176, 190, 197, 1);
  --intellij-spinner-background: rgba(38, 50, 56, 1);
  --intellij-spinner-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-spinner-foreground: rgba(176, 190, 197, 1);
  --intellij-spinner-selectionForeground: rgba(17, 17, 17, 1);
  --intellij-splitPane-background: rgba(38, 50, 56, 1);
  --intellij-splitPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-splitPane-highlight: rgba(60, 63, 65, 1);
  --intellij-splitPane-shadow: rgba(0, 0, 0, 0.27);
  --intellij-splitPaneDivider-draggingColor: rgba(64, 64, 64, 1);
  --intellij-stateWidget-activeBackground: rgba(46, 60, 67, 1);
  --intellij-statusBar-background: rgba(38, 50, 56, 0);
  --intellij-statusBar-borderColor: rgba(42, 55, 62, 1);
  --intellij-statusBar-bottomColor: rgba(38, 50, 56, 1);
  --intellij-statusBar-breadcrumbs-floatingBackground: rgba(48, 69, 77, 1);
  --intellij-statusBar-breadcrumbs-floatingForeground: rgba(176, 190, 197, 1);
  --intellij-statusBar-breadcrumbs-foreground: rgba(176, 190, 197, 1);
  --intellij-statusBar-breadcrumbs-hoverBackground: rgba(84, 110, 122, 0.8);
  --intellij-statusBar-breadcrumbs-hoverForeground: rgba(176, 190, 197, 1);
  --intellij-statusBar-breadcrumbs-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-statusBar-breadcrumbs-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-statusBar-breadcrumbs-selectionInactiveBackground: rgba(46, 60, 67, 1);
  --intellij-statusBar-breadcrumbs-selectionInactiveForeground: rgba(176, 190, 197, 1);
  --intellij-statusBar-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-statusBar-lightEditBackground: rgba(47, 71, 94, 1);
  --intellij-statusBar-top2Color: rgba(38, 50, 56, 1);
  --intellij-statusBar-topColor: rgba(38, 50, 56, 1);
  --intellij-statusBar-widget-foreground: rgba(176, 190, 197, 1);
  --intellij-statusBar-widget-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-statusBar-widget-hoverForeground: rgba(255, 255, 255, 1);
  --intellij-statusBar-widget-pressedBackground: rgba(46, 60, 67, 1);
  --intellij-statusBar-widget-pressedForeground: rgba(176, 190, 197, 1);
  --intellij-tabbedPane-background: rgba(38, 50, 56, 1);
  --intellij-tabbedPane-borderColor: rgba(38, 50, 56, 1);
  --intellij-tabbedPane-contentAreaColor: rgba(66, 91, 103, 1);
  --intellij-tabbedPane-darkShadow: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-tabbedPane-disabledText: rgba(65, 89, 103, 1);
  --intellij-tabbedPane-disabledUnderlineColor: rgba(65, 89, 103, 1);
  --intellij-tabbedPane-focus: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-focusColor: rgba(0, 150, 136, 1);
  --intellij-tabbedPane-foreground: rgba(176, 190, 197, 1);
  --intellij-tabbedPane-highlight: rgba(42, 55, 62, 1);
  --intellij-tabbedPane-hoverColor: rgba(46, 49, 51, 1);
  --intellij-tabbedPane-light: rgba(8, 74, 217, 1);
  --intellij-tabbedPane-mt-tab-background: rgba(38, 50, 56, 1);
  --intellij-tabbedPane-nonSelectedTabTitleNormalColor: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-selectHighlight: rgba(66, 91, 103, 1);
  --intellij-tabbedPane-selected: rgba(84, 110, 122, 1);
  --intellij-tabbedPane-selectedColor: rgba(0, 150, 136, 1);
  --intellij-tabbedPane-selectedDisabledColor: rgba(65, 89, 103, 1);
  --intellij-tabbedPane-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-tabbedPane-selectedTabTitleDisabledColor: rgba(255, 255, 255, 0.55);
  --intellij-tabbedPane-selectedTabTitleNonFocusColor: rgba(0, 0, 0, 1);
  --intellij-tabbedPane-selectedTabTitleNormalColor: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-selectedTabTitlePressedColor: rgba(0, 0, 0, 0.85);
  --intellij-tabbedPane-selectedTabTitleShadowDisabledColor: rgba(0, 0, 0, 0.25);
  --intellij-tabbedPane-selectedTabTitleShadowNormalColor: rgba(0, 0, 0, 0.39);
  --intellij-tabbedPane-shadow: rgba(38, 50, 56, 1);
  --intellij-tabbedPane-underlineColor: rgba(0, 150, 136, 1);
  --intellij-table-alternativeRowBackground: rgba(30, 39, 44, 1);
  --intellij-table-background: rgba(38, 50, 56, 1);
  --intellij-table-cellFocusRing: rgba(10, 96, 255, 1);
  --intellij-table-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-table-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-table-dropLineShortColor: rgba(0, 0, 0, 1);
  --intellij-table-focusCellBackground: rgba(0, 0, 0, 1);
  --intellij-table-focusCellForeground: rgba(255, 255, 255, 1);
  --intellij-table-foreground: rgba(176, 190, 197, 1);
  --intellij-table-gridColor: rgba(79, 81, 82, 1);
  --intellij-table-highlightOuter: rgba(49, 69, 73, 1);
  --intellij-table-hoverBackground: rgba(48, 69, 77, 1);
  --intellij-table-hoverInactiveBackground: rgba(49, 69, 73, 1);
  --intellij-table-lightSelectionBackground: rgba(49, 69, 73, 1);
  --intellij-table-lightSelectionForeground: rgba(255, 255, 255, 1);
  --intellij-table-lightSelectionInactiveBackground: rgba(50, 66, 74, 1);
  --intellij-table-lightSelectionInactiveForeground: rgba(96, 125, 139, 1);
  --intellij-table-selectionBackground: rgba(49, 69, 73, 1);
  --intellij-table-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-table-selectionInactiveBackground: rgba(49, 69, 73, 1);
  --intellij-table-selectionInactiveForeground: rgba(176, 190, 197, 1);
  --intellij-table-sortIconColor: rgba(0, 0, 0, 0.27);
  --intellij-table-stripeColor: rgba(30, 39, 44, 1);
  --intellij-table-stripedBackground: rgba(30, 39, 44, 1);
  --intellij-tableHeader-background: rgba(46, 60, 67, 1);
  --intellij-tableHeader-borderColor: rgba(66, 91, 103, 1);
  --intellij-tableHeader-bottomSeparatorColor: rgba(51, 54, 56, 1);
  --intellij-tableHeader-disabledForeground: rgba(65, 89, 103, 1);
  --intellij-tableHeader-focusCellBackground: rgba(255, 255, 255, 1);
  --intellij-tableHeader-focusCellForeground: rgba(255, 255, 255, 1);
  --intellij-tableHeader-foreground: rgba(176, 190, 197, 1);
  --intellij-tableHeader-separatorColor: rgba(42, 55, 62, 1);
  --intellij-tag-background: rgba(46, 60, 67, 1);
  --intellij-text: rgba(38, 50, 56, 1);
  --intellij-textArea-background: rgba(38, 50, 56, 1);
  --intellij-textArea-caretForeground: rgba(0, 150, 136, 1);
  --intellij-textArea-font-family: "Operator Mono", system-ui, sans-serif;
  --intellij-textArea-font-size: 13px;
  --intellij-textArea-foreground: rgba(176, 190, 197, 1);
  --intellij-textArea-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-textArea-inactiveForeground: rgba(176, 190, 197, 1);
  --intellij-textArea-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-textArea-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-textComponent-selectionBackgroundInactive: rgba(84, 110, 122, 1);
  --intellij-textField-background: rgba(38, 50, 56, 1);
  --intellij-textField-borderColor: rgba(38, 50, 56, 1);
  --intellij-textField-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textField-darkShadow: rgba(0, 0, 0, 1);
  --intellij-textField-disabledBackground: rgba(46, 60, 67, 1);
  --intellij-textField-focusedBorderColor: rgba(38, 50, 56, 1);
  --intellij-textField-foreground: rgba(176, 190, 197, 1);
  --intellij-textField-highlight: rgba(255, 255, 255, 1);
  --intellij-textField-hoverBorderColor: rgba(38, 50, 56, 1);
  --intellij-textField-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-textField-inactiveForeground: rgba(176, 190, 197, 1);
  --intellij-textField-light: rgba(8, 74, 217, 1);
  --intellij-textField-selectedSeparatorColor: rgba(0, 150, 136, 1);
  --intellij-textField-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-textField-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-textField-separatorColor: rgba(66, 91, 103, 1);
  --intellij-textField-separatorColorDisabled: rgba(38, 50, 56, 1);
  --intellij-textField-shadow: rgba(0, 0, 0, 0.27);
  --intellij-textHighlight: rgba(165, 205, 255, 1);
  --intellij-textHighlightText: rgba(255, 255, 255, 1);
  --intellij-textInactiveText: rgba(128, 128, 128, 1);
  --intellij-textPane-background: rgba(38, 50, 56, 1);
  --intellij-textPane-caretForeground: rgba(187, 187, 187, 1);
  --intellij-textPane-font-family: "Dialog", system-ui, sans-serif;
  --intellij-textPane-font-size: 13px;
  --intellij-textPane-foreground: rgba(176, 190, 197, 1);
  --intellij-textPane-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-textPane-inactiveForeground: rgba(65, 89, 103, 1);
  --intellij-textPane-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-textPane-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-textText: rgba(187, 187, 187, 1);
  --intellij-tipOfTheDay-image-borderColor: rgba(42, 55, 62, 1);
  --intellij-titlePane-background: rgba(38, 50, 56, 1);
  --intellij-titlePane-button-hoverBackground: rgba(84, 110, 122, 0.8);
  --intellij-titlePane-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-titlePane-inactiveInfoForeground: rgba(65, 89, 103, 1);
  --intellij-titlePane-infoForeground: rgba(96, 125, 139, 1);
  --intellij-titledBorder-titleColor: rgba(176, 190, 197, 1);
  --intellij-toggleButton-background: rgba(38, 50, 56, 1);
  --intellij-toggleButton-borderColor: rgba(46, 60, 67, 1);
  --intellij-toggleButton-buttonColor: rgba(46, 60, 67, 1);
  --intellij-toggleButton-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toggleButton-disabledText: rgba(65, 89, 103, 1);
  --intellij-toggleButton-foreground: rgba(176, 190, 197, 1);
  --intellij-toggleButton-highlight: rgba(255, 255, 255, 1);
  --intellij-toggleButton-light: rgba(8, 74, 217, 1);
  --intellij-toggleButton-off-background: rgba(176, 190, 197, 1);
  --intellij-toggleButton-off-foreground: rgba(176, 190, 197, 1);
  --intellij-toggleButton-offBackground: rgba(176, 190, 197, 1);
  --intellij-toggleButton-offForeground: rgba(176, 190, 197, 1);
  --intellij-toggleButton-on-background: rgba(0, 150, 136, 1);
  --intellij-toggleButton-on-foreground: rgba(0, 150, 136, 1);
  --intellij-toggleButton-onBackground: rgba(0, 150, 136, 1);
  --intellij-toggleButton-onForeground: rgba(0, 150, 136, 1);
  --intellij-toggleButton-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolBar-background: rgba(38, 50, 56, 1);
  --intellij-toolBar-borderHandleColor: rgba(96, 125, 139, 1);
  --intellij-toolBar-comboBoxButtonBackground: rgba(46, 60, 67, 1);
  --intellij-toolBar-darkShadow: rgba(0, 0, 0, 1);
  --intellij-toolBar-dockingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-dockingForeground: rgba(8, 74, 217, 1);
  --intellij-toolBar-floatingBackground: rgba(238, 238, 238, 1);
  --intellij-toolBar-floatingForeground: rgba(96, 125, 139, 1);
  --intellij-toolBar-foreground: rgba(176, 190, 197, 1);
  --intellij-toolBar-highlight: rgba(255, 255, 255, 1);
  --intellij-toolBar-light: rgba(8, 74, 217, 1);
  --intellij-toolBar-separatorColor: rgba(42, 55, 62, 1);
  --intellij-toolBar-shadow: rgba(0, 0, 0, 0.27);
  --intellij-toolTip-actions-background: rgba(38, 50, 56, 1);
  --intellij-toolTip-actions-grayForeground: rgba(96, 125, 139, 1);
  --intellij-toolTip-actions-infoForeground: rgba(96, 125, 139, 1);
  --intellij-toolTip-background: rgba(30, 39, 44, 1);
  --intellij-toolTip-borderColor: rgba(42, 55, 62, 1);
  --intellij-toolTip-foreground: rgba(176, 190, 197, 1);
  --intellij-toolTip-infoForeground: rgba(96, 125, 139, 1);
  --intellij-toolTip-linkForeground: rgba(0, 150, 136, 1);
  --intellij-toolTip-shortcutForeground: rgba(96, 125, 139, 1);
  --intellij-toolWindow-active-header-background: rgba(50, 66, 74, 1);
  --intellij-toolWindow-active-headerTab-background: rgba(30, 39, 44, 1);
  --intellij-toolWindow-background: rgba(38, 50, 56, 1);
  --intellij-toolWindow-button-dragAndDrop-buttonDropBackground: rgba(84, 110, 122, 0.6);
  --intellij-toolWindow-button-dragAndDrop-buttonDropBorderColor: rgba(0, 150, 136, 1);
  --intellij-toolWindow-button-dragAndDrop-buttonFloatingBackground: rgba(46, 60, 67, 1);
  --intellij-toolWindow-button-dragAndDrop-stripeBackground: rgba(38, 50, 56, 1);
  --intellij-toolWindow-button-hoverBackground: rgba(49, 69, 73, 1);
  --intellij-toolWindow-button-selectedBackground: rgba(53, 115, 240, 1);
  --intellij-toolWindow-button-selectedForeground: rgba(255, 255, 255, 1);
  --intellij-toolWindow-dragAndDrop-areaBackground: rgba(0, 150, 136, 1);
  --intellij-toolWindow-header-active-background: rgba(50, 66, 74, 1);
  --intellij-toolWindow-header-background: rgba(38, 50, 56, 1);
  --intellij-toolWindow-header-border-background: rgba(42, 55, 62, 1);
  --intellij-toolWindow-header-borderColor: rgba(42, 55, 62, 1);
  --intellij-toolWindow-header-closeButton-background: rgba(38, 50, 56, 1);
  --intellij-toolWindow-header-inactiveBackground: rgba(38, 50, 56, 1);
  --intellij-toolWindow-header-tab-selected-active-background: rgba(30, 39, 44, 1);
  --intellij-toolWindow-header-tab-selected-background: rgba(30, 39, 44, 1);
  --intellij-toolWindow-headerCloseButton-background: rgba(38, 50, 56, 1);
  --intellij-toolWindow-headerTab-borderColor: rgba(38, 50, 56, 1);
  --intellij-toolWindow-headerTab-hoverBackground: rgba(66, 91, 103, 1);
  --intellij-toolWindow-headerTab-hoverInactiveBackground: rgba(66, 91, 103, 1);
  --intellij-toolWindow-headerTab-inactiveUnderlineColor: rgba(0, 150, 136, 1);
  --intellij-toolWindow-headerTab-selectedBackground: rgba(49, 59, 69, 1);
  --intellij-toolWindow-headerTab-selectedInactiveBackground: rgba(52, 54, 56, 1);
  --intellij-toolWindow-headerTab-underlineColor: rgba(0, 150, 136, 1);
  --intellij-toolWindow-headerTab-underlinedTabBackground: rgba(49, 69, 73, 1);
  --intellij-toolWindow-headerTab-underlinedTabForeground: rgba(255, 255, 255, 1);
  --intellij-toolWindow-headerTab-underlinedTabInactiveBackground: rgba(38, 50, 56, 1);
  --intellij-toolWindow-headerTab-underlinedTabInactiveForeground: rgba(176, 190, 197, 1);
  --intellij-toolWindow-inactive-header-background: rgba(38, 50, 56, 1);
  --intellij-toolWindow-inactive-headerTab-background: rgba(30, 39, 44, 1);
  --intellij-toolWindow-stripe-dragAndDrop-separatorColor: rgba(0, 150, 136, 1);
  --intellij-toolWindow-stripe-separatorColor: rgba(49, 69, 73, 1);
  --intellij-toolbar-floating-background: rgba(38, 50, 56, 1);
  --intellij-toolbar-floating-borderColor: rgba(42, 55, 62, 1);
  --intellij-toolbarComboWidget-background: rgba(46, 60, 67, 1);
  --intellij-toolbarComboWidget-hoverBackground: rgba(42, 55, 62, 1);
  --intellij-tooltip-actions-background: rgba(38, 50, 56, 1);
  --intellij-tooltip-background: rgba(38, 50, 56, 1);
  --intellij-tooltip-foreground: rgba(176, 190, 197, 1);
  --intellij-tooltip-learning-background: rgba(0, 150, 136, 1);
  --intellij-tooltip-learning-borderColor: rgba(0, 150, 136, 1);
  --intellij-tooltip-learning-codeBorderColor: rgba(30, 39, 44, 1);
  --intellij-tooltip-learning-codeForeground: rgba(176, 190, 197, 1);
  --intellij-tooltip-learning-foreground: rgba(176, 190, 197, 1);
  --intellij-tooltip-learning-header-foreground: rgba(176, 190, 197, 1);
  --intellij-tooltip-learning-linkForeground: rgba(0, 150, 136, 1);
  --intellij-tooltip-learning-linkUnderlineHoveredColor: rgba(0, 150, 136, 1);
  --intellij-tooltip-learning-secondaryActionForeground: rgba(106, 157, 222, 1);
  --intellij-tooltip-learning-spanBackground: rgba(0, 150, 136, 0.27);
  --intellij-tooltip-learning-spanForeground: rgba(176, 190, 197, 1);
  --intellij-tooltip-learning-stepNumberForeground: rgba(0, 150, 136, 1);
  --intellij-tooltip-separatorColor: rgba(42, 55, 62, 1);
  --intellij-tooltips-actions-keymap-text-color: rgba(96, 125, 139, 1);
  --intellij-tooltips-actions-settings-icon-background-color: rgba(38, 50, 56, 1);
  --intellij-tooltips-description-title-text-color: rgba(176, 190, 197, 1);
  --intellij-tree-background: rgba(38, 50, 56, 1);
  --intellij-tree-dropLineColor: rgba(0, 0, 0, 0.27);
  --intellij-tree-foreground: rgba(176, 190, 197, 1);
  --intellij-tree-hash: rgba(42, 55, 62, 1);
  --intellij-tree-hoverBackground: rgba(48, 69, 77, 1);
  --intellij-tree-hoverInactiveBackground: rgba(49, 69, 73, 1);
  --intellij-tree-iconColor: rgba(96, 125, 139, 1);
  --intellij-tree-iconColor-dark: rgba(96, 125, 139, 1);
  --intellij-tree-line: rgba(255, 255, 255, 1);
  --intellij-tree-modifiedItemForeground: rgba(0, 150, 136, 1);
  --intellij-tree-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-tree-selectionBorderColor: rgba(38, 50, 56, 1);
  --intellij-tree-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-tree-selectionInactiveBackground: rgba(48, 69, 77, 0.5);
  --intellij-tree-selectionInactiveForeground: rgba(255, 255, 255, 1);
  --intellij-tree-textBackground: rgba(38, 50, 56, 1);
  --intellij-tree-textForeground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-activity-borderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-canvas-background: rgba(30, 39, 44, 1);
  --intellij-uiDesigner-colorPicker-background: rgba(50, 66, 74, 1);
  --intellij-uiDesigner-colorPicker-foreground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-component-background: rgba(38, 50, 56, 1);
  --intellij-uiDesigner-component-borderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-component-foreground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-component-hoverBorderColor: rgba(66, 91, 103, 1);
  --intellij-uiDesigner-connector-borderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-connector-hoverBorderColor: rgba(66, 91, 103, 1);
  --intellij-uiDesigner-highStroke-foreground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-label-foreground: rgba(96, 125, 139, 1);
  --intellij-uiDesigner-list-selectionBackground: rgba(48, 69, 77, 1);
  --intellij-uiDesigner-motion-addConstraintColor: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-addConstraintPlus: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-axis-lineSeparatorColor: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-motion-borderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-motion-cSPanel-selectedBackground: rgba(48, 69, 77, 1);
  --intellij-uiDesigner-motion-cSPanel-selectedFocusBackground: rgba(84, 110, 122, 1);
  --intellij-uiDesigner-motion-component-foreground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-motion-constraintSet-background: rgba(50, 66, 74, 1);
  --intellij-uiDesigner-motion-constraintSetText-foreground: rgba(96, 125, 139, 1);
  --intellij-uiDesigner-motion-cs_FocusText-infoForeground: rgba(96, 125, 139, 1);
  --intellij-uiDesigner-motion-cursorTextColor-foreground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-motion-graphLine-lineSeparatorColor: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-grid-lineSeparatorColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-motion-hoverBorderColor: rgba(66, 91, 103, 1);
  --intellij-uiDesigner-motion-hoverColor-disabledBackground: rgba(65, 89, 103, 1);
  --intellij-uiDesigner-motion-key-selectedForeground: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-label-background: rgba(50, 66, 74, 1);
  --intellij-uiDesigner-motion-label-textColor: rgba(96, 125, 139, 1);
  --intellij-uiDesigner-motion-light-borderColor: rgba(66, 91, 103, 1);
  --intellij-uiDesigner-motion-motionGraph-background: rgba(38, 50, 56, 1);
  --intellij-uiDesigner-motion-notification-background: rgba(30, 39, 44, 1);
  --intellij-uiDesigner-motion-ourAvg-background: rgba(50, 66, 74, 1);
  --intellij-uiDesigner-motion-ourCS-background: rgba(50, 66, 74, 1);
  --intellij-uiDesigner-motion-ourCS_Border-borderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-motion-ourCS_SelectedBackground-selectionInactiveBackground: rgba(
    49,
    69,
    73,
    1
  );
  --intellij-uiDesigner-motion-ourCS_SelectedBorder-pressedBorderColor: rgba(66, 91, 103, 1);
  --intellij-uiDesigner-motion-ourCS_SelectedFocusBackground-selectionForeground: rgba(
    255,
    255,
    255,
    1
  );
  --intellij-uiDesigner-motion-ourCS_SelectedFocusBorder-focusedBorderColor: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-ourCS_TextColor-foreground: rgba(96, 125, 139, 1);
  --intellij-uiDesigner-motion-ourML_BarColor-separatorColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-motion-positionMarkColor: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-primaryPanel-background: rgba(30, 39, 44, 1);
  --intellij-uiDesigner-motion-secondaryPanel-background: rgba(38, 50, 56, 1);
  --intellij-uiDesigner-motion-secondaryPanel-header-background: rgba(30, 39, 44, 1);
  --intellij-uiDesigner-motion-secondaryPanel-header-foreground: rgba(96, 125, 139, 1);
  --intellij-uiDesigner-motion-timeCursor-end-selectedForeground: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-timeCursor-selectedForeground: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-timeCursor-start-selectedForeground: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-motion-timeLine-disabledBorderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-panel-background: rgba(38, 50, 56, 1);
  --intellij-uiDesigner-panel-borderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-panel-graphLabel: rgba(96, 125, 139, 1);
  --intellij-uiDesigner-panel-graphLines: rgba(66, 91, 103, 1);
  --intellij-uiDesigner-panel-lines3d: rgba(0, 150, 136, 1);
  --intellij-uiDesigner-panel-secondaryGraphLines: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-percent-foreground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-placeholder-background: rgba(38, 50, 56, 1);
  --intellij-uiDesigner-placeholder-borderColor: rgba(42, 55, 62, 1);
  --intellij-uiDesigner-placeholder-foreground: rgba(176, 190, 197, 1);
  --intellij-uiDesigner-placeholder-selectedForeground: rgba(156, 205, 255, 1);
  --intellij-uiDesigner-preview-background: rgba(38, 50, 56, 1);
  --intellij-uiDesigner-stroke-acceleratorForeground: rgba(96, 125, 139, 1);
  --intellij-validationTooltip-errorBackground: rgba(30, 39, 44, 1);
  --intellij-validationTooltip-errorBackgroundColor: rgba(30, 39, 44, 1);
  --intellij-validationTooltip-errorBorderColor: rgba(30, 39, 44, 1);
  --intellij-validationTooltip-warningBackground: rgba(89, 78, 50, 1);
  --intellij-validationTooltip-warningBackgroundColor: rgba(30, 39, 44, 1);
  --intellij-validationTooltip-warningBorderColor: rgba(30, 39, 44, 1);
  --intellij-versionControl-fileHistory-commit-otherBranchBackground: rgba(38, 50, 56, 1);
  --intellij-versionControl-fileHistory-commit-selectedBranchBackground: rgba(73, 73, 63, 1);
  --intellij-versionControl-gitCommits-graphColor: rgba(66, 91, 103, 1);
  --intellij-versionControl-gitLog-localBranchIconColor: rgba(0, 150, 136, 1);
  --intellij-versionControl-gitLog-otherIconColor: rgba(96, 125, 139, 1);
  --intellij-versionControl-gitLog-remoteBranchIconColor: rgba(176, 190, 197, 1);
  --intellij-versionControl-gitLog-tagIconColor: rgba(96, 125, 139, 1);
  --intellij-versionControl-hgLog-bookmarkIconColor: rgba(159, 121, 181, 1);
  --intellij-versionControl-hgLog-branchIconColor: rgba(0, 150, 136, 1);
  --intellij-versionControl-hgLog-closedBranchIconColor: rgba(255, 95, 111, 1);
  --intellij-versionControl-hgLog-headIconColor: rgba(195, 30, 140, 1);
  --intellij-versionControl-hgLog-localTagIconColor: rgba(96, 125, 139, 1);
  --intellij-versionControl-hgLog-mqTagIconColor: rgba(96, 125, 139, 1);
  --intellij-versionControl-hgLog-tagIconColor: rgba(96, 125, 139, 1);
  --intellij-versionControl-hgLog-tipIconColor: rgba(225, 199, 49, 1);
  --intellij-versionControl-log-commit-currentBranchBackground: rgba(63, 71, 73, 1);
  --intellij-versionControl-log-commit-hoveredBackground: rgba(48, 69, 77, 1);
  --intellij-versionControl-log-commit-reference-foreground: rgba(176, 190, 197, 1);
  --intellij-versionControl-log-commit-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-versionControl-log-commit-selectionForeground: rgba(255, 255, 255, 1);
  --intellij-versionControl-log-commit-selectionInactiveBackground: rgba(84, 110, 122, 0.8);
  --intellij-versionControl-log-commit-selectionInactiveForeground: rgba(176, 190, 197, 1);
  --intellij-versionControl-log-commit-unmatchedForeground: rgba(96, 125, 139, 1);
  --intellij-versionControl-markerPopup-borderColor: rgba(42, 55, 62, 1);
  --intellij-versionControl-markerPopup-toolbar-background: rgba(38, 50, 56, 1);
  --intellij-versionControl-ref-backgroundBase: rgba(66, 91, 103, 1);
  --intellij-versionControl-ref-foreground: rgba(255, 255, 255, 1);
  --intellij-versionControl-refLabel-backgroundBase: rgba(66, 91, 103, 1);
  --intellij-versionControl-refLabel-foreground: rgba(176, 190, 197, 1);
  --intellij-viewport-background: rgba(38, 50, 56, 1);
  --intellij-viewport-foreground: rgba(176, 190, 197, 1);
  --intellij-welcomeScreen-associatedComponent-background: rgba(38, 50, 56, 1);
  --intellij-welcomeScreen-background: rgba(38, 50, 56, 1);
  --intellij-welcomeScreen-borderColor: rgba(38, 50, 56, 1);
  --intellij-welcomeScreen-captionBackground: rgba(30, 39, 44, 1);
  --intellij-welcomeScreen-captionForeground: rgba(176, 190, 197, 1);
  --intellij-welcomeScreen-details-background: rgba(38, 50, 56, 1);
  --intellij-welcomeScreen-footerBackground: rgba(30, 39, 44, 1);
  --intellij-welcomeScreen-footerForeground: rgba(176, 190, 197, 1);
  --intellij-welcomeScreen-groupIconBorderColor: rgba(46, 60, 67, 1);
  --intellij-welcomeScreen-headerBackground: rgba(38, 50, 56, 1);
  --intellij-welcomeScreen-headerForeground: rgba(176, 190, 197, 1);
  --intellij-welcomeScreen-list-background: rgba(30, 39, 44, 1);
  --intellij-welcomeScreen-projects-actions-background: rgba(38, 50, 56, 1);
  --intellij-welcomeScreen-projects-actions-selectionBackground: rgba(84, 110, 122, 1);
  --intellij-welcomeScreen-projects-actions-selectionBorderColor: rgba(0, 150, 136, 1);
  --intellij-welcomeScreen-projects-background: rgba(50, 66, 74, 1);
  --intellij-welcomeScreen-separatorColor: rgba(42, 55, 62, 1);
  --intellij-welcomeScreen-sidePanel-background: rgba(38, 50, 56, 1);
  --intellij-window: rgba(60, 63, 65, 1);
  --intellij-windowBorder: rgba(154, 154, 154, 1);
  --intellij-windowText: rgba(0, 0, 0, 0.85);
}
::-webkit-scrollbar {
  width: 11px;
  height: 11px;
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-track:hover {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 150, 136, 0.752941);
  border-radius: 5px;
  border-width: 2px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0);
  background-clip: padding-box;
  outline: 1px solid rgba(38, 38, 38, 0.34902);
  outline-offset: -2px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 150, 136, 1);
  border-radius: 5px;
  border-width: 2px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0);
  background-clip: padding-box;
  outline: 1px solid rgba(38, 38, 38, 0.54902);
  outline-offset: -2px;
}
::-webkit-scrollbar-corner {
  background-color: rgba(0, 0, 0, 0);
}
::-webkit-scrollbar-button {
  display: none;
}
:root {
  background: var(--intellij-tree-background);
  color: var(--intellij-tree-foreground);
}
a {
  color: var(--intellij-hyperlink-linkColor);
}

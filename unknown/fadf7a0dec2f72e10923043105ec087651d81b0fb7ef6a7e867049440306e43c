# YAML 1.2
---
authors:
  - affiliation: EleutherAI
    family-names: Andonian
    given-names: Alex
  - affiliation: EleutherAI
    family-names: Biderman
    given-names: Stella
  - affiliation: EleutherAI
    family-names: Black
    given-names: Sid
  - affiliation: EleutherAI
    family-names: Gali
    given-names: <PERSON>eth<PERSON>
  - affiliation: EleutherAI
    family-names: <PERSON>
    given-names: Leo
  - affiliation: EleutherAI
    family-names: <PERSON><PERSON>
    given-names: <PERSON>
  - affiliation: EleutherAI
    family-names: <PERSON><PERSON>
    given-names: Josh
  - affiliation: EleutherAI
    family-names: <PERSON><PERSON>
    given-names: <PERSON>
  - affiliation: EleutherAI
    family-names: Nestler
    given-names: Lucas
  - affiliation: EleutherAI
    family-names: <PERSON>
    given-names: <PERSON><PERSON>
  - affiliation: EleutherAI
    family-names: <PERSON>ler
    given-names: Michael
  - affiliation: EleutherAI
    family-names: <PERSON><PERSON>hit
    given-names: <PERSON><PERSON>hu
  - affiliation: EleutherAI
    family-names: <PERSON>z
    given-names: Tri
  - affiliation: EleutherAI
    family-names: Phil
    given-names: Wang
  - affiliation: EleutherAI
    family-names: <PERSON><PERSON>bach
    given-names: <PERSON>
cff-version: "1.1.0"
keywords:
  - "Transformers"
  - "Massive language model"
  - "Autoregressive language model"
license: "Apache-2.0"
message: "If you use this software, please cite it using these metadata."
repository-code: "https://www.github.com/eleutherai/gpt-neox"
title: "GPT-NeoX: Large Scale Autoregressive Language Modeling in PyTorch"
version: "0.0.1"
doi: "10.5281/zenodo.5879544"
date-released: 2021-08-23
...

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "expiration_watcher",
    srcs = [
        "expiration_watcher.py",
    ],
    deps = [
        requirement("python-dateutil"),
        requirement("kubernetes"),
        requirement("prometheus-client"),
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
    ],
)

pytest_test(
    name = "expiration_watcher_test",
    srcs = ["expiration_watcher_test.py"],
    deps = [
        ":expiration_watcher",
    ],
)

py_oci_image(
    name = "expiration_watcher_image",
    package_name = package_name(),
    binary = ":expiration_watcher",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":expiration_watcher_image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
    ],
)

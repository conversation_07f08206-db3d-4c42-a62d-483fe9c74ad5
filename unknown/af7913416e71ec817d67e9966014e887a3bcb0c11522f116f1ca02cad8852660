"""Unit tests for the diff_retriever module."""

from unittest import mock

import pytest

from base.diff_utils.diff_utils import DiffHunk, File, LineRange
from base.diff_utils.edit_events import GranularEditEvent, SingleEdit
from base.prompt_format.chunk_origin import Chunk<PERSON><PERSON><PERSON>
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from services.completion_host import completion_pb2
from services.lib.file_retriever.file_retriever import FileRetriever
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.diff_retriever import (
    DiffRetriever,
    diff_hunk_to_retrieval_chunk,
)
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)


@pytest.fixture
def mock_file_retriever():
    return mock.MagicMock(spec=FileRetriever)


@pytest.fixture
def mock_internal_retriever():
    return mock.MagicMock(spec=Retriever)


@pytest.fixture
def diff_retriever(mock_file_retriever, mock_internal_retriever):
    return DiffRetriever(
        num_results=5,
        max_total_changed_chars=1000,
        big_event_lines=8,
        diff_context_lines=3,
        use_smart_header=True,
        filter_duplicated_file_paths=True,
        internal_retriever=mock_internal_retriever,
        file_retriever=mock_file_retriever,
        ri_builder=None,
    )


def test_diff_hunk_to_retrieval_chunk():
    diff_hunk = DiffHunk(
        text="def test_function():\n    pass\n",
        before_path="test/file.py",
        after_path="test/file.py",
        before_crange=LineRange(start=0, stop=0),
        after_crange=LineRange(start=0, stop=0),
        before_lrange=LineRange(start=2, stop=4),
        after_lrange=LineRange(start=2, stop=4),
    )

    result = diff_hunk_to_retrieval_chunk(diff_hunk)

    assert isinstance(result, RetrievalChunk)
    assert result.text == "def test_function():\n    pass\n"
    assert result.char_start == 0
    assert result.char_end == len(diff_hunk.text)
    assert result.blob_name is None
    assert result.chunk_index is None
    assert result.origin == ChunkOrigin.DIFF_RETRIEVER.value
    assert result.path == "+++ test/file.py\n"


def test_diff_retriever_with_empty_recency_and_edits(diff_retriever):
    """Test retrieval when there are empty recency and edit events."""
    request_context = RequestContext.create()
    auth_info = mock.MagicMock(tenant_id=None, tenant_name="dev-augie")

    recency_info = completion_pb2.RecencyInfo(
        recent_changes=[],
    )

    input_ = RetrievalInput(
        prompt_input=CompletionRetrieverPromptInput(
            prefix="def test():",
            suffix="",
            path="test/file.py",
        ),
        recency_info=recency_info,
        edit_events=[],
    )

    result = diff_retriever.retrieve(input_, request_context, auth_info)
    assert isinstance(result, RetrievalResult)
    assert len(list(result.get_retrieved_chunks())) == 0
    assert len(list(result.get_missing_blob_names())) == 0


def test_diff_retriever_with_none_recency_and_edits(diff_retriever):
    """Test retrieval when recency and edit events is None."""
    request_context = RequestContext.create()
    auth_info = mock.MagicMock(tenant_id=None, tenant_name="dev-augie")

    input_ = RetrievalInput(
        prompt_input=CompletionRetrieverPromptInput(
            prefix="def test():",
            suffix="",
            path="test/file.py",
        ),
        recency_info=None,
        edit_events=None,
    )

    result = diff_retriever.retrieve(input_, request_context, auth_info)
    assert len(list(result.get_retrieved_chunks())) == 0
    assert len(list(result.get_missing_blob_names())) == 0


def test_diff_retriever_max_edit_events(diff_retriever, mock_file_retriever):
    """Test retrieval with more edit events than the max allowed.

    The retriever edit events input is ordered from oldest to newest, and the output should be the most recent 5 edit events.
    Therefore, input edit events [1 2 3 ... 7 8] should return content of [8 7 6 5 4]
    """

    request_context = RequestContext.create()
    auth_info = mock.MagicMock(tenant_id=None, tenant_name="dev-augie")

    recency_info = completion_pb2.RecencyInfo(
        recent_changes=[],
    )

    # Mock file retriever to return 8 files.
    mock_files = []
    updated_mock_files = []
    for i in range(8):
        mock_file = File(path=f"test/file{i}.py", contents=f"original content{i}")
        mock_files.append(mock_file)
        updated_mock_file = File(path=f"test/file{i}.py", contents=f"new content{i}")
        updated_mock_files.append(updated_mock_file)

    mock_file_retriever.retrieve_indexed_files.return_value = {
        updated_mock_file.blob_name: updated_mock_file
        for updated_mock_file in updated_mock_files
    }

    # Note that edit events are ordered from oldest to newest, so edit_events[-1] is the most recent edit event.
    edit_events = []
    for i in range(8):
        edit_event = GranularEditEvent(
            path=mock_files[i].path,
            before_blob_name=mock_files[i].blob_name,
            after_blob_name=updated_mock_files[i].blob_name,
            edits=[
                SingleEdit(
                    before_start=0,
                    after_start=0,
                    before_text=mock_files[i].contents,
                    after_text=updated_mock_files[i].contents,
                )
            ],
        )
        edit_events.append(edit_event)

    # Get the last 5 edit events in reverse order.
    expected_retrieved_chunk_paths = [f"+++ test/file{i}.py\n" for i in range(3, 8)]
    expected_retrieved_chunk_paths.reverse()

    input_ = RetrievalInput(
        prompt_input=CompletionRetrieverPromptInput(
            prefix="def test():",
            suffix="",
            path="test/file.py",
        ),
        recency_info=recency_info,
        edit_events=edit_events,
    )
    result = diff_retriever.retrieve(input_, request_context, auth_info)
    retrieved_chunks = list(result.get_retrieved_chunks())
    retrieved_chunk_paths = [chunk.path for chunk in retrieved_chunks]
    assert (
        retrieved_chunk_paths == expected_retrieved_chunk_paths
    ), f"Got {retrieved_chunk_paths}. Expected {expected_retrieved_chunk_paths}"
    assert len(retrieved_chunks) == 5


def test_diff_retriever_with_replacement_text(diff_retriever, mock_file_retriever):
    """Test retrieval when using replacement text to reconstruct the file."""
    request_context = RequestContext.create()
    auth_info = mock.MagicMock(tenant_id=None, tenant_name="dev-augie")

    # Mock file retriever to return a file
    mock_file = File(path="test/file.py", contents="original content")
    mock_file_retriever.retrieve_indexed_files.return_value = {
        mock_file.blob_name: mock_file
    }

    updated_mock_file = File(
        path="test/file.py",
        contents="def new_function():\n    return True\n    #content",
    )

    # Create a recency info with replacement text
    replacement = completion_pb2.ReplacementText()
    replacement.path = "test/file.py"
    replacement.blob_name = mock_file.blob_name
    replacement.expected_blob_name = updated_mock_file.blob_name
    replacement.replacement_text = "def new_function():\n    return True\n    #"
    replacement.char_start = 0
    replacement.char_end = 9

    recency_info = completion_pb2.RecencyInfo(
        recent_changes=[replacement],
    )

    previous_mock_file = File(path="test/file.py", contents="def ():\n")

    edit_event = GranularEditEvent(
        path="test/file.py",
        before_blob_name=previous_mock_file.blob_name,
        after_blob_name=updated_mock_file.blob_name,
        edits=[
            SingleEdit(
                before_start=0,
                after_start=0,
                before_text=previous_mock_file.contents,
                after_text=updated_mock_file.contents,
            )
        ],
    )

    input_ = RetrievalInput(
        prompt_input=CompletionRetrieverPromptInput(
            prefix="def test():",
            suffix="",
            path="test/file.py",
        ),
        recency_info=recency_info,
        edit_events=[edit_event],
    )

    result = diff_retriever.retrieve(input_, request_context, auth_info)
    chunks = list(result.get_retrieved_chunks())
    assert len(chunks) == 1
    assert (
        chunks[0].text
        == "@@ -1,1 +1,3 @@\n-def ():\n+def new_function():\n+    return True\n+    #content\n\\ No newline at end of file\n"
    )
    assert all(chunk.origin == ChunkOrigin.DIFF_RETRIEVER.value for chunk in chunks)

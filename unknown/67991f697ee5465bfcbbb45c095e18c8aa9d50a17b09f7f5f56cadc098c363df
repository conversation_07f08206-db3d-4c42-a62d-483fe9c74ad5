-- time points to compute the values on
WITH timepoint AS (
  SELECT value
  FROM UNNEST(GENERATE_TIMESTAMP_ARRAY(
    PARSE_TIMESTAMP('%Y%m%d', @DS_START_DATE),
    PARSE_TIMESTAMP('%Y%m%d', @DS_END_DATE),
    INTERVAL 1 DAY
  )) as value
  ORDER BY value
),
-- reveart and lemonade installed before dataset,
-- and ddn uses github enterprise and does not count
adjusted_github_installation AS (
  SELECT
        tenant,
        time AS event_time,
        JSON_VALUE(sanitized_json, '$.event_type') AS event_type
  FROM system-services-prod.us_prod_request_insight_analytics_dataset.github_app_installation_event
  WHERE
      tenant not in ('ddn', 'augmentdemo')
      AND JSON_VALUE(sanitized_json, '$.event_type') IN ('INSTALL', 'UNINSTALL')
      AND JSON_VALUE(sanitized_json, '$.status.code') IS NULL
  UNION ALL
  SELECT 'lemonade', '2024-11-11', 'INSTALL'
  UNION ALL
  SELECT 'reveart', '2024-11-11', 'INSTALL'
),
ranked_github_installation AS (
  SELECT
    *,
    timepoint.value AS cutoff,
    ROW_NUMBER() OVER (PARTITION BY tenant, timepoint.value ORDER BY event_time desc) AS recency
  FROM adjusted_github_installation
  JOIN timepoint ON adjusted_github_installation.event_time < timepoint.value
),
active_github_tenant AS (
  SELECT
     tenant,
     cutoff
  FROM ranked_github_installation
  WHERE recency=1 AND event_type='INSTALL'
),
ranked_slack_installation AS (
  SELECT
    tenant,
    timepoint.value AS cutoff,
    time AS event_time,
    JSON_VALUE(sanitized_json, '$.event_type') AS event_type,
    ROW_NUMBER() OVER (PARTITION BY tenant, timepoint.value ORDER BY time desc) AS recency
  FROM system-services-prod.us_prod_request_insight_analytics_dataset.slackbot_installation_event
  JOIN timepoint ON time < timepoint.value
  WHERE JSON_VALUE(sanitized_json, '$.event_type') IN ('INSTALL', 'UNINSTALL')
        AND JSON_VALUE(sanitized_json, '$.status.code') IS NULL
)
SELECT
    slack.cutoff AS cutoff_date,
    COUNT(*) AS active_tenant_count
FROM ranked_slack_installation AS slack
JOIN active_github_tenant AS github ON github.tenant=slack.tenant and github.cutoff = slack.cutoff
WHERE recency=1 AND event_type='INSTALL'
GROUP BY 1
ORDER BY 1

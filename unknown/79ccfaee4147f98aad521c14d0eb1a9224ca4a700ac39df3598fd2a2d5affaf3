package com.augmentcode.intellij.settings

import com.intellij.openapi.components.BaseState

class AugmentIntegrationsConfigState : BaseState() {
  // Atlassian
  var atlassianServerUrl by string()
  var atlassianPersonalApiToken by string()
  var atlassianUsername by string()

  // Notion
  var notionApiToken by string()

  // Linear
  var linearApiToken by string()

  // GitHub
  var githubApiToken by string()

  var mcpServersJson by string()

  var userGuidelines by string()
}

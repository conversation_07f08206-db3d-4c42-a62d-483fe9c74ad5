WITH all_guideline_events AS (
    SELECT
        session_id,
        tenant_id,
        STRING(JSON_QUERY(sanitized_json, '$.client_metric')) AS metric_name,
        MAX(time) AS max_time
    FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.client_metric`
    WHERE
  		STRING(JSON_QUERY(sanitized_json, '$.client_metric')) IN ("webview__chat__chat-set-user-guidelines", "webview__chat__chat-clear-user-guidelines", "webview__chat__chat-set-workspace-guidelines", "webview__chat__chat-clear-workspace-guidelines")
  		AND DATE(time, @TIMEZONE) BETWEEN
  			PARSE_DATE('%Y%m%d', @DS_START_DATE) AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
    GROUP BY tenant_id, session_id, metric_name
),

events AS (
    SELECT
        session_id,
        MAX(CASE WHEN metric_name = "webview__chat__chat-set-user-guidelines" THEN max_time END) AS set_user_guidelines_time,
        MAX(CASE WHEN metric_name = "webview__chat__chat-clear-user-guidelines" THEN max_time END) AS clear_user_guidelines_time,
        MAX(CASE WHEN metric_name = "webview__chat__chat-set-workspace-guidelines" THEN max_time END) AS set_workspace_guidelines_time,
        MAX(CASE WHEN metric_name = "webview__chat__chat-clear-workspace-guidelines" THEN max_time END) AS clear_workspace_guidelines_time
    FROM
        all_guideline_events
    GROUP BY
        session_id
),

final_events AS (
    SELECT
        session_id,
        CASE
            WHEN set_user_guidelines_time IS NOT NULL AND
                 (clear_user_guidelines_time IS NULL OR set_user_guidelines_time > clear_user_guidelines_time)
            THEN 1
            ELSE 0
        END AS set_user_guidelines,
        CASE
            WHEN clear_user_guidelines_time IS NOT NULL AND
                 (set_user_guidelines_time IS NULL OR clear_user_guidelines_time > set_user_guidelines_time)
            THEN 1
            ELSE 0
        END AS clear_user_guidelines,
        CASE
            WHEN set_workspace_guidelines_time IS NOT NULL AND
                 (clear_workspace_guidelines_time IS NULL OR set_workspace_guidelines_time > clear_workspace_guidelines_time)
            THEN 1
            ELSE 0
        END AS set_workspace_guidelines,
        CASE
            WHEN clear_workspace_guidelines_time IS NOT NULL AND
                 (set_workspace_guidelines_time IS NULL OR clear_workspace_guidelines_time > set_workspace_guidelines_time)
            THEN 1
            ELSE 0
        END AS clear_workspace_guidelines
    FROM
        events
)

SELECT
    'User Guidelines In Use' AS guidelines_action,
    COUNT(session_id) AS user_count
FROM
    final_events
WHERE
    set_user_guidelines = 1

UNION ALL

SELECT
    'User Guidelines Deleted & Not Restored' AS guidelines_action,
    COUNT(session_id) AS user_count
FROM
    final_events
WHERE
    clear_user_guidelines = 1

UNION ALL

SELECT
    'Workspace Guidelines In Use' AS guidelines_action,
    COUNT(session_id) AS user_count
FROM
    final_events
WHERE
    set_workspace_guidelines = 1

UNION ALL

SELECT
    'Workspace Guidelines Deleted & Not Restored' AS guidelines_action,
    COUNT(session_id) AS user_count
FROM
    final_events
WHERE
    clear_workspace_guidelines = 1

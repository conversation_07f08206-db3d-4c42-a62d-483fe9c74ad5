"""Tests for process.py."""

import io
import os
import sys
import time

import pytest

import base.test_utils.process as process


def test_echo():
    """Test echo."""

    str = "foo\nbar\nbaz\n"
    infile = io.StringIO(str)
    outfile = io.StringIO()
    process.echo(infile, outfile)
    assert outfile.getvalue() == str


def test_echo_flush():
    """Test that echo flushes the output stream."""

    # I actually tested this test by deleting the flush() call
    # in echo and it failed.
    str = "foo\nbar\nbaz\n"
    infile = io.StringIO(str)

    # It's easier to wrap a pipe in a buffered stream
    # than a StringIO
    (readfd, writefd) = os.pipe()
    outfile = os.fdopen(writefd, "w")

    process.echo(infile, outfile)
    assert str[:1] == os.fdopen(readfd, "r").read(1)


def test_wait_for_line():
    """Test wait_for_line."""

    # Flaky test because we don't mock time

    def subcase(str, regexp, timeout_secs, close=False):
        (readfd, writefd) = os.pipe()
        with os.fdopen(readfd, "r") as infile:
            with os.fdopen(writefd, "w") as outfile:
                echo = io.StringIO()

                outfile.write(str)
                outfile.flush()
                if close:
                    outfile.close()
                m = process.wait_for_line(infile, regexp, echo, timeout_secs)

        return (m, echo.getvalue())

    (m, echo) = subcase("foo\nbar\nbaz\n", r"bar", 1000.0)
    assert m.group(0) == "bar"
    assert echo == "foo\nbar\n"

    with pytest.raises(TimeoutError):
        subcase("foo\nbar\nbaz\n", r"frob", 0.2)

    with pytest.raises(RuntimeError):
        subcase("foo\nbar\nbaz\n", r"frob", 0.2, close=True)


def test_server_manager():
    """Test server minder."""

    # Flaky test because we don't mock time
    s = io.StringIO()
    with process.ServerManager(
        [
            sys.executable,
            "-c",
            'import time; print("Started", flush=True); time.sleep(20)',
        ]
    ) as p:
        p.detach_stdout(s)
        time.sleep(1)

    assert s.getvalue().startswith("Started")

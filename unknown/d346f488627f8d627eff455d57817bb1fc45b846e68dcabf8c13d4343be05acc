package com.augmentcode.intellij.metrics

import com.augmentcode.api.ClientMetric
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Service(Service.Level.PROJECT)
class ClientMetricsReporter(
  private val project: Project,
  private val cs: CoroutineScope,
) : MetricsReporter<ClientMetric>(
    maxRecords = DEFAULT_MAX_RECORDS,
    uploadBatchSize = DEFAULT_BATCH_SIZE,
    uploadIntervalMs = DEFAULT_UPLOAD_MSEC,
  ) {
  companion object {
    const val DEFAULT_MAX_RECORDS = 10000
    const val DEFAULT_BATCH_SIZE = 500
    val DEFAULT_UPLOAD_MSEC = if (ApplicationManager.getApplication().isUnitTestMode) 10L else 10_000L

    fun getInstance(project: Project): ClientMetricsReporter = project.getService(ClientMetricsReporter::class.java)
  }

  suspend fun reportWebviewClientMetric(
    webviewName: String,
    clientMetric: String,
    value: Long,
  ) {
    report(
      ClientMetric().apply {
        this.clientMetric = "webview__${webviewName}__$clientMetric"
        this.value = value
      },
    )
  }

  fun reportWebviewClientMetricAsync(
    webviewName: String,
    clientMetric: String,
    value: Long,
  ) {
    cs.launch {
      reportWebviewClientMetric(webviewName, clientMetric, value)
    }
  }

  override suspend fun performUpload(batch: List<ClientMetric>) {
    AugmentAPI.instance.clientMetrics(batch)
  }
}

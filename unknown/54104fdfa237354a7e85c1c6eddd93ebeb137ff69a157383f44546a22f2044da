"""The prompt formatter for the selected code and the current file section."""

from enum import Enum
from textwrap import dedent
from typing import Callable

from base.prompt_format_chat.lib.string_formatter import StringFormatter
from base.prompt_format_chat.lib.token_counter import Token<PERSON>ounter
from base.prompt_format_chat.lib.truncation_utils import (
    head_n_lines,
    last_approx_tokens,
    trailing_n_lines,
)
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ExceedContextLength,
)

CHAT_RESPONSE_MESSAGE = "Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction."

AGENT_RESPONSE_MESSAGE = "Noted."

NO_SELECTED_CODE_TEMPLATE = """\
I have the file `{path}` open. Here is an excerpt from the file:

```
{prefix}{suffix}
```

"""

NO_SELECTED_CODE_TEMPLATE_V3 = """\
I have the file `{path}` open. Here is an excerpt from the file:

<augment_code_snippet path="{path}" mode="EXCERPT">
````
{prefix}{suffix}
````
</augment_code_snippet>

"""

NO_SELECTED_CODE_TEMPLATE_V4 = """\
The user has the file `{path}` open. Here is an excerpt from the file:

```
{prefix}{suffix}
```

"""

NO_SELECTED_CODE_TEMPLATE_V5 = """\
The user has the file `{path}` open. The user message may or may not be related to this file.

"""

WHOLE_FILE_IS_SELECTED_TEMPLATE = """\
I have the file `{path}` open and selected all of code within the file:

```
{selected_code}
```

"""

WHOLE_FILE_IS_SELECTED_TEMPLATE_V3 = """\
I have the file `{path}` open and selected all of code within the file:

<augment_code_snippet path="{path}" mode="EXCERPT">
````
{selected_code}
````
</augment_code_snippet>

"""

WHOLE_FILE_IS_SELECTED_TEMPLATE_V4 = """\
The user has the file `{path}` open and selected all of code within the file:

```
{selected_code}
```

"""

WHOLE_FILE_IS_SELECTED_TEMPLATE_V5 = """\
The user has the file `{path}` open and selected all of code within the file:

<augment_code_snippet path="{path}" mode="EXCERPT">
````
{selected_code}
````
</augment_code_snippet>

"""

SELECTED_CODE_TEMPLATE = """\
I have the file `{path}` open and has selected part of the code.

Here is the full file:

```
{prefix}[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
{suffix}
```

Here is the selected code:

```
{selected_code}
```

"""

SELECTED_CODE_TEMPLATE_V3 = """\
I have the file `{path}` open and has selected part of the code.

Here is the full file:

<augment_code_snippet path="{path}" mode="EXCERPT">
````
{prefix}[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
{suffix}
````
</augment_code_snippet>

Here is the selected code:

<augment_code_snippet path="{path}" mode="EXCERPT">
````
{selected_code}
````
</augment_code_snippet>

"""

SELECTED_CODE_TEMPLATE_V4 = """\
The user has the file `{path}` open and has selected part of the code.

Here is the full file:

```
{prefix}[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
{suffix}
```

Here is the selected code:

```
{selected_code}
```

"""

SELECTED_CODE_TEMPLATE_V5 = """\
The user has the file `{path}` open and has selected part of the code.
This may or may not be related to the user message.

Here is the selected code:

```
{selected_code}
```

"""


class SelectedCodeTemplates(Enum):
    NO_SELECTED_CODE = "no_selected_code"
    WHOLE_FILE_IS_SELECTED = "whole_file_is_selected"
    SELECTED_CODE = "selected_code"
    RESPONSE_MESSAGE = "response_message"


def compute_prefix_suffix_length(
    n_prefix_tokens: int,
    n_suffix_tokens: int,
    max_prefix_tokens: int,
    max_suffix_tokens: int,
    total_token_budget: int,
) -> tuple[int, int]:
    """Compute the number of prefix and suffix tokens to use."""
    if n_prefix_tokens < 0:
        raise ValueError("n_prefix_tokens must be non-negative")
    if n_suffix_tokens < 0:
        raise ValueError("n_suffix_tokens must be non-negative")
    if max_prefix_tokens < 0:
        raise ValueError("max_prefix_tokens must be non-negative")
    if max_suffix_tokens < 0:
        raise ValueError("max_suffix_tokens must be non-negative")
    if total_token_budget < 0:
        raise ValueError("total_token_budget must be non-negative")

    n_prefix_tokens = min(n_prefix_tokens, max_prefix_tokens)
    n_suffix_tokens = min(n_suffix_tokens, max_suffix_tokens)

    if n_prefix_tokens + n_suffix_tokens > total_token_budget:
        # We are out of budget even after clipping prefix and suffix tokens.
        # We need to adjust the prefix and suffix tokens further to fit within the budget.
        # First, we allocate half of the remaining budget to the prefix tokens
        n_prefix_tokens_adjusted = min(n_prefix_tokens, total_token_budget // 2)
        # Second, allocate the remaining budget to the suffix tokens
        n_suffix_tokens = min(
            n_suffix_tokens, total_token_budget - n_prefix_tokens_adjusted
        )
        # Finally, if there wasn't too many suffix tokens,
        # we can reallocate the remaining budget back to the prefix tokens.
        n_prefix_tokens = min(n_prefix_tokens, total_token_budget - n_suffix_tokens)

    return n_prefix_tokens, n_suffix_tokens


class SelectedCodePromptFormatterV2:
    """The class formats the selected code prompt."""

    def __init__(
        self,
        token_counter: TokenCounter,
        max_path_tokens: int,
        max_prefix_tokens: int,
        max_suffix_tokens: int,
        version: int = 2,
    ):
        self.token_counter = token_counter
        self.max_path_tokens = max_path_tokens
        self.max_prefix_tokens = max_prefix_tokens
        self.max_suffix_tokens = max_suffix_tokens
        if version == 2:
            self.templates = {
                SelectedCodeTemplates.NO_SELECTED_CODE: NO_SELECTED_CODE_TEMPLATE,
                SelectedCodeTemplates.WHOLE_FILE_IS_SELECTED: WHOLE_FILE_IS_SELECTED_TEMPLATE,
                SelectedCodeTemplates.SELECTED_CODE: SELECTED_CODE_TEMPLATE,
                SelectedCodeTemplates.RESPONSE_MESSAGE: CHAT_RESPONSE_MESSAGE,
            }
        elif version == 3:
            self.templates = {
                SelectedCodeTemplates.NO_SELECTED_CODE: NO_SELECTED_CODE_TEMPLATE_V3,
                SelectedCodeTemplates.WHOLE_FILE_IS_SELECTED: WHOLE_FILE_IS_SELECTED_TEMPLATE_V3,
                SelectedCodeTemplates.SELECTED_CODE: SELECTED_CODE_TEMPLATE_V3,
                SelectedCodeTemplates.RESPONSE_MESSAGE: CHAT_RESPONSE_MESSAGE,
            }
        elif version in {4, 6}:
            self.templates = {
                SelectedCodeTemplates.NO_SELECTED_CODE: NO_SELECTED_CODE_TEMPLATE_V4,
                SelectedCodeTemplates.WHOLE_FILE_IS_SELECTED: WHOLE_FILE_IS_SELECTED_TEMPLATE_V4,
                SelectedCodeTemplates.SELECTED_CODE: SELECTED_CODE_TEMPLATE_V4,
                SelectedCodeTemplates.RESPONSE_MESSAGE: CHAT_RESPONSE_MESSAGE,
            }
        elif version == 5:
            self.templates = {
                SelectedCodeTemplates.NO_SELECTED_CODE: NO_SELECTED_CODE_TEMPLATE_V5,
                SelectedCodeTemplates.WHOLE_FILE_IS_SELECTED: WHOLE_FILE_IS_SELECTED_TEMPLATE_V5,
                SelectedCodeTemplates.SELECTED_CODE: SELECTED_CODE_TEMPLATE_V5,
                SelectedCodeTemplates.RESPONSE_MESSAGE: AGENT_RESPONSE_MESSAGE,
            }
        else:
            raise ValueError(f"Unknown version {version}")

        if max_path_tokens < 0:
            raise ValueError(
                f"max_path_tokens must be non-negative, but got {max_path_tokens}."
            )

        if max_prefix_tokens < 0 or max_suffix_tokens < 0:
            raise ValueError(
                f"max_prefix_tokens and max_suffix_tokens must be non-negative, "
                f"but got {max_prefix_tokens} and {max_suffix_tokens}."
            )
        self.no_selected_code_formatter = StringFormatter(
            self.templates[SelectedCodeTemplates.NO_SELECTED_CODE],
            self.token_counter,
        )
        self.whole_file_is_selected_formatter = StringFormatter(
            self.templates[SelectedCodeTemplates.WHOLE_FILE_IS_SELECTED],
            self.token_counter,
        )
        self.selected_code_formatter = StringFormatter(
            self.templates[SelectedCodeTemplates.SELECTED_CODE],
            self.token_counter,
        )
        self.selected_code_response_message = self.templates[
            SelectedCodeTemplates.RESPONSE_MESSAGE
        ]

    def _compute_prefix_suffix_length(
        self, n_prefix_tokens: int, n_suffix_tokens: int, total_token_budget: int
    ) -> tuple[int, int]:
        """Compute the number of prefix and suffix tokens to use."""
        return compute_prefix_suffix_length(
            n_prefix_tokens=n_prefix_tokens,
            n_suffix_tokens=n_suffix_tokens,
            max_prefix_tokens=self.max_prefix_tokens,
            max_suffix_tokens=self.max_suffix_tokens,
            total_token_budget=total_token_budget,
        )

    def format(
        self,
        prompt_input: ChatPromptInput,
        total_token_budget: int,
    ) -> tuple[str, str, str]:
        """Format the current file.

        There are four situations to format:
        - no selected code, no prefix/suffix: return empty string
        - no selected code, with prefix/suffix: use self.no_selected_code_formatter
        - selected code, no prefix/suffix: use self.whole_file_is_selected_formatter
        - selected code, with prefix/suffix: use self.selected_code_formatter

        Returns:
            - prompt: the prompt
            - clipped_prefix: the clipped prefix
            - clipped_suffix: the clipped suffix
        """
        if (
            prompt_input.selected_code == ""
            and prompt_input.prefix == ""
            and prompt_input.suffix == ""
        ):
            return "", "", ""

        clipped_path = last_approx_tokens(
            prompt_input.path, self.max_path_tokens, self.token_counter
        )

        if prompt_input.prefix == "" and prompt_input.suffix == "":
            prompt = self.whole_file_is_selected_formatter.format(
                {"path": clipped_path, "selected_code": prompt_input.selected_code}
            )
            if self.token_counter.count_tokens(prompt) > total_token_budget:
                raise ExceedContextLength(
                    f"The prompt is too long, with length >= {self.token_counter.count_tokens(prompt)} "
                    f"(total token budget: {total_token_budget})."
                )
            return prompt, "", ""

        prompt_formatter = (
            self.no_selected_code_formatter
            if prompt_input.selected_code == ""
            else self.selected_code_formatter
        )

        min_prompt_tok_ct = prompt_formatter.format_and_count_tokens(
            {
                "path": clipped_path,
                "selected_code": prompt_input.selected_code,
                "prefix": "",
                "suffix": "",
            }
        )
        remaining_budget_for_prefix_suffix = total_token_budget - min_prompt_tok_ct
        if remaining_budget_for_prefix_suffix < 0:
            raise ExceedContextLength(
                f"The prompt is too long, with length >= {min_prompt_tok_ct} "
                f"(total token budget: {total_token_budget})."
            )

        # Note: we need to do it line-by-line because result might be different if computed on the whole string at once.
        # And later we clip line-by-line, so we need computation methods to match.
        num_prefix_tokens = sum(
            [
                self.token_counter.count_tokens(line)
                for line in prompt_input.prefix.splitlines(keepends=True)
            ]
        )
        num_suffix_tokens = sum(
            [
                self.token_counter.count_tokens(line)
                for line in prompt_input.suffix.splitlines(keepends=True)
            ]
        )
        n_prefix_tokens, n_suffix_tokens = self._compute_prefix_suffix_length(
            num_prefix_tokens, num_suffix_tokens, remaining_budget_for_prefix_suffix
        )

        clipped_prefix = trailing_n_lines(
            prompt_input.prefix, n_prefix_tokens, self.token_counter
        )
        clipped_suffix = head_n_lines(
            prompt_input.suffix, n_suffix_tokens, self.token_counter
        )

        prompt = prompt_formatter.format(
            {
                "path": clipped_path,
                "selected_code": prompt_input.selected_code,
                "prefix": clipped_prefix,
                "suffix": clipped_suffix,
            }
        )
        prompt_tok_ct = self.token_counter.count_tokens(prompt)
        final_budget = total_token_budget - prompt_tok_ct
        if final_budget < 0:
            raise ExceedContextLength(
                f"The prompt is too long, with length >= {prompt_tok_ct} "
                f"(total token budget: {final_budget})."
            )

        return prompt, clipped_prefix, clipped_suffix

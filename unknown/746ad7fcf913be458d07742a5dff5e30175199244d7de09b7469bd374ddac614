"""Tests the behavior of the rogue prompt formatter."""

from typing import Iterable, Optional

from base import tokenizers
from base.prompt_format_completion import Prompt<PERSON>hunk, PromptInput
from base.prompt_format_completion.rogue_prompt_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>atter
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig


def _get_rogue_prompt_formatter(config: Optional[TokenApportionmentConfig] = None):
    tokenizer = tokenizers.create_tokenizer_by_name("rogue")
    if not config:
        config = TokenApportionmentConfig(
            max_content_len=2048,
            input_fraction=0.5,
            prefix_fraction=0.75,
            max_path_tokens=20,
        )
    prompter = RoguePromptFormatter(config, tokenizer)
    return prompter, tokenizer


def _format(
    prompt_input: PromptInput,
    config: Optional[TokenApportionmentConfig] = None,
    max_output_token_count: int = 64,
):
    prompter, tokenizer = _get_rogue_prompt_formatter(config)

    prompt_tokens = prompter.format_prompt(
        prompt_input, max_output_token_count=max_output_token_count
    ).tokens()
    print(len(prompt_tokens), prompt_tokens)
    prompt = tokenizer.detokenize(prompt_tokens)
    print(prompt)
    return prompt


def test_rogue_prompt_formatter_basic(example_input: PromptInput):
    """This is a simple sanity check to catch obvious bugs in Starcoder's prompt formatting."""
    prompt = _format(example_input)

    expected_prompt = (
        """<|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
        ""
    )
    assert prompt == expected_prompt


def test_rogue_prompt_formatter_empty_suffix(example_input: PromptInput):
    """Verifies the formatting with an empty suffix."""

    example_input.suffix = ""
    prompt = _format(example_input)

    expected_prompt = """<|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
<fim_suffix><fim_middle>"""
    assert prompt == expected_prompt


def test_rogue_prompt_formatter_empty_prefix_and_suffix():
    """Verifies the formatting with an empty prefix and suffix.

    This can e.g. happen for a completion in an empty file.
    """
    test_prompt_input = PromptInput(
        path="src/example.py",
        prefix="",
        suffix="",
        prefix_begin=0,
        retrieved_chunks=(),
    )
    prompt = _format(test_prompt_input)

    expected_prompt = """<|retrieval_section|><fim_prefix><filename>src/example.py<|prefix-body|><fim_suffix><fim_middle>"""
    assert prompt == expected_prompt


def test_rogue_prompt_formatter_retrieval(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """Tests the basic behavior with retrieved chunks."""
    example_input.retrieved_chunks = example_chunks
    prompt = _format(example_input)

    expected_prompt = """<|retrieval_section|><|ret-start|><filename>src/foo.py<|ret-body|># You can aggregate
# with a pooling function.
<|ret-start|><filename>src/bar.py<|ret-body|># You can aggregate
# with a maxing
# function.
<fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt


def test_rogue_prompt_formatter_retrieval_no_newline(
    example_input: PromptInput, example_chunks_no_newline: Iterable[PromptChunk]
):
    """Tests the basic behavior with retrieved chunks where the chunks do not end on a newline."""

    example_input.retrieved_chunks = example_chunks_no_newline
    prompt = _format(example_input)

    # starcoder/repocoder had really unpleasent behavior in this case, which is no fixed.
    expected_prompt = """<|retrieval_section|><|ret-start|><filename>src/foo.py<|ret-body|>def aggregate(a,b):
    while<fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt


def test_rogue_prompt_formatter_retrieval_content_len_limit(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """Tests the basic behavior with retrieved chunks with a limited content length."""

    config = TokenApportionmentConfig(
        max_content_len=128,
        input_fraction=0.7,
        prefix_fraction=0.75,
        max_path_tokens=20,
    )
    example_input.retrieved_chunks = example_chunks
    prompt = _format(example_input, config=config)
    # one chunk is dropped, the high value chunk is kept
    expected_prompt = """<|retrieval_section|><|ret-start|><filename>src/bar.py<|ret-body|># You can aggregate
# with a maxing
# function.
<fim_prefix><filename>src/example.py<|prefix-body|>def aggregate(a,b):
<fim_suffix>
return aggregated_output
<fim_middle>"""
    assert prompt == expected_prompt

    # The second part of the test was buggy and got removed; the test only
    # passed because the formerly stateful `example_chunks` object was
    # reused.


def test_rogue_prompt_formatter_uses_tokens():
    """Assert that we've fixed AU-661 - inputs are measured in tokens not characters."""

    test_input = PromptInput(
        path=" on",  # Tokenizes to 1 token
        prefix=" the" * 8,  # Tokenizes to 8 tokens
        suffix=" an",  # Tokenizes to 1 token
        prefix_begin=0,
        retrieved_chunks=(),
    )

    # Set max_content_len to 32. That's less than the number of characters (38) in the input
    # so if the prompt formatter is counting characters, it will truncate.
    #
    # But 32 is a lot more than the number of tokens (10) in the input, so if the prompt formatter
    # is counting tokens, it will include all the input.
    #
    # 32 leaves us with 22 token budget for special tokens and output tokens.
    config = TokenApportionmentConfig(
        max_content_len=32,
        max_path_tokens=2,
        input_fraction=1.0,
        prefix_fraction=1.0,
    )

    prompt = _format(test_input, config=config, max_output_token_count=1)
    assert " on" in prompt
    assert " the" in prompt
    assert " an" in prompt


def test_rogue_prompt_formatter_retrieval_path_size(
    example_input: PromptInput, example_chunks: Iterable[PromptChunk]
):
    """Tests the behavior with retrieved chunks with a limited path length."""

    config = TokenApportionmentConfig(
        max_content_len=128,
        input_fraction=0.7,
        prefix_fraction=0.75,
        max_path_tokens=3,
    )
    example_input.retrieved_chunks = example_chunks
    prompt = _format(example_input, config=config)
    expected_prompt = "".join(
        [
            "<|retrieval_section|>",
            "<|ret-start|><filename>foo.py",
            "<|ret-body|># You can aggregate\n# with a pooling function.\n",
            "<|ret-start|><filename>bar.py",
            "<|ret-body|># You can aggregate\n# with a maxing\n# function.\n",
            "<fim_prefix><filename>example.py<|prefix-body|>def aggregate(a,b):\n",
            "<fim_suffix>\nreturn aggregated_output\n",
            "<fim_middle>",
        ]
    )
    assert prompt == expected_prompt

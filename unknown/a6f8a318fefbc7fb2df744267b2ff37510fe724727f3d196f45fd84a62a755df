determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - RepoEval api, diffb1m_16b_alphal_fixtoken, ethanol6-15.2
  workspace: Dev
  project: Eval
import_modules:
  experimental.igor.systems.ethanol
system:
    name: basic_rag
    model:
      checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
      name: rogue
      prompt:
        max_prefix_tokens: 1280
        max_prompt_tokens: 3816
        max_retrieved_chunk_tokens: -1
        max_suffix_tokens: 768
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      scorer:
        name: ethanol
        checkpoint_path: ethanol/ethanol6-15.2
      chunker:
        name: line_level
        max_lines_per_chunk: 30
      query_formatter:
        name: ethanol6_query
        max_tokens: 1023
        add_path: true
        add_suffix: true
        prefix_ratio: 0.9
      document_formatter:
        name: ethanol6_document
        max_tokens: 999
        add_path: true
        add_prefix: true
    experimental:
      remove_suffix: False
      retriever_top_k: 100
      trim_on_dedent: False
      trim_on_max_lines: null
task:
  dataset: finegrained-python.large
  name: api
podspec: 1xA100.yaml

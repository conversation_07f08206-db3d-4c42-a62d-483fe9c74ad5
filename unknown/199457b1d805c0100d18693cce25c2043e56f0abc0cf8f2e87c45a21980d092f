package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/wait"

	"github.com/augmentcode/augment/base/logging"
	"github.com/rs/zerolog/log"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/tools/cache"
)

func buildConfigWithContextFromFlags(context string, kubeconfigPath string) (*rest.Config, error) {
	return clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
		&clientcmd.ClientConfigLoadingRules{ExplicitPath: kubeconfigPath},
		&clientcmd.ConfigOverrides{
			CurrentContext: context,
		}).ClientConfig()
}

type SyncPolicy struct {
	SourceContext   string
	SourceNamespace string
	SourceName      string // If empty, sync all
	TargetContext   string
	TargetNamespace string
	TargetName      string // If empty, maintain original name
	Kind            string
}

type Config struct {
	Policies []SyncPolicy
}

func sanityCheck(config *Config) error {
	errors := make([]string, 0)

	// Check for valid regex
	for _, p := range config.Policies {
		if p.Kind == "" {
			errors = append(errors, fmt.Sprintf("Invalid kind: %s", p.Kind))
		}
		if p.SourceNamespace == "" {
			errors = append(errors, fmt.Sprintf("Invalid source namespace: %s", p.SourceNamespace))
		}
		if p.TargetNamespace == "" {
			errors = append(errors, fmt.Sprintf("Invalid target namespace: %s", p.TargetNamespace))
		}
		if p.SourceContext == "" {
			errors = append(errors, fmt.Sprintf("Invalid source context: %s", p.SourceContext))
		}
		if p.TargetContext == "" {
			errors = append(errors, fmt.Sprintf("Invalid target context: %s", p.TargetContext))
		}
		// SourceName and TargetName are optional. An empty SourceName means to
		// sync all objects, and an empty TargetName means that the target's
		// name should match the source's name. However, TargetName only makes
		// sense if SourceName is also specified.
		if p.SourceName == "" && p.TargetName != "" {
			errors = append(errors, fmt.Sprintf("Target name can only be specified with source name: %s", p.TargetName))
		}
	}

	if len(config.Policies) == 0 {
		errors = append(errors, "No policies")
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors: %s", errors)
	}
	return nil
}

type SecretSyncer struct {
	KubeConfig      string
	SourceContext   string
	SourceNamespace string
	SourceName      string
	TargetContext   string
	TargetNamespace string
	TargetName      string
	sourceClientset *kubernetes.Clientset
	targetClientset *kubernetes.Clientset
	stopCh          chan struct{}
}

func NewSecretSyncer(kubeconfig string, policy SyncPolicy, stopCh chan struct{}) (*SecretSyncer, error) {
	if policy.Kind != "Secret" {
		log.Fatal().Msg("Only Secret is supported")
		return nil, fmt.Errorf("only kind Secret is supported")
	}
	sourceConfig, err := buildConfigWithContextFromFlags(policy.SourceContext, kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error building kubeconfig")
		return nil, err
	}

	sourceClientset, err := kubernetes.NewForConfig(sourceConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating Kubernetes client")
		return nil, err
	}

	targetConfig, err := buildConfigWithContextFromFlags(policy.TargetContext, kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error building kubeconfig")
		return nil, err
	}

	targetClientset, err := kubernetes.NewForConfig(targetConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating Kubernetes client")
		return nil, err
	}

	return &SecretSyncer{
		KubeConfig:      kubeconfig,
		SourceContext:   policy.SourceContext,
		SourceNamespace: policy.SourceNamespace,
		SourceName:      policy.SourceName,
		TargetContext:   policy.TargetContext,
		TargetNamespace: policy.TargetNamespace,
		TargetName:      policy.TargetName,

		sourceClientset: sourceClientset,
		targetClientset: targetClientset,
		stopCh:          stopCh,
	}, nil
}

func (s SecretSyncer) String() string {
	return fmt.Sprintf("SecretSyncer{KubeConfig: %s, SourceContext: %s, SourceNamespace: %s, SourceName: %s, TargetContext: %s, TargetNamespace: %s, TargetName: %s}",
		s.KubeConfig, s.SourceContext, s.SourceNamespace, s.SourceName, s.TargetContext, s.TargetNamespace, s.TargetName)
}

func (s SecretSyncer) Abort(err error) {
	log.Error().Err(err).Msg("Error syncing Secrets")
	panic(err)
}

func (s SecretSyncer) CreateOrUpdate(secret *corev1.Secret) error {
	var exists bool
	targetName := s.getTargetName(secret)
	targetSecret, err := s.targetClientset.CoreV1().Secrets(s.TargetNamespace).Get(context.Background(), targetName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			exists = false
		} else {
			return err
		}
	} else {
		if targetSecret.Labels["eng.augmentcode.com/cloud-synced"] != "true" {
			log.Info().Msgf("Secret not updated as not owned by cloud-sync: %s/%s", targetSecret.Namespace, targetSecret.Name)
			return nil
		}
		exists = true
	}

	secret.Name = targetName
	secret.ObjectMeta.Name = targetName
	secret.ObjectMeta.Labels["eng.augmentcode.com/cloud-synced"] = "true"
	secret.Namespace = s.TargetNamespace
	secret.ResourceVersion = ""
	secret.UID = ""

	if exists {
		updatedSecret, err := s.targetClientset.CoreV1().Secrets(secret.Namespace).Update(context.Background(), secret, metav1.UpdateOptions{})
		if err != nil {
			return err
		}
		log.Info().Msgf("Synced Secret updated: %s/%s in %s", updatedSecret.Namespace, updatedSecret.Name, s.TargetContext)
		return nil
	} else {
		newSecret, err := s.targetClientset.CoreV1().Secrets(secret.Namespace).Create(context.Background(), secret, metav1.CreateOptions{})
		if err != nil {
			return err
		}
		log.Info().Msgf("Synced Secret created: %s/%s in %s", newSecret.Namespace, newSecret.Name, s.TargetContext)
		return nil
	}
}

func (s SecretSyncer) getSecret(obj interface{}) (*corev1.Secret, error) {
	secret, ok := obj.(*corev1.Secret)
	if !ok {
		return nil, fmt.Errorf("error casting object to Secret")
	}
	if s.SourceName != "" && s.SourceName != secret.Name {
		log.Debug().Msgf("Ignoring Secret: %s/%s in %s", secret.Namespace, secret.Name, s.SourceContext)
		return nil, nil
	}
	return secret, nil
}

func (s SecretSyncer) getTargetName(secret *corev1.Secret) string {
	if s.TargetName != "" {
		return s.TargetName
	}
	return secret.Name
}

func (s SecretSyncer) HandleAdd(obj interface{}) {
	secret, err := s.getSecret(obj)
	if err != nil {
		s.Abort(err)
		return
	} else if secret == nil { // pragma: allowlist secret
		return
	}
	log.Info().Msgf("Secret added: %s/%s/%s in %s", secret.Namespace, secret.Name, secret.ResourceVersion, s.SourceContext)
	err = s.CreateOrUpdate(secret)
	if err != nil {
		s.Abort(err)
		return
	}
}

func (s SecretSyncer) HandleUpdate(oldObj, newObj interface{}) {
	secret, err := s.getSecret(newObj)
	if err != nil {
		s.Abort(err)
		return
	} else if secret == nil { // pragma: allowlist secret
		return
	}
	log.Info().Msgf("Secret updated: %s/%s/%s in %s", secret.Namespace, secret.Name, secret.ResourceVersion, s.SourceContext)
	err = s.CreateOrUpdate(secret)
	if err != nil {
		s.Abort(err)
		return
	}
}

func (s SecretSyncer) HandleDelete(obj interface{}) {
	secret, err := s.getSecret(obj)
	if err != nil {
		s.Abort(err)
		return
	} else if secret == nil { // pragma: allowlist secret
		return
	}
	log.Info().Msgf("Secret deleted: %s/%s in %s", secret.Namespace, secret.Name, s.SourceContext)
	targetName := s.getTargetName(secret)
	targetSecret, err := s.targetClientset.CoreV1().Secrets(s.TargetNamespace).Get(context.Background(), targetName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return
		}
		s.Abort(err)
		return
	}
	if targetSecret.Labels["eng.augmentcode.com/cloud-synced"] != "true" {
		log.Info().Msgf("Secret not deleted as not owned by cloud-sync: %s/%s", targetSecret.Namespace, targetSecret.Name)
	}
	err = s.targetClientset.CoreV1().Secrets(targetSecret.Namespace).Delete(context.Background(), targetSecret.Name, metav1.DeleteOptions{})
	if err != nil {
		s.Abort(err)
		return
	}
	log.Info().Msgf("Synced Secret deleted: %s/%s in %s", targetSecret.Namespace, targetSecret.Name, s.TargetContext)
}

func (s SecretSyncer) Run() {
	log.Info().Msgf("Running syncer: %s", s)

	// Set up informers for Secrets
	factory := informers.NewSharedInformerFactoryWithOptions(s.sourceClientset, 0,
		informers.WithNamespace(s.SourceNamespace),
		informers.WithTweakListOptions(func(opts *metav1.ListOptions) {
			opts.LabelSelector = "eng.augmentcode.com/cloud-sync=true"
		}))
	secretInformer := factory.Core().V1().Secrets()

	informer := secretInformer.Informer()
	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    s.HandleAdd,
		UpdateFunc: s.HandleUpdate,
		DeleteFunc: s.HandleDelete,
	})

	log.Info().Msg("Starting informer")

	go informer.Run(s.stopCh)
	if !cache.WaitForCacheSync(s.stopCh, informer.HasSynced) {
		s.Abort(fmt.Errorf("error waiting for informer cache to sync"))
	}

	log.Info().Msg("Synced informer")

	wait.Forever(func() { <-s.stopCh }, 0)
}

func main() {
	logging.SetupServerLogging()

	var configFile string
	flag.StringVar(&configFile, "config", "", "Path to config file")
	var kubeconfig string
	flag.StringVar(&kubeconfig, "kubeconfig", "tools/deploy/auth_kube_config.yaml", "Path to kubeconfig file")

	flag.Parse()
	log.Info().Msgf("Config file: %s", configFile)
	log.Info().Msgf("Kubeconfig: %s", kubeconfig)

	// check if kubeconfig exists
	if _, err := os.Stat(kubeconfig); os.IsNotExist(err) {
		log.Fatal().Err(err).Msg("kubeconfig file does not exist")
		os.Exit(1)
	}

	var config Config
	if configFile == "" {
		log.Fatal().Msg("Missing config file")
		os.Exit(1)
	}

	f, err := os.Open(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
		os.Exit(1)
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()

	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
		os.Exit(1)
	}
	log.Info().Msgf("Config: %s", config)

	if err := sanityCheck(&config); err != nil {
		log.Fatal().Err(err).Msg("Error in config file")
		os.Exit(1)
	}

	stopCh := make(chan struct{})
	defer close(stopCh)

	var syncers []*SecretSyncer
	for _, policy := range config.Policies {
		syncer, err := NewSecretSyncer(kubeconfig, policy, stopCh)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating syncer")
			os.Exit(1)
		}
		syncers = append(syncers, syncer)
	}

	for _, syncer := range syncers {
		go syncer.Run()
	}
	wait.Forever(func() { <-stopCh }, 0)
	os.Exit(1)
}

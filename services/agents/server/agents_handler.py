import json
import time
from typing import Callable, TypeVar

import google.protobuf.message
import grpc
import structlog
from google.rpc import status_pb2
from prometheus_client import Counter, Histogram
from pydantic import ValidationError

import base.feature_flags
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.third_party_clients.third_party_model_client import (
    ModelAPICall,
    PromptCacheUsage,
    ThirdPartyModelClient,
    ToolDefinition,
    ToolUseResponse,
)
from services.agents import agents_pb2
from services.agents.agents_pb2 import (
    CodebaseRetrievalRequest,
    CodebaseRetrievalResponse,
    EditFileRequest,
    EditFileResponse,
    ListRemoteToolsRequest,
    ListRemoteToolsResponse,
    ListRemoteToolsResponseMessage,
    LLMGenerateRequest,
    LLMGenerateResponse,
    RemoteToolId,
    RunRemoteToolRequest,
    RunRemoteToolResponse,
)
from services.agents.server.agents_tools import create_tool_extra_input
from services.agents.server.codebase_retrieval_agent import (
    CodebaseMultiRetrievalAgent,
    CodebaseQueryRewritingRetrievalAgent,
    SimpleRetrievalAgent,
)
from services.agents.server.edit_file_agent import EditClientProvider, EditFileAgent
from services.agents.tool import (
    Tool,
    ToolAuthenticationError,
    ToolNotAvailableError,
    ToolRequestContext,
)
from services.chat_host import chat_pb2
from services.chat_host.chat_proto_util import (
    convert_history,
    convert_tool_choice,
    request_to_message,
)
from services.edit_host.client import EditClient
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    Retriever,
)
from services.request_insight import request_insight_pb2
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
    new_event,
)

log: structlog.stdlib.BoundLogger = structlog.get_logger()

_RETRIEVAL_TOOL_NAME = base.feature_flags.StringFlag("retrieval_tool_name", "multi")
_RETRIEVAL_GEN_MODEL = base.feature_flags.StringFlag(
    "retrieval_tool_generation_model", ""
)
_EDIT_FILE_GEN_MODEL = base.feature_flags.StringFlag(
    "edit_file_tool_generation_model", ""
)

_remote_tool_latency = Histogram(
    "au_remote_tool_latency",
    "Latency of remote tool calls in seconds",
    ["tool_name", "tenant_name"],
)

_remote_tool_response_size = Histogram(
    "au_remote_tool_response_size",
    "Sizes of remote tool responses in bytes",
    ["tool_name", "tenant_name"],
    # Exponentially increasing buckets from 64 bytes to 1M bytes
    buckets=[2**i for i in range(6, 21)] + [float("inf")],
)

_remote_tool_counter = Counter(
    "au_remote_tool_calls",
    "Count of remote tool calls",
    ["tool_name", "tenant_name", "status"],
)

MsgT = TypeVar("MsgT", bound=google.protobuf.message.Message)


class AgentsHandler:
    def __init__(
        self,
        ri_publisher: RequestInsightPublisher,
        llm_clients: dict[str, ThirdPartyModelClient],
        default_llm_client_name: str,
        codebase_retriever: Retriever[ChatRetrieverPromptInput],
        commit_retriever: Retriever[ChatRetrieverPromptInput],
        edit_client_provider: EditClientProvider,
        tools: list[Tool],
    ):
        self.ri_publisher = ri_publisher
        self.llm_clients = llm_clients
        self.default_llm_client_name = default_llm_client_name
        self.codebase_retriever = codebase_retriever
        self.commit_retriever = commit_retriever
        self.edit_file_agent = EditFileAgent(
            edit_client_provider,
            ri_publisher=ri_publisher,
        )
        self.tools: dict[RemoteToolId.ValueType, Tool] = {
            tool.id: tool for tool in tools
        }
        log.info(f"Registered tools: {self.tools}")

    def publish_event(
        self,
        msg: MsgT,
        field_accessor: Callable[[request_insight_pb2.RequestEvent], MsgT],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        event = new_event()
        field_accessor(event).MergeFrom(msg)
        self.ri_publisher.publish_request_insight(
            self.ri_publisher.update_request_info_request(
                request_id=request_context.request_id,
                events=[event],
                auth_info=auth_info,
            )
        )

    def llm_generate(
        self,
        request: LLMGenerateRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> LLMGenerateResponse:
        self.publish_event(
            request_insight_pb2.RILLMGenerateRequest(request=request),
            lambda e: e.llm_generate_request,
            request_context,
            auth_info,
        )

        # Define a callback to record model API calls
        def model_api_call_callback(api_call: ModelAPICall):
            event = new_event()
            model_api_call_event = event.model_api_call
            model_api_call_event.model_name = api_call.model_name
            model_api_call_event.provider = api_call.provider
            if api_call.region:
                model_api_call_event.region = api_call.region
            if api_call.request_params:
                model_api_call_event.request_params_json = json.dumps(
                    api_call.request_params
                )
            if api_call.response_metadata:
                model_api_call_event.response_metadata_json = json.dumps(
                    api_call.response_metadata
                )

            self.ri_publisher.publish_request_insight(
                self.ri_publisher.update_request_info_request(
                    request_id=request_context.request_id,
                    events=[event],
                    auth_info=auth_info,
                )
            )

        resp = self.llm_clients[self.default_llm_client_name].generate_response_stream(
            model_caller="agent-llm-generate",
            system_prompt=request.system_prompt,
            cur_message=request_to_message("", request.user_message),
            chat_history=convert_history(request.dialog),
            tool_definitions=[
                ToolDefinition(
                    tool.name,
                    tool.description,
                    tool.input_schema_json,
                )
                for tool in request.tool_definitions
            ],
            tool_choice=convert_tool_choice(request.tool_choice)
            if request.HasField("tool_choice")
            else None,
            temperature=request.temperature,
            # If max_tokens wasn't provided (protobuf default: 0), use the
            # client default
            max_output_tokens=request.max_tokens or None,
            use_caching=True,
            request_context=request_context,
            model_api_call_callback=model_api_call_callback,
        )
        text_accum = []
        tool_use: ToolUseResponse | None = None
        prompt_cache_usage: PromptCacheUsage | None = None
        for chunk in resp:
            if chunk.text:
                text_accum.append(chunk.text)
            elif chunk.tool_use:
                tool_use = chunk.tool_use
                pass
            elif chunk.prompt_cache_usage:
                prompt_cache_usage = chunk.prompt_cache_usage
                pass
            # Intentionally ignoring tool_use_start, final_parameters, and
            # end_of_stream events
        resp_nodes = []
        if text_accum:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
                    content="".join(text_accum),
                )
            )
        if tool_use:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )

        response = LLMGenerateResponse(
            response_nodes=resp_nodes,
        )
        self.publish_event(
            request_insight_pb2.RILLMGenerateResponse(response=response),
            lambda e: e.llm_generate_response,
            request_context,
            auth_info,
        )
        if prompt_cache_usage:
            self.publish_event(
                request_insight_pb2.PromptCacheUsage(
                    input_tokens=prompt_cache_usage.input_tokens,
                    cache_read_input_tokens=prompt_cache_usage.cache_read_input_tokens,
                    cache_creation_input_tokens=prompt_cache_usage.cache_creation_input_tokens,
                ),
                lambda e: e.prompt_cache_usage,
                request_context,
                auth_info,
            )
        return response

    def codebase_retrieval(
        self,
        request: CodebaseRetrievalRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> CodebaseRetrievalResponse:
        self.publish_event(
            request_insight_pb2.RIRemoteToolCallRequest(
                codebase_retrieval_request=request
            ),
            lambda e: e.remote_tool_call_request,
            request_context,
            auth_info,
        )

        context = base.feature_flags.get_global_context().bind_attribute(
            "tenant_name", auth_info.tenant_name
        )

        # Initialize response components
        codebase_results = ""
        commit_results = ""

        # Handle codebase retrieval (unless disabled)
        if not request.disable_codebase_retrieval:
            name = _RETRIEVAL_GEN_MODEL.get(context)
            llm_client = self.llm_clients.get(
                name, self.llm_clients[self.default_llm_client_name]
            )

            retrieval_tool_name = _RETRIEVAL_TOOL_NAME.get(
                context,
            )
            if retrieval_tool_name == "multi":
                retrieval_agent = CodebaseMultiRetrievalAgent(
                    retriever=self.codebase_retriever,
                    ri_publisher=self.ri_publisher,
                )
            elif retrieval_tool_name == "query_rewrite":
                retrieval_agent = CodebaseQueryRewritingRetrievalAgent(
                    retriever=self.codebase_retriever,
                    ri_publisher=self.ri_publisher,
                )
            else:
                raise ValueError(f"Unknown retrieval tool name: {retrieval_tool_name}")

            codebase_response = retrieval_agent.run(
                request, llm_client, request_context, auth_info
            )
            codebase_results = codebase_response.formatted_retrieval

        # Handle commit retrieval (if enabled)
        if request.enable_commit_retrieval:
            log.info("Running commit retrieval agent", request=request)
            retrieval_agent = SimpleRetrievalAgent(
                retriever=self.commit_retriever,
                ri_publisher=self.ri_publisher,
            )
            commit_results = retrieval_agent.run(
                request, None, request_context, auth_info
            ).formatted_retrieval

        # Combine results
        if codebase_results and commit_results:
            combined_results = codebase_results + commit_results
        elif codebase_results:
            combined_results = codebase_results
        elif commit_results:
            combined_results = commit_results
        else:
            combined_results = "No retrieval was performed based on the provided flags."

        response = CodebaseRetrievalResponse(formatted_retrieval=combined_results)

        self.publish_event(
            request_insight_pb2.RIRemoteToolCallResponse(
                codebase_retrieval_response=response
            ),
            lambda e: e.remote_tool_call_response,
            request_context,
            auth_info,
        )
        return response

    def edit_file(
        self,
        request: EditFileRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> EditFileResponse:
        self.publish_event(
            request_insight_pb2.RIRemoteToolCallRequest(edit_file_request=request),
            lambda e: e.remote_tool_call_request,
            request_context,
            auth_info,
        )
        context = base.feature_flags.get_global_context().bind_attribute(
            "tenant_name", auth_info.tenant_name
        )
        name = _EDIT_FILE_GEN_MODEL.get(context)
        llm_client = self.llm_clients.get(
            name, self.llm_clients[self.default_llm_client_name]
        )
        response = self.edit_file_agent.run(
            request, llm_client, request_context, auth_info
        )
        self.publish_event(
            request_insight_pb2.RIRemoteToolCallResponse(edit_file_response=response),
            lambda e: e.remote_tool_call_response,
            request_context,
            auth_info,
        )
        return response

    def _get_tool_availability_status(
        self, tool: Tool, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            return tool.get_availability_status(request_context)
        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.UNAVAILABLE:  # type: ignore
                # This is a valid failure that can occur when the service for a tool is not deployed.
                # Special cased so that we can be less verbose about it + not treat as a Python error.
                log.warn(
                    f"Failed to get tool availability status for tool '{tool.name}'. gRPC unavailable. Is service deployed?"
                )
                return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

            # Otherwise this is an unexpected error, raise for someone else to handle.
            raise

    def list_remote_tools(
        self,
        request: ListRemoteToolsRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> ListRemoteToolsResponse:
        found_tools: list[ListRemoteToolsResponseMessage] = []
        tool_ids = request.tool_ids
        # Local logger for local variable context binding.
        llog = log.bind(
            requested_tool_ids=sorted(list(tool_ids)),
            self_tool_ids=sorted(list(self.tools.keys())),
        )
        unavailable_tools = []
        if len(tool_ids) == 0:
            # For backwards compatibility, if we get an empty request, return
            # just the web search tool
            tool_ids = [RemoteToolId.WEB_SEARCH]
        for tool_id in tool_ids:
            if tool_id not in self.tools:
                continue

            tool = self.tools[tool_id]

            # Only return tools that successfully respond with an availability status.
            availability_status = self._get_tool_availability_status(
                tool, request_context
            )
            if availability_status == agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS:
                unavailable_tools.append(tool_id)
                continue

            tool_request_context = ToolRequestContext(
                request_context=request_context, auth_info=auth_info
            )
            tool_definition = tool.get_tool_definition(request_context)
            found_tools.append(
                ListRemoteToolsResponseMessage(
                    tool_definition=chat_pb2.ToolDefinition(
                        name=tool_definition.name,
                        description=tool_definition.description,
                        input_schema_json=tool_definition.input_schema_json,
                    ),
                    remote_tool_id=tool.id,
                    availability_status=availability_status,
                    tool_safety=tool.get_tool_safety(tool_request_context),
                    oauth_url=tool.get_oauth_url(request_context),
                )
            )

        llog.info(
            "list_remote_tools (canonical)",
            unavailable_tools=unavailable_tools,
            returned_tools=sorted([tool.remote_tool_id for tool in found_tools]),
        )
        return ListRemoteToolsResponse(tools=found_tools)

    def run_remote_tool(
        self,
        request: RunRemoteToolRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> RunRemoteToolResponse:
        start_time = time.time()
        tool_name = request.tool_name
        metrics_tenant_name = auth_info.metrics_tenant_name

        self.publish_event(
            request_insight_pb2.RIRemoteToolCallRequest(
                ri_run_remote_tool_request=request_insight_pb2.RIRunRemoteToolRequest(
                    tool_name=tool_name,
                    tool_input_json=request.tool_input_json,
                    tool_id=request.tool_id,
                    # omit extra_tool_input, since it may contain sensitive info
                    # like API tokens
                )
            ),
            lambda e: e.remote_tool_call_request,
            request_context,
            auth_info,
        )

        if request.tool_id == 0 and tool_name != "":
            # For backwards compatibility
            tool = None
            for t in self.tools.values():
                if t.name == tool_name:
                    tool = t
                    break
        else:
            tool = self.tools.get(request.tool_id)

        # NOTE: Out of an abundance of caution, tool name, if not known to our server
        # already, should be considered sensitive information. Tool input is
        # always considered as such. They are produced by a model given access
        # to customer's codebase (or explicitly written into an API call, which doesn't
        # make them less sensitive).
        # Metrics and logs may include tool ID and tool names that are part of our
        # interface or the set of tools we provide.
        # OK to return errors containing this information as part of the tool output
        # itself.
        if tool is not None:
            try:
                tool_input = json.loads(request.tool_input_json)
                extra_tool_input = create_tool_extra_input(request)
                tool_output = tool.run(tool_input, extra_tool_input, request_context)
                tool_result_message = ""
                is_error = False
                status = agents_pb2.RemoteToolResponseStatus.TOOL_EXECUTION_SUCCESS
                _remote_tool_counter.labels(
                    tool_name=tool.name,
                    tenant_name=metrics_tenant_name,
                    status="success",
                ).inc()
            except json.JSONDecodeError as e:
                log.info(
                    "Error calling tool (id=%s, name=%s): Invalid JSON input",
                    request.tool_id,
                    tool.name,
                )
                tool_output = f"Invalid JSON input: {e}"
                tool_result_message = "Invalid JSON input."
                is_error = True
                status = agents_pb2.RemoteToolResponseStatus.INVALID_TOOL_INPUT
                _remote_tool_counter.labels(
                    tool_name=tool.name,
                    tenant_name=metrics_tenant_name,
                    status="json_decode_error",
                ).inc()
            except ValidationError as e:
                log.info(
                    "Validation error running tool (id=%s, name=%s)",
                    request.tool_id,
                    tool.name,
                )
                tool_output = str(e.errors(include_context=False, include_url=False))
                tool_result_message = "Invalid tool input."
                is_error = True
                status = agents_pb2.RemoteToolResponseStatus.INVALID_TOOL_INPUT
                _remote_tool_counter.labels(
                    tool_name=tool.name,
                    tenant_name=metrics_tenant_name,
                    status="validation_error",
                ).inc()
            except ToolAuthenticationError as e:
                log.info(
                    "Authentication error running tool (id=%s, name=%s): %s",
                    request.tool_id,
                    tool.name,
                    str(e),
                )
                tool_output = f"Authentication failed: {e}"
                tool_result_message = "Tool authentication failed."
                is_error = True
                status = agents_pb2.RemoteToolResponseStatus.TOOL_AUTHENTICATION_ERROR
                _remote_tool_counter.labels(
                    tool_name=tool.name,
                    tenant_name=metrics_tenant_name,
                    status="auth_error",
                ).inc()
            except ToolNotAvailableError as e:
                log.info(
                    "Tool not available (id=%s, name=%s): %s",
                    request.tool_id,
                    tool.name,
                    str(e),
                )
                tool_output = f"Tool not available: {e}"
                tool_result_message = "Tool not available."
                is_error = True
                status = agents_pb2.RemoteToolResponseStatus.TOOL_NOT_AVAILABLE
                _remote_tool_counter.labels(
                    tool_name=tool.name,
                    tenant_name=metrics_tenant_name,
                    status="not_available",
                ).inc()
            except Exception as e:
                log.info(
                    "Error running tool (id=%s, name=%s): %s",
                    request.tool_id,
                    tool.name,
                    type(e).__name__,
                )
                tool_output = f"Error running tool: {e}"
                tool_result_message = "Tool execution error."
                is_error = True
                status = agents_pb2.RemoteToolResponseStatus.TOOL_EXECUTION_ERROR
                _remote_tool_counter.labels(
                    tool_name=tool.name,
                    tenant_name=metrics_tenant_name,
                    status="execution_error",
                ).inc()
                _remote_tool_latency.labels(
                    tool_name=tool.name, tenant_name=metrics_tenant_name
                ).observe(time.time() - start_time)
        else:
            log.info(
                f"Tool (id={request.tool_id}, name=<see RequestInsight>) not found, skipping"
            )
            tool_output = f"Tool {tool_name} not found."
            tool_result_message = "Tool not found."
            is_error = True
            status = agents_pb2.RemoteToolResponseStatus.TOOL_NOT_FOUND
            _remote_tool_counter.labels(
                tool_name="unknown",
                tenant_name=metrics_tenant_name,
                status="not_found",
            ).inc()

        run_remote_tool_response = RunRemoteToolResponse(
            tool_output=tool_output,
            tool_result_message=tool_result_message,
            is_error=is_error,
            status=status,
        )
        self.publish_event(
            request_insight_pb2.RIRemoteToolCallResponse(
                run_remote_tool_response=run_remote_tool_response
            ),
            lambda e: e.remote_tool_call_response,
            request_context,
            auth_info,
        )

        _remote_tool_response_size.labels(
            tool_name=tool_name, tenant_name=metrics_tenant_name
        ).observe(len(tool_output.encode("utf-8")))

        _remote_tool_latency.labels(
            tool_name=tool_name, tenant_name=metrics_tenant_name
        ).observe(time.time() - start_time)

        return run_remote_tool_response

    def revoke_tool_access(
        self,
        request: agents_pb2.RevokeToolAccessRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> status_pb2.Status:  # type: ignore
        """Revoke tool access by deactivating OAuth for a tool."""

        tool = self.tools.get(request.tool_id)
        if tool is None:
            log.warning(
                f"Tool (id={request.tool_id}) not found for revoking tool access"
            )
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.NOT_FOUND.value[0],
                message=f"Tool with ID {request.tool_id} not found",
            )

        try:
            return tool.revoke_tool_access(request_context)
        except Exception as e:
            log.error(f"Error revoking access for tool (id={request.tool_id}): {e}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Error revoking access: {str(e)}",
            )

    def test_tool_connection(
        self,
        request: agents_pb2.TestToolConnectionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> status_pb2.Status:  # type: ignore
        """Test connection for a tool."""

        tool = self.tools.get(request.tool_id)
        if tool is None:
            log.warning(
                f"Tool (id={request.tool_id}) not found for test_tool_connection"
            )
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.NOT_FOUND.value[0],
                message=f"Tool with ID {request.tool_id} not found",
            )

        try:
            return tool.test_tool_connection(request_context)
        except Exception as e:
            log.error(f"Error testing connection for tool (id={request.tool_id}): {e}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Error testing connection: {str(e)}",
            )

    def check_tool_safety(
        self,
        request: agents_pb2.CheckToolSafetyRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> agents_pb2.CheckToolSafetyResponse:
        """Check if a tool call is safe with the given input."""
        tool = self.tools.get(request.tool_id)
        if tool is None:
            log.info(f"Tool {request.tool_id} not found")
            return agents_pb2.CheckToolSafetyResponse(is_safe=False)

        try:
            tool_input = json.loads(request.tool_input_json)
            tool_request_context = ToolRequestContext(
                request_context=request_context, auth_info=auth_info
            )
            is_safe = tool.check_tool_call_safe(tool_input, tool_request_context)
            return agents_pb2.CheckToolSafetyResponse(is_safe=is_safe)
        except (json.JSONDecodeError, ValidationError) as e:
            log.info(f"Invalid tool input for safety check: {e}")
            return agents_pb2.CheckToolSafetyResponse(is_safe=False)

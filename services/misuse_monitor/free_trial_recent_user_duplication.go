package main

import (
	"context"
	"fmt"
	"strconv"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var freeTrialRecentUserDuplicationDryRunFlag = featureflags.NewBoolFlag("free_trial_recent_user_duplication_dry_run", true)

type FreeTrialRecentUserDuplicationJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	tenantCache         tenantwatcherclient.TenantCache
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure MisuseMonitorJob implements the Job interface
var _ Job = (*FreeTrialRecentUserDuplicationJob)(nil)

func NewFreeTrialRecentUserDuplicationJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCache,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialRecentUserDuplicationJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialRecentUserDuplicationJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-recent-user-duplicate",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		tenantCache:         tenantCache,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialRecentUserDuplicationJob) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialRecentUserDuplicationJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	suspects, err := m.getSuspects(ctx)
	if err != nil {
		return fmt.Errorf("error getting malicious users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(suspects))

	// Ban the users
	err = m.suspendSuspects(ctx, suspects)
	if err != nil {
		return fmt.Errorf("error banning users: %w", err)
	}

	return nil
}

type freeTrialRecentUserSuspect struct {
	ID              string   `bigquery:"opaque_user_id"`
	TenantID        string   `bigquery:"tenant_id"`
	SessionIDs      []string `bigquery:"session_ids"`
	RequestCount    int      `bigquery:"request_count"`
	InactiveIds     []string `bigquery:"inactive_ids"`
	TrialIds        []string `bigquery:"trial_ids"`
	ProfessionalIds []string `bigquery:"professional_ids"`
	CommunityIds    []string `bigquery:"community_ids"`
	TeamIds         []string `bigquery:"team_ids"`
	EnterpriseIds   []string `bigquery:"enterprise_ids"`
}

func (m *FreeTrialRecentUserDuplicationJob) getSuspects(ctx context.Context) ([]*freeTrialRecentUserSuspect, error) {
	// Construct the query.
	query := m.bqClient.Query(`
	WITH
	-- aggregate by user activity
	activity AS (
		SELECT
			opaque_user_id,
			session_id,
			MIN(first_request_time) as first_request_time,
			MAX(last_request_time) as last_request_time,
			SUM(request_count) as request_count
		FROM request_aggregates_by_session
		GROUP BY 1,2
	),
	-- identify users
	recent_user AS (
		SELECT
			augment_user_id as opaque_user_id,
			tenant_id,
			LOWER(user_email) as email
		FROM add_user_to_tenant
		WHERE time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
	),
	all_user AS (
		SELECT
			id as opaque_user_id,
			tenant_ids[0] as tenant_id,
			LOWER(email) as email,
		FROM user
		WHERE ARRAY_LENGTH(tenant_ids) = 1
	),
	-- determine service tier
	tier AS (
		SELECT
			id as tenant_id,
			CASE
				WHEN tier = 'PROFESSIONAL' AND is_self_serve_team
					THEN 'TEAM'
				ELSE tier
			END AS tier
		FROM tenant
	),
	-- determine subscription type
	sub AS (
		SELECT
			user_id,
			subscription_id,
			CASE
				WHEN orb_status = 'ORB_STATUS_ACTIVE'
					THEN CASE
						WHEN external_plan_id = 'orb_trial_plan'
							THEN 'TRIAL'
						ELSE 'ACTIVE'
					END
				ELSE 'INACTIVE'
			END AS subscription_category
		FROM subscription
	),
	-- build user profile for historic users
	profile AS (
		SELECT
			activity.opaque_user_id,
			all_user.tenant_id,
			activity.session_id,
			activity.request_count,
			activity.first_request_time,
			activity.last_request_time,
			sub.subscription_id,
			all_user.email,
			CASE
				WHEN sub.subscription_category = 'ACTIVE'
					THEN tier.tier
				WHEN sub.subscription_category = 'INACTIVE'
					THEN CASE
						-- Enterprise and teams activity for inactive users always counts as enterprise or teams activity.
						WHEN tier.tier IN ('ENTERPRISE', 'TEAM')
							THEN tier.tier
						ELSE 'INACTIVE'
					END
				ELSE sub.subscription_category
			END as category,
			tier.tier,
			sub.subscription_category
		FROM all_user
		JOIN activity ON activity.opaque_user_id = all_user.opaque_user_id
		JOIN tier ON all_user.tenant_id = tier.tenant_id
		JOIN sub ON all_user.opaque_user_id = sub.user_id
	),
	-- Consider recent users to be suspect.
		suspect AS (
			SELECT
				recent_user.opaque_user_id,
				tenant_id,
				email,
				ARRAY_AGG(DISTINCT activity.session_id) as session_ids,
			FROM recent_user
			JOIN activity ON activity.opaque_user_id = recent_user.opaque_user_id
			GROUP BY 1, 2, 3
		)
	-- Find suspects that have had prior accounts
	SELECT
		suspect.opaque_user_id,
		suspect.tenant_id,
		suspect.session_ids,
		-- Lists of each category of connected users
		SUM(profile.request_count) as request_count,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'INACTIVE' THEN profile.opaque_user_id END IGNORE NULLS) AS inactive_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'TRIAL' THEN profile.opaque_user_id END IGNORE NULLS) AS trial_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'PROFESSIONAL' THEN profile.opaque_user_id END IGNORE NULLS) AS professional_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'COMMUNITY' THEN profile.opaque_user_id END IGNORE NULLS) AS community_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'TEAM' THEN profile.opaque_user_id END IGNORE NULLS) AS team_ids,
		ARRAY_AGG(DISTINCT CASE WHEN profile.category = 'ENTERPRISE' THEN profile.opaque_user_id END IGNORE NULLS) AS enterprise_ids,
	FROM suspect
	JOIN profile
	ON profile.session_id IN UNNEST(suspect.session_ids)
	AND suspect.opaque_user_id != profile.opaque_user_id
	-- Don't match users with same email in case it's our error
	AND suspect.email != profile.email
	GROUP BY 1, 2, 3
	ORDER BY
		-- Deal with heaviest trial users first
		request_count DESC
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var suspects []*freeTrialRecentUserSuspect
	for {
		var row freeTrialRecentUserSuspect
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			suspects = append(suspects, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialDuplicates to ban", len(suspects))
	return suspects, nil
}

func (m *FreeTrialRecentUserDuplicationJob) suspendSuspects(
	ctx context.Context,
	suspects []*freeTrialRecentUserSuspect,
) error {
	dryRun, err := freeTrialRecentUserDuplicationDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(suspects)))

	// Limit users suspended per execution
	suspensionsToIssue := 1000

	sessionId := requestcontext.NewRandomRequestSessionId()
	for _, duplicate := range suspects {

		tenant, err := m.tenantCache.GetTenant(duplicate.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "tenant_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting tenant %s", duplicate.TenantID)
			continue
		}

		// Do the blocking.
		token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
			ctx, duplicate.TenantID, tenant.ShardNamespace, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_RW},
		)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "token_exchange_error", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting token for tenant %s", duplicate.TenantID)
			continue
		}
		requestCtx := requestcontext.New(
			requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

		// Suspensions are issued for duplicate inactive, trial, professional and community accounts only.
		// Ignore duplicate teams and enterprise accounts if those are the only duplicates.
		if len(duplicate.InactiveIds) == 0 && len(duplicate.TrialIds) == 0 && len(duplicate.ProfessionalIds) == 0 && len(duplicate.CommunityIds) == 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "ineligible_duplicate", strconv.FormatBool(dryRun)).Inc()
			log.Info().Msgf("Skipping duplicate user %s in tenant %s, no inactive, trial, professional or community duplicates", duplicate.ID, duplicate.TenantID)
			continue
		}

		// Check if the user is exempt or already suspended for free trial abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, duplicate.ID, &duplicate.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", duplicate.ID, duplicate.TenantID, err)
			continue
		}
		if userObj.SuspensionExempt {
			log.Info().Msgf("User %s is suspension exempt in tenant %s", duplicate.ID, duplicate.TenantID)
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_exempt", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse in tenant %s", duplicate.ID, duplicate.TenantID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		evidence := fmt.Sprintf("Duplicate accounts with free trial: Shared session IDs: %v inactive: %d trial: %d professional: %d community: %d team: %d enterprise: %d",
			duplicate.SessionIDs, len(duplicate.InactiveIds), len(duplicate.TrialIds), len(duplicate.ProfessionalIds), len(duplicate.CommunityIds), len(duplicate.TeamIds), len(duplicate.EnterpriseIds))
		log.Info().Msgf("Misuse monitor detected free trial duplication by user %s in tenant %s. %s",
			duplicate.ID, duplicate.TenantID, evidence)
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue free trial abuse suspension
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, duplicate.ID, duplicate.TenantID, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, evidence)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", duplicate.ID, duplicate.TenantID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s in tenant %s", suspensionID, duplicate.ID, duplicate.TenantID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}

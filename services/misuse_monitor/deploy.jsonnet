local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';

function(cloud, env, namespace, namespace_config)
  local appName = 'misuse-monitor';
  local shortAppName = 'misuse-mon';

  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local serverCert = certLib.createCentralServerCert(
    name='%s-server-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='server-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName, cloud=cloud, env=env, namespace=namespace, iam=true, overridePrefix=shortAppName
  );
  local datasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(
    env=env, namespace=namespace, cloud=cloud, appName=appName
  );

  // Determine which bucket to use based on environment
  local bucketName = if env == 'DEV' then 'augment-data-dev' else 'augment-data';

  local config = {
    promPort: 9090,
    clientMtls: clientCert.config,
    serverMtls: serverCert.config,
    namespace: namespace,
    projectId: cloudInfo[cloud].projectId,
    datasetName: datasetLib.datasetGcp,
    authEndpoint: '%s:50051' % endpoints.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    tokenExchangeEndpoint: endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    tenantWatcherEndpoint: endpoints.getTenantWatcherGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    featureFlagsSdkKeyPath: dynamicFeatureFlags.secretsFilePath,
    dynamicFeatureFlagsEndpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    gCSBucketName: bucketName,
    port: 50051,
    jobs: [
      {
        jobName: 'api-misuse',
        executionInterval: '15m',
      },
      {
        jobName: 'community-duplicate',
        executionInterval: '10m',
      },
      {
        jobName: 'free-trial-duplicate',
        executionInterval: '10m',
      },
      {
        jobName: 'free-trial-recent-user-duplicate',
        executionInterval: '10m',
      },
      {
        jobName: 'free-trial-feature-vector-duplicate',
        executionInterval: '15m',
      },
      {
        jobName: 'free-trial-feature-vector-duplicate-v2',
        executionInterval: '15m',
      },
    ],
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local bigqueryAccess = [
    // Access to the dataset.
    bigqueryLib.datasetAccess(
      namespace,
      appName,
      datasetLib.cloudIdentityGroup,
      datasetLib.dataNamespace,
      serviceAccount.serviceAccountGcpEmailAddress,
    ),
    // Permission to run jobs (i.e., queries).
    gcpLib.grantAccess(
      name='%s-bigquery-job-policy' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'Project',
        external: 'project/%s' % cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/bigquery.jobUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ]
    ),
    // PII access. Emails are useful for logging.
    gcpLib.grantAccess(
      name='%s-pii-finegrained-reader-access' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'DataCatalogPolicyTag',
        external: bigqueryLib.dataAccessPolicyTag(cloud, env, 'pii'),
      },
      bindings=[
        {
          role: 'roles/datacatalog.categoryFineGrainedReader',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    ),
    // GCS bucket access for banned users list
    gcpLib.grantAccess(
      name='%s-gcs-bucket-access' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'StorageBucket',
        external: bucketName,
      },
      bindings=[
        {
          role: 'roles/storage.objectViewer',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
      abandon=true,
    ),
  ];

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local container =
    {
      name: appName,
      target: {
        name: '//services/misuse_monitor:image',
        dst: appName,
      },
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        serverCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      args: [
        '--config',
        '/config/config.json',
      ],
      env: lib.flatten([
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
        dynamicFeatureFlags.env,
      ]),
      resources: {
        limits: {
          cpu: 0.5,
          memory: '512Mi',
        },
      },
      readinessProbe: grpcLib.grpcHealthCheck(
        appName + '-svc',
        tls=true,
        serverCerts=serverCert.volumeMountDef.mountPath
      ) + {
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: grpcLib.grpcHealthCheck(
        appName + '-svc',
        tls=true,
        serverCerts=serverCert.volumeMountDef.mountPath
      ) + {
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
    };

  local pod = {
    serviceAccountName: serviceAccount.name,
    tolerations: tolerations,
    affinity: affinity,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      clientCert.podVolumeDef,
      serverCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: '%s-svc' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      ports: [
        {
          name: 'grpc',
          port: 50051,
          targetPort: 50051,
        },
        {
          name: 'metrics',
          port: config.promPort,
          targetPort: config.promPort,
        },
      ],
      selector: {
        app: appName,
      },
    },
  };

  lib.flatten([
    configMap.objects,
    clientCert.objects,
    serverCert.objects,
    serviceAccount.objects,
    bigqueryAccess,
    deployment,
    service,
    dynamicFeatureFlags.k8s_objects,
  ])

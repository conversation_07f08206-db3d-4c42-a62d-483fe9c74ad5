// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/auth/central/server/auth_entities.proto (package auth_entities, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum auth_entities.CustomerUiRole
 */
export declare enum CustomerUiRole {
  /**
   * @generated from enum value: UNKNOWN_CUSTOMER_UI_ROLE = 0;
   */
  UNKNOWN_CUSTOMER_UI_ROLE = 0,

  /**
   * @generated from enum value: ADMIN = 1;
   */
  ADMIN = 1,
}

/**
 * @generated from enum auth_entities.UserTier
 */
export declare enum UserTier {
  /**
   * @generated from enum value: UNKNOWN_TIER = 0;
   */
  UNKNOWN_TIER = 0,

  /**
   * @generated from enum value: COMMUNITY = 1;
   */
  COMMUNITY = 1,

  /**
   * @generated from enum value: PROFESSIONAL = 2;
   */
  PROFESSIONAL = 2,
}

/**
 * @generated from enum auth_entities.BillingMethod
 */
export declare enum BillingMethod {
  /**
   * @generated from enum value: BILLING_METHOD_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: BILLING_METHOD_STRIPE = 1;
   */
  STRIPE = 1,

  /**
   * @generated from enum value: BILLING_METHOD_ORB = 2;
   */
  ORB = 2,

  /**
   * @generated from enum value: BILLING_METHOD_MIGRATING_TO_ORB = 3 [deprecated = true];
   * @deprecated
   */
  MIGRATING_TO_ORB = 3,

  /**
   * @generated from enum value: BILLING_METHOD_ORB_WITH_ENDING_STRIPE = 4 [deprecated = true];
   * @deprecated
   */
  ORB_WITH_ENDING_STRIPE = 4,
}

/**
 * @generated from enum auth_entities.UserSuspensionType
 */
export declare enum UserSuspensionType {
  /**
   * @generated from enum value: USER_SUSPENSION_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: USER_SUSPENSION_TYPE_API_ABUSE = 1;
   */
  API_ABUSE = 1,

  /**
   * @generated from enum value: USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE = 2;
   */
  FREE_TRIAL_ABUSE = 2,

  /**
   * @generated from enum value: USER_SUSPENSION_TYPE_COMMUNITY_ABUSE = 3;
   */
  COMMUNITY_ABUSE = 3,
}

/**
 * @generated from enum auth_entities.PromotionStatus
 */
export declare enum PromotionStatus {
  /**
   * @generated from enum value: PROMOTION_STATUS_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PROMOTION_STATUS_ELIGIBLE = 1;
   */
  ELIGIBLE = 1,

  /**
   * @generated from enum value: PROMOTION_STATUS_INELIGIBLE = 2;
   */
  INELIGIBLE = 2,

  /**
   * @generated from enum value: PROMOTION_STATUS_ENROLLED = 3;
   */
  ENROLLED = 3,
}

/**
 * @generated from message auth_entities.UserSuspension
 */
export declare class UserSuspension extends Message<UserSuspension> {
  /**
   * @generated from field: string suspension_id = 1;
   */
  suspensionId: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_time = 2;
   */
  createdTime?: Timestamp;

  /**
   * @generated from field: auth_entities.UserSuspensionType suspension_type = 3;
   */
  suspensionType: UserSuspensionType;

  /**
   * @generated from field: string evidence = 4;
   */
  evidence: string;

  constructor(data?: PartialMessage<UserSuspension>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.UserSuspension";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserSuspension;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserSuspension;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserSuspension;

  static equals(a: UserSuspension | PlainMessage<UserSuspension> | undefined, b: UserSuspension | PlainMessage<UserSuspension> | undefined): boolean;
}

/**
 * @generated from message auth_entities.User
 */
export declare class User extends Message<User> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: uint64 nonce = 3;
   */
  nonce: bigint;

  /**
   * @generated from field: repeated string tenants = 4;
   */
  tenants: string[];

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp in_usa = 6;
   */
  inUsa?: Timestamp;

  /**
   * @generated from field: string stripe_customer_id = 7;
   */
  stripeCustomerId: string;

  /**
   * @generated from field: optional auth_entities.User.TierChangeInfo tier_change = 8;
   */
  tierChange?: User_TierChangeInfo;

  /**
   * @generated from field: optional string subscription_id = 9;
   */
  subscriptionId?: string;

  /**
   * @generated from field: bool blocked = 10;
   */
  blocked: boolean;

  /**
   * @generated from field: repeated string idp_user_ids = 11;
   */
  idpUserIds: string[];

  /**
   * @generated from field: string orb_customer_id = 12;
   */
  orbCustomerId: string;

  /**
   * @generated from field: auth_entities.BillingMethod billing_method = 14;
   */
  billingMethod: BillingMethod;

  /**
   * @generated from field: string orb_subscription_id = 15;
   */
  orbSubscriptionId: string;

  /**
   * @generated from field: string subscription_creation_id = 16 [deprecated = true];
   * @deprecated
   */
  subscriptionCreationId: string;

  /**
   * @generated from field: repeated auth_entities.UserSuspension suspensions = 17;
   */
  suspensions: UserSuspension[];

  /**
   * @generated from field: auth_entities.User.SubscriptionCreationInfo subscription_creation_info = 18;
   */
  subscriptionCreationInfo?: User_SubscriptionCreationInfo;

  /**
   * @generated from field: bool suspension_exempt = 19;
   */
  suspensionExempt: boolean;

  /**
   * @generated from field: auth_entities.User.DeletionState deletion_state = 20;
   */
  deletionState: User_DeletionState;

  /**
   * @generated from field: repeated auth_entities.UserPromotion promotions = 21;
   */
  promotions: UserPromotion[];

  /**
   * @generated from field: string given_name = 22;
   */
  givenName: string;

  /**
   * @generated from field: string family_name = 23;
   */
  familyName: string;

  /**
   * @generated from field: optional auth_entities.PendingChange pending_change = 24;
   */
  pendingChange?: PendingChange;

  constructor(data?: PartialMessage<User>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.User";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): User;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): User;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): User;

  static equals(a: User | PlainMessage<User> | undefined, b: User | PlainMessage<User> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.User.DeletionState
 */
export declare enum User_DeletionState {
  /**
   * @generated from enum value: NOT_DELETED = 0;
   */
  NOT_DELETED = 0,

  /**
   * @generated from enum value: GDPR_CCPA_DELETED = 1;
   */
  GDPR_CCPA_DELETED = 1,
}

/**
 * @generated from message auth_entities.User.TierChangeInfo
 */
export declare class User_TierChangeInfo extends Message<User_TierChangeInfo> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: auth_entities.UserTier target_tier = 2;
   */
  targetTier: UserTier;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 3;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 4;
   */
  updatedAt?: Timestamp;

  constructor(data?: PartialMessage<User_TierChangeInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.User.TierChangeInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): User_TierChangeInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): User_TierChangeInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): User_TierChangeInfo;

  static equals(a: User_TierChangeInfo | PlainMessage<User_TierChangeInfo> | undefined, b: User_TierChangeInfo | PlainMessage<User_TierChangeInfo> | undefined): boolean;
}

/**
 * @generated from message auth_entities.User.SubscriptionCreationInfo
 */
export declare class User_SubscriptionCreationInfo extends Message<User_SubscriptionCreationInfo> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: auth_entities.User.SubscriptionCreationInfo.Status status = 2;
   */
  status: User_SubscriptionCreationInfo_Status;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 3;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 4;
   */
  updatedAt?: Timestamp;

  constructor(data?: PartialMessage<User_SubscriptionCreationInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.User.SubscriptionCreationInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): User_SubscriptionCreationInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): User_SubscriptionCreationInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): User_SubscriptionCreationInfo;

  static equals(a: User_SubscriptionCreationInfo | PlainMessage<User_SubscriptionCreationInfo> | undefined, b: User_SubscriptionCreationInfo | PlainMessage<User_SubscriptionCreationInfo> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.User.SubscriptionCreationInfo.Status
 */
export declare enum User_SubscriptionCreationInfo_Status {
  /**
   * @generated from enum value: SUCCESS = 0;
   */
  SUCCESS = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,
}

/**
 * @generated from message auth_entities.UserPromotion
 */
export declare class UserPromotion extends Message<UserPromotion> {
  /**
   * @generated from field: string promotion_name = 1;
   */
  promotionName: string;

  /**
   * @generated from field: auth_entities.PromotionStatus status = 2;
   */
  status: PromotionStatus;

  /**
   * @generated from field: google.protobuf.Timestamp enrolled_at = 3;
   */
  enrolledAt?: Timestamp;

  /**
   * @generated from field: int32 enrollment_attempts = 4;
   */
  enrollmentAttempts: number;

  constructor(data?: PartialMessage<UserPromotion>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.UserPromotion";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserPromotion;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserPromotion;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserPromotion;

  static equals(a: UserPromotion | PlainMessage<UserPromotion> | undefined, b: UserPromotion | PlainMessage<UserPromotion> | undefined): boolean;
}

/**
 * @generated from message auth_entities.UserTenantMapping
 */
export declare class UserTenantMapping extends Message<UserTenantMapping> {
  /**
   * @generated from field: string tenant = 1;
   */
  tenant: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * @generated from field: repeated auth_entities.CustomerUiRole customer_ui_roles = 3;
   */
  customerUiRoles: CustomerUiRole[];

  constructor(data?: PartialMessage<UserTenantMapping>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.UserTenantMapping";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserTenantMapping;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserTenantMapping;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserTenantMapping;

  static equals(a: UserTenantMapping | PlainMessage<UserTenantMapping> | undefined, b: UserTenantMapping | PlainMessage<UserTenantMapping> | undefined): boolean;
}

/**
 * @generated from message auth_entities.IdpUserMapping
 */
export declare class IdpUserMapping extends Message<IdpUserMapping> {
  /**
   * @generated from field: string idp_user_id = 1;
   */
  idpUserId: string;

  /**
   * @generated from field: string augment_user_id = 2;
   */
  augmentUserId: string;

  constructor(data?: PartialMessage<IdpUserMapping>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.IdpUserMapping";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IdpUserMapping;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IdpUserMapping;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IdpUserMapping;

  static equals(a: IdpUserMapping | PlainMessage<IdpUserMapping> | undefined, b: IdpUserMapping | PlainMessage<IdpUserMapping> | undefined): boolean;
}

/**
 * @generated from message auth_entities.TokenHash
 */
export declare class TokenHash extends Message<TokenHash> {
  /**
   * @generated from field: string hash = 1;
   */
  hash: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: string idp_user_id = 3;
   */
  idpUserId: string;

  /**
   * @generated from field: string email_address = 4;
   */
  emailAddress: string;

  /**
   * @generated from field: string augment_user_id = 5;
   */
  augmentUserId: string;

  /**
   * @generated from field: google.protobuf.Timestamp creation_time = 6;
   */
  creationTime?: Timestamp;

  /**
   * @generated from field: int64 expiration_time_seconds = 7;
   */
  expirationTimeSeconds: bigint;

  constructor(data?: PartialMessage<TokenHash>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.TokenHash";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TokenHash;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TokenHash;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TokenHash;

  static equals(a: TokenHash | PlainMessage<TokenHash> | undefined, b: TokenHash | PlainMessage<TokenHash> | undefined): boolean;
}

/**
 * @generated from message auth_entities.Code
 */
export declare class Code extends Message<Code> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  /**
   * @generated from field: string idp_user_id = 2;
   */
  idpUserId: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: string client_id = 4;
   */
  clientId: string;

  /**
   * @generated from field: string tenant_id = 5;
   */
  tenantId: string;

  /**
   * @generated from field: string redirect_uri = 6;
   */
  redirectUri: string;

  /**
   * @generated from field: string code_challenge = 7;
   */
  codeChallenge: string;

  /**
   * @generated from field: bool is_used = 8;
   */
  isUsed: boolean;

  /**
   * @generated from field: int64 creation_time_seconds = 9;
   */
  creationTimeSeconds: bigint;

  /**
   * @generated from field: string augment_user_id = 10;
   */
  augmentUserId: string;

  constructor(data?: PartialMessage<Code>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.Code";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Code;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Code;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Code;

  static equals(a: Code | PlainMessage<Code> | undefined, b: Code | PlainMessage<Code> | undefined): boolean;
}

/**
 * @generated from message auth_entities.TermsApproval
 */
export declare class TermsApproval extends Message<TermsApproval> {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: bool approved = 2;
   */
  approved: boolean;

  /**
   * @generated from field: string revision = 3;
   */
  revision: string;

  constructor(data?: PartialMessage<TermsApproval>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.TermsApproval";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TermsApproval;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TermsApproval;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TermsApproval;

  static equals(a: TermsApproval | PlainMessage<TermsApproval> | undefined, b: TermsApproval | PlainMessage<TermsApproval> | undefined): boolean;
}

/**
 * @generated from message auth_entities.SignupLimiterState
 */
export declare class SignupLimiterState extends Message<SignupLimiterState> {
  /**
   * @generated from field: float credits_available = 1;
   */
  creditsAvailable: number;

  /**
   * @generated from field: int64 last_update_time_seconds = 2;
   */
  lastUpdateTimeSeconds: bigint;

  /**
   * @generated from field: int64 max_burst = 3;
   */
  maxBurst: bigint;

  constructor(data?: PartialMessage<SignupLimiterState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.SignupLimiterState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SignupLimiterState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SignupLimiterState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SignupLimiterState;

  static equals(a: SignupLimiterState | PlainMessage<SignupLimiterState> | undefined, b: SignupLimiterState | PlainMessage<SignupLimiterState> | undefined): boolean;
}

/**
 * @generated from message auth_entities.UserId
 */
export declare class UserId extends Message<UserId> {
  /**
   * @generated from field: auth_entities.UserId.UserIdType user_id_type = 1;
   */
  userIdType: UserId_UserIdType;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  constructor(data?: PartialMessage<UserId>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.UserId";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserId;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserId;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserId;

  static equals(a: UserId | PlainMessage<UserId> | undefined, b: UserId | PlainMessage<UserId> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.UserId.UserIdType
 */
export declare enum UserId_UserIdType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: AUGMENT = 1;
   */
  AUGMENT = 1,

  /**
   * @generated from enum value: API_TOKEN = 2;
   */
  API_TOKEN = 2,

  /**
   * @generated from enum value: SLACK = 3;
   */
  SLACK = 3,

  /**
   * @generated from enum value: INTERNAL_IAP = 4;
   */
  INTERNAL_IAP = 4,
}

/**
 * @generated from message auth_entities.TenantCreation
 */
export declare class TenantCreation extends Message<TenantCreation> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 2;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: auth_entities.TenantCreation.Status status = 3;
   */
  status: TenantCreation_Status;

  /**
   * @generated from field: string tenant_id = 4;
   */
  tenantId: string;

  constructor(data?: PartialMessage<TenantCreation>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.TenantCreation";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantCreation;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantCreation;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantCreation;

  static equals(a: TenantCreation | PlainMessage<TenantCreation> | undefined, b: TenantCreation | PlainMessage<TenantCreation> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.TenantCreation.Status
 */
export declare enum TenantCreation_Status {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: SUCCESS = 2;
   */
  SUCCESS = 2,

  /**
   * @generated from enum value: ERROR = 3;
   */
  ERROR = 3,
}

/**
 * @generated from message auth_entities.TenantInvitation
 */
export declare class TenantInvitation extends Message<TenantInvitation> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 2;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string invitee_email = 3;
   */
  inviteeEmail: string;

  /**
   * @generated from field: string tenant_id = 4;
   */
  tenantId: string;

  /**
   * @generated from field: string inviter_user_id = 5;
   */
  inviterUserId: string;

  /**
   * @generated from field: auth_entities.TenantInvitation.Status status = 6;
   */
  status: TenantInvitation_Status;

  /**
   * @generated from field: string inviter_email = 7;
   */
  inviterEmail: string;

  constructor(data?: PartialMessage<TenantInvitation>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.TenantInvitation";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantInvitation;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantInvitation;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantInvitation;

  static equals(a: TenantInvitation | PlainMessage<TenantInvitation> | undefined, b: TenantInvitation | PlainMessage<TenantInvitation> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.TenantInvitation.Status
 */
export declare enum TenantInvitation_Status {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: ACCEPTED = 2;
   */
  ACCEPTED = 2,

  /**
   * @generated from enum value: DECLINED = 3;
   */
  DECLINED = 3,
}

/**
 * @generated from message auth_entities.TenantSubscriptionMapping
 */
export declare class TenantSubscriptionMapping extends Message<TenantSubscriptionMapping> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string stripe_subscription_id = 2;
   */
  stripeSubscriptionId: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 3;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string stripe_customer_id = 4;
   */
  stripeCustomerId: string;

  /**
   * @generated from field: string orb_customer_id = 5;
   */
  orbCustomerId: string;

  /**
   * @generated from field: auth_entities.BillingMethod billing_method = 6;
   */
  billingMethod: BillingMethod;

  /**
   * @generated from field: string orb_subscription_id = 7;
   */
  orbSubscriptionId: string;

  /**
   * @generated from field: optional auth_entities.TenantSubscriptionMapping.PlanChangeInfo plan_change = 24;
   */
  planChange?: TenantSubscriptionMapping_PlanChangeInfo;

  /**
   * @generated from field: int32 trial_credits_awarded_count = 8;
   */
  trialCreditsAwardedCount: number;

  /**
   * @generated from field: optional auth_entities.PendingChange pending_change = 9;
   */
  pendingChange?: PendingChange;

  constructor(data?: PartialMessage<TenantSubscriptionMapping>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.TenantSubscriptionMapping";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantSubscriptionMapping;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantSubscriptionMapping;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantSubscriptionMapping;

  static equals(a: TenantSubscriptionMapping | PlainMessage<TenantSubscriptionMapping> | undefined, b: TenantSubscriptionMapping | PlainMessage<TenantSubscriptionMapping> | undefined): boolean;
}

/**
 * @generated from message auth_entities.TenantSubscriptionMapping.PlanChangeInfo
 */
export declare class TenantSubscriptionMapping_PlanChangeInfo extends Message<TenantSubscriptionMapping_PlanChangeInfo> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string target_orb_plan_id = 2;
   */
  targetOrbPlanId: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 3;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 4;
   */
  updatedAt?: Timestamp;

  constructor(data?: PartialMessage<TenantSubscriptionMapping_PlanChangeInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.TenantSubscriptionMapping.PlanChangeInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantSubscriptionMapping_PlanChangeInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantSubscriptionMapping_PlanChangeInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantSubscriptionMapping_PlanChangeInfo;

  static equals(a: TenantSubscriptionMapping_PlanChangeInfo | PlainMessage<TenantSubscriptionMapping_PlanChangeInfo> | undefined, b: TenantSubscriptionMapping_PlanChangeInfo | PlainMessage<TenantSubscriptionMapping_PlanChangeInfo> | undefined): boolean;
}

/**
 * @generated from message auth_entities.Subscription
 */
export declare class Subscription extends Message<Subscription> {
  /**
   * @generated from field: string subscription_id = 1;
   */
  subscriptionId: string;

  /**
   * @generated from field: string stripe_customer_id = 2;
   */
  stripeCustomerId: string;

  /**
   * @generated from field: string price_id = 3;
   */
  priceId: string;

  /**
   * @generated from field: auth_entities.Subscription.Status status = 4;
   */
  status: Subscription_Status;

  /**
   * @generated from field: int32 seats = 5;
   */
  seats: number;

  /**
   * @generated from field: google.protobuf.Timestamp start_date = 6;
   */
  startDate?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp trial_end = 7;
   */
  trialEnd?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_date = 8;
   */
  endDate?: Timestamp;

  /**
   * @generated from field: bool cancel_at_period_end = 9;
   */
  cancelAtPeriodEnd: boolean;

  /**
   * @generated from field: bool has_payment_method = 10;
   */
  hasPaymentMethod: boolean;

  /**
   * @generated from oneof auth_entities.Subscription.owner
   */
  owner: {
    /**
     * @generated from field: string tenant_id = 11;
     */
    value: string;
    case: "tenantId";
  } | {
    /**
     * @generated from field: string user_id = 12;
     */
    value: string;
    case: "userId";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 13;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 14;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: string orb_customer_id = 15;
   */
  orbCustomerId: string;

  /**
   * @generated from field: auth_entities.Subscription.OrbStatus orb_status = 16;
   */
  orbStatus: Subscription_OrbStatus;

  /**
   * @generated from field: bool has_unpaid_invoice = 17;
   */
  hasUnpaidInvoice: boolean;

  /**
   * @generated from field: string external_plan_id = 18;
   */
  externalPlanId: string;

  /**
   * @generated from field: auth_entities.BillingMethod billing_method = 19;
   */
  billingMethod: BillingMethod;

  /**
   * @generated from field: string seats_id = 20;
   */
  seatsId: string;

  /**
   * @generated from field: string included_credits_id = 21;
   */
  includedCreditsId: string;

  /**
   * @generated from field: double credits_per_month = 22;
   */
  creditsPerMonth: number;

  /**
   * @generated from field: bool usage_balance_depleted = 23;
   */
  usageBalanceDepleted: boolean;

  /**
   * @generated from field: optional string subscription_change_id = 24;
   */
  subscriptionChangeId?: string;

  /**
   * @generated from field: bool cancelled_due_to_payment_failure = 25;
   */
  cancelledDueToPaymentFailure: boolean;

  constructor(data?: PartialMessage<Subscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.Subscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Subscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Subscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Subscription;

  static equals(a: Subscription | PlainMessage<Subscription> | undefined, b: Subscription | PlainMessage<Subscription> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.Subscription.Status
 */
export declare enum Subscription_Status {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: INCOMPLETE = 1;
   */
  INCOMPLETE = 1,

  /**
   * @generated from enum value: INCOMPLETE_EXPIRED = 2;
   */
  INCOMPLETE_EXPIRED = 2,

  /**
   * @generated from enum value: TRIALING = 3;
   */
  TRIALING = 3,

  /**
   * @generated from enum value: ACTIVE = 4;
   */
  ACTIVE = 4,

  /**
   * @generated from enum value: PAST_DUE = 5;
   */
  PAST_DUE = 5,

  /**
   * @generated from enum value: CANCELED = 6;
   */
  CANCELED = 6,

  /**
   * @generated from enum value: UNPAID = 7;
   */
  UNPAID = 7,

  /**
   * @generated from enum value: PAUSED = 8;
   */
  PAUSED = 8,
}

/**
 * @generated from enum auth_entities.Subscription.OrbStatus
 */
export declare enum Subscription_OrbStatus {
  /**
   * @generated from enum value: ORB_STATUS_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ORB_STATUS_UPCOMING = 1;
   */
  UPCOMING = 1,

  /**
   * @generated from enum value: ORB_STATUS_ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * @generated from enum value: ORB_STATUS_ENDED = 3;
   */
  ENDED = 3,
}

/**
 * @generated from message auth_entities.InvitationResolution
 */
export declare class InvitationResolution extends Message<InvitationResolution> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 2;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: auth_entities.InvitationResolution.Status status = 3;
   */
  status: InvitationResolution_Status;

  constructor(data?: PartialMessage<InvitationResolution>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.InvitationResolution";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InvitationResolution;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InvitationResolution;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InvitationResolution;

  static equals(a: InvitationResolution | PlainMessage<InvitationResolution> | undefined, b: InvitationResolution | PlainMessage<InvitationResolution> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.InvitationResolution.Status
 */
export declare enum InvitationResolution_Status {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: SUCCESS = 2;
   */
  SUCCESS = 2,

  /**
   * @generated from enum value: ERROR = 3;
   */
  ERROR = 3,
}

/**
 * @generated from message auth_entities.PlanChange
 */
export declare class PlanChange extends Message<PlanChange> {
  /**
   * @generated from field: auth_entities.PlanChange.Status status = 1;
   */
  status: PlanChange_Status;

  /**
   * @generated from field: string target_plan_id = 2;
   */
  targetPlanId: string;

  /**
   * @generated from field: string checkout_session_url = 3;
   */
  checkoutSessionUrl: string;

  /**
   * @generated from field: string total_cost = 4;
   */
  totalCost: string;

  /**
   * @generated from field: string orb_pending_change_id = 5;
   */
  orbPendingChangeId: string;

  constructor(data?: PartialMessage<PlanChange>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.PlanChange";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PlanChange;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PlanChange;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PlanChange;

  static equals(a: PlanChange | PlainMessage<PlanChange> | undefined, b: PlanChange | PlainMessage<PlanChange> | undefined): boolean;
}

/**
 * @generated from enum auth_entities.PlanChange.Status
 */
export declare enum PlanChange_Status {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STAGED = 1;
   */
  STAGED = 1,

  /**
   * @generated from enum value: PENDING = 2;
   */
  PENDING = 2,

  /**
   * @generated from enum value: SUCCESS = 3;
   */
  SUCCESS = 3,

  /**
   * @generated from enum value: PAYMENT_FAILED = 4;
   */
  PAYMENT_FAILED = 4,
}

/**
 * @generated from message auth_entities.PendingChange
 */
export declare class PendingChange extends Message<PendingChange> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 2;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 3;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from oneof auth_entities.PendingChange.change
   */
  change: {
    /**
     * @generated from field: auth_entities.PlanChange plan_change = 4;
     */
    value: PlanChange;
    case: "planChange";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<PendingChange>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth_entities.PendingChange";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PendingChange;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PendingChange;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PendingChange;

  static equals(a: PendingChange | PlainMessage<PendingChange> | undefined, b: PendingChange | PlainMessage<PendingChange> | undefined): boolean;
}


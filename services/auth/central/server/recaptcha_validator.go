package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	recaptchaenterprise "cloud.google.com/go/recaptchaenterprise/v2/apiv1"
	"cloud.google.com/go/recaptchaenterprise/v2/apiv1/recaptchaenterprisepb"
	gax "github.com/googleapis/gax-go/v2"
	"github.com/rs/zerolog/log"
)

// RecaptchaClient interface (for dependency injection and testing)
type RecaptchaClient interface {
	CreateAssessment(ctx context.Context, req *recaptchaenterprisepb.CreateAssessmentRequest, opts ...gax.CallOption) (*recaptchaenterprisepb.Assessment, error)
	Close() error
}

type RecaptchaValidator interface {
	Allow(ctx context.Context, token, action, userIP, userAgent string, threshold float64, userEmail string) bool
	Close() error
}

type RecaptchaValidatorImpl struct {
	client    RecaptchaClient
	projectID string
	siteKey   string
}

// NewRecaptchaValidator creates a new reCAPTCHA validator
func NewRecaptchaValidator(ctx context.Context, projectID, keysPath string) (*RecaptchaValidatorImpl, error) {
	// Read and parse the keys file (matches Python behavior)
	keysData, err := os.ReadFile(keysPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read reCAPTCHA keys file: %w", err)
	}

	var keysConfig struct {
		SiteKey string `json:"site_key"`
	}
	if err := json.Unmarshal(keysData, &keysConfig); err != nil {
		return nil, fmt.Errorf("failed to parse reCAPTCHA keys JSON: %w", err)
	}

	client, err := recaptchaenterprise.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create reCAPTCHA Enterprise client: %w", err)
	}

	return &RecaptchaValidatorImpl{
		client:    client,
		projectID: projectID,
		siteKey:   keysConfig.SiteKey,
	}, nil
}

// Allow validates a reCAPTCHA token and returns whether to allow the request
func (v *RecaptchaValidatorImpl) Allow(ctx context.Context, token, action, userIP, userAgent string, threshold float64, userEmail string) bool {
	// Bypass validation if threshold is negative
	if threshold < 0.0 {
		return true
	}

	score, err := v.createAssessment(ctx, token, action, userIP, userAgent, userEmail)
	if err != nil {
		log.Error().Err(err).Msg("reCAPTCHA assessment failed")
		return false
	}

	if score == nil {
		// Fail closed unless threshold is 0.0 (log-only mode)
		return threshold == 0.0
	}

	return *score >= threshold
}

// createAssessment creates a reCAPTCHA assessment and returns the score
func (v *RecaptchaValidatorImpl) createAssessment(ctx context.Context, token, action, userIP, userAgent, userEmail string) (*float64, error) {
	if token == "" {
		return nil, fmt.Errorf("reCAPTCHA token is empty")
	}

	event := &recaptchaenterprisepb.Event{
		Token:         token,
		SiteKey:       v.siteKey,
		UserIpAddress: userIP,
		UserAgent:     userAgent,
	}

	if userEmail != "" {
		event.UserInfo = &recaptchaenterprisepb.UserInfo{
			AccountId: userEmail,
		}
	}

	req := &recaptchaenterprisepb.CreateAssessmentRequest{
		Parent: fmt.Sprintf("projects/%s", v.projectID),
		Assessment: &recaptchaenterprisepb.Assessment{
			Event: event,
		},
	}

	assessment, err := v.client.CreateAssessment(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create reCAPTCHA assessment: %w", err)
	}

	if assessment.TokenProperties == nil || !assessment.TokenProperties.Valid {
		log.Warn().Msg("reCAPTCHA token is invalid")
		return nil, nil
	}

	if action != "" && assessment.TokenProperties.Action != action {
		log.Warn().
			Str("expected", action).
			Str("actual", assessment.TokenProperties.Action).
			Msg("reCAPTCHA action mismatch")
		return nil, nil
	}

	if assessment.RiskAnalysis != nil {
		score := float64(assessment.RiskAnalysis.Score)
		log.Info().
			Str("action", action).
			Float64("score", score).
			Msg("reCAPTCHA validation successful")
		return &score, nil
	}

	return nil, nil
}

func (v *RecaptchaValidatorImpl) Close() error {
	return v.client.Close()
}

package main

import (
	"bytes"
	"fmt"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// compareTimestamps compares two timestamps ignoring internal fields like sizeCache
func compareTimestamps(t *testing.T, expected, actual *timestamppb.Timestamp) {
	if expected == nil && actual == nil {
		return
	}
	assert.NotNil(t, expected)
	assert.NotNil(t, actual)
	assert.Equal(t, expected.GetSeconds(), actual.GetSeconds())
	assert.Equal(t, expected.GetNanos(), actual.GetNanos())
}

func caseDaoFactory(t *testing.T, bigtableFixture *BigtableFixture) {
	factory := NewDAOFactory(bigtableFixture.Table)

	t.Run("creates appropriate DAOs", func(t *testing.T) {
		assert.IsType(t, &UserDAO{}, factory.GetUserDAO())
		assert.IsType(t, &UserTenantMappingDAO{}, factory.GetUserTenantMappingDAO("test-tenant"))
		assert.IsType(t, &TokenHashDAO{}, factory.GetTokenHashDAO())
		assert.IsType(t, &CodeDAO{}, factory.GetCodeDAO())
		assert.IsType(t, &TermsApprovalDAO{}, factory.GetTermsDAO())
		assert.IsType(t, &TenantInvitationDAO{}, factory.GetTenantInvitationDAO("test-tenant"))
	})
}

func caseUserDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewUserDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("not found returns nil", func(t *testing.T) {
		user, err := dao.Get(ctx, "non-existent-id")
		assert.NoError(t, err)
		assert.Nil(t, user)
	})

	t.Run("find all empty", func(t *testing.T) {
		count := 0
		err := dao.FindAll(ctx, func(_ *auth_entities.User) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 0)
	})

	t.Run("create and get", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		user := &auth_entities.User{
			Id:    "test-user-1",
			Email: "<EMAIL>",
		}

		created, err := dao.Create(ctx, user)
		assert.NoError(t, err)
		assert.Equal(t, user.GetId(), created.GetId())
		assert.Equal(t, user.GetEmail(), created.GetEmail())

		retrieved, err := dao.Get(ctx, user.GetId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, user.GetId(), retrieved.GetId())
		assert.Equal(t, user.GetEmail(), retrieved.GetEmail())
	})

	t.Run("update nonce", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create a test user
		user := &auth_entities.User{
			Id:    "test-user-1",
			Email: "<EMAIL>",
			Nonce: 12345, // Set initial nonce
		}

		created, err := dao.Create(ctx, user)
		assert.NoError(t, err)
		assert.Equal(t, user.GetNonce(), created.GetNonce())

		// Update nonce
		err = dao.UpdateNonce(ctx, user.GetId())
		assert.NoError(t, err)

		// Verify nonce was changed
		updated, err := dao.Get(ctx, user.GetId())
		assert.NoError(t, err)
		assert.NotNil(t, updated)
		assert.NotEqual(t, user.GetNonce(), updated.GetNonce())
		assert.IsType(t, uint64(0), updated.GetNonce())

		// Test updating nonce for non-existent user
		err = dao.UpdateNonce(ctx, "non-existent-user") // Should not raise error, just log warning
		assert.NoError(t, err)
	})

	t.Run("get subscription id", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create a test user
		subId := "sub_test"
		user := &auth_entities.User{
			Id:             "test-user-1",
			Email:          "<EMAIL>",
			SubscriptionId: &subId,
		}

		created, err := dao.Create(ctx, user)
		assert.NoError(t, err)
		assert.Equal(t, user.GetSubscriptionId(), created.GetSubscriptionId())

		// Get subscription ID
		subscriptionId, err := dao.GetSubscriptionID(ctx, user.GetId())
		assert.NoError(t, err)
		assert.Equal(t, user.GetSubscriptionId(), subscriptionId)
	})

	t.Run("get orb subscription id", func(t *testing.T) {
		// Create a test user
		orbSubId := "orb_sub_test"
		user := &auth_entities.User{
			Id:                "test-user-orb",
			Email:             "<EMAIL>",
			OrbSubscriptionId: orbSubId,
		}

		created, err := dao.Create(ctx, user)
		assert.NoError(t, err)
		assert.Equal(t, user.GetOrbSubscriptionId(), created.GetOrbSubscriptionId())

		// Get Orb subscription ID
		orbSubscriptionId, err := dao.GetOrbSubscriptionID(ctx, user.GetId())
		assert.NoError(t, err)
		assert.Equal(t, user.GetOrbSubscriptionId(), orbSubscriptionId)
	})

	t.Run("billing method", func(t *testing.T) {
		// Create a test user with billing method
		user := &auth_entities.User{
			Id:            "test-user-billing",
			Email:         "<EMAIL>",
			BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_ORB,
		}

		created, err := dao.Create(ctx, user)
		assert.NoError(t, err)
		assert.Equal(t, user.GetBillingMethod(), created.GetBillingMethod())

		// Retrieve the user and verify billing method
		retrieved, err := dao.Get(ctx, user.GetId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, retrieved.GetBillingMethod())
	})

	t.Run("batch get", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create multiple users
		userIDs := make([]string, 0)

		for i := 0; i < 6; i++ {
			user := &auth_entities.User{
				Id:    fmt.Sprintf("test-user-%d", i),
				Email: fmt.Sprintf("<EMAIL>", i),
			}
			created, err := dao.Create(ctx, user)
			assert.NoError(t, err)
			userIDs = append(userIDs, created.GetId())
		}

		slices.Sort(userIDs)

		// Test batch get
		idx := 0
		err := dao.BatchGet(ctx, userIDs, 2, func(user *auth_entities.User) bool {
			assert.Equal(t, userIDs[idx], user.GetId())
			idx++
			return true
		})
		assert.NoError(t, err)
	})

	t.Run("find all with start key", func(t *testing.T) {
		bigtableFixture.ClearTable(t)

		// Create multiple users
		userIDs := make([]string, 0)

		for i := 0; i < 6; i++ {
			user := &auth_entities.User{
				Id:    fmt.Sprintf("test-user-%d", i),
				Email: fmt.Sprintf("<EMAIL>", i),
			}
			created, err := dao.Create(ctx, user)
			assert.NoError(t, err)
			userIDs = append(userIDs, created.GetId())
		}

		slices.Sort(userIDs)

		idx := 0
		err := dao.FindAllWithStartKey(ctx, "", func(user *auth_entities.User) bool {
			assert.Equal(t, userIDs[idx], user.GetId())
			idx++
			return true
		})
		assert.NoError(t, err)

		// Test find all with start key
		idx = 2
		err = dao.FindAllWithStartKey(ctx, userIDs[idx], func(user *auth_entities.User) bool {
			assert.Equal(t, userIDs[idx], user.GetId())
			idx++
			return true
		})
		assert.NoError(t, err)
	})
}

func caseUserTenantMappingDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewUserTenantMappingDAO(bigtableFixture.Table, "test-tenant")
	ctx := bigtableFixture.Ctx

	t.Run("find all with regex and prefix filters", func(t *testing.T) {
		// Create multiple mappings
		for i := 0; i < 3; i++ {
			mapping := &auth_entities.UserTenantMapping{
				UserId: fmt.Sprintf("test-user-%d", i),
				Tenant: "test-tenant",
			}
			_, err := dao.Create(ctx, mapping)
			assert.NoError(t, err)
		}

		// Test find_all for tenant
		count := 0
		err := dao.FindAll(ctx, func(_ *auth_entities.UserTenantMapping) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 3)
	})

	t.Run("duplicate mapping is idempotent", func(t *testing.T) {
		mapping := &auth_entities.UserTenantMapping{
			UserId: "test-user",
			Tenant: "test-tenant",
		}

		_, err := dao.Create(ctx, mapping)
		assert.NoError(t, err)
		// Creating same mapping again should work
		_, err = dao.Create(ctx, mapping)
		assert.NoError(t, err)
	})
}

func caseTokenHashDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewTokenHashDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("multiple tokens operations", func(t *testing.T) {
		// Test find all with no tokens
		count := 0
		err := dao.FindAll(ctx, func(_ *auth_entities.TokenHash) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 0)

		// Create multiple tokens
		var tokens []*auth_entities.TokenHash
		for i := 0; i < 3; i++ {
			token := &auth_entities.TokenHash{
				Hash:          fmt.Sprintf("test-hash-%d", i),
				AugmentUserId: fmt.Sprintf("test-user-%d", i),
			}
			created, err := dao.Create(ctx, token)
			assert.NoError(t, err)
			tokens = append(tokens, created)
		}

		// Test find_all
		count = 0
		err = dao.FindAll(ctx, func(_ *auth_entities.TokenHash) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 3)

		// Test retrieval
		for i := 0; i < 3; i++ {
			retrieved, err := dao.Get(ctx, fmt.Sprintf("test-hash-%d", i))
			assert.NoError(t, err)
			assert.NotNil(t, retrieved)
			assert.Equal(t, fmt.Sprintf("test-hash-%d", i), retrieved.GetHash())
			assert.Equal(t, fmt.Sprintf("test-user-%d", i), retrieved.GetAugmentUserId())
			assert.Equal(t, tokens[i].GetTenantId(), retrieved.GetTenantId())
		}

		// Test deletion
		err = dao.Delete(ctx, tokens[0].GetHash())
		assert.NoError(t, err)
		deleted, err := dao.Get(ctx, tokens[0].GetHash())
		assert.NoError(t, err)
		assert.Nil(t, deleted)

		// Test find_all after deletion
		count = 0
		err = dao.FindAll(ctx, func(_ *auth_entities.TokenHash) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 2)
	})
}

func caseCodeDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewCodeDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("updates", func(t *testing.T) {
		code := &auth_entities.Code{
			Code:          "test-code",
			AugmentUserId: "test-user-1",
			RedirectUri:   "http://example.com",
		}

		created, err := dao.Create(ctx, code)
		assert.NoError(t, err)

		// Update the code
		created.RedirectUri = "http://updated.com"
		_, err = dao.Update(ctx, created)
		assert.NoError(t, err)

		retrieved, err := dao.Get(ctx, code.GetCode())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, "http://updated.com", retrieved.GetRedirectUri())
	})

	t.Run("creation time", func(t *testing.T) {
		currentTime := time.Now().Unix()
		code := &auth_entities.Code{
			Code:                "test-code",
			AugmentUserId:       "test-user",
			RedirectUri:         "http://example.com",
			CreationTimeSeconds: currentTime,
		}

		_, err := dao.Create(ctx, code)
		assert.NoError(t, err)

		retrieved, err := dao.Get(ctx, code.GetCode())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, currentTime, retrieved.GetCreationTimeSeconds())
		assert.False(t, retrieved.GetIsUsed())
	})
}

func caseTermsApprovalDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewTermsApprovalDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("multiple revisions", func(t *testing.T) {
		email := "<EMAIL>"
		revisions := []string{"v1.0", "v1.1", "v2.0"}

		// Create approvals for multiple revisions
		for _, revision := range revisions {
			terms := &auth_entities.TermsApproval{
				Email:    email,
				Revision: revision,
			}
			_, err := dao.Create(ctx, terms)
			assert.NoError(t, err)
		}

		// Verify all revisions can be retrieved
		for _, revision := range revisions {
			retrieved, err := dao.Get(ctx, email, revision)
			assert.NoError(t, err)
			assert.NotNil(t, retrieved)
			assert.Equal(t, email, retrieved.GetEmail())
			assert.Equal(t, revision, retrieved.GetRevision())
		}
	})

	t.Run("multiple users", func(t *testing.T) {
		users := []string{"<EMAIL>", "<EMAIL>"}
		revision := "v1.0"

		for _, email := range users {
			terms := &auth_entities.TermsApproval{
				Email:    email,
				Revision: revision,
			}
			_, err := dao.Create(ctx, terms)
			assert.NoError(t, err)
		}

		for _, email := range users {
			approval, err := dao.Get(ctx, email, revision)
			assert.NoError(t, err)
			assert.NotNil(t, approval)
			assert.Equal(t, email, approval.GetEmail())
			assert.Equal(t, revision, approval.GetRevision())
		}
	})
}

func caseDaoFactoryTenantSpecific(t *testing.T, bigtableFixture *BigtableFixture) {
	ctx := bigtableFixture.Ctx

	factory := NewDAOFactory(bigtableFixture.Table)

	t.Run("tenant isolation", func(t *testing.T) {
		tenant1Dao := factory.GetUserTenantMappingDAO("tenant1")
		tenant2Dao := factory.GetUserTenantMappingDAO("tenant2")

		// Create mapping for tenant1
		mapping1 := &auth_entities.UserTenantMapping{
			UserId: "test-user",
			Tenant: "tenant1",
		}
		_, err := tenant1Dao.Create(ctx, mapping1)
		assert.NoError(t, err)

		// Create mapping for tenant2
		mapping2 := &auth_entities.UserTenantMapping{
			UserId: "test-user",
			Tenant: "tenant2",
		}
		_, err = tenant2Dao.Create(ctx, mapping2)
		assert.NoError(t, err)

		// Verify tenant1 only sees its own mappings
		count := 0
		err = tenant1Dao.FindAll(ctx, func(_ *auth_entities.UserTenantMapping) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 1)

		// Verify tenant2 only sees its own mappings
		count = 0
		err = tenant2Dao.FindAll(ctx, func(_ *auth_entities.UserTenantMapping) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 1)
	})
}

func caseCompareAndSet(t *testing.T, bigtableFixture *BigtableFixture) {
	t.Run("compare and set", func(t *testing.T) {
		value_with_regex_chars := []byte(")][(|?+*\\")
		rowKey := "test_compare_and_set#"

		// Create a row with a value that contains regex characters
		ret, err := compareAndSet(bigtableFixture.Table, rowKey, "User", "value", value_with_regex_chars, []byte("new_value"))
		assert.NoError(t, err)
		assert.False(t, ret, "Should fail because row doesn't exist")

		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", nil, value_with_regex_chars)
		assert.NoError(t, err)
		assert.True(t, ret, "Should succeed because row doesn't exist")

		row, err := bigtableFixture.Table.ReadRow(bigtableFixture.Ctx, rowKey)
		assert.NoError(t, err)
		assert.NotNil(t, row)
		assert.Equal(t, value_with_regex_chars, row["User"][0].Value)

		newValue := []byte("new_value")

		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", newValue, value_with_regex_chars)
		assert.NoError(t, err)
		assert.False(t, ret, "Should fail because row exists but value doesn't match")

		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", value_with_regex_chars, newValue)
		assert.NoError(t, err)
		assert.True(t, ret, "Should succeed because row exists and value matches")

		row, err = bigtableFixture.Table.ReadRow(bigtableFixture.Ctx, rowKey)
		assert.NoError(t, err)
		assert.NotNil(t, row)
		assert.Equal(t, newValue, row["User"][0].Value)
	})
}

func caseCompareAndSetWithLargeValues(t *testing.T, bigtableFixture *BigtableFixture) {
	t.Run("compare and set with large values", func(t *testing.T) {
		rowKey := "test_compare_and_set_large_values#"

		// Create a row with a large value
		baseString := "This is a test string for large value comparison. "
		repeats := 25000/len(baseString) + 1
		largeString := strings.Repeat(baseString, repeats)
		largeValue := []byte(largeString[:25000])
		ret, err := compareAndSet(bigtableFixture.Table, rowKey, "User", "value", largeValue, []byte("new_value"))
		assert.NoError(t, err)
		assert.False(t, ret, "Should fail because row doesn't exist")

		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", nil, largeValue)
		assert.NoError(t, err)
		assert.True(t, ret, "Should succeed because row doesn't exist")

		row, err := bigtableFixture.Table.ReadRow(bigtableFixture.Ctx, rowKey)
		assert.NoError(t, err)
		assert.NotNil(t, row)
		assert.Equal(t, largeValue, row["User"][0].Value)

		// Update the row with a new large value
		newLargeString := "Update" + largeString
		newLargeValue := []byte(newLargeString[:25000])
		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", largeValue, newLargeValue)
		assert.NoError(t, err)
		assert.True(t, ret, "Should succeed because row exists and value matches")

		// Update again with the old value.
		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", largeValue, newLargeValue)
		assert.NoError(t, err)
		assert.False(t, ret, "Should fail because row exists but value doesn't match")

		row, err = bigtableFixture.Table.ReadRow(bigtableFixture.Ctx, rowKey)
		assert.NoError(t, err)
		assert.NotNil(t, row)
		assert.Equal(t, newLargeValue, row["User"][0].Value)
	})
}

func caseCompareAndSetWithLargeBinaryValues(t *testing.T, bigtableFixture *BigtableFixture) {
	t.Run("compare and set with large values", func(t *testing.T) {
		rowKey := "test_compare_and_set_large_values#"

		// Create a row with a large binary value
		largeValue := make([]byte, 25000)
		for i := range largeValue {
			largeValue[i] = byte(i % 256)
		}
		ret, err := compareAndSet(bigtableFixture.Table, rowKey, "User", "value", largeValue, []byte("new_value"))
		assert.NoError(t, err)
		assert.False(t, ret, "Should fail because row doesn't exist")

		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", nil, largeValue)
		assert.NoError(t, err)
		assert.True(t, ret, "Should succeed because row doesn't exist")

		row, err := bigtableFixture.Table.ReadRow(bigtableFixture.Ctx, rowKey)
		assert.NoError(t, err)
		assert.NotNil(t, row)
		assert.Equal(t, largeValue, row["User"][0].Value)

		// Update the row with a new large binary value
		newLargeValue := make([]byte, 25000)
		for i := range newLargeValue {
			newLargeValue[i] = byte((i + 1) % 256)
		}
		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", largeValue, newLargeValue)
		assert.NoError(t, err)
		assert.True(t, ret, "Should succeed because row exists and value matches")

		// Udpate again with the old value
		ret, err = compareAndSet(bigtableFixture.Table, rowKey, "User", "value", largeValue, newLargeValue)
		assert.NoError(t, err)
		assert.False(t, ret, "Should fail because row exists but value doesn't match")

		row, err = bigtableFixture.Table.ReadRow(bigtableFixture.Ctx, rowKey)
		assert.NoError(t, err)
		assert.NotNil(t, row)
		if bytes.Equal(newLargeValue, row["User"][0].Value) {
			t.Log("Expected value matches newLargeValue")
		} else if bytes.Equal(largeValue, row["User"][0].Value) {
			t.Error("Expected value matches largeValue")
		} else {
			t.Error("Unexpected value")
		}
		assert.Equal(t, newLargeValue, row["User"][0].Value)
	})
}

func caseTenantCreationDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewTenantCreationDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("not found returns nil", func(t *testing.T) {
		tenantCreation, err := dao.Get(ctx, "non-existent-id")
		assert.NoError(t, err)
		assert.Nil(t, tenantCreation)
	})

	t.Run("create and get", func(t *testing.T) {
		tenantCreation := &auth_entities.TenantCreation{
			Id:        "test-tenant-creation-1",
			CreatedAt: timestamppb.New(time.Now().UTC()),
			Status:    auth_entities.TenantCreation_PENDING,
		}

		created, err := dao.Create(ctx, tenantCreation)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(tenantCreation, created))

		retrieved, err := dao.Get(ctx, "test-tenant-creation-1")
		assert.NoError(t, err)
		assert.True(t, proto.Equal(tenantCreation, retrieved))
	})

	t.Run("update", func(t *testing.T) {
		tenantCreation := &auth_entities.TenantCreation{
			Id:        "test-tenant-creation-2",
			CreatedAt: timestamppb.New(time.Now().UTC()),
			Status:    auth_entities.TenantCreation_PENDING,
		}

		// Create the tenant creation record
		_, err := dao.Create(ctx, tenantCreation)
		assert.NoError(t, err)

		// Update the status to SUCCESS and add a tenant ID
		tenantCreation.Status = auth_entities.TenantCreation_SUCCESS
		tenantCreation.TenantId = "new-tenant-id"

		updated, err := dao.Update(ctx, tenantCreation)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(tenantCreation, updated))

		// Verify the update was persisted
		retrieved, err := dao.Get(ctx, "test-tenant-creation-2")
		assert.NoError(t, err)
		assert.True(t, proto.Equal(tenantCreation, retrieved))
	})
}

func caseTenantSubscriptionMappingDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewTenantSubscriptionMappingDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("not found returns nil", func(t *testing.T) {
		mapping, err := dao.Get(ctx, "non-existent-tenant")
		assert.NoError(t, err)
		assert.Nil(t, mapping)
	})

	t.Run("find all empty", func(t *testing.T) {
		count := 0
		err := dao.FindAll(ctx, func(_ *auth_entities.TenantSubscriptionMapping) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 0)
	})

	t.Run("create and get", func(t *testing.T) {
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             "test-tenant-1",
			StripeSubscriptionId: "sub_123456",
		}

		created, err := dao.Create(ctx, mapping)
		assert.NoError(t, err)
		assert.Equal(t, mapping.GetTenantId(), created.GetTenantId())
		assert.Equal(t, mapping.GetStripeSubscriptionId(), created.GetStripeSubscriptionId())

		retrieved, err := dao.Get(ctx, mapping.GetTenantId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, mapping.GetTenantId(), retrieved.GetTenantId())
		assert.Equal(t, mapping.GetStripeSubscriptionId(), retrieved.GetStripeSubscriptionId())
	})

	t.Run("update mapping", func(t *testing.T) {
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             "test-tenant-update",
			StripeSubscriptionId: "sub_original",
		}

		created, err := dao.Create(ctx, mapping)
		assert.NoError(t, err)

		// Update the mapping
		created.StripeSubscriptionId = "sub_updated"
		updated, err := dao.Update(ctx, created)
		assert.NoError(t, err)
		assert.Equal(t, "sub_updated", updated.GetStripeSubscriptionId())

		// Verify the update
		retrieved, err := dao.Get(ctx, mapping.GetTenantId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, "sub_updated", retrieved.GetStripeSubscriptionId())
	})

	t.Run("delete mapping", func(t *testing.T) {
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             "test-tenant-delete",
			StripeSubscriptionId: "sub_to_delete",
		}

		_, err := dao.Create(ctx, mapping)
		assert.NoError(t, err)

		// Verify it exists
		retrieved, err := dao.Get(ctx, mapping.GetTenantId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)

		// Delete it
		err = dao.Delete(ctx, mapping.GetTenantId())
		assert.NoError(t, err)

		// Verify it's gone
		deleted, err := dao.Get(ctx, mapping.GetTenantId())
		assert.NoError(t, err)
		assert.Nil(t, deleted)
	})
}

func caseTenantSubscriptionMappingDAOFindAllPaginated(t *testing.T, bigtableFixture *BigtableFixture) {
	// Create a single fixture for all tests
	dao := NewTenantSubscriptionMappingDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("empty results", func(t *testing.T) {
		// Test with empty database
		mappings, nextStartKey, err := dao.FindAllPaginated(ctx, "", 10)
		assert.NoError(t, err)
		assert.Empty(t, mappings)
		assert.Empty(t, nextStartKey)
	})

	t.Run("single page of results", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create 3 tenant subscription mappings
		for i := 1; i <= 3; i++ {
			mapping := &auth_entities.TenantSubscriptionMapping{
				TenantId:             fmt.Sprintf("tenant_single_page_%d", i),
				StripeSubscriptionId: fmt.Sprintf("sub_single_page_%d", i),
				StripeCustomerId:     fmt.Sprintf("cus_single_page_%d", i),
			}
			_, err := dao.Create(ctx, mapping)
			assert.NoError(t, err)
		}

		// Fetch with page size exactly matching number of items
		mappings, nextStartKey, err := dao.FindAllPaginated(ctx, "", 3)
		assert.NoError(t, err)
		assert.Len(t, mappings, 3)
		assert.Empty(t, nextStartKey, "Should not have next key when all items fit in page")

		// Verify all mappings are returned
		tenantIDs := make([]string, len(mappings))
		for i, mapping := range mappings {
			tenantIDs[i] = mapping.GetTenantId()
		}
		assert.ElementsMatch(t, []string{
			"tenant_single_page_1",
			"tenant_single_page_2",
			"tenant_single_page_3",
		}, tenantIDs)
	})

	t.Run("multiple pages of results", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create 10 tenant subscription mappings
		expectedTenantIDs := make([]string, 10)
		for i := 1; i <= 10; i++ {
			tenantID := fmt.Sprintf("tenant_multi_page_%d", i)
			expectedTenantIDs[i-1] = tenantID
			mapping := &auth_entities.TenantSubscriptionMapping{
				TenantId:             tenantID,
				StripeSubscriptionId: fmt.Sprintf("sub_multi_page_%d", i),
				StripeCustomerId:     fmt.Sprintf("cus_multi_page_%d", i),
			}
			_, err := dao.Create(ctx, mapping)
			assert.NoError(t, err)
		}

		// Page through all results with small page size
		pageSize := uint32(3)
		startKey := ""
		var allResults []*auth_entities.TenantSubscriptionMapping
		pageCount := 0

		for {
			pageCount++
			page, nextKey, err := dao.FindAllPaginated(ctx, startKey, pageSize)
			assert.NoError(t, err)

			// Verify page size (except possibly last page)
			if nextKey != "" {
				assert.Len(t, page, int(pageSize), "Non-final pages should have exactly pageSize items")
			} else {
				assert.LessOrEqual(t, len(page), int(pageSize), "Final page should have <= pageSize items")
			}

			allResults = append(allResults, page...)

			if nextKey == "" {
				break
			}
			startKey = nextKey
		}

		// Verify we got 4 pages (ceiling of 10/3)
		assert.Equal(t, 4, pageCount, "Should have exactly 4 pages with page size 3")

		// Verify we got all 10 mappings without duplicates
		assert.Len(t, allResults, 10, "Should have retrieved all 10 mappings")

		resultTenantIDs := make([]string, len(allResults))
		for i, mapping := range allResults {
			resultTenantIDs[i] = mapping.GetTenantId()
		}
		assert.ElementsMatch(t, expectedTenantIDs, resultTenantIDs, "Should retrieve all expected tenant IDs")
	})

	t.Run("pagination with startKey", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create mappings with predictable IDs
		var createdMappings []*auth_entities.TenantSubscriptionMapping
		for i := 1; i <= 5; i++ {
			mapping := &auth_entities.TenantSubscriptionMapping{
				TenantId:             fmt.Sprintf("tenant_pagination_%d", i),
				StripeSubscriptionId: fmt.Sprintf("sub_pagination_%d", i),
				StripeCustomerId:     fmt.Sprintf("cus_pagination_%d", i),
			}
			created, err := dao.Create(ctx, mapping)
			assert.NoError(t, err)
			createdMappings = append(createdMappings, created)
		}

		// Test with invalid startKey
		results, nextKey, err := dao.FindAllPaginated(ctx, "invalid_key_format", 10)
		assert.NoError(t, err, "Invalid key should not cause error, just return results from beginning")
		assert.Empty(t, results, "Should return empty results when startKey is invalid")
		assert.Empty(t, nextKey)

		// Use the middle mapping as the startKey
		middleMapping := createdMappings[2] // index 2 = 3rd item
		middleRowKey := fmt.Sprintf("%s#%s", dao.rowCategory, middleMapping.GetTenantId())

		// Fetch results starting from the middle mapping (inclusive)
		results, nextKey, err = dao.FindAllPaginated(ctx, middleRowKey, 2)
		assert.NoError(t, err)
		assert.Len(t, results, 2, "Should respect page size when starting from middle")
		assert.NotEmpty(t, nextKey, "Should have next key when more results exist")

		// Verify we got the expected mappings (3rd and 4th)
		resultTenantIDs := []string{results[0].GetTenantId(), results[1].GetTenantId()}
		assert.ElementsMatch(t, []string{"tenant_pagination_3", "tenant_pagination_4"}, resultTenantIDs)

		// Get the final page
		lastResults, finalNextKey, err := dao.FindAllPaginated(ctx, nextKey, 2)
		assert.NoError(t, err)
		assert.Len(t, lastResults, 1, "Last page should have exactly 1 item")
		assert.Empty(t, finalNextKey, "Final page should have empty next key")
		assert.Equal(t, "tenant_pagination_5", lastResults[0].GetTenantId())
	})

	t.Run("page size exactly at boundary", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create exactly 5 mappings
		for i := 1; i <= 5; i++ {
			mapping := &auth_entities.TenantSubscriptionMapping{
				TenantId:             fmt.Sprintf("tenant_boundary_%d", i),
				StripeSubscriptionId: fmt.Sprintf("sub_boundary_%d", i),
				StripeCustomerId:     fmt.Sprintf("cus_boundary_%d", i),
			}
			_, err := dao.Create(ctx, mapping)
			assert.NoError(t, err)
		}

		// Fetch with page size exactly matching total count
		results, nextKey, err := dao.FindAllPaginated(ctx, "", 5)
		assert.NoError(t, err)
		assert.Len(t, results, 5)
		assert.Empty(t, nextKey, "Next key should be empty when exactly all items fit in page")
	})
}

func caseSubscriptionDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewSubscriptionDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("not found returns nil", func(t *testing.T) {
		subscription, err := dao.Get(ctx, "non-existent-id")
		assert.NoError(t, err)
		assert.Nil(t, subscription)
	})

	t.Run("find all empty", func(t *testing.T) {
		count := 0
		err := dao.FindAll(ctx, func(_ *auth_entities.Subscription) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 0)
	})

	t.Run("create and get", func(t *testing.T) {
		subscription := createTestSubscription(map[string]interface{}{
			"SubscriptionId":   "sub_123456",
			"StripeCustomerId": "cus_123456",
		})

		created, err := dao.Create(ctx, subscription)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(subscription, created))

		retrieved, err := dao.Get(ctx, subscription.GetSubscriptionId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.True(t, proto.Equal(subscription, retrieved))
	})

	t.Run("update subscription", func(t *testing.T) {
		subscription := createTestSubscription(map[string]interface{}{
			"SubscriptionId":   "sub_update_test",
			"StripeCustomerId": "cus_update_test",
			"PriceId":          "price_community",
		})

		created, err := dao.Create(ctx, subscription)
		assert.NoError(t, err)

		// Update the subscription
		created.Status = auth_entities.Subscription_CANCELED
		created.PriceId = "price_professional"
		updated, err := dao.Update(ctx, created)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(created, updated))

		// Verify the update
		retrieved, err := dao.Get(ctx, subscription.GetSubscriptionId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.True(t, proto.Equal(created, retrieved))
	})

	t.Run("delete subscription", func(t *testing.T) {
		subscription := createTestSubscription(map[string]interface{}{
			"SubscriptionId":   "sub_delete_test",
			"StripeCustomerId": "cus_delete_test",
			"PriceId":          "price_community",
		})

		_, err := dao.Create(ctx, subscription)
		assert.NoError(t, err)

		// Verify it exists
		retrieved, err := dao.Get(ctx, subscription.GetSubscriptionId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)

		// Delete it
		err = dao.Delete(ctx, subscription.GetSubscriptionId())
		assert.NoError(t, err)

		// Verify it's gone
		deleted, err := dao.Get(ctx, subscription.GetSubscriptionId())
		assert.NoError(t, err)
		assert.Nil(t, deleted)
	})
}

func caseTenantInvitationDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	ctx := bigtableFixture.Ctx

	t.Run("not found returns nil", func(t *testing.T) {
		tenantID := uuid.New().String()
		dao := NewTenantInvitationDAO(bigtableFixture.Table, tenantID)
		invitation, err := dao.Get(ctx, "non-existent-id")
		assert.NoError(t, err)
		assert.Nil(t, invitation)
	})

	t.Run("create and get", func(t *testing.T) {
		tenantID := uuid.New().String()
		dao := NewTenantInvitationDAO(bigtableFixture.Table, tenantID)

		id := uuid.New().String()
		invitation := &auth_entities.TenantInvitation{
			Id:            id,
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "test-tenant",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		created, err := dao.Create(ctx, invitation)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(invitation, created))

		retrieved, err := dao.Get(ctx, id)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(invitation, retrieved))
	})

	t.Run("update", func(t *testing.T) {
		tenantID := uuid.New().String()
		dao := NewTenantInvitationDAO(bigtableFixture.Table, tenantID)

		id := uuid.New().String()
		invitation := &auth_entities.TenantInvitation{
			Id:            id,
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "test-tenant",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err := dao.Create(ctx, invitation)
		assert.NoError(t, err)

		invitation.Status = auth_entities.TenantInvitation_ACCEPTED
		updated, err := dao.Update(ctx, invitation)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(invitation, updated))

		retrieved, err := dao.Get(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, auth_entities.TenantInvitation_ACCEPTED, retrieved.GetStatus())
	})

	t.Run("delete", func(t *testing.T) {
		tenantID := uuid.New().String()
		dao := NewTenantInvitationDAO(bigtableFixture.Table, tenantID)

		id := uuid.New().String()
		invitation := &auth_entities.TenantInvitation{
			Id:            id,
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "test-tenant",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err := dao.Create(ctx, invitation)
		assert.NoError(t, err)

		err = dao.Delete(ctx, id)
		assert.NoError(t, err)

		retrieved, err := dao.Get(ctx, id)
		assert.NoError(t, err)
		assert.Nil(t, retrieved)
	})

	t.Run("find all", func(t *testing.T) {
		tenantID := uuid.New().String()
		dao := NewTenantInvitationDAO(bigtableFixture.Table, tenantID)

		id1 := uuid.New().String()
		invitation1 := &auth_entities.TenantInvitation{
			Id:            id1,
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "test-tenant",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err := dao.Create(ctx, invitation1)
		assert.NoError(t, err)

		id2 := uuid.New().String()
		invitation2 := &auth_entities.TenantInvitation{
			Id:            id2,
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "test-tenant",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err = dao.Create(ctx, invitation2)
		assert.NoError(t, err)

		count := 0
		err = dao.FindAll(ctx, func(_ *auth_entities.TenantInvitation) bool {
			count++
			return true
		})
		assert.NoError(t, err)
		assert.Equal(t, count, 2)

		// Invitation from another tenant that shouldn't be returned.
		dao2 := NewTenantInvitationDAO(bigtableFixture.Table, "other-tenant")
		invitation3 := &auth_entities.TenantInvitation{
			Id:            uuid.New().String(),
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "other-tenant",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err = dao2.Create(ctx, invitation3)
		assert.NoError(t, err)

		invitations := make([]*auth_entities.TenantInvitation, 0)
		err = dao.FindAll(ctx, func(invitation *auth_entities.TenantInvitation) bool {
			invitations = append(invitations, invitation)
			return true
		})
		assert.NoError(t, err)
		assert.Len(t, invitations, 2)
		returnedIDs := []string{invitations[0].GetId(), invitations[1].GetId()}
		assert.Contains(t, returnedIDs, id1)
		assert.Contains(t, returnedIDs, id2)
	})

	t.Run("ListInvitationsForAllTenants", func(t *testing.T) {
		// This test needs to start fresh.
		bigtableFixture.ClearTable(t)

		dao1 := NewTenantInvitationDAO(bigtableFixture.Table, "tenant1")
		invitation1 := &auth_entities.TenantInvitation{
			Id:            "invitation1",
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "tenant1",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err := dao1.Create(ctx, invitation1)
		assert.NoError(t, err)

		dao2 := NewTenantInvitationDAO(bigtableFixture.Table, "tenant2")
		invitation2 := &auth_entities.TenantInvitation{
			Id:            "invitation2",
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  "<EMAIL>",
			TenantId:      "tenant2",
			InviterUserId: "inviter-user",
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err = dao2.Create(ctx, invitation2)
		assert.NoError(t, err)

		globalDAO := NewTenantInvitationDAO(bigtableFixture.Table, "")
		invitations := make([]*auth_entities.TenantInvitation, 0)
		err = globalDAO.ListInvitationsForAllTenants(ctx, func(invitation *auth_entities.TenantInvitation) bool {
			invitations = append(invitations, invitation)
			return true
		})
		assert.NoError(t, err)
		assert.Len(t, invitations, 2)
		returnedIDs := []string{invitations[0].GetId(), invitations[1].GetId()}
		assert.Contains(t, returnedIDs, "invitation1")
		assert.Contains(t, returnedIDs, "invitation2")
	})
}

func caseInvitationResolutionDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewInvitationResolutionDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("not found returns nil", func(t *testing.T) {
		invitationResolution, err := dao.Get(ctx, "non-existent-id")
		assert.NoError(t, err)
		assert.Nil(t, invitationResolution)
	})

	t.Run("create and get", func(t *testing.T) {
		invitationResolution := &auth_entities.InvitationResolution{
			Id:        "invitation-resolution-1",
			CreatedAt: timestamppb.New(time.Now().UTC()),
			Status:    auth_entities.InvitationResolution_PENDING,
		}

		created, err := dao.Create(ctx, invitationResolution)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(invitationResolution, created))

		retrieved, err := dao.Get(ctx, "invitation-resolution-1")
		assert.NoError(t, err)
		assert.True(t, proto.Equal(invitationResolution, retrieved))
	})

	t.Run("update", func(t *testing.T) {
		invitationResolution := &auth_entities.InvitationResolution{
			Id:        "invitation-resolution-2",
			CreatedAt: timestamppb.New(time.Now().UTC()),
			Status:    auth_entities.InvitationResolution_PENDING,
		}

		// Create the tenant creation record
		_, err := dao.Create(ctx, invitationResolution)
		assert.NoError(t, err)

		// Update the status to SUCCESS and add a tenant ID
		invitationResolution.Status = auth_entities.InvitationResolution_SUCCESS

		updated, err := dao.Update(ctx, invitationResolution)
		assert.NoError(t, err)
		assert.True(t, proto.Equal(invitationResolution, updated))

		// Verify the update was persisted
		retrieved, err := dao.Get(ctx, "invitation-resolution-2")
		assert.NoError(t, err)
		assert.True(t, proto.Equal(invitationResolution, retrieved))
	})
}

func caseSubscriptionDAOFindAllPaginated(t *testing.T, bigtableFixture *BigtableFixture) {
	// Create a single fixture for all tests
	dao := NewSubscriptionDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("empty results", func(t *testing.T) {
		// Test with empty database
		subscriptions, nextStartKey, err := dao.FindAllPaginated(ctx, "", 10)
		assert.NoError(t, err)
		assert.Empty(t, subscriptions)
		assert.Empty(t, nextStartKey)
	})

	t.Run("single page of results", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create 3 subscriptions
		for i := 1; i <= 3; i++ {
			subscription := createTestSubscription(map[string]interface{}{
				"SubscriptionId":   fmt.Sprintf("sub_single_page_%d", i),
				"StripeCustomerId": fmt.Sprintf("cus_single_page_%d", i),
			})
			_, err := dao.Create(ctx, subscription)
			assert.NoError(t, err)
		}

		// Fetch with page size exactly matching number of items
		subscriptions, nextStartKey, err := dao.FindAllPaginated(ctx, "", 3)
		assert.NoError(t, err)
		assert.Len(t, subscriptions, 3)
		assert.Empty(t, nextStartKey, "Should not have next key when all items fit in page")

		// Verify all subscriptions are returned
		subIDs := make([]string, len(subscriptions))
		for i, sub := range subscriptions {
			subIDs[i] = sub.GetSubscriptionId()
		}
		assert.ElementsMatch(t, []string{
			"sub_single_page_1",
			"sub_single_page_2",
			"sub_single_page_3",
		}, subIDs)
	})

	t.Run("multiple pages of results", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create 10 subscriptions
		expectedIDs := make([]string, 10)
		for i := 1; i <= 10; i++ {
			subID := fmt.Sprintf("sub_multi_page_%d", i)
			expectedIDs[i-1] = subID
			subscription := createTestSubscription(map[string]interface{}{
				"SubscriptionId":   subID,
				"StripeCustomerId": fmt.Sprintf("cus_multi_page_%d", i),
			})
			_, err := dao.Create(ctx, subscription)
			assert.NoError(t, err)
		}

		// Page through all results with small page size
		pageSize := uint32(3)
		startKey := ""
		var allResults []*auth_entities.Subscription
		pageCount := 0

		for {
			pageCount++
			page, nextKey, err := dao.FindAllPaginated(ctx, startKey, pageSize)
			assert.NoError(t, err)

			// Verify page size (except possibly last page)
			if nextKey != "" {
				assert.Len(t, page, int(pageSize), "Non-final pages should have exactly pageSize items")
			} else {
				assert.LessOrEqual(t, len(page), int(pageSize), "Final page should have <= pageSize items")
			}

			allResults = append(allResults, page...)

			if nextKey == "" {
				break
			}
			startKey = nextKey
		}

		// Verify we got 4 pages (ceiling of 10/3)
		assert.Equal(t, 4, pageCount, "Should have exactly 4 pages with page size 3")

		// Verify we got all 10 subscriptions without duplicates
		assert.Len(t, allResults, 10, "Should have retrieved all 10 subscriptions")

		resultIDs := make([]string, len(allResults))
		for i, sub := range allResults {
			resultIDs[i] = sub.GetSubscriptionId()
		}
		assert.ElementsMatch(t, expectedIDs, resultIDs, "Should retrieve all expected subscription IDs")
	})

	t.Run("pagination with startKey", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create subscriptions with predictable IDs
		var createdSubscriptions []*auth_entities.Subscription
		for i := 1; i <= 5; i++ {
			subscription := createTestSubscription(map[string]interface{}{
				"SubscriptionId":   fmt.Sprintf("sub_pagination_%d", i),
				"StripeCustomerId": fmt.Sprintf("cus_pagination_%d", i),
			})
			created, err := dao.Create(ctx, subscription)
			assert.NoError(t, err)
			createdSubscriptions = append(createdSubscriptions, created)
		}

		// Test with invalid startKey
		results, nextKey, err := dao.FindAllPaginated(ctx, "invalid_key_format", 10)
		assert.NoError(t, err, "Invalid key should not cause error, just return results from beginning")
		assert.Empty(t, results, "Should return all results when startKey is invalid")
		assert.Empty(t, nextKey)

		// Use the middle subscription as the startKey
		middleSubscription := createdSubscriptions[2] // index 2 = 3rd item
		middleRowKey := fmt.Sprintf("%s#%s", dao.rowCategory, middleSubscription.GetSubscriptionId())

		// Fetch results starting from the middle subscription (inclusive)
		results, nextKey, err = dao.FindAllPaginated(ctx, middleRowKey, 2)
		assert.NoError(t, err)
		assert.Len(t, results, 2, "Should respect page size when starting from middle")
		assert.NotEmpty(t, nextKey, "Should have next key when more results exist")

		// Verify we got the expected subscriptions (3rd and 4th)
		resultIDs := []string{results[0].GetSubscriptionId(), results[1].GetSubscriptionId()}
		assert.ElementsMatch(t, []string{"sub_pagination_3", "sub_pagination_4"}, resultIDs)

		// Get the final page
		lastResults, finalNextKey, err := dao.FindAllPaginated(ctx, nextKey, 2)
		assert.NoError(t, err)
		assert.Len(t, lastResults, 1, "Last page should have exactly 1 item")
		assert.Empty(t, finalNextKey, "Final page should have empty next key")
		assert.Equal(t, "sub_pagination_5", lastResults[0].GetSubscriptionId())
	})

	t.Run("page size exactly at boundary", func(t *testing.T) {
		// Clear the table before this test
		bigtableFixture.ClearTable(t)

		// Create exactly 5 subscriptions
		for i := 1; i <= 5; i++ {
			subscription := createTestSubscription(map[string]interface{}{
				"SubscriptionId":   fmt.Sprintf("sub_boundary_%d", i),
				"StripeCustomerId": fmt.Sprintf("cus_boundary_%d", i),
			})
			_, err := dao.Create(ctx, subscription)
			assert.NoError(t, err)
		}

		// Fetch with page size exactly matching total count
		results, nextKey, err := dao.FindAllPaginated(ctx, "", 5)
		assert.NoError(t, err)
		assert.Len(t, results, 5)
		assert.Empty(t, nextKey, "Next key should be empty when exactly all items fit in page")
	})
}

func caseIDPUserMappingDAO(t *testing.T, bigtableFixture *BigtableFixture) {
	dao := NewIDPUserMappingDAO(bigtableFixture.Table)
	ctx := bigtableFixture.Ctx

	t.Run("create and get", func(t *testing.T) {
		mapping := &auth_entities.IdpUserMapping{
			IdpUserId:     "idp_123456",
			AugmentUserId: "augment_123456",
		}

		created, err := dao.Create(ctx, mapping)
		assert.NoError(t, err)
		assert.Equal(t, mapping.GetIdpUserId(), created.GetIdpUserId())
		assert.Equal(t, mapping.GetAugmentUserId(), created.GetAugmentUserId())

		retrieved, err := dao.Get(ctx, mapping.GetIdpUserId())
		assert.NoError(t, err)
		assert.NotNil(t, retrieved)
		assert.Equal(t, mapping.GetIdpUserId(), retrieved.GetIdpUserId())
		assert.Equal(t, mapping.GetAugmentUserId(), retrieved.GetAugmentUserId())
	})

	t.Run("try create", func(t *testing.T) {
		mapping := &auth_entities.IdpUserMapping{
			IdpUserId:     "idp_1234",
			AugmentUserId: "augment_1234",
		}
		created, err := dao.TryCreate(ctx, mapping)
		assert.NoError(t, err)
		assert.True(t, created)

		mapping = &auth_entities.IdpUserMapping{
			IdpUserId:     "idp_1234",
			AugmentUserId: "augment_5678",
		}
		created, err = dao.TryCreate(ctx, mapping)
		assert.NoError(t, err)
		assert.False(t, created)

		dbMapping, err := dao.Get(ctx, "idp_1234")
		assert.NoError(t, err)
		assert.Equal(t, "augment_1234", dbMapping.GetAugmentUserId())
	})
}

func TestAuthDao(t *testing.T) {
	// Create a single fixture for all tests
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	testCases := []struct {
		name     string
		testFunc func(t *testing.T, bigtableFixture *BigtableFixture)
	}{
		{
			name:     "dao factory",
			testFunc: caseDaoFactory,
		},
		{
			name:     "user dao",
			testFunc: caseUserDAO,
		},
		{
			name:     "user tenant mapping dao",
			testFunc: caseUserTenantMappingDAO,
		},
		{
			name:     "token hash dao",
			testFunc: caseTokenHashDAO,
		},
		{
			name:     "code dao",
			testFunc: caseCodeDAO,
		},
		{
			name:     "terms approval dao",
			testFunc: caseTermsApprovalDAO,
		},
		{
			name:     "dao factory tenant specific",
			testFunc: caseDaoFactoryTenantSpecific,
		},
		{
			name:     "compare and set",
			testFunc: caseCompareAndSet,
		},
		{
			name:     "compare and set with large values",
			testFunc: caseCompareAndSetWithLargeValues,
		},
		{
			name:     "compare and set with large binary values",
			testFunc: caseCompareAndSetWithLargeBinaryValues,
		},
		{
			name:     "tenant creation dao",
			testFunc: caseTenantCreationDAO,
		},
		{
			name:     "tenant subscription mapping dao",
			testFunc: caseTenantSubscriptionMappingDAO,
		},
		{
			name:     "tenant subscription mapping dao find all paginated",
			testFunc: caseTenantSubscriptionMappingDAOFindAllPaginated,
		},
		{
			name:     "subscription dao",
			testFunc: caseSubscriptionDAO,
		},
		{
			name:     "tenant invitation dao",
			testFunc: caseTenantInvitationDAO,
		},
		{
			name:     "invitation resolution dao",
			testFunc: caseInvitationResolutionDAO,
		},
		{
			name:     "subscription dao find all paginated",
			testFunc: caseSubscriptionDAOFindAllPaginated,
		},
		{
			name:     "idp user mapping dao",
			testFunc: caseIDPUserMappingDAO,
		},
	}

	for _, tc := range testCases {
		bigtableFixture.ClearTable(t)

		t.Run(tc.name, func(t *testing.T) {
			tc.testFunc(t, bigtableFixture)
		})
	}
}

load("@aspect_bazel_lib//lib:copy_to_bin.bzl", "copy_to_bin")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_binary", "go_grpc_library", "go_library", "go_oci_image", "go_proto_library", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library", "py_oci_image")
load("//tools/bzl:rust.bzl", "rust_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

filegroup(
    name = "write_key",
    srcs = ["write_key.yaml"],
    visibility = ["//services/auth:__subpackages__"],
)

exports_files(
    ["column_family.json"],
    visibility = ["//services/auth/central:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        "column_family.json",
        "nginx.conf",
        ":write_key",
        "//services/auth/central/server:grpc_image",
        "//services/auth/central/server:image",
    ],
    # uses auth-central and auth-central-grpc
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/auth:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/tenant_watcher/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/deploy/configs:orb",
        "//services/lib/pubsub:pubsub-lib",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    visibility = [
        "//services/auth/central:__subpackages__",
    ],
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

go_library(
    name = "auth_dao",
    srcs = [
        "auth_dao.go",
    ],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_dao",
    visibility = ["//services/auth/central:__subpackages__"],
    deps = [
        ":auth_entities_go_proto",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
    ],
)

py_binary(
    name = "server",
    srcs = [
        "app.py",
        "config.py",
        "invitation_service.py",
        "tenant_map.py",
    ],
    data = glob(["static/**/*"]) + glob([
        "templates/**/*",
        "tos/**/*",
    ]) + [
        "column_family.json",
        ":copy_css",
        ":copy_static_files",
        ":copy_templates",
    ],
    main = "app.py",
    pyright_extra_args = {
        "reportMissingParameterType": True,
    },
    deps = [
        requirement("authlib"),
        requirement("dataclasses_json"),
        requirement("flask"),
        requirement("requests"),
        requirement("segment-analytics-python"),
        requirement("prometheus_flask_exporter"),
        requirement("prometheus_client"),
        requirement("pydantic"),
        requirement("gunicorn"),
        requirement("python-dateutil"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("google-cloud-recaptcha-enterprise"),
        ":auth_entities_py_proto",
        ":auth_py_proto",
        ":front_end_token_service_py_proto",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//base/logging/audit:audit_py",
        "//base/python/grpc:client_options",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/auth/central/client:auth_client_py",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/request_insight/publisher:publisher_py",
        "//services/tenant_watcher/client",
        "@rules_python//python/runfiles",
    ],
)

genrule(
    name = "copy_static_files",
    srcs = ["//services/auth/common/frontend:static_files"],
    outs = ["static/favicon.ico"],
    cmd = "cp $(SRCS) $(@D)",
    local = 1,
    output_to_bindir = 1,
)

genrule(
    name = "copy_templates",
    srcs = [
        "//services/auth/common/frontend:templates_files",
    ],
    outs = [
        "templates/common/augi-logo.svg",
        "templates/common/c-footer.html",
        "templates/common/l-oauth-open.html",
        "templates/common/l-oauth-open-left.html",
        "templates/common/l-oauth-close.html",
        "templates/common/html-head.html",
    ],
    cmd = "cp $(SRCS) $(RULEDIR)/templates/common/",
)

genrule(
    name = "copy_css",
    srcs = ["//services/auth/common/frontend:postcss"],
    outs = [
        "templates/build/client_redirect.css",
        "templates/build/login.css",
        "templates/build/unauthenticated.css",
        "templates/build/invitations.css",
    ],
    cmd = "cp $(SRCS) $(RULEDIR)/templates/build/",
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    # Use an image that has tini, which we use to handle signals sent to PID 1,
    # so that it can shut down the whole container, which may include gRPC
    # processes and gunicorn processes.
    base = "//tools/docker:ubuntu_tini_base_image",
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

copy_to_bin(
    name = "css_files",
    srcs = glob([
        "styles/**/*.css",
    ]),
    visibility = ["//services/auth/common/frontend:__subpackages__"],
)

TEST_DEPS = [
    ":server",
    "//base/test_utils:bigtable_emulator",
    "//base/test_utils:bigtable_setup",
    "//base/test_utils/pubsub:pubsub_emulator",
    "//services/tenant_watcher/server:tenant_watcher_test_setup",
    "//services/token_exchange/server:token_exchange_test_setup",
    ":auth_central_test_setup",
    requirement("beautifulsoup4"),
]

pytest_test(
    name = "app_test",
    srcs = [
        "app_test.py",
        "auth_fixtures.py",
        "conftest.py",
    ],
    deps = TEST_DEPS,
)

pytest_test(
    name = "tenant_map_test",
    srcs = [
        "auth_fixtures.py",
        "conftest.py",
        "tenant_map_test.py",
    ],
    deps = TEST_DEPS,
)

pytest_test(
    name = "invitation_service_test",
    srcs = [
        "invitation_service_test.py",
    ],
    deps = TEST_DEPS,
)

test_suite(
    name = "all_tests",
    tags = ["manual"],
    tests = [
        ":app_test",
        ":invitation_service_test",
        ":tenant_map_test",
    ],
)

# Grpc server

proto_library(
    name = "auth_proto",
    srcs = [
        "auth.proto",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":auth_entities_proto",
        "@protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "auth_entities_proto",
    srcs = [
        "auth_entities.proto",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "auth_dev_proto",
    srcs = [
        "auth_dev.proto",
    ],
    deps = [
        ":auth_entities_proto",
        "@protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "auth_internal_proto",
    srcs = [
        "auth_internal.proto",
    ],
    visibility = ["//services/auth:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_proto",
        "//services/auth/central/server:auth_proto",
        "//services/tenant_watcher:tenant_watcher_proto",
        "@protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "front_end_token_service_proto",
    srcs = [
        "front_end_token_service.proto",
    ],
    deps = [
        ":auth_entities_proto",
        ":auth_proto",
    ],
)

py_grpc_library(
    name = "auth_py_proto",
    protos = [":auth_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

py_grpc_library(
    name = "front_end_token_service_py_proto",
    protos = [":front_end_token_service_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

py_grpc_library(
    name = "auth_entities_py_proto",
    protos = [":auth_entities_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

ts_proto_library(
    name = "auth_entities_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":auth_entities_proto",
    visibility = ["//services:__subpackages__"],
)

ts_proto_library(
    name = "auth_ts_proto",
    copy_files = True,
    data = [
        "//services/auth/central/server:auth_entities_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":auth_proto",
    visibility = ["//services:__subpackages__"],
)

go_proto_library(
    name = "auth_entities_go_proto",
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_entities",
    proto = ":auth_entities_proto",
    visibility = ["//services:__subpackages__"],
)

go_proto_library(
    name = "auth_internal_go_proto",
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_internal",
    proto = ":auth_internal_proto",
    visibility = ["//services/auth:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/tenant_watcher:tenant_watcher_go_proto",
    ],
)

rust_library(
    name = "auth_entities_rs_proto",
    srcs = ["auth_entities.rs"],
    aliases = aliases(),
    crate_name = "auth_entities_proto",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":auth_entities_rs_proto_gen",
    ],
)

cargo_build_script(
    name = "auth_entities_rs_proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        ":auth_entities_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

py_library(
    name = "auth_central_test_setup",
    testonly = True,
    srcs = [
        "auth_central_test_setup.py",
        "bigtable_connector.py",
    ],
    data = [
        ":grpc_server",
    ],
    pyright_extra_args = {
        "reportMissingParameterType": True,
    },
    visibility = ["//services/auth:__subpackages__"],
    deps = [
        ":front_end_token_service_py_proto",
        ":server",
        "//base/test_utils:bigtable_emulator",
        "//base/test_utils:bigtable_setup",
        "//base/test_utils:process",
        requirement("beautifulsoup4"),
        requirement("cryptography"),
        requirement("google-cloud-bigtable"),
        requirement("google-cloud-pubsub"),
        requirement("grpcio"),
        requirement("pyjwt"),
    ],
)

go_grpc_library(
    name = "auth_go_grpc",
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth",
    proto = ":auth_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        ":auth_entities_go_proto",
    ],
)

go_grpc_library(
    name = "auth_dev_go_grpc",
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_dev",
    proto = ":auth_dev_proto",
    deps = [
        ":auth_entities_go_proto",
    ],
)

go_grpc_library(
    name = "front_end_token_service_go_grpc",
    importpath = "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service",
    proto = ":front_end_token_service_proto",
    deps = [
        ":auth_entities_go_proto",
        ":auth_go_grpc",
    ],
)

go_library(
    name = "grpc_server_library_go",
    srcs = [
        "async_ops_publisher.go",
        "async_ops_worker.go",
        "auth_dao.go",
        "auth_dev_servicer.go",
        "auth_servicer.go",
        "billing_event_processor.go",
        "billing_util.go",
        "config.go",
        "front_end_token_service.go",
        "invitation_email.go",
        "invitation_resolution.go",
        "main.go",
        "orb_util.go",
        "recaptcha_validator.go",
        "revoker.go",
        "run_user_security_checks.go",
        "signup_limiter.go",
        "stripe.go",
        "stripe_event_processor.go",
        "subscription_creation.go",
        "subscriptions_handler.go",
        "team_management.go",
        "team_plan_change.go",
        "tenant_creation.go",
        "tenant_map.go",
        "update_subscription.go",
        "user_tier_change.go",
        "utils.go",
    ],
    importpath = "github.com/augmentcode/augment/services/auth/central/server",
    visibility = ["//services/auth:__subpackages__"],
    deps = [
        ":auth_dev_go_grpc",
        ":auth_entities_go_proto",
        ":auth_go_grpc",
        ":auth_internal_go_proto",
        ":front_end_token_service_go_grpc",
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/logging/audit:audit_go",
        "//base/tracing/go:tracing_go",
        "//services/auth/billing_webhook/orb_event:orb_event_go_proto",
        "//services/auth/central/server/test_utils",
        "//services/auth/stripe_webhook:stripe_event_go_grpc",
        "//services/integrations/customerio:customerio_lib",
        "//services/integrations/orb:orb_lib",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/util:go_lib",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_googleapis_gax_go_v2//:gax-go",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_orbcorp_orb_go//:orb-go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_stripe_stripe_go_v80//:go_default_library",
        "@com_github_stripe_stripe_go_v80//charge",
        "@com_github_stripe_stripe_go_v80//checkout/session",
        "@com_github_stripe_stripe_go_v80//customer",
        "@com_github_stripe_stripe_go_v80//paymentmethod",
        "@com_github_stripe_stripe_go_v80//setupintent",
        "@com_github_stripe_stripe_go_v80//subscription",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@com_google_cloud_go_recaptchaenterprise_v2//apiv1:go_default_library",
        "@com_google_cloud_go_recaptchaenterprise_v2//apiv1/recaptchaenterprisepb:go_default_library",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//credentials:go_default_library",
        "@org_golang_google_grpc//health:go_default_library",
        "@org_golang_google_grpc//health/grpc_health_v1:go_default_library",
        "@org_golang_google_grpc//keepalive:go_default_library",
        "@org_golang_google_grpc//peer:go_default_library",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/emptypb:go_default_library",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_x_sync//errgroup",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "grpc_server",
    embed = [":grpc_server_library_go"],
)

go_oci_image(
    name = "grpc_image",
    package_name = package_name(),
    binary = ":grpc_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

TEST_DEPS_GO = [
    ":auth_go_grpc",
    "//base/feature_flags:feature_flags_go",
    "//base/test_utils/bigtable_utils:bigtable_utils_go",
    "//base/test_utils/pubsub:pubsub_emulator_go",
    "//services/auth/central/server/test_utils",
    "//services/lib/grpc/auth:grpc_auth_go",
    "//services/tenant_watcher:tenant_watcher_go_proto",
    "//services/tenant_watcher/client:client_go",
    "//services/integrations/orb:orb_lib",
    "@com_github_stripe_stripe_go_v80//:go_default_library",
    "@com_github_stretchr_testify//assert",
    "@com_github_stretchr_testify//mock",
    "@com_github_stretchr_testify//require",
    "@com_github_stretchr_testify//suite",
    "@org_golang_google_api//option",
    "@org_golang_google_protobuf//proto",
    "@org_golang_google_grpc//:go_default_library",
    "@org_golang_google_grpc//credentials/insecure:go_default_library",
    "@org_golang_google_grpc//metadata:go_default_library",
    "//services/auth/stripe_webhook:stripe_event_go_grpc",
    "@com_github_google_uuid//:uuid",
    "@com_google_cloud_go_pubsub//:pubsub",
    "@com_github_rs_zerolog//:zerolog",
    "@com_github_rs_zerolog//log",
    "@org_golang_x_sync//errgroup",
    "@com_github_prometheus_client_golang//prometheus/testutil",
    "@com_github_googleapis_gax_go_v2//:gax-go",
]

go_test(
    name = "auth_dao_test_go",
    size = "small",
    srcs = [
        "auth_dao_test.go",
        "auth_fixtures.go",
        "test_utils.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "auth_dev_servicer_test_go",
    size = "small",
    srcs = [
        "auth_dev_servicer_test.go",
        "auth_fixtures.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "auth_servicer_test_go",
    size = "small",
    srcs = [
        "auth_fixtures.go",
        "auth_servicer_test.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "tenant_map_test_go",
    srcs = [
        "auth_fixtures.go",
        "tenant_map_test.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "run_user_security_checks_test_go",
    srcs = [
        "auth_fixtures.go",
        "run_user_security_checks_test.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "signup_limiter_test_go",
    srcs = [
        "auth_fixtures.go",
        "signup_limiter_test.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "front_end_token_service_test_go",
    size = "small",
    srcs = [
        "auth_fixtures.go",
        "front_end_token_service_test.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "user_tier_change_test_go",
    srcs = [
        "auth_fixtures.go",
        "user_tier_change_test.go",
    ],
    embed = [":grpc_server_library_go"],
    importpath = "github.com/augmentcode/augment/services/auth/central/server/auth_server",
    deps = TEST_DEPS_GO,
)

go_test(
    name = "team_plan_change_test_go",
    srcs = [
        "auth_fixtures.go",
        "team_plan_change_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "team_management_test_go",
    srcs = [
        "auth_fixtures.go",
        "team_management_test.go",
        "test_utils.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "recaptcha_validator_test_go",
    srcs = [
        "recaptcha_validator_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "async_ops_publisher_test_go",
    srcs = [
        "async_ops_publisher_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "tenant_creation_test_go",
    srcs = [
        "auth_fixtures.go",
        "tenant_creation_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "utils_test_go",
    srcs = [
        "auth_fixtures.go",
        "utils_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

test_suite(
    name = "all_tests_go",
    tags = ["manual"],
    tests = [
        ":async_ops_publisher_test_go",
        ":auth_dao_test_go",
        ":auth_dev_servicer_test_go",
        ":auth_servicer_test_go",
        ":billing_event_processor_test_go",
        ":billing_util_test_go",
        ":front_end_token_service_test_go",
        ":invitation_email_test_go",
        ":invitation_resolution_test_go",
        ":orb_util_test_go",
        ":recaptcha_validator_test_go",
        ":signup_limiter_test_go",
        ":stripe_event_processor_test_go",
        ":stripe_test_go",
        ":subscription_creation_test_go",
        ":team_management_test_go",
        ":team_plan_change_test_go",
        ":tenant_creation_test_go",
        ":tenant_map_test_go",
        ":update_subscription_test_go",
        ":user_tier_change_test_go",
    ],
)

go_test(
    name = "stripe_event_processor_test_go",
    srcs = [
        "auth_fixtures.go",
        "stripe_event_processor_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "billing_event_processor_test_go",
    srcs = [
        "auth_fixtures.go",
        "billing_event_processor_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "stripe_test_go",
    srcs = [
        "stripe_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "invitation_resolution_test_go",
    srcs = [
        "auth_fixtures.go",
        "invitation_resolution_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "invitation_email_test_go",
    srcs = [
        "auth_fixtures.go",
        "invitation_email_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO + [
        "//services/integrations/customerio:customerio_lib",
    ],
)

go_test(
    name = "subscription_creation_test_go",
    srcs = [
        "auth_fixtures.go",
        "subscription_creation_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "orb_util_test_go",
    srcs = [
        "auth_fixtures.go",
        "orb_util_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "update_subscription_test_go",
    srcs = [
        "auth_fixtures.go",
        "update_subscription_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

go_test(
    name = "billing_util_test_go",
    srcs = [
        "auth_fixtures.go",
        "billing_util_test.go",
    ],
    embed = [":grpc_server_library_go"],
    deps = TEST_DEPS_GO,
)

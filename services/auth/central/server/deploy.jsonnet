local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local orbConfigFunc = import 'services/deploy/configs/orb.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

local writeKeys = std.parseYaml(importstr 'services/auth/central/server/write_key.yaml');

// K8S deployment file for the auth web UI
function(env, namespace, cloud, namespace_config)
  local appName = 'auth-central';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local nginx_config_str = (importstr './services/auth/central/server/nginx.conf');
  local endpoints = import './services/deploy/endpoints.jsonnet';

  local auth_hostname = endpoints.get_auth_hostname(env, namespace, cloud);
  local oauth_host = if namespace_config.flags.loadBalancerType == 'ingress' then endpoints.get_oauth_v2_hostname(env, namespace, cloud) else 'oauth2-v2-proxy';
  local login_base_url = 'https://%s/login' % auth_hostname;
  local oauth_url = 'https://%s/oauth2/auth' % oauth_host;
  local auth0_login_url = 'https://%s/oauth2/start' % oauth_host;

  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local monitoringAccess = gcpLib.grantAccess(
    name='%s-monitoring-grant' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'Project',
      external: cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/monitoring.metricWriter',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
    abandon=true,
  );

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);
  local requestInsightPublisher = (import 'services/request_insight/publisher/publisher_lib.jsonnet')(
    cloud, env, namespace, appName
  );
  local requestInsightPublisherAccess = requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName);

  local auth_ui_gatekeeper_config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'auth-central-gatekeeper-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    data: {
      'nginx.conf': nginx_config_str % {
        login_base_url: login_base_url,
        oauth_url: oauth_url,
        oauth_host: oauth_host,
      },
    },
  };

  local grpcService = grpcLib.createGrpcService(appName=appName, serviceBaseName='auth-central-grpc', namespace=namespace);
  local globalGrpcHostnames = grpcLib.globalGrpcServiceHostnames(cloud=cloud, serviceName='auth-rpc', namespace=namespace);
  local globalGrpcService = grpcLib.globalGrpcService(cloud=cloud, appName=appName, serviceBaseName='auth-rpc', namespace=namespace);
  local backendConfig = gcpLib.createBackendConfig(
    app=appName,
    cloud=cloud,
    namespace=namespace,
    healthCheck={
      checkIntervalSec: 15,
      port: 5000,
      type: 'HTTP',
      requestPath: '/health',
    }
  );
  local restService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'auth-central-svc',
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 80,
          targetPort: 'http-gatekeeper',
        },
      ],
    },
  };

  local loginPage = {
    auth0_login_url: auth0_login_url,
  };

  local internalServerCert =
    certLib.createCentralServerCert(
      name='auth-central-grpc-cert',
      namespace=namespace,
      env=env,
      appName=appName,
      volumeName='internal-server-cert',
      dnsNames=
      grpcLib.grpcServiceNames(serviceBaseName='auth-central-grpc', namespace=namespace) +
      globalGrpcHostnames,
    );
  local clientCert =
    certLib.createCentralClientCert(
      name='auth-central-client-cert',
      namespace=namespace,
      env=env,
      appName=appName,
      volumeName='client-certs',
      dnsNames=
      grpcLib.grpcServiceNames(serviceBaseName='auth-central-grpc', namespace=namespace) +
      globalGrpcHostnames,
    );

  local columnFamily = import 'services/auth/central/server/column_family.json';
  local bigtable = gcpLib.createBigtableTable(cloud=cloud,
                                              env=env,
                                              app=appName,
                                              namespace=namespace,
                                              tableName='auth-central',
                                              columnFamily=columnFamily.items,
                                              iamServiceAccountName=serviceAccount.iamServiceAccountName);

  local cookieSigningSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='cookie-signing',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local auth0LoginCredentials = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='auth0-login',
    version='2',
    serviceAccount=serviceAccount,
  );

  local auth0SignupCredentials = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='auth0-signup',
    version='1',
    serviceAccount=serviceAccount,
  );

  local stripeSecretKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='stripe',
    version={
      PROD: '1',
      STAGING: '3',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local verosintApiKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='verosint-api-key',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local verisoulApiKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='verisoul-api-key',
    version={
      PROD: '2',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  // Create the reCAPTCHA keys secret
  local recaptchaKeys = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='recaptcha-keys',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local recaptchaGrant = gcpLib.grantAccess(
    env=env,
    namespace=namespace,
    appName=appName,
    name=appName + '-recaptcha-grant',
    resourceRef={
      kind: 'Project',
      external: 'projects/%s' % cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/recaptchaenterprise.agent',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
  );

  // Create the Orb API key secret in all environments
  local orbApiKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='orb',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  // Create the customer.io API key secret in all environments
  local customerioApiKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='customerio-api-key',
    version={
      PROD: '2',
      STAGING: '2',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  // Create the e2e testing secret for customer UI automation
  local e2eCustomerUiSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='e2e-customerui',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local auth0WebhookSubscriber = pubsubLib.namespaceSubscriber(
    env=env,
    namespace=namespace,
    appName=appName,
    cloud=cloud,
    publisherAppName='auth0-webhook',
    serviceAccount=serviceAccount,
    externalTopicRef=true,
  );

  local stripeEventSubscriber = pubsubLib.namespaceSubscriber(
    env=env,
    namespace=namespace,
    appName='auth-central-stripe-event-processor',
    cloud=cloud,
    publisherAppName='stripe-webhook',
    serviceAccount=serviceAccount,
    deadLetterMaxDeliveryAttempts=5,
    spec={
      retryPolicy: {
        minimumBackoff: '60s',
        maximumBackoff: '300s',
      },
    },
    externalTopicRef=true,
  );

  local billingEventSubscriber = pubsubLib.namespaceSubscriber(
    env=env,
    namespace=namespace,
    appName='auth-central-billing-event-processor',
    cloud=cloud,
    publisherAppName='billing-webhook',
    serviceAccount=serviceAccount,
    deadLetterMaxDeliveryAttempts=5,
    spec={
      retryPolicy: {
        minimumBackoff: '60s',
        maximumBackoff: '300s',
      },
    },
    externalTopicRef=true,
  );

  local asyncOpsTopic = pubsubLib.publisherTopic(
    cloud=cloud,
    env=env,
    namespace=namespace,
    appName='auth-central-async-ops',
    serviceAccount=serviceAccount,
  );

  local asyncOpsSubscriber = pubsubLib.namespaceSubscriber(
    env=env,
    namespace=namespace,
    appName='auth-central-async-ops',
    cloud=cloud,
    publisherAppName='auth-central-async-ops',
    serviceAccount=serviceAccount,
    deadLetterMaxDeliveryAttempts=5,
    spec={
      retryPolicy: {
        minimumBackoff: '10s',
        maximumBackoff: '300s',
      },
    },
  );

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local orbConfig = orbConfigFunc(env);
  local auth_ui_config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'auth-central-config',
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJson({
        auth_config: {
          token_exchange_endpoint: endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
        },
        backend_port: 50061,
        client_config_map: {
          'augment-vscode-extension': {
            name: 'Visual Studio Code',
            redirect_uris: [
              'vscode://augment.vscode-augment/auth/result',
              'cursor://augment.vscode-augment/auth/result',
              'vscode-insiders://augment.vscode-augment/auth/result',
              'code-oss://augment.vscode-augment/auth/result',
              'haystack-editor://augment.vscode-augment/auth/result',
              'trae://augment.vscode-augment/auth/result',
              'windsurf://augment.vscode-augment/auth/result',
              'windsurf-next://augment.vscode-augment/auth/result',
              'codellm://augment.vscode-augment/auth/result',
              'flexpilot://augment.vscode-augment/auth/result',
              'aide://augment.vscode-augment/auth/result',
              'pearai://augment.vscode-augment/auth/result',
              'vscodium://augment.vscode-augment/auth/result',
              'cursor-nightly://augment.vscode-augment/auth/result',
              'positron://augment.vscode-augment/auth/result',
              'void-editor://augment.vscode-augment/auth/result',
              'trae-cn://augment.vscode-augment/auth/result',
              'void://augment.vscode-augment/auth/result',
            ],
            expiration_time_seconds: 0,
          },
          'augment-intellij-plugin': {
            name: 'Intellij',
            redirect_uris: [
              'http://127.0.0.1/api/augment/auth/result',
            ],
            expiration_time_seconds: 0,
          },
          'augment-vim-extension': {
            name: 'Vim',
            redirect_uris: [''],
            expiration_time_seconds: 0,
          },
          invitations: {
            name: 'Team Invitations',
            redirect_uris: [''],
            expiration_time_seconds: 0,
          },
          v: {  // To facilitate shorter of auth urls for vim, allow this alternate client id
            name: 'Vim',
            redirect_uris: [''],
            expiration_time_seconds: 0,
          },
          'customer-ui': {
            name: 'Augment Dashboard',  // for customer-ui
            redirect_uris: {
              PROD: [
                'https://app.augmentcode.com/auth/callback',
                'https://app.augmentcode.com/logout/complete',
              ],
              STAGING: [
                'https://app.staging.augmentcode.com/auth/callback',
                'https://app.staging.augmentcode.com/logout/complete',
              ],
              DEV: [
                'https://app.%(tenant)s.%(internal_suffix)s/auth/callback' % { tenant: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
                'https://app.%(tenant)s.%(internal_suffix)s/logout/complete' % { tenant: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
                'http://localhost/auth/callback',
                'http://localhost/logout/complete',
              ],
            }[env],
            expiration_time_seconds: 0,  // Not used - this is a special case
            instant_redirect: true,
          },
          www: {
            name: 'www.augmentcode.com',
            redirect_uris: {
              PROD: [
                'https://www.augmentcode.com/auth/callback',
                'https://www.augmentcode.com/logout/complete',
              ],
              STAGING: [
                'https://www.augmentcode.com/auth/callback',
                'https://www.augmentcode.com/logout/complete',
              ],
              DEV: [
                'https://www.augmentcode.com/auth/callback',
                'https://www.augmentcode.com/logout/complete',
              ],
            }[env],
            expiration_time_seconds: 900,  // 15 minutes
          },
        } + if env != 'DEV' then {} else {
          'augment-web-ui': {
            name: 'Augment Web UI',
            redirect_uris: {
              // TODO(mattm,amelia): PROD, STAGING
              DEV: [
                'https://app.%(tenant)s.%(internal_suffix)s/auth/callback' % { tenant: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
                'https://app.%(tenant)s.%(internal_suffix)s/logout/complete' % { tenant: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
                'http://localhost/auth/callback',
                'http://localhost/logout/complete',
              ],
            }[env],
            expiration_time_seconds: 0,  // Not used - this is a special case
            instant_redirect: true,
          },
        },
        code_ttl_seconds: 600,  // 10 minutes, as recommended in RFC 6749
        dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
        feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
        gcp: {
          instance_id: bigtable.instanceName,
          table_name: bigtable.tableName,
          setup_table: false,
        },
        grpc: if !mtls then {} else {
          client_mtls: clientCert.config,
        },
        login_page: loginPage,
        public_bind_address: '0.0.0.0:5000',
        prometheus_bind_address: '0.0.0.0:9090',
        request_insight_publisher_config_path: requestInsightPublisher.configFilePath,
        tenant_watcher: {
          tenant_watcher_endpoint: endpoints.getTenantWatcherGrpcUrl(env, namespace, cloud),
          api_proxy_hostname_domain: cloudInfo[cloud].apiDomain,
        },
        auth_url: 'https://%s' % auth_hostname,
        customer_ui_url: 'https://%s' % endpoints.getCustomerUiHostname(env=env, namespace=namespace, cloud=cloud),
        staging_url: if env == 'PROD' then 'https://%s' % endpoints.get_auth_hostname(env='STAGING', namespace='', cloud=cloud) else null,
        staging_user_email_regex: if env == 'PROD' then '^[a-zA-Z0-9\\._%+-]+@(augmentcode)\\.com$' else null,
        allowed_discovery_user_email_regex: if env == 'STAGING' || env == 'DEV' then '^[a-zA-Z0-9\\._%+-]+@augm\\.io$' else null,
        e2e_customerui_secret_path: e2eCustomerUiSecret.filePath,
        secrets_path: cookieSigningSecret.filePath,
        recaptcha_config: {
          keys_path: recaptchaKeys.filePath,
          project_id: cloudInfo[cloud].projectId,
        },
        verisoul: {
          api_key_path: verisoulApiKey.filePath,
          project_id: {
            PROD: '9d08ce27-3d9b-4737-b477-e57c7446255b',
            STAGING: '28401750-21c0-41a9-9ee4-3c3598171c9d',
            DEV: '28401750-21c0-41a9-9ee4-3c3598171c9d',
          }[env],
          env: {
            PROD: 'prod',
            STAGING: 'sandbox',
            DEV: 'sandbox',
          }[env],
        },
        signup_tenant: {
          DEV: 'augment',
          STAGING: null,
          PROD: null,  // i0+
        }[env],
        individual_tenant: {
          DEV: 'augment',
          STAGING: null,
          PROD: null,  // d0+
        }[env],
        login_auth0: {
          credentials_path: auth0LoginCredentials.filePath,
          server_metadata_url: 'https://%s/.well-known/openid-configuration' % {
            PROD: 'login.augmentcode.com',
            STAGING: 'login.augmentcode.com',
            DEV: 'login.dev.augmentcode.com',
          }[env],
        },
        signup_auth0: {
          credentials_path: auth0SignupCredentials.filePath,
          server_metadata_url: 'https://%s/.well-known/openid-configuration' % {
            PROD: 'login.augmentcode.com',
            STAGING: 'login.augmentcode.com',
            DEV: 'login.dev.augmentcode.com',
          }[env],
        },
        segment: {
          write_key: writeKeys[env].segment_write_key,
          host: writeKeys[env].host,
          enabled: true,
        },
        dev_callback_url: if env == 'DEV' then 'https://auth-dev-callback.us-central1.dev.augmentcode.com/oauth2/callback',
      }),
    },
  };
  local auth_grpc_config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'auth-central-grpc-config',
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: appName,
      },
    },
    data: {
      'config-grpc.json': std.manifestJson({
        auth_config: {
          token_exchange_endpoint: endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
        },
        code_ttl_seconds: 600,  // 10 minutes, as recommended in RFC 6749
        dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
        feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
        request_insight_publisher_config_path: requestInsightPublisher.configFilePath,
        auth_central_hostname: 'https://%s' % auth_hostname,
        gcp: {
          instance_id: bigtable.instanceName,
          project_id: bigtable.projectId,
          table_name: bigtable.tableName,
          setup_table: false,
        },
        grpc: {
          ports: [50051],
          private_port: 50061,
          max_server_workers: 32,
          enabled: true,
        } + if !mtls then {} else {
          server_mtls: internalServerCert.config,
          client_mtls: clientCert.config,
        },
        prometheus_bind_address: '0.0.0.0:9091',
        stripe: {
          secret_key_path: stripeSecretKey.filePath,
          enabled: true,
        },
        orb: {
          api_key_path: orbApiKey.filePath,
          enabled: true,
          seats_item_id: orbConfig.seats_item_id[env],
          included_messages_item_id: orbConfig.included_messages_item_id[env],
          pricing_unit: orbConfig.pricing_unit,
          cost_per_message: orbConfig.cost_per_message,
          plans: orbConfig.plans,
          min_addon_purchase: orbConfig.min_addon_purchase,
          max_addon_purchase: orbConfig.max_addon_purchase,
          professional_plan_id: orbConfig.professional_plan_id,
          stripe_professional_plan_price_per_seat: orbConfig.stripe_professional_plan_price_per_seat,
          developer_plan_for_stripe_users: orbConfig.developer_plan_for_stripe_users[env],
        },
        customerio_api_key_path: customerioApiKey.filePath,
        tenant_watcher: {
          tenant_watcher_endpoint: endpoints.getTenantWatcherGrpcUrl(env, namespace, cloud),
          api_proxy_hostname_domain: cloudInfo[cloud].apiDomain,
        },
        revoker: {
          subscription_name: auth0WebhookSubscriber.subscriptionName,
        },
        async_ops: {
          topic_name: asyncOpsTopic.topicName,
          subscription_name: asyncOpsSubscriber.subscriptionName,
          dead_letter_subscription_name: asyncOpsSubscriber.deadLetterSubscriptionName,
          max_concurrent_receivers: 10,
          dead_letter_max_concurrent_receivers: 2,
        },
        stripe_event_processor: {
          subscription_name: stripeEventSubscriber.subscriptionName,
          dead_letter_subscription_name: stripeEventSubscriber.deadLetterSubscriptionName,
          max_concurrent_receivers: 10,
          dead_letter_max_concurrent_receivers: 2,
        },
        billing_event_processor: {
          subscription_name: billingEventSubscriber.subscriptionName,
          dead_letter_subscription_name: billingEventSubscriber.deadLetterSubscriptionName,
          max_concurrent_receivers: 10,
          dead_letter_max_concurrent_receivers: 2,
        },
        verosint: {
          api_key_path: verosintApiKey.filePath,
          endpoint: 'https://api.verosint.com/v1/rules/evaluate',
          timeout: '10s',
        },
        recaptcha_config: {
          keys_path: recaptchaKeys.filePath,
          project_id: cloudInfo[cloud].projectId,
        },
        namespace: namespace,
        dev_servicer_enabled: env == 'DEV',
      }),
    },
  };
  local auth_container =
    {
      name: appName,
      target: {
        name: '//services/auth/central/server:image',
        dst: appName,
      },
      args: [
        '--config',
        '/config/config.json',
      ],
      ports: [
        {
          containerPort: 5000,
          name: 'http-svc',
        },
      ],
      env: lib.flatten([
        {
          name: 'PROMETHEUS_MULTIPROC_DIR',
          value: '/tmp/prometheus_multiproc_dir',
        },
        telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
        dynamicFeatureFlags.env,
      ]),
      volumeMounts: [
        {
          name: 'auth-central-config',
          mountPath: '/config',
          readOnly: true,
        },
        {
          name: 'prometheus-multiproc-dir',
          mountPath: '/tmp/prometheus_multiproc_dir',
        },
        internalServerCert.volumeMountDef,
        clientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        cookieSigningSecret.volumeMountDef,
        auth0SignupCredentials.volumeMountDef,
        auth0LoginCredentials.volumeMountDef,
        stripeSecretKey.volumeMountDef,
        recaptchaKeys.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        verisoulApiKey.volumeMountDef,
        e2eCustomerUiSecret.volumeMountDef,
      ],
      readinessProbe: {
        httpGet: {
          path: '/health',
          port: 5000,
          scheme: 'HTTP',
        },
        periodSeconds: 30,
      },
      livenessProbe: {
        httpGet: {
          path: '/health',
          port: 5000,
          scheme: 'HTTP',
        },
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 0.5,
          memory: '512Mi',
        },
      },
    };

  local auth_grpc_container = {
    name: 'auth-central-grpc',
    target: {
      name: '//services/auth/central/server:grpc_image',
      dst: 'auth-central-grpc',
    },
    args: [
      '--config',
      '/config/config-grpc.json',
    ],
    ports: [
      {
        containerPort: 50051,
        name: grpcService.portDetails['50051'].targetPort,
      },
      {
        containerPort: 9091,
        name: 'metrics',
      },
    ],
    env: lib.flatten([
      telemetryLib.telemetryEnv('auth-central-grpc', telemetryLib.collectorUri(env, namespace, cloud)),
      dynamicFeatureFlags.env,
    ]),
    volumeMounts: [
      {
        name: 'auth-central-grpc-config',
        mountPath: '/config',
        readOnly: true,
      },
      internalServerCert.volumeMountDef,
      clientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      requestInsightPublisher.volumeMountDef,
      cookieSigningSecret.volumeMountDef,
      auth0SignupCredentials.volumeMountDef,
      stripeSecretKey.volumeMountDef,
      recaptchaKeys.volumeMountDef,
      verisoulApiKey.volumeMountDef,
      verosintApiKey.volumeMountDef,
      orbApiKey.volumeMountDef,
      customerioApiKey.volumeMountDef,
      e2eCustomerUiSecret.volumeMountDef,
    ],
    readinessProbe: grpcLib.grpcHealthCheck('auth-central-grpc-svc', tls=mtls, serverCerts=internalServerCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck('auth-central-grpc-svc', tls=mtls, serverCerts=internalServerCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    resources: {
      limits: {
        // We see spikes up to 2.5 CPUs during the sync job
        cpu: 3,
        // Raised to 2Gi because of OOMs seen 2025/05/09
        memory: '2Gi',
      },
    },
  };

  local nginx_container = {
    name: 'nginx',
    image: 'nginx:1.25.3-alpine',
    resources: {
      limits: {
        cpu: 0.2,
        memory: '512Mi',
      },
    },
    volumeMounts: [
      {
        name: 'auth-central-gatekeeper-volume',
        mountPath: '/etc/nginx/',
        readOnly: true,
      },
    ],
    ports: [
      {
        containerPort: 80,
        name: 'http-gatekeeper',
      },
    ],

    livenessProbe: {
      httpGet: {
        path: '/healthcheck',
        port: 80,
      },
      initialDelaySeconds: 3,
      timeoutSeconds: 3,
      failureThreshold: 2,
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      auth_container,
      auth_grpc_container,
      nginx_container,
    ],
    tolerations: tolerations,
    affinity: affinity,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    volumes: [
      {
        name: 'auth-central-gatekeeper-volume',
        configMap: {
          name: 'auth-central-gatekeeper-config',
          items: [
            {
              key: 'nginx.conf',
              path: 'nginx.conf',
            },
          ],
        },
      },
      {
        name: 'auth-central-grpc-config',
        configMap: {
          name: 'auth-central-grpc-config',
          items: [
            {
              key: 'config-grpc.json',
              path: 'config-grpc.json',
            },
          ],
        },
      },
      {
        name: 'auth-central-config',
        configMap: {
          name: 'auth-central-config',
          items: [
            {
              key: 'config.json',
              path: 'config.json',
            },
          ],
        },
      },
      internalServerCert.podVolumeDef,
      clientCert.podVolumeDef,
      {
        name: 'prometheus-multiproc-dir',
        emptyDir: {},
      },
      dynamicFeatureFlags.podVolumeDef,
      requestInsightPublisher.podVolumeDef,
      cookieSigningSecret.podVolumeDef,
      auth0SignupCredentials.podVolumeDef,
      auth0LoginCredentials.podVolumeDef,
      stripeSecretKey.podVolumeDef,
      recaptchaKeys.podVolumeDef,
      verisoulApiKey.podVolumeDef,
      verosintApiKey.podVolumeDef,
      orbApiKey.podVolumeDef,
      customerioApiKey.podVolumeDef,
      e2eCustomerUiSecret.podVolumeDef,
    ],
  };
  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: 'auth-central-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'auth-ssl-cert',  // pragma: allowlist secret
            hosts: [auth_hostname],
          },
        ],
        rules: [
          {
            host: auth_hostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'auth-central-svc',
                      port: {
                        number: 80,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: if env == 'DEV' then 1 else 4,
      minReadySeconds: if env == 'DEV' then 0 else 30,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);

  // Additional monitoring for auth-central-grpc container.
  local grpc_service_monitoring = {
    apiVersion: 'monitoring.googleapis.com/v1',
    kind: 'PodMonitoring',
    metadata: {
      name: 'auth-central-grpc-monitoring',
      namespace: namespace,
      labels: {
        app: appName,
        'app.kubernetes.io/managed-by': 'kubecfg',
      },
    },
    spec: {
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      endpoints: [
        {
          port: 'metrics',
          interval: '60s',
        },
      ],
    },
  };

  lib.flatten([
    auth_ui_gatekeeper_config,
    auth_ui_config,
    auth_grpc_config,
    restService,
    deployment,
    grpcService.objects,
    globalGrpcService,
    serviceAccount.objects,
    dynamicFeatureFlags.k8s_objects,
    bigtable.objects,
    if namespace_config.flags.loadBalancerType == 'ingress' then ingressObjects else [],
    internalServerCert.objects,
    clientCert.objects,
    pbd,
    cookieSigningSecret.objects,
    auth0LoginCredentials.objects,
    auth0SignupCredentials.objects,
    stripeSecretKey.objects,
    orbApiKey.objects,
    customerioApiKey.objects,
    recaptchaKeys.objects,
    recaptchaGrant,
    grpc_service_monitoring,
    monitoringAccess,
    auth0WebhookSubscriber.objects,
    stripeEventSubscriber.objects,
    billingEventSubscriber.objects,
    asyncOpsTopic.objects,
    asyncOpsSubscriber.objects,
    requestInsightPublisherAccess,
    verisoulApiKey.objects,
    verosintApiKey.objects,
    e2eCustomerUiSecret.objects,
  ])

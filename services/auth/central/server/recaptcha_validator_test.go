package main

import (
	"context"
	"errors"
	"testing"

	"cloud.google.com/go/recaptchaenterprise/v2/apiv1/recaptchaenterprisepb"
	gax "github.com/googleapis/gax-go/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRecaptchaClient is a mock implementation of the RecaptchaClient interface
type MockRecaptchaClient struct {
	mock.Mock
}

func (m *MockRecaptchaClient) CreateAssessment(ctx context.Context, req *recaptchaenterprisepb.CreateAssessmentRequest, opts ...gax.CallOption) (*recaptchaenterprisepb.Assessment, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*recaptchaenterprisepb.Assessment), args.Error(1)
}

func (m *MockRecaptchaClient) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestRecaptchaValidator_Allow(t *testing.T) {
	t.Run("negative threshold bypasses validation", func(t *testing.T) {
		validator := &RecaptchaValidatorImpl{
			client:    nil, // Should not be called
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		result := validator.Allow(context.Background(), "any-token", "any-action", "127.0.0.1", "test-agent", -1.0, "<EMAIL>")
		assert.True(t, result)
	})

	t.Run("empty token returns false", func(t *testing.T) {
		validator := &RecaptchaValidatorImpl{
			client:    nil, // Should not be called since token is empty
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		result := validator.Allow(context.Background(), "", "test-action", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.False(t, result)
	})

	t.Run("zero threshold allows when score is nil", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock assessment with invalid token (score will be nil)
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "invalid-token"
		})).Return(&recaptchaenterprisepb.Assessment{
			TokenProperties: &recaptchaenterprisepb.TokenProperties{
				Valid: false,
			},
		}, nil)

		result := validator.Allow(context.Background(), "invalid-token", "test-action", "127.0.0.1", "test-agent", 0.0, "<EMAIL>")
		assert.True(t, result)
		mockClient.AssertExpectations(t)
	})

	t.Run("positive threshold rejects when score is nil", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock assessment with invalid token (score will be nil)
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "invalid-token"
		})).Return(&recaptchaenterprisepb.Assessment{
			TokenProperties: &recaptchaenterprisepb.TokenProperties{
				Valid: false,
			},
		}, nil)

		result := validator.Allow(context.Background(), "invalid-token", "test-action", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.False(t, result)
		mockClient.AssertExpectations(t)
	})

	t.Run("valid token with score above threshold allows", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock assessment with valid token and high score
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "valid-token"
		})).Return(&recaptchaenterprisepb.Assessment{
			TokenProperties: &recaptchaenterprisepb.TokenProperties{
				Valid:  true,
				Action: "test-action",
			},
			RiskAnalysis: &recaptchaenterprisepb.RiskAnalysis{
				Score: 0.8,
			},
		}, nil)

		result := validator.Allow(context.Background(), "valid-token", "test-action", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.True(t, result)
		mockClient.AssertExpectations(t)
	})

	t.Run("valid token with score below threshold rejects", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock assessment with valid token but low score
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "low-score-token"
		})).Return(&recaptchaenterprisepb.Assessment{
			TokenProperties: &recaptchaenterprisepb.TokenProperties{
				Valid:  true,
				Action: "test-action",
			},
			RiskAnalysis: &recaptchaenterprisepb.RiskAnalysis{
				Score: 0.2,
			},
		}, nil)

		result := validator.Allow(context.Background(), "low-score-token", "test-action", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.False(t, result)
		mockClient.AssertExpectations(t)
	})

	t.Run("action mismatch rejects", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock assessment with valid token but wrong action
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "wrong-action-token"
		})).Return(&recaptchaenterprisepb.Assessment{
			TokenProperties: &recaptchaenterprisepb.TokenProperties{
				Valid:  true,
				Action: "different-action",
			},
			RiskAnalysis: &recaptchaenterprisepb.RiskAnalysis{
				Score: 0.8,
			},
		}, nil)

		result := validator.Allow(context.Background(), "wrong-action-token", "test-action", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.False(t, result)
		mockClient.AssertExpectations(t)
	})

	t.Run("empty action allows any action", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock assessment with valid token and any action
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "any-action-token"
		})).Return(&recaptchaenterprisepb.Assessment{
			TokenProperties: &recaptchaenterprisepb.TokenProperties{
				Valid:  true,
				Action: "some-action",
			},
			RiskAnalysis: &recaptchaenterprisepb.RiskAnalysis{
				Score: 0.8,
			},
		}, nil)

		result := validator.Allow(context.Background(), "any-action-token", "", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.True(t, result)
		mockClient.AssertExpectations(t)
	})

	t.Run("API error rejects request", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock API error
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "error-token"
		})).Return(nil, errors.New("API error"))

		result := validator.Allow(context.Background(), "error-token", "test-action", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.False(t, result)
		mockClient.AssertExpectations(t)
	})

	t.Run("missing risk analysis rejects", func(t *testing.T) {
		mockClient := &MockRecaptchaClient{}
		validator := &RecaptchaValidatorImpl{
			client:    mockClient,
			projectID: "test-project",
			siteKey:   "test-site-key",
		}

		// Mock assessment with valid token but no risk analysis
		mockClient.On("CreateAssessment", mock.Anything, mock.MatchedBy(func(req *recaptchaenterprisepb.CreateAssessmentRequest) bool {
			return req.Assessment.Event.Token == "no-risk-token"
		})).Return(&recaptchaenterprisepb.Assessment{
			TokenProperties: &recaptchaenterprisepb.TokenProperties{
				Valid:  true,
				Action: "test-action",
			},
			RiskAnalysis: nil, // No risk analysis
		}, nil)

		result := validator.Allow(context.Background(), "no-risk-token", "test-action", "127.0.0.1", "test-agent", 0.5, "<EMAIL>")
		assert.False(t, result)
		mockClient.AssertExpectations(t)
	})
}

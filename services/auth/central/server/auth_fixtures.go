package main

import (
	"context"
	"os"
	"testing"

	"cloud.google.com/go/bigtable"
	"cloud.google.com/go/pubsub"
	bigtable_utils "github.com/augmentcode/augment/base/test_utils/bigtable_utils"
	pubsub_utils "github.com/augmentcode/augment/base/test_utils/pubsub"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/option"
)

const (
	InstanceID = "test-instance"
	TableName  = "test-table"
	ProjectID  = "test-project"
	TenantName = "test-tenant1"
)

// BigtableFixture provides a Bigtable emulator and table
type BigtableFixture struct {
	Ctx          context.Context
	Table        *bigtable.Table
	Cleanup      func()
	TableDetails *bigtable_utils.TableDetails
}

// NewBigtableFixure creates a new test environment with Bigtable emulator
func NewBigtableFixture(t *testing.T) *BigtableFixture {
	t.Helper()
	ctx := context.Background()

	// Start emulator
	hostPort, cleanup, err := bigtable_utils.StartEmulator()
	require.NoError(t, err)
	bigtable_utils.SetupEmulatorEnv(hostPort)

	// Create table with required column families
	tableDetails := bigtable_utils.NewTableDetails("auth-test")
	columnFamilies := []string{
		"User",
		"UserTenantMapping",
		"TokenHash",
		"Code",
		"TermsApproval",
		"TenantCreation",
		"TenantSubscriptionMapping",
		"Subscription",
		"TenantInvitation",
		"InvitationResolution",
		"IDPUserMapping",
	}

	tableCleanup, err := bigtable_utils.CreateTable(ctx, tableDetails, columnFamilies)
	require.NoError(t, err)

	// Create Bigtable client
	client, err := bigtable.NewClient(
		ctx,
		tableDetails.Project,
		tableDetails.Instance,
		option.WithEndpoint(hostPort),
		option.WithoutAuthentication(),
	)
	require.NoError(t, err)

	finalCleanup := func() {
		tableCleanup()
		client.Close()
		cleanup()
		bigtable_utils.CleanupEmulatorEnv()
	}

	return &BigtableFixture{
		Ctx:          ctx,
		Table:        client.Open(tableDetails.TableName),
		Cleanup:      finalCleanup,
		TableDetails: tableDetails,
	}
}

// ClearTable deletes all rows in the table and recreates it
func (f *BigtableFixture) ClearTable(t *testing.T) {
	t.Helper()

	// Delete the table
	adminClient, err := bigtable.NewAdminClient(
		f.Ctx,
		f.TableDetails.Project,
		f.TableDetails.Instance,
		option.WithEndpoint(os.Getenv("BIGTABLE_EMULATOR_HOST")),
		option.WithoutAuthentication(),
	)
	require.NoError(t, err)

	// Delete and recreate the table
	err = adminClient.DeleteTable(f.Ctx, f.TableDetails.TableName)
	require.NoError(t, err)

	// Create table with required column families
	columnFamilies := []string{
		"User",
		"UserTenantMapping",
		"TokenHash",
		"Code",
		"TermsApproval",
		"TenantCreation",
		"TenantSubscriptionMapping",
		"Subscription",
		"TenantInvitation",
		"InvitationResolution",
		"IDPUserMapping",
	}

	err = adminClient.CreateTable(f.Ctx, f.TableDetails.TableName)
	require.NoError(t, err)

	// Create column families
	for _, family := range columnFamilies {
		err = adminClient.CreateColumnFamily(f.Ctx, f.TableDetails.TableName, family)
		require.NoError(t, err)
	}

	adminClient.Close()
}

// PubSubFixture provides a PubSub emulator and client
type PubSubFixture struct {
	Ctx        context.Context
	Client     *pubsub.Client
	ProjectID  string
	Endpoint   string
	Cleanup    func()
	TopicName  string
	Topic      *pubsub.Topic
	SubName    string
	Sub        *pubsub.Subscription
	DLQSubName string
	DLQSub     *pubsub.Subscription
}

// NewPubSubFixture creates a new test environment with PubSub emulator
func NewPubSubFixture(t *testing.T) *PubSubFixture {
	t.Helper()
	ctx := context.Background()

	// Start emulator
	endpoint, cleanup, err := pubsub_utils.StartEmulator(0)
	require.NoError(t, err)

	// Set environment variable for PubSub client
	os.Setenv("PUBSUB_EMULATOR_HOST", endpoint)

	// Create PubSub client
	client, err := pubsub.NewClient(
		ctx,
		ProjectID,
		option.WithEndpoint(endpoint),
		option.WithoutAuthentication(),
	)
	require.NoError(t, err)

	// Generate unique topic and subscription names
	topicName := "test-topic-" + uuid.New().String()
	subName := "test-sub-" + uuid.New().String()
	dlqSubName := "test-dlq-" + uuid.New().String()

	// Create topic
	topic, err := client.CreateTopic(ctx, topicName)
	require.NoError(t, err)

	// Create subscription
	sub, err := client.CreateSubscription(ctx, subName, pubsub.SubscriptionConfig{
		Topic: topic,
	})
	require.NoError(t, err)

	// Create dead letter queue subscription
	dlqSub, err := client.CreateSubscription(ctx, dlqSubName, pubsub.SubscriptionConfig{
		Topic: topic,
	})
	require.NoError(t, err)

	finalCleanup := func() {
		sub.Delete(ctx)
		dlqSub.Delete(ctx)
		topic.Delete(ctx)
		client.Close()
		cleanup()
		os.Unsetenv("PUBSUB_EMULATOR_HOST")
	}

	return &PubSubFixture{
		Ctx:        ctx,
		Client:     client,
		ProjectID:  ProjectID,
		Endpoint:   endpoint,
		Cleanup:    finalCleanup,
		TopicName:  topicName,
		Topic:      topic,
		SubName:    subName,
		Sub:        sub,
		DLQSubName: dlqSubName,
		DLQSub:     dlqSub,
	}
}

// DAOFactory fixture wraps the factory and its dependencies
type DAOFactoryFixture struct {
	bigtableFixture *BigtableFixture
	DAOFactory      *DAOFactory
	Cleanup         func()
}

func NewDAOFactoryFixture(bigtableFixture *BigtableFixture) *DAOFactoryFixture {
	factory := NewDAOFactory(bigtableFixture.Table)

	cleanup := func() {
		bigtableFixture.Cleanup()
	}

	return &DAOFactoryFixture{
		DAOFactory: factory,
		Cleanup:    cleanup,
	}
}

func createAuthorizedRequestContext() context.Context {
	claims := &auth.AugmentClaims{
		TenantID:       "individual-2",
		TenantName:     "individual",
		ShardNamespace: "individual",
		Cloud:          "test-cloud",
		Scope:          []string{"AUTH_RW", "AUTH_R"},
	}
	return claims.NewContext(context.Background())
}

func createCentralAuthorizedRequestContext() context.Context {
	claims := &auth.AugmentClaims{
		Cloud: "test-cloud",
		Scope: []string{"AUTH_RW", "AUTH_R", "PII_ADMIN"},
	}
	return claims.NewContext(context.Background())
}

func createAuthorizedRequestContextForTeam() context.Context {
	claims := &auth.AugmentClaims{
		TenantID:       "self-serve-team-id",
		TenantName:     "self-serve-team",
		ShardNamespace: "individual",
		Cloud:          "test-cloud",
		Scope:          []string{"AUTH_RW", "AUTH_R"},
	}
	return claims.NewContext(context.Background())
}

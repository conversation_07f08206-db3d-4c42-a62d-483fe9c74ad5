import hashlib
import structlog
from random import random
from typing import Any, Callable, Optional

import base.feature_flags
from base.prompt_format.common import (
    RequestMessage,
)
from base.prompt_format.common import (
    Exchange as PromptExchange,
)
from base.third_party_clients.anthropic_direct_client import (
    AnthropicDirectClient,
)
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.third_party_model_client import (
    ModelAPICall,
    ThirdPartyModelClient,
    ToolDefinition,
    ToolChoice,
)

logger = structlog.get_logger()

# API model names
_VERTEXAI_CLAUDE_3_5 = "claude-3-5-sonnet-v2@20241022"
_VERTEXAI_CLAUDE_3_7 = "claude-3-7-sonnet@20250219"
_ANTHROPIC_CLAUDE_3_5 = "claude-3-5-sonnet-20241022"
_ANTHROPIC_CLAUDE_3_7 = "claude-3-7-sonnet-20250219"

_CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_EUROPE_RATE = base.feature_flags.FloatFlag(
    "chat_anthropic_vertexai_load_balance_europe_rate", 0.0
)

_CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_ASIA_RATE = base.feature_flags.FloatFlag(
    "chat_anthropic_vertexai_load_balance_asia_rate", 0.0
)

_CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_MODELS = base.feature_flags.StringFlag(
    "chat_anthropic_vertexai_load_balance_models", ""
)

_CHAT_ANTHROPIC_DIRECT_RATE = base.feature_flags.FloatFlag(
    "chat_anthropic_direct_rate", 0.0
)


def get_pseudo_random_from_session_id(session_id: str) -> float:
    """
    Generate a deterministic pseudo-random value between 0 and 1 based on the session ID.

    Args:
        session_id: The session ID to use as a seed

    Returns:
        A float between 0 and 1
    """

    # Use MD5 to get a consistent hash value from the session ID
    # We're not using this for security, just for deterministic randomness
    hash_object = hashlib.md5(session_id.encode())  # nosec
    hash_hex = hash_object.hexdigest()

    # Convert the first 8 hex characters to an integer and normalize to [0, 1]
    # 8 hex characters = 32 bits = values from 0 to 4,294,967,295
    int_value = int(hash_hex[:8], 16)
    return int_value / 0xFFFFFFFF  # Normalize to [0, 1]


class AnthropicMultiClient(ThirdPartyModelClient):
    """A client that can distribute load between direct Anthropic and VertexAI.

    This is a temporary solution to handle high load by distributing traffic between
    direct Anthropic API and VertexAI. The distribution is controlled by the
    _CHAT_ANTHROPIC_DIRECT_RATE feature flag.

    This implementation avoids correlation issues by using different salts for the
    direct vs. VertexAI decision and the regional decision within VertexAI. This ensures
    that the two decisions are independent and don't create unintended distribution patterns.
    """

    def __init__(
        self,
        api_key,
        project_id,
        model_name,
        temperature,
        max_output_tokens,
    ):
        # Store model names for reference
        self.vertex_model_name = model_name
        self.direct_model_name = self._convert_model_name_for_direct(model_name)

        # Initialize direct client only if we have a valid direct model name
        if self.direct_model_name is not None:
            self.direct_client = AnthropicDirectClient(
                api_key=api_key,
                model_name=self.direct_model_name,
                temperature=temperature,
                max_output_tokens=max_output_tokens,
            )
        else:
            self.direct_client = None
            logger.info(
                "No direct Anthropic mapping for model %s. Will only use VertexAI.",
                self.vertex_model_name,
            )

        # Always initialize the VertexAI client
        self.multi_region_vertex_client = AnthropicVertexAiMultiRegionClient(
            project_id=project_id,
            model_name=self.vertex_model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )

    def _convert_model_name_for_direct(self, vertex_model_name: str) -> str | None:
        """Convert VertexAI model name to direct Anthropic model name.

        The model names differ between VertexAI and direct Anthropic configurations.
        This method handles the conversion based on known patterns.
        """

        anthropic_vertexai_model_name_to_direct = {
            _VERTEXAI_CLAUDE_3_5: _ANTHROPIC_CLAUDE_3_5,
            _VERTEXAI_CLAUDE_3_7: _ANTHROPIC_CLAUDE_3_7,
        }

        if vertex_model_name in anthropic_vertexai_model_name_to_direct:
            return anthropic_vertexai_model_name_to_direct[vertex_model_name]
        else:
            logger.info(
                "Unknown model name for direct Anthropic: %s. Using VertexAI.",
                vertex_model_name,
            )
            return None

    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[PromptExchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Any | None = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ):
        # Get the direct rate from feature flag
        direct_rate = _CHAT_ANTHROPIC_DIRECT_RATE.get(
            base.feature_flags.get_global_context()
        )

        # Get a deterministic value based on session ID for consistent routing
        # Use a different salt ("direct") for the direct vs. VertexAI decision to avoid correlation
        if (
            request_context
            and hasattr(request_context, "request_session_id")
            and request_context.request_session_id
        ):
            # Add "direct" suffix to get a different hash for the direct vs. VertexAI decision
            rand_val = get_pseudo_random_from_session_id(
                request_context.request_session_id + "direct"
            )
            logger.info(
                "Using session ID for direct randomization: %s",
                request_context.request_session_id,
            )
        else:
            # Fallback to random if no session ID is available
            rand_val = random()
            logger.info(
                "No session ID available, using random value for direct decision"
            )

        # Select client based on the rate
        if rand_val < direct_rate:
            # We want to use direct client based on the rate
            if self.direct_client is not None:
                logger.info(
                    "Using Direct Anthropic client. rand_val: %f, direct_rate: %f",
                    rand_val,
                    direct_rate,
                )
                client = self.direct_client
            else:
                # We want to use direct client but don't know a valid model name
                logger.warning(
                    "Unknown model name for direct Anthropic: %s. Using VertexAI.",
                    self.vertex_model_name,
                )
                client = self.multi_region_vertex_client
        else:
            # Use VertexAI based on the rate
            logger.info(
                "Using VertexAI client. rand_val: %f, direct_rate: %f, model: %s",
                rand_val,
                direct_rate,
                self.vertex_model_name,
            )
            client = self.multi_region_vertex_client

        # Forward the request to the selected client
        return client.generate_response_stream(
            model_caller=model_caller,
            messages=messages,
            system_prompt=system_prompt,
            cur_message=cur_message,
            chat_history=chat_history,
            tools=tools,
            tool_definitions=tool_definitions,
            tool_choice=tool_choice,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
            prefill=prefill,
            use_caching=use_caching,
            request_context=request_context,
            yield_final_parameters=yield_final_parameters,
            model_api_call_callback=model_api_call_callback,
        )

    def count_tokens(self, message: str) -> int:
        # Use the multi-region VertexAI client for token counting since direct client
        # might be None if we don't have a valid direct model name.
        return self.multi_region_vertex_client.count_tokens(message)


class AnthropicVertexAiMultiRegionClient(ThirdPartyModelClient):
    def __init__(
        self,
        project_id,
        model_name,
        temperature,
        max_output_tokens,
    ):
        self.model_name = model_name

        self.us_client = AnthropicVertexAiClient(
            project_id=project_id,
            region="us-east5",
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )
        self.eu_client = AnthropicVertexAiClient(
            project_id=project_id,
            region="europe-west1",
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )
        self.asia_client = AnthropicVertexAiClient(
            project_id=project_id,
            region="asia-southeast1",
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )

    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[PromptExchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Any | None = None,
        yield_final_parameters: bool = False,
        model_api_call_callback: Optional[Callable[[ModelAPICall], None]] = None,
    ):
        load_balanced_models_flag = _CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_MODELS.get(
            base.feature_flags.get_global_context()
        )
        if self.model_name in load_balanced_models_flag:
            logger.info("Using load balancing for model %s", self.model_name)
            if (
                request_context
                and hasattr(request_context, "request_session_id")
                and request_context.request_session_id
            ):
                rand_val = get_pseudo_random_from_session_id(
                    request_context.request_session_id
                )
                logger.info(
                    "Using session ID for randomization: %s",
                    request_context.request_session_id,
                )
            else:
                # Fallback to random if no session ID is available
                rand_val = random()
                logger.info("No session ID available, using random value")

            # Get the load balancing rates from feature flags
            europe_rate = _CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_EUROPE_RATE.get(
                base.feature_flags.get_global_context()
            )
            asia_rate = _CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_ASIA_RATE.get(
                base.feature_flags.get_global_context()
            )

            # Ensure rates sum to at most 1.0
            total_rate = europe_rate + asia_rate
            if total_rate > 1.0:
                # Normalize rates if they exceed 1.0
                europe_rate = europe_rate / total_rate
                asia_rate = asia_rate / total_rate

            # Select client based on normalized rates
            if rand_val < europe_rate:
                logger.info(
                    "Using Europe client. rand_val: %f, europe_rate: %f, asia_rate: %f",
                    rand_val,
                    europe_rate,
                    asia_rate,
                )
                client = self.eu_client
            elif rand_val < europe_rate + asia_rate:
                logger.info(
                    "Using Asia client. rand_val: %f, europe_rate: %f, asia_rate: %f",
                    rand_val,
                    europe_rate,
                    asia_rate,
                )
                client = self.asia_client
            else:
                logger.info(
                    "Using US client. rand_val: %f, europe_rate: %f, asia_rate: %f",
                    rand_val,
                    europe_rate,
                    asia_rate,
                )
                client = self.us_client
        else:
            logger.info("Not using load balancing for model %s", self.model_name)
            client = self.us_client

        return client.generate_response_stream(
            model_caller=model_caller,
            messages=messages,
            system_prompt=system_prompt,
            cur_message=cur_message,
            chat_history=chat_history,
            tools=tools,
            tool_definitions=tool_definitions,
            tool_choice=tool_choice,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
            prefill=prefill,
            use_caching=use_caching,
            yield_final_parameters=yield_final_parameters,
            model_api_call_callback=model_api_call_callback,
        )

    def count_tokens(self, message: str) -> int:
        return self.us_client.count_tokens(message)

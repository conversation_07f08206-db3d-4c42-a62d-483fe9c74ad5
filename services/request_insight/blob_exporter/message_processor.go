package main

import (
	"context"
	"sync"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"

	pb "github.com/augmentcode/augment/services/request_insight/proto"
)

// BlobProcessorInterface defines the interface for processing blobs.
type BlobProcessorInterface interface {
	// ProcessBlobs processes a batch of blobs for a tenant.
	ProcessBlobs(ctx context.Context, tenantID string, blobNames []string) error
}

// CheckpointProcessorInterface defines the interface for processing checkpoints.
type CheckpointProcessorInterface interface {
	// ProcessCheckpoint processes a checkpoint for a tenant.
	ProcessCheckpoint(ctx context.Context, tenantID string, checkpointID string) error
}

// MessageProcessor processes RequestInsightMessages and extracts blobs and checkpoints.
type MessageProcessor struct {
	// Processors
	blobProcessor       BlobProcessorInterface
	checkpointProcessor CheckpointProcessorInterface

	// Metrics
	blobCount       prometheus.Counter
	checkpointCount prometheus.Counter
}

// NewMessageProcessor creates a new MessageProcessor.
func NewMessageProcessor(
	blobProcessor BlobProcessorInterface,
	checkpointProcessor CheckpointProcessorInterface,
) *MessageProcessor {
	blobCount := BlobsExported.WithLabelValues("extracted")
	checkpointCount := CheckpointsExported.WithLabelValues("extracted")

	return &MessageProcessor{
		blobProcessor:       blobProcessor,
		checkpointProcessor: checkpointProcessor,
		blobCount:           blobCount,
		checkpointCount:     checkpointCount,
	}
}

// ProcessMessage processes a single RequestInsightMessage.
func (p *MessageProcessor) ProcessMessage(ctx context.Context, message *pb.RequestInsightMessage) error {
	switch m := message.Message.(type) {
	case *pb.RequestInsightMessage_RecordSessionEventsRequest:
		tenantID := m.RecordSessionEventsRequest.TenantInfo.TenantId
		var wg sync.WaitGroup

		for _, event := range m.RecordSessionEventsRequest.Events {
			// Process content manager upload blobs events
			if uploadBlobs := event.GetContentManagerUploadBlobs(); uploadBlobs != nil {
				if len(uploadBlobs.UploadedBlobs) == 0 {
					continue
				}

				// Extract blob names
				blobNames := make([]string, 0, len(uploadBlobs.UploadedBlobs))
				for _, blob := range uploadBlobs.UploadedBlobs {
					blobNames = append(blobNames, blob.BlobName)
				}

				p.blobCount.Add(float64(len(blobNames)))
				log.Info().
					Int("blob_count", len(blobNames)).
					Str("tenant_id", tenantID).
					Msg("Processing content manager upload blobs event")

				// Process blobs asynchronously
				wg.Add(1)
				go func(blobNames []string) {
					defer func() {
						wg.Done()
					}()
					if err := p.blobProcessor.ProcessBlobs(ctx, tenantID, blobNames); err != nil {
						log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to process blobs")
					}
				}(blobNames) // Pass blobNames as a parameter to avoid closure issues
			}

			// Process content manager checkpoint events
			if checkpoint := event.GetContentManagerCheckpointBlobs(); checkpoint != nil {
				p.checkpointCount.Inc()
				log.Info().
					Str("checkpoint_id", checkpoint.CheckpointId).
					Str("tenant_id", tenantID).
					Uint32("added_blobs", checkpoint.AddedBlobsCount).
					Uint32("deleted_blobs", checkpoint.DeletedBlobsCount).
					Msg("Processing content manager checkpoint event")

				// Process checkpoint asynchronously
				wg.Add(1)
				go func(checkpointID string) {
					defer func() {
						wg.Done()
					}()
					if err := p.checkpointProcessor.ProcessCheckpoint(ctx, tenantID, checkpointID); err != nil {
						log.Error().Err(err).Str("checkpoint", checkpointID).Msg("Failed to process checkpoint")
					}
				}(checkpoint.CheckpointId)
			}
		}

		// Wait for all processing to complete
		wg.Wait()
	}

	return nil
}

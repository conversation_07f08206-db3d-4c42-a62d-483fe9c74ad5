package main

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/civil"
	"github.com/augmentcode/augment/base/logging/audit"
	testutils "github.com/augmentcode/augment/base/test_utils"
	bqemulator "github.com/augmentcode/augment/base/test_utils/bigquery/emulator"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	pb "github.com/augmentcode/augment/services/request_insight/analytics/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	projectId        = "test-project"
	datasetName      = "test_dataset"
	noDataTenantID   = "123"
	noDataTenantName = "nodata"
	userId           = "<EMAIL>"
)

// Shared test fixtures. These are initialized in TestMain.
var (
	bqEmulator *bqemulator.BigQueryEmulator = nil
)

// TestMain is the entry point for the test suite. This is where shared test fixtures should be
// set up.
func TestMain(m *testing.M) {
	// Set up a shared emulator for all the tests.
	var err error
	bqEmulator, err = bqemulator.New(context.Background(), projectId, datasetName)
	if err != nil {
		panic(err)
	}
	defer bqEmulator.Close()

	err = bqEmulator.LoadJsonSchemaFile(context.Background(), "../analytics_dataset/schema.json")
	if err != nil {
		panic(err)
	}

	// Run the tests.
	os.Exit(m.Run())
}

func testServer(t *testing.T) *analyticsServer {
	if bqEmulator == nil {
		t.Fatal("BigQuery emulator is not set up")
	}

	return &analyticsServer{
		bqClient:    bqEmulator.Client,
		datasetName: datasetName,
		auditLogger: audit.NewDefaultAuditLogger(),
	}
}

func noDataAuthorizedContext() context.Context {
	return authorizedContext(noDataTenantID, noDataTenantName, userId)
}

func authorizedContext(tenantID string, tenantName string, userId string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:   tenantID,
		TenantName: tenantName,
		UserID:     userId,
		Scope:      []string{"PII_ADMIN"},
	}
	return claims.NewContext(context.Background())
}

func badDateFilters() []*pb.DateFilters {
	return []*pb.DateFilters{
		// Missing end date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
		},
		// Missing start date.
		{
			EndDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
		},
		// Start date after end date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
			EndDate:   &pb.Date{Year: 2023, Month: 1, Day: 1},
		},
		// Invalid start date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 0},
			EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
		},
		// Invalid end date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
			EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 0},
		},
	}
}

// Run basic auth tests that apply to all endpoints.
func runAuthTests[ReqT proto.Message, RespT proto.Message](
	t *testing.T, goodRequest ReqT, requestFunc func(context.Context, ReqT) (RespT, error),
) {
	t.Run("auth validation", func(t *testing.T) {
		// Test missing auth claims.
		_, err := requestFunc(context.Background(), goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for missing auth claims")
		}

		// Test mismatch between auth claims and request.
		wrongTenantClaims := &auth.AugmentClaims{
			TenantID:   "456",
			TenantName: "another-tenant",
			UserID:     "234",
			Scope:      []string{"CONTENT_RW"},
		}
		badCtx := wrongTenantClaims.NewContext(noDataAuthorizedContext())
		_, err = requestFunc(badCtx, goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for wrong tenant ID in claims")
		}
	})
}

func TestCheckDatasetName(t *testing.T) {
	goodNames := []string{
		"prod_request_insight_dataset",
	}
	for _, name := range goodNames {
		if !checkDatasetName(name) {
			t.Errorf("Dataset name should be valid: %s", name)
		}
	}

	badNames := []string{
		"prod-request-insight-dataset",
		"prod_request_insight_dataset;",
		"; drop table x",
	}
	for _, name := range badNames {
		if checkDatasetName(name) {
			t.Errorf("Dataset name should be valid: %s", name)
		}
	}
}

func TestGetDevDays(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetDevDaysRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetDevDaysRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetDevDays(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetDevDaysRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetDevDays,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetDevDaysRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetDevDaysResponse{
						DevDays: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 0,
							},
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
								Count: 0,
							},
						},
					},
				},
			},
			server.GetDevDays,
		)
	})
}

func TestGetActiveUsers(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetActiveUsersRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
			// Invalid request type.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
				RequestTypes: []pb.GetActiveUsersRequest_RequestType{
					pb.GetActiveUsersRequest_UNKNOWN,
				},
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetActiveUsersRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetActiveUsers(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetActiveUsersRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetActiveUsers,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 0,
							},
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
								Count: 0,
							},
						},
					},
				},
			},
			server.GetActiveUsers,
		)
	})

	t.Run("request type filter", func(t *testing.T) {
		tenantID := uuid.New().String()
		ctx := authorizedContext(tenantID, "test-tenant", userId)

		// Insert rows for a completion request and an edit request from different users on the
		// same day.
		query := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
			    request_id, tenant, tenant_id, shard_namespace, user_id, session_id, user_agent,
				time, request_type
			)
			VALUES
				(
			        "request1", "test-tenant", @tenant_id, "test-namespace", "user1",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "COMPLETION"
				),
			    (
				    "request2", "test-tenant", @tenant_id, "test-namespace", "user2",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "EDIT"
				)
		`, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		dateFilter := &pb.DateFilters{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
			EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// There was one completion user.
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId:    tenantID,
						DateFilters: dateFilter,
						RequestTypes: []pb.GetActiveUsersRequest_RequestType{
							pb.GetActiveUsersRequest_COMPLETION,
						},
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 1,
							},
						},
					},
				},
				// There were no chat users.
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId:    tenantID,
						DateFilters: dateFilter,
						RequestTypes: []pb.GetActiveUsersRequest_RequestType{
							pb.GetActiveUsersRequest_CHAT,
						},
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 0,
							},
						},
					},
				},
				// There were two users of any request type.
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId:    tenantID,
						DateFilters: dateFilter,
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 2,
							},
						},
					},
				},
			},
			server.GetActiveUsers,
		)
	})
}

func TestGetCompletionStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetCompletionStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetCompletionStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetCompletionStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetCompletionStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetCompletionStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetCompletionStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetCompletionStatsResponse{
						CompletionStats: []*pb.GetCompletionStatsResponse_CompletionStats{
							{
								Aggregation: &pb.GetCompletionStatsResponse_CompletionStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 1},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
							{
								Aggregation: &pb.GetCompletionStatsResponse_CompletionStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 2},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
						},
					},
				},
			},
			server.GetCompletionStats,
		)
	})
}

func TestGetEditStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetEditStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetEditStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetEditStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetEditStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetEditStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetEditStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetEditStatsResponse{
						EditStats: []*pb.GetEditStatsResponse_EditStats{
							{
								Aggregation: &pb.GetEditStatsResponse_EditStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 1},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
							{
								Aggregation: &pb.GetEditStatsResponse_EditStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 2},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
						},
					},
				},
			},
			server.GetEditStats,
		)
	})
}

func TestGetChatStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetChatStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetChatStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetChatStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetChatStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetChatStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetChatStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetChatStatsResponse{
						ChatStats: []*pb.GetChatStatsResponse_ChatStats{
							{
								Aggregation: &pb.GetChatStatsResponse_ChatStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 1},
								},
								RequestCount: 0,
							},
							{
								Aggregation: &pb.GetChatStatsResponse_ChatStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 2},
								},
								RequestCount: 0,
							},
						},
					},
				},
			},
			server.GetChatStats,
		)
	})
}

func TestGetKeywordsStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetKeywordsStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
				RequestType: pb.GetKeywordsStatsRequest_EDIT,
			},
			// Missing date filters.
			{
				TenantId:    noDataTenantID,
				RequestType: pb.GetKeywordsStatsRequest_EDIT,
			},
			// Missing request type.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetKeywordsStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
				RequestType: pb.GetKeywordsStatsRequest_EDIT,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetKeywordsStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetKeywordsStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
			RequestType: pb.GetKeywordsStatsRequest_EDIT,
		},
		server.GetKeywordsStats,
	)
}

func TestGetCategoriesStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetCategoriesStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
				RequestType: pb.GetCategoriesStatsRequest_EDIT,
			},
			// Missing date filters.
			{
				TenantId:    noDataTenantID,
				RequestType: pb.GetCategoriesStatsRequest_EDIT,
			},
			// Missing request type.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetCategoriesStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
				RequestType: pb.GetCategoriesStatsRequest_EDIT,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetCategoriesStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetCategoriesStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
			RequestType: pb.GetCategoriesStatsRequest_EDIT,
		},
		server.GetCategoriesStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
						RequestType: pb.GetCategoriesStatsRequest_CHAT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{},
					},
				},
			},
			server.GetCategoriesStats,
		)
	})

	t.Run("chat data", func(t *testing.T) {
		tenantID := uuid.New().String()
		ctx := authorizedContext(tenantID, "test-tenant", userId)

		// Insert rows for sample chat requests
		query := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.chat_host_request (
                request_id, tenant, tenant_id, shard_namespace, time, sanitized_json,
                message_categories
			)
			VALUES
				(
                    "request1", "test-tenant", @tenant_id, "test-namespace",
                    "2024-01-01 00:00:00", JSON '{}', ['test']
                ),
				(
                    "request2", "test-tenant", @tenant_id, "test-namespace",
                    "2024-01-02 00:00:00", JSON '{}', ['test']
                )
        `, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}

		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for aggregated chat categories
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
						RequestType: pb.GetCategoriesStatsRequest_CHAT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{
							{
								Category: "test",
								Count:    2,
							},
						},
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
						RequestType: pb.GetCategoriesStatsRequest_CHAT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{
							{
								Category: "test",
								Count:    1,
							},
						},
					},
				},
				// Request for edit categories
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
						RequestType: pb.GetCategoriesStatsRequest_EDIT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{},
					},
				},
			},
			server.GetCategoriesStats,
		)
	})
}

func TestGetEarliestRequestTimestamp(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		// Missing tenant id.
		badRequest := &pb.GetEarliestRequestTimestampRequest{}
		_, err := server.GetEarliestRequestTimestamp(noDataAuthorizedContext(), badRequest)
		if err == nil || status.Code(err) != codes.InvalidArgument {
			t.Errorf("Expected error for request: %v", badRequest)
		}
	})

	runAuthTests(
		t,
		&pb.GetEarliestRequestTimestampRequest{
			TenantId: noDataTenantID,
		},
		server.GetEarliestRequestTimestamp,
	)
}

func TestGetUserLastRequestTimestamp(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserLastRequestTimestampRequest{
			// Missing user id
			{
				TenantId: "test-tenant",
				RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
					pb.GetUserLastRequestTimestampRequest_COMPLETION,
				},
				MinSearchTimestamp: timestamppb.Now(),
			},
			// Missing tenant id
			{
				UserId: "test-user",
				RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
					pb.GetUserLastRequestTimestampRequest_COMPLETION,
				},
				MinSearchTimestamp: timestamppb.Now(),
			},
			// Missing min_search_timestamp
			{
				UserId:   "test-user",
				TenantId: "test-tenant",
				RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
					pb.GetUserLastRequestTimestampRequest_COMPLETION,
				},
			},
		}

		for _, req := range badRequests {
			_, err := server.GetUserLastRequestTimestamp(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected InvalidArgument error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserLastRequestTimestampRequest{
			UserId:   "test-user",
			TenantId: noDataTenantID,
			RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
				pb.GetUserLastRequestTimestampRequest_COMPLETION,
			},
			MinSearchTimestamp: timestamppb.Now(),
		},
		server.GetUserLastRequestTimestamp,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: noDataTenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
						},
						MinSearchTimestamp: timestamppb.Now(),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: nil,
					},
				},
			},
			server.GetUserLastRequestTimestamp,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		userId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", userId)

		// Insert sample data
		query := server.bqClient.Query(fmt.Sprintf(`
            INSERT INTO %s.request_metadata (
                request_id, tenant, tenant_id, shard_namespace, user_id, session_id, user_agent,
                time, request_type
            )
            VALUES
                (
                    "request1", "test-tenant", @tenant_id, "test-namespace", @user_id,
                    "test-session", "test-user-agent", TIMESTAMP("2024-03-01 00:00:00 UTC"), "COMPLETION"
                ),
                (
                    "request2", "test-tenant", @tenant_id, "test-namespace", @user_id,
                    "test-session", "test-user-agent", TIMESTAMP("2024-02-01 00:00:00 UTC"), "EDIT"
                ),
                (
                    "request3", "test-tenant", @tenant_id, "test-namespace", @user_id,
                    "test-session", "test-user-agent", TIMESTAMP("2024-01-05 00:00:00 UTC"), "CHAT"
                )
        `, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: userId},
		}
		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 2, 2, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 4, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: nil,
					},
				},
			},
			server.GetUserLastRequestTimestamp,
		)
	})
}

func TestGetUserChatRequestStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserChatRequestStatsRequest{
			// Missing tenant id.
			{
				UserId: userId,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing user id.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
				UserId:   userId,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetUserChatRequestStatsRequest{
				TenantId:    noDataTenantID,
				UserId:      userId,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetUserChatRequestStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserChatRequestStatsRequest{
			TenantId: noDataTenantID,
			UserId:   userId,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetUserChatRequestStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserChatRequestStatsRequest{
						TenantId: noDataTenantID,
						UserId:   userId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserChatRequestStatsResponse{
						RequestCount: 0,
					},
				},
			},
			server.GetUserChatRequestStats,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		testUserId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", testUserId)

		// Insert sample data for chat requests
		query := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type
			)
			VALUES
				(
					"request1", "test-tenant", @tenant_id, "test-namespace", "user1", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "CHAT"
				),
				(
					"request2", "test-tenant", @tenant_id, "test-namespace", "user2", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "CHAT"
				),
				(
					"request3", "test-tenant", @tenant_id, "test-namespace", "user3", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "CHAT"
				),
				(
					"request4", "test-tenant", @tenant_id, "test-namespace", "user4", "different-user", "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "CHAT"
				)
		`, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: testUserId},
		}
		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for all dates
				{
					Request: &pb.GetUserChatRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserChatRequestStatsResponse{
						RequestCount: 3,
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetUserChatRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
					},
					Response: &pb.GetUserChatRequestStatsResponse{
						RequestCount: 2,
					},
				},
			},
			server.GetUserChatRequestStats,
		)
	})
}

func TestGetUserAgentRequestStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserAgentRequestStatsRequest{
			// Missing tenant id.
			{
				UserId: userId,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing user id.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
				UserId:   userId,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetUserAgentRequestStatsRequest{
				TenantId:    noDataTenantID,
				UserId:      userId,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetUserAgentRequestStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserAgentRequestStatsRequest{
			TenantId: noDataTenantID,
			UserId:   userId,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetUserAgentRequestStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserAgentRequestStatsRequest{
						TenantId: noDataTenantID,
						UserId:   userId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentRequestStatsResponse{
						RequestCount: 0,
					},
				},
			},
			server.GetUserAgentRequestStats,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		testUserId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", testUserId)

		// First insert data into request_metadata table
		metadataQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "user1", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace", "user2", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace", "user3", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "AGENT_CHAT"
				)
		`, server.datasetName))
		metadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: testUserId},
		}
		_, err := metadataQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Then insert corresponding events into agent_request_event table
		eventQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.agent_request_event (
				request_id, tenant, tenant_id, shard_namespace, time, sanitized_json
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace",
					"2024-01-01 00:00:00", JSON '{"event_name": "sent-user-message"}'
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace",
					"2024-01-01 00:00:00", JSON '{"event_name": "sent-user-message"}'
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace",
					"2024-01-02 00:00:00", JSON '{"event_name": "sent-user-message"}'
				)
		`, server.datasetName))
		eventQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err = eventQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for all dates
				{
					Request: &pb.GetUserAgentRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentRequestStatsResponse{
						RequestCount: 3,
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetUserAgentRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
					},
					Response: &pb.GetUserAgentRequestStatsResponse{
						RequestCount: 2,
					},
				},
			},
			server.GetUserAgentRequestStats,
		)
	})
}

func TestGetUserAgentToolUseStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserAgentToolUseStatsRequest{
			// Missing tenant id.
			{
				UserId: userId,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing user id.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
				UserId:   userId,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetUserAgentToolUseStatsRequest{
				TenantId:    noDataTenantID,
				UserId:      userId,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetUserAgentToolUseStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserAgentToolUseStatsRequest{
			TenantId: noDataTenantID,
			UserId:   userId,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetUserAgentToolUseStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserAgentToolUseStatsRequest{
						TenantId: noDataTenantID,
						UserId:   userId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentToolUseStatsResponse{
						ToolUseCount: 0,
					},
				},
			},
			server.GetUserAgentToolUseStats,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		testUserId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", testUserId)

		// Insert sample data for agent chat requests
		metadataQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "user1", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace", "user2", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace", "user3", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "AGENT_CHAT"
				)
		`, server.datasetName))
		metadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: testUserId},
		}
		_, err := metadataQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert sample data for tool uses
		toolUseQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.tool_use_data (
				request_id, tenant, tenant_id, shard_namespace, time, sanitized_json
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00", JSON '{"tool_use_id": "tool1"}'
				),
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00", JSON '{"tool_use_id": "tool2"}'
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00", JSON '{"tool_use_id": "tool3"}'
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace", "2024-01-02 00:00:00", JSON '{"tool_use_id": "tool4"}'
				)
		`, server.datasetName))
		toolUseQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err = toolUseQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for all dates
				{
					Request: &pb.GetUserAgentToolUseStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentToolUseStatsResponse{
						ToolUseCount: 4,
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetUserAgentToolUseStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
					},
					Response: &pb.GetUserAgentToolUseStatsResponse{
						ToolUseCount: 3,
					},
				},
			},
			server.GetUserAgentToolUseStats,
		)
	})
}

func TestAddZeroDates(t *testing.T) {
	t.Run("empty data", func(t *testing.T) {
		data := []*pb.DateAndCount{}
		start := civil.Date{Year: 2024, Month: 1, Day: 1}
		end := civil.Date{Year: 2024, Month: 1, Day: 2}
		res := addZeroDates(data, start, end)
		expected := []*pb.DateAndCount{
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
				Count: 0,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
				Count: 0,
			},
		}
		if len(res) != len(expected) {
			t.Errorf("Expected %d results, got %d", len(expected), len(res))
		}
		for i := range res {
			if res[i].Date.String() != expected[i].Date.String() || res[i].Count != expected[i].Count {
				t.Errorf("Expected %v, got %v", expected[i], res[i])
			}
		}
	})

	t.Run("non-empty data", func(t *testing.T) {
		data := []*pb.DateAndCount{
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
				Count: 1,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 3},
				Count: 2,
			},
		}
		start := civil.Date{Year: 2024, Month: 1, Day: 1}
		end := civil.Date{Year: 2024, Month: 1, Day: 3}
		res := addZeroDates(data, start, end)
		expected := []*pb.DateAndCount{
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
				Count: 1,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
				Count: 0,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 3},
				Count: 2,
			},
		}
		if len(res) != len(expected) {
			t.Errorf("Expected %d results, got %d", len(expected), len(res))
		}
		for i := range res {
			if res[i].Date.String() != expected[i].Date.String() || res[i].Count != expected[i].Count {
				t.Errorf("Expected %v, got %v", expected[i], res[i])
			}
		}
	})
}

func TestForgetUser(t *testing.T) {
	server := testServer(t)

	t.Run("auth", func(t *testing.T) {
		// Missing claims.
		_, err := server.ForgetUser(context.Background(), &pb.ForgetUserRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Missing PII_ADMIN scope.
		claims := &auth.AugmentClaims{
			TenantID:   "",
			TenantName: "",
			UserID:     "",
			Scope:      []string{},
		}
		ctx := claims.NewContext(context.Background())
		_, err = server.ForgetUser(ctx, &pb.ForgetUserRequest{})
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))

		// Correctly authenticated.
		claims = &auth.AugmentClaims{
			TenantID:   "",
			TenantName: "",
			UserID:     "",
			Scope:      []string{"PII_ADMIN"},
		}
		ctx = claims.NewContext(context.Background())
		_, err = server.ForgetUser(ctx, &pb.ForgetUserRequest{})
		if err != nil {
			require.Equal(t, codes.InvalidArgument, status.Code(err))
		}
	})

	t.Run("dry run", func(t *testing.T) {
		tenantID := uuid.New().String()
		opaqueUserID1 := uuid.New().String()
		userEmail1 := fmt.Sprintf("%<EMAIL>", opaqueUserID1)
		opaqueUserID2 := uuid.New().String()
		userEmail2 := fmt.Sprintf("%<EMAIL>", opaqueUserID2)
		ctx := authorizedContext("", "", "")

		// Insert rows for 2 users
		metadataQuery := server.bqClient.Query(`
			INSERT INTO request_metadata (
				user_id, opaque_user_id, user_id_type, user_email,
				request_id, tenant, tenant_id, shard_namespace, session_id, user_agent, time, request_type
			)
			VALUES
				(
					@user_email1, @opaque_user_id1, "AUGMENT", @user_email1,
					"request1", "test-tenant", @tenant_id, "test-namespace", "test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					@user_email2, @opaque_user_id2, "AUGMENT", @user_email2,
					"request2", "test-tenant", @tenant_id, "test-namespace", "test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				)
		`)
		metadataQuery.DefaultProjectID = projectId
		metadataQuery.DefaultDatasetID = datasetName
		metadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "opaque_user_id1", Value: opaqueUserID1},
			{Name: "opaque_user_id2", Value: opaqueUserID2},
			{Name: "user_email1", Value: userEmail1},
			{Name: "user_email2", Value: userEmail2},
		}
		_, err := metadataQuery.Read(ctx)
		require.NoError(t, err, "Failed to insert metadata")

		// Dry-run forget user1.
		req := &pb.ForgetUserRequest{
			UserId:    opaqueUserID1,
			UserEmail: userEmail1,
			DryRun:    true,
		}
		_, err = server.ForgetUser(ctx, req)
		require.NoError(t, err, "Failed to forget user")

		// Check that user1's row was not updated.
		query := server.bqClient.Query(fmt.Sprintf(`
			SELECT user_id, user_email FROM request_metadata
			WHERE opaque_user_id = @opaque_user_id1
		`))
		query.DefaultDatasetID = datasetName
		query.Parameters = []bigquery.QueryParameter{
			{Name: "opaque_user_id1", Value: opaqueUserID1},
		}
		it, err := query.Read(ctx)
		require.NoError(t, err)
		var results []bigquery.Value
		err = it.Next(&results)
		require.NoError(t, err, "Unexpected iterator error")
		require.Len(t, results, 2, "Expected 2 results")
		require.Equal(t, userEmail1, results[0])
		require.Equal(t, userEmail1, results[1])
	})

	t.Run("request_metadata", func(t *testing.T) {
		tenantID := uuid.New().String()
		opaqueUserID1 := uuid.New().String()
		userEmail1 := fmt.Sprintf("%<EMAIL>", opaqueUserID1)
		opaqueUserID2 := uuid.New().String()
		userEmail2 := fmt.Sprintf("%<EMAIL>", opaqueUserID2)
		ctx := authorizedContext("", "", "")

		// Insert rows for 2 users
		metadataQuery := server.bqClient.Query(`
			INSERT INTO request_metadata (
				user_id, opaque_user_id, user_id_type, user_email,
				request_id, tenant, tenant_id, shard_namespace, session_id, user_agent, time, request_type
			)
			VALUES
				(
					@user_email1, @opaque_user_id1, "AUGMENT", @user_email1,
					"request1", "test-tenant", @tenant_id, "test-namespace", "test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					@user_email2, @opaque_user_id2, "AUGMENT", @user_email2,
					"request2", "test-tenant", @tenant_id, "test-namespace", "test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				)
		`)
		metadataQuery.DefaultProjectID = projectId
		metadataQuery.DefaultDatasetID = datasetName
		metadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "opaque_user_id1", Value: opaqueUserID1},
			{Name: "opaque_user_id2", Value: opaqueUserID2},
			{Name: "user_email1", Value: userEmail1},
			{Name: "user_email2", Value: userEmail2},
		}
		_, err := metadataQuery.Read(ctx)
		require.NoError(t, err, "Failed to insert metadata")

		// Forget user1.
		// Note(jacqueline): The bigquery emulator doesn't update job statistics correctly, so we can't
		// check the responses rowsUpdated count.
		req := &pb.ForgetUserRequest{
			UserId:    opaqueUserID1,
			UserEmail: userEmail1,
			DryRun:    false,
		}
		_, err = server.ForgetUser(ctx, req)
		require.NoError(t, err, "Failed to forget user")

		// Query the table to check that user1's row was updated as expected.
		query := server.bqClient.Query(fmt.Sprintf(`
			SELECT user_id, user_email FROM request_metadata
			WHERE opaque_user_id = @opaque_user_id1
		`))
		query.DefaultDatasetID = datasetName
		query.Parameters = []bigquery.QueryParameter{
			{Name: "opaque_user_id1", Value: opaqueUserID1},
		}
		it, err := query.Read(ctx)
		require.NoError(t, err)
		var results []bigquery.Value
		err = it.Next(&results)
		require.NoError(t, err, "Unexpected iterator error")
		require.Len(t, results, 2, "Expected 2 results")
		require.Equal(t, "_DELETED_", results[0])
		require.Equal(t, "_DELETED_", results[1])

		// Check that user2's row was not updated.
		query = server.bqClient.Query(fmt.Sprintf(`
			SELECT user_id, user_email FROM request_metadata
			WHERE opaque_user_id = @opaque_user_id2
		`))
		query.DefaultDatasetID = datasetName
		query.Parameters = []bigquery.QueryParameter{
			{Name: "opaque_user_id2", Value: opaqueUserID2},
		}
		it, err = query.Read(ctx)
		require.NoError(t, err)
		err = it.Next(&results)
		require.NoError(t, err, "Unexpected iterator error")
		require.Len(t, results, 2, "Expected 2 results")
		require.Equal(t, userEmail2, results[0])
		require.Equal(t, userEmail2, results[1])
	})

	t.Run("invite_user_to_tenant", func(t *testing.T) {
		tenantID := uuid.New().String()
		userEmail1 := fmt.Sprintf("%<EMAIL>", uuid.New().String())
		userEmail2 := fmt.Sprintf("%<EMAIL>", uuid.New().String())
		ctx := authorizedContext("", "", "")

		// Insert rows for 2 invitations, 1 where user1 is the invitee and 1 where user1 is the inviter.
		inviteQuery := server.bqClient.Query(`
			INSERT INTO invite_user_to_tenant (
				invitee_email, inviter_email, tenant_id
			)
			VALUES
				(
					@user_email1, @user_email2, @tenant_id
				),
				(
					@user_email2, @user_email1, @tenant_id
				)
		`)
		inviteQuery.DefaultProjectID = projectId
		inviteQuery.DefaultDatasetID = datasetName
		inviteQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_email1", Value: userEmail1},
			{Name: "user_email2", Value: userEmail2},
		}
		_, err := inviteQuery.Read(ctx)
		require.NoError(t, err, "Failed to insert invite")

		// Forget user1.
		req := &pb.ForgetUserRequest{
			UserId:    "unused",
			UserEmail: userEmail1,
			DryRun:    false,
		}
		_, err = server.ForgetUser(ctx, req)
		require.NoError(t, err, "Failed to forget user")

		// Check that user1 appears nowhere in the dataset.
		query := server.bqClient.Query(fmt.Sprintf(`
			SELECT COUNT(*) FROM invite_user_to_tenant
			WHERE invitee_email = @email OR inviter_email = @email
		`))
		query.DefaultDatasetID = datasetName
		query.Parameters = []bigquery.QueryParameter{
			{Name: "email", Value: userEmail1},
		}
		it, err := query.Read(ctx)
		require.NoError(t, err)
		var results []bigquery.Value
		err = it.Next(&results)
		require.NoError(t, err, "Unexpected iterator error")
		require.Len(t, results, 1, "Expected 1 result")
		require.Equal(t, int64(0), results[0])

		// Check that user2 is still in the dataset.
		query.Parameters = []bigquery.QueryParameter{
			{Name: "email", Value: userEmail2},
		}
		it, err = query.Read(ctx)
		require.NoError(t, err)
		err = it.Next(&results)
		require.NoError(t, err, "Unexpected iterator error")
		require.Len(t, results, 1, "Expected 1 result")
		require.Equal(t, int64(2), results[0])
	})
}

func TestGetUserFeatureStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserFeatureStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetUserFeatureStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetUserFeatureStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserFeatureStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetUserFeatureStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserFeatureStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserFeatureStatsResponse{
						UserFeatureStats: []*pb.GetUserFeatureStatsResponse_UserFeatureStats{},
					},
				},
			},
			server.GetUserFeatureStats,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		wrongTenantID := uuid.New().String()
		testUserId := "test-user"
		opaqueUserId := uuid.New().String()
		wrongOpaqueUserId := uuid.New().String()
		userEmail := "<EMAIL>"
		wrongUserEmail := "<EMAIL>"
		ctx := authorizedContext(tenantID, "test-tenant", testUserId)

		// Insert user data
		userQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.user (
				id, email, tenant_ids, created_at, billing_method, check_subscription_status
			)
			VALUES
				(@opaque_user_id, @user_email, @tenant_id, TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_UNKNOWN', false)
		`, server.datasetName))
		userQuery.Parameters = []bigquery.QueryParameter{
			{Name: "opaque_user_id", Value: opaqueUserId},
			{Name: "user_email", Value: userEmail},
			{Name: "tenant_id", Value: []string{tenantID}},
		}
		_, err := userQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert metadata for completion requests
		metadataQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type, user_email
			)
			VALUES
				(
					"completion-req1", "test-tenant", @tenant_id, "test-namespace", "user1", @opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "COMPLETION", @user_email
				),
				(
					"completion-req2", "test-tenant", @tenant_id, "test-namespace", "user2", @opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "COMPLETION", @user_email
				),
				(
					"chat-req1", "test-tenant", @tenant_id, "test-namespace", "user3", @opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "CHAT", @user_email
				)
		`, server.datasetName))
		metadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "opaque_user_id", Value: opaqueUserId},
			{Name: "user_email", Value: userEmail},
		}
		_, err = metadataQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert completion requests
		completionQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.completion_request (
				request_id, tenant, tenant_id, shard_namespace, time
			)
			VALUES
				("completion-req1", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00"),
				("completion-req2", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00")
		`, server.datasetName))
		completionQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err = completionQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert completion resolutions (one accepted, one not)
		resolutionQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.completion_resolution (
				request_id, tenant_id, time, accepted
			)
			VALUES
				("completion-req1", @tenant_id, "2024-01-01 00:00:00", true),
				("completion-req2", @tenant_id, "2024-01-01 00:00:00", false)
		`, server.datasetName))
		resolutionQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err = resolutionQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert data for wrong tenant (should not be returned)
		// This data has more completions, higher acceptance rate, and more recent timestamps
		wrongUserQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.user (
				id, email, tenant_ids, created_at, billing_method, check_subscription_status
			)
			VALUES
				(@wrong_opaque_user_id, @wrong_user_email, @wrong_tenant_id, TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_UNKNOWN', false)
		`, server.datasetName))
		wrongUserQuery.Parameters = []bigquery.QueryParameter{
			{Name: "wrong_opaque_user_id", Value: wrongOpaqueUserId},
			{Name: "wrong_user_email", Value: wrongUserEmail},
			{Name: "wrong_tenant_id", Value: []string{wrongTenantID}},
		}
		_, err = wrongUserQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert metadata for wrong tenant with more recent data and higher counts
		wrongMetadataQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type, user_email
			)
			VALUES
				(
					"wrong-completion-req1", "wrong-tenant", @wrong_tenant_id, "test-namespace", "wrong-user1", @wrong_opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-02-01 00:00:00", "COMPLETION", @wrong_user_email
				),
				(
					"wrong-completion-req2", "wrong-tenant", @wrong_tenant_id, "test-namespace", "wrong-user2", @wrong_opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-02-01 00:00:00", "COMPLETION", @wrong_user_email
				),
				(
					"wrong-completion-req3", "wrong-tenant", @wrong_tenant_id, "test-namespace", "wrong-user3", @wrong_opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-02-01 00:00:00", "COMPLETION", @wrong_user_email
				),
				(
					"wrong-chat-req1", "wrong-tenant", @wrong_tenant_id, "test-namespace", "wrong-user4", @wrong_opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-02-01 00:00:00", "CHAT", @wrong_user_email
				),
				(
					"wrong-chat-req2", "wrong-tenant", @wrong_tenant_id, "test-namespace", "wrong-user5", @wrong_opaque_user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-02-01 00:00:00", "CHAT", @wrong_user_email
				)
		`, server.datasetName))
		wrongMetadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "wrong_tenant_id", Value: wrongTenantID},
			{Name: "wrong_opaque_user_id", Value: wrongOpaqueUserId},
			{Name: "wrong_user_email", Value: wrongUserEmail},
		}
		_, err = wrongMetadataQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert completion requests for wrong tenant
		wrongCompletionQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.completion_request (
				request_id, tenant, tenant_id, shard_namespace, time
			)
			VALUES
				("wrong-completion-req1", "wrong-tenant", @wrong_tenant_id, "test-namespace", "2024-02-01 00:00:00"),
				("wrong-completion-req2", "wrong-tenant", @wrong_tenant_id, "test-namespace", "2024-02-01 00:00:00"),
				("wrong-completion-req3", "wrong-tenant", @wrong_tenant_id, "test-namespace", "2024-02-01 00:00:00")
		`, server.datasetName))
		wrongCompletionQuery.Parameters = []bigquery.QueryParameter{
			{Name: "wrong_tenant_id", Value: wrongTenantID},
		}
		_, err = wrongCompletionQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert completion resolutions for wrong tenant (all accepted for higher acceptance rate)
		wrongResolutionQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.completion_resolution (
				request_id, tenant_id, time, accepted
			)
			VALUES
				("wrong-completion-req1", @wrong_tenant_id, "2024-02-01 00:00:00", true),
				("wrong-completion-req2", @wrong_tenant_id, "2024-02-01 00:00:00", true),
				("wrong-completion-req3", @wrong_tenant_id, "2024-02-01 00:00:00", true)
		`, server.datasetName))
		wrongResolutionQuery.Parameters = []bigquery.QueryParameter{
			{Name: "wrong_tenant_id", Value: wrongTenantID},
		}
		_, err = wrongResolutionQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserFeatureStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
					},
					Response: &pb.GetUserFeatureStatsResponse{
						UserFeatureStats: []*pb.GetUserFeatureStatsResponse_UserFeatureStats{
							{
								UserEmail:                          userEmail,
								TotalActiveDays:                    1,
								CompletionDays:                     1,
								ChatDays:                           1,
								AgentDays:                          0,
								TotalCompletionsInTimePeriod:       2,
								AcceptedCompletionsInTimePeriod:    1,
								AcceptanceRatePercentage:           50.0,
								TotalChatMessagesInTimePeriod:      1,
								AvgMessagesPerChatDay:              1.0,
								AgentChatTotal:                     0,
								TotalAgentChatMessagesInTimePeriod: 0,
								AvgAgentChatsPerDay:                0.0,
							},
						},
					},
				},
				// Test tenant isolation: should not return data from wrong tenant
				// even though wrong tenant has more data (3 completions vs 2, 2 chats vs 1)
				// and more recent timestamps (2024-02-01 vs 2024-01-01)
				// and higher acceptance rate (100% vs 50%)
				{
					Request: &pb.GetUserFeatureStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 2, Day: 1},
						},
					},
					Response: &pb.GetUserFeatureStatsResponse{
						UserFeatureStats: []*pb.GetUserFeatureStatsResponse_UserFeatureStats{
							{
								// Should return data only from correct tenant, not wrong tenant
								UserEmail:                          userEmail,
								TotalActiveDays:                    1,
								CompletionDays:                     1,
								ChatDays:                           1,
								AgentDays:                          0,
								TotalCompletionsInTimePeriod:       2,    // Not 3 from wrong tenant
								AcceptedCompletionsInTimePeriod:    1,    // Not 3 from wrong tenant
								AcceptanceRatePercentage:           50.0, // Not 100.0 from wrong tenant
								TotalChatMessagesInTimePeriod:      1,    // Not 2 from wrong tenant
								AvgMessagesPerChatDay:              1.0,  // Not 2.0 from wrong tenant
								AgentChatTotal:                     0,
								TotalAgentChatMessagesInTimePeriod: 0,
								AvgAgentChatsPerDay:                0.0,
							},
						},
					},
				},
			},
			server.GetUserFeatureStats,
		)
	})
}

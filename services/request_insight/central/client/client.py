"""A Python client library to interact with the request insight central service."""

import logging
from typing import Iterator, Optional

import grpc

import services.request_insight.central.request_insight_central_pb2 as ri_central_pb2
import services.request_insight.central.request_insight_central_pb2_grpc as ri_central_pb2_grpc
from base.python.grpc import client_options
from services.lib.request_context.request_context import RequestContext


def setup_stubs(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> tuple[
    ri_central_pb2_grpc.RequestInsightCentralConfidentialStub,
    ri_central_pb2_grpc.RequestInsightCentralRestrictedStub,
]:
    """Setup the client stubs for the request insight central service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        logging.info(
            "No credentials provided, creating insecure channel to %s", endpoint
        )
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    return (
        ri_central_pb2_grpc.RequestInsightCentralConfidentialStub(channel),
        ri_central_pb2_grpc.RequestInsightCentralRestrictedStub(channel),
    )


class RequestInsightCentralClient:
    """Client for interacting with the request insight central service."""

    def __init__(
        self,
        confidential_stub: ri_central_pb2_grpc.RequestInsightCentralConfidentialStub,
        restricted_stub: ri_central_pb2_grpc.RequestInsightCentralRestrictedStub,
    ):
        self.confidential_stub = confidential_stub
        self.restricted_stub = restricted_stub

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ) -> "RequestInsightCentralClient":
        """Creates a new client instance from endpoint and credentials."""
        confidential_stub, restricted_stub = setup_stubs(endpoint, credentials, options)
        return cls(confidential_stub, restricted_stub)

    # Confidential methods
    def cross_tenant_confidential_search(
        self,
        request: ri_central_pb2.CrossTenantConfidentialSearchRequest,
        request_context: RequestContext,
    ) -> ri_central_pb2.CrossTenantConfidentialSearchResponse:
        """Search across tenants for confidential data."""
        response = self.confidential_stub.CrossTenantConfidentialSearch(
            request,
            metadata=request_context.to_metadata(),
        )
        return response

    # Restricted methods
    def restricted_search(
        self,
        request: ri_central_pb2.RestrictedSearchRequest,
        request_context: RequestContext,
    ) -> ri_central_pb2.RestrictedSearchResponse:
        """Search for requests that match the given filters."""
        response = self.restricted_stub.RestrictedSearch(
            request,
            metadata=request_context.to_metadata(),
        )
        return response

    def get_request_events(
        self,
        request: ri_central_pb2.GetRequestEventsRequest,
        request_context: RequestContext,
    ) -> Iterator[ri_central_pb2.GetRequestEventResponse]:
        """Get request events for a specific request."""
        try:
            response_stream = self.restricted_stub.GetRequestEvents(
                request,
                metadata=request_context.to_metadata(),
            )
            for response in response_stream:
                if response:
                    yield response
        except grpc.RpcError as e:
            logging.error(
                "Error streaming request events for tenant %s, request %s: %s",
                request.tenant_id,
                request.request_id,
                e,
            )
            raise

    def get_remote_agent_logs(
        self,
        request: ri_central_pb2.GetRemoteAgentLogsRequest,
        request_context: RequestContext,
    ) -> Iterator[ri_central_pb2.GetRemoteAgentLogsResponse]:
        """Get logs for a specific remote agent.

        Args:
            request: The request containing tenant_id, agent_id, time_filter, and optional max_results.
            request_context: The request context to use.

        Returns:
            An iterator of GetRemoteAgentLogsResponse objects.

        Raises:
            grpc.RpcError: If there is an error streaming the logs.
        """
        try:
            response_stream = self.restricted_stub.GetRemoteAgentLogs(
                request,
                metadata=request_context.to_metadata(),
            )
            for response in response_stream:
                if response:
                    yield response
        except grpc.RpcError as e:
            logging.error(
                "Error streaming remote agent logs for tenant %s, agent %s: %s",
                request.tenant_id,
                request.agent_id,
                e,
            )
            raise

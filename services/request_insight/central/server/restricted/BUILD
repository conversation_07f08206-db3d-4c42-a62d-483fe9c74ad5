load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "server_lib",
    srcs = [
        "restricted_server.go",
    ],
    importpath = "github.com/augmentcode/augment/services/request_insight/central/server/restricted",
    visibility = ["//services/request_insight/central/server:__subpackages__"],
    deps = [
        "//base/logging/audit:audit_go",
        "//services/gcs_proxy:gcs_proxy_go_proto",
        "//services/gcs_proxy/client:client_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/central:request_insight_central_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@org_golang_google_api//iterator",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "server_test",
    srcs = ["restricted_server_test.go"],
    data = [
        "//services/request_insight/support_database/search_dataset:schema_json",
    ],
    embed = [":server_lib"],
    deps = [
        "//base/test_utils:testing_utils_go",
        "//base/test_utils/bigquery:emulator_go",
        "//services/gcs_proxy/client:client_go",
        "//services/request_insight:request_insight_go_proto",
        "@google_jsonnet_go//:go_default_library",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_protobuf//proto",
    ],
)

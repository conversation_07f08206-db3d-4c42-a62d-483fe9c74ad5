package main

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	gcsproxyproto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

func TestRequestEventKey(t *testing.T) {
	eventId := "test-event-id"
	eventWithEventId := &pb.RequestEvent{
		EventId: &eventId,
		Event:   &pb.RequestEvent_CompletionHostRequest{},
	}
	key, err := getRequestEventKey("test-request", eventWithEventId)
	if err != nil {
		t.Fatal(err)
	}
	expectedKey := "request/test-request/completion_host_request/test-event-id"
	if key != expectedKey {
		t.Fatalf("Expected key %s, got %s", expectedKey, key)
	}

	// Missing request event id. A random one should be generated.
	eventWithoutEventId := &pb.RequestEvent{
		Event: &pb.RequestEvent_CompletionHostRequest{},
	}
	key, err = getRequestEventKey("test-request", eventWithoutEventId)
	if err != nil {
		t.Fatal(err)
	}
	expectedKeyPrefix := "request/test-request/completion_host_request/"
	if !strings.HasPrefix(key, expectedKeyPrefix) {
		t.Fatalf("Expected key to start with %s, got %s", expectedKeyPrefix, key)
	}

	// Missing request id.
	_, err = getRequestEventKey("", eventWithEventId)
	if err == nil {
		t.Fatal("Expected error for missing request_id")
	}
	if !strings.Contains(err.Error(), "Event missing request_id") {
		t.Fatalf("Expected error message to contain 'Event missing request_id', got %s", err.Error())
	}
}

func TestSessionEventKey(t *testing.T) {
	eventId := "test-event-id"
	eventWithEventId := &pb.SessionEvent{
		EventId: &eventId,
		Event:   &pb.SessionEvent_NextEditSessionEvent{},
	}
	key, err := getSessionEventKey("test-session", eventWithEventId)
	if err != nil {
		t.Fatal(err)
	}
	expectedKey := "session/test-session/next_edit_session_event/test-event-id"
	if key != expectedKey {
		t.Fatalf("Expected key %s, got %s", expectedKey, key)
	}

	// Missing session event id. A random one should be generated.
	eventWithoutEventId := &pb.SessionEvent{
		Event: &pb.SessionEvent_NextEditSessionEvent{},
	}
	key, err = getSessionEventKey("test-session", eventWithoutEventId)
	if err != nil {
		t.Fatal(err)
	}
	expectedKeyPrefix := "session/test-session/next_edit_session_event/"
	if !strings.HasPrefix(key, expectedKeyPrefix) {
		t.Fatalf("Expected key to start with %s, got %s", expectedKeyPrefix, key)
	}

	// Missing session id.
	_, err = getSessionEventKey("", eventWithEventId)
	if err == nil {
		t.Fatal("Expected error for missing session_id")
	}
	if !strings.Contains(err.Error(), "Event missing session_id") {
		t.Fatalf("Expected error message to contain 'Event missing session_id', got %s", err.Error())
	}
}

// This is thoroughly tested via integration tests so not repeating a ton here. This is
// more so we have a unit test framework to expand in the future.
// todo: move more of the testing logic to unit tests and have less in the integration,
// then move integration into its own folder
// this is a follow up PR (nikita)
func TestProtoExporter(t *testing.T) {
	gcsProxyClient := gcsproxyclient.NewMockGcsProxyClient()
	rc := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.NewRandomRequestSessionId(),
		"export-test",
		secretstring.SecretString{},
	)
	incomingContext := requestcontext.NewIncomingContext(context.Background(), rc)

	// todo: this will almost definitely break
	var requestContextFunc getRequestContextFunc = func(ctx context.Context, tenantID string) (*requestcontext.RequestContext, error) {
		return rc, nil
	}

	exporter := NewProtoExporter(gcsProxyClient, requestContextFunc)
	tenantID := uuid.New().String()
	eventId := "test-event-id"
	event := &pb.RequestEvent{
		EventId: &eventId,
		Event: &pb.RequestEvent_CompletionResolution{
			CompletionResolution: &pb.CompletionResolution{
				AcceptedIdx: 1,
			},
		},
	}
	pubsubMessage := &pb.RequestInsightMessage{
		Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
			UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
				RequestId: "test-request",
				TenantInfo: &pb.TenantInfo{
					TenantName: "test-tenant",
					TenantId:   tenantID,
				},
				Events: []*pb.RequestEvent{event},
			},
		},
	}

	err := exporter.ProcessMessage(incomingContext, pubsubMessage)
	if err != nil {
		t.Fatal(err)
	}
	data, err := gcsProxyClient.ReadObject(incomingContext, nil, tenantID, "request/test-request/completion_resolution/test-event-id", gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
	if err != nil {
		t.Fatal(err)
	}
	unmarshalledEvent := &pb.RequestEvent{}
	err = proto.Unmarshal(data, unmarshalledEvent)
	if unmarshalledEvent.GetEventId() != "test-event-id" {
		t.Fatalf("Expected data to be test-event-id, got %s", unmarshalledEvent.GetEventId())
	}
}

func TestDropError(t *testing.T) {
	// Permission Denied errors should be dropped.
	if !dropError(status.Error(codes.PermissionDenied, "Access denied")) {
		t.Fatal("Expected to drop permission denied error")
	}

	// Internal errors should not be dropped.
	if dropError(status.Error(codes.Internal, "Internal error")) {
		t.Fatal("Expected to not drop internal error")
	}

	// Non-grpc errors should not be dropped.
	if dropError(errors.New("Non-grpc error")) {
		t.Fatal("Expected to not drop non-grpc error")
	}

	// Nil errors should not be dropped.
	if dropError(nil) {
		t.Fatal("Expected to not drop nil error")
	}
}

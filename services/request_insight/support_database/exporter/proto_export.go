package main

import (
	"context"
	"fmt"
	"strings"

	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	gcsproxyproto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// ProtoExporter is the component of the RI support database exporter that writes proto data to a
// GCS bucket via the gcs-proxy service.
type ProtoExporter interface {
	ProcessMessage(ctx context.Context, message *pb.RequestInsightMessage) error
	Close() error
}

type ProtoExporterImpl struct {
	gcsProxyClient     gcsproxyclient.GcsProxyClient
	requestContextFunc getRequestContextFunc
}

type getRequestContextFunc func(ctx context.Context, tenantID string) (*requestcontext.RequestContext, error)

func NewProtoExporter(
	gcsProxyClient gcsproxyclient.GcsProxyClient,
	requestContextFunc getRequestContextFunc,
) ProtoExporter {
	return &ProtoExporterImpl{
		gcsProxyClient:     gcsProxyClient,
		requestContextFunc: requestContextFunc,
	}
}

func (e *ProtoExporterImpl) Close() error {
	err := e.gcsProxyClient.Close()
	if err != nil {
		return fmt.Errorf("Failed to close GCS proxy client: %w", err)
	}
	return nil
}

// Process a single message from the pub/sub queue. This is compatible with RequestInsightSubscriber
// but is intended to be used within SupportDatabaseExporter.
func (e *ProtoExporterImpl) ProcessMessage(ctx context.Context, message *pb.RequestInsightMessage) error {
	switch message.Message.(type) {
	case *pb.RequestInsightMessage_UpdateRequestInfoRequest:
		request := message.GetUpdateRequestInfoRequest()
		requestID := request.GetRequestId()
		tenantID := request.GetTenantInfo().GetTenantId()
		log.Info().Msgf(
			"Processing UpdateRequestInfoRequest for tenant %s, request %s: %d events",
			tenantID, requestID, len(request.GetEvents()),
		)
		for _, event := range request.GetEvents() {
			err := e.exportRequestEvent(ctx, tenantID, requestID, event)
			if err != nil {
				if dropError(err) {
					log.Warn().Err(err).Msgf(
						"Dropping request event with non-retryable error for tenant %s, request %s, event %s",
						tenantID, requestID, event.GetEventId(),
					)
					continue
				}

				// Abort immediately if writing any event from this message fails. The pub/sub
				// queue will retry later, and writes are idempotent as long as the publisher set
				// an event id.
				log.Error().Err(err).Msgf(
					"Failed to export request event for tenant %s, request %s, event %s",
					tenantID, requestID, event.GetEventId(),
				)
				return err
			}
		}
	case *pb.RequestInsightMessage_RecordSessionEventsRequest:
		request := message.GetRecordSessionEventsRequest()
		sessionID := request.GetSessionId()
		tenantID := request.GetTenantInfo().GetTenantId()
		log.Info().Msgf(
			"Processing RecordSessionEventsRequest for tenant %s, session %s: %d events",
			tenantID, sessionID, len(request.GetEvents()),
		)
		for _, event := range request.GetEvents() {
			err := e.exportSessionEvent(ctx, tenantID, sessionID, event)
			if err != nil {
				if dropError(err) {
					log.Warn().Err(err).Msgf(
						"Dropping session event with non-retryable error for tenant %s, session %s, event %s",
						tenantID, sessionID, event.GetEventId(),
					)
					continue
				}

				log.Error().Err(err).Msgf(
					"Failed to export request event for tenant %s, session %s, event %s",
					tenantID, sessionID, event.GetEventId(),
				)
				return err
			}
		}
	default:
		log.Debug().Msgf("Ignoring message of unsupported type")
	}

	return nil
}

// Write the given request event to this exporter's GCS bucket.
func (e *ProtoExporterImpl) exportRequestEvent(
	ctx context.Context, tenantID string, requestID string, event *pb.RequestEvent,
) error {
	var err error
	var eventKey string
	if tenantID == "" {
		err = fmt.Errorf("Event missing tenant_id")
	}
	if err == nil {
		eventKey, err = getRequestEventKey(requestID, event)
	}
	if err != nil {
		// This event is missing a critical piece of information. Retrying won't help, so just drop it.
		log.Error().Err(err).Msgf("Dropping request event missing information for GCS object key")
		return nil
	}

	eventBytes, err := proto.Marshal(event)
	if err != nil {
		return fmt.Errorf("Failed to marshal event: %w", err)
	}

	requestContext, err := e.requestContextFunc(ctx, tenantID)
	if err != nil {
		return fmt.Errorf("Failed to get request context: %w", err)
	}
	return e.gcsProxyClient.WriteObject(ctx, requestContext, tenantID, eventKey, eventBytes, gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
}

// Get the key that the given event should be written to.
func getRequestEventKey(requestID string, event *pb.RequestEvent) (string, error) {
	if requestID == "" {
		return "", fmt.Errorf("Event missing request_id")
	}

	ref := event.ProtoReflect()
	eventType := ref.WhichOneof(ref.Descriptor().Oneofs().ByName("event"))
	if eventType == nil {
		// This is expected during the rollout of a new event type.
		return "", fmt.Errorf("Unknown event type")
	}
	eventId := ensureEventId(event.GetEventId())

	return strings.Join(
		[]string{
			"request",
			requestID,
			string(eventType.Name()),
			eventId,
		}, "/",
	), nil
}

// Write the given session event to this exporter's GCS bucket.
func (e *ProtoExporterImpl) exportSessionEvent(
	ctx context.Context, tenantID string, sessionID string, event *pb.SessionEvent,
) error {
	var err error
	var eventKey string
	if tenantID == "" {
		err = fmt.Errorf("Event missing tenant_id")
	}
	if err == nil {
		eventKey, err = getSessionEventKey(sessionID, event)
	}
	if err != nil {
		// This event is missing a critical piece of information. Retrying won't help, so just drop it.
		log.Error().Err(err).Msgf("Dropping session event missing information for GCS object key")
		return nil
	}

	eventBytes, err := proto.Marshal(event)
	if err != nil {
		return fmt.Errorf("Failed to marshal event: %w", err)
	}

	requestContext, err := e.requestContextFunc(ctx, tenantID)
	if err != nil {
		return fmt.Errorf("Failed to get request context: %w", err)
	}
	return e.gcsProxyClient.WriteObject(ctx, requestContext, tenantID, eventKey, eventBytes, gcsproxyproto.BucketType_REQUEST_INSIGHT_EVENTS)
}

// Get the key that the given event should be written to.
func getSessionEventKey(sessionID string, event *pb.SessionEvent) (string, error) {
	if sessionID == "" {
		return "", fmt.Errorf("Event missing session_id")
	}

	ref := event.ProtoReflect()
	eventType := ref.WhichOneof(ref.Descriptor().Oneofs().ByName("event"))
	if eventType == nil {
		// This is expected during the rollout of a new event type.
		return "", fmt.Errorf("Unknown event type")
	}
	eventId := ensureEventId(event.GetEventId())

	return strings.Join(
		[]string{
			"session",
			sessionID,
			string(eventType.Name()),
			eventId,
		}, "/",
	), nil
}

// Pass through the given event id, or generate a random one if it's missing.
func ensureEventId(eventId string) string {
	if eventId == "" {
		// Publishers should be setting the event id, but don't fail to export if they don't.
		log.Warn().Msgf("Event missing event_id, generating random id")
		return uuid.New().String()
	}
	return eventId
}

// Returns true if the given error is one that we should just drop and not put back in the queue.
func dropError(err error) bool {
	st, ok := status.FromError(err)
	if !ok {
		// This means it's not a gRPC error. Don't drop it.
		return false
	}

	// Drop "permission denied" errors. A known likely cause of this error is if a customer revokes
	// their CMK.
	if st.Code() == codes.PermissionDenied {
		log.Warn().Msgf("Dropping event due to permission denied error")
		return true
	}
	return false
}

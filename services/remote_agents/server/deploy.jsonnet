local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local lockLib = import 'deploy/common/lock-lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local appName = 'remote-agents';

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(cloud, env, namespace, appName);

  local requestInsightPublisher = publisherLib(cloud, env, namespace, appName);

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );

  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  local enableAPIProxyTunnel = namespace_config.flags.loadBalancerType != 'ingress';

  // When running as a test, enable a kubectl proxy tunnel from the remote workspace back to
  // the private API Proxy service.
  local apiProxyTunnel = {
    objects:: if !enableAPIProxyTunnel then [] else (
      self.sa.objects + [self.role, self.role_binding, self.tok_role, self.tok_role_binding]
    ),

    sa:: gcpLib.createServiceAccount(
      appName, env, cloud, namespace, iam=false, overridePrefix=appName + '-api-tunnel',
    ),

    role:: {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        name: appName + '-api-tunneler',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      rules: [
        {
          apiGroups: ['apps'],
          resources: ['deployments'],
          resourceNames: ['api-proxy'],
          verbs: ['get'],
        },
        {
          apiGroups: [''],
          resources: ['pods'],
          verbs: ['list', 'get'],
        },
        {
          apiGroups: [''],
          resources: ['pods/portforward'],
          verbs: ['create'],
        },
      ],
    },

    role_binding:: {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: appName + '-api-tunnelers',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: apiProxyTunnel.sa.name,
          namespace: namespace,
        },
      ],
      roleRef: {
        kind: 'Role',
        name: apiProxyTunnel.role.metadata.name,
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },

    tok_role:: {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        name: appName + '-token-getter',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      rules: [
        {
          apiGroups: [''],
          resources: ['serviceaccounts/token'],
          resourceNames: [apiProxyTunnel.sa.name],
          verbs: ['create'],
        },
      ],
    },

    tok_role_binding:: {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: appName + '-token-getters',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: serviceAccount.name,
          namespace: namespace,
        },
      ],
      roleRef: {
        kind: 'Role',
        name: apiProxyTunnel.tok_role.metadata.name,
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  };

  local sandbox = {
    local _sb_base = {
      region: 'us-central1',
      namespace: namespace,
      env: std.asciiLower(env),
      rev_ip: null,
      rev_ca: null,
      rev_sa: if enableAPIProxyTunnel then apiProxyTunnel.sa.name,

      local img_tag_flag = namespace_config.flags.remoteAgentImageTag,
      img_tag: if img_tag_flag == '' then std.asciiUpper(env) else if img_tag_flag == '{namespace}' then namespace else img_tag_flag,

      img_repo_name: 'agents-%(env)s-%(region)s' % self,
      img: '%(region)s-docker.pkg.dev/%(project)s/%(img_repo_name)s/augment-remote-agent-virt:%(img_tag)s' % self,

      ssh_proxy_domain: error 'ssh_proxy_domain required',
      ssh_proxy_hostname: 'ws-proxy-00.' + self.ssh_proxy_domain,
      ssh_proxy_port: 2222,
    },
    local _sb_prod = _sb_base + {
      project: 'agent-sandbox-prod',
      cluster: 'gcp-prod-agent0',
      ip: '***********',
      ca: '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',
      rev_ip: '*************',
      rev_ca: '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',
      ssh_proxy_domain: 'ws.augmentcode.com',
    },
    local _sb_prod_eu = _sb_base + {
      project: 'agent-sandbox-prod',
      cluster: 'gcp-eu-w4-prod-agent0',
      ip: '***********',
      ca: '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',
      rev_ip: '************',
      rev_ca: 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVMRENDQXBTZ0F3SUJBZ0lRSjJsUThwa1VxdGZuT2sxMDRMeGVlREFOQmdrcWhraUc5dzBCQVFzRkFEQXYKTVMwd0t3WURWUVFERXlRMk5HWmtZVGs1TUMwNU5UVTNMVFJqWVRNdE9XVXdOQzFrT1RBelpEazVNbU0xWWpFdwpJQmNOTWpRd016RXhNak16T1RBeVdoZ1BNakExTkRBek1EVXdNRE01TURKYU1DOHhMVEFyQmdOVkJBTVRKRFkwClptUmhPVGt3TFRrMU5UY3ROR05oTXkwNVpUQTBMV1E1TUROa09Ua3lZelZpTVRDQ0FhSXdEUVlKS29aSWh2Y04KQVFFQkJRQURnZ0dQQURDQ0FZb0NnZ0dCQUxkcUg5d3ZlaGtYRm5DRm44VVVyZTZaOVo5YTV3ZXNiRW9SQWU4Qgoyd0x2U0xFUW0yNXNHVkppYVdaWFZ4RHpZUVFJNjFvS0VvYW9DVmVqREVjTWlvTDgxc0xUUUdNdU94QUxVdXhsCkhwb21HZkQrNmEwNzh0UWdBRzNxeDhBVktCRldzR25DbEFOd29FQkJwWHpYRVZuNENGdWNpTmhRN1ozQS9LK3AKUlJFM3M1N2x5d0VybG9DUVBJL1ExdXNsMkVHblJjVGI5eG1LY1FUcFk4OU9qcExtWkxWTTcvSTgyYUt5R0I5UQpOQjY4cmRHZDJ1bmRsTXVSNEF3MzZKNDk5ZkZGVnVDTWlNMDJtOHVXRG1HVFdnczMwTlZKdGpuVWtiVjZjZjZqCnE0bFBwbjQxUzBxT1gzOUNsVy83OEpnaXZxTlBVMzZYdExkd1NGOHFwRWVSQjZPYnVOY05sQXZIbHgrM1RyOE0KMzVtdGw2WVdEQVBMTnpVQVRSb3o5TkFWcVhic1BpZ2FsK04ybGNFVnhrbTAvQWZlRjd5aTc5d3A3QUpRc0tVVAo4a1VLb203REpYYlFnWjU2ZFlSTTh4OSsxMjdITlI1SmZyYWQyUU1JMDdFaEdRMEZpWGM2UkFQTEk2djNEU3ZnCjRyMFB6RmphZEF0dkhscklRbk90RlRXYkJ3SURBUUFCbzBJd1FEQU9CZ05WSFE4QkFmOEVCQU1DQWdRd0R3WUQKVlIwVEFRSC9CQVV3QXdFQi96QWRCZ05WSFE0RUZnUVVlRHJ0a0VWWHdZWDMvK0pJbUJlSEtQT05YckF3RFFZSgpLb1pJaHZjTkFRRUxCUUFEZ2dHQkFCQUxyWlc3YVp0cnkwSmRtay9mV0ZIL0RpbkRPZTlTVkR3bGFOaWhCMEE0Ck80ZHVSMloxK2U4SXkzL01YenhUaXd5bFdzN3VYUUtPM2ppaVdqSGIwSHYrUTFzMkJHZ0RSc2N3YXhGME5jSHgKdi9XUDBEK1Y1YjVBV2M0T0FQWUZ5V0xXZnI4SFBkNURTRHdjYUZkUUdwTXBxT2w4TFplVDhmRzc5bWYrOUZhMwpmSlFXd3RobmFlSW9JYWIzWlZhMDJ4RTIzWVo3WWliNVFTUktKeDlCdGN0d3h3eVVnQVQwRURRR3c2NkN6NnBkCk9ETGc2L1htUGdhdzdXdC83SlRZamZEQUgxd2NIb1ZVY3ZwazZZSDBHVnovNVJ2V2tqQU5tQlZIeFB0b0lxRnAKeUR1OWkvYW5xTmVQbGd4dlhwcTdhdVQyaFJnM3I1VXpxNGt5L28yNGg0bkNjYzlQOEhwOEZwY3EycVpNelJTQQp5RjlWbWRMbGtHQlROelZxNG1wNnphejRiSTlPSmovM09QY0ZQM0ZhVDdEVThoQ3hMWVNqaW52V00vMzhZbGZtCk1pZi9YOFdKV2xIWkt5dlBHcU8rVG0rU1JnTW0ySHNEOEVIaUVFRFkrRnhDRFRZZTNpbFVJYlBhZkV3THVWTlEKL05JWHNwa0pkN0s0NGlHVm92b0hzZz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K',
      ssh_proxy_domain: 'eu-west4.ws.augmentcode.com',

      // NOTE(mattm): CC support for REMOTE registries is limited. For EU we currently
      // use one created by hand, not in `agent-workspaces.tmpl.jsonnet`.
      // https://github.com/GoogleCloudPlatform/k8s-config-connector/issues/3720
      img_repo_name+: '-remote',
    },
    local _sb_dev = _sb_base + {
      project: 'augment-research-gsc',
      cluster: 'gcp-agent0',
      ip: '*************',
      ca: '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',
      rev_ip: '**************',
      rev_ca: '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',
      ssh_proxy_domain: 'ws.dev.augmentcode.com',
    },
    sb:: if env != 'DEV' && cloud == 'GCP_US_CENTRAL1_PROD' then _sb_prod else if env != 'DEV' && cloud == 'GCP_EU_WEST4_PROD' then _sb_prod_eu else _sb_dev,
  }.sb;

  local remoteWorkspaceSecrets = {
    local o = self,
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: 'remote-agents-workspace-controller-secrets',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      template: {
        metadata: {
          name: o.metadata.name,
          namespace: o.metadata.namespace,
          labels: o.metadata.labels,
        },
        type: 'Opaque',
        data: {
          kubeconfig: |||
            apiVersion: v1
            kind: Config
            current-context: %(cluster)s

            clusters:
            - name: %(cluster)s
              cluster:
                server: https://%(ip)s
                certificate-authority-data: %(ca)s

            users:
            - name: user
              user:
                token: {{.satoken}}

            contexts:
            - name: %(cluster)s
              context:
                cluster: %(cluster)s
                user: user
                namespace: %(namespace)s
          ||| % sandbox,
          rev_kubeconfig_tmpl: if enableAPIProxyTunnel then |||
            apiVersion: v1
            kind: Config
            current-context: tunnel

            clusters:
            - name: tunnel
              cluster:
                server: https://%(rev_ip)s
                certificate-authority-data: %(rev_ca)s

            users:
            - name: %(rev_sa)s
              user:
                token: RUNTIME_SA_TOKEN

            contexts:
            - name: tunnel
              context:
                cluster: tunnel
                user: %(rev_sa)s
                namespace: %(namespace)s
          ||| % sandbox,
        },
      },
      encryptedData: {
        // Seal with whole-cluster scope:
        // kubectl --context="${AGENT_CONTEXT?}" get secret/rwc-superuser-${ENV?}-tok --template='{{.data.token}}' | base64 -d | augi k8s seal -K "${PROD_CONTEXT?}" '' ''
        GCP_US_CENTRAL1_DEV: {
          DEV: {
            satoken: 'AgAaf+iqDU63q3Fsn3y60hDm1PIBXq/E0zJfubxsopXpLVSDOxmGuZ+XCUyAXt0myOstc6BOiEBokrD1ZGm/3LcOyk8/uNvs6J7LpQy8AYbez5M2/ts1ZOUYFg9RgprE0UFeSQAXzVFY3nf6xfUtOGiInGhwzGaAlnYG7Yjbvp/+lX5euNoOUvo3vRZ3KZ537w3sXVFntOVPauLquy6IWZg125lRZy0GMyFAi0ID3LgLpnhlUIXc2q1FhTfYi3SGgthQz3oUhiMozCljh8pGe4boP4QyPOLt4GNqAxqxEGdnQq3pAo71wFxPIKMn3RMgoLUibednqTqUOMV8oSAajWSqzXwgJ6G5hCcZc0WRm4Pf+fnHEyNT63daRAPw4OpAarruBrDyswGA7D/l8aqMtfDueL5VsBcAXgAUo6QTljZ37vctIhhBEgne7rMvBny/HDKgHvN37Zs2DhibpsooEkbKhE/WMO4O8xKfB83GYG4atueAOa1mAKQWjKjTfuFrphJSXRfthzmQNGuYQcN6N0zSFJB8r1c1V0Qga/Gr3MLZO2xHjfacWjaYwlA5SWDLqzbkaRXOl/vGZ8v6rRrLxh5TCA2hpz6iHO2ehCijJXzNnBr619XGb/Bk/BlgoBuoqnH+vuROJbxlg2ELH3u2LynlLqONCSdm0H/Ip6GhhEcgLC7trbYF4q9dn3Lvcs8GaHASmp6tWVi3sZQYLPCsiOrgjK60rF1KINtha3ITetwI5mSXCGAd+RjY9C8sjSZrJv6pH+62YTR4tBnDbnRP6i0gFwGTgSg35RnV4mfbD+Td723WSKgvCqanH9+x37wJ1cX7XevId8UA3gGXhGSxdPCIX0ZqBQKDDEISWlG5cQ9r6g9YobE0lJvgltd8GdTwb+vaap9BwuwpCZ6d3jExmmiAbaI53eofRMHcW535cE3+8OJm+yt25msJRTnRIxQbpjvsDpYos0EtkMzCxaBCModfxaFOT+cbCbshf2MS0GkRlEYSrSoxGnW9dRVmXHo4/fd1aN1krlT5jnxXv2B3tyEX0PUlG+b/v7bpN64mGlGp4RxE+5bcdf3MkR58a0d48lnFTiKeRGTdhC7yUfZeoAsq8a4PPz9Bu0Gq4B7ieZmYE4CcqtrdQaZHOR0f/3BRh7Dd0yYtQiOIwDUr5n52vunMGeuFsUqp2vs4i94VaE+X7ZeTx5T04kpQceTkHGs/L6zJh90/jNzb/OBXNP7tM10dVxnmra0Zew+1jnUmBnj6rCwZailYg8oCxRREHvJ76J/md0NdS+KQbsh27S9wjESsqX+C058cx/rENyzM9pSXfAcClgGvUWFf3qk/3uAm20PzsriJb32niW2MqIqULXkAGuZ+tWFnKGBoVe/ZSdZBIE6d3mxJP2MqHIkvreyx8zwXfDYp5nMRg+mtrnBpnEhxeWwCqqi9CpAdZOXz8cZwYcoUdSR/+TzqU13dUQWEMdUjw/TQ6PVEdzj6Om5jXy/GRllplVCjiQfKIZ72FV+vmdz/m8+plEPI8O9dSKwrroM2mTNwJo1wiX1mSvYUX5aNl9hSKThqgYKA2pd+KrSWVoupOrRs6e3cJJD0DBNcNkFUr/rua9pnO4JinZdOJDPii2U0WmRm3xNhqpJQaxqmYCb5V1lRyG0QxiDxccQ9kHWAz+zjpCLa/3HVeL9CiOhrNaU1ko4VQo1pVAIkRaPn65TWaSTRYtx4Z+mec8kB+DbFTAU/ELU/fpChP2BUM6PN0AP9KqpBzGBcaEJLaSZZAjxh5H8tiD62TEf5uNKiRC0BqjbF2Nal9NzWcgaliudj6Tu6BxCrWIkx7LKN17P1L2pCmYmPdsNPvAyyJ6q7ITN3w+DZr6qUI1/m+v5K6rdOF0xZyhn/TZjTQk4zS15oaYkDbmv07XpSAKnPLCjX6ukGjVIq9ENCEjvdKgU7wh0kriKgLKajQKpz',
          },
        },
        GCP_US_CENTRAL1_PROD: {
          STAGING: {
            satoken: 'AgAHX+Y63GFQbtBMF4JosCZg0Snbyv7VSHQ2Q2m1xfUAkDrXN7YG8ByzRD4mraTocb0rjdz3Vqqt62zGOTj8IGrNp1Z204/mlDANp+ZZ6oy5Qxbo95bMFDHyrt51KTt00gHMBGIUB3xpZY0iXFFwrNquBjlHD3MfMKXsqGZwBzczx65FLBqgyjy7yoLrub+gJ43Vfx00336/JLiB9Z0amNBqE6b/yJfPHRnDmqkFA0ivy8mpApdhoBs0tewnlxjKnV7eZnH/GSBuRW5PSqA1b6JHrgMs2V3AAuhttU2HSVqJjlS1Bj6QnRztZ2LmOwEcJl8oP89dMr7lSMvVlFoxAtENUoyC9y/vR7LGfXQdJHC6bDRk1TTmcZYE0MKG5o2dFJPeOn5+hGTpOubemhUsb2fHIc3E4J2fYHkmlr4DFycZ8r6FRtH+dbneefP0Y2/9wNoP2YgzFG9vlIUwygCrc77fALiH5O20dwM1X/1cn6tZyjXRRWo/aSkf8IBBV0wrOdeBwyFBxmOYqMiaS19hA7RUXVNpeYjbF7icgxJ2AET3Mn7J0+/d033WA5zlarOYC2so2i8/5z6Eea/CaShq2hfFjd8jF4w8r1Kpw4Kda3hvKSXJnDClY0wuJC3bfy90Bb2lzu128jbi/gp4qGwupv/PSZ7KbU/dJJ74qSOfumd9UxPqlwFWCw5uWnbn3DapiPXPzC4A4OX44AI97JIH9Guj8bisEt0dTkoPf44+Q8LOQe5HJ0W96htwoLGc2qwiSvUqWonygqosxMNwmcmI3RzjaiJokGk6DJTj0lXIOAliYj7OaPAjZu1YHrcJtizzVMyh5VA6OaFG7FAvugod4+C+lM5o2gckr/r2sYSSS4m/2DsOCuoGlmZb0UXDi2iUiz5AqRLdNqh5NyrCqeNg3kR/CyhrNGqGUyHPJU5HvaArJ9nYFS/VstPUhKCtmBg6i5doYGAnElTUdxs1WZlKdolbPVwXq/ws1LtAuwnZEcQNpM6RHOweO3jwxgb840Ceq9dIbbt22fpZwciBSeuLyPRo2o8lZyFyZ/SeI1eprmps62VgQ28+q4fXxXxcyncrxKwnPyJdw3ekPZuZV+6+CeDEx/IPly1jrPyVJ935g7joTPvZynDVWe9RnzT1neYHSP2T0U/BTReOMwOklrt4UtdXo63Qy+YmSG/5kQvdgVlu0PdsPLN60WJCY9VjJO5nD8LaabyaVC4jxrS37KoJG8mOY+2dhfPMbd2Ib2vARG78kTmItlD3f6z9eqLD0KIfb0plOqnzvbkbvx2RShcqu40dQqdVeajjVx1DPvU/9Z23L8YDbbPYk7paSqT8M0KpL4FcqemBrhZBLbz65WF8EE0sg71E0TuYaPZHMtU29PDsZ4ODmTgG9X+6/TOosrc5YFUBJ513FlJqGZHa5cm2AjedHn4GRwgi7pBeDSFd8n1gbiM2JOHiGjkFIdl3uJnLICOmzwCShJQ8ttCs0F/FsodOaaApRt9WaqaAr7nC6Ec4cVR2Sf+zv6a3QcZ1+o/Jf1dud4tb70En1U8SSb7JuN+OT/MaD8Un1tp6ISu2Y/QB1mcnwTRsd5HtN3TEZkCAq5v30b4HWH3epTLVyfxsUJRvy0tevujYDvIvCqtxqs0xwR2zpxyczke7kGzzIrHU9SyBboljVb0LUtcq4PL9y5e1BNWp+ZZc1sSgIuH0OV8Vu9kpdHCGuylo6KqHh141TUEwZq6Yl7V6Ig8moQbc0nz7qb+ebtzruHOauvtbGhHC3DdWHwPvpyfb7qTPuQv+5TQkQ86LQbKGO1rnK+UMvVKOwKcHpbaYKX+JKoVBsVSC4oJhoaYW962BECr1LfCUUQYP9dFRzvrSvqiJ3l1K3IRvBHly2y1AoRvxWwFUJHcbM9OrWoDnieoyq4x93FTDpIQ309PZ+UhTGajSVqUlKug4zGjUlPVN/OPqffF+Ag0kyAVCGGDIW27bfQ==',
          },
          PROD: {
            satoken: 'AgAMNUydAOTkfrw0ECgJsXk4NUkGE3xyx7JJ3NTdTuCTFoVeVF5WQSwFURSpNrt9NobUVG8jzqUV64KUVkxP9lrZcm/Mlwbsnc6WNF1BUMMuUpsz/hFv4/N5+GUEL0vz4CbXadF7S3hrRKN1rRSWfsse7sj7TlIli/IJxUVC3ZvQE0gf7XxlHkEO+RTUqx5m2q1oq9OaYPx9qXY1+8D4R+WezWGqGZxTpQTopOiSMt6OBikWXArbBNaYsn2n0e9pxk4gXz68tWQDPI9vGNTdPF7uObSgVZgcEzo/zhzK49p8X5gpj2Xd5r8sHaw3gHiZL0RJgHH35vltu2wAS6DoBoAG/FKuwhNUs6AvEAVOopR9M/Gjy/yQXo8MNvxaLIEnf3jN5eOFLKI03APSKBf4Yv9Q/md0pMNuzCwboEe4mFECruDege4XzNaHMWsWHx+z2qaOJeyf05OAn1w0bM20MAlbnsesG0YeblWGqcewlBtNYP4NNfpzA0ZfFYDQFKJpz/iQYSeBUAhIjZbwxv2jNM7K4gCVSKCrEQrH8BezfKBcpI2zvXxhKWVIJ0Tsvb/jNJZPW8ivtc+9sngppB8lexnPBrg0dwDW2MZ7KyDCw07L9m0boSx2Ou4vIolZwJCSFcGceYY22UX5U2cMlnWfVK/uu4GN6zXpWxdDrcqWSFmqgUJDkWg9Mx1MjhxE4bKA4NkFtiOjFvDbR5vLxzn7kdrmIYdbYwwIod/HnW7KMx9lQYcJJcfmGfD057Vw7T4bU1i6AcOCgepEngAYN4w8kyyR1sgv2ETZaFCdigYfs/r0Ku7hUGNBrkC8+Ct8Ken6xP+TuXeMqN/W0NDWuL4O4VbsDwFL+yPXmffyQvy8t/LR8inokdcEe0Swl/+YYzv7hjLWE8MMwrSTB9i/sHe3QUurP6apUIjPJXtBJ3Spq/jpp6W51g23Qam7jfBUJTSW6lC1rAelryBVM2aHY4tA2T/E24p7UWnFoEvEC5Wxg9ioh+RIIrwMs1MBcXzbjHYikMlbOUYfRxvBMlOZWATm7m60eZgHUWjihKZ/4y2w7WZO4QJW/0+9wwDIotjElF4MfbilyTeQX0Bfc6qwCv8g6ckIJNm1PTM+vERUixKD++n8roPpy2slwveeTJ0lWJ10XYLVFxedxHEZSVdmdBt+O+6WMcJEgc+bOFGACUx2SbgD8QpSc9jQzu+A+W+wkzkg0g0us/KAXkhBMI+HoKSosUx59EZ8WK+kTaL5+DxBfo+h8XNNmys+RwMvlMyN/yA3DrSq2vloYMS0A9X51nhDSZNn+jlJNdvdYXR2gLpn+/Xnwr5gsak69ILCakPBWB7T3anvwkgqmrsPFMrOiYVjn1WPZLjAhLp2Drvzp0S5EwMTvjvl9cFEOvwcvrmXfpYynWOgWXbBKlbmqPI57uO3khirmsrj7Px4XGBfapW1ntEQwIZGeDaT6pDZZE3p7+SLgt34O2kLYc41RpTCfqYlZ/FMmEzgl1KEuIBMpH7I+VfhpSiuVF6MnuYHhtaG0uu8Lv8RUnaz5TYIoDczcDHkjgmOoWJSkSSNc5wSSlukKl/tmLuNhH637y4ZBE8tTVhgdecFaY98h6bSn+ActYaSRfb0ijENask5OBV84d5ufFCOMP9aMZf3+tEsVS1lnbc/aJ4yzqlcQm6DbSuk9LbCq7d8jHGxOR+jSfYafZol75KaVyPIidtsrz7hABibg+APIR1IOrgchNp4IJsw9qD9VnDS0NIf2J44psTryDY/fvbnqqb1utGLpEueF3P+UYlatgjrNGn/GzhJtEIdR8IXlfdHQNUba+pnA7Wco5jinP7uIP8ideQyzERJP7bzBe0T1uGKgxDMWeUR6hr0qthiYCNYS9SyNHguud4BI6vCTkNsyC6P2ovsIOWTGg0wXgj++wFaaGIthdIBwMoAux6FKjEjbXJz7VD/3XCIQQSj2Q==',
          },
        },
        GCP_EU_WEST4_PROD: {
          STAGING: {
            satoken: 'AgCPUbdWgTv/lI40Fe+fE3y4YdxWBbA/iK7xmH+2RPCQHwH4zm5rgw6wAoyuclc3aSyY62ECv45SLukN3zNTAupo+qATdFrDEG8RGzSywpv3cTDRNBkiPyrwI5Irs9dJJQm2oJvSWHyTDEiwdHHakMrHWb3hJU4+lHIfEITG5/KvxUhzrfjpqr8FWZiayam7jg6lxrSkgir+em7AEAUQ43F0TZrQMTTISSnYxN0lf1MT/hzuQfYgaWGddYoXdvHTDS7Utt7K8voC86cvty3QdXboWythZX0ayVTRmdLaU/FYPETvibsHBYZcmBhzrdGv+nPW2QfAhEh26X4oPpHlIKVWFvpTwZQNyid/X2CCnefaoW/fZyXl/P+g1SH/x9sdfVU13tMzV0gwUOjBkY6cNFwhci6hA17DZ+Ws4zcjwkgUfTe5zuXKb0hJlG21F4Um+2PbKvtppV7bJ1jvHnoZ090T6a2GwCMZz8cu1hCfA5egXqQ77SQeakxo4rSfM33SRonHsrasqroHhIs4KJQvbA2RFMXNVTDabZIQ2eg/CCued7IRgv0MIuoAdPfWAXXjdC+ym/wXJyQcBrAS0IQzGx955x6NWApoXGxZX3Y1Z8owaICVUdQI5iI40kYDpwFB3E/wAp5iU449/mw2vU71vifc4UaOaxp6uYO8WnfJx9Duhumcq2iooLSzkz4OBSfEy6UrgOjB5ntgvGWUHmJwRZAHJsqG27NYrWRdTZKhMd+a5j1ZFxFb29kio+HWpyyYaHc5yrHL2T6inu8xrmg8WQN9kk1gZF2KI68fnKPivuCHPLkXbrF+hjXp7s6XOVtgjJ4UGuAt9lDqxYxHZEIYrrK8IrLl58lofIOl7HYYIuD6D6d/kDd5zLIHsgyfQ8Nb5GUm23ipqr5V3QPXVJtrPWa6oXgMqQftfNYIJ8qDIDKcWc/fakqvrwoiKeUqSax1PPFd+cr1a0EzLyst0UdoDOZGIXiAHIIInXrY/2pJR6gQvhwO1UzVz7JmPQSd8q3HEkCBdnVO5Amuzor8q1dKbeXO31dcg8qmusVaSE7rykuazi4TJOO7X284EerkycKWj3VBlfgEpw+S/ug37rrFVQLlAb04k4/cR0WbJqW5zDBOJRVln0TSm43SuuC80lv7KfyJFQWwgzZ/+zNOwERlyOG3IW/HOK8DmR4p4PhmxOH7Xfj8FEntLEhMn8bfNEnEaM/LLh59bG0y8dTqntwROplvGniBBEkKDERua37aikqhgfRVpesG7OrRznG8ARnarT0+vQL9MKUHX7tzNL5G/g9JuTqKUFtZBAOwvncNQuj9Nngc1p/VSxy5hQgTVPAQSIMvD7bWuEqkIEyW7+rPRGAcyZwOxDuw4mvyBUVU/9hT6N0VVBMJpmN71uWtx/jFAFwo+TdeUSv4a5mYj+x/ZHy9MG2JdVaTUnDz7sSpK9nPKPh04u9DPRTaDEz0+oT3q2gAPE0tQ1/aUskuWXX/+HCM13ZiUq4uZNZJQZrNDy25i4NDHWDco3e5FT5n51VIsdZIeYkM8K03IA5pR7HNi+vpZK0C3sopIoXnBYQ+eD/SRL1/jtb19RtAkfzcW+DhgG9yKW34UejNv530azItIqsZ5qUYezbzVT6VJUazBWkdTNaNOqHuxIjAao1Ia9Ba8WDdR8wUDQMBzaNBXu8BqVyTv7Ux/YMzrSfIbbGaDYvv+D9pF3I2iKn4jLZQA8Od2GBacTd4l3SdqJ2lRVU2vBN2c2CMGxHmv/+GQDaLGqBEjV2mHEXcKwt1ceIBb6drxaLnejPnL+eTs9bjJ5HApDc+a7VMH5yao7T/3/lXMqznm9tbmt4CDaiPseJOQuSqAiSpK3OhCaJiW7LK1dPFcsVpgm8kGNZnS5R4uc+hP6BP0Z9M+IjjoHVNFg6KBYw7Z0WS/m9PlIX9s7z+5bZQ58Gy5P7WWsnFCSm9kJrMNJdGLWMS2OQHqNOK2w==',
          },
          PROD: {
            satoken: 'AgBef0QTby3M/+PUG3p9MIGGSQAdXxRuiLZdjgJ+/AxkVNqQ+LF9Am+QrNjuG9a/C3x+9OddS8brYIMUZk+oZ5gJRskzDBwoCuYfOfvKXQ/vACFvPx0o8mWI6MJWM6OxuVR079LKCPF7nLXhyAM1e/V6/VN9vl4Nr2iZwFv4I7saOQP9ICYR6I5fATLIpOrxh1EHc0q07tmoSipbS3mQklDEi9Wnea00XmT3goP9+dkkr24F5l6/YT2bqRztLSV25SQw2iAQz2gJu//4U63/3mVNw5jcc1VsUHTmdajVSomig0Zqun5he/AmqBl6/uSt1fBCO1PL7sTVlxTvz4JkWoMxg2fvngNnP/6Fylvt//6JwgpnG6OAcZVXMSHUfzdxfP6+VreYztah9rtBIL8v+zb2y+1YVKrvv795/slEcwQant8aDmpamgSGRBG1hmvfNLOu6vhD9YHil0zpSruC+/BgLqONTkWbnUTKkoHx/4GvibFbTCuq+Yvsh7E/NOqD6C/UuwvSNN7MIXpa82meFuXD7Gi7stUea7/Twt57gFQuM/9vzi0ELmre4y9U4fug8GXMkqGZ6IfaeYBzpAzLqpZeIZg0vwm9dh0YG4eaxjcDlROjv+MuXnoOtZpSc+lW/16kS7D0GdcPPnWIP6H22yfpFrY64lXf59PSIE7CwHQitHaA4YMQAk4FUvwdeG+ulJvKXf+7Os6i8TFvBzrriBHKMtNM8y1OFyzrSVLq00zhVRFWpWoWSFBNTjOzSBRaxIiVqRioGua/wwuYSXA8DKqMrhhnWMheyQJi6vPj/JFOMI1sO/HNvwK0lmcQiXonYpZAGB5gHu2QZNUUPIFzmotX1WAO6UlohIYUn6f7fA9aWl6NK+ALVi0Tw9QQlL29e3X5mR50u1DkGnna7vp613sbsREftZZwm7ganQ1exHUc0gPyL5pG4ylg5AN5tNqw3b4/4KmqFVyxs4MH64VvAoQSARXaq1JSxP0MFwkhv4+6Zj+yv9c3MhiEf2SAFXzxmgBBoNhoBbu96UMjXW1eBwjTymAazFmCABlU1cepkUTVPT2j4VhCLFmS/Ft0P+0WNggnj3zOVhoRJKyi70Ok0uRyDLJwCtXE2+ogtXQoxoAIrDUjtD5gTpKm7o+Vwpi9ib7tIYyLw8XLqgjksvOi4gn26+7Td2QUc96LjKXk4Fw8PiJ8ilAbxqRm77MJdnL64v7CNib1Vyzqu08m7KOFrIZQxshi99IFGNIieTLmo3tvmdm/BvnpQmExm5Lm6ikgNr4+i7AYyPBie3+koqX/JGEX0OE3eyEP5qEc6dbLqjKpYHz4f+m4lvAbGhWRvEmtC/hJNklXWpgjuVTVtNyaig1XVwgKx2+X2j/YEhkRRPW+cEsrKP97mAXNnTVwIOs8L2F50W1Izs/t4RZ/rvWL7nXj7BUrdIc2lo/rLtcBtoRhQiubblI+FIcd1iqqGDpYfM1kYPgtTRaJkH3cwyvg39BWmFwVpw2F0baybQO6Oy5pcvu+/RdGTY6a/PspNHLTqvQVpDmbMEmw6YXaco985p0rB10RwqrNeIfI01tuIeV66LmsSZp8Oaabv1TJ3l+1M16GWQFPkBhvigUGpd+8NVxzOPXFc/3CjYnLCRXVDOAoj+2tZSuwENPtVEUz0aV/gywdqRs5m95PQu8DnCeJW2TC/r2mB+IcttuTfvp6enu/O9vUlMZeXQZ1gXsura84MMNlODILkFYh0hClEbG7ZjYOdOh3pb4XH6XN8YQWI6lhfAcmBOAIBhibENBqVHu9azeqpbJWwD2b6cz1cPyqNu/y8Hds3/dIVUbKlKipkYs902xF6HNgPEnyDkBwUszrfTgkjqEYkuLJl8BH0yHR65qjlmCq6T/UYysqu+u2k//gNV4psowngRaJ3sWUQRlByKF6BETBTAtBCPB9UHzOngeznNLrRwok+yB9DQDPvQ==',
          },
        },
      }[cloud][env],
    },

    // content_hash is used in the deployment's pod template annotations to force a restart on changes.
    content_hash:: std.md5(std.manifestJson({
      data: o.spec.template.data,
      enc: o.spec.encryptedData,
    })),
  };

  // Create lock resources for leader election
  local lock = lockLib.createLock(namespace=namespace, appName=appName, serviceAccountName=serviceAccount.name);

  local config = {
    port: 50051,
    server_mtls: if mtls then serverCert.config else null,
    client_mtls: if mtls then clientCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    prom_port: 9090,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    bigtable_proxy_endpoint: 'bigtable-proxy-svc:50051',
    github_processor_endpoint: 'github-processor-svc:50051',
    memstore_endpoint: 'memstore-svc:50051',  // Added memstore endpoint
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    request_insight_topic_name: requestInsightPublisher.topicName,
    leader_election: {
      namespace: namespace,
      lock_name: lock.name,
      lease_duration_seconds: 15,
      renew_deadline_seconds: 10,
      retry_period_seconds: 2,
    },

    remote_workspaces: std.prune({
      kubeconfig_path: '/run/secrets/' + remoteWorkspaceSecrets.spec.template.metadata.name + '/kubeconfig',
      image: sandbox.img,
      ssh_proxy_hostname: sandbox.ssh_proxy_hostname,
      ssh_proxy_port: sandbox.ssh_proxy_port,
      node_pool_groups: namespace_config.flags.remoteAgentWorkspacePoolGroups,
      enable_instruction_flags: namespace_config.flags.remoteAgentEnableInstructionFlags,
      rev_tunnel_kubeconfig_tmpl: if enableAPIProxyTunnel then '/run/secrets/' + remoteWorkspaceSecrets.spec.template.metadata.name + '/rev_kubeconfig_tmpl',
    }),
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container = {
    name: appName,
    target: {
      name: '//services/remote_agents/server:image',
      dst: 'remote-agents',
    },
    // the arguments that are passed to the server
    args: std.prune([
      '--config=' + configMap.filename,
      '--request-insight-publisher-config-file=' + requestInsightPublisher.configFilePath,
    ]),
    // ports that the pod exposes
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      centralClientCert.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      requestInsightPublisher.volumeMountDef,
      {
        local sec = remoteWorkspaceSecrets.spec.template,
        name: sec.metadata.name,
        mountPath: '/run/secrets/' + sec.metadata.name,
      },
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // the liveness check is used to determine if the pod is alive
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '1Gi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      // the client certificate is mounted into the pod
      centralClientCert.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCert.podVolumeDef,
      // the client certificate is mounted into the pod
      clientCert.podVolumeDef,
      // feature flags secret
      dynamicFeatureFlags.podVolumeDef,
      // request insight publisher config
      requestInsightPublisher.podVolumeDef,
      // remote workspace kubeconfig
      {
        local sec = remoteWorkspaceSecrets.spec.template,
        name: sec.metadata.name,
        secret: {
          secretName: sec.metadata.name,
        },
      },
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            [remoteWorkspaceSecrets.metadata.name + '/content-hash']: remoteWorkspaceSecrets.content_hash,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    services,
    remoteWorkspaceSecrets,
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
    apiProxyTunnel.objects,
    lock.objects,
  ])

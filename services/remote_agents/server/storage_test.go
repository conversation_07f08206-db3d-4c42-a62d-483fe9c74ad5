package main

import (
	"context"
	"testing"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	"github.com/augmentcode/augment/base/go/secretstring"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Helper function to create a test AgentEntity
func createTestAgentEntity(oldWorkspaceStatus, newWorkspaceStatus remoteagentsproto.WorkspaceStatus, agentStatus remoteagentsproto.AgentStatus) *AgentEntity {
	return &AgentEntity{
		statusInStorage: &entitiesproto.AgentStatus{
			WorkspaceStatus: oldWorkspaceStatus,
			Status:          remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, // Old agent status doesn't matter for current validation
		},
		Status: &entitiesproto.AgentStatus{
			WorkspaceStatus: newWorkspaceStatus,
			Status:          agentStatus,
		},
		AgentID: "test-agent-id",
	}
}

// Helper function to create a test AgentEntity with specific old and new agent statuses
func createTestAgentEntityWithAgentStatus(oldWorkspaceStatus, newWorkspaceStatus remoteagentsproto.WorkspaceStatus, oldAgentStatus, newAgentStatus remoteagentsproto.AgentStatus) *AgentEntity {
	return &AgentEntity{
		statusInStorage: &entitiesproto.AgentStatus{
			WorkspaceStatus: oldWorkspaceStatus,
			Status:          oldAgentStatus,
		},
		Status: &entitiesproto.AgentStatus{
			WorkspaceStatus: newWorkspaceStatus,
			Status:          newAgentStatus,
		},
		AgentID: "test-agent-id",
	}
}

// TestValidateStateTransition tests the current behavior of ValidateStateTransition
func TestValidateStateTransition(t *testing.T) {
	tests := []struct {
		name        string
		oldWS       remoteagentsproto.WorkspaceStatus
		newWS       remoteagentsproto.WorkspaceStatus
		agentStatus remoteagentsproto.AgentStatus
		expectError bool
		errorMsg    string
	}{
		// Valid workspace transitions
		{"Valid: RUNNING to PAUSING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: PAUSING to PAUSED", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: PAUSED to RESUMING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: RESUMING to RUNNING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: UNSPECIFIED to RUNNING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},

		// Valid agent statuses
		{"Valid: IDLE agent", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, false, ""},
		{"Valid: FAILED agent", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED, false, ""},

		// Invalid workspace transitions
		{"Invalid: RUNNING to PAUSED", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, true, "invalid workspace status transition"},
		{"Invalid: PAUSED to RUNNING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, true, "invalid workspace status transition"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := createTestAgentEntity(tt.oldWS, tt.newWS, tt.agentStatus)
			err := entity.ValidateStateTransition()

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestValidateStateTransition_PendingDeletion tests the PENDING_DELETION agent status transitions
func TestValidateStateTransition_PendingDeletion(t *testing.T) {
	tests := []struct {
		name           string
		oldAgentStatus remoteagentsproto.AgentStatus
		newAgentStatus remoteagentsproto.AgentStatus
		oldWS          remoteagentsproto.WorkspaceStatus
		newWS          remoteagentsproto.WorkspaceStatus
		expectError    bool
		errorMsg       string
	}{
		// Valid: PENDING_DELETION can only transition to PENDING_DELETION
		{
			name:           "Valid: PENDING_DELETION to PENDING_DELETION",
			oldAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			newAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			oldWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			newWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:    false,
			errorMsg:       "",
		},
		// Invalid: PENDING_DELETION can't transition to any other status
		{
			name:           "Invalid: PENDING_DELETION to IDLE (with no workspace change)",
			oldAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			newAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			oldWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			newWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:    true,
			errorMsg:       "invalid agent status transition from AGENT_STATUS_PENDING_DELETION to AGENT_STATUS_IDLE",
		},
		// Test PENDING_DELETION transitions when workspace status changes
		{
			name:           "Invalid: PENDING_DELETION to IDLE (with workspace change)",
			oldAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			newAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			oldWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			newWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING,
			expectError:    true,
			errorMsg:       "invalid agent status transition from AGENT_STATUS_PENDING_DELETION to AGENT_STATUS_IDLE",
		},
		// Valid: Other statuses can transition to PENDING_DELETION
		{
			name:           "Valid: IDLE to PENDING_DELETION",
			oldAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			newAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			oldWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			newWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Valid: RUNNING to PENDING_DELETION",
			oldAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING,
			newAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			oldWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			newWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:    false,
			errorMsg:       "",
		},
		{
			name:           "Valid: FAILED to PENDING_DELETION",
			oldAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED,
			newAgentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			oldWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			newWS:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:    false,
			errorMsg:       "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := createTestAgentEntityWithAgentStatus(tt.oldWS, tt.newWS, tt.oldAgentStatus, tt.newAgentStatus)
			err := entity.ValidateStateTransition()

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got nil. Test case: %s", tt.name)
				} else {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v. Test case: %s", err, tt.name)
				}
			}
		})
	}
}

func TestWriteChatHistoryChunk(t *testing.T) {
	bigtableProxyClient := realBigtableProxyClient

	tenantID := "test-tenant"
	userId := "test-user"
	agentId := "test-agent"
	requestContext := requestcontext.New(
		requestcontext.RequestId(uuid.New().String()),
		requestcontext.RequestSessionId("test-session-id"),
		"test-source",
		secretstring.New("test-token"),
	)

	// Try writing chat history from empty state
	requestId := "request-id-1"
	responseText := "Hello"
	chatHistory := []*remoteagentsproto.ChatHistoryExchange{
		{
			SequenceId: 1,
			Exchange: &chatproto.Exchange{
				RequestMessage: "Hello",
				ResponseText:   &responseText,
				RequestId:      &requestId,
			},
			FinishedAt: timestamppb.Now(),
		},
	}
	err := writeChatHistoryChunk(
		context.Background(),
		requestContext,
		bigtableProxyClient,
		tenantID,
		userId,
		agentId,
		chatHistory,
	)
	assert.NoError(t, err)

	// Delete the timestamp cell, to test backward compatibility
	_, err = bigtableProxyClient.MutateRows(
		context.Background(),
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentChatHistoryRowKey(agentId, 1),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromColumn_{
							DeleteFromColumn: &bigtableproto.Mutation_DeleteFromColumn{
								FamilyName:      timestampFamilyName,
								ColumnQualifier: []byte(timestampColumn),
							},
						},
					},
				},
			},
		},
		requestContext,
	)
	assert.NoError(t, err)

	// Try writing chat history again with the same sequence ID
	requestId = "request-id-2"
	responseText = "Hello, world"
	chatHistory = []*remoteagentsproto.ChatHistoryExchange{
		{
			SequenceId: 1,
			Exchange: &chatproto.Exchange{
				RequestMessage: "Hello",
				ResponseText:   &responseText,
				RequestId:      &requestId,
			},
			FinishedAt: timestamppb.Now(),
		},
	}
	err = writeChatHistoryChunk(
		context.Background(),
		requestContext,
		bigtableProxyClient,
		tenantID,
		userId,
		agentId,
		chatHistory,
	)
	assert.NoError(t, err)

	// Try one more time, with existing data in the timestamp column
	requestId = "request-id-3"
	responseText = "Hello, world!!!"
	chatHistory = []*remoteagentsproto.ChatHistoryExchange{
		{
			SequenceId: 1,
			Exchange: &chatproto.Exchange{
				RequestMessage: "Hello",
				ResponseText:   &responseText,
				RequestId:      &requestId,
			},
			FinishedAt: timestamppb.Now(),
		},
	}
	err = writeChatHistoryChunk(
		context.Background(),
		requestContext,
		bigtableProxyClient,
		tenantID,
		userId,
		agentId,
		chatHistory,
	)
	assert.NoError(t, err)

	// Read the row back and verify the value
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	rows, err := bigtableProxyClient.ReadRows(
		context.Background(),
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{RowKeys: [][]byte{getRemoteAgentChatHistoryRowKey(agentId, 1)}},
		filter,
		0,
		requestContext,
	)
	assert.NoError(t, err)
	assert.Len(t, rows, 1)
	assert.Len(t, rows[0].Cells, 1)
	// unmarshal
	output := &entitiesproto.AgentOutput{}
	err = proto.Unmarshal(rows[0].Cells[0].Value, output)
	assert.NoError(t, err)
	assert.Len(t, output.Exchange, 1)
	assert.NotNil(t, output.Exchange[0].Exchange.ResponseText)
	assert.Equal(t, "Hello, world!!!", *output.Exchange[0].Exchange.ResponseText)
}

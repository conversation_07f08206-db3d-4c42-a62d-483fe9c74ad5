syntax = "proto3";

package shredder;

import "google/protobuf/timestamp.proto";

service Shredder {
  // Request deletion.
  rpc EnqueueUserDataDeletion(EnqueueUserDataDeletionRequest) returns (EnqueueUserDataDeletionResponse);

  // Get list of deletions, in reverse chronological order.
  rpc ListDeletions(ListDeletionsRequest) returns (ListDeletionsResponse);

  // Get information about a single deletion.
  rpc GetDeletion(GetDeletionRequest) returns (GetDeletionResponse);

  // Customer-facing RPC for getting the status of their latest deletion. Eventually we might want
  // to expose more to the customer, but for now the information we give is intentionally extremely
  // minimal.
  rpc GetLatestDeletionStatus(GetLatestDeletionStatusRequest) returns (GetLatestDeletionStatusResponse);
}

message EnqueueUserDataDeletionRequest {
  // The augment user id (uuid) of the user who requested data deletion.
  string user_id = 1;

  // The reason for the deletion. Expected to be something like "requested in Customer UI" for
  // user-initiated requests or a link to linear ticket for support requests.
  string reason = 2;

  // The type of deletion requested and any necessary additional information.
  oneof request {
    ContentDeletionRequest content_deletion = 3;
    AccountDeletionRequest account_deletion = 4;
    GdprCcpaDeletionRequest gdpr_ccpa_deletion = 5;
  }
}

// User requested to have their account deleted (but not under GDPR/CCPA).
message AccountDeletionRequest {}

// User requested to have their content removed from the system.
message ContentDeletionRequest {}

// User made a GDPR/CCPA request to be forgotten.
message GdprCcpaDeletionRequest {}

message EnqueueUserDataDeletionResponse {
  string deletion_id = 1;

  // Additional information returned for use by customer-ui.
  DeletionInfo.Status status = 2;
  google.protobuf.Timestamp created_at = 3;
}

message ListDeletionsRequest {
  // If non-positive, server chooses a reasonable default.
  uint32 page_size = 1;

  // From a previous ListDeletionsResponse.
  // TODO(jacqueline): Currently unused; implement pagination.
  string page_token = 2;
}

message ListDeletionsResponse {
  repeated DeletionInfo deletion_info = 1;
  string next_page_token = 2;
}

message GetDeletionRequest {
  string deletion_id = 1;
}

message GetDeletionResponse {
  DeletionInfo deletion_info = 1;

  // Information about each task. The assumption is that there isn't very many tasks. If that ever
  // changes we can split fetching tasks into a separate streaming RPC.
  repeated TaskInfo task_info = 2;
}

message GetLatestDeletionStatusRequest {
  string user_id = 1;
}

message GetLatestDeletionStatusResponse {
  string deletion_id = 1;
  DeletionInfo.Status status = 2;
  google.protobuf.Timestamp created_at = 3;
}

message DeletionInfo {
  enum Status {
    STATUS_UNKNOWN = 0;
    PENDING = 1;
    COMPLETED = 2;
    FAILED = 3;
  }

  // Unique id of the deletion.
  string deletion_id = 1;

  // Current status of the deletion. A deletion is only complete if all of its task are complete.
  Status status = 2;

  // The original request for this deletion.
  EnqueueUserDataDeletionRequest request = 3;

  // The email of the user at the time of deletion. This is saved for fraud detection purposes.
  string user_email = 4;

  // When the deletion was created within shredder.
  google.protobuf.Timestamp created_at = 5;

  // When the deletion was last updated.
  google.protobuf.Timestamp updated_at = 6;

  // When the deletion was completed.
  google.protobuf.Timestamp completed_at = 7;
}

// Information about a deletion subtask.
message TaskInfo {
  enum Status {
    STATUS_UNKNOWN = 0;
    // Blocked means other tasks need to complete first.
    BLOCKED = 1;
    PENDING = 2;
    COMPLETED = 3;
    FAILED = 4;
  }

  // Unique id for the task.
  string task_id = 1;

  // Current status of the task.
  Status status = 2;

  // Timestamp when the task first began processing.
  google.protobuf.Timestamp started_at = 3;

  // Timestamp when the task was last updated.
  google.protobuf.Timestamp updated_at = 4;

  // Timestamp when the task completed.
  google.protobuf.Timestamp completed_at = 5;

  // Details about the task.
  TaskDetails details = 6;
}

// Wrapper around task-specific details. We only user a wrapper here to make working with the
// generated Go code nicer because oneofs don't have an exported type.
// See https://github.com/golang/protobuf/issues/1326.
message TaskDetails {
  oneof task {
    AnalyticsForgetUserTask analytics_forget_user = 1;
    AuthCentralForgetUserTask auth_central_forget_user = 2;
    AuthCentralDeleteAccountTask auth_central_delete_account = 3;
    VanguardExportDeleteBlobsTask vanguard_export_delete_blobs = 4;
    VanguardExportDeleteRequestDataTask vanguard_export_delete_request_data = 5;
    ContentManagerDeleteBlobsTask content_manager_delete_blobs = 6;
    SettingsDeleteUserSettings settings_delete_user_settings = 7;
    RemoteAgentsDeleteDataTask remote_agents_delete_data = 8;
  }
}

// Delete PII from analytics.
message AnalyticsForgetUserTask {}

// Delete PII from auth central.
message AuthCentralForgetUserTask {}

// Delete account from auth central. After this task a user belongs to no tenants and is logged out.
message AuthCentralDeleteAccountTask {}

// Delete blobs from vanguard GCS bucket.
message VanguardExportDeleteBlobsTask {}

// Delete request events from vanguard GCS bucket.
message VanguardExportDeleteRequestDataTask {}

// Delete blobs from content manager.
message ContentManagerDeleteBlobsTask {}

// Delete user settings from settings service.
message SettingsDeleteUserSettings {}

// Delete user data from remote agents.
message RemoteAgentsDeleteDataTask {}

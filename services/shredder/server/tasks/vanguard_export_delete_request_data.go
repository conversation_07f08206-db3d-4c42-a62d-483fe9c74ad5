package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type VanguardExportDeleteRequestDataExecutor struct{}

func NewVanguardExportDeleteRequestDataExecutor() *VanguardExportDeleteRequestDataExecutor {
	return &VanguardExportDeleteRequestDataExecutor{}
}

func (e *VanguardExportDeleteRequestDataExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetVanguardExportDeleteRequestData() == nil {
		return fmt.Errorf("invalid task details")
	}

	return fmt.Errorf("not implemented")
}

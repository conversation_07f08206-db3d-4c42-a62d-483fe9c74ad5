package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type AnalyticsForgetUserExecutor struct{}

func NewAnalyticsForgetUserExecutor() *AnalyticsForgetUserExecutor {
	return &AnalyticsForgetUserExecutor{}
}

func (e *AnalyticsForgetUserExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetAnalyticsForgetUser() == nil {
		return fmt.Errorf("invalid task details")
	}

	return fmt.Errorf("not implemented")
}

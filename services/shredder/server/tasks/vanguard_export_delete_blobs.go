package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type VanguardExportDeleteBlobsExecutor struct{}

func NewVanguardExportDeleteBlobsExecutor() *VanguardExportDeleteBlobsExecutor {
	return &VanguardExportDeleteBlobsExecutor{}
}

func (e *VanguardExportDeleteBlobsExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetVanguardExportDeleteBlobs() == nil {
		return fmt.Errorf("invalid task details")
	}

	return fmt.Errorf("not implemented")
}

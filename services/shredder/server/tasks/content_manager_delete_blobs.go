package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type ContentManagerDeleteBlobsExecutor struct{}

func NewContentManagerDeleteBlobsExecutor() *ContentManagerDeleteBlobsExecutor {
	return &ContentManagerDeleteBlobsExecutor{}
}

func (e *ContentManagerDeleteBlobsExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetContentManagerDeleteBlobs() == nil {
		return fmt.Errorf("invalid task details")
	}

	return fmt.Errorf("not implemented")
}

load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "tasks_lib",
    srcs = [
        "analytics_forget_user.go",
        "auth_central_delete_account.go",
        "auth_central_forget_user.go",
        "content_manager_delete_blobs.go",
        "interface.go",
        "remote_agents_delete_data.go",
        "settings_delete_user_settings.go",
        "vanguard_export_delete_blobs.go",
        "vanguard_export_delete_request_data.go",
    ],
    importpath = "github.com/augmentcode/augment/services/shredder/server/tasks",
    visibility = ["//services/shredder/server:__subpackages__"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "//services/shredder:shredder_go_proto",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_rs_zerolog//log",
    ],
)

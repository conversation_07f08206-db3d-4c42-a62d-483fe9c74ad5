package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type RemoteAgentsDeleteDataExecutor struct{}

func NewRemoteAgentsDeleteDataExecutor() *RemoteAgentsDeleteDataExecutor {
	return &RemoteAgentsDeleteDataExecutor{}
}

func (e *RemoteAgentsDeleteDataExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetRemoteAgentsDeleteData() == nil {
		return fmt.Errorf("invalid task details")
	}

	return fmt.Errorf("not implemented")
}

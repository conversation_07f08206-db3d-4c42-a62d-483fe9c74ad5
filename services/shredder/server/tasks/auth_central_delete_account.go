package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type AuthCentralDeleteAccountExecutor struct{}

func NewAuthCentralDeleteAccountExecutor() *AuthCentralDeleteAccountExecutor {
	return &AuthCentralDeleteAccountExecutor{}
}

func (e *AuthCentralDeleteAccountExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetAuthCentralDeleteAccount() == nil {
		return fmt.Errorf("invalid task details")
	}
	return fmt.Errorf("not implemented")
}

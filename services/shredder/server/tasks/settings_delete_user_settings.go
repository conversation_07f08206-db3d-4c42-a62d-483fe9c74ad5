package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type SettingsDeleteUserSettingsExecutor struct{}

func NewSettingsDeleteUserSettingsExecutor() *SettingsDeleteUserSettingsExecutor {
	return &SettingsDeleteUserSettingsExecutor{}
}

func (e *SettingsDeleteUserSettingsExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetSettingsDeleteUserSettings() == nil {
		return fmt.Errorf("invalid task details")
	}

	return fmt.Errorf("not implemented")
}

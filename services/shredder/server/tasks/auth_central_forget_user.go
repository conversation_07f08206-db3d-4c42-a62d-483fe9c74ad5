package tasks

import (
	"context"
	"fmt"

	pb "github.com/augmentcode/augment/services/shredder/proto"
)

type AuthCentralForgetUserExecutor struct{}

func NewAuthCentralForgetUserExecutor() *AuthCentralForgetUserExecutor {
	return &AuthCentralForgetUserExecutor{}
}

func (e *AuthCentralForgetUserExecutor) Execute(
	ctx context.Context, userID string, userEmail string, details *pb.TaskDetails,
) error {
	if details.GetAuthCentralForgetUser() == nil {
		return fmt.Errorf("invalid task details")
	}

	return fmt.Errorf("not implemented")
}

package main

import (
	"context"
	"fmt"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	pb "github.com/augmentcode/augment/services/shredder/proto"
	"github.com/rs/zerolog/log"
)

// DeletionManager is responsible for the loop that watches the database for pending deletion
// requests, performs tasks, and updates the database with the results.
// DeletionManager is not thread safe. Intended use is to have a single instance running globally.
// The goal is to simplify the logic, under the assumption that it will be a long time until we have
// enough deletion requests that they can't all be handled by a single thread.
type DeletionManager struct {
	featureFlagHandle                       featureflags.FeatureFlagHandle
	auditLogger                             *audit.AuditLogger
	persistence                             *ShredderPersistence
	analyticsForgetUserExecutor             TaskExecutor
	authCentralForgetUserExecutor           TaskExecutor
	authCentralDeleteAccountExecutor        TaskExecutor
	vanguardExportDeleteBlobsExecutor       TaskExecutor
	vanguardExportDeleteRequestDataExecutor TaskExecutor
	contentManagerDeleteBlobsExecutor       TaskExecutor
	settingsDeleteUserSettingsExecutor      TaskExecutor
	remoteAgentsDeleteDataExecutor          TaskExecutor
}

type TaskExecutor interface {
	Execute(ctx context.Context, userID string, userEmail string, details *pb.TaskDetails) error
}

func NewDeletionManager(
	featureFlagHandle featureflags.FeatureFlagHandle,
	auditLogger *audit.AuditLogger,
	persistence *ShredderPersistence,
	analyticsForgetUserExecutor TaskExecutor,
	authCentralForgetUserExecutor TaskExecutor,
	authCentralDeleteAccountExecutor TaskExecutor,
	vanguardExportDeleteBlobsExecutor TaskExecutor,
	vanguardExportDeleteRequestDataExecutor TaskExecutor,
	contentManagerDeleteBlobsExecutor TaskExecutor,
	settingsDeleteUserSettingsExecutor TaskExecutor,
	remoteAgentsDeleteDataExecutor TaskExecutor,
) *DeletionManager {
	return &DeletionManager{
		featureFlagHandle:                       featureFlagHandle,
		auditLogger:                             auditLogger,
		persistence:                             persistence,
		analyticsForgetUserExecutor:             analyticsForgetUserExecutor,
		authCentralForgetUserExecutor:           authCentralForgetUserExecutor,
		authCentralDeleteAccountExecutor:        authCentralDeleteAccountExecutor,
		vanguardExportDeleteBlobsExecutor:       vanguardExportDeleteBlobsExecutor,
		vanguardExportDeleteRequestDataExecutor: vanguardExportDeleteRequestDataExecutor,
		contentManagerDeleteBlobsExecutor:       contentManagerDeleteBlobsExecutor,
		settingsDeleteUserSettingsExecutor:      settingsDeleteUserSettingsExecutor,
		remoteAgentsDeleteDataExecutor:          remoteAgentsDeleteDataExecutor,
	}
}

// Start the processing loop. Does not return unless there's an unrecoverable problem or stopChan is
// closed.
func (m *DeletionManager) Run(ctx context.Context, stopChan chan struct{}) error {
	// Sleep for 60 seconds at startup to make it less likely we have two managers running
	// concurrently during a deployment.
	sleepDuration := 60 * time.Second
	for {
		select {
		case <-stopChan:
			return nil
		case <-time.After(sleepDuration):
		}

		// Get pending deletions.
		pendingDeletionIDs, err := m.persistence.ListPendingDeletionIDs(ctx, 1000)
		if err != nil {
			// Wait a few seconds before retrying if we can't connect to the database.
			sleepDuration = 5 * time.Second
			log.Error().Err(err).Msgf("Failed to list pending deletions")
			continue
		} else if len(pendingDeletionIDs) == 0 {
			// Sleep 30 seconds before polling again if there's nothing to do.
			sleepDuration = 30 * time.Second
			continue
		}

		sleepDuration = 0 * time.Second
		for _, deletionID := range pendingDeletionIDs {
			// Deletions can take a while, so check for stop signal before processing each deletion.
			select {
			case <-stopChan:
				return nil
			default:
			}

			err := m.processDeletion(ctx, deletionID)
			if err != nil {
				log.Error().
					Str("deletion_id", deletionID).
					Err(err).Msgf("Failed to process deletion %s", deletionID)
			}
		}
	}
}

func (m *DeletionManager) processDeletion(
	ctx context.Context, deletionID string,
) error {
	// Before doing anything, make sure the deletion and task's statuses are in a consistent state.
	// They might not be if we crashed in the middle of processing previously.
	err := m.resolveDeletionAndTaskStatuses(ctx, deletionID)
	if err != nil {
		return fmt.Errorf("failed to resolve statuses for deletion %s: %w", deletionID, err)
	}

	deletion, tasks, err := m.persistence.GetDeletionAndTasks(ctx, deletionID)
	if err != nil {
		return fmt.Errorf("failed to get deletion: %w", err)
	} else if deletion.Status != pb.DeletionInfo_PENDING {
		log.Info().
			Str("deletion_id", deletionID).
			Msgf("Skipping deletion %s, status is %s", deletionID, deletion.Status)
		return nil
	}

	// Find the pending task.
	pendingTaskIdx := -1
	for i, task := range tasks {
		if task.Status == pb.TaskInfo_PENDING {
			pendingTaskIdx = i
			break
		}
	}
	if pendingTaskIdx == -1 {
		// This indicates a logic bug somewhere. It should be impossible to have a pending deletion
		// with no pending tasks.
		return fmt.Errorf("no pending tasks for deletion %s", deletionID)
	}

	// Execute tasks starting with the first pending task. Keep executing until we're done or
	// something doesn't succeed.
	for i := pendingTaskIdx; i < len(tasks); i++ {
		if tasks[i].Status != pb.TaskInfo_COMPLETED {
			status, err := m.executeTask(ctx, deletion, tasks[i])
			if err != nil {
				return fmt.Errorf("execution of task %s failed: %w", tasks[i].TaskId, err)
			}
			if status == pb.TaskInfo_FAILED {
				break
			}
		}
	}

	// Resolve statuses. Task execution updates the task's status, but doesn't update the deletion's
	// status or statuses of other tasks.
	err = m.resolveDeletionAndTaskStatuses(ctx, deletionID)
	if err != nil {
		return fmt.Errorf("failed to update deletion status: %w", err)
	}

	return nil
}

// Execute a task and update its status with the result. Returns the task's updated status. An error
// is returned if something went wrong with the task management, not if the task itself failed.
func (m *DeletionManager) executeTask(
	ctx context.Context, deletion *pb.DeletionInfo, task *pb.TaskInfo,
) (pb.TaskInfo_Status, error) {
	var err error
	userID := deletion.GetRequest().GetUserId()
	userEmail := deletion.GetUserEmail()
	details := task.GetDetails()

	// Mark the task as started in the database.
	now := time.Now()
	err = m.persistence.UpdateTask(
		ctx, deletion.DeletionId, task.TaskId, nil, &now, nil, nil,
	)
	if err != nil {
		return pb.TaskInfo_FAILED, fmt.Errorf("failed to update task: %w", err)
	}

	// Execute the task.
	switch task.GetDetails().Task.(type) {
	case *pb.TaskDetails_ContentManagerDeleteBlobs:
		err = m.contentManagerDeleteBlobsExecutor.Execute(ctx, userID, userEmail, details)
	case *pb.TaskDetails_RemoteAgentsDeleteData:
		err = m.remoteAgentsDeleteDataExecutor.Execute(ctx, userID, userEmail, details)
	case *pb.TaskDetails_SettingsDeleteUserSettings:
		err = m.settingsDeleteUserSettingsExecutor.Execute(ctx, userID, userEmail, details)
	case *pb.TaskDetails_AuthCentralDeleteAccount:
		err = m.authCentralDeleteAccountExecutor.Execute(ctx, userID, userEmail, details)
	case *pb.TaskDetails_AnalyticsForgetUser:
		err = m.analyticsForgetUserExecutor.Execute(ctx, userID, userEmail, details)
	case *pb.TaskDetails_AuthCentralForgetUser:
		err = m.authCentralForgetUserExecutor.Execute(ctx, userID, userEmail, details)
	case *pb.TaskDetails_VanguardExportDeleteBlobs:
		err = m.vanguardExportDeleteBlobsExecutor.Execute(ctx, userID, userEmail, details)
	case *pb.TaskDetails_VanguardExportDeleteRequestData:
		err = m.vanguardExportDeleteRequestDataExecutor.Execute(ctx, userID, userEmail, details)
	default:
		err = fmt.Errorf("unknown task type: %v", task.GetDetails())
	}

	if err != nil {
		log.Error().
			Str("deletion_id", deletion.DeletionId).
			Str("task_id", task.TaskId).
			Err(err).
			Msgf("Failed to execute task %s (%s)", task.TaskId, taskType(task))
	}

	// Update the task status in the database.
	var status pb.TaskInfo_Status
	var completedAt *time.Time
	now = time.Now()
	if err != nil {
		status = pb.TaskInfo_FAILED
	} else {
		status = pb.TaskInfo_COMPLETED
		completedAt = &now
	}
	err = m.persistence.UpdateTask(
		ctx, deletion.DeletionId, task.TaskId, &status, nil, &now, completedAt,
	)
	if err != nil {
		return status, fmt.Errorf("failed to update task: %w", err)
	}

	return status, nil
}

// Update task statuses from BLOCKED to PENDING as appropriate, and a deletion's status from
// PENDING to COMPLETED or FAILED based on its tasks' statuses.
func (m *DeletionManager) resolveDeletionAndTaskStatuses(
	ctx context.Context, deletionID string,
) error {
	deletion, tasks, err := m.persistence.GetDeletionAndTasks(ctx, deletionID)
	if err != nil {
		return fmt.Errorf("failed to get deletion: %w", err)
	}

	// Update the tasks.
	prevTaskStatus := pb.TaskInfo_COMPLETED
	for _, task := range tasks {
		if task.Status == pb.TaskInfo_BLOCKED && prevTaskStatus == pb.TaskInfo_COMPLETED {
			// Update the task to PENDING.
			status := pb.TaskInfo_PENDING
			now := time.Now()
			err := m.persistence.UpdateTask(ctx, deletion.DeletionId, task.TaskId, &status, nil, &now, nil)
			if err != nil {
				return fmt.Errorf("failed to update task: %w", err)
			}
			log.Info().
				Str("deletion_id", deletion.DeletionId).
				Str("task_id", task.TaskId).
				Msgf("Updated task %s to PENDING", task.TaskId)
			break
		}
		prevTaskStatus = task.Status
		if task.Status == pb.TaskInfo_FAILED {
			break
		}
	}

	if prevTaskStatus == pb.TaskInfo_FAILED {
		// Update the deletion to FAILED.
		now := time.Now()
		status := pb.DeletionInfo_FAILED
		err := m.persistence.UpdateDeletion(ctx, deletion.DeletionId, &status, &now, nil)
		if err != nil {
			return fmt.Errorf("failed to update deletion: %w", err)
		}
		log.Info().
			Str("deletion_id", deletion.DeletionId).
			Msgf("Updated deletion %s to FAILED", deletion.DeletionId)
	}

	// Check if all tasks are complete.
	allTasksComplete := true
	for _, task := range tasks {
		if task.Status != pb.TaskInfo_COMPLETED {
			allTasksComplete = false
			break
		}
	}
	if allTasksComplete {
		// Update the deletion to COMPLETED.
		now := time.Now()
		status := pb.DeletionInfo_COMPLETED
		err := m.persistence.UpdateDeletion(ctx, deletion.DeletionId, &status, &now, &now)
		if err != nil {
			return fmt.Errorf("failed to update deletion: %w", err)
		}
		log.Info().
			Str("deletion_id", deletion.DeletionId).
			Msgf("Updated deletion %s to COMPLETED", deletion.DeletionId)
	}

	return nil
}

package main

import (
	"context"
	"fmt"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	pb "github.com/augmentcode/augment/services/shredder/proto"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

type taskExecFunc func(ctx context.Context, userID string, userEmail string, details *pb.TaskDetails) error

var (
	successfulExec = func(ctx context.Context, userID string, userEmail string, details *pb.TaskDetails) error {
		return nil
	}
	failedExec = func(ctx context.Context, userID string, userEmail string, details *pb.TaskDetails) error {
		return fmt.Errorf("failed task")
	}
	executorsList = []string{
		"analyticsForget",
		"authCentralForget",
		"authCentralDelete",
		"vanguardDeleteBlobs",
		"vanguardDeleteData",
		"contentManager",
		"settings",
		"remoteAgents",
	}
)

type MockTaskExecutor struct {
	mock.Mock
	execFunc taskExecFunc
}

func (m *MockTaskExecutor) Execute(ctx context.Context, userID, userEmail string, details *pb.TaskDetails) error {
	_ = m.Called(ctx, userID, userEmail, details)
	return m.execFunc(ctx, userID, userEmail, details)
}

func testDeletionManager(t *testing.T, taskExecutors map[string]*MockTaskExecutor) (*DeletionManager, func()) {
	database, cleanup := NewShredderDatabase(context.Background())
	persistence := &ShredderPersistence{
		spannerClient: database,
	}

	return &DeletionManager{
		featureFlagHandle:                       featureflags.NewLocalFeatureFlagHandler(),
		auditLogger:                             audit.NewDefaultAuditLogger(),
		persistence:                             persistence,
		analyticsForgetUserExecutor:             taskExecutors["analyticsForget"],
		authCentralForgetUserExecutor:           taskExecutors["authCentralForget"],
		authCentralDeleteAccountExecutor:        taskExecutors["authCentralDelete"],
		vanguardExportDeleteBlobsExecutor:       taskExecutors["vanguardDeleteBlobs"],
		vanguardExportDeleteRequestDataExecutor: taskExecutors["vanguardDeleteData"],
		contentManagerDeleteBlobsExecutor:       taskExecutors["contentManager"],
		settingsDeleteUserSettingsExecutor:      taskExecutors["settings"],
		remoteAgentsDeleteDataExecutor:          taskExecutors["remoteAgents"],
	}, cleanup
}

func successExecutors() map[string]*MockTaskExecutor {
	executors := make(map[string]*MockTaskExecutor)
	for _, executor := range executorsList {
		mockExecutor := &MockTaskExecutor{execFunc: successfulExec}
		mockExecutor.On("Execute", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
		executors[executor] = mockExecutor
	}
	return executors
}

func failedExecutors() map[string]*MockTaskExecutor {
	executors := make(map[string]*MockTaskExecutor)
	for _, executor := range executorsList {
		mockExecutor := &MockTaskExecutor{execFunc: failedExec}
		mockExecutor.On("Execute", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
		executors[executor] = mockExecutor
	}
	return executors
}

func deletion() *pb.DeletionInfo {
	return &pb.DeletionInfo{
		DeletionId: "test-deletion",
		Status:     pb.DeletionInfo_PENDING,
		Request: &pb.EnqueueUserDataDeletionRequest{
			UserId: "test-user",
			Reason: "test-reason",
			Request: &pb.EnqueueUserDataDeletionRequest_AccountDeletion{
				AccountDeletion: &pb.AccountDeletionRequest{},
			},
		},
		UserEmail: "<EMAIL>",
	}
}

// Full list of possible tasks, all blocked.
func allTasks() []*pb.TaskInfo {
	return []*pb.TaskInfo{
		{
			TaskId:  "analytics-forget",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_AnalyticsForgetUser{}},
		},
		{
			TaskId:  "auth-central-forget",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralForgetUser{}},
		},
		{
			TaskId:  "auth-central-delete",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralDeleteAccount{}},
		},
		{
			TaskId:  "vanguard-delete-blobs",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteBlobs{}},
		},
		{
			TaskId:  "vanguard-delete-data",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_VanguardExportDeleteRequestData{}},
		},
		{
			TaskId:  "content-manager-delete-blobs",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_ContentManagerDeleteBlobs{}},
		},
		{
			TaskId:  "settings-delete-user-settings",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_SettingsDeleteUserSettings{}},
		},
		{
			TaskId:  "remote-agents-delete-data",
			Status:  pb.TaskInfo_BLOCKED,
			Details: &pb.TaskDetails{Task: &pb.TaskDetails_RemoteAgentsDeleteData{}},
		},
	}
}

func TestResolveDeletionAndTaskStatuses(t *testing.T) {
	t.Run("happy path", func(t *testing.T) {
		manager, cleanup := testDeletionManager(t, successExecutors())
		defer cleanup()

		// Create a pending deletion with two tasks that are BLOCKED.
		_, err := manager.persistence.CreateDeletion(context.Background(), &pb.DeletionInfo{
			DeletionId: "test-deletion",
			Status:     pb.DeletionInfo_PENDING,
			Request: &pb.EnqueueUserDataDeletionRequest{
				UserId: "test-user",
				Reason: "test-reason",
				Request: &pb.EnqueueUserDataDeletionRequest_AccountDeletion{
					AccountDeletion: &pb.AccountDeletionRequest{},
				},
			},
			UserEmail: "<EMAIL>",
		}, []*pb.TaskInfo{
			{
				TaskId: "test-task-1",
				Status: pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{
					Task: &pb.TaskDetails_ContentManagerDeleteBlobs{},
				},
			},
			{
				TaskId: "test-task-2",
				Status: pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{
					Task: &pb.TaskDetails_RemoteAgentsDeleteData{},
				},
			},
		})
		require.NoError(t, err)

		// After one resolution, the first task should be PENDING.
		err = manager.resolveDeletionAndTaskStatuses(context.Background(), "test-deletion")
		require.NoError(t, err)
		deletion, tasks, err := manager.persistence.GetDeletionAndTasks(
			context.Background(), "test-deletion")
		require.NoError(t, err)
		require.Equal(t, pb.DeletionInfo_PENDING, deletion.Status)
		require.Len(t, tasks, 2)
		require.Equal(t, pb.TaskInfo_PENDING, tasks[0].Status)
		require.Equal(t, pb.TaskInfo_BLOCKED, tasks[1].Status)

		// Mark the first task as completed.
		completedStatus := pb.TaskInfo_COMPLETED
		err = manager.persistence.UpdateTask(
			context.Background(), "test-deletion", "test-task-1", &completedStatus, nil, nil, nil)
		require.NoError(t, err)

		// After another resolution, the second task should be PENDING.
		err = manager.resolveDeletionAndTaskStatuses(context.Background(), "test-deletion")
		require.NoError(t, err)
		deletion, tasks, err = manager.persistence.GetDeletionAndTasks(context.Background(), "test-deletion")
		require.NoError(t, err)
		require.Equal(t, pb.DeletionInfo_PENDING, deletion.Status)
		require.Len(t, tasks, 2)
		require.Equal(t, pb.TaskInfo_COMPLETED, tasks[0].Status)
		require.Equal(t, pb.TaskInfo_PENDING, tasks[1].Status)

		// Mark the second task as completed.
		err = manager.persistence.UpdateTask(
			context.Background(), "test-deletion", "test-task-2", &completedStatus, nil, nil, nil)
		require.NoError(t, err)

		// After another resolution, the deletion should be completed.
		err = manager.resolveDeletionAndTaskStatuses(context.Background(), "test-deletion")
		require.NoError(t, err)
		deletion, tasks, err = manager.persistence.GetDeletionAndTasks(
			context.Background(), "test-deletion")
		require.NoError(t, err)
		require.Equal(t, pb.DeletionInfo_COMPLETED, deletion.Status)
		require.Len(t, tasks, 2)
		require.Equal(t, pb.TaskInfo_COMPLETED, tasks[0].Status)
		require.Equal(t, pb.TaskInfo_COMPLETED, tasks[1].Status)
	})

	t.Run("failed task", func(t *testing.T) {
		manager, cleanup := testDeletionManager(t, failedExecutors())
		defer cleanup()

		// Create a pending deletion with a failed task.
		_, err := manager.persistence.CreateDeletion(context.Background(), deletion(), []*pb.TaskInfo{
			{
				TaskId: "test-task-1",
				Status: pb.TaskInfo_FAILED,
				Details: &pb.TaskDetails{
					Task: &pb.TaskDetails_ContentManagerDeleteBlobs{},
				},
			},
			{
				TaskId: "test-task-2",
				Status: pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{
					Task: &pb.TaskDetails_RemoteAgentsDeleteData{},
				},
			},
		})
		require.NoError(t, err)

		// After resolution the deletion should be failed.
		err = manager.resolveDeletionAndTaskStatuses(context.Background(), "test-deletion")
		require.NoError(t, err)
		deletion, tasks, err := manager.persistence.GetDeletionAndTasks(
			context.Background(), "test-deletion")
		require.NoError(t, err)
		require.Equal(t, pb.DeletionInfo_FAILED, deletion.Status)
		require.Len(t, tasks, 2)
		require.Equal(t, pb.TaskInfo_FAILED, tasks[0].Status)
		require.Equal(t, pb.TaskInfo_BLOCKED, tasks[1].Status)
	})
}

func TestProcessDeletion(t *testing.T) {
	// Deletion that runs all executors and all succeed.
	t.Run("happy path, all executors", func(t *testing.T) {
		executors := successExecutors()
		manager, cleanup := testDeletionManager(t, executors)
		defer cleanup()

		// Create a pending deletion with two tasks that are BLOCKED.
		_, err := manager.persistence.CreateDeletion(context.Background(), deletion(), allTasks())
		require.NoError(t, err)

		// Process the deletion.
		err = manager.processDeletion(context.Background(), "test-deletion")
		require.NoError(t, err)

		// The deletion and all tasks should be completed.
		dbDeletion, dbTasks, err := manager.persistence.GetDeletionAndTasks(
			context.Background(), "test-deletion")
		require.NoError(t, err)
		require.Equal(t, pb.DeletionInfo_COMPLETED, dbDeletion.Status)
		require.NotNil(t, dbDeletion.UpdatedAt)
		require.NotNil(t, dbDeletion.CompletedAt)
		require.Len(t, dbTasks, len(allTasks()))
		for _, task := range dbTasks {
			require.Equal(t, pb.TaskInfo_COMPLETED, task.Status)
			require.NotNil(t, task.StartedAt)
			require.NotNil(t, task.UpdatedAt)
			require.NotNil(t, task.CompletedAt)
		}

		// All executors should have been called.
		for _, executor := range executors {
			executor.AssertNumberOfCalls(t, "Execute", 1)
		}
	})

	// Deletion where an executor in the middle fails.
	t.Run("executor fails", func(t *testing.T) {
		// Make the auth central forget user executor fail.
		executors := successExecutors()
		executors["authCentralForget"].execFunc = failedExec
		manager, cleanup := testDeletionManager(t, executors)
		defer cleanup()

		// Create a pending deletion with 3 tasks that are blocked.
		_, err := manager.persistence.CreateDeletion(context.Background(), deletion(), []*pb.TaskInfo{
			{
				TaskId:  "test-task-1",
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AnalyticsForgetUser{}},
			},
			{
				TaskId:  "test-task-2",
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralForgetUser{}},
			},
			{
				TaskId:  "test-task-3",
				Status:  pb.TaskInfo_BLOCKED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AuthCentralDeleteAccount{}},
			},
		})
		require.NoError(t, err)

		// Process the deletion.
		err = manager.processDeletion(context.Background(), "test-deletion")
		require.NoError(t, err)

		// The deletion and all tasks should be failed.
		dbDeletion, dbTasks, err := manager.persistence.GetDeletionAndTasks(
			context.Background(), "test-deletion")
		require.NoError(t, err)
		require.Equal(t, pb.DeletionInfo_FAILED, dbDeletion.Status)
		require.Len(t, dbTasks, 3)
		require.Equal(t, pb.TaskInfo_COMPLETED, dbTasks[0].Status)
		require.Equal(t, pb.TaskInfo_FAILED, dbTasks[1].Status)
		require.Equal(t, pb.TaskInfo_BLOCKED, dbTasks[2].Status)
	})

	t.Run("no pending tasks", func(t *testing.T) {
		executors := successExecutors()
		manager, cleanup := testDeletionManager(t, executors)
		defer cleanup()

		// Create a pending deletion with one failed task.
		_, err := manager.persistence.CreateDeletion(context.Background(), deletion(), []*pb.TaskInfo{
			{
				TaskId:  "test-task-1",
				Status:  pb.TaskInfo_FAILED,
				Details: &pb.TaskDetails{Task: &pb.TaskDetails_AnalyticsForgetUser{}},
			},
		})
		require.NoError(t, err)

		// Process the deletion.
		err = manager.processDeletion(context.Background(), "test-deletion")
		require.NoError(t, err)

		// The deletion should be failed now.
		dbDeletion, dbTasks, err := manager.persistence.GetDeletionAndTasks(
			context.Background(), "test-deletion")
		require.NoError(t, err)
		require.Equal(t, pb.DeletionInfo_FAILED, dbDeletion.Status)
		require.Len(t, dbTasks, 1)

		// The task should not have executed.
		require.Equal(t, pb.TaskInfo_FAILED, dbTasks[0].Status)
		executors["analyticsForget"].AssertNumberOfCalls(t, "Execute", 0)
	})
}

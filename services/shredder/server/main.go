package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpc_metrics "github.com/augmentcode/augment/services/lib/grpc/metrics"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	pb "github.com/augmentcode/augment/services/shredder/proto"
	"github.com/augmentcode/augment/services/shredder/server/tasks"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs" // Set GOMAXPROCS to container limits on startup
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var (
	configFile = flag.String("config", "", "Path to config file")

	disableGetFeature = featureflags.NewBoolFlag("example_disable_get_feature", false)
)

type Config struct {
	// the port the grpc server will listen on
	Port int

	// TLS configuration
	ServerMtls *tlsconfig.ServerConfig
	ClientMtls *tlsconfig.ClientConfig

	Namespace string

	TokenExchangeEndpoint string
	AuthCentralEndpoint   string

	// Prometheus metrics port
	PromPort int

	// the path to the feature flag sdk key
	FeatureFlagsSdkKeyPath string

	// the endpoint for the dynamic feature flags service or None if not used
	DynamicFeatureFlagsEndpoint string

	SpannerConfig *SpannerConfig
}

func main() {
	ctx := context.Background()
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	wg := sync.WaitGroup{}

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	featureFlagHandle, err := featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}
	_ = featureFlagHandle // this is just to suppress unused variable warning. Remove the code is not used

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	grpcMetricsInterceptor := grpc_metrics.NewMetricsInterceptor()
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
		grpcMetricsInterceptor.UnaryInterceptor,
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
		grpcMetricsInterceptor.StreamInterceptor,
	))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(clientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()
	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	authClient, err := authclient.New(config.AuthCentralEndpoint, grpc.WithTransportCredentials(clientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating auth client")
	}
	defer authClient.Close()

	persistence, err := NewShredderPersistence(ctx, config.SpannerConfig)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating persistence")
	}
	defer persistence.Close()

	// Task executors.
	analyticsForgetUserExecutor := tasks.NewAnalyticsForgetUserExecutor()
	authCentralForgetUserExecutor := tasks.NewAuthCentralForgetUserExecutor()
	authCentralDeleteAccountExecutor := tasks.NewAuthCentralDeleteAccountExecutor()
	vanguardExportDeleteBlobsExecutor := tasks.NewVanguardExportDeleteBlobsExecutor()
	vanguardExportDeleteRequestDataExecutor := tasks.NewVanguardExportDeleteRequestDataExecutor()
	contentManagerDeleteBlobsExecutor := tasks.NewContentManagerDeleteBlobsExecutor()
	settingsDeleteUserSettingsExecutor := tasks.NewSettingsDeleteUserSettingsExecutor()
	remoteAgentsDeleteDataExecutor := tasks.NewRemoteAgentsDeleteDataExecutor()

	// Set up the deletion manager in a background thread.
	deletionManager := NewDeletionManager(
		featureFlagHandle,
		audit.NewDefaultAuditLogger(),
		persistence,
		analyticsForgetUserExecutor,
		authCentralForgetUserExecutor,
		authCentralDeleteAccountExecutor,
		vanguardExportDeleteBlobsExecutor,
		vanguardExportDeleteRequestDataExecutor,
		contentManagerDeleteBlobsExecutor,
		settingsDeleteUserSettingsExecutor,
		remoteAgentsDeleteDataExecutor,
	)
	stopChan := make(chan struct{})
	go func() {
		wg.Add(1)
		defer wg.Done()
		err := deletionManager.Run(ctx, stopChan)
		if err != nil {
			log.Fatal().Err(err).Msg("Error running deletion manager")
		}
		log.Info().Msgf("DeletionManager closed gracefully")
	}()

	server := NewShredderServer(
		featureFlagHandle, audit.NewDefaultAuditLogger(), persistence, authClient,
	)

	pb.RegisterShredderServer(grpcServer, server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	close(stopChan)
	grpcServer.GracefulStop()
	wg.Wait()
	log.Info().Msg("Server stopped")
}

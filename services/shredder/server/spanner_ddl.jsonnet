// Schema DDL statements go here. This is an immutable list. ConfigConnector will apply just new
// statements. Changes are applied atomically and are all-or-nothing.
//
// Every DDL statement should have its own entry in the list and should NOT end with a semicolon or
// include SQL comments, or parsing will fail.
//
// See Spanner docs for best practices: https://cloud.google.com/spanner/docs/schema-and-data-model

[
  |||
    CREATE TABLE Deletion (
      DeletionID STRING(36) NOT NULL,
      Status STRING(MAX) NOT NULL,
      RequestType STRING(MAX) NOT NULL,
      UserID STRING(36) NOT NULL,
      UserEmail STRING(128) NOT NULL,
      Reason STRING(MAX) NOT NULL,
      CreatedAt TIMESTAMP NOT NULL,
      UpdatedAt TIMESTAMP,
      CompletedAt TIMESTAMP,
      RequestProto BYTES(MAX) NOT NULL,
    ) PRIMARY KEY (DeletionID)
  |||,

  |||
    CREATE TABLE Task (
      DeletionID STRING(36) NOT NULL,
      TaskID STRING(36) NOT NULL,
      SequenceID INT64 NOT NULL,
      Status STRING(MAX) NOT NULL,
      StartedAt TIMESTAMP,
      UpdatedAt TIMESTAMP,
      CompletedAt TIMESTAMP,
      TaskType STRING(MAX) NOT NULL,
      DetailsProto BYTES(MAX) NOT NULL,
    ) PRIMARY KEY (DeletionID, TaskID),
    INTERLEAVE IN PARENT Deletion ON DELETE CASCADE
  |||,

  // Index for looking up deletions by user/status.
  |||
    CREATE INDEX Deletion_UserID_Status ON Deletion (UserID, Status)
  |||,

  // Index for ordering deletions reverse chronologically.
  |||
    CREATE INDEX Deletion_CreatedAt ON Deletion (CreatedAt DESC)
  |||,

  // Index for looking up deletions by status, in chronological order.
  |||
    CREATE INDEX Deletion_Status_CreatedAt ON Deletion (Status, CreatedAt)
  |||,
]

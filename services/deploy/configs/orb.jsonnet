// Configuration for Orb billing plans, price IDs, and plan logic
function(env='PROD')
  // Common configuration for plan creation
  local planDefaults = {
    currency: 'USD',
    status: 'active',
    net_terms: 0,
    billing_cycle: {
      duration: 1,
      duration_unit: 'month',
    },
    cadence: 'monthly',
  };

  local allPlans = [
    {
      id: 'orb_community_plan',
      name: 'Community Plan',
      currency: planDefaults.currency,
      status: planDefaults.status,
      net_terms: planDefaults.net_terms,

      // UI configuration
      color: 'blue',
      icon: 'person',
      sort_order: 1,

      // Plan features
      features: {
        training_allowed: true,
        teams_allowed: false,
        max_seats: 1,
        add_credits_available: true,
        plan_type: 'community',
      },

      prices: [
        {
          name: 'Seats',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 1,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          item_id_ref: 'seats_item_id',
        },
        {
          name: 'User Message',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: 'usermessages',
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '1.00',
          },
          item_id_ref: 'user_message_item_id',
          billable_metric_id_ref: 'user_message_metric_id',
        },
        {
          name: 'Included Allocation (User Messages)',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 50,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          credit_allocation: {
            currency: 'usermessages',
            allows_rollover: false,
          },
          item_id_ref: 'included_messages_item_id',
        },
      ],
    },
    {
      id: 'orb_trial_plan',
      name: 'Trial Plan',
      currency: planDefaults.currency,
      status: planDefaults.status,
      net_terms: planDefaults.net_terms,

      // UI configuration
      color: 'gray',
      icon: 'clock',
      sort_order: 2,

      // Plan features
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 5,
        add_credits_available: false,
        plan_type: 'trial',
      },

      prices: [
        {
          name: 'Seats',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 5,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          item_id_ref: 'seats_item_id',
        },
        {
          name: 'User Message',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: 'usermessages',
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          item_id_ref: 'user_message_item_id',
          billable_metric_id_ref: 'user_message_metric_id',
        },
        {
          name: 'Included Allocation (User Messages)',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 1000,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          credit_allocation: {
            currency: 'usermessages',
            allows_rollover: false,
          },
          item_id_ref: 'included_messages_item_id',
        },
      ],
    },
    {
      id: 'orb_developer_plan',
      name: 'Developer Plan',
      currency: planDefaults.currency,
      status: planDefaults.status,
      net_terms: planDefaults.net_terms,

      // UI configuration
      color: 'indigo',
      icon: 'rocket',
      sort_order: 3,

      // Plan features
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 100,
        add_credits_available: true,
        plan_type: 'paid',
      },

      prices: [
        {
          name: 'Seats',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 1,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '30.00',
          },
          item_id_ref: 'seats_item_id',
        },
        {
          name: 'User Message',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: 'usermessages',
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.10',
          },
          item_id_ref: 'user_message_item_id',
          billable_metric_id_ref: 'user_message_metric_id',
        },
        {
          name: 'Included Allocation (User Messages)',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 600,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          credit_allocation: {
            currency: 'usermessages',
            allows_rollover: false,
          },
          item_id_ref: 'included_messages_item_id',
        },
      ],
    },
    {
      id: 'orb_pro_plan',
      name: 'Pro Plan',
      currency: planDefaults.currency,
      status: planDefaults.status,
      net_terms: planDefaults.net_terms,

      // UI configuration
      color: 'purple',
      icon: 'rocket',
      sort_order: 4,

      // Plan features
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 100,
        add_credits_available: true,
        plan_type: 'paid',
      },

      prices: [
        {
          name: 'Seats',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 1,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '50.00',
          },
          item_id_ref: 'seats_item_id',
        },
        {
          name: 'User Message',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: 'usermessages',
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.08',
          },
          item_id_ref: 'user_message_item_id',
          billable_metric_id_ref: 'user_message_metric_id',
        },
        {
          name: 'Included Allocation (User Messages)',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 1000,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          credit_allocation: {
            currency: 'usermessages',
            allows_rollover: false,
          },
          item_id_ref: 'included_messages_item_id',
        },
      ],
    },
    {
      id: 'orb_max_plan',
      name: 'Max Plan',
      currency: planDefaults.currency,
      status: planDefaults.status,
      net_terms: planDefaults.net_terms,

      // UI configuration
      color: 'gold',
      icon: 'lightning',
      sort_order: 5,

      // Plan features
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 100,
        add_credits_available: true,
        plan_type: 'paid',
      },

      prices: [
        {
          name: 'Seats',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 1,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '100.00',
          },
          item_id_ref: 'seats_item_id',
        },
        {
          name: 'User Message',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: 'usermessages',
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.05',
          },
          item_id_ref: 'user_message_item_id',
          billable_metric_id_ref: 'user_message_metric_id',
        },
        {
          name: 'Included Allocation (User Messages)',
          model_type: 'unit',
          cadence: planDefaults.cadence,
          currency: planDefaults.currency,
          fixed_price_quantity: 2000,
          billing_cycle_configuration: planDefaults.billing_cycle,
          unit_config: {
            unit_amount: '0.00',
          },
          credit_allocation: {
            currency: 'usermessages',
            allows_rollover: false,
          },
          item_id_ref: 'included_messages_item_id',
        },
      ],
    },
  ];

  // Filter plans based on environment
  local filteredPlans =
    if env == 'DEV' || env == 'STAGING' || env == 'PROD' then
      // Include all plans for dev, staging, and prod (including pro and max)
      allPlans
    else
      // Only include community, trial, and developer plans
      [plan for plan in allPlans if plan.id == 'orb_community_plan' || plan.id == 'orb_trial_plan' || plan.id == 'orb_developer_plan'];

  {
    community_plan_id: 'orb_community_plan',
    trial_plan_id: 'orb_trial_plan',
    professional_plan_id: 'orb_developer_plan',
    pro_plan_id: 'orb_pro_plan',
    max_plan_id: 'orb_max_plan',
    // Item IDs stay constant across plans and as plans change
    seats_item_id: {
      DEV: 'fPTPFSjvP7fjF3PZ',
      STAGING: 'fPTPFSjvP7fjF3PZ',
      PROD: 'i6nDyZKNgFu4d4rd',
    },
    included_messages_item_id: {
      DEV: 'kcwmTLYJpnTb33Nq',
      STAGING: 'kcwmTLYJpnTb33Nq',
      PROD: 'DunFsa2DZzHpVq45',
    },
    user_message_item_id: {
      DEV: 'i8fNmqjVYyXmpht8',
      STAGING: 'i8fNmqjVYyXmpht8',
      PROD: '',  // TODO(siyao): Update with actual prod item ID
    },

    // Billable metric IDs for usage tracking
    user_message_metric_id: {
      DEV: 'HpZt6dxLggckWTCn',
      STAGING: 'HpZt6dxLggckWTCn',
      PROD: '',  // TODO(siyao): Update with actual prod metric ID
    },

    pricing_unit: 'usermessages',
    cost_per_message: 0.1,  // Cost in USD per message sent. Can be moved to be plan-specific if necessary.

    // The version of the developer plan to use for existing Stripe users migrated to Orb.
    developer_plan_for_stripe_users: {
      DEV: {
        version_number: 8,
        seats_price_id: 'dBG5XVEops2rwFzR',
        included_messages_price_id: 'QwzWagYAVGL2ZjfH',
        messages_per_seat: 600.0,
      },
      STAGING: {
        version_number: 8,
        seats_price_id: 'dBG5XVEops2rwFzR',
        included_messages_price_id: 'QwzWagYAVGL2ZjfH',
        messages_per_seat: 600.0,
      },
      PROD: {
        version_number: 4,
        seats_price_id: 'bE9vSbszbyc4pB78',
        included_messages_price_id: 'Xvtjg8F6DmpuXk9j',
        messages_per_seat: 600.0,
      },
    },
    // The price of the original professional plan in Stripe, used only for existing Stripe users migrated to Orb.
    stripe_professional_plan_price_per_seat: 30.0,

    // Configs related to the logic of plans
    // ID refers to the plan's external_plan_id in Orb
    plans: filteredPlans,
    // These max purchase amounts are in USD
    min_addon_purchase: 10.0,
    max_addon_purchase: 100.0,
  }

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details

// To capture the raw JSON output (useful for e.g. diffing before and after a change):
// bazel build //services/deploy:metadata_json && cat $(bazel info bazel-bin)/services/deploy/METADATA.json

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
local tombstones = import 'services/deploy/tombstones.jsonnet';

// Deploy attempt central pods first.
// This isn't a guarantee, but it's a good heuristic and might reduce false negative
// alerts when deploying a new model. Code should still work if this is not the case.

// The use of several independent priority groups is a short-term hack to help avoid
// H100 GPU utilization pressure from too many deploys independently surging at once.
// Each priority group runs all deploys within the group in parallel, but the various
// groups run sequentially. The choice of group doesn't impact correctness; if adding
// a deploy you can choose randomly, or check the grafana hardware dashboard and/or
// devtools deploy logs to estimate which group is the least utilized.
local CENTRAL_PRIORITY_GROUP_4 = 1;
local CENTRAL_PRIORITY_GROUP_3 = 2;
local CENTRAL_PRIORITY_GROUP_2 = 3;
local CENTRAL_PRIORITY_GROUP_1 = 4;

// all non-GSC tasks for deploying H100s in central namespaces
// For now, EU staging is a special case where we don't want to deploy anything that uses H100s.
// NOTE: do not use centralNamespaces for models where inference occurs in US only! Use leadClusterOnlyCentralNamespaces instead.
local centralNamespaces = [t for t in cloudInfo.centralNamespaces if t.cloud != 'GCP_US_CENTRAL1_GSC_PROD' && (t.cloud != 'GCP_EU_WEST4_PROD' || t.env != 'STAGING')];
local gscCentralNamespaces = [t for t in cloudInfo.centralNamespaces if t.cloud == 'GCP_US_CENTRAL1_GSC_PROD'];

// central tasks used by dogfood namespaces
local dogfoodCentralNamespaces = [t for t in centralNamespaces if std.member(std.map(function(c) [c.cloud, c.env], tenantNamespaces.dogfoodNamespaces), [t.cloud, t.env])];
assert std.length(dogfoodCentralNamespaces) == 1;  // US central staging only

// For central services that we only want to deploy to the lead cluster (e.g., chat inference).
local leadClusterOnlyCentralNamespaces = [t for t in centralNamespaces if cloudInfo.isLeadCluster(t.cloud)];
// For services where we don't support redirect from EU to US, but also haven't set up an EU deploy yet.
// This should be a rare and transient case.
local leadClusterOnlyTenantNamespaces = [t for t in tenantNamespaces.namespaces if cloudInfo.isLeadCluster(t.cloud)];

// Helper function: specify a model host and inference host for a completion model
local deploymentsForCompletion(name, target, namespaces, health, inferenceOnly=false) =
  local inferenceNamespaces = [t for t in centralNamespaces if std.member(std.map(function(c) [c.cloud, c.env], namespaces), [t.cloud, t.env])];
  assert std.length(gscCentralNamespaces) == 2 : 'Expected exactly two GSC central namespaces, found %d' % std.length(gscCentralNamespaces);
  local gscNamespacesByEnv = {
    STAGING: [t for t in gscCentralNamespaces if t.env == 'STAGING'][0],
    PROD: [t for t in gscCentralNamespaces if t.env == 'PROD'][0],
  };
  // For production central namespaces, add the corresponding GSC namespace.
  // That way, we only add a deployment target for central/central-staging in GSC
  // if the corresponding production central/central-staging namespace is also deployed.
  local inferenceNamespacesPlusGSC = inferenceNamespaces + [gscNamespacesByEnv[t.env] for t in inferenceNamespaces if t.cloud == 'GCP_US_CENTRAL1_PROD'];
  lib.flatten([
    if !inferenceOnly then {
      name: 'completion-%s' % name,
      kubecfg: {
        target: target,
        task: namespaces,
        extra_config_args: [{
          name: 'filter',
          value: 'completion',
        }],
      },
      health: health,
    },
    {
      name: 'infer-%s' % name,
      deployment_schedule_name: 'INFER_AND_EMBEDDER',
      kubecfg: {
        target: target,
        task: inferenceNamespacesPlusGSC,
        extra_config_args: [{
          name: 'filter',
          value: 'inference',
        }],
      },
      // this is designed to target a random priority 1-4 from CENTRAL_PRIORITY_GROUP_* above
      priority: 1 + (std.parseHex(std.substr(std.md5(name), 0, 8)) % 4),
      health: health,
    },
  ]);

// Completion service deployments
local completionDeployments = lib.flatten([
  deploymentsForCompletion(
    name='eldenv4-0c-15b',
    target='//services/deploy/completion:eldenv4_0c_15b_kubecfg',
    namespaces=tenantNamespaces.dogfoodNamespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['xuanyi', 'markus'],
        slack_channel: '#system-services',
      },
    }
  ),
  deploymentsForCompletion(
    name='qweldenv1-1-14b',
    target='//services/deploy/completion:qweldenv1_1_14b_kubecfg',
    namespaces=tenantNamespaces.dogfoodNamespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['jeff', 'xuanyi', 'markus'],
        slack_channel: '#system-services',
      },
    }
  ),
  deploymentsForCompletion(
    name='qweldenv3-2-14b',
    target='//services/deploy/completion:qweldenv3_2_14b_kubecfg',
    namespaces=tenantNamespaces.namespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['jeff', 'markus', 'pranay'],
        slack_channel: '#system-services',
      },
    }
  ),
]);

local deploymentsForNextEdit(name, target, namespaces, health, inferenceOnly=false) =
  local inferenceNamespaces = [t for t in centralNamespaces if std.member(std.map(function(c) [c.cloud, c.env], namespaces), [t.cloud, t.env])];
  assert std.length(gscCentralNamespaces) == 2 : 'Expected exactly two GSC central namespaces, found %d' % std.length(gscCentralNamespaces);
  local gscNamespacesByEnv = {
    STAGING: [t for t in gscCentralNamespaces if t.env == 'STAGING'][0],
    PROD: [t for t in gscCentralNamespaces if t.env == 'PROD'][0],
  };
  // For production central namespaces, add the corresponding GSC namespace.
  // That way, we only add a deployment target for central/central-staging in GSC
  // if the corresponding production central/central-staging namespace is also deployed.
  local inferenceNamespacesPlusGSC = inferenceNamespaces + [gscNamespacesByEnv[t.env] for t in inferenceNamespaces if t.cloud == 'GCP_US_CENTRAL1_PROD'];
  lib.flatten([
    if !inferenceOnly then {
      name: 'next-edit-%s' % name,
      kubecfg: {
        target: target,
        task: namespaces,
        extra_config_args: [{
          name: 'filter',
          value: 'next_edit',
        }],
      },
      health: health,
    },

    {
      name: 'infer-%s' % name,
      deployment_schedule_name: 'INFER_AND_EMBEDDER',
      kubecfg: {
        target: target,
        task: inferenceNamespacesPlusGSC,
        extra_config_args: [{
          name: 'filter',
          value: 'inference',
        }],
      },
      // this is designed to target a random priority 1-4 from CENTRAL_PRIORITY_GROUP_* above
      priority: 1 + (std.parseHex(std.substr(std.md5(name), 0, 8)) % 4),
      health: health,
    },
  ]);

local nextEditDeployments = lib.flatten([
  deploymentsForNextEdit(
    name='raven-edit-v6-15b',
    target='//services/deploy/next_edit:raven_edit_v6_15b_kubecfg',
    namespaces=leadClusterOnlyTenantNamespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['vzhao', 'jiayi', 'moogi'],
        slack_channel: '#team-next-edit',
      },
    }
  ),
]);

local deploymentsForEmbedding(name, target, namespaces, health, inferenceOnly=false) =
  local embedderNamespaces = [t for t in centralNamespaces if std.member(std.map(function(c) [c.cloud, c.env], namespaces), [t.cloud, t.env])];
  assert std.length(gscCentralNamespaces) == 2 : 'Expected exactly two GSC central namespaces, found %d' % std.length(gscCentralNamespaces);
  local gscNamespacesByEnv = {
    STAGING: [t for t in gscCentralNamespaces if t.env == 'STAGING'][0],
    PROD: [t for t in gscCentralNamespaces if t.env == 'PROD'][0],
  };
  // For production central namespaces, add the corresponding GSC namespace.
  // That way, we only add a deployment target for central/central-staging in GSC
  // if the corresponding production central/central-staging namespace is also deployed.
  local embedderNamespacesPlusGSC = embedderNamespaces + [gscNamespacesByEnv[t.env] for t in embedderNamespaces if t.cloud == 'GCP_US_CENTRAL1_PROD'];
  lib.flatten([
    if !inferenceOnly then {
      name: '%s-indexer' % name,
      kubecfg: {
        target: target,
        task: namespaces,
        extra_config_args: [{
          name: 'filter',
          value: 'indexer',
        }],
      },
      health: health,
    },

    {
      name: '%s-embedder' % name,
      deployment_schedule_name: 'INFER_AND_EMBEDDER',
      kubecfg: {
        target: target,
        task: embedderNamespacesPlusGSC,
        extra_config_args: [{
          name: 'filter',
          value: 'embedder',
        }],
      },
      // this is designed to target a random priority 1-4 from CENTRAL_PRIORITY_GROUP_* above
      priority: 1 + (std.parseHex(std.substr(std.md5(name), 0, 8)) % 4),
      health: health,
    },
  ]);

// Embedding model deployments (embedder + indexer)
local embeddingDeployments = lib.flatten([
  // Completion models
  deploymentsForEmbedding(
    name='starethanol-smart',
    target='//services/deploy:starethanol_smart_kubecfg',
    namespaces=tenantNamespaces.namespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['dirk'],  // (Dirk) I am not the expert. If you are reading this, please find the right person.
        slack_channel: '#system-services',
      },
    }
  ),
  deploymentsForEmbedding(
    name='methanol-0416-4',
    target='//services/deploy:methanol_0416_4_kubecfg',
    namespaces=tenantNamespaces.namespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['vincent'],
        slack_channel: '#system-services',
      },
    }
  ),
  deploymentsForEmbedding(
    name='chatanol-qwen-v1-1',
    target='//services/deploy:chatanol-qwen-v1-1_kubecfg',
    namespaces=tenantNamespaces.namespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['tongfei'],
        slack_channel: '#system-services',
      },
    }
  ),
  deploymentsForEmbedding(
    name='commit-indexer',
    target='//services/deploy:commit_indexer_kubecfg',
    // NOTE(arun): We are sharing the embedder from chatanol-qwen-v1-1_kubecfg',
    namespaces=tenantNamespaces.dogfoodNamespaces,
    health={
      tier: 'TIER_2',
      experts: {
        users: ['arunchaganty'],
        slack_channel: '#team-past-edit',
      },
    },
  ),
  // Next edit models
  deploymentsForEmbedding(
    name='raven-retriever-v1',
    target='//services/deploy:raven_retriever_v1_kubecfg',
    namespaces=leadClusterOnlyTenantNamespaces,
    health={
      tier: 'TIER_1_A',
      experts: {
        users: ['moogi', 'des'],
        slack_channel: '#team-next-edit',
      },
    }
  ),
  deploymentsForEmbedding(
    name='raven-location-v2',
    target='//services/deploy:raven_location_v2_kubecfg',
    namespaces=leadClusterOnlyTenantNamespaces,
    health={
      tier: 'TIER_2',
      experts: {
        users: ['moogi', 'des'],
        slack_channel: '#team-next-edit',
      },
    }
  ),
]);

// Deployments of chat, edit services as well as the inference and embedder services.
//
// Please note that env STAGING is not the same as "dogfood".
// In particular, pre-prod tenants should be setup as close to prod as possible.
local otherDeployments = [
  {
    name: 'service-deploy-shared',
    kubecfg: {
      target: '//services/deploy:kubecfg_shared',
      task: [
        {
          cloud: 'ALL',
        },
      ],
    },
  },

  // Instruction hosts (new variant of edit host)

  {
    name: 'edit-claude-instruction-v2',
    kubecfg: {
      target: '//services/deploy:claude_instruction_v2_edit_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran'],
        slack_channel: '#team-chat',
      },
    },
  },

  {
    name: 'edit-claude-instruction-v3',
    kubecfg: {
      target: '//services/deploy:claude_instruction_v3_edit_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran'],
        slack_channel: '#team-chat',
      },
    },
  },

  {
    name: 'edit-claude-instruction-v4',
    kubecfg: {
      target: '//services/deploy:claude_instruction_v4_edit_kubecfg',
      task: tenantNamespaces.dogfoodNamespaces,
    },
    deployment_schedule_name: 'EXPERIMENTAL',
    health: {
      tier: 'TIER_2',
      experts: {
        users: ['ran'],
        slack_channel: '#team-chat',
      },
    },
  },

  {
    name: 'edit-claude-instruction-v2-direct',
    kubecfg: {
      target: '//services/deploy:claude_instruction_v2_direct_edit_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran'],
        slack_channel: '#team-chat',
      },
    },
  },

  // Smart Paste hosts
  {
    name: 'infer-forger-v2-qwen-14b-q-32k',
    deployment_schedule_name: 'INFER_AND_EMBEDDER',
    kubecfg: {
      target: '//services/deploy:forger_v2_qwen_14b_q_32k_edit_kubecfg',
      task: leadClusterOnlyCentralNamespaces + gscCentralNamespaces,
      extra_config_args: [{
        name: 'filter',
        value: 'inference',
      }],
    },
    priority: CENTRAL_PRIORITY_GROUP_4,
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran', 'vpas'],
        slack_channel: '#system-services',
      },
    },
  },

  {
    name: 'edit-forger-v2-qwen-14b-q-32k',
    kubecfg: {
      target: '//services/deploy:forger_v2_qwen_14b_q_32k_edit_kubecfg',
      task: tenantNamespaces.namespaces,
      extra_config_args: [{
        name: 'filter',
        value: 'edit',
      }],
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran', 'vpas'],
        slack_channel: '#team-chat',
      },
    },
  },

  // Chat hosts and their inference hosts

  {
    name: 'infer-pleasehold-14b-v2',
    deployment_schedule_name: 'INFER_AND_EMBEDDER',
    kubecfg: {
      target: '//services/deploy:pleasehold_14b_v2_kubecfg',
      task: leadClusterOnlyCentralNamespaces + gscCentralNamespaces,
      extra_config_args: [{
        name: 'filter',
        value: 'inference',
      }],
    },
    priority: CENTRAL_PRIORITY_GROUP_1,
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran'],
        slack_channel: '#team-chat',
      },
    },
  },

  {
    name: 'infer-sentry-v1',
    deployment_schedule_name: 'INFER_AND_EMBEDDER',
    kubecfg: {
      target: '//services/deploy:sentry_v1_kubecfg',
      task: leadClusterOnlyCentralNamespaces + gscCentralNamespaces,
      extra_config_args: [{
        name: 'filter',
        value: 'inference',
      }],
    },
    priority: CENTRAL_PRIORITY_GROUP_3,
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran', 'zhuoran'],
        slack_channel: '#team-chat',
      },
    },
  },

  {
    name: 'chat-chatanol-qwen-v1-1-third-party',
    kubecfg: {
      target: '//services/deploy:chatanol-qwen-v1-1_third_party_chat_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran', 'zhuoran'],
        slack_channel: '#system-services',
      },
    },
  },

  {
    name: 'chat-chatanol-q1-1-nor-third-party',
    kubecfg: {
      target: '//services/deploy:chatanol_q1_1_nor_third_party_chat_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran'],
        slack_channel: '#system-services',
      },
    },
  },

  {
    name: 'chat-chatanol-q1-1-r2-third-party',
    kubecfg: {
      target: '//services/deploy:chatanol_q1_1_r2_third_party_chat_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran'],
        slack_channel: '#system-services',
      },
    },
  },

  {
    name: 'chat-third-party-chatanol4-pleasehold2',
    kubecfg: {
      target: '//services/deploy:third_party_chatanol4_pleasehold2_chat_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran', 'zhuoran'],
        slack_channel: '#team-chat',
      },
    },
  },

  {
    name: 'chat-third-party-chatanol4',
    kubecfg: {
      target: '//services/deploy:third_party_chatanol4_chat_kubecfg',
      task: tenantNamespaces.dogfoodNamespaces + tenantNamespaces.aiTutorNamespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['ran', 'zhuoran'],
        slack_channel: '#system-services',
      },
    },
  },

  {
    name: 'tenant-config-prod',
    bazel: {
      target: '//services/deploy:tenant_config',
      arguments: ['--cloud=GCP_US_CENTRAL1_PROD', '--env=PROD', '--namespace=central'],
    },
    // deploy tenant changes directly after merging to main
    deployment_schedule_name: 'QUICK_DEPLOY',
    health: {
      tier: 'TIER_1_B',
      experts: {
        users: ['dirk', 'costa'],
        slack_channel: '#system-services',
      },
    },
  },

  {
    name: 'tenant-config-staging',
    bazel: {
      target: '//services/deploy:tenant_config',
      arguments: ['--cloud=GCP_US_CENTRAL1_PROD', '--env=STAGING', '--namespace=central-staging'],
    },
    // deploy tenant changes directly after merging to main
    deployment_schedule_name: 'QUICK_DEPLOY',
    health: {
      tier: 'TIER_1_B',
      experts: {
        users: ['dirk', 'costa'],
        slack_channel: '#system-services',
      },
    },
  },

  {
    name: 'shard-namespace-base',
    kubecfg: {
      target: '//services/deploy:shard_namespace_base_kubecfg',
      task: tenantNamespaces.namespaces,
    },
    health: {
      tier: 'TIER_1_A',
      experts: {
        users: ['dirk'],
        slack_channel: '#system-services',
      },
    },
  },
];

{
  // Ordering of these objects doesn't matter for actual deploys, but it's convenient for diffing
  // the state of the deploy config before and after as described at the top of the file.
  deployment: std.sort(completionDeployments + nextEditDeployments + embeddingDeployments + otherDeployments + tombstones, function(x) x.name),
}

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';

{
  get_oauth_v2_hostname:: function(env, namespace, cloud)
    {
      PROD: 'oauth2.augmentcode.com',
      STAGING: 'oauth2-staging.augmentcode.com',
      DEV: 'oauth2.%(tenant)s.%(internal_suffix)s' % { tenant: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
    }[env],
  get_auth_hostname:: function(env, namespace, cloud)
    {
      PROD: 'auth.augmentcode.com',
      STAGING: 'auth-staging.augmentcode.com',
      DEV: 'auth-central.%(tenant)s.%(internal_suffix)s' % { tenant: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
    }[env],
  get_api_proxy_hostname:: function(env, namespace, cloud)
    '%(tenant)s.%(apiDomain)s' % { tenant: namespace, apiDomain: cloudInfo[cloud].apiDomain },
  get_support_hostname:: function(env, namespace, cloud)
    local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
    'support.%s.t.%s' % [namespace, domainSuffix],
  getCustomerUiHostname:: function(env, namespace, cloud)
    {
      PROD: 'app.augmentcode.com',
      STAGING: 'app.staging.augmentcode.com',
      DEV: 'app.%(namespace)s.%(internal_suffix)s' % { namespace: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
    }[env],
  // Used to return the hostname for the new support UI currently in development
  // TODO(liam): remove the 'new-' prefix when the new support UI is released
  getSupportUiV2Hostname:: function(env, namespace, cloud)
    {
      PROD: 'support-v2.augmentcode.com',
      STAGING: 'support-v2.staging.augmentcode.com',
      DEV: 'support-v2.%(namespace)s.%(internal_suffix)s' % { namespace: namespace, internal_suffix: cloudInfo[cloud].internalDomainSuffix },
    }[env],
  // return the grpc url for auth-central
  //
  // auth-central GRPC address
  // use the central namespace. Use cluster internal routing if in lead cluster, use global routing if not
  getAuthCentralGrpcUrl:: function(env, namespace, cloud, headless=false)
    if env == 'DEV' then
      // don't bother with the global load-balancer in DEV
      'auth-central-grpc-svc'
    else
      local centralNamespace = std.filter(function(cn) cn.env == env && cn.cloud == cloud, cloudInfo.centralNamespaces)[0].namespace;
      local globalCloud = 'GCP_US_CENTRAL1_PROD';
      grpcLib.globalGrpcServiceHostname(cloud=globalCloud, serviceName='auth-rpc', namespace=centralNamespace, headless=headless),
  // tenant-watcher GRPC address
  // Use the central namespace in the lead cluster. Use cluster internal routing
  // if in lead cluster, use global routing if not
  getTenantWatcherGrpcUrl:: function(env, namespace, cloud)
    if env == 'DEV' then
      // don't bother with the global load-balancer in DEV
      'tenant-central-svc:50051'
    else
      local centralNamespace = std.filter(function(cn) cn.env == env && cn.cloud == cloud, cloudInfo.centralNamespaces)[0].namespace;
      local globalCloud = 'GCP_US_CENTRAL1_PROD';
      '%s:50051' % grpcLib.globalGrpcServiceHostname(cloud=globalCloud, serviceName='tenant-central', namespace=centralNamespace),
  // return the grpc url for token-exchange-central-svc
  //
  // token-exchange-central-svc GRPC address
  // this does not include a http or https prefix
  getTokenExchangeGrpcUrl:: function(env, namespace, cloud)
    local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud);
    if namespace == centralNamespace then
      'token-exchange-central-svc:50051'
    else
      'token-exchange-central-svc.%s:50051' % centralNamespace,

  // Return the gRPC url for request-insight-analytics. Note that this is a "global" service in the
  // sense that it can be called across clusters, but it is deployed to both us-central1 and
  // eu-west4. Callers are responsible for deciding whether to make requests to the US or EU.
  // Location should be one of "us" or "eu".
  getRequestInsightAnalyticsGrpcUrl:: function(env, location)
    if env == 'DEV' then
      // We don't deploy the global loadbalancer in dev.
      'request-insight-analytics-svc:50051'
    else
      assert location == 'us' || location == 'eu' : 'location must be "us" or "eu"';
      local cloud = if location == 'us' then 'GCP_US_CENTRAL1_PROD' else 'GCP_EU_WEST4_PROD';
      local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, cloud=cloud, namespace=null);
      '%s:50051' % grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName='request-insight-analytics', namespace=centralNamespace),

  getRequestInsightCentralGrpcUrl:: function(env, location)
    if env == 'DEV' then
      'request-insight-central-svc:50051'
    else
      assert location == 'us' || location == 'eu' : 'location must be "us" or "eu"';
      local cloud = if location == 'us' then 'GCP_US_CENTRAL1_PROD' else 'GCP_EU_WEST4_PROD';
      local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, cloud=cloud, namespace=null);
      '%s:50051' % grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName='request-insight-central', namespace=centralNamespace),

  getThirdPartyArbiterGrpcUrl:: function(env, namespace, cloud)
    if env == 'DEV' then
      'third-party-arbiter-svc:50051'
    else
      local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, cloud=cloud, namespace=null);
      local globalCloud = 'GCP_US_CENTRAL1_PROD';
      '%s:50051' % grpcLib.globalGrpcServiceHostname(cloud=globalCloud, serviceName='third-party-arbiter', namespace=centralNamespace),

  getShredderGrpcUrl:: function(env, namespace, cloud)
    if env == 'DEV' then
      'shredder-svc:50051'
    else
      local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, cloud=cloud, namespace=null);
      local globalCloud = 'GCP_US_CENTRAL1_PROD';
      '%s:50051' % grpcLib.globalGrpcServiceHostname(cloud=globalCloud, serviceName='shredder', namespace=centralNamespace),
}

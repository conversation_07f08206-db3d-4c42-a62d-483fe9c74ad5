local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local namespaces = import 'deploy/tenants/namespaces.jsonnet';
local chatHost = import 'services/chat_host/server/deploy_lib.jsonnet';
local singleModelCompletionHost = import 'services/completion_host/single_model_server/deploy_lib.jsonnet';
local modelInstanceConfig = import 'services/deploy/model_instance/deploy_lib.jsonnet';
local editHost = import 'services/edit_host/server/deploy_lib.jsonnet';
local fastForwardEmbedderHost = import 'services/embedder_host/deploy/deploy_lib.jsonnet';
local embeddingIndexerDeploy = import 'services/embeddings_indexer/deploy_lib.jsonnet';
local inferenceHost = import 'services/inference_host/server/continuous_batching/deploy_lib.jsonnet';
local nextEditHost = import 'services/next_edit_host/server/deploy_lib.jsonnet';
local Reranker = import 'services/reranker/deploy/deploy_lib.jsonnet';

// mapping from cloud/env combination to the cloud that should be used for global deployments.
// this map is used for non-forced routing
//
// US staging deployments that use H100s get deployed as a global service so that EU staging (where
// we have fewer GPUs available) can use them.
local globalDeployCloud = {
  GCP_US_CENTRAL1_PROD: {
    STAGING: 'GCP_US_CENTRAL1_PROD',  // US staging uses US staging
    PROD: 'GCP_US_CENTRAL1_PROD',  // US prod uses US prod central
  },
  GCP_EU_WEST4_PROD: {
    STAGING: 'GCP_US_CENTRAL1_PROD',  // EU staging uses US staging
    PROD: 'GCP_EU_WEST4_PROD',  // EU prod uses EU prod central
  },
  GCP_US_CENTRAL1_DEV: {
    DEV: 'GCP_US_CENTRAL1_DEV',  // for completeness => DEV is mapped to D
  },
  GCP_US_CENTRAL1_GSC_PROD: {
    STAGING: 'GCP_US_CENTRAL1_GSC_PROD',  // deploy global endpoints
    PROD: 'GCP_US_CENTRAL1_GSC_PROD',  // deploy global endpoints
  },
};
local isGlobalDeploy(cloud, env) =
  cloud == globalDeployCloud[cloud][env]
;

// EU staging deployments use the global central services.
local isGlobalClient(cloud, env) =
  cloud == 'GCP_EU_WEST4_PROD' && env == 'STAGING'
;

// mapping from cloud/env combination to the cloud that should be used for global deployments.
// this map is used for forced routing
//
local forceGlobalDeployCloud = {
  GCP_US_CENTRAL1_PROD: {
    STAGING: 'GCP_US_CENTRAL1_PROD',  // US staging uses US staging
    PROD: 'GCP_US_CENTRAL1_PROD',  // US prod uses US prod central
  },
  GCP_EU_WEST4_PROD: {
    STAGING: 'GCP_US_CENTRAL1_PROD',  // EU staging uses US staging
    PROD: 'GCP_US_CENTRAL1_PROD',  // EU prod uses EU prod central
  },
  GCP_US_CENTRAL1_DEV: {
    DEV: 'GCP_US_CENTRAL1_DEV',  // for completeness => DEV is mapped to D
  },
  GCP_US_CENTRAL1_GSC_PROD: {
    STAGING: 'GCP_US_CENTRAL1_GSC_PROD',  // deploy global endpoints
    PROD: 'GCP_US_CENTRAL1_GSC_PROD',  // deploy global endpoints
  },
};

// Returns the hostname that clients should use to connect to a possibly global service. This is
// currently only relevant to central services that use H100s in staging.
//
// Args:
// inferHostname: the inference host name without the -svc suffix
// cloud: the cloud that the inference host is deployed to
// env: the environment that the inference host is deployed to
// headless: if headless mode (for client side load balancing) should be used for a non-global serivce or not
local centralServiceHostname(inferHostname, cloud, env, namespace, headless=false) =
  if isGlobalClient(cloud, env) then
    local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud);
    grpcLib.globalGrpcServiceHostname(cloud=globalDeployCloud[cloud][env], serviceName=inferHostname, namespace=centralNamespace)
  else
    local centralNamespaceSuffix = if env == 'DEV' then '' else if env == 'STAGING' then '.central-staging' else '.central';
    if headless then
      '%s-headless-svc%s' % [inferHostname, centralNamespaceSuffix]
    else
      '%s-svc%s' % [inferHostname, centralNamespaceSuffix]
;

// An abridged version of the above function that allows a host to always connect to the central service for its inference host.
//
// Args:
// inferHostname: the inference host name without the -svc suffix
// cloud: the cloud that the inference host is deployed to
// env: the environment that the inference host is deployed to
// headless: if headless mode (for client side load balancing) should be used for a non-global serivce or not
local centralServiceHostnameForceGlobal(inferHostname, cloud, env, headless=false, namespace, targetCloud=null) =
  if env == 'DEV' then
    // do not use global load balancer in dev. Setting up for a load balancer takes a lot of time
    if headless then
      '%s-headless-svc' % inferHostname
    else
      '%s-svc' % inferHostname
  else
    local inferCloud = if targetCloud == null then forceGlobalDeployCloud[cloud][env] else targetCloud;
    if inferCloud == cloud then
      // don't use global load balancer if inference host is in the same cloud as the host
      local centralNamespaceSuffix = if env == 'STAGING' then '.central-staging' else '.central';
      if headless then
        '%s-headless-svc%s' % [inferHostname, centralNamespaceSuffix]
      else
        '%s-svc%s' % [inferHostname, centralNamespaceSuffix]
    else
      local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=inferCloud);
      grpcLib.globalGrpcServiceHostname(cloud=inferCloud, serviceName=inferHostname, namespace=centralNamespace)
;


{
  // returns an object with configuration information about a transformation key.
  //
  // Args:
  //    namespace: the namespace of the transformation key
  //    appName: the name of the application that uses the transformation key
  //    transformationKeyName: the name of the transformation key
  //    overrideName: if not null, the name of the transformation key. Do not use the parameter for new
  //      transformation keys. It is only used for legacy transformation keys.
  transformationKey: function(namespace, appName, transformationKeyName) [
    assert std.length(transformationKeyName) <= 60;
    {
      apiVersion: 'eng.augmentcode.com/v1',
      kind: 'TransformationKey',
      metadata: {
        name: transformationKeyName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        transformationKeyName: transformationKeyName,
      },
    },
  ],

  // returns an object with configuration information about an embedding model.
  // the object contains:
  // - modelConfig: The model config object from //models
  // - hostName: the pod name of the embedder
  // - serviceName: the service name of the embedder
  // - transformationKey: the transformation key of the embedding model.
  //   The indexer is informed about all blob updates and it uploads
  //   the transformed objects using the transformtion key. During
  //   retrieval, the transformed key is used to download the embeddings and
  //   chunks.
  embeddingModelConfig: function(modelConfig, transformationKey, chunkOrigin)
    local embedderHostName = 'embedder-%s' % (std.asciiLower(modelConfig.name));
    local serviceName = function(cloud, env, namespace)
      centralServiceHostname(embedderHostName, cloud, env, headless=true, namespace=namespace);
    local serviceNameInCloud = function(cloud, env, namespace, targetCloud)
      centralServiceHostnameForceGlobal(embedderHostName, cloud, env, headless=false, namespace=namespace, targetCloud=targetCloud);
    // return the embedder endpoints for the given cloud and environment
    //
    // Args:
    // cloud: the cloud the embedder is deployed in
    // env: the environment the embedder is deployed in
    // namespace: the namespace the embedder is deployed in
    // useGscEmbedder: whether to use the GSC embedder or not. null means default
    //
    // The default for the gsc embedder is to allow GSC embedder in STAGING AND PROD
    // the usage of GSC embedder is then controlled by dynamic feature flags.
    local embedderEndpoints = function(cloud, env, namespace, useGscEmbedder)
      // don't use GSC embedder in dev and in EU prod
      local useGsc = if useGscEmbedder == null then if env == 'DEV' || (env == 'PROD' && cloud == 'GCP_EU_WEST4_PROD') then false else true else useGscEmbedder;
      local embedderServiceName = serviceName(cloud=cloud, env=env, namespace=namespace);
      local gscEmbedderServiceName = serviceNameInCloud(cloud=cloud, env=env, namespace=namespace, targetCloud='GCP_US_CENTRAL1_GSC_PROD');
      {
        default: '%s:50051' % embedderServiceName,
      } + (if useGsc then {
             gsc: '%s:50051' % gscEmbedderServiceName,
           } else {});
    {
      modelConfig: modelConfig,
      hostName: embedderHostName,
      serviceName:: serviceName,
      serviceNameInCloud:: serviceNameInCloud,
      embedderEndpoints:: embedderEndpoints,
      transformationKey: transformationKey,
      // The value should be one of the `ChunkOrigin` enum values defined in
      // "services/deploy/constants.jsonnet". This is used by CompletionHost to
      // determine the origin of the chunk.
      chunkOrigin: chunkOrigin,
    },
  // Configure an embedder
  //
  // Args:
  // env: the environment the embedder is deployed in
  // namespace: the namespace the embedder is deployed in
  // namespace_config: the namespace config for the embedder
  // cloud: the cloud the embedder is deployed in
  // embedderConfig: the embedding model config
  // embedderGpu: the GPU type to use for the embedder. If null, the default GPU type is used
  // replicas: the number of replicas to use for the embedder. If null, the default number of replicas is used
  embedder: function(env,
                     namespace,
                     namespace_config,
                     cloud,
                     embedderConfig,
                     embedderGpu=null,
                     embedder_host_handler='queueing',
                     replicas=null)
    local name = embedderConfig.modelConfig.name;
    local modelInstanceConfigMapName = '%s-model-instance-config' % (std.asciiLower(name));
    local embedderHostName = embedderConfig.hostName;
    local embedderServiceName = embedderConfig.serviceName;

    local gpu = if embedderGpu == null then if env == 'DEV' then 'small' else 'large' else embedderGpu;

    local defaultReplicas = {
      GCP_US_CENTRAL1_DEV_DEV: 1,
      GCP_EU_WEST4_PROD_STAGING: 0,
      GCP_EU_WEST4_PROD_PROD: 4,
      GCP_US_CENTRAL1_PROD_STAGING: 0,
      GCP_US_CENTRAL1_PROD_PROD: 0,
      GCP_US_CENTRAL1_GSC_PROD_STAGING: 1,
      GCP_US_CENTRAL1_GSC_PROD_PROD: 0,
    }[std.asciiUpper('%s_%s' % [cloud, env])];
    local numReplicas = if replicas == null then defaultReplicas else replicas;

    // only fastForward embedder are supported in central mode
    fastForwardEmbedderHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=embedderHostName,
      modelConfig=embedderConfig.modelConfig,
      modelInstanceConfigMapName=modelInstanceConfigMapName,
      gpu=gpu,
      replicas=numReplicas,
      embedder_host_handler=embedder_host_handler,
      global=isGlobalDeploy(cloud=cloud, env=env),
    ),
  rerankingModelConfig: function(modelConfig)
    local rerankerHostName = 'reranker-%s' % (std.asciiLower(modelConfig.name));
    local serviceName = function(cloud, env, namespace)
      centralServiceHostname(rerankerHostName, cloud, env, headless=true, namespace=namespace);
    {
      modelConfig: modelConfig,
      hostName: rerankerHostName,
      serviceName:: serviceName,
    },
  reranker: function(env,
                     namespace,
                     namespace_config,
                     cloud,
                     rerankerConfig,
                     rerankerGpu,
                     replicas=null)
    local name = rerankerConfig.modelConfig.name;
    local modelInstanceConfigMapName = '%s-model-instance-config' % (std.asciiLower(name));
    local rerankerHostName = rerankerConfig.hostName;

    local defaultReplicas = 1;
    local numReplicas = if replicas == null then defaultReplicas else replicas;
    Reranker(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=rerankerHostName,
      modelConfig=rerankerConfig.modelConfig,
      modelInstanceConfigMapName=modelInstanceConfigMapName,
      gpu=rerankerGpu,
      replicas=numReplicas,
      global=isGlobalDeploy(cloud=cloud, env=env),
    ),
  embeddingIndexer: function(env,
                             namespace,
                             namespace_config,
                             cloud,
                             embedderConfig,
                             chunker='line_level',
                             memoryOverride=null,
                             cpuOverride=null,
                             useGscEmbedder=null,
                             serviceAccount=null)
    assert cloud != 'GCP_US_CENTRAL1_GSC_PROD' : 'Indexer is not supported in GCP_US_CENTRAL1_GSC_PROD';
    local name = embedderConfig.modelConfig.name;
    local embeddingIndexerHostName = 'embedding-indexer-%s' % (std.asciiLower(name));
    local isShardNamespace = namespaces.isShardNamespace(env=env, namespace=namespace, cloud=cloud);
    local replicas =
      if env == 'DEV' then {
        min: 1,
        max: 2,
      }
      else if env == 'STAGING' then {
        min: {
          GCP_US_CENTRAL1_PROD: 2,
          GCP_EU_WEST4_PROD: 1,
        }[cloud],
        max: 8,
      }
      else if isShardNamespace then {
        min: 8,
        max: {
          GCP_US_CENTRAL1_PROD: 32,
          GCP_EU_WEST4_PROD: 8,
        }[cloud],
      } else {
        min: {
          GCP_US_CENTRAL1_PROD: 4,
          GCP_EU_WEST4_PROD: 2,
        }[cloud],
        max: 8,
      };


    local embedderEndpoints = embedderConfig.embedderEndpoints(cloud=cloud, env=env, namespace=namespace, useGscEmbedder=useGscEmbedder);

    embeddingIndexerDeploy(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=embeddingIndexerHostName,
      modelConfig=embedderConfig.modelConfig,
      embedderEndpoints=embedderEndpoints,
      transformationKey=embedderConfig.transformationKey,
      replicas=replicas.min,
      maxReplicas=replicas.max,
      chunker=chunker,
      memoryOverride=memoryOverride,
      cpuOverride=cpuOverride,
      serviceAccount=serviceAccount
    ),
  // Gets the retrieval configuration for a dense retrieval from an embedding config:
  //
  // Args:
  //    embeddingConfig: the config for the embedding service
  //    cloud: the cloud the embedding service is deployed in
  //    env: the environment the embedding service is deployed in
  //    namespace: the namespace the embedding service is deployed in
  //    namespace_config: the namespace config for the embedding service
  //    tag: an optional tag to use for the retrieval config
  //    useGscEmbedder: whether to use the GSC embedder for the retrieval config. null means default
  denseRetrievalConfig: function(embedderConfig, cloud, env, namespace, namespace_config, tag=null, useGscEmbedder=null)
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local volumeName = if tag == null then 'embedder-cert' else '%s-embedder-cert' % [tag];
    {
      retriever_name: 'DenseRetriever',
      config: {
        max_retrieval_results: if embedderConfig.modelConfig != null && std.objectHas(embedderConfig.modelConfig, 'max_retrieval_results') then embedderConfig.modelConfig.max_retrieval_results else 32,
        embedder_endpoints: embedderConfig.embedderEndpoints(cloud=cloud, env=env, namespace=namespace, useGscEmbedder=useGscEmbedder),
        embedder_mtls: mtls,
        embedder_model_name: embedderConfig.modelConfig.name,
        embedder_client_ca_path: '/%s/ca.crt' % volumeName,
        embedder_client_key_path: '/%s/tls.key' % volumeName,
        embedder_client_cert_path: '/%s/tls.crt' % volumeName,
        embeddings_search_endpoint: namespace_config.flags.embeddingsSearchEndpoint,
        partitioned_embeddings_search_endpoints: if namespace_config.flags.enablePartitionedEmbeddingsSearch then std.map(
          function(i) 'embeddings-search-cpu-%d-svc:50051' % i,
          std.range(0, namespace_config.flags.embeddingsSearchPartitions - 1),
        ) else [],
        embeddings_transformation_key: embedderConfig.transformationKey,
        embeddings_query_prompt_formatter_name: if embedderConfig.modelConfig != null then embedderConfig.modelConfig.embedding.query_prompt_formatter_name else '',
        embeddings_query_tokenizer_name: if embedderConfig.modelConfig != null then embedderConfig.modelConfig.embedding.tokenizer_name else '',
        origin: embedderConfig.chunkOrigin,
      },
      // returns kuberentes objects required for the given retrieval configuration
      getRetrievalObjects:: function(name, namespace)
        local secretName = if tag == null then '%s-embedder-cert' % name else '%s-%s-embedder-cert' % [name, tag];
        local dnsNames = grpcLib.grpcServiceNames(name, namespace=namespace);

        {
          podVolumeDef: {
            name: volumeName,
            secret: {
              secretName: secretName,
            },
          },
          volumeMountDef:
            {
              name: volumeName,
              mountPath: '/%s' % volumeName,
              readOnly: true,
            },
          objects: certLib.createCentralClientCert(
            name=secretName,
            namespace=namespace,
            env=env,
            dnsNames=dnsNames,
            appName=name,
            volumeName='',  // not used as a pod volume
          ).objects,
        },
    },
  // Gets the retrieval configuration for a docset retrieval from an embedding config:
  //
  // Args:
  //    embeddingConfig: the config for the embedding service
  //    cloud: the cloud the embedding service is deployed in
  //    env: the environment the embedding service is deployed in
  //    namespace: the namespace the embedding service is deployed in
  //    namespace_config: the namespace config for the embedding service
  //    tag: an optional tag to use for the retrieval config
  //    useGscEmbedder: whether to use the GSC embedder for the retrieval config. null means default
  docsetRetrievalConfig: function(embedderConfig, cloud, env, namespace, namespace_config, tag=null, useGscEmbedder=null)
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    // It's unfortunate that we are duplicating this with the dense retriever
    // most of the time, but it's the easiest way to implement this while keeping
    // the dense and docset retriever configs separate.
    // TODO: find a way to dedup?
    local volumeName = if tag == null then 'docs-embed-cert' else '%s-docs-embed-cert' % [tag];
    {
      retriever_name: 'DocsetRetriever',
      config: {
        max_retrieval_results: if embedderConfig.modelConfig != null && std.objectHas(embedderConfig.modelConfig, 'max_retrieval_results') then embedderConfig.modelConfig.max_retrieval_results else 32,
        embedder_endpoints: embedderConfig.embedderEndpoints(cloud=cloud, env=env, namespace=namespace, useGscEmbedder=useGscEmbedder),
        embedder_mtls: mtls,
        embedder_model_name: embedderConfig.modelConfig.name,
        embedder_client_ca_path: '/%s/ca.crt' % volumeName,
        embedder_client_key_path: '/%s/tls.key' % volumeName,
        embedder_client_cert_path: '/%s/tls.crt' % volumeName,
        docset_endpoint: 'doc-sets-svc:50051',
        embeddings_transformation_key: embedderConfig.transformationKey,
        embeddings_query_prompt_formatter_name: if embedderConfig.modelConfig != null then embedderConfig.modelConfig.embedding.query_prompt_formatter_name else '',
        embeddings_query_tokenizer_name: if embedderConfig.modelConfig != null then embedderConfig.modelConfig.embedding.tokenizer_name else '',
        origin: embedderConfig.chunkOrigin,
        auto_external_source_ids: [],
      },
      getRetrievalObjects:: function(name, namespace)
        // It's unfortunate that we are duplicating this with the dense retriever
        // most of the time, but it's the easiest way to implement this while
        // keeping the dense and docset retriever configs separate.
        // TODO: find a way to dedup?
        local secretName = if tag == null then '%s-docs-embed-cert' % name else '%s-%s-docs-embed-cert' % [name, tag];
        local dnsNames = grpcLib.grpcServiceNames(name, namespace=namespace);
        {
          podVolumeDef: {
            name: volumeName,
            secret: {
              secretName: secretName,
            },
          },
          volumeMountDef:
            {
              name: volumeName,
              mountPath: '/%s' % volumeName,
              readOnly: true,
            },
          objects: certLib.createCentralClientCert(
            name=secretName,
            namespace=namespace,
            env=env,
            dnsNames=dnsNames,
            appName=name,
            volumeName='',  // not used as a pod volume
          ).objects,
        },
    },
  RerankerConfig: function(rerankerConfig, cloud, env, namespace, namespace_config, tag=null, inner_retriever_config=null)
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local volumeName = if tag == null then 'reranker-cert' else '%s-reranker-cert' % [tag];
    assert rerankerConfig.modelConfig != null;
    {
      retriever_name: 'Reranker',
      config: {
        max_retrieval_results: rerankerConfig.modelConfig.max_retrieval_results,
        seq_len_per_batch_elem: rerankerConfig.modelConfig.seq_len_per_batch_elem,
        max_dialogue_tokens: rerankerConfig.modelConfig.max_dialogue_tokens,
        batch_size: rerankerConfig.modelConfig.batch_size,
        reranker_endpoint: '%s:50051' % rerankerConfig.serviceName(cloud=cloud, env=env, namespace=namespace),
        reranker_mtls: mtls,
        reranker_client_ca_path: '/%s/ca.crt' % volumeName,
        reranker_client_key_path: '/%s/tls.key' % volumeName,
        reranker_client_cert_path: '/%s/tls.crt' % volumeName,

        reranker_prompt_formatter_name: if rerankerConfig.modelConfig != null then rerankerConfig.modelConfig.prompt_formatter.reranker_prompt_formatter_name else '',
        reranker_tokenizer_name: if rerankerConfig.modelConfig != null then rerankerConfig.modelConfig.prompt_formatter.tokenizer_name else '',

        cache_memory_limit_gb: 1,
        reranker_transformation_key: inner_retriever_config.config.embeddings_transformation_key,
        origin: inner_retriever_config.config.origin,

        inner_retriever_name: inner_retriever_config.retriever_name,
        inner_retriever_config: inner_retriever_config.config,
      },
      // returns kuberentes objects required for the given retrieval configuration
      getRetrievalObjects:: function(name, namespace)
        local secretName = if tag == null then '%s-reranker-cert' % name else '%s-%s-reranker-cert' % [name, tag];
        local dnsNames = grpcLib.grpcServiceNames(name, namespace=namespace);

        local inner_retriever_info = inner_retriever_config.getRetrievalObjects(name, namespace);

        {
          podVolumeDef: {
            name: volumeName,
            secret: {
              secretName: secretName,
            },
          },
          volumeMountDef:
            {
              name: volumeName,
              mountPath: '/%s' % volumeName,
              readOnly: true,
            },
          objects: certLib.createCentralClientCert(
            name=secretName,
            namespace=namespace,
            env=env,
            dnsNames=dnsNames,
            appName=name,
            volumeName='',  // not used as a pod volume
          ).objects,
          innerRetrieverInfo: inner_retriever_info,
        },
    },

  routerConfig: function(
    routerConfig,
    cloud,
    env,
    namespace,
    namespace_config,
    code_retriever_config,
    docset_retriever_config,
    user_guided_retriever_config,
    tag=null
               )
    local inferHostname = 'infer-%s' % (std.asciiLower(routerConfig.name));
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local volumeName = if tag == null then 'router-cert' else '%s-router-cert' % [tag];
    local serviceName = centralServiceHostnameForceGlobal(inferHostname, cloud, env, headless=true, namespace=namespace);
    assert routerConfig != null;
    assert routerConfig.inference != null;
    {
      retriever_name: 'Router',
      config: {
        agent_endpoint: '%s:50051' % serviceName,
        agent_mtls: mtls,
        agent_client_ca_path: '/%s/ca.crt' % volumeName,
        agent_client_key_path: '/%s/tls.key' % volumeName,
        agent_client_cert_path: '/%s/tls.crt' % volumeName,
        router_timeout_s: if std.objectHas(routerConfig, 'router_timeout_s') then routerConfig.router_timeout_s else 5,
        router_name: routerConfig.name,
        router_prompt_formatter_name: routerConfig.inference.prompt_formatter_name,
        router_tokenizer_name: routerConfig.inference.tokenizer_name,
        router_max_output_length: routerConfig.inference.max_output_length,
        router_token_apportionment: routerConfig.inference.token_apportionment,

        code_retriever_name: code_retriever_config.retriever_name,
        code_retriever_config: code_retriever_config.config,
        docset_retriever_name: docset_retriever_config.retriever_name,
        docset_retriever_config: docset_retriever_config.config,
        user_guided_retriever_name: user_guided_retriever_config.retriever_name,
        user_guided_retriever_config: user_guided_retriever_config.config,
      },
      // returns kuberentes objects required for the given retrieval configuration
      getRetrievalObjects:: function(name, namespace)
        local secretName = if tag == null then '%s-router-cert' % name else '%s-%s-router-cert' % [name, tag];
        local dnsNames = grpcLib.grpcServiceNames(name, namespace=namespace);

        local inner_retrievers_info = lib.flatten([
          code_retriever_config.getRetrievalObjects(name, namespace),
          docset_retriever_config.getRetrievalObjects(name, namespace),
          user_guided_retriever_config.getRetrievalObjects(name, namespace),
        ]);

        {
          podVolumeDef: {
            name: volumeName,
            secret: {
              secretName: secretName,
            },
          },
          volumeMountDef:
            {
              name: volumeName,
              mountPath: '/%s' % volumeName,
              readOnly: true,
            },
          objects: certLib.createCentralClientCert(
            name=secretName,
            namespace=namespace,
            env=env,
            dnsNames=dnsNames,
            appName=name,
            volumeName='',  // not used as a pod volume
          ).objects,
          innerRetrieversInfo: inner_retrievers_info,
        },
    },

  postprocessConfig: function(postprocessConfig, cloud, env, namespace, namespace_config, tag=null)
    local inferHostname = 'infer-%s' % (std.asciiLower(postprocessConfig.name));
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local volumeName = if tag == null then 'postprocess-cert' else '%s-postprocess-cert' % [tag];
    local serviceName = centralServiceHostnameForceGlobal(inferHostname, cloud, env, headless=true, namespace=namespace);
    assert postprocessConfig != null;
    assert postprocessConfig.inference != null;
    {
      config: {
        endpoint: '%s:50051' % serviceName,
        mtls: mtls,
        client_ca_path: '/%s/ca.crt' % volumeName,
        client_key_path: '/%s/tls.key' % volumeName,
        client_cert_path: '/%s/tls.crt' % volumeName,
        timeout_s: if std.objectHas(postprocessConfig, 'timeout_s') then postprocessConfig.timeout_s else 5,
        name: postprocessConfig.name,
        prompt_formatter_name: postprocessConfig.inference.prompt_formatter_name,
        tokenizer_name: postprocessConfig.inference.tokenizer_name,
        max_output_length: postprocessConfig.inference.max_output_length,
        token_apportionment: postprocessConfig.inference.token_apportionment,
      },
      // returns kuberentes objects required for the given retrieval configuration
      getRetrievalObjects:: function(name, namespace)
        local secretName = if tag == null then '%s-postprocess-cert' % name else '%s-%s-postprocess-cert' % [name, tag];
        local dnsNames = grpcLib.grpcServiceNames(name, namespace=namespace);
        {
          podVolumeDef: {
            name: volumeName,
            secret: {
              secretName: secretName,
            },
          },
          volumeMountDef:
            {
              name: volumeName,
              mountPath: '/%s' % volumeName,
              readOnly: true,
            },
          objects: certLib.createCentralClientCert(
            name=secretName,
            namespace=namespace,
            env=env,
            dnsNames=dnsNames,
            appName=name,
            volumeName='',  // not used as a pod volume
          ).objects,
        },
    },

  // Gets the retrieval configuration for a recency retrieval from an embedding config:
  //
  // Args:
  //    embeddingConfig: the config for the embedding service
  recencyRetrievalConfig: function(
    env,
    namespace,
    namespace_config,
    tabSwitchAndGitDiffRetrievalEnabled=false

                         )
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    {
      retriever_name: 'RecencyRetriever',
      config: {
        internal_retriever_name: 'DenseRetriever',
        max_retrieval_results: if env == 'PROD' then 32 else 128,
        tab_switch_and_git_diff_retrieval_enabled: tabSwitchAndGitDiffRetrievalEnabled,
        cache_memory_limit_gb: 1,
      },
      getRetrievalObjects:: function(name, namespace)
        {
          podVolumeDef: null,
          volumeMountDef: null,
          objects: [],
        },
    },
  // Gets the retrieval configuration for a user guided retrieval from an embedding config:
  userGuidedRetrievalConfig: function(env, namespace, namespace_config, max_retrieval_results=32)
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    {
      retriever_name: 'UserGuidedRetriever',
      config: {
        internal_retriever_name: 'DenseRetriever',
        max_retrieval_results: max_retrieval_results,
      },
      getRetrievalObjects:: function(name, namespace)
        {
          podVolumeDef: null,
          volumeMountDef: null,
          objects: [],
        },
    },
  // Gets the retrieval configuration for a diff retrieval from an embedding config:
  diffRetrievalConfig: function(env, namespace, namespace_config, max_total_changed_chars=5000, big_event_lines=8, diff_context_lines=7, use_smart_header=true, filter_duplicated_file_paths=true)
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    {
      retriever_name: 'DiffRetriever',
      config: {
        internal_retriever_name: 'DenseRetriever',
        max_retrieval_results: 32,
        content_cache_size_mb: 1024,
        max_total_changed_chars: max_total_changed_chars,
        big_event_lines: big_event_lines,
        diff_context_lines: diff_context_lines,
        use_smart_header: use_smart_header,
        filter_duplicated_file_paths: filter_duplicated_file_paths,
      },
      getRetrievalObjects:: function(name, namespace)
        {
          podVolumeDef: null,
          volumeMountDef: null,
          objects: [],
        },
    },
  // configures an single completion model deployment:
  //
  // Args:
  //    env: environemnt
  //    namespace: namespace to use
  //    name: name of the model
  //    model config: model config (from //services/deploy/configs) object
  //    embeddingModelConfig: the model config for the embedding model to use
  //    embedderService: The name of the embedder service to use
  //    embeddingsTransformationKey: the name of the transformation key to embedder stores the transformed content under
  //    overrideCompletionHostConfig: object that overrides a completion host configuration
  //    modelPriority: model priority
  completionHost: function(
    env,
    namespace,
    namespace_config,
    cloud=cloud,
    name,
    modelConfig,
    retrievalCollectorConfig=null,
    retrievalConfigs,
    overrideCompletionHostConfig=null,
    modelPriority,
    mtls=null,
    inferenceMtls=null,
    skip_token_str=null,
                 )
    local modelInstanceConfigMapName = '%s-model-config' % (std.asciiLower(name));
    local inferenceServices = if cloud == 'GCP_EU_WEST4_PROD' || env == 'DEV' then {
      default: centralServiceHostnameForceGlobal('infer-%s' % (std.asciiLower(name)), cloud, env, namespace=namespace, headless=true),
    } else {
      default: centralServiceHostname('infer-%s' % (std.asciiLower(name)), cloud, env, namespace=namespace, headless=true),
      gsc: centralServiceHostnameForceGlobal('infer-%s' % (std.asciiLower(name)), cloud, env, namespace=namespace, headless=false, targetCloud='GCP_US_CENTRAL1_GSC_PROD'),
    };
    local completionName = 'completion-%s' % (std.asciiLower(name));
    // Enabling client-side load-balancing across the headless-svc endpoints is
    // known to cause low rate of 503 errors. Based upon testing in staging,
    // these primarily affect infrequent health check requests to non-default
    // models, i.e. the lowest-bandwidth channels. Automatic retrying of the
    // completion request within api-proxy ensures that
    // these completion requests succeed regardless.
    local completionService = '%s-headless-svc' % completionName;
    local defaultMtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local defaultRetrievalCollectorConfig = {
      enabled: false,
      modified_chunks_filter_enabled: false,
    };

    local completionHostObjects = singleModelCompletionHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      modelConfig=modelConfig,
      inferenceServices=inferenceServices,
      name=completionName,
      retrievalCollectorConfig=if retrievalCollectorConfig == null then defaultRetrievalCollectorConfig else retrievalCollectorConfig,
      retrievalConfigs=retrievalConfigs,
      overrideConfig=overrideCompletionHostConfig,
      mtls=if mtls == null then defaultMtls else mtls,
      inferenceMtls=if inferenceMtls == null then defaultMtls else inferenceMtls,
      skip_token_str=skip_token_str,
    );
    local modelInstanceConfigObjects = modelInstanceConfig(
      name=modelInstanceConfigMapName,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      modelConfig=modelConfig,
      generationService=completionService,
      modelPriority=modelPriority,
      // NOTE(arun): We are overriding the model name to be that of the completion host.
      // The model name is used both as a name in the inference host (for logging) and
      // as a "public" name that can be configured in HTTP requests to the API proxy.
      // Furthermore, the "model name" here really is a "completion strategy" that
      // combines the language model (e.g. rogue-16B) with the retriever (e.g. ethanol).
      overrideModelName=name,
      appName=completionName,
    );
    completionHostObjects + modelInstanceConfigObjects,
  // configures a central (shared between tenants) inference host.
  //
  // Args:
  //    env: environemnt
  //    namespace: namespace to use
  //    name: name of the model
  //    model config: model config (from //services/deploy/configs) object
  //    inferGpu: The GPU to use for inference
  //    inferTensorPara: The tensor parallelism to use
  //    enforceGlobal: whether to deploy the service as a global service
  centralInferenceHost: function(
    env,
    namespace,
    namespace_config,
    cloud=cloud,
    name,
    modelConfig,
    inferGpu,
    inferTensorPara=1,
    mtls,
    replicas=null,
    attentionType='MULTI_REQUEST_FLASH',
    allReduceImplementation='NCCL',
    // TODO: Remove this once we have a better way to enforce global services for EDIT services.
    enforceGlobal=false,
                       )
    local inferenceHostName = 'infer-%s' % (std.asciiLower(name));
    local numReplicas = if replicas == null then {
      GCP_US_CENTRAL1_PROD: {
        PROD: 2,
        STAGING: 1,
      },
      GCP_EU_WEST4_PROD: {
        PROD: 2,
        STAGING: 1,
      },
      GCP_US_CENTRAL1_DEV: {
        DEV: 1,
      },
      GCP_US_CENTRAL1_GSC_PROD: {
        PROD: 0,
        STAGING: 0,
      },
    }[cloud][env] else replicas;
    // Temporary solution to deploy central inference hosts as global services for EU to use.
    local global = if modelConfig.model_type == 'INFERENCE' || modelConfig.model_type == 'CHAT' || enforceGlobal then true else isGlobalDeploy(cloud=cloud, env=env);
    local inferenceHostObjects = inferenceHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=inferenceHostName,
      modelConfig=modelConfig,
      gpu=inferGpu,
      gpuCount=inferTensorPara,
      mtls=mtls,
      replicas=numReplicas,
      attentionType=attentionType,
      allReduceImplementation=allReduceImplementation,
      // Temporary solution to deploy central inference hosts as global services for EU to use.
      // global=globalDeploy(cloud=cloud, env=env),
      global=global,
    );
    inferenceHostObjects,
  // configures an edit model deployment with a central/shared inference host:
  //
  // Args:
  //    env: environemnt
  //    namespace: namespace to use
  //    name: name of the model
  //    model config: model config (from //services/deploy/configs) object
  //    embeddingModelConfig: the model config for the embedding model to use
  //    embedderService: The name of the embedder service to use
  //    embeddingsTransformationKey: the name of the transformation key to embedder stores the transformed content under
  //    overrideEditHostConfig: object that overrides a edit host configuration
  //    modelPriority: model priority
  editDeploymentHost: function(
    env,
    namespace,
    namespace_config,
    cloud=cloud,
    name,
    modelConfig,
    retrievalConfigs,
    overrideEditHostConfig=null,
    modelPriority,
    mtls=null,
    inferenceMtls=null,
    thirdPartyInferenceConfig=null,
                     )
    local modelInstanceConfigMapName = '%s-model-config' % (std.asciiLower(name));
    local inferenceServices = {
      default: centralServiceHostnameForceGlobal('infer-%s' % (std.asciiLower(name)), cloud, env, namespace=namespace, headless=true),
      gsc: centralServiceHostnameForceGlobal('infer-%s' % (std.asciiLower(name)), cloud, env, namespace=namespace, headless=true, targetCloud='GCP_US_CENTRAL1_GSC_PROD'),
    };
    local editName = 'edit-%s' % (std.asciiLower(name));
    local editService = 'edit-%s-svc' % (std.asciiLower(name));
    local defaultMtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local editHostObjects = editHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      modelConfig=modelConfig,
      inferenceServices=inferenceServices,
      name=editName,
      retrievalConfigs=retrievalConfigs,
      overrideConfig=overrideEditHostConfig,
      mtls=if mtls == null then defaultMtls else mtls,
      inferenceMtls=if inferenceMtls == null then defaultMtls else inferenceMtls,
      thirdPartyInferenceConfig=thirdPartyInferenceConfig,
    );
    local modelInstanceConfigObjects = modelInstanceConfig(
      name=modelInstanceConfigMapName,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      modelConfig=modelConfig,
      generationService=editService,
      modelPriority=modelPriority,
      // NOTE(arun): We are overriding the model name to be that of the edit host.
      // The model name is used both as a name in the inference host (for logging) and
      // as a "public" name that can be configured in HTTP requests to the API proxy.
      // Furthermore, the "model name" here really is a "edit strategy" that
      // combines the language model (e.g. rogue-16B) with the retriever (e.g. ethanol).
      overrideModelName=name,
      appName=editName,
    );
    editHostObjects + modelInstanceConfigObjects,
  chatDeploymentHost: function(
    env,
    namespace,
    namespace_config,
    cloud=cloud,
    name,
    modelConfig,
    retrievalConfigs,
    overrideChatHostConfig=null,
    modelPriority,
    mtls=null,
    inferenceMtls=null,
    thirdPartyInferenceConfig=null,
    thirdPartyInferenceMultiConfig=null,
    suggestedQuestionsConfig=null,
    postprocessConfig=null,
    prodReplicas=2,
                     )
    local inferHostname = 'infer-%s' % (std.asciiLower(name));
    local modelInstanceConfigMapName = '%s-model-config' % (std.asciiLower(name));
    local inferenceServices = if thirdPartyInferenceConfig == null then {
      // Temporary solution to make sure all chat deployments (including EU) use global services.
      default: centralServiceHostnameForceGlobal(inferHostname, cloud, env, namespace=namespace, headless=true),
      gsc: centralServiceHostnameForceGlobal(inferHostname, cloud, env, namespace=namespace, headless=true, targetCloud='GCP_US_CENTRAL1_GSC_PROD'),
    }
    else null;
    local chatName = 'chat-%s' % (std.asciiLower(name));
    local chatService = chatName + '-headless-svc';  // API proxy to chat host connects headlessly (direct to individual pods)
    local defaultMtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local chatHostObjects = chatHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      modelConfig=modelConfig,
      retrievalConfigs=retrievalConfigs,
      inferenceServices=inferenceServices,
      name=chatName,
      overrideConfig=overrideChatHostConfig,
      mtls=if mtls == null then defaultMtls else mtls,
      inferenceMtls=if inferenceMtls == null then defaultMtls else inferenceMtls,
      thirdPartyInferenceConfig=thirdPartyInferenceConfig,
      thirdPartyInferenceMultiConfig=thirdPartyInferenceMultiConfig,
      suggestedQuestionsConfig=suggestedQuestionsConfig,
      postprocessConfig=postprocessConfig,
      prodReplicas=prodReplicas,
    );

    // Support mapping of third party multiconfig handlers to a client
    local updatedModelConfig = if thirdPartyInferenceMultiConfig == null
    then modelConfig
    else modelConfig + { handler_names: std.objectFields(thirdPartyInferenceMultiConfig) };

    local modelInstanceConfigObjects = modelInstanceConfig(
      name=modelInstanceConfigMapName,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      modelConfig=updatedModelConfig,
      generationService=chatService,
      modelPriority=modelPriority,
      // NOTE(arun): We are overriding the model name to be that of the chat host.
      // The model name is used both as a name in the inference host (for logging) and
      // as a "public" name that can be configured in HTTP requests to the API proxy.
      // Furthermore, the "model name" here really is a "chat strategy" that
      // combines the language model (e.g. rogue-16B) with the retriever (e.g. ethanol).
      overrideModelName=name,
      appName=chatName,
    );
    chatHostObjects + modelInstanceConfigObjects,

  nextEditDeploymentHost: function(
    env,
    namespace,
    namespace_config,
    cloud,
    name,
    modelConfig,
    rerankerName,
    chatName,
    handlerConfig,
    generationRetrievalConfigs,
    locationRetrievalConfigs,
    lowQualityFilterConfig,
                         )
    local modelInstanceConfigMapName = '%s-model-config' % (std.asciiLower(name));
    local chatModelInstanceConfigMapName = '%s-chat-model-config' % (std.asciiLower(name));
    local centralInferenceHostNamespace = if env == 'DEV' then namespace else if env == 'STAGING' then 'central-staging' else 'central';
    local inferenceServices = {
      default: 'infer-%s-headless-svc.%s' % [std.asciiLower(name), centralInferenceHostNamespace],
      gsc: centralServiceHostnameForceGlobal('infer-%s' % std.asciiLower(name), cloud, env, headless=false, namespace=namespace, targetCloud='GCP_US_CENTRAL1_GSC_PROD'),
    };
    local rerankerServices = if rerankerName != null then {
      default: 'infer-%s-headless-svc.%s' % [std.asciiLower(rerankerName), centralInferenceHostNamespace],
      gsc: centralServiceHostnameForceGlobal('infer-%s' % std.asciiLower(rerankerName), cloud, env, headless=false, namespace=namespace, targetCloud='GCP_US_CENTRAL1_GSC_PROD'),
    } else null;
    local descriptionServices = if chatName != null then {
      default: 'infer-%s-headless-svc.%s' % [std.asciiLower(chatName), centralInferenceHostNamespace],
      gsc: centralServiceHostnameForceGlobal('infer-%s' % std.asciiLower(chatName), cloud, env, headless=false, namespace=namespace, targetCloud='GCP_US_CENTRAL1_GSC_PROD'),
    } else null;
    local nextEditName = 'next-edit-%s' % (std.asciiLower(name));
    local nextEditService = 'next-edit-%s-svc' % (std.asciiLower(name));
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local nextEditHostObjects = nextEditHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      handlerConfig=handlerConfig,
      generationRetrievalConfigs=generationRetrievalConfigs,
      locationRetrievalConfigs=locationRetrievalConfigs,
      inferenceServices=inferenceServices,
      descriptionServices=descriptionServices,
      rerankerServices=rerankerServices,
      name=nextEditName,
      mtls=mtls,
      inferenceMtls=mtls,
      lowQualityFilterConfig=lowQualityFilterConfig,
    );
    local modelInstanceConfigObjects = modelInstanceConfig(
      name=modelInstanceConfigMapName,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      modelConfig=modelConfig,
      generationService=nextEditService,
      modelPriority=0,  // deprecated, we use flags instead
      // NOTE(arun): We are overriding the model name to be that of the next edit host.
      // The model name is used both as a name in the inference host (for logging) and
      // as a "public" name that can be configured in HTTP requests to the API proxy.
      // Furthermore, the "model name" here really is a "next edit strategy" that
      // combines the language model (e.g. rogue-16B) with the retriever (e.g. ethanol).
      overrideModelName=name,
      appName=nextEditName,
    );
    nextEditHostObjects + modelInstanceConfigObjects,
}

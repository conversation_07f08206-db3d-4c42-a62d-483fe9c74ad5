"""Configuration and factory methods to create chunkers."""

import logging
import socket
import sys
import typing
from dataclasses import dataclass
from typing import cast

import grpc
from dataclasses_json import dataclass_json

import services.embeddings_indexer.line_rpc as line_rpc
from models.retrieval.chunking import (
    chunking,
    docset_chunkers,
    line_level_chunker,
    pre_chunked_chunker,
    signature_chunker,
    smart_line_chunker,
    transform_chunker,
)


@dataclass_json
@dataclass
class ChunkerConfig:
    """Configuration for a chunker."""

    name: str
    """The name of the chunker to create."""
    config: dict
    """Configuration for the chunker specified by `name`.

    Valid configs are:
        - `LineLevelChunkerConfig`
        - `SignatureChunkerConfig`
        - `TransformLineLevelChunkerConfig`

    Note: The config values are validated by corresponding factory functions in this
    module. We have tried `config: LineLevelChunkerConfig | SignatureChunkerConfig` to
    simplify json validation and it doesn't work.
    """
    subprocess_chunker: bool = False
    """Whether to use the newer subprocess mechanism to run the chunker."""
    skip_docset_documents: bool = False
    """Whether to skip docset documents."""


def create_chunker(config: ChunkerConfig) -> chunking.Chunker:
    """Create an instance of the chunker."""
    inner_chunker = None
    if config.name == "line_level":
        inner_chunker = create_line_level_chunker(config.config)
    elif config.name == "signature_raw":
        inner_chunker = create_signature_chunker(config.config)
    elif config.name == "smart_line_level":
        inner_chunker = create_smart_line_level_chunker(config.config)
    elif config.name == "transform_line_level":
        inner_chunker = create_transform_line_level_chunker(config.config)
    elif config.name == "transform_smart_line_level":
        inner_chunker = create_transform_smart_line_level_chunker(config.config)
    else:
        raise ValueError(f"Unknown chunker: {config.name}")

    if config.skip_docset_documents:
        return docset_chunkers.DocsetSkippingChunker(inner_chunker)
    else:
        return docset_chunkers.DocsetPreservingChunker(inner_chunker)


@dataclass_json
@dataclass
class LineLevelChunkerConfig:
    """Configuration for a chunker."""

    max_lines_per_chunk: int
    """The name of the chunker to create."""
    max_chunk_size: int
    hard_line_limit: int = 2048


def create_line_level_chunker(config: dict) -> line_level_chunker.LineLevelChunker:
    """Create an instance of `LineLevelChunker`."""
    config = cast(
        LineLevelChunkerConfig,
        # pylint:disable-next=no-member
        LineLevelChunkerConfig.schema().load(config),  # type: ignore
    )
    assert isinstance(config, LineLevelChunkerConfig)
    return line_level_chunker.LineLevelChunker(
        max_lines_per_chunk=config.max_lines_per_chunk,
        max_chunk_size=config.max_chunk_size,
        hard_line_limit=config.hard_line_limit,
    )


@dataclass_json
@dataclass
class SignatureChunkerConfig:
    """Configuration for a chunker."""

    show_private_members: bool = False
    """Whether to show private members."""
    max_docstr_chars: int = 0
    """Maximum number of docstring characters to include in signatures."""


def create_signature_chunker(config: dict) -> signature_chunker.SignatureChunker:
    """Create an instance of `SignatureChunker`."""
    config = cast(
        SignatureChunkerConfig,
        # pylint:disable-next=no-member
        SignatureChunkerConfig.schema().load(config),  # type: ignore
    )
    assert isinstance(config, SignatureChunkerConfig)
    return signature_chunker.SignatureChunker(
        show_private_members=config.show_private_members,
        max_docstr_chars=config.max_docstr_chars,
    )


class SubprocessChunker(chunking.Chunker):
    """A chunker that runs a subprocess to split the document."""

    def __init__(self):
        self.rpc_subprocess = line_rpc.RestartableSubprocess(
            command=sys.executable,
            args=sys.orig_argv[1:] + ["--serve-chunker"],
        )

    def split_into_chunks(
        self, doc: chunking.Document
    ) -> typing.Iterable[chunking.Chunk]:
        try:
            responses = self.rpc_subprocess.call(
                doc.to_json(),
                lambda response: chunking.Chunk.schema().loads(response, many=True),
                timeout_s=10,
            )
        except TimeoutError as exc:
            logging.exception("Chunk processing timed out")
            raise chunking.ChunkingException(grpc.StatusCode.DEADLINE_EXCEEDED) from exc
        except RuntimeError as exc:
            logging.exception("Chunk processing failed to complete")
            raise chunking.ChunkingException(grpc.StatusCode.INTERNAL) from exc
        except Exception as exc:  # pylint: disable=broad-exception-caught
            logging.exception(exc)
            raise

        for chunk in responses:
            chunk.parent_doc = doc
            yield chunk


def create_subprocess_chunker() -> SubprocessChunker:
    """Create an instance of `SubprocessChunker`."""
    return SubprocessChunker()


@dataclass_json
@dataclass
class SmartLineLevelChunkerConfig:
    """Configuration for a chunker."""

    max_chunk_chars: int
    """Maximum number of characters in a chunk."""
    max_headers: int = 3
    """The maximum number of headers to include in the chunk.

    Deeper headers will be ignored. Setting this to 0 will disable smart headers.
    """


def create_smart_line_level_chunker(
    config: dict,
) -> smart_line_chunker.SmartLineChunker:
    """Create an instance of `LineLevelChunker`."""
    config = cast(
        SmartLineLevelChunkerConfig,
        # pylint:disable-next=no-member
        SmartLineLevelChunkerConfig.schema().load(config),  # type: ignore
    )
    assert isinstance(config, SmartLineLevelChunkerConfig)
    return smart_line_chunker.SmartLineChunker(
        max_chunk_chars=config.max_chunk_chars,
        max_headers=config.max_headers,
    )


@dataclass_json
@dataclass
class TransformLineLevelChunkerConfig:
    """Configuration for a chunker."""

    max_lines_per_chunk: int
    """The name of the chunker to create."""
    max_chunk_size: int
    hard_line_limit: int = 2048


def create_transform_line_level_chunker(
    config: dict,
) -> transform_chunker.TransformChunker:
    """Create an instance of `TransformLineLevelChunker`."""
    config = cast(
        TransformLineLevelChunkerConfig,
        # pylint:disable-next=no-member
        TransformLineLevelChunkerConfig.schema().load(config),  # type: ignore
    )
    assert isinstance(config, TransformLineLevelChunkerConfig)
    return transform_chunker.TransformChunker(
        chunker=line_level_chunker.LineLevelChunker(
            max_lines_per_chunk=config.max_lines_per_chunk,
            max_chunk_size=config.max_chunk_size,
            hard_line_limit=config.hard_line_limit,
        )
    )


@dataclass_json
@dataclass
class TransformSmartLineLevelChunkerConfig:
    """Configuration for a chunker."""

    max_chunk_chars: int
    max_headers: int = 3


def create_transform_smart_line_level_chunker(
    config: dict,
) -> transform_chunker.TransformChunker:
    """Create an instance of `TransformSmartLineLevelChunker`."""
    config = cast(
        TransformSmartLineLevelChunkerConfig,
        # pylint:disable-next=no-member
        TransformSmartLineLevelChunkerConfig.schema().load(config),  # type: ignore
    )
    assert isinstance(config, TransformSmartLineLevelChunkerConfig)
    return transform_chunker.TransformChunker(
        chunker=smart_line_chunker.SmartLineChunker(
            max_chunk_chars=config.max_chunk_chars,
            max_headers=config.max_headers,
        )
    )


def serve_chunker(chunker: chunking.Chunker) -> None:
    chunk_schema = chunking.Chunk.schema()

    def split_into_chunks(request: str) -> str:
        doc = chunking.Document.from_json(request)
        try:
            results = list(chunker.split_into_chunks(doc))
        except Exception as exc:  # pylint: disable=broad-exception-caught
            logging.exception(exc)
            # If we care about passing back error details, then we
            # should create a type that's either a error or a list of results

            results = []

        # Don't send back the text of the document we split
        doc.text = ""

        return chunk_schema.dumps(results, many=True)

    line_rpc.server(socket.socket(fileno=0), split_into_chunks)

// K8S deployment file for the embeddings indexer server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
function(name,
         namespace,
         namespace_config,
         cloud,
         modelConfig,
         transformationKey,
         embedderEndpoints,
         env,
         replicas,
         maxReplicas,
         chunker,
         memoryOverride=null,
         cpuOverride=null,
         serviceAccount=null,)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local services = grpcLib.grpcService(appName=name, namespace=namespace);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, appName=name, cloud=cloud);
  local tokenExchangeEndpoint = endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace);
  local config = {
    port: 50051,
    content_manager_endpoint: 'content-manager-svc:50051',
    embedder_endpoints: embedderEndpoints,
    token_exchange_endpoint: tokenExchangeEndpoint,
    tokenizer_name: modelConfig.embedding.tokenizer_name,
    model_name: modelConfig.name,
    chunking: std.get(modelConfig, 'chunking', default={
      name: chunker,
      config: {
        max_lines_per_chunk: 30,
        max_chunk_size: 1024,
      },
    }),
    embedding_prompt_formatter_name: modelConfig.embedding.key_prompt_formatter_name,
    transformation_key: transformationKey,
    max_handler_workers: 4,
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    max_number_of_chunks_per_blob: std.get(modelConfig, 'max_number_of_chunks_per_blob', default=null),
    max_embedder_request_size: 32,
    forward_metadata: if env == 'DEV' then true else false,
  } + if !mtls then {} else {
    client_mtls: {
      ca_path: '/client-certs/ca.crt',
      key_path: '/client-certs/tls.key',
      cert_path: '/client-certs/tls.crt',
    },
    server_mtls: {
      ca_path: '/certs/ca.crt',
      key_path: '/certs/tls.key',
      cert_path: '/certs/tls.crt',
    },
    central_client_mtls: {
      ca_path: '/central-client-certs/ca.crt',
      key_path: '/central-client-certs/tls.key',
      cert_path: '/central-client-certs/tls.crt',
    },
  };

  local configMap = configMapLib.createConfigMap(appName=name, namespace=namespace, config=config);
  // create a client TLS certificate to securely access the content manager
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % name,
    namespace=namespace,
    appName=name,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(name)
  );
  // the name is outdated, but we need to keep it for backwards compatibility
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-embedder-cert' % name,
    namespace=namespace,
    env=env,
    appName=name,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(name, namespace=namespace)
  );
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % name,
                                              namespace=namespace,
                                              appName=name,
                                              dnsNames=grpcLib.grpcServiceNames(name),
                                              volumeName='certs');
  local container =
    {
      name: 'embeddings-indexer',
      target: {
        name: '//services/embeddings_indexer:image',
        dst: 'embeddings_indexer',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv('embeddings-indexer', telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        centralClientCert.volumeMountDef,
        serverCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: if cpuOverride == null then '0.5' else cpuOverride,
          memory: if memoryOverride == null then '1600Mi' else memoryOverride,
        },
      },
    };
  local pod =
    {
      containers: [
        container,
      ],
      terminationGracePeriodSeconds: 120,
      volumes: [
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        centralClientCert.podVolumeDef,
        serverCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    } + if serviceAccount != null then {
      serviceAccountName: serviceAccount.name,
    } else {};
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  // we allow multiple indexers on the same host as they run in the background
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: name,
      namespace: namespace,
      labels: {
        app: name,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: replicas,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: if replicas <= 4 then 1 else 2,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: name,
        },
      },
      template: {
        metadata: {
          labels: {
            app: name,
          },
        },
        spec: pod + {
          affinity: affinity,
          tolerations: tolerations,
          priorityClassName: cloudInfo.envToPriorityClass(env),
        },
      },
    },
  };
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: '%s-scaledobject' % name,
      namespace: namespace,
      labels: {
        app: name,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: name,
      },
      minReplicaCount: replicas,
      maxReplicaCount: maxReplicas,
      advanced: {
        // Keda passes these to the HPA.
        // - https://keda.sh/docs/2.14/concepts/scaling-deployments/
        // - https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/#configurable-scaling-behavior
        horizontalPodAutoscalerConfig: {
          behavior: {
            scaleDown: {
              // This is a rolling maximum over the last 2 minutes
              // to ensure that the indexer queues can drain before
              // scaling down.
              stabilizationWindowSeconds: 120,
              policies: [
                {
                  // Reduce the number of indexers by 50% every 60 seconds.
                  type: 'Percent',
                  value: 50,
                  periodSeconds: 60,
                },
              ],
            },
            scaleUp: {
              stabilizationWindowSeconds: 0,  // default
              policies: [
                {
                  // Increase the number of indexers by 4. The goal
                  // is to scale up quickly.
                  type: 'Pods',
                  value: 4,
                  periodSeconds: 30,
                },
              ],
              selectPolicy: 'Max',  // default; select the policy that affects the most pods
            },
          },
        },
      },
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            metricName: 'au_embeddings_indexer_blob_latency_seconds',
            threshold: '1',
            // if the average indexer are more than 40% busy, get more indexers
            // latency_sum is the sum of all indexing (which is single-threaded), so rate gives a utilization
            //
            // We also subtract the backoff time from the utilization, so that we don't scale up when the
            // indexers are just waiting.
            query:
              // first we define a promql query that represents the indexer utilization in percent (and subtract the time spent in backoff)
              local indexerUtilizationPercent = '(avg(rate(au_embeddings_indexer_blob_latency_seconds_sum{model_name="%s", namespace="%s"}[1m]) - rate(au_embeddings_indexer_retry_backoff_seconds_histogram_sum{model_name="%s", namespace="%s"}[1m]))) * 100' % [modelConfig.name, namespace, modelConfig.name, namespace];
              // Then we use the utilization to define the TARGET VALUE for the HPA. The documentation elaborates that the target value for the resource is:
              //     desiredReplicas = ceil[currentReplicas * ( currentMetricValue / desiredMetricValue )]
              // We want to achieve aggressive upscaling as soon as we exceed 40% utilization, so do some hacking:
              // - we set the target value to 1%  (see threshold above)
              // - we subtract 39% from the utilization to get the target value for the HPA
              // If, for example, we observe a utilization of 45%, we get a desiredReplicas of ceil(4 * ((45 - 39) / 1)) = 24.
              // Note that the target number is also limited by the scaleUp policy, which limits the number of indexers we can
              // add at one time, and by the maxReplicaCount.
              '%s - 39' % indexerUtilizationPercent,
          },
        },
      ],
    },
  };
  lib.flatten([
    clientCert.objects,
    centralClientCert.objects,
    serverCert.objects,
    configMap.objects,
    deployment,
    scaledObject,
    services,
    dynamicFeatureFlags.k8s_objects,
  ])

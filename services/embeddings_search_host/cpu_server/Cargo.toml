[package]
name = "embeddings_search"
version = "0.1.0"
edition = "2021"

[dependencies]
rand = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tonic = { workspace = true , features = ["tls", "gzip", "tls-roots"] }
tonic-build = { workspace = true }
async-trait = { workspace = true }
tokio = { workspace = true }
tokio-metrics-collector = { workspace = true }
tokio-stream = { workspace = true }
async-std = "*"
futures = { workspace = true }
async-rwlock = { workspace = true }
itertools = { workspace = true }
clap = { workspace = true }
secrecy = { workspace = true }
prometheus = { workspace = true }
lazy_static = { workspace = true }
uuid = { workspace = true }
tracing = { workspace = true }
prost = {workspace = true}
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
async-lock = { workspace = true }
bytes = { workspace = true }
pin-project = { workspace = true }
sha256 = { workspace = true }
sha2 = { workspace = true }
hex-literal = { workspace = true }
tonic-health = { workspace = true }
tonic-reflection = { workspace = true }
moka = { workspace = true }
chrono = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
hyper = { workspace = true }
tower = { workspace = true }
struct_logging = { path = "../../../base/logging" }
metrics-server = { path = "../../../base/metrics_server/rust" }
blob_names = { path = "../../../base/blob_names/rust" }
blob_names_rs_proto = { path = "../../../base/blob_names" }
half = { version = "2.3.1", features = ["serde", "zerocopy", "bytemuck"] }
zerocopy = { version = "0.7.32", features = ["derive", "simd"] }
rayon = "1.8.1"
dashmap = "5.5.3"
ordered-float = "4.2.0"
feature-flags = { path = "../../../base/feature_flags" }
content_manager_rs_proto = { path = "../../content_manager" }
grpc_auth = { path = "../../lib/grpc/auth" }
grpc_client = { path = "../../lib/grpc/client" }
grpc_metrics = { path = "../../lib/grpc/metrics" }
grpc_tls_config = { path = "../../lib/grpc/tls_config" }
token_exchange_client = { path = "../../token_exchange/client" }
request_context = { path = "../../lib/request_context" }
request_insight_publisher = { path = "../../request_insight/publisher" }
raw-cpuid = "11.0.1"
glob = { workspace = true }
scann_rs = { path = "../../../third_party/scann_rs" }
working_set_client = { path = "../../working_set/client" }
numpy = { path = "../../../base/rust/numpy" }
content_manager_client = { path = "../../content_manager/client" }
grpc_service = { path = "../../lib/grpc/service" }

[[bench]]
name = "dot_benchmark"
harness = false

[[bench]]
name = "one_to_many_benchmark"
harness = false

[[bench]]
name = "topk_benchmark"
harness = false

[dev-dependencies]
actix-rt =  { workspace = true }
assert_unordered =  { workspace = true }
divan = "=0.1.15"
tonic-build = { workspace = true }
prost-build = {workspace = true}
prost-wkt-build = {workspace = true}
bencher = "*"
assert2 = "0.3.13"
mockall = "0.13.1"

[build-dependencies]
tonic-build = { workspace = true }

[profile.release]
debug = false
debug-assertions = false
incremental = false
lto = false
opt-level = 3
overflow-checks = false

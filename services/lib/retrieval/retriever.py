"""Protocol for retrieval as used for sparse and dense retriever in the completion host."""

import concurrent.futures
import re
import threading
from dataclasses import dataclass, field, replace
from enum import Enum
from typing import Callable, Generic, Iterable, Optional, Protocol, Sequence, TypeVar

from base.blob_names.python.blob_names import Blobs
from base.diff_utils.edit_events import GranularEditEvent
from base.prompt_format.chunk_origin import ChunkOriginValues
from base.prompt_format.common import DocumentationMetadata
from base.prompt_format.recency_info import (
    GitDiffFileInfo,
    RecencyInfo,
    ReplacementText,
    TabSwitchEvent,
)
from base.prompt_format_completion import PromptChunk
from base.python.au_itertools import reusable_iterable
from base.ranges.range_types import CharRange
from services.completion_host import completion_pb2
from services.content_manager.client.content_manager_client import ContentKey
from services.embeddings_indexer import chunk_pb2
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


@dataclass(frozen=True)
class Exchange:
    """Class containing the user's message and the model's response."""

    request_message: str
    """The user's message"""

    response_text: str
    """The response message from the model"""

    def __repr__(self):
        return f"{self.__class__.__name__}( \
            request_message={self.request_message!r}, \
            response_text={self.response_text!r}, \
        )"


RetrievalPromptInputT = TypeVar("RetrievalPromptInputT", contravariant=True)
RetrievalPromptInputT_co = TypeVar("RetrievalPromptInputT_co", covariant=True)


@dataclass(frozen=True)
class RetrievalInput(Generic[RetrievalPromptInputT_co]):
    """Class containing all relevant input for retrieval.

    NOTE(arun): This class is populated from the completion request proto, and so uses
    default values for all its fields corresponding to those in the proto.
    """

    prompt_input: RetrievalPromptInputT_co
    """Part of the input related to the prompt formatter."""

    blobs: list[Blobs] = field(default_factory=list[Blobs])
    """The blob names in delta format.

    It supports multiple blobs, and the union of the blobs is used.
    """

    recency_info: Optional[completion_pb2.RecencyInfo] = None
    """Recent client events."""

    edit_events: Optional[list[GranularEditEvent]] = None
    """More granular events representing the most recent edits."""

    user_guided_blobs: Optional[Sequence[str]] = None
    """List of blob names provided by the user."""

    external_source_ids: Optional[Sequence[str]] = None
    """List of external source IDs provided by the user.

    At this point CHAT only, but eventually will be in other generation modes
    """

    disable_auto_external_sources: bool = False
    """Whether to disable external sources that would otherwise automatically be added.

    At this point CHAT only, but eventually will be in other generation modes
    """

    namespace: Optional[str] = None
    """The namespace of the request."""

    sequence_id: Optional[int] = None
    """The sequence ID of the request, for potential cancellation."""

    model_name: Optional[str] = None
    """The name of the model that is being used for the request."""

    # query
    def workspace_empty(self) -> bool:
        """Returns true if the workspace is empty."""
        return (
            (len(self.blobs) == 0 or all(b.is_empty() for b in self.blobs))
            and not self.external_source_ids
            # TODO: implicit docsets are not used in this case
        )

    def clone_for_user_guided_retrieval(
        self, user_guided_blobs: Sequence[str]
    ) -> "RetrievalInput":
        """Returns a clone of the RetrievalInput for use with user_guided retrieval."""
        return replace(
            self,
            blobs=[Blobs.from_blob_names(user_guided_blobs)],
            # Drop external sources for user guided retrieval, just set all of
            # the flags off to be safe
            external_source_ids=None,
            disable_auto_external_sources=True,
        )


@dataclass(frozen=True)
class RetrievalChunk:
    """Class containing the result of a retrieval."""

    text: str
    """The text of the chunk."""

    path: str
    """The path of the chunk."""

    char_start: int
    """Offset in UTF-8 characters where the region represented by this chunk starts."""

    char_end: int
    """Offset in UTF-8 characters where the region represented by this chunk ends. This is the index of the first character AFTER the region."""

    blob_name: Optional[str]
    """The blob name the chunk is from."""

    chunk_index: Optional[int]
    """The chunk index in the blob name."""

    origin: str = ""
    """The name of the retriever that originated this chunk."""

    score: float | None = None
    """The score of the chunk."""

    header: str = ""
    """The header of the chunk."""

    line_start: Optional[int] = None
    """Offset in lines where the region represented by this chunk starts."""

    length_in_lines: Optional[int] = None
    """Number of lines the chunk would occupy if pasted into an editor.
       Note: This also means that sum of length_in_lines of all previous
       chunks is not necessarily line_start.
    """

    documentation_metadata: DocumentationMetadata | None = None
    """Optional additional metadata for the chunk if the chunk is a documentation chunk."""

    @property
    def crange(self) -> CharRange:
        """The character range of the chunk."""
        return CharRange(self.char_start, self.char_end)

    @property
    def unique_id(self) -> Optional[str]:
        """A unique chunk ID, or None if not available."""
        if self.documentation_metadata:
            return f"{self.documentation_metadata.page_id}:{self.char_start}"

        if self.blob_name is None or self.chunk_index is None:
            return None
        return f"{self.blob_name}:{self.chunk_index}"

    def to_prompt_chunk(self) -> PromptChunk:
        """Returns a PromptChunk object."""
        if self.line_start is not None and self.length_in_lines is not None:
            line_start = self.line_start
            line_end = line_start + self.length_in_lines
        else:
            line_start = -1
            line_end = -1

        return PromptChunk(
            text=self.text,
            path=self.path,
            char_start=self.char_start,
            char_end=self.char_end,
            origin=self.origin,
            blob_name=self.blob_name or "",
            unique_id=self.unique_id,
            header=self.header,
            documentation_metadata=self.documentation_metadata,
            line_start=line_start,
            line_end=line_end,
        )


_CHUNK_RE = re.compile(r"chunk-(\d+).pb")


def from_chunk_pb(
    chunk_pb: chunk_pb2.Chunk,
    blob_name: str | None,
    chunk_index: int,
    origin: str,
    score: float | None = None,
) -> RetrievalChunk:
    """Converts a chunk_pb2.Chunk to a RetrievalChunk."""
    if chunk_pb.HasField("documentation_metadata"):
        metadata = DocumentationMetadata(
            name=chunk_pb.documentation_metadata.name,
            page_id=chunk_pb.documentation_metadata.page_id,
            headers=list(chunk_pb.documentation_metadata.headers),
            source_id=chunk_pb.documentation_metadata.source_id,
        )
    else:
        metadata = None
    return RetrievalChunk(
        text=chunk_pb.text,
        path=chunk_pb.path,
        char_start=chunk_pb.char_offset,
        char_end=chunk_pb.char_offset + len(chunk_pb.text),
        line_start=chunk_pb.line_offset,
        length_in_lines=chunk_pb.length_in_lines,
        blob_name=blob_name,
        chunk_index=chunk_index,
        header=chunk_pb.header,
        origin=origin,
        score=score,
        documentation_metadata=metadata,
    )


def from_recency_info_pb(
    recency_info_pb: completion_pb2.RecencyInfo,
) -> RecencyInfo:
    """Converts a completion_pb2.RecencyInfo to a RecencyInfo."""
    return RecencyInfo(
        tab_switch_events=[
            TabSwitchEvent(
                path=tab_switch_event.path,
                file_blob_name=tab_switch_event.file_blob_name,
            )
            for tab_switch_event in recency_info_pb.tab_switch_events
        ],
        git_diff_info=[
            GitDiffFileInfo(
                content_blob_name=git_diff_file_info.content_blob_name,
                file_blob_name=git_diff_file_info.file_blob_name,
            )
            for git_diff_file_info in recency_info_pb.git_diff_file_info
        ],
        recent_changes=[
            ReplacementText(
                blob_name=recent_change.blob_name,
                path=recent_change.path,
                char_start=recent_change.char_start,
                char_end=recent_change.char_end,
                replacement_text=recent_change.replacement_text,
                present_in_blob=recent_change.present_in_blob,
                expected_blob_name=recent_change.expected_blob_name,
            )
            for recent_change in recency_info_pb.recent_changes
        ],
    )


def parse_retrieval_chunk_from_bytes_builder(
    origin: str,
) -> Callable[[tuple[str, ContentKey], bytes], RetrievalChunk]:
    """Returns a function that can parse a retrieval chunk from bytes.

    Args:
        origin: The origin of the chunk. This is used to identify the retriever that
          generated the chunk.
    """
    # Requires the value to be one of the known values defined in `ChunkOrigin`.
    if origin not in ChunkOriginValues:
        raise ValueError(f"Unknown chunk origin: {origin}.")

    def parse_retrieval_chunk_from_bytes(
        key: tuple[str, ContentKey], chunk_content: bytes
    ) -> RetrievalChunk:
        """Parse a retrieval chunk from the bytes returned by the content manager."""
        # NOTE(arun): Unfortunately, this is the only way we can figure out the chunk index
        # from just the arguments to the content manager.
        if match := _CHUNK_RE.match(key[1].sub_key):
            chunk_index = int(match.group(1))
        else:
            chunk_index = 0

        chunk_pb = chunk_pb2.Chunk()
        chunk_pb.ParseFromString(chunk_content)
        return from_chunk_pb(chunk_pb, key[1].blob_name, chunk_index, origin)

    return parse_retrieval_chunk_from_bytes


class RetrievalResult:
    """Class containing the result of a retrieval.

    Threadsafe: Can be accessed from multiple threads.

    Lazy: It buffers the chunks and missing blob names given as iterables.
    Each call to `get_retrieved_chunks` and `get_missing_blob_names` returns a
    fresh iterator that will block until the retrieval makes progress.

    Important:
    If the `checkpoint_not_found` field is set to True, retrieval failed to
    resolve the given baseline_checkpoint_id. This condition will persist until
    the client either regenerates the checkpoint or retries with a different
    checkpoint_id. This field does not have a default value so that the creator
    is forced to provide one.

    TODO(aswin): passing the checkpoint_not_found bool directly breaks a design goal
    of using the RetrievalResults type as an interface to Futures, where data becomes
    available _after_ returning the RetrievalResults object.
    """

    def __init__(
        self,
        retrieved_chunks: Iterable[RetrievalChunk],
        missing_blob_names: Iterable[str],
        checkpoint_not_found: bool,
    ):
        """Initializes the result."""
        self.retrieved_chunks = reusable_iterable.ReusableIterable(retrieved_chunks)
        self._missing_blob_names_iteratable = missing_blob_names
        self._missing_blob_names_buffer = None
        self._checkpoint_not_found = checkpoint_not_found

        self._missing_blob_names_lock = threading.Lock()

    def get_retrieved_chunks(self) -> Iterable[RetrievalChunk]:
        """Returns the retrieved chunks lazily."""
        return self.retrieved_chunks.get_iterable()

    def get_missing_blob_names(self) -> Sequence[str]:
        """Returns the names of all blob names that could not be received."""
        with self._missing_blob_names_lock:
            is_first_thread = self._missing_blob_names_buffer is None
            if is_first_thread:
                missing = set()
                missing.update(self._missing_blob_names_iteratable)
                self._missing_blob_names_buffer = list(missing)
            assert self._missing_blob_names_buffer is not None
            return self._missing_blob_names_buffer

    def get_checkpoint_not_found(self) -> bool:
        """Returns True if the blobs checkpoint is not found during retrieval."""
        return self._checkpoint_not_found


@dataclass
class FindMissingResult:
    """A dataclass holding the results of a find missing request."""

    missing_blob_names: Sequence[str]
    """A list of blob names that could not be found by the embeddings search."""


class Retriever(Protocol[RetrievalPromptInputT]):
    """Protocol for a class that allows to retrieve chunks from a retrieval system."""

    def retrieve(
        self,
        input_: RetrievalInput[RetrievalPromptInputT],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
        attempt_cancel_fn: Optional[Callable[[str], None]] = None,
    ) -> RetrievalResult:
        """Returns a tuple of retrieved chunks and unknown blob names.

        Args:
            input_: The retrieval input from the user request.
            request_context: The request context used for tracking, metrics and authentication.
            auth_info: Authentication information for the request.
            executor: If present, an concurrent executor that can be used by the
                retriever for concurrency. If absent, the retriever should run
                synchronously.
            attempt_cancel_fn: If present, a callback function that can be called to
                check for cancellation. The callback takes a cancel_source string
                parameter and should raise an exception if the request has been cancelled.
        """
        raise NotImplementedError()

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> FindMissingResult:
        """Returns the subset of the given blob names that are not indexed for this retriever.

        Args:
            blob_names: A list of blob names to check.
            request_id: The current request id. Will be attached to any RPC that might
              be done.
            session_id: The current session id. Used to potentially cache
              session-specific information.
            executor: If present, an concurrent executor that can be used by the
              retriever for concurrency. If absent, the retriever should run
              synchronously.
        """
        raise NotImplementedError()

    def get_name(self) -> str:
        """Returns the class name of the retriever.

        This is used for logging purposes.
        """
        return __class__.__name__


class Category(Enum):
    """
    The category of the request

    Important - the numbers have to match the training and prompt of the router model
    For example, see `pleasehold_prompt_formatter.py`
    """

    GENERAL = 0
    EDIT = 1
    CURRENT_FILE = 2
    CODEBASE = 3
    OVERVIEW = 4


@dataclass
class RouterResponse:
    category: Category
    filenames: list[str]
    docsets: list[str]


@dataclass
class RouterRetrievalResult(RetrievalResult):
    category: Category | None

    def __init__(
        self,
        retrieved_chunks: Iterable[RetrievalChunk],
        missing_blob_names: Iterable[str],
        checkpoint_not_found: bool,
        category: Category | None,
    ):
        super().__init__(retrieved_chunks, missing_blob_names, checkpoint_not_found)
        self.category = category

"""A retriever that retrieves from user provided files."""

import concurrent.futures
import dataclasses
from typing import Callable, Optional, Sequence

from services.lib.retrieval import dummy_executor
import structlog

from base.prompt_format.chunk_origin import Chunk<PERSON>rigin
from services.lib.retrieval import (
    retriever_request_insight_builder,
)
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2

log = structlog.get_logger()


class UserGuidedRetriever(Retriever):
    """A retriever that uses recency signals."""

    def __init__(
        self,
        num_results: int,
        internal_retriever: Retriever,
        ri_builder: Optional[
            retriever_request_insight_builder.RetrieverRequestInsightBuilder
        ] = None,
    ):
        """Construct a new retriever.

        Args:
            num_results: The maximal number of chunks to return
            internal_retriever: Internal retriever to delegate chunk retrieval based on blobs
            ri_builder: Optional request insight builder
        """
        self._internal_retriever = internal_retriever
        self._num_results = num_results
        self._request_insight_builder = ri_builder

    def _retrieve_from_file_context(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        user_guided_blobs: Sequence[str],
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> RetrievalResult:
        """
        Retrieve chunks from blob names attached by the user
        """
        input_with_blobs = input_.clone_for_user_guided_retrieval(
            user_guided_blobs=user_guided_blobs
        )
        result: RetrievalResult = self._internal_retriever.retrieve(
            input_with_blobs,
            request_context,
            auth_info,
            executor,
        )

        retrieved_chunks = []
        for dense_chunk in result.get_retrieved_chunks():
            retrieved_chunks.append(
                dataclasses.replace(
                    dense_chunk, origin=ChunkOrigin.USER_GUIDED_RETRIEVER.value
                )
            )

        return RetrievalResult(
            retrieved_chunks,
            missing_blob_names=result.get_missing_blob_names(),
            checkpoint_not_found=result.get_checkpoint_not_found(),
        )

    def retrieve(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
        attempt_cancel_fn: Optional[Callable[[str], None]] = None,
    ) -> RetrievalResult:
        """Returns a tuple of retrieved chunks and unknown blob names.

        Args:
            input_: The retrieval input from the user request.
            request_context: The request context used for tracking, metrics and authentication.
            executor: If present, an concurrent executor that can be used by the
                retriever for concurrency. If absent, the retriever should run
                synchronously.
        """
        del attempt_cancel_fn
        user_guided_blobs: Sequence[str] | None = input_.user_guided_blobs

        if user_guided_blobs is None or len(user_guided_blobs) == 0:
            # No user guided blobs for retrieval, return empty result
            return RetrievalResult(
                retrieved_chunks=[], missing_blob_names=[], checkpoint_not_found=False
            )

        if executor is None:
            executor = dummy_executor.DummyExecutor()

        log.info("Retriever got user files context: %d", len(user_guided_blobs))

        result = self._retrieve_from_file_context(
            input_,
            request_context,
            auth_info,
            user_guided_blobs,
            executor,
        )

        all_retrieved_chunks = list(result.get_retrieved_chunks())

        retrieved_chunks = all_retrieved_chunks[: self._num_results]

        result = RetrievalResult(
            retrieved_chunks,
            missing_blob_names=result.get_missing_blob_names(),
            checkpoint_not_found=result.get_checkpoint_not_found(),
        )

        # We rely on the caller to wait for the executor to terminate all calls.
        if self._request_insight_builder:
            executor.submit(
                self._request_insight_builder.record_retrieval_result,
                retrieval_type=request_insight_pb2.RetrievalType.USER_GUIDED,
                query_prompt=[],
                embedder_tokenizer=None,
                retrieved_chunks=all_retrieved_chunks,
                request_context=request_context,
                auth_info=auth_info,
            )

        return result

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> FindMissingResult:
        return self._internal_retriever.find_missing(
            blob_names, request_context, executor
        )

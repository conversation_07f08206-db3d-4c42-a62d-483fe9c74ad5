from concurrent import futures
from dataclasses import dataclass
from typing import Callable, Optional, Sequence

import structlog
from dataclasses_json import dataclass_json
from typing_extensions import override

from base.prompt_format.chunk_origin import Chunk<PERSON><PERSON><PERSON>, ChunkOriginValues
from base.prompt_format_completion.overlap import modified_chunks_filter
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalInput,
    RetrievalResult,
    Retriever,
    from_recency_info_pb,
)

log = structlog.get_logger()


@dataclass_json
@dataclass
class RetrievalCollectorConfig:
    """Configuration for the retrieval collector."""

    enabled: bool
    """"Use the new retrieval collector. Temporary until we complete the migration."""

    modified_chunks_filter_enabled: bool
    """Should we filter chunks based on recency."""


class RetrievalCollector(Retriever):
    """A retriever that collects chunks from multiple retrievers and processes them."""

    def __init__(self, retriever: Retriever, config: RetrievalCollectorConfig):
        self._retriever = retriever
        self._config = config

    @override
    def retrieve(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[futures.Executor] = None,
        attempt_cancel_fn: Optional[Callable[[str], None]] = None,
    ) -> RetrievalResult:
        """Retrieve chunk from each retriever."""
        retrieval = self._retriever.retrieve(
            input_,
            request_context=request_context,
            auth_info=auth_info,
            executor=executor,
            attempt_cancel_fn=attempt_cancel_fn,
        )

        retrieved_chunks = retrieval.get_retrieved_chunks()

        if (
            self._config.modified_chunks_filter_enabled
            and input_.recency_info is not None
        ):
            log.info("Filtering chunks based on recency info.")
            retrieved_chunks = modified_chunks_filter(
                retrieved_chunks,
                from_recency_info_pb(input_.recency_info),
                origins=ChunkOriginValues,
                skip_origins=[
                    ChunkOrigin.RECENCY_RETRIEVER.value,
                    ChunkOrigin.DIFF_RETRIEVER.value,
                ],
            )

        result = RetrievalResult(
            retrieved_chunks=retrieved_chunks,
            missing_blob_names=retrieval.get_missing_blob_names(),
            checkpoint_not_found=retrieval.get_checkpoint_not_found(),
        )
        return result

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[futures.Executor] = None,
    ) -> FindMissingResult:
        return self._retriever.find_missing(
            blob_names, request_context=request_context, executor=executor
        )

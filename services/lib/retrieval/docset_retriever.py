import dataclasses
from concurrent.futures import Executor
from typing import Callable, Optional, Sequence

from services.lib.retrieval import dummy_executor
import structlog
import uuid
from typing_extensions import override

import base.tokenizers
from base.prompt_format.common import DocumentationMetadata
from base.prompt_format_retrieve import RetrieverPromptFormatter
from services.lib.retrieval import (
    retriever_request_insight_builder,
)
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
    RetrievalPromptInputT,
    from_chunk_pb,
)
from services.embedder_host.client.client import (
    EmbedderRequestType,
    EmbedderClientProtocol,
)
from services.embeddings_indexer import chunk_pb2
from services.integrations.docset.client.client import DocSetClient
from services.request_insight import request_insight_pb2
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext, clamp_timeout

log = structlog.get_logger()


class DocsetRetriever(Retriever[RetrievalPromptInputT]):
    """Embeddings-based Dense Retrieval over docsets."""

    def __init__(
        self,
        embedder_client: EmbedderClientProtocol,
        docset_client: DocSetClient,
        query_tokenizer: base.tokenizers.Tokenizer,
        origin: str,
        transformation_key: str,
        query_prompt_formatter: RetrieverPromptFormatter[RetrievalPromptInputT],
        num_results: int,
        search_timeout_ms: int,
        ri_builder: Optional[
            retriever_request_insight_builder.RetrieverRequestInsightBuilder
        ] = None,
        auto_external_source_ids: Optional[list[str]] = None,
    ):
        """Construct a new dense retriever.

        Args:
            embedder_client: A client object for the embedder service
            docset_client: A client object for the docset service
            query_tokenizer: The tokenizer to use
            origin: The identifier of the retriever.
            transformation_key: The transformation key under which the emdeddings and chunks are stored
                                in the content manager
            query_prompt_formatter: prompt formatter to use for the embeddings prompt.
            num_results: The maximal number of chunks to return
            search_timeout_ms: Timeout for the search request in milliseconds
            ri_builder: A client object for the request insight service
            auto_external_source_ids: List of external source IDs to automatically add to the request
        """
        self._embedder_client = embedder_client
        self._docset_client = docset_client
        self._query_tokenizer = query_tokenizer
        self._origin = origin
        self._transformation_key = transformation_key
        self._query_prompt_formatter = query_prompt_formatter
        self._num_results = num_results
        self._search_timeout_ms = search_timeout_ms
        self._request_insight_builder = ri_builder
        self._auto_external_source_ids = auto_external_source_ids

    def _get_prompt_tokens(self, input_: RetrievalInput) -> list[int]:
        """Calculates the tokens send to the embedder service.

        Some models do not include the file path into the embeddings prompt.

        The last token is always the end of query token.
        """
        return self._query_prompt_formatter.format_prompt(input_.prompt_input).tokens()

    def _parse_retrieval_chunk_from_bytes(
        self,
        blob_name: str | None,
        chunk_index: int,
        chunk_content: bytes,
        score: float,
    ) -> RetrievalChunk:
        """Parse a retrieval chunk from the bytes returned by the content manager."""
        chunk_pb = chunk_pb2.Chunk()
        chunk_pb.ParseFromString(chunk_content)
        return from_chunk_pb(
            chunk_pb,
            blob_name=blob_name,
            chunk_index=chunk_index,
            origin=self._origin,
            score=score,
        )

    def retrieve(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[Executor] = None,
        attempt_cancel_fn: Optional[Callable[[str], None]] = None,
    ) -> RetrievalResult:
        """Returns a list of retrieved chunks.

        It will calculate an embedding based on the path and input tokens and then will
        search embeddings that are "similar". Based on this information, it will load
        chunks from the content manager.

        Args:
            input_: The retrieval input from the user request.
            request_context: The request context used for tracking, metrics and authentication.
            executor: If present, an concurrent executor that can be used to parallelize
              requests.

        Returns:
            it returns a RetrievalResult object. The object contains the list of retrieved chunks.
            The size of all retrieved chunks does not exceed retrieval_len
            The returned object also contains all the blob names that were not received.
        """
        del attempt_cancel_fn
        if input_.workspace_empty():
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=[], checkpoint_not_found=False
            )

        if executor is None:
            executor = dummy_executor.DummyExecutor()

        # TODO: deduplicate this with the DenseRetriever's query embedding, if
        # we are in a MultiRetriever with it?
        query_tokens = self._get_prompt_tokens(input_)
        query_embedding = self._embedder_client.calculate_embedding(
            tokens=[query_tokens],
            request_type=EmbedderRequestType.QUERY,
            source_namespace=input_.namespace,
            request_context=request_context,
            timeout=clamp_timeout(request_context.time_remaining_or_raise(), 30),
        )

        if not isinstance(input_.prompt_input.message, str):
            # This is a structured message
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=[], checkpoint_not_found=False
            )

        explicit_source_ids = list(input_.external_source_ids or [])
        if not input_.disable_auto_external_sources:
            implicit_docsets = self._docset_client.get_implicit_docsets(
                message_text=input_.prompt_input.message,
                request_context=request_context,
                timeout=clamp_timeout(request_context.time_remaining_or_raise(), 30),
            )
            implicit_source_ids = [docset.doc_set_id for docset in implicit_docsets]
        else:
            implicit_source_ids = []

        # dedupe, and then sort the list to make testing more deterministic
        combined_external_source_ids = sorted(
            list(set(explicit_source_ids + implicit_source_ids))
        )

        if not combined_external_source_ids:
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=[], checkpoint_not_found=False
            )

        log.info(
            "Searching for docset chunks with implicit external source ids: %s and explicit external source ids: %s",
            implicit_source_ids,
            explicit_source_ids,
        )

        assert self._docset_client is not None
        assert combined_external_source_ids is not None
        result = self._docset_client.search(
            doc_set_ids=combined_external_source_ids,
            query=query_embedding,
            num_results=self._num_results,
            transformation_key=self._transformation_key,
            request_context=request_context,
            timeout=clamp_timeout(request_context.time_remaining_or_raise(), 30),
        )

        # Ignore missing blobs or checkpoints in the response
        chunks = [
            self._parse_retrieval_chunk_from_bytes(
                blob_name=None,  # docsets don't have blob names
                chunk_index=chunk.chunk_index,
                chunk_content=chunk.content,
                score=chunk.score,
            )
            for chunk in result.chunks
        ]

        # This is docset result, so the documentation_metadata field have to be not None
        # no matter what the response is.  This ensures that even if we have a problem,
        # downstream won't misrecognize it as a workspace file.
        for chunk in chunks:
            if chunk.documentation_metadata is None:
                log.warn(
                    "Docset search returned Chunks without documentation_metadata.  Adding placeholder field."
                )
                chunk = dataclasses.replace(
                    chunk,
                    documentation_metadata=DocumentationMetadata(
                        name="unknown",
                        page_id=str(
                            uuid.uuid4()
                        ),  # assign an unique page_id to avoid merging with other chunks
                        headers=[],
                        source_id="",
                    ),
                )

        # We rely on the caller to wait for the executor to terminate all calls.
        if self._request_insight_builder:
            executor.submit(
                self._request_insight_builder.record_retrieval_result,
                request_insight_pb2.RetrievalType.DENSE,
                query_tokens,
                self._query_tokenizer,
                chunks,
                request_context,
                auth_info=auth_info,
            )

        return RetrievalResult(
            retrieved_chunks=chunks,
            missing_blob_names=(),
            checkpoint_not_found=False,
        )

    @override
    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[Executor] = None,
    ) -> FindMissingResult:
        return FindMissingResult(missing_blob_names=[])

    @override
    def get_name(self) -> str:
        return f"DocsetRetriever({self._transformation_key})"

"""Test for the multi retriever module."""

import concurrent.futures
import sys
import time
import uuid
from typing import Callable, Optional
from unittest import mock

import grpc
import pytest

from base.blob_names.python.blob_names import Blobs
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from services.lib.retrieval.multi_retriever import MultiRetriever
from services.lib.retrieval.null_retriever import NullRetriever
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


def gen_blob_name(s: str) -> str:
    """Convert a string to a hex-string blob name."""
    return s.encode("utf-8").hex()


def make_chunk(i: int, score: float = 1.0) -> RetrievalChunk:
    return RetrievalChunk(
        text=f"chunk-{i}",
        path="foo.py",
        char_start=0,
        char_end=len(f"chunk-{i}"),
        score=score,
        blob_name=None,
        chunk_index=None,
        origin=f"retriever-{i}",
    )


def mock_retriever(i: int, scores: list[float] | None = None):
    def retrieve(
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
        attempt_cancel_fn: Optional[Callable[[str], None]] = None,
    ):
        if attempt_cancel_fn:
            attempt_cancel_fn("test_retriever")

        # Simulate out of ordering.
        time.sleep((1 + i % 3) * sys.getswitchinterval())
        chunks = [make_chunk(i)]
        if scores is not None:
            chunks = [make_chunk(i, score) for score in scores]
        return RetrievalResult(
            retrieved_chunks=chunks,
            missing_blob_names=[f"missing-{i}"],
            checkpoint_not_found=False,
        )

    retriever = mock.MagicMock(NullRetriever)
    retriever.retrieve.side_effect = retrieve
    return retriever


@pytest.mark.parametrize("with_executor", [False, True])
@pytest.mark.parametrize("num_retrievers", [1, 2, 3])
def test_multi_retriever(num_retrievers: int, with_executor: bool):
    """Default test for the signature retrieval system."""

    retrievers = [mock_retriever(i) for i in range(num_retrievers)]
    multi_retriever = MultiRetriever(retrievers)

    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            path="foo/foo.py",
            prefix="Hi AI!\n",
            suffix="Bye AI!",
        ),
        blobs=[Blobs.from_blob_names([gen_blob_name("blob1")])],
    )
    request_context = RequestContext.create()
    auth_info = AuthInfo.create_for_test()
    executor = (
        concurrent.futures.ThreadPoolExecutor(
            max_workers=2,
            thread_name_prefix="multiretriever_test",
        )
        if with_executor
        else None
    )
    result = multi_retriever.retrieve(
        input_=input_,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    assert list(result.get_retrieved_chunks()) == [
        make_chunk(i) for i in range(num_retrievers)
    ]
    assert set(result.get_missing_blob_names()) == {
        f"missing-{i}" for i in range(num_retrievers)
    }
    for retriever in retrievers:
        retriever.retrieve.assert_called()

        if num_retrievers > 1:
            assert (
                retriever.retrieve.call_args.kwargs.get("executor") is not None
            ), "Nested retrievers should have executors."


# Number of retrievers to test, along with the expected results for each test. The expected
# results are the union of the relevant elements of the missing_blob_names map below.
@pytest.mark.parametrize(
    "num_retrievers,expected",
    [
        (1, ["blob-1", "blob-2"]),
        (2, ["blob-1", "blob-2", "blob-3", "blob-4"]),
        (3, ["blob-1", "blob-2", "blob-3", "blob-4", "blob-6"]),
    ],
)
def test_find_missing(num_retrievers: int, expected: list[str]):
    """Test find_missing behavior with multiple retrievers."""

    all_blob_names = ["blob-1", "blob-2", "blob-3", "blob-4", "blob-5", "blob-6"]

    # map of retriever-id to blobs missing for that retriever
    missing_blob_names = {
        0: ["blob-1", "blob-2"],
        1: ["blob-2", "blob-3", "blob-4"],
        2: ["blob-2", "blob-4", "blob-6"],
    }

    retrievers = [mock_retriever(i) for i in range(num_retrievers)]
    multi_retriever = MultiRetriever(retrievers)
    for idx, retriever in enumerate(retrievers):
        retriever.find_missing.return_value = FindMissingResult(
            missing_blob_names=missing_blob_names[idx],
        )

    request_id = uuid.uuid4()
    session_id = uuid.uuid4()
    request_context = RequestContext(
        request_id=str(request_id),
        request_session_id=str(session_id),
        request_source="unknown",
    )
    result = multi_retriever.find_missing(
        blob_names=all_blob_names, request_context=request_context
    )

    assert set(result.missing_blob_names) == set(expected)
    for retriever in retrievers:
        retriever.find_missing.assert_called()


def test_merge_sort_results():
    """Test that results across retrievers are sorted by score."""
    retrievers = [
        # Each individual retriever returns sorted results
        mock_retriever(0, [8, 6, 1]),
        mock_retriever(1, [7, 5, 2]),
        mock_retriever(2, [9, 4, 3]),
        mock_retriever(3),
    ]
    multi_retriever = MultiRetriever(retrievers)

    test_blob_names = ["blob1", "missing"]
    test_blob_names_hex = [gen_blob_name(x) for x in test_blob_names]

    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            path="foo/foo.py",
            prefix="Hi AI!\n",
            suffix="Bye AI!",
        ),
        blobs=[Blobs.from_blob_names(test_blob_names_hex)],
    )
    request_context = RequestContext.create()
    auth_info = AuthInfo.create_for_test()
    executor = concurrent.futures.ThreadPoolExecutor(
        max_workers=2,
        thread_name_prefix="multiretriever_test",
    )
    result = multi_retriever.retrieve(
        input_=input_,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    # The final results should be sorted overall
    assert list(result.get_retrieved_chunks()) == [
        make_chunk(2, 9),
        make_chunk(0, 8),
        make_chunk(1, 7),
        make_chunk(0, 6),
        make_chunk(1, 5),
        make_chunk(2, 4),
        make_chunk(2, 3),
        make_chunk(1, 2),
        make_chunk(0, 1),
        make_chunk(3),
    ]
    for retriever in retrievers:
        retriever.retrieve.assert_called()


def test_multi_retriever_cancellation():
    """Test that cancellation works when called during retrieval execution."""
    retrievers = [mock_retriever(i) for i in range(3)]
    multi_retriever = MultiRetriever(retrievers)

    # Create a cancel function that raises an exception
    def cancel_fn(cancel_source: str):
        raise Exception(
            "CANCELLED", f"Request cancelled during execution from {cancel_source}"
        )

    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            path="foo/foo.py",
            prefix="Hi AI!\n",
            suffix="Bye AI!",
        ),
        blobs=[Blobs.from_blob_names([gen_blob_name("blob1")])],
    )
    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )

    # The retrieval should raise an exception due to cancellation
    with pytest.raises(Exception) as exc_info:
        multi_retriever.retrieve(
            input_=input_,
            request_context=request_context,
            auth_info=auth_info,
            attempt_cancel_fn=cancel_fn,
        )

    assert "CANCELLED" in str(exc_info.value)

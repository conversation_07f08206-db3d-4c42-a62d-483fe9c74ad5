"""Module implementing a null retriever."""

import concurrent.futures
from typing import Any, Callable, Optional, Sequence

from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


class NullRetriever(Retriever[Any]):
    """The NullRetriever will never produce chunks."""

    def retrieve(
        self,
        input_: RetrievalInput[Any],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
        attempt_cancel_fn: Optional[Callable[[str], None]] = None,
    ) -> RetrievalResult:
        """Returns the list retrieved chunks.

        In this case, we do not retrieve any chunks.
        """
        del input_, request_context, auth_info, executor, attempt_cancel_fn
        return RetrievalResult(
            retrieved_chunks=(), missing_blob_names=[], checkpoint_not_found=False
        )

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> FindMissingResult:
        """Returns the subset of the given blob names that are not indexed for this retriever.

        Always returns the empty list.
        """
        del blob_names, request_context, executor
        return FindMissingResult(missing_blob_names=[])

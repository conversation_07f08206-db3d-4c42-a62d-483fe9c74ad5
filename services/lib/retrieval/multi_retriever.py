"""Module implementing a multi retriever."""

from concurrent import futures
from functools import partial
from heapq import merge
import itertools
from typing import Callable, Iterable, Iterator, Optional, Sequence, TypeVar

from typing_extensions import override
import structlog
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval import dummy_executor
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)

logger = structlog.get_logger()


def merge_sort_chunks_by_score(
    chunk_iterables: Iterable[Iterator[RetrievalChunk]],
) -> Iterator[RetrievalChunk]:
    """Merge sort multiple iterators of chunks by their scores.

    Each input iterator is expected to yield chunks in descending score order.
    Returns chunks in descending score order (highest scores first). Undefined
    ordering for any iterators that are not sorted.

    Args:
        chunk_iterables: Iterable of iterators, where each iterator yields ScoredChunks
            in descending score order

    Returns:
        Iterator yielding merged chunks in descending score order
    """

    # Merge the iterators using heapq.merge which maintains sorted order
    # heapq.merge does ascending order by default, but we want descending, so use reverse=True
    merged = merge(*chunk_iterables, key=lambda x: x.score or 0, reverse=True)

    # Yield just the chunks, which will be in descending actual score order
    for chunk in merged:
        yield chunk


class MultiRetriever(Retriever):
    """Retriever that combines the results from multiple retrievers.

    Each retriever is responsible for tagging its output in the `RetrievalChunk.origin`
    field.
    """

    def __init__(self, retrievers: Sequence[Retriever]):
        self._retrievers = list(retrievers)

    @override
    def retrieve(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[futures.Executor] = None,
        attempt_cancel_fn: Optional[Callable[[str], None]] = None,
    ) -> RetrievalResult:
        """Retrieve chunk from each retriever.

        Returns:
            The combined retrieval result:
                - Retrieved chunks from a single retriever are guaranteed to appear in
                  decreasing order of relevance, but no guarantees are provided on the
                  ordering of chunks between retrievers.
                - Missing blob names include blobs missing for any retriever.
                - Metadata includes the union of metadata for any retriever. If two
                  retrievers return metadata with the same key, no guarantees are
                  provided on which is used.
        """
        if not self._retrievers:
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=[], checkpoint_not_found=False
            )

        if executor is None:
            executor = dummy_executor.DummyExecutor()

        def do_retrieve(
            retriever: Retriever, executor: Optional[futures.Executor] = None
        ) -> RetrievalResult:
            return retriever.retrieve(
                input_=input_,
                request_context=request_context,
                auth_info=auth_info,
                executor=executor,
                attempt_cancel_fn=attempt_cancel_fn,
            )

        # TODO(arun,markus): In an ideal implementation, we would return a stream of
        # results on an as-completed basis. For this reason, we don't provide any
        # guarantees on the ordering in the contract above.
        # The implementation is complicated by the way that RetrievalResult includes
        # multiple streams for retrieved chunks, missing_blob_names and metadata.
        results = list(
            executor.map(partial(do_retrieve, executor=executor), self._retrievers)
        )

        return RetrievalResult(
            # Try to order chunks between retrievers. Note that the scores may
            # or may not be comparable though, so this could lead to weird
            # results.
            # TODO: group chunks by origin when sorting
            retrieved_chunks=merge_sort_chunks_by_score(
                (iter(result.get_retrieved_chunks()) for result in results)
            ),
            missing_blob_names=itertools.chain.from_iterable(
                result.get_missing_blob_names() for result in results
            ),
            checkpoint_not_found=any(x.get_checkpoint_not_found() for x in results),
        )

    @override
    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[futures.Executor] = None,
    ) -> FindMissingResult:
        return multi_retriever_find_missing(
            self._retrievers, blob_names, request_context, executor
        )


T = TypeVar("T")


def _first_not_none(it: Iterable[T | None]) -> T | None:
    for i in it:
        if i is not None:
            return i
    return None


def multi_retriever_find_missing(
    retrievers: Sequence[Retriever],
    blob_names: Sequence[str],
    request_context: RequestContext,
    executor: Optional[futures.Executor] = None,
) -> FindMissingResult:
    """Stateless implementation for find_missing on multiple retrievers.
    While "retrieve" only belongs to retrievers that are grouped by their return meaning,
    find_missing can be called to any group of retrievers.

    For example in next-edit we have location retriever and content retriever.
    They should not be used together for retrieval, but can be used together for find_missing.
    """
    if not retrievers:
        return FindMissingResult(missing_blob_names=[])

    if executor is None:
        executor = dummy_executor.DummyExecutor()

    def run_find_missing(
        retriever: Retriever, executor: Optional[futures.Executor] = None
    ) -> set[str]:
        result = retriever.find_missing(
            blob_names=blob_names,
            request_context=request_context,
            executor=executor,
        )
        return set(result.missing_blob_names)

    # A blob name is considered missing if it is missing for any retriever.
    result_sets = list(executor.map(run_find_missing, retrievers))
    for retriever, rst in zip(retrievers, result_sets):
        logger.info("Retriever %s misses %s blobs.", retriever.get_name(), rst)

    union = set().union(*result_sets)
    return FindMissingResult(missing_blob_names=list(union))

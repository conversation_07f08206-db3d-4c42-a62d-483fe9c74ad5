package gcsproxyclient

import (
	"context"
	"strings"

	proto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
)

type GcsProxyClient interface {
	Write(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.WriteRequest) (*proto.WriteResponse, error)
	Read(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ReadRequest) (*proto.ReadResponse, error)
	List(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ListRequest) (*proto.ListResponse, error)
	Delete(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.DeleteRequest) (*proto.DeleteResponse, error)
	Exist(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ExistRequest) (*proto.ExistResponse, error)
	WriteObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, data []byte, bucketType proto.BucketType) error
	ReadObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, bucketType proto.BucketType) ([]byte, error)
	ListObjects(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, prefix string, bucketType proto.BucketType) ([]string, error)
	DeleteObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, bucketType proto.BucketType) (bool, error)
	ObjectExist(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, bucketType proto.BucketType) (bool, error)
	Close() error
}

type GcsProxyClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	client proto.GcsProxyClient
}

func NewGcsProxyClientFromConn(conn *grpc.ClientConn) GcsProxyClient {
	client := proto.NewGcsProxyClient(conn)
	return &GcsProxyClientImpl{conn: conn, client: client}
}

func NewGcsProxyClient(endpoint string, credentials credentials.TransportCredentials) (GcsProxyClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := proto.NewGcsProxyClient(conn)
	return &GcsProxyClientImpl{conn: conn, client: client}, nil
}

func (c *GcsProxyClientImpl) Write(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.WriteRequest) (*proto.WriteResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.Write(ctx, req)
}

func (c *GcsProxyClientImpl) Read(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ReadRequest) (*proto.ReadResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.Read(ctx, req)
}

func (c *GcsProxyClientImpl) List(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ListRequest) (*proto.ListResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.List(ctx, req)
}

func (c *GcsProxyClientImpl) Delete(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.DeleteRequest) (*proto.DeleteResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.Delete(ctx, req)
}

func (c *GcsProxyClientImpl) Exist(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ExistRequest) (*proto.ExistResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.Exist(ctx, req)
}

func (c *GcsProxyClientImpl) WriteObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, data []byte, bucketType proto.BucketType) error {
	req := &proto.WriteRequest{
		TenantId:   tenantID,
		ObjectPath: objectPath,
		Data:       data,
		BucketType: bucketType,
	}
	_, err := c.Write(ctx, requestContext, req)
	return err
}

func (c *GcsProxyClientImpl) ReadObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, bucketType proto.BucketType) ([]byte, error) {
	req := &proto.ReadRequest{
		TenantId:   tenantID,
		ObjectPath: objectPath,
		BucketType: bucketType,
	}
	resp, err := c.Read(ctx, requestContext, req)
	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}

func (c *GcsProxyClientImpl) ListObjects(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, prefix string, bucketType proto.BucketType) ([]string, error) {
	req := &proto.ListRequest{
		TenantId:   tenantID,
		Prefix:     prefix,
		BucketType: bucketType,
	}
	resp, err := c.List(ctx, requestContext, req)
	if err != nil {
		return nil, err
	}
	return resp.ObjectPaths, nil
}

func (c *GcsProxyClientImpl) DeleteObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, bucketType proto.BucketType) (bool, error) {
	req := &proto.DeleteRequest{
		TenantId:   tenantID,
		ObjectPath: objectPath,
		BucketType: bucketType,
	}
	resp, err := c.Delete(ctx, requestContext, req)
	if err != nil {
		return false, err
	}
	return resp.Existed, nil
}

func (c *GcsProxyClientImpl) ObjectExist(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, bucketType proto.BucketType) (bool, error) {
	req := &proto.ExistRequest{
		TenantId:   tenantID,
		ObjectPath: objectPath,
		BucketType: bucketType,
	}
	resp, err := c.Exist(ctx, requestContext, req)
	if err != nil {
		return false, err
	}
	return resp.Exists, nil
}

func (c *GcsProxyClientImpl) Close() error {
	return c.conn.Close()
}

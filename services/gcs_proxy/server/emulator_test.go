package server

import (
	"context"
	"testing"

	"github.com/augmentcode/augment/services/lib/grpc/auth"

	proto "github.com/augmentcode/augment/services/gcs_proxy/proto"
)

func TestGcsProxyServiceReadWritesWithEmulator(t *testing.T) {
	ctx := context.Background()

	gcsEmulator, gcsObjects := NewGcsEmulatorWithDefaultObjectsForTesting(t)
	// defers are lifo
	defer gcsEmulator.Stop()
	defer gcsObjects.gcsClient.Close()

	service := NewTestService(
		ctx,
		gcsObjects,
		NewDefaultMockTenantWatcherClientForTesting(),
	)
	defer service.tenantCache.Close()

	t.Run("testReadWrites", func(t *testing.T) {
		ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
			TenantID: enterpriseTenantID,
			Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
		})

		// we haven't written anything yet
		_, err := service.Read(ctx, &proto.ReadRequest{
			TenantId:   enterpriseTenantID,
			ObjectPath: "test-object",
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err == nil {
			t.Fatalf("expected error reading nonexistent object, got nil")
		}

		_, err = service.Write(ctx, &proto.WriteRequest{
			TenantId:   enterpriseTenantID,
			ObjectPath: "test-object",
			Data:       []byte("test-data"),
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err != nil {
			t.Fatalf("error writing object: %v", err)
		}

		defer func() {
			// remove object from bucket for other tests
			err := gcsObjects.riEnterpriseBucket.Object(enterpriseTenantID + "/test-object").Delete(ctx)
			if err != nil {
				t.Fatalf("error deleting object: %v", err)
			}
		}()

		// now we should be able to read it
		resp, err := service.Read(ctx, &proto.ReadRequest{
			TenantId:   enterpriseTenantID,
			ObjectPath: "test-object",
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err != nil {
			t.Fatalf("error reading object: %v", err)
		}
		if string(resp.Data) != "test-data" {
			t.Fatalf("expected test-data, got %v", string(resp.Data))
		}
	})

	t.Run("testList", func(t *testing.T) {
		ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
			TenantID: enterpriseTenantID,
			Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
		})

		// we haven't written anything yet
		resp, err := service.List(ctx, &proto.ListRequest{
			TenantId:   enterpriseTenantID,
			Prefix:     "",
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err != nil {
			t.Fatalf("error listing objects: %v", err)
		}
		if len(resp.ObjectPaths) != 0 {
			t.Fatalf("expected empty list, got %v", resp.ObjectPaths)
		}

		_, err = service.Write(ctx, &proto.WriteRequest{
			TenantId:   enterpriseTenantID,
			ObjectPath: "test-list-object-1",
			Data:       []byte("test-data-1"),
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err != nil {
			t.Fatalf("error writing object: %v", err)
		}

		defer func() {
			err := gcsObjects.riEnterpriseBucket.Object(enterpriseTenantID + "/test-list-object-1").Delete(ctx)
			if err != nil {
				t.Fatalf("error deleting object: %v", err)
			}
		}()

		// now we should be able to list it
		resp, err = service.List(ctx, &proto.ListRequest{
			TenantId:   enterpriseTenantID,
			Prefix:     "",
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err != nil {
			t.Fatalf("error listing objects: %v", err)
		}
		if len(resp.ObjectPaths) != 1 {
			t.Fatalf("expected list with 1 item, got %v", resp.ObjectPaths)
		}
		if resp.ObjectPaths[0] != "test-list-object-1" {
			t.Fatalf("expected test-list-object-1, got %v", resp.ObjectPaths[0])
		}

		// add another object
		_, err = service.Write(ctx, &proto.WriteRequest{
			TenantId:   enterpriseTenantID,
			ObjectPath: "test-list-object-2",
			Data:       []byte("test-data-2"),
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err != nil {
			t.Fatalf("error writing object: %v", err)
		}

		defer func() {
			err := gcsObjects.riEnterpriseBucket.Object(enterpriseTenantID + "/test-list-object-2").Delete(ctx)
			if err != nil {
				t.Fatalf("error deleting object: %v", err)
			}
		}()

		// now we should be able to list both objects
		resp, err = service.List(ctx, &proto.ListRequest{
			TenantId:   enterpriseTenantID,
			Prefix:     "",
			BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
		})
		if err != nil {
			t.Fatalf("error listing objects: %v", err)
		}
		if len(resp.ObjectPaths) != 2 {
			t.Fatalf("expected list with 2 items, got %v", resp.ObjectPaths)
		}
		if resp.ObjectPaths[0] != "test-list-object-1" {
			t.Fatalf("expected test-list-object-1, got %v", resp.ObjectPaths[0])
		}
		if resp.ObjectPaths[1] != "test-list-object-2" {
			t.Fatalf("expected test-list-object-2, got %v", resp.ObjectPaths[1])
		}
	})
}

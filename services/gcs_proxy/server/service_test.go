package server

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	proto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	token_exchange "github.com/augmentcode/augment/services/token_exchange/proto"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestGcsProxyServiceGetCorrectHandles(t *testing.T) {
	ctx := context.Background()

	service := NewTestService(
		ctx,
		NewDefaultMockGcsObjectsForTesting(),
		NewDefaultMockTenantWatcherClientForTesting(),
	)
	defer service.tenantCache.Close()

	t.Run("validateTenantGetBucketHandle", func(t *testing.T) {
		tests := []struct {
			name           string
			ctx            context.Context
			tenantID       string
			bucketType     proto.BucketType
			expectBucket   bool
			expectedBucket string
			desiredScope   token_exchange.Scope
		}{
			{
				name: "valid enterprise tenant with tenant access",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: enterpriseTenantID,
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       enterpriseTenantID,
				bucketType:     proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket:   true,
				expectedBucket: riEnterpriseBucket,
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "valid community tenant with tenant access",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: communityTenantID,
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       communityTenantID,
				bucketType:     proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket:   true,
				expectedBucket: riNonEnterpriseBucket,
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "valid enterprise tenant with namespace access",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					ShardNamespace: testNamespace,
					Scope:          []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       enterpriseTenantID,
				bucketType:     proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket:   true,
				expectedBucket: riEnterpriseBucket,
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "valid community tenant with namespace access",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					ShardNamespace: testNamespace,
					Scope:          []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       communityTenantID,
				bucketType:     proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket:   true,
				expectedBucket: riNonEnterpriseBucket,
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "valid enterprise tenant with data export tier override",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: dogfoodTenantID,
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       dogfoodTenantID,
				bucketType:     proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket:   true,
				expectedBucket: riNonEnterpriseBucket,
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "invalid tenant ",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: "enterprise-tenant",
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:     "different-tenant",
				bucketType:   proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket: false,
				desiredScope: token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "invalid namespace",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					ShardNamespace: "different-namespace",
					Scope:          []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:     "enterprise-tenant",
				bucketType:   proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket: false,
				desiredScope: token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "invalid scope",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: enterpriseTenantID,
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:     enterpriseTenantID,
				bucketType:   proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket: false,
				desiredScope: token_exchange.Scope_REQUEST_RESTRICTED_RW,
			},
			{
				name: "no scope",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: enterpriseTenantID,
					Scope:    []string{},
				}),
				tenantID:     enterpriseTenantID,
				bucketType:   proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket: false,
				desiredScope: token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "wildcard tenant id should work with any tenant ID",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: auth.TenantIDWildcard,
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       enterpriseTenantID,
				bucketType:     proto.BucketType_REQUEST_INSIGHT_EVENTS,
				expectBucket:   true,
				expectedBucket: riEnterpriseBucket,
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "valid tenant with other_namespace access",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					ShardNamespace: "other-namespace",
					Scope:          []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       "migrated-tenant",
				bucketType:     proto.BucketType_UNKNOWN,
				expectBucket:   true,
				expectedBucket: riEnterpriseBucket,
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "valid community tenant with blob exporter bucket",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: communityTenantID,
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:       communityTenantID,
				bucketType:     proto.BucketType_NON_ENTERPRISE_BLOBS,
				expectBucket:   true,
				expectedBucket: "blob-exporter-bucket",
				desiredScope:   token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
			{
				name: "enterprise tenant denied blob exporter bucket access",
				ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
					TenantID: enterpriseTenantID,
					Scope:    []string{"REQUEST_RESTRICTED_R"},
				}),
				tenantID:     enterpriseTenantID,
				bucketType:   proto.BucketType_NON_ENTERPRISE_BLOBS,
				expectBucket: false,
				desiredScope: token_exchange.Scope_REQUEST_RESTRICTED_R,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				bucket, _ := service.validateTenantGetBucketHandle(tt.ctx, tt.tenantID, tt.desiredScope, tt.bucketType)
				if tt.expectBucket {
					assert.Equal(t, tt.expectedBucket, bucket.BucketName())
				} else {
					assert.Nil(t, bucket)
				}
			})
		}
	})
}

func TestGcsProxyServiceMakeKey(t *testing.T) {
	service := GcsProxyService{}
	key := service.makeKey("tenant-id", "object-path")
	assert.Equal(t, "tenant-id/object-path", key)
}

func TestGcsProxyServicePathValidation(t *testing.T) {
	ctx := context.Background()
	service := NewTestService(
		ctx,
		NewDefaultMockGcsObjectsForTesting(),
		NewDefaultMockTenantWatcherClientForTesting(),
	)
	defer service.tenantCache.Close()

	tests := []struct {
		name           string
		objectName     string
		expectedTenant string
		wantPath       string
		wantErr        bool
		wantErrCode    codes.Code
	}{
		{
			name:           "valid path",
			objectName:     "tenant123/path/to/file.txt",
			expectedTenant: "tenant123",
			wantPath:       "path/to/file.txt",
			wantErr:        false,
		},
		{
			name:           "valid path with single component after tenant",
			objectName:     "tenant123/file.txt",
			expectedTenant: "tenant123",
			wantPath:       "file.txt",
			wantErr:        false,
		},
		{
			name:           "invalid path - missing slash",
			objectName:     "tenant123",
			expectedTenant: "tenant123",
			wantErr:        true,
			wantErrCode:    codes.Internal,
		},
		{
			name:           "invalid path - wrong tenant",
			objectName:     "tenant456/file.txt",
			expectedTenant: "tenant123",
			wantErr:        true,
			wantErrCode:    codes.Internal,
		},
		{
			name:           "invalid path - empty string",
			objectName:     "",
			expectedTenant: "tenant123",
			wantErr:        true,
			wantErrCode:    codes.Internal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			relativePath, err := service.validateAndExtractObjectPath(tt.objectName, tt.expectedTenant)

			if tt.wantErr {
				assert.Error(t, err)
				st, ok := status.FromError(err)
				assert.True(t, ok)
				assert.Equal(t, tt.wantErrCode, st.Code())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantPath, relativePath)
			}
		})
	}
}

func TestGcsProxyServicePathFormat(t *testing.T) {
	ctx := context.Background()
	service := NewTestService(
		ctx,
		NewDefaultMockGcsObjectsForTesting(),
		NewDefaultMockTenantWatcherClientForTesting(),
	)
	defer service.tenantCache.Close()

	tests := []struct {
		name       string
		components []string
		objectName string
		wantErr    bool
	}{
		{
			name:       "valid components",
			components: []string{"tenant123", "path", "file.txt"},
			objectName: "tenant123/path/file.txt",
			wantErr:    false,
		},
		{
			name:       "invalid - single component",
			components: []string{"tenant123"},
			objectName: "tenant123",
			wantErr:    true,
		},
		{
			name:       "invalid - empty components",
			components: []string{},
			objectName: "",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.validatePathFormat(tt.components, tt.objectName)
			if tt.wantErr {
				assert.Error(t, err)
				st, ok := status.FromError(err)
				assert.True(t, ok)
				assert.Equal(t, codes.Internal, st.Code())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGcsProxyServiceExtractRelativePath(t *testing.T) {
	ctx := context.Background()
	service := NewTestService(
		ctx,
		NewDefaultMockGcsObjectsForTesting(),
		NewDefaultMockTenantWatcherClientForTesting(),
	)
	defer service.tenantCache.Close()

	tests := []struct {
		name           string
		pathComponents []string
		want           string
	}{
		{
			name:           "multiple components",
			pathComponents: []string{"tenant123", "path", "to", "file.txt"},
			want:           "path/to/file.txt",
		},
		{
			name:           "single component after tenant",
			pathComponents: []string{"tenant123", "file.txt"},
			want:           "file.txt",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := service.extractRelativePath(tt.pathComponents)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestGcsProxyServiceDelete(t *testing.T) {
	ctx := context.Background()

	// Use GCS emulator for proper testing
	gcsEmulator, gcsObjects := NewGcsEmulatorWithDefaultObjectsForTesting(t)
	defer gcsEmulator.Stop()
	defer gcsObjects.gcsClient.Close()

	service := NewTestService(
		ctx,
		gcsObjects,
		NewDefaultMockTenantWatcherClientForTesting(),
	)
	defer service.tenantCache.Close()

	// Create test context with proper auth
	authCtx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
		TenantID: communityTenantID,
		Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
	})

	// Write a test object that exists
	_, err := service.Write(authCtx, &proto.WriteRequest{
		TenantId:   communityTenantID,
		ObjectPath: "test-object",
		Data:       []byte("test data"),
		BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
	})
	if err != nil {
		t.Fatalf("Failed to write test object: %v", err)
	}

	tests := []struct {
		name            string
		ctx             context.Context
		tenantID        string
		objectPath      string
		expectedExisted bool
		expectError     bool
		expectedCode    codes.Code
	}{
		{
			name: "delete existing object",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: communityTenantID,
				Scope:    []string{"REQUEST_RESTRICTED_RW"},
			}),
			tenantID:        communityTenantID,
			objectPath:      "test-object",
			expectedExisted: true,
			expectError:     false,
		},
		{
			name: "delete non-existing object",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: communityTenantID,
				Scope:    []string{"REQUEST_RESTRICTED_RW"},
			}),
			tenantID:        communityTenantID,
			objectPath:      "non-existing-object",
			expectedExisted: false,
			expectError:     false,
		},
		{
			name: "unauthorized tenant",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: "unauthorized-tenant",
				Scope:    []string{"REQUEST_RESTRICTED_RW"},
			}),
			tenantID:     communityTenantID,
			objectPath:   "test-object",
			expectError:  true,
			expectedCode: codes.PermissionDenied,
		},
		{
			name: "missing scope",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: communityTenantID,
				Scope:    []string{"SOME_OTHER_SCOPE"},
			}),
			tenantID:     communityTenantID,
			objectPath:   "test-object",
			expectError:  true,
			expectedCode: codes.PermissionDenied,
		},
		{
			name: "insufficient scope - REQUEST_RESTRICTED_R denied for delete operation",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: communityTenantID,
				Scope:    []string{"REQUEST_RESTRICTED_R"},
			}),
			tenantID:     communityTenantID,
			objectPath:   "test-object",
			expectError:  true,
			expectedCode: codes.PermissionDenied,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &proto.DeleteRequest{
				TenantId:   tt.tenantID,
				ObjectPath: tt.objectPath,
				BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
			}

			resp, err := service.Delete(tt.ctx, req)

			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedCode, status.Code(err))
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedExisted, resp.Existed)

				// If object existed and was deleted, verify it's no longer accessible
				if tt.expectedExisted {
					// Try to read the object - it should fail with NotFound
					_, err := service.Read(tt.ctx, &proto.ReadRequest{
						TenantId:   tt.tenantID,
						ObjectPath: tt.objectPath,
						BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
					})
					assert.Error(t, err, "Object should not be readable after deletion")
				}
			}
		})
	}
}

func TestGcsProxyServiceExist(t *testing.T) {
	ctx := context.Background()

	// Use GCS emulator for proper testing
	gcsEmulator, gcsObjects := NewGcsEmulatorWithDefaultObjectsForTesting(t)
	defer gcsEmulator.Stop()
	defer gcsObjects.gcsClient.Close()

	service := NewTestService(
		ctx,
		gcsObjects,
		NewDefaultMockTenantWatcherClientForTesting(),
	)
	defer service.tenantCache.Close()

	// Create test context with proper auth
	authCtx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
		TenantID: communityTenantID,
		Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
	})

	// Write a test object that exists
	_, err := service.Write(authCtx, &proto.WriteRequest{
		TenantId:   communityTenantID,
		ObjectPath: "existing-object",
		Data:       []byte("test data"),
		BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
	})
	if err != nil {
		t.Fatalf("Failed to write test object: %v", err)
	}

	tests := []struct {
		name           string
		ctx            context.Context
		tenantID       string
		objectPath     string
		expectedExists bool
		expectError    bool
		expectedCode   codes.Code
	}{
		{
			name: "check existing object",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: communityTenantID,
				Scope:    []string{"REQUEST_RESTRICTED_R"},
			}),
			tenantID:       communityTenantID,
			objectPath:     "existing-object",
			expectedExists: true,
			expectError:    false,
		},
		{
			name: "check non-existing object",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: communityTenantID,
				Scope:    []string{"REQUEST_RESTRICTED_R"},
			}),
			tenantID:       communityTenantID,
			objectPath:     "non-existing-object",
			expectedExists: false,
			expectError:    false,
		},
		{
			name: "unauthorized tenant",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: "unauthorized-tenant",
				Scope:    []string{"REQUEST_RESTRICTED_R"},
			}),
			tenantID:     communityTenantID,
			objectPath:   "existing-object",
			expectError:  true,
			expectedCode: codes.PermissionDenied,
		},
		{
			name: "missing scope",
			ctx: auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{
				TenantID: communityTenantID,
				Scope:    []string{"SOME_OTHER_SCOPE"},
			}),
			tenantID:     communityTenantID,
			objectPath:   "existing-object",
			expectError:  true,
			expectedCode: codes.PermissionDenied,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &proto.ExistRequest{
				TenantId:   tt.tenantID,
				ObjectPath: tt.objectPath,
				BucketType: proto.BucketType_REQUEST_INSIGHT_EVENTS,
			}

			resp, err := service.Exist(tt.ctx, req)

			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedCode, status.Code(err))
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, tt.expectedExists, resp.Exists)
			}
		})
	}
}

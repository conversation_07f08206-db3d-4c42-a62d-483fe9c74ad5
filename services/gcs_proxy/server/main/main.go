package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	_ "net/http/pprof"

	proto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	server "github.com/augmentcode/augment/services/gcs_proxy/server"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
)

var configFile = flag.String("config", "", "Path to config file")

type Config struct {
	// the port the grpc server will listen on
	Port int

	// TLS configs
	ServerMtls        *tlsconfig.ServerConfig
	CentralClientMtls *tlsconfig.ClientConfig

	// Feature flag configs -- we will need this eventually, can plumb through later
	FeatureFlagsSdkKeyPath      string
	DynamicFeatureFlagsEndpoint string

	TokenExchangeEndpoint string
	TenantWatcherEndpoint string

	Namespace                 string
	ProjectId                 string
	RIEnterpriseBucketName    string
	RINonEnterpriseBucketName string
	BlobExporterBucketName    string
	PromPort                  int

	// Retry configurations, per bucket operation.
	MaxAttempts       int
	InitialBackoffMs  int
	MaxBackoffMs      int
	BackoffMultiplier float64

	// CMK support configuration
	KMSServiceAccountEmail string
	UseMockKMS             bool
}

// Load configuration from the given file.
func loadConfig(configFile string) *Config {
	var config Config
	if configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}

	log.Info().Msgf("Config: %v", config)
	return &config
}

func setupServer(ctx context.Context, config *Config,
	srvMetrics *grpcprom.ServerMetrics, serverTls credentials.TransportCredentials,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantWatcherClient tenantwatcherclient.TenantWatcherClient,
) (*grpc.Server, func(), error) {
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))
	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	// Add message size options
	maxMsgSize := 10 * 1024 * 1024 // 8MB
	opts = append(opts,
		grpc.MaxRecvMsgSize(maxMsgSize),
		grpc.MaxSendMsgSize(maxMsgSize),
	)

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	gcsProxyConfig := server.GcsProxyConfig{
		RIEnterpriseBucket:     config.RIEnterpriseBucketName,
		RINonEnterpriseBucket:  config.RINonEnterpriseBucketName,
		BlobExporterBucket:     config.BlobExporterBucketName,
		ProjectId:              config.ProjectId,
		MaxAttempts:            config.MaxAttempts,
		InitialBackoffMs:       config.InitialBackoffMs,
		MaxBackoffMs:           config.MaxBackoffMs,
		BackoffMultiplier:      config.BackoffMultiplier,
		KMSServiceAccountEmail: config.KMSServiceAccountEmail,
		UseMockKMS:             config.UseMockKMS,
	}
	gcsProxyServer, err := server.New(ctx, gcsProxyConfig, tenantWatcherClient)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create bigtable proxy server: %w", err)
	}

	proto.RegisterGcsProxyServer(grpcServer, gcsProxyServer)

	done := func() {
		// no need to shut down the grpc server here since that's done in main
		log.Info().Msg("Shutting down GCS Proxy service")
		gcsProxyServer.Close()
	}
	return grpcServer, done, nil
}

func main() {
	logging.SetupServerLogging()
	log.Info().Msg("Starting new GCS proxy")
	ctx := context.Background()

	// Parse flags.
	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	// Load config.
	config := loadConfig(*configFile)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	wg := sync.WaitGroup{}

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()
	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating central client credentials")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(
		config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds),
	)

	grpcServer, done, err := setupServer(ctx, config, srvMetrics, serverTls, tokenExchangeClient, tenantWatcherClient)
	if err != nil {
		log.Fatal().Err(err).Msg("Error setting up server")
	}

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	grpcServer.GracefulStop()
	done()
	wg.Wait()
	log.Info().Msg("Server stopped")
}

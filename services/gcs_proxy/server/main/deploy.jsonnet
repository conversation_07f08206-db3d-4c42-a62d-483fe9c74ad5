local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local cmkLib = import 'deploy/common/cmk_lib.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local exportLib = import 'services/request_insight/blob_exporter/deploy_lib.jsonnet';
local bucketLib = import 'services/request_insight/support_database/event_buckets/bucket_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  // don't want to tie the app name to request insight; maybe we move this later
  local appName = 'gcs-proxy';
  local projectId = cloudInfo[cloud].projectId;

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local isCentralService = env != 'DEV' && cloudInfo.isCentralNamespace(env, namespace, cloud);
  local serverCert = if isCentralService
  then
    certLib.createCentralServerCert(
      name='%s-central-server-certificate' % appName,
      namespace=namespace,
      env=env,
      appName=appName,
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    )
  else
    certLib.createServerCert(
      name='%s-server-certificate' % appName,
      namespace=namespace,
      appName=appName,
      dnsNames=grpcLib.grpcServiceNames(appName),
    );
  // Always need central client cert for token exchange, tenant watcher, etc
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-certificate' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  // Need namespace client cert for health checks on non-central services
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % appName,
    namespace=namespace,
    appName=appName,
  );

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=appName
  );

  // Service account and impersonation permission for accessing KMS for CMK support
  local cmkServiceAccountName = cmkLib.getCmkServiceAccountName(env);
  local kmsServiceAccountImpersonationPermission = gcpLib.grantServiceAccountImpersonation(
    app=appName,
    env=env,
    cloud=cloud,
    namespace=namespace,
    impersonatorServiceAccountName=serviceAccount.iamServiceAccountName,
    impersonatorServiceAccountEmail=serviceAccount.serviceAccountGcpEmailAddress,
    targetServiceAccountName=cmkServiceAccountName,
    targetNamespace=namespace,
  );

  // note(nikita) -- I like the idea of prod gcs proxy having full access to various buckets within one service
  // I don't think gcs proxy should carry much state, including enterprise status
  // we can see how this evolves
  local RIEnterpriseBucketName = bucketLib.bucketName(cloud, env, namespace, true);
  local RINonEnterpriseBucketName = bucketLib.bucketName(cloud, env, namespace, false);
  local BlobExporterBucketName = exportLib.centralBucketName(namespace, env);

  local config = {
    serverMtls: if mtls then serverCert.config else null,
    centralClientMtls: if mtls then centralClientCert.config else null,
    port: 50051,
    RIEnterpriseBucketName: RIEnterpriseBucketName,
    RINonEnterpriseBucketName: RINonEnterpriseBucketName,
    BlobExporterBucketName: BlobExporterBucketName,
    projectId: projectId,
    promPort: 9090,
    tokenExchangeEndpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tenantWatcherEndpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    // Retry configurations, per bucket operation.
    maxAttempts: 10,
    initialBackoffMs: 10,
    maxBackoffMs: 10000,
    backoffMultiplier: 2,
    KMSServiceAccountEmail: cmkLib.getCmkServiceAccountEmail(env, cloud),
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // Give write access to the buckets.
  local RIEnterpriseBucketAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-%s-policy' % [appName, RIEnterpriseBucketName],
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'StorageBucket',
        external: RIEnterpriseBucketName,
      },
      bindings: [
        {
          role: 'roles/storage.objectUser',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ],
        },
      ],
    },
  };
  local RINonEnterpriseBucketAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-%s-policy' % [appName, RINonEnterpriseBucketName],
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'StorageBucket',
        external: RINonEnterpriseBucketName,
      },
      bindings: [
        {
          role: 'roles/storage.objectUser',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ],
        },
      ],
    },
  };

  // Give access to the central blob exporter bucket
  local blobExporterBucketAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-central-bucket-policy' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'StorageBucket',
        external: exportLib.centralBucketName(namespace, env),
      },
      bindings: [
        {
          role: 'roles/storage.objectUser',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ],
        },
      ],
    },
  };

  local services = grpcLib.grpcService(appName=appName, namespace=namespace);
  local container = {
    name: appName,
    target: {
      name: '//services/gcs_proxy/server/main:image',
      dst: 'gcs_proxy',
    },
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: [
      {
        name: 'POD_NAME',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.name',
          },
        },
      },
      {
        name: 'POD_NAMESPACE',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.namespace',
          },
        },
      },
    ] + telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    volumeMounts: [
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 1.5,
        memory: '1Gi',
      },
    },
    args: [
      '--config',
      configMap.filename,
    ],
    readinessProbe: grpcLib.grpcHealthCheck(
      serviceName=appName + '-svc',
      tls=mtls,
      serverCerts=serverCert.volumeMountDef.mountPath,
      clientCerts=if isCentralService
      then centralClientCert.volumeMountDef.mountPath
      else clientCert.volumeMountDef.mountPath
    ) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(
      serviceName=appName + '-svc',
      tls=mtls,
      serverCerts=serverCert.volumeMountDef.mountPath,
      clientCerts=if isCentralService
      then centralClientCert.volumeMountDef.mountPath
      else clientCert.volumeMountDef.mountPath
    ) + {
      periodSeconds: 30,
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
    ],
  };

  // should be completely stateless so able to run on any node
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: 0,
      replicas: if env == 'PROD' then 4 else 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    kmsServiceAccountImpersonationPermission,
    serverCert.objects,
    clientCert.objects,
    centralClientCert.objects,
    deployment,
    RIEnterpriseBucketAccess,
    RINonEnterpriseBucketAccess,
    blobExporterBucketAccess,
    services,
  ])

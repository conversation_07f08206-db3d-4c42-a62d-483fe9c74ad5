import { z } from "zod";

// Corresponds to GetUserFeatureStatsResponse_UserFeatureStats message
// Based on services/request_insight/analytics/request_insight_analytics.proto
export const UserFeatureStatSchema = z.object({
  userEmail: z.string(),

  // Activity metrics
  totalActiveDays: z.number().int().nonnegative(),
  completionDays: z.number().int().nonnegative(),
  chatDays: z.number().int().nonnegative(),
  agentDays: z.number().int().nonnegative(),

  // Completion metrics (in time period)
  totalCompletionsInTimePeriod: z.number().int().nonnegative(),
  acceptedCompletionsInTimePeriod: z.number().int().nonnegative(),
  acceptanceRatePercentage: z.number().nonnegative(),

  // Additional chat metrics (in time period)
  totalChatMessagesInTimePeriod: z.number().int().nonnegative(),
  avgMessagesPerChatDay: z.number().nonnegative(),

  // Agent metrics
  agentChatTotal: z.number().int().nonnegative(),
  totalAgentChatMessagesInTimePeriod: z.number().int().nonnegative(),
  avgAgentChatsPerDay: z.number().nonnegative(),
});

export const UserFeatureStatsSchema = z.array(UserFeatureStatSchema);

export type UserFeatureStat = z.infer<typeof UserFeatureStatSchema>;
export type UserFeatureStats = z.infer<typeof UserFeatureStatsSchema>;

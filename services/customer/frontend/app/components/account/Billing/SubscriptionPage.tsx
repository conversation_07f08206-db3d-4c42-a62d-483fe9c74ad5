import { Box, Flex, Text, But<PERSON>, Card, Heading, Link } from "@radix-ui/themes";
import { StarFilledIcon } from "@radix-ui/react-icons";
import { formatDistance } from "date-fns";
import PlanFeature from "app/components/account/PlanFeature";
import { PlanPickerDialog } from "./PlanPickerDialog";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  putUserOnPlan,
  userQueryOptions,
  unschedulePlanChange,
  paymentQueryOptions,
} from "app/client-cache";
import type { PlanOptionSchema, PlanFeatureSchema } from "app/schemas/plan";
import { pc } from "app/utils/plural";
import type { OrbCustomerInfoSchema } from "app/schemas/orb";
import { type MouseEvent, useState } from "react";
import { CancelSubscriptionModal } from "./CancelSubscriptionModal";
import { UndoCancellationButton } from "./UndoCancellationButton";
import {
  SuspensionType,
  type SuspensionSchema,
  type SuspensionTypeSchema,
} from "app/schemas/suspensions";
import { assertUnreachable } from "@augment-internal/ts-utils/type";
import { AugmentPlanType } from "app/schemas/augment-plan";
import { CreditsCard } from "./CreditsCard";
import { BillingCard } from "./BillingCard";

export type SubscriptionPageProps = {
  subscriptionData: OrbCustomerInfoSchema;
  trialEndsInDays: string | null;
  isSettingUpPayment: boolean;
  handleSetupPayment: () => void;
  handleClearPaymentMethod: () => void;
  planFacts: PlanFeatureSchema[] | undefined;
  plans: PlanOptionSchema[];
  suspensions: SuspensionSchema[];
  scheduledPlanId: string | null;
};

function getReasonText(reason: SuspensionTypeSchema) {
  switch (reason) {
    case SuspensionType.ApiAbuse:
      return "Your account has been suspended due to content or usage that violates our terms of use. Please contact support for more information.";
    case SuspensionType.FreeTrialAbuse:
      return "Your access has been restricted. To continue, you may upgrade to a paid subscription.";
    case SuspensionType.CommunityAbuse:
      return "Your account was suspended due to too many community accounts. Please contact support for more information.";
    default:
      assertUnreachable(reason);
  }
}

function SuspensionNotification({ reason }: { reason: SuspensionTypeSchema }) {
  const reasonText = getReasonText(reason);

  return (
    <Card
      className="bg-gradient-to-r from-red-500/90 to-orange-500"
      style={{
        marginBottom: "24px",
      }}
    >
      <Flex align="center" justify="between" gap="4">
        <Flex align="center" gap="2">
          <StarFilledIcon color="orange" width="17" height="17" />
          <Text>{reasonText}</Text>
        </Flex>
      </Flex>
    </Card>
  );
}

export function SubscriptionPage({
  subscriptionData,
  trialEndsInDays,
  isSettingUpPayment,
  handleSetupPayment,
  handleClearPaymentMethod,
  planFacts,
  plans,
  suspensions,
  scheduledPlanId,
}: SubscriptionPageProps) {
  const [collapsePlanFeatures, setCollapsePlanFeatures] = useState(true);
  const [isPlanPickerOpen, setIsPlanPickerOpen] = useState(false);
  const userIsAdmin = !!useQuery(userQueryOptions).data?.isAdmin;
  // Use the shared mutation from client-cache
  const planMutation = useMutation(putUserOnPlan);
  const unschedulePlanChangeMutation = useMutation(unschedulePlanChange);
  const paymentQuery = useQuery(paymentQueryOptions);

  // Find the scheduled plan when needed
  const scheduledPlanChange = scheduledPlanId
    ? (plans.find((plan) => plan.id === scheduledPlanId) ?? null)
    : null;

  const suspensionReasons = new Set(suspensions.map((s) => s.suspensionType));
  if (suspensionReasons.has(SuspensionType.FreeTrialAbuse)) {
    plans = plans.filter(
      (p) => p.augmentPlanType !== AugmentPlanType.Community,
    );
  }

  return (
    <Box style={{ width: "100%" }}>
      <style scoped>
        {`
            :scope {
              .delete-account-button {
                background-color: var(--ds-color-error-3);
                color: var(--ds-color-error-11);
                border: 1px solid var(--ds-color-error-6);
                transition: all 0.2s ease;
              }
              .delete-account-button:hover {
                background-color: var(--ds-color-error-4);
                border: 1px solid var(--ds-color-error-7);
              }
              .subtle-text {
                color: var(--ds-color-neutral-11);
                font-size: var(--ds-font-size-2);
              }
              .pending-credits {
                color: var(--ds-color-neutral-11);
                font-size: var(--ds-font-size-2);
                margin-left: var(--ds-spacing-3);
                font-style: italic;
              }
              .change-subscription-card {
                margin-bottom: 32px;
              }
              .change-subscription-card--scheduled {
                background-color: var(--ds-color-warning-2);
                border: 1px solid var(--ds-color-warning-6);
              }
              .warning-text {
                color: var(--ds-text-warning);
              }
              .warning-button {
                background-color: var(--ds-color-warning-4);
                color: var(--ds-color-warning-11);
                border: 1px solid var(--ds-color-warning-6);
              }
              .warning-button:hover {
                background-color: var(--ds-color-warning-5);
              }
              .unschedule-button {
                background-color: var(--ds-color-neutral-2);
                color: var(--ds-color-neutral-11);
                border: 1px solid var(--ds-color-neutral-6);
              }
              .unschedule-button:hover {
                background-color: var(--ds-color-neutral-3);
              }
            }
          `}
      </style>

      {/* Trial ending notification */}
      {subscriptionData.trialPeriodEnd && (
        <Card
          className="bg-gradient-to-r from-amber-500/90 to-orange-500"
          style={{
            marginBottom: "24px",
          }}
        >
          <Flex align="center" justify="between" gap="4">
            <Flex align="center" gap="2">
              <StarFilledIcon color="orange" width="17" height="17" />
              <Text>Your trial ends in {trialEndsInDays}</Text>
            </Flex>
            {/* Hide the button for non-admin users who are part of a self-serve team */}
            {userIsAdmin && (
              <Button
                color="orange"
                variant="solid"
                size="2"
                onClick={() => setIsPlanPickerOpen(true)}
                disabled={planMutation.isPending}
              >
                Upgrade plan
              </Button>
            )}
          </Flex>
        </Card>
      )}

      {/* Account suspension notification */}
      {Array.from(suspensionReasons).map((reason) => (
        <SuspensionNotification key={reason} reason={reason} />
      ))}

      {/* Subscription section */}
      <Heading size="8" style={{ marginBottom: "16px" }}>
        Subscription
      </Heading>
      <Text
        as="div"
        size="3"
        className="subtle-text"
        style={{ marginBottom: "24px" }}
      >
        Manage your subscription and billing details.
      </Text>

      <Flex
        gap="4"
        direction={{ initial: "column", sm: "row" }}
        style={{ marginBottom: "32px" }}
      >
        <CreditsCard setIsPlanPickerOpen={setIsPlanPickerOpen} />

        <BillingCard
          handleSetupPayment={handleSetupPayment}
          handleClearPaymentMethod={handleClearPaymentMethod}
          setIsPlanPickerOpen={setIsPlanPickerOpen}
          isSettingUpPayment={isSettingUpPayment}
        />
      </Flex>

      {/* Current plan section */}
      <Heading size="5" style={{ marginBottom: "16px" }}>
        Current plan
      </Heading>

      <Card style={{ marginBottom: "32px" }} size="3">
        <Flex direction="column" gap="3">
          <Flex align="start" gap="2">
            <Text weight="medium" size="4">
              {subscriptionData.planName}
            </Text>
            {subscriptionData.trialPeriodEnd && (
              <Box
                style={{
                  backgroundColor: "var(--ds-color-warning-4)",
                  padding: "2px 8px",
                  borderRadius: "10px",
                  fontSize: "12px",
                }}
              >
                Trial ends in {trialEndsInDays}
              </Box>
            )}
            <Flex flexGrow="1"></Flex>
            {!subscriptionData.trialPeriodEnd &&
              (subscriptionData.subscriptionEndDate ? (
                <Flex gap="2" align="center">
                  <Box
                    style={{
                      backgroundColor: "var(--ds-color-warning-4)",
                      padding: "2px 8px",
                      borderRadius: "10px",
                      fontSize: "12px",
                    }}
                  >
                    Subscription ends in{" "}
                    {formatDistance(
                      new Date(subscriptionData.subscriptionEndDate),
                      new Date(),
                      {
                        addSuffix: true,
                      },
                    )}
                  </Box>
                  {userIsAdmin && <UndoCancellationButton />}
                </Flex>
              ) : (
                userIsAdmin && <CancelSubscriptionModal />
              ))}
          </Flex>

          {/* Plan features */}
          <Flex direction="column" gap="2">
            {planFacts &&
              (collapsePlanFeatures ? planFacts.slice(0, 3) : planFacts)?.map(
                (feature) => (
                  <PlanFeature
                    key={typeof feature === "string" ? feature : feature.text}
                    feature={feature}
                  />
                ),
              )}
            {planFacts && planFacts.length > 3 && (
              <Link
                size="2"
                href="#"
                style={{ textDecoration: "none", marginLeft: "24px" }}
                onClick={(e: MouseEvent) => {
                  e.preventDefault();
                  setCollapsePlanFeatures(!collapsePlanFeatures);
                }}
              >
                {collapsePlanFeatures ? "Show more" : "Show less"}
              </Link>
            )}
          </Flex>

          <Flex align="center" gap="2" style={{ marginTop: "8px" }}>
            <Text className="subtle-text">
              ${subscriptionData.pricePerSeat}/user/mo
            </Text>
            <Text className="subtle-text">•</Text>
            <Text className="subtle-text">
              {pc(
                subscriptionData.numberOfSeatsNextBillingCycle,
                "seat",
                "seats",
              )}{" "}
              purchased
            </Text>
          </Flex>

          <Text size="2" weight="medium" style={{ marginTop: "8px" }}>
            Monthly total: ${subscriptionData.monthlyTotalCost}
          </Text>
        </Flex>
      </Card>

      {/* Change subscription section */}
      {userIsAdmin && (
        <Card
          className={`change-subscription-card${scheduledPlanChange ? " change-subscription-card--scheduled" : ""}`}
          size="3"
        >
          <Flex align="center" justify="between" gap="4">
            <Flex direction="column" gap="1">
              <Text
                weight="medium"
                className={scheduledPlanChange ? "warning-text" : undefined}
              >
                {scheduledPlanChange
                  ? "Scheduled plan change"
                  : "Change your subscription"}
              </Text>
              <Text className="subtle-text">
                {scheduledPlanChange ? (
                  <>
                    Your plan will change to{" "}
                    <Text as="span" weight="bold">
                      {scheduledPlanChange.name}
                    </Text>{" "}
                    at the end of your current billing period
                  </>
                ) : (
                  "Switch plans or contact sales about Enterprise options"
                )}
              </Text>
            </Flex>
            <Flex gap="2">
              {scheduledPlanChange && (
                <Button
                  variant="soft"
                  className="unschedule-button"
                  onClick={() => unschedulePlanChangeMutation.mutate()}
                  disabled={unschedulePlanChangeMutation.isPending}
                >
                  {unschedulePlanChangeMutation.isPending
                    ? "Unscheduling..."
                    : "Unschedule Plan Change"}
                </Button>
              )}
              <Button
                variant="soft"
                className={scheduledPlanChange ? "warning-button" : undefined}
                onClick={() => setIsPlanPickerOpen(true)}
                disabled={planMutation.isPending}
              >
                {scheduledPlanChange
                  ? "Change plan"
                  : planMutation.isPending
                    ? "Updating..."
                    : "Change plan"}
              </Button>
            </Flex>
          </Flex>
        </Card>
      )}

      {/* Plan Picker Dialog */}
      {plans && !paymentQuery.isLoading && !paymentQuery.isError && (
        <PlanPickerDialog
          isOpen={isPlanPickerOpen}
          onOpenChange={setIsPlanPickerOpen}
        />
      )}

      {/* Danger zone */}
      {/*<Heading size="5" style={{ marginBottom: "16px" }}>
          Danger zone
        </Heading>

        <Card style={{
          borderColor: "var(--ds-color-error-6)",
          backgroundColor: "var(--ds-color-error-1)"
        }}>
          <Flex direction="column" gap="3">
            <Text weight="medium" color="red">Delete account</Text>
            <Text size="2" color="gray">
              Permanently delete your account and all associated data
            </Text>
            <Flex justify="end">
              <Button
                className="delete-account-button"
                onClick={handleDeleteAccount}
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete my account"}
              </Button>
            </Flex>
          </Flex>
        </Card>*/}
    </Box>
  );
}

import Modal from "app/components/ui/Modal";
import { PlanOptionCard } from "./PlanOptionCard";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  cancelSubscription,
  plansQueryOptions,
  subscriptionQueryOptions,
} from "app/client-cache";
import { Button, Skeleton } from "@radix-ui/themes";
import { isCommunityPlanOption } from "app/schemas/plan";
import { toast } from "app/components/ui/Toast";
import { typography } from "app/utils/style";
import { Enabled } from "app/components/ui/Enabled";
import { ChangePlanConfirmationPage } from "./ChangePlanConfirmationPage";
import Wizard from "app/components/ui/Wizard";
import { useState, useEffect } from "react";

export function CancelSubscriptionModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [pageNumber, setPageNumber] = useState(0);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const cancelSubscriptionMutation = useMutation(cancelSubscription);
  const {
    data: plans,
    isLoading: isPlansLoading,
    isError: isPlansError,
  } = useQuery(plansQueryOptions);
  const {
    data: subscriptionData,
    isLoading: isSubscriptionLoading,
    isError: isSubscriptionError,
  } = useQuery(subscriptionQueryOptions);

  const clearState = () => {
    setSelectedPlanId(null);
    setPageNumber(0);
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      clearState();
    }
  }, [isOpen]);

  if (isPlansLoading || isSubscriptionLoading) {
    return (
      <Skeleton>
        <Button variant="ghost" disabled={true}>
          Cancel subscription
        </Button>
      </Skeleton>
    );
  }

  if (!plans || !subscriptionData) {
    return null;
  }

  const currentPlanId = subscriptionData?.planId;

  const communityPlan = plans.find((plan) => isCommunityPlanOption(plan));
  const isCommunityCurrentPlan = currentPlanId
    ? plans.find((plan) => plan.id === currentPlanId)?.augmentPlanType ===
      "community"
    : false;

  const shouldOfferCommunityPlan = !!(!isCommunityCurrentPlan && communityPlan);

  function handleCancelSubscription() {
    cancelSubscriptionMutation.mutate(undefined, {
      onSuccess: () => {
        toast.success({
          title: "Subscription canceled",
          description: "Your subscription has been successfully canceled.",
        });
        setIsOpen(false);
      },
      onError: () => {
        toast.error({
          title: "Failed to cancel subscription",
          description: "Please try again later.",
        });
      },
    });
  }

  return (
    <>
      <Wizard
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        pageNumber={pageNumber}
        setPageNumber={setPageNumber}
        maxWidth="550px"
        trigger={(open) => (
          <Button
            onClick={open}
            color="red"
            variant="ghost"
            disabled={
              cancelSubscriptionMutation.isPending ||
              isPlansError ||
              isSubscriptionError
            }
          >
            Cancel subscription
          </Button>
        )}
      >
        {({ close, setPage }) => (
          <>
            <Modal
              title="Cancel your subscription?"
              description={
                shouldOfferCommunityPlan
                  ? "Before you cancel, consider switching to our free Community plan to keep access to basic features."
                  : ""
              }
              footer={() => (
                <>
                  <Button variant="soft" onClick={close}>
                    Close
                  </Button>
                  <Button
                    onClick={close(handleCancelSubscription)}
                    disabled={cancelSubscriptionMutation.isPending}
                    variant="solid"
                    color="red"
                  >
                    Cancel subscription
                  </Button>
                </>
              )}
            >
              <Enabled enabled={shouldOfferCommunityPlan}>
                {communityPlan && (
                  <PlanOptionCard
                    plan={communityPlan}
                    isCurrent={false}
                    allowCardClick={false}
                    onClick={() => {
                      setSelectedPlanId(communityPlan.id);
                      setPage(1);
                    }}
                    className="community-plan-card"
                  />
                )}
              </Enabled>
              <div className="soft-notice">
                If you cancel your subscription:
                <ul>
                  <li>You will lose access to premium features</li>
                  <li>
                    Your subscription will remain active until the end of your
                    billing period
                  </li>
                  <li>
                    You won&apos;t be charged again after your current billing
                    period
                  </li>
                </ul>
              </div>
              <style scoped>{`
                :scope {
                  display: flex;
                  flex-direction: column;
                  gap: var(--ds-spacing-3);
                  ${typography.text2.regular}
                  line-height: 1.75;
                }

                .soft-notice {
                  color: var(--ds-color-text-subtle);
                  ul {
                    list-style: disc;
                    padding-left: var(--ds-spacing-5);
                  }
                }

                .community-plan-card {
                  margin-bottom: var(--ds-spacing-4);
                  margin-top: var(--ds-spacing-3);
                }
              `}</style>
            </Modal>
            {shouldOfferCommunityPlan && (
              <ChangePlanConfirmationPage
                selectedPlanId={selectedPlanId}
                close={close}
                setPage={setPage}
              />
            )}
          </>
        )}
      </Wizard>
    </>
  );
}

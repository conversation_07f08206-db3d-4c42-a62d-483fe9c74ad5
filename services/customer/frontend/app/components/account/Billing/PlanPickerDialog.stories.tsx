import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { But<PERSON> } from "@radix-ui/themes";
import { useState } from "react";
import { fn } from "@storybook/test";
import { PlanPickerDialog } from "./PlanPickerDialog";
import { getPlanDescriptionWithPricingLink } from "./utils";
import { samplePlans, customPlans } from "./storyData";
import { HttpResponse } from "msw";

// Wrapper component to control dialog state
function PlanPickerDialogWithControls(props: any) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="container">
      <Button
        size="3"
        variant="solid"
        color="blue"
        onClick={() => setIsOpen(true)}
        className="open-button"
      >
        Open Plan Picker
      </Button>
      <PlanPickerDialog {...props} isOpen={isOpen} onOpenChange={setIsOpen} />
    </div>
  );
}

// Always open version for Storybook
function AlwaysOpenPlanPickerDialog(props: any) {
  return (
    <div className="container">
      <PlanPickerDialog {...props} isOpen={true} />
    </div>
  );
}

const meta = {
  title: "Components/Account/PlanPickerDialog",
  component: PlanPickerDialog,
  parameters: {
    layout: "centered",
    msw: {
      overrides: (http) => [
        http.get("/api/plans", async () => {
          return HttpResponse.json(samplePlans);
        }),
      ],
    },
  },
  args: {
    plans: samplePlans,
    description: getPlanDescriptionWithPricingLink(),
    isOpen: true,
    onOpenChange: fn(),
  },
} satisfies Meta<typeof PlanPickerDialog>;

export default meta;
type Story = StoryObj<typeof PlanPickerDialog>;

// Interactive story with controls
export const Interactive: Story = {
  render: (args) => <PlanPickerDialogWithControls {...args} />,
};

// Main variants
export const Default: Story = {
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithCurrentPlan: Story = {
  args: {
    currentPlanId: "orb_developer_plan",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithScheduledPlanChange: Story = {
  args: {
    currentPlanId: "orb_developer_plan",
    scheduledPlanId: "orb_max_plan",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithCurrentMaxPlan: Story = {
  args: {
    currentPlanId: "orb_max_plan",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithCommunityPlan: Story = {
  args: {
    currentPlanId: "orb_community_plan",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const CustomPlans: Story = {
  args: {
    plans: customPlans,
    currentPlanId: "basic",
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

// Story with custom MSW handlers to simulate different states
export const WithNoPaymentMethod: Story = {
  args: {
    currentPlanId: "orb_community_plan",
  },
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/payment", () => {
          return HttpResponse.json({ hasPaymentMethod: false });
        }),
      ],
    },
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

export const WithLoadingError: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", () => {
          return HttpResponse.error();
        }),
      ],
    },
  },
  render: (args) => <AlwaysOpenPlanPickerDialog {...args} />,
};

import type { <PERSON>a, StoryObj } from "@storybook/react";
import { CancelSubscriptionModal } from "./CancelSubscriptionModal";
import { samplePlans } from "./storyData";
import { HttpResponse } from "msw";
import mocks from "app/mocks";

const meta = {
  title: "Components/Account/Billing/CancelSubscriptionModal",
  component: CancelSubscriptionModal,
  parameters: {
    layout: "centered",
    msw: {
      overrides: (http) => [
        http.get("/api/plans", async () => {
          return HttpResponse.json(samplePlans);
        }),
      ],
    },
  },
  args: {
    currentPlanId: "orb_developer_plan",
  },
} satisfies Meta<typeof CancelSubscriptionModal>;

export default meta;
type Story = StoryObj<typeof CancelSubscriptionModal>;

export const Default: Story = {};

export const WithoutCommunityPlan: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/plans", async () => {
          return HttpResponse.json(
            samplePlans.filter((plan) => plan.id !== "orb_community_plan"),
          );
        }),
      ],
    },
  },
};

export const AlreadyOnCommunityPlan: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", async () => {
          return HttpResponse.json({
            ...mocks.subscription.GET.Response[200],
            planId: "orb_community_plan",
          });
        }),
        http.get("/api/plans", async () => {
          return HttpResponse.json(samplePlans);
        }),
      ],
    },
  },
};

export const WithScheduledPlanChange: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", async () => {
          return HttpResponse.json({
            ...mocks.subscription.GET.Response[200],
            planId: "orb_pro_plan",
            scheduledTargetPlanId: "orb_developer_plan",
          });
        }),
        http.get("/api/plans", async () => {
          return HttpResponse.json(samplePlans);
        }),
      ],
    },
  },
};

export const WithCancelError: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.delete("/api/subscription", () => {
          return HttpResponse.json(
            { error: "Failed to cancel subscription" },
            { status: 500 },
          );
        }),
        http.get("/api/plans", async () => {
          return HttpResponse.json(samplePlans);
        }),
      ],
    },
  },
};

export const WithLoadingError: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", () => {
          return HttpResponse.error();
        }),
        http.get("/api/plans", async () => {
          return HttpResponse.json(samplePlans);
        }),
      ],
    },
  },
};

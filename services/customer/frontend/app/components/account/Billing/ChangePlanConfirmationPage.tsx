import { <PERSON><PERSON>, Checkbox, <PERSON>lex, Spinner, Text } from "@radix-ui/themes";
import { PlanOptionCard } from "./PlanOptionCard";
import {
  isCommunityPlanOption,
  isEnterprisePlanOption,
  type PlanOptionSchema,
} from "app/schemas/plan";
import { CSSTransition, fadeAnimation } from "app/components/ui/CSSTransitions";
import { type ComponentProps, useEffect, useRef, useState } from "react";
import { Callout } from "app/components/ui/Callout";
import { USAGE_UNITS } from "app/data/constants";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  createCheckoutSession,
  paymentQueryOptions,
  plansQueryOptions,
  putUserOnPlan,
  subscriptionQueryOptions,
} from "app/client-cache";
import { toast } from "app/components/ui/Toast";
import { isChangeImmediate } from "app/business-logic/plan-changes";
import Modal from "app/components/ui/Modal";
import type { CloseFnProp } from "app/components/ui/Modal/Modal";

export type ChangePlanConfirmationPageProps = {
  selectedPlanId: string | null;
  close: CloseFnProp;
  setPage: (page: number) => void;
};

/**
 * Expected to be used within a Wizard. (see PlanPickerDialog)
 */
export function ChangePlanConfirmationPage({
  selectedPlanId,
  close,
  setPage,
}: ChangePlanConfirmationPageProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [confirmationChecked, setConfirmationChecked] =
    useState<ComponentProps<typeof Checkbox>["checked"]>(false);
  const createCheckoutSessionMutation = useMutation(createCheckoutSession);

  const { data: subscriptionData } = useQuery(subscriptionQueryOptions);
  const { data: paymentData } = useQuery(paymentQueryOptions);
  const { data: plansData } = useQuery(plansQueryOptions);
  const planMutation = useMutation(putUserOnPlan);

  if (!subscriptionData || !paymentData || !plansData) {
    return (
      <Modal title="Loading" description="Loading plan data...">
        <Spinner />
      </Modal>
    );
  }

  const currentPlanId = subscriptionData.planId;
  const scheduledPlanId = subscriptionData.scheduledTargetPlanId;
  const hasPaymentMethod = paymentData.hasPaymentMethod ?? false;
  const plans = plansData ?? [];

  const selectedPlan = plans.find((plan) => plan.id === selectedPlanId);

  if (!selectedPlan) {
    throw new Error("Selected plan not found");
  }

  const handleSelectPlan = async () => {
    // Don't allow selecting the current plan
    if (selectedPlanId && !isEnterprise() && selectedPlanId !== currentPlanId) {
      setIsProcessing(true);

      try {
        // Check if the user has a payment method or if it's a Community plan
        const isCommunityPlan = isCommunityPlanOption(selectedPlan);

        if (!selectedPlan) {
          throw new Error("Selected plan not found");
        }

        // For Community plan or if user has payment method, proceed directly
        if (hasPaymentMethod || isCommunityPlan) {
          await planMutation.mutateAsync({ planId: selectedPlanId });
          setIsProcessing(false);
          return true; // shoud close
        }

        // For non-Community plans without payment method, create checkout session
        createCheckoutSessionMutation.mutate(
          { planId: selectedPlanId },
          {
            onSuccess: () => {
              // Keep isProcessing true during redirection
              // The redirect is handled in the mutation
            },
            onError: () => {
              // Only reset processing state on error
              setIsProcessing(false);
            },
          },
        );
        return false; // don't close
      } catch (error) {
        console.error("Error handling plan selection:", error);
        toast.error({
          title: "Error",
          description: "Failed to process your request. Please try again.",
        });
        setIsProcessing(false);
      }
    }
  };

  function isEnterprise() {
    const selectedPlan = plans.find((plan) => plan.id === selectedPlanId);
    return !!selectedPlan && isEnterprisePlanOption(selectedPlan);
  }

  function getButtonText() {
    if (isProcessing) {
      return "Processing...";
    } else if (selectedPlanId) {
      // Check if the selected plan is a Community plan
      return hasPaymentMethod ||
        (selectedPlan && isCommunityPlanOption(selectedPlan))
        ? "Select Plan"
        : "Proceed to Payment";
    } else {
      return "Select a Plan";
    }
  }

  function hasValidPlanPricing(
    currentPlanId: string | undefined,
    targetPlanId: string,
  ): boolean {
    try {
      isChangeImmediate(currentPlanId, targetPlanId, plans);
      return true;
    } catch (error) {
      return false;
    }
  }

  function getCheckboxText() {
    if (!selectedPlanId) return "";
    if (!selectedPlan)
      throw new Error("Error with plan selection, please contact support");

    let aiTrainingText = "";
    if (selectedPlan.hasTraining) {
      aiTrainingText = `By switching to the ${selectedPlan.name}, I agree to permit Augment to train AI models on my code and usage data.`;
    }

    // Determine if this is an upgrade (immediate) or downgrade (end of billing period)
    let isImmediate: boolean;
    let hasValidPricing = true;
    try {
      isImmediate = isChangeImmediate(currentPlanId, selectedPlanId, plans);
    } catch (error) {
      // If we can't determine upgrade status due to invalid pricing data,
      // mark pricing as invalid and disable the plan change
      hasValidPricing = false;
      isImmediate = false; // This value won't be used since hasValidPricing is false
    }

    return (
      <>
        <p>{aiTrainingText}</p>
        <p>
          {!hasValidPricing ? (
            <>
              Unable to determine plan change timing due to invalid pricing
              data. Please contact support for assistance.
            </>
          ) : isImmediate ? (
            <>
              I understand this change will take effect{" "}
              <strong>immediately</strong>, and I will lose any unused{" "}
              {USAGE_UNITS} from my monthly subscription, but any additional
              purchased {USAGE_UNITS} will remain.
            </>
          ) : (
            <>
              I understand this downgrade will take effect at the end of my
              billing period. My existing {USAGE_UNITS} and invoice amount will
              remain unchanged for the current billing period.
            </>
          )}
        </p>
      </>
    );
  }

  return (
    <Modal
      title="Confirm Plan Change"
      description="Review your plan change."
      footer={() => (
        <>
          <Button
            variant="soft"
            onClick={() => {
              setPage(0);
              setConfirmationChecked(false);
            }}
          >
            Back
          </Button>
          <Button
            size="2"
            variant="solid"
            onClick={close(handleSelectPlan)}
            disabled={
              !confirmationChecked ||
              !selectedPlanId ||
              isEnterprise() ||
              selectedPlanId === currentPlanId ||
              selectedPlanId === scheduledPlanId ||
              isProcessing ||
              (selectedPlanId
                ? !hasValidPlanPricing(currentPlanId, selectedPlanId)
                : false)
            }
            className="plan-select-button"
          >
            {getButtonText()}
          </Button>
        </>
      )}
    >
      <ChangePlanConfirmationPageContent
        selectedPlan={selectedPlan}
        confirmationChecked={confirmationChecked}
        setConfirmationChecked={setConfirmationChecked}
        checkboxText={getCheckboxText()}
      />
    </Modal>
  );
}

type ChangePlanConfirmationPageContentProps = {
  selectedPlan: PlanOptionSchema;
  confirmationChecked: ComponentProps<typeof Checkbox>["checked"];
  setConfirmationChecked: (checked: boolean) => void;
  checkboxText: React.ReactNode;
};

function ChangePlanConfirmationPageContent({
  selectedPlan,
  confirmationChecked,
  setConfirmationChecked,
  checkboxText,
}: ChangePlanConfirmationPageContentProps) {
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    setIsVisible(true);
  }, []);
  const selectedPlanRef = useRef(null);
  const checkboxRef = useRef(null);

  if (!selectedPlan) return null;
  return (
    <div className="plan-confirmation">
      <style scoped>{`
          ${fadeAnimation("selected-plan-container")}
          ${fadeAnimation("confirmation-checkbox-container")}

          :scope.plan-confirmation {
            display: flex;
            flex-direction: column;
            gap: var(--ds-spacing-4);
            min-height: 200px;
          }

          .confirmation-card {
            background: var(--ds-color-warning-4);
            border: 1px solid var(--ds-color-warning-9);
            padding: var(--ds-spacing-3);
            border-radius: var(--ds-radius-3);
          }

          .confirmation-card p {
            margin-bottom: var(--ds-spacing-1);
            line-height: 1.5;
          }

          .confirmation-card p:last-child {
            margin-bottom: 0;
          }

        `}</style>
      <CSSTransition
        in={isVisible}
        timeout={400}
        baseClassName="selected-plan-container"
        nodeRef={selectedPlanRef}
      >
        <div className="selected-plan-container" ref={selectedPlanRef}>
          <PlanOptionCard
            plan={selectedPlan}
            isCurrent={false}
            isSelected={true}
            allowCardClick={false}
            showButton={false}
            onClick={() => {}} // No-op since card is not clickable
          />
        </div>
      </CSSTransition>
      <CSSTransition
        in={isVisible}
        timeout={400}
        baseClassName="confirmation-checkbox-container"
        nodeRef={checkboxRef}
      >
        <div className="confirmation-checkbox-container" ref={checkboxRef}>
          <Callout type="warning" size="small" icon={null}>
            <Text as="label">
              <Flex gap="2">
                <Checkbox
                  checked={confirmationChecked}
                  onCheckedChange={setConfirmationChecked}
                  className="confirmation-checkbox"
                  aria-labelledby="confirmation-text"
                />
                <Text size="2" color="gray" id="confirmation-text">
                  {checkboxText}
                </Text>
              </Flex>
            </Text>
          </Callout>
        </div>
      </CSSTransition>
    </div>
  );
}

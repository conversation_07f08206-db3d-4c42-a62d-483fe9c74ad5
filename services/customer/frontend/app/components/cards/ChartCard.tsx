import { Badge, Card, Flex, Skeleton, Select } from "@radix-ui/themes";
import { ShadowInnerIcon } from "@radix-ui/react-icons";
import CardHeader from "./CardHeader";
import { TrendBadge } from "../ui/Badge";
import { TimeFrame } from "../../utils/date";

export interface ChartCardProps {
  children: React.ReactNode;
  title: string;
  granularity?: string;
  rollingAvg?: string;
  trendValue?: number | null;
  style?: React.CSSProperties;
  dateFilter?: string;
  onFilterChange?: (value: string) => void;
}

export default function ChartCard({
  children,
  title,
  granularity,
  rollingAvg,
  trendValue,
  style,
  dateFilter,
  onFilterChange,
}: ChartCardProps) {
  return (
    <Card variant="classic" style={{ height: "100%", width: "100%", ...style }}>
      <Flex gap="0" direction="column" px="2" justify="center" height="100%">
        <CardHeader title={title}>
          {trendValue && (
            <Skeleton loading={!trendValue} height="100%">
              <TrendBadge value={trendValue} />
            </Skeleton>
          )}
          {granularity && (
            <Badge size="1" color="blue">
              <ShadowInnerIcon />
              {granularity}
            </Badge>
          )}
          {rollingAvg && (
            <Badge size="1" color="gray">
              <ShadowInnerIcon />
              {rollingAvg}
            </Badge>
          )}
          {dateFilter && onFilterChange && (
            <Select.Root value={dateFilter} onValueChange={onFilterChange}>
              <Select.Trigger />
              <Select.Content>
                <Select.Item value={TimeFrame.Last7Days}>Last 7 days</Select.Item>
                <Select.Item value={TimeFrame.Last30Days}>Last 30 days</Select.Item>
                <Select.Item value={TimeFrame.Last60Days}>Last 60 days</Select.Item>
                <Select.Item value={TimeFrame.Last90Days}>Last 90 days</Select.Item>
                <Select.Item value={TimeFrame.AllTime}>All time</Select.Item>
              </Select.Content>
            </Select.Root>
          )}
        </CardHeader>
        {children}
      </Flex>
    </Card>
  );
}

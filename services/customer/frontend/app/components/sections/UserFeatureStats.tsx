import { useMemo } from "react";
import { Box, Table, Text, Flex, IconButton } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { t } from "../../utils/helpers";
import ChartCard from "../cards/ChartCard";
import { useDateFilter, TimeFrame } from "../../utils/date";
import { useAppStore } from "../../utils/app-store";
import { DownloadIcon } from "@radix-ui/react-icons";
import SectionLayout from "../global/SectionLayout";
import type { FilterOption } from "../filters/TimeFilter";
import type {
  UserFeatureStat,
  UserFeatureStats,
} from "../../schemas/userFeatureStats";
import { dateRangeQuery } from "app/client-cache/queries/dateRange";

// Helper function to format percentages
function formatPercentage(value: number): string {
  if (isNaN(value)) return "N/A";
  return `${value.toFixed(2)}%`;
}

// Helper function to convert user stats to CSV
function convertToCSV(users: UserFeatureStat[]): string {
  if (users.length === 0) return "";

  // Define headers
  const headers = [
    "User Email",
    "Active Days",
    "Completion Days",
    "Chat Days",
    "Agent Days",
    "Total Completions",
    "Accepted Completions",
    "Acceptance Rate",
    "Total Chat Messages",
    "Avg Messages Per Chat Day",
    "Agent Chats",
    "Avg Agent Chats Per Day",
  ];

  // Create CSV content
  const csvRows = [];
  csvRows.push(headers.join(","));

  // Add data rows
  for (const user of users) {
    const row = [
      user.userEmail,
      user.totalActiveDays,
      user.completionDays,
      user.chatDays,
      user.agentDays,
      user.totalCompletionsInTimePeriod,
      user.acceptedCompletionsInTimePeriod,
      `${user.acceptanceRatePercentage.toFixed(2)}%`,
      user.totalChatMessagesInTimePeriod,
      user.avgMessagesPerChatDay.toFixed(2),
      user.agentChatTotal,
      user.avgAgentChatsPerDay.toFixed(2),
    ];
    csvRows.push(row.join(","));
  }

  return csvRows.join("\n");
}

// Helper function to download CSV
function downloadCSV(csvContent: string, fileName: string): void {
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", fileName);
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Use the proper type from schema instead of duplicate definition

// Custom filter options for UserFeatureStats - excludes "All Time" to avoid expensive queries
const userFeatureStatsFilterOptions: FilterOption[] = [
  { label: "Last 7 Days", value: TimeFrame.Last7Days },
  { label: "Last 30 Days", value: TimeFrame.Last30Days },
  { label: "Last 60 Days", value: TimeFrame.Last60Days },
];

export default function UserFeatureStats() {
  const earliestData = useAppStore((state) => state.earliestData);

  // Use date filter for the component, always defaulting to Last30Days
  const [dateFilter, setDateFilter, startDate, endDate] = useDateFilter(
    earliestData,
    "YourUsage", // Use valid DashboardName
    "yourUsageFilter", // Use valid FilterKey
  );

  // Fetch user feature stats data with date parameters
  const { data: userFeatureStats = [], isLoading } = useQuery(
    dateRangeQuery<UserFeatureStats>("user-feature-stats", startDate, endDate),
  );

  // Sort users by total active days (descending)
  const sortedUsers = useMemo(() => {
    return [...(userFeatureStats || [])].sort(
      (a, b) => b.totalActiveDays - a.totalActiveDays,
    );
  }, [userFeatureStats]);

  // Handle CSV download
  const handleDownloadCSV = () => {
    const csvContent = convertToCSV(sortedUsers);
    // Format date for filename using year, month, day
    const startDateStr = `${startDate.year}-${String(startDate.month).padStart(2, "0")}-${String(startDate.day).padStart(2, "0")}`;
    const endDateStr = `${endDate.year}-${String(endDate.month).padStart(2, "0")}-${String(endDate.day).padStart(2, "0")}`;
    const fileName = `user-feature-stats-${startDateStr}-to-${endDateStr}.csv`;
    downloadCSV(csvContent, fileName);
  };

  return (
    <SectionLayout
      title={t("userFeatureStats").title}
      activeFilter={dateFilter}
      setActiveFilter={setDateFilter}
      customFilterOptions={userFeatureStatsFilterOptions}
    >
      <ChartCard title={t("userFeatureStats").title}>
        {isLoading ? (
          <Flex justify="center" p="4">
            <Text>Loading user feature data...</Text>
          </Flex>
        ) : (
          <Box style={{ overflowX: "auto", paddingBottom: "12px" }}>
            <Table.Root>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeaderCell>User Email</Table.ColumnHeaderCell>

                  {/* Activity metrics */}
                  <Table.ColumnHeaderCell>Active Days</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>
                    Completion Days
                  </Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Chat Days</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Agent Days</Table.ColumnHeaderCell>

                  {/* Completion metrics */}
                  <Table.ColumnHeaderCell>
                    Total Completions
                  </Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>
                    Accepted Completions
                  </Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>
                    Acceptance Rate
                  </Table.ColumnHeaderCell>

                  {/* Chat metrics */}
                  <Table.ColumnHeaderCell>
                    Total Chat Messages
                  </Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>
                    Avg Messages Per Chat Day
                  </Table.ColumnHeaderCell>

                  {/* Agent metrics */}
                  <Table.ColumnHeaderCell>Agent Chats</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>
                    Avg Agent Chats Per Day
                  </Table.ColumnHeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {sortedUsers.map((user: UserFeatureStat) => (
                  <Table.Row key={user.userEmail}>
                    <Table.Cell>{user.userEmail}</Table.Cell>

                    {/* Activity metrics */}
                    <Table.Cell>{user.totalActiveDays}</Table.Cell>
                    <Table.Cell>{user.completionDays}</Table.Cell>
                    <Table.Cell>{user.chatDays}</Table.Cell>
                    <Table.Cell>{user.agentDays}</Table.Cell>

                    {/* Completion metrics */}
                    <Table.Cell>{user.totalCompletionsInTimePeriod}</Table.Cell>
                    <Table.Cell>
                      {user.acceptedCompletionsInTimePeriod}
                    </Table.Cell>
                    <Table.Cell>
                      {formatPercentage(user.acceptanceRatePercentage)}
                    </Table.Cell>

                    {/* Chat metrics */}
                    <Table.Cell>
                      {user.totalChatMessagesInTimePeriod}
                    </Table.Cell>
                    <Table.Cell>
                      {user.avgMessagesPerChatDay.toFixed(2)}
                    </Table.Cell>

                    {/* Agent metrics */}
                    <Table.Cell>{user.agentChatTotal}</Table.Cell>
                    <Table.Cell>
                      {user.avgAgentChatsPerDay.toFixed(2)}
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table.Root>

            {/* Download button at bottom right */}
            {sortedUsers.length > 0 && (
              <Flex justify="end" mt="3">
                <IconButton
                  size="2"
                  variant="soft"
                  onClick={handleDownloadCSV}
                  aria-label="Download as CSV"
                  title="Download as CSV"
                >
                  <DownloadIcon />
                </IconButton>
              </Flex>
            )}
          </Box>
        )}
        {!isLoading && sortedUsers.length === 0 && (
          <Flex justify="center" p="4">
            <Text>No user feature data available</Text>
          </Flex>
        )}
      </ChartCard>
    </SectionLayout>
  );
}

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card,
  Link as RadixLink,
} from "@radix-ui/themes";
import {
  CheckCircledIcon,
  ExclamationTriangleIcon,
  ArrowRightIcon,
} from "@radix-ui/react-icons";
import { Link } from "@remix-run/react";
import { PromotionStatus } from "~services/auth/central/server/auth_entities_pb";
import { USAGE_UNITS } from "app/data/constants";

export interface PromotionStatusCardProps {
  status: PromotionStatus | null;
  creditAmount: number;
  promotionLink: string;
}

export function PromotionStatusCard({
  status,
  creditAmount,
  promotionLink,
}: PromotionStatusCardProps) {
  const sharedStyles = (
    <style scoped>{`
      :scope .promotion-bullet-list {
        margin: 0;
        padding-left: var(--ds-spacing-4_5);
        color: var(--ds-color-neutral-11);
        font-size: var(--ds-font-size-2);
        line-height: 1.4;
        margin-bottom: var(--ds-spacing-4);
        list-style-type: disc;
        list-style-position: outside;
      }

      :scope .promotion-bullet-list li {
        margin-bottom: var(--ds-spacing-1);
      }

      :scope .promotion-bullet-list li:last-child {
        margin-bottom: 0;
      }
    `}</style>
  );

  if (status === PromotionStatus.INELIGIBLE) {
    return (
      <Box>
        <Card
          style={{
            marginBottom: "var(--ds-spacing-5)",
            backgroundColor: "var(--ds-color-warning-2)",
            border: "1px solid var(--ds-color-warning-6)",
          }}
          size="3"
        >
          <Flex align="center" gap="4">
            <Box
              style={{
                color: "var(--ds-color-warning-9)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                minWidth: "var(--ds-spacing-6)",
                height: "var(--ds-spacing-6)",
                padding: "var(--ds-spacing-1)",
              }}
            >
              <ExclamationTriangleIcon width="24" height="24" />
            </Box>
            <Box>
              <Text
                size="4"
                weight="medium"
                style={{
                  display: "block",
                  marginBottom: "var(--ds-spacing-1)",
                }}
              >
                Not Eligible for This Promotion
              </Text>
              <Text
                size="2"
                style={{
                  color: "var(--ds-color-neutral-11)",
                  lineHeight: "1.4",
                  marginBottom: "var(--ds-spacing-4)",
                }}
              >
                We&apos;re sorry - this promotion is not available for your
                account. For full eligibility details, please visit our{" "}
                <RadixLink asChild underline="always" highContrast>
                  <Link
                    to={promotionLink}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    promotions page
                  </Link>
                </RadixLink>
                .
              </Text>
            </Box>
          </Flex>
        </Card>
        <Box style={{ paddingTop: "var(--ds-spacing-4)" }}>
          <Link to="/">
            <Button variant="soft" size="2">
              Return to Home
              <ArrowRightIcon width="16" height="16" />
            </Button>
          </Link>
        </Box>
        {sharedStyles}
      </Box>
    );
  }

  if (status === PromotionStatus.ENROLLED) {
    return (
      <>
        <Card
          style={{
            marginBottom: "var(--ds-spacing-5)",
            backgroundColor: "var(--ds-color-success-2)",
            border: "1px solid var(--ds-color-success-6)",
          }}
          size="3"
        >
          <Flex direction="column" gap="3">
            <Flex align="center" gap="4">
              <Box
                style={{
                  color: "var(--ds-color-success-9)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  minWidth: "var(--ds-spacing-6)",
                  height: "var(--ds-spacing-6)",
                  padding: "var(--ds-spacing-1)",
                }}
              >
                <CheckCircledIcon width="24" height="24" />
              </Box>
              <Box>
                <Text
                  size="4"
                  weight="medium"
                  style={{
                    display: "block",
                    marginBottom: "var(--ds-spacing-1)",
                  }}
                >
                  Welcome Benefit Applied!
                </Text>
                <Text
                  size="2"
                  style={{
                    color: "var(--ds-color-neutral-11)",
                    lineHeight: "1.4",
                    marginBottom: "var(--ds-spacing-4)",
                  }}
                >
                  Great news! {creditAmount} free {USAGE_UNITS} have been added
                  to your account.
                </Text>
              </Box>
            </Flex>
          </Flex>
          <Box
            style={{
              paddingTop: "var(--ds-spacing-4)",
              borderTop: "1px solid var(--ds-color-success-4)",
              marginTop: "var(--ds-spacing-4)",
            }}
          >
            <Flex gap="3">
              <Link to="/" style={{ flex: 1 }}>
                <Button variant="soft" size="2" style={{ width: "100%" }}>
                  Return to Home
                  <ArrowRightIcon width="16" height="16" />
                </Button>
              </Link>
              <RadixLink asChild style={{ flex: 1 }}>
                <Link
                  to={promotionLink}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button variant="outline" size="2" style={{ width: "100%" }}>
                    Learn More
                    <ArrowRightIcon width="16" height="16" />
                  </Button>
                </Link>
              </RadixLink>
            </Flex>
          </Box>
        </Card>
        {sharedStyles}
      </>
    );
  }

  // Default case for ELIGIBLE, UNKNOWN, or null status - the page itself should render the upload section
  return null;
}

import { Heading, Text } from "@radix-ui/themes";
import { USAGE_UNITS } from "app/data/constants";
import { typography } from "app/utils/style";

export interface PromotionPageHeaderProps {
  title?: string;
  description?: string;
  promotionName: string;
  creditAmount: number;
}

export function PromotionPageHeader({
  title = "Welcome to Augment",
  description,
  promotionName,
  creditAmount,
}: PromotionPageHeaderProps) {
  const defaultDescription = `Making the switch from ${promotionName}? We're excited to have you! Verify your ${promotionName} customer status to get started with ${creditAmount} free ${USAGE_UNITS}.`;

  return (
    <div className="promotion-header">
      <style scoped>
        {`
          :scope {
            .promotion-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              padding: var(--ds-spacing-6) 0 var(--ds-spacing-8) 0;
            }

            .promotion-title {
              margin-bottom: var(--ds-spacing-4);
            }

            .promotion-header .promotion-description {
              display: flex;
              gap: var(--ds-spacing-2);
              ${typography.text3.regular}
              color: var(--ds-color-text-subtle);
              line-height: 1.6;
            }

            .subtle-text {
              color: var(--ds-color-neutral-11);
              font-size: var(--ds-font-size-2);
            }
          }
        `}
      </style>

      <div>
        <Heading size="8" weight="bold" as="h1" className="promotion-title">
          {title}
        </Heading>
        <div className="promotion-description">
          <Text size="3" className="subtle-text">
            {description || defaultDescription}
          </Text>
        </div>
      </div>
    </div>
  );
}

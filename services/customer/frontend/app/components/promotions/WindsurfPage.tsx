import {
  Flex,
  Text,
  Box,
  Card,
  Skeleton,
  Link as RadixLink,
} from "@radix-ui/themes";
import { useState, useEffect } from "react";
import { Link } from "@remix-run/react";
import { PromotionStatus } from "~services/auth/central/server/auth_entities_pb";
import { toast } from "app/components/ui/Toast";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  promotionEligibilityQueryOptions,
  processPromotion,
} from "app/client-cache";
import { PromotionStatusCard } from "./PromotionStatusCard";
import { PromotionPageHeader } from "./PromotionPageHeader";
import { PDFUploadComponent } from "./PDFUploadComponent";

const WINDSURF_PROMOTION_ID = "windsurf_2025";
const WINDSURF_PROMOTION_NAME = "Windsurf";
const WINDSURF_CREDIT_AMOUNT = 600;
const WINDSURF_PROMOTION_LINK = "www.augmentcode.com/resources/windsurf";
export function WindsurfPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Use query for promotion eligibility
  const { data: promotionData, isLoading } = useQuery(
    promotionEligibilityQueryOptions(WINDSURF_PROMOTION_ID),
  );
  const promotionStatus = promotionData?.promotionStatus ?? null;

  // Use mutation for promotion processing
  const processPromotionMutation = useMutation(processPromotion);

  const isSubmitting = processPromotionMutation.isPending;

  // Handle success/error notifications
  useEffect(() => {
    if (processPromotionMutation.isSuccess) {
      // Show success toast notification
      toast.success({
        title: "Welcome to Augment!",
        description:
          processPromotionMutation.data?.message ||
          "Your invoice has been uploaded successfully.",
      });
    } else if (processPromotionMutation.isError) {
      // Show error toast notification
      toast.error({
        title: "Validation Error",
        description:
          processPromotionMutation.error?.message ||
          "An unexpected error occurred",
      });
      setSelectedFile(null);
    }
  }, [
    processPromotionMutation.isSuccess,
    processPromotionMutation.isError,
    processPromotionMutation.data,
    processPromotionMutation.error,
  ]);

  const handleFileSubmit = (file: File) => {
    processPromotionMutation.mutate({
      promotionId: WINDSURF_PROMOTION_ID,
      file,
    });
  };

  return (
    <Box style={{ width: "100%" }}>
      <style scoped>
        {`
          :scope {
            padding-bottom: var(--ds-spacing-10);
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--ds-spacing-5);

            .subtle-text {
              color: var(--ds-color-neutral-11);
              font-size: var(--ds-font-size-2);
              line-height: 1.5;
            }

            .note-text {
              font-style: italic;
              padding: var(--ds-spacing-4);
              background: var(--ds-color-panel);
              border-radius: var(--ds-radius-2);
              border-left: 3px solid var(--blue-8);
            }
          }
        `}
      </style>

      {/* Page Header */}
      <Box>
        <PromotionPageHeader
          promotionName={WINDSURF_PROMOTION_NAME}
          creditAmount={WINDSURF_CREDIT_AMOUNT}
        />
      </Box>

      {/* Eligibility Status Cards */}
      {isLoading ? (
        <Card size="3">
          <Flex align="center" gap="4">
            <Skeleton width="32px" height="32px" />
            <Box style={{ flex: 1 }}>
              <Skeleton
                width="200px"
                height="20px"
                style={{ marginBottom: "8px" }}
              />
              <Skeleton width="100%" height="16px" />
            </Box>
          </Flex>
        </Card>
      ) : (
        <PromotionStatusCard
          status={promotionStatus}
          creditAmount={WINDSURF_CREDIT_AMOUNT}
          promotionLink={WINDSURF_PROMOTION_LINK}
        />
      )}

      {/* Upload Section - Only show for eligible users */}
      {!isLoading &&
        (promotionStatus === PromotionStatus.ELIGIBLE ||
          promotionStatus === PromotionStatus.UNKNOWN ||
          promotionStatus === null) && (
          <>
            <Card size="3">
              <Flex direction="column" gap="4">
                <PDFUploadComponent
                  selectedFile={selectedFile}
                  onFileSelect={setSelectedFile}
                  isSubmitting={isSubmitting}
                  onSubmit={handleFileSubmit}
                  maxSizeMB={1}
                />
              </Flex>
            </Card>
            <div className="note-text">
              <Text
                size="3"
                className="subtle-text"
                style={{ lineHeight: "1.5" }}
              >
                <strong>Note:</strong> This offer is available to new customers
                only. For complete terms and conditions, visit our{" "}
                <RadixLink asChild underline="always" highContrast>
                  <Link
                    to={WINDSURF_PROMOTION_LINK}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    promotion page.
                  </Link>
                </RadixLink>
              </Text>
            </div>
          </>
        )}
    </Box>
  );
}

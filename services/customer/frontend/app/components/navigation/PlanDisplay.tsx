import { getIconComponentSafe } from "app/utils/icon";
import { Flex, Skeleton, Text } from "@radix-ui/themes";
import { useLocation } from "@remix-run/react";
import type { PlanOptionSchema, UserPlanSchema } from "app/schemas/plan";
import { useQuery } from "@tanstack/react-query";
import {
  userQueryOptions,
  subscriptionQueryOptions,
  plansQueryOptions,
  planChangeInProgressAtom,
  subscriptionCreationPendingAtom,
} from "app/client-cache";
import { capitalize } from "app/utils/string";
import { useAtomValue } from "jotai";

const getPlanBadgeConfig = (
  tier: UserPlanSchema["name"] | undefined,
  planId: PlanOptionSchema["id"] | undefined,
  plans: PlanOptionSchema[] | undefined,
) => {
  if (!tier) {
    return null;
  }
  if (tier == "enterprise") {
    return {
      color: "gray",
      name: "Enterprise",
      icon: getIconComponentSafe("lock"),
      cssColor: "var(--gray-12)",
    };
  }
  if (!planId || !plans) {
    return null;
  }
  // Find the plan data dynamically from the plans array
  const planData = plans.find(p => p.id === planId);
  if (!planData) {
    return null;
  }
  return {
    name: planData.name,
    icon: getIconComponentSafe(planData.icon),
    color: planData.colorScheme.radixColor,
    cssColor: planData.color,
  };
};

// Component for displaying plan information in header
export function PlanDisplay() {
  const location = useLocation();
  const { data: userData, isLoading: userDataIsLoading } =
    useQuery(userQueryOptions);
  const { data: subscriptionData, isLoading: subscriptionIsLoading } = useQuery(
    {
      ...subscriptionQueryOptions,
      enabled: !userDataIsLoading && userData?.tenantTier !== "enterprise",
    },
  );
  const { data: plansData } = useQuery({
    ...plansQueryOptions,
    enabled: !userDataIsLoading,
  });

  const isPlanChangeInProgress = useAtomValue(planChangeInProgressAtom);
  const isSubscriptionCreationPending = useAtomValue(
    subscriptionCreationPendingAtom,
  );

  const tenantTier = userData?.tenantTier;
  const planId = subscriptionData?.planId;

  // Determine if there's a pending plan change
  const isPending = isPlanChangeInProgress || isSubscriptionCreationPending;

  const planConfig = getPlanBadgeConfig(tenantTier, planId, plansData);
  const planName = planConfig?.name;
  const PlanIcon = planConfig?.icon;

  return (
    <Flex align="center" gap="3">
      <Flex
        align="center"
        gap="1"
        className={`topnav-plan-section ${location.pathname.includes("/select-plan") ? "hidden" : ""}`}
      >
        {tenantTier &&
        PlanIcon &&
        planConfig &&
        !userDataIsLoading &&
        !isPending &&
        (!subscriptionIsLoading || tenantTier === "enterprise") ? (
          <>
            <PlanIcon style={{ color: planConfig.cssColor }} />
            <Text
              size="2"
              weight="bold"
              className={`topnav-plan-text-real ${
                isPending
                  ? "topnav-plan-text-pending"
                  : "topnav-plan-text-gradient"
              }`}
              style={
                {
                  "--plan-color": planConfig.cssColor,
                } as React.CSSProperties
              }
            >
              {capitalize(planName || "")}
            </Text>
          </>
        ) : (
          <>
            <Skeleton width="16px" height="16px" />
            <Skeleton width="80px" height="15px" />
          </>
        )}
      </Flex>
    </Flex>
  );
}

import { <PERSON>, Button, Flex, Text } from "@radix-ui/themes";
import { Form } from "@remix-run/react";

type EmailLogoutHeaderProps = {
  email: string;
  containerWidth?: string;
};

export default function EmailLogoutHeader({
  email,
  containerWidth = "1200px",
}: EmailLogoutHeaderProps) {
  return (
    <>
      <style scoped>{`
        :scope {
          max-width: ${containerWidth};
          margin: 0 auto;
          padding: 0 32px;
        }

        .email {
          opacity: 0.9;
          font-weight: 500;
          font-size: 15px;
        }

        .logout-button {
          transition: all 0.2s ease;
          font-weight: 600;
          font-size: 15px;
          opacity: 0.9;
          padding: 8px 16px;
        }
      `}</style>
      <Box width="100%">
        <Flex justify="end" align="center" gap="3" mb="4" mt="4">
          <Text size="2" color="gray" className="email">
            {email}
          </Text>
          <Box>
            <Form action="/logout">
              <Button
                size="2"
                color="gray"
                variant="soft"
                type="submit"
                className="logout-button"
              >
                Logout
              </Button>
            </Form>
          </Box>
        </Flex>
      </Box>
    </>
  );
}

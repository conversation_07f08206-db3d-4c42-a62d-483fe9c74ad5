import { addEventListener } from "app/utils/extra";
import { dedent } from "ts-dedent";

export const SCOPED_CSS = {
  attr: "scoped",
} as NonNullable<Window["SCOPED_CSS"]>;

/**
 * This script transforms every <style scoped> in the DOM into a regular <style>
 * whose content is wrapped in "@scope { ... }".
 *
 * It uses a MutationObserver to:
 *  1. Wrap newly added <style scoped>.
 *  2. Re-wrap the text if the content of an existing <style scoped> changes.
 *  3. Wrap / unwrap the text if the scoped attribute is added or removed.
 */

export function applyPolyfill() {
  if (window.SCOPED_CSS) return;
  /**
   * Wrap the raw CSS in "@scope { ... }" if it's not already wrapped or empty.
   * @param rawCSS The unprocessed CSS text from <style>
   * @returns Possibly modified CSS string
   */

  const isFirefox =
    typeof navigator !== "undefined" && /Firefox\//.test(navigator.userAgent);

  // Map each <style scoped> to its original text content and class
  const styleMap = new WeakMap<
    HTMLStyleElement,
    { cls: string; raw: string }
  >();
  // For Firefox: map each parent element to its unique scope class
  const parentStyleClassMap = new WeakMap<HTMLElement, string>();
  let parentObserver: MutationObserver;

  if (isFirefox) {
    parentObserver = new MutationObserver((mutations) => {
      for (const m of mutations) {
        const el = m.target as HTMLElement;
        if (!parentStyleClassMap.has(el)) continue;
        const cls = parentStyleClassMap.get(el)!;
        if (el.classList.contains(cls)) continue;
        el.classList.add(cls);
      }
    });
    parentObserver.observe(document.documentElement, {
      subtree: true,
      attributes: true,
      attributeFilter: ["class"],
    });
  }

  let counter = 0;
  function makeScopeClass() {
    if (isFirefox) {
      return `scope-${counter++}${Math.random().toString(36).replace(".", "")}`;
    }
    return "@scope";
  }

  function transformCSS(styleEl: HTMLStyleElement, raw: string): string {
    const trimmed = dedent(raw).trim();
    if (!trimmed) return raw;

    // Firefox: wrap everything in .uniqueClass { … }
    // Other browsers: wrap everything in @scope { … }
    const cls = styleMap.get(styleEl)?.cls ?? makeScopeClass();
    if (isFirefox) {
      const parent = styleEl.parentElement!;
      parent.classList.add(cls);
      parentStyleClassMap.set(parent, cls);
    }
    const sel = isFirefox ? `.${cls}` : cls;
    if (trimmed.startsWith(`${sel} {`)) return raw;
    styleMap.set(styleEl, { cls, raw });
    // Firefox: replace :scope with &
    const body = isFirefox ? trimmed.replaceAll(/:scope/g, `&`) : trimmed;
    return dedent`${sel} {
      ${body}
    }`;
  }

  /**
   * Try to remove the "@scope { ... }" wrapper if we detect it's present.
   * We do a naive check for the first line being "@scope {" and the last being "}",
   * and remove them. This might need extra handling for more complex cases.
   *
   * @returns The CSS string with the @scope wrapper removed, if present
   */
  function removeWrapper(styleEl: HTMLStyleElement): string {
    if (!styleMap.has(styleEl)) return styleEl.textContent ?? "";
    const { cls, raw } = styleMap.get(styleEl)!;
    if (isFirefox) {
      const parent = styleEl.parentElement;
      if (parent) {
        parentStyleClassMap.delete(parent);
        parent.classList.remove(cls);
      }
    }
    styleMap.delete(styleEl);
    // Strip @scope { … } or .uniqueClass { … }
    return raw;
  }

  /**
   * Process a single <style scoped> by wrapping its text in "@scope { ... }".
   * Prevent re-processing by comparing old vs. new text.
   */
  function upgradeStyle(styleEl: HTMLStyleElement) {
    if (!styleEl.hasAttribute(SCOPED_CSS.attr)) return;
    const oldText = styleEl.textContent ?? "";
    const newText = transformCSS(styleEl, oldText);
    if (newText !== oldText) {
      styleEl.textContent = newText;
    }
  }

  /**
   * Downgrade a <style> by removing the @scope wrapper if it currently has it.
   */
  function downgradeStyle(styleEl: HTMLStyleElement) {
    if (styleEl.hasAttribute(SCOPED_CSS.attr)) return;
    const oldText = styleEl.textContent ?? "";
    const newText = removeWrapper(styleEl);
    if (newText !== oldText) {
      styleEl.textContent = newText;
    }
  }

  /**
   * Process all existing <style scoped> elements in the document.
   */
  function upgradeAllExisting(target: Element = document.documentElement) {
    target
      .querySelectorAll<HTMLStyleElement>(`style[${SCOPED_CSS.attr}]`)
      .forEach(upgradeStyle);
  }

  let removeListener: () => void;
  if (document.readyState === "loading") {
    removeListener = addEventListener(document, "DOMContentLoaded", () =>
      upgradeAllExisting(),
    );
  } else {
    upgradeAllExisting();
  }

  // Observe additions/changes to <style scoped>
  SCOPED_CSS.observer = new MutationObserver((mutations) => {
    for (const m of mutations) {
      m.addedNodes.forEach((addedNode) => {
        let node = addedNode;
        if (addedNode.nodeType === Node.TEXT_NODE && addedNode.parentNode) {
          node = addedNode.parentNode;
        }

        if (node instanceof Element) {
          if (node.matches(`style[${SCOPED_CSS.attr}]`)) {
            upgradeStyle(node as HTMLStyleElement);
          } else {
            upgradeAllExisting(node);
          }
        }
      });

      if (
        m.type === "characterData" &&
        m.target.parentElement?.tagName === "STYLE"
      ) {
        upgradeStyle(m.target.parentElement as HTMLStyleElement);
      }

      if (m.type === "attributes" && m.attributeName === SCOPED_CSS.attr) {
        const targetEl = m.target as HTMLStyleElement;
        if (targetEl.hasAttribute(SCOPED_CSS.attr)) {
          upgradeStyle(targetEl);
        } else {
          downgradeStyle(targetEl);
        }
      }
    }
  });

  SCOPED_CSS.observer.observe(document.documentElement, {
    childList: true,
    subtree: true,
    characterData: true,
    attributes: true,
    attributeFilter: [SCOPED_CSS.attr],
  });

  SCOPED_CSS.cleanup = () => {
    removeListener();
    SCOPED_CSS.observer.disconnect();
    if (isFirefox) parentObserver.disconnect();
  };

  window.SCOPED_CSS = SCOPED_CSS;
}

declare global {
  interface Window {
    SCOPED_CSS:
      | {
          observer: MutationObserver;
          cleanup: () => void;
          attr: string;
        }
      | undefined;
  }
}

import { getFeatureFlag } from "app/feature-flags/feature-flags.server";
import { withApiRoute } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { PromotionIdSchema } from "../schemas/promotions";
import { validatePDFContent, validatePDFFile } from "../utils/pdf-validation";
import { parseISO } from "date-fns";

// GET /api/promotions - Evaluate promotion eligibility
export const loader = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();
    const isWindsurfFeatureEnabled = await getFeatureFlag(
      "customer_ui_windsurf_promotion_enabled",
      {
        tenantId: user.tenantId,
        shardNamespace: user.shardNamespace,
      },
    );
    if (!isWindsurfFeatureEnabled) {
      const status = 403;
      log.error("Windsurf feature is disabled", {
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json(
        { error: "This promotion is not available" },
        { status },
      );
    }
    const url = new URL(request.url);
    const promotionIdParam = url.searchParams.get("id");

    log.info("Evaluating promotion eligibility", {
      promotion_id: promotionIdParam,
    });

    if (!promotionIdParam) {
      const status = 400;
      log.error("Missing id parameter", undefined, {
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "id parameter is required" }, { status });
    }

    try {
      PromotionIdSchema.parse(promotionIdParam);
    } catch (validationError) {
      const status = 400;
      log.error("Invalid promotion id format", validationError, {
        promotion_id: promotionIdParam,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Invalid id format" }, { status });
    }

    try {
      const authCentralClient = AuthCentralClient.getInstance();
      const response = await authCentralClient.evaluatePromotionEligibility(
        user,
        promotionIdParam,
      );

      const status = 200;
      log.info("Promotion eligibility evaluated successfully", {
        promotion_id: promotionIdParam,
        promotion_status: response.promotionStatus,
        status_code: status,
        duration_ms: Date.now() - start,
      });

      return Response.json({
        promotionStatus: response.promotionStatus,
      });
    } catch (error) {
      const status = 500;
      log.error("Failed to evaluate promotion eligibility", error, {
        promotion_id: promotionIdParam,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Internal server error" }, { status });
    }
  },
  {
    adminOnly: false,
  },
);

// POST /api/promotions - Process promotion
export const action = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();
    const isWindsurfFeatureEnabled = await getFeatureFlag(
      "customer_ui_windsurf_promotion_enabled",
      {
        tenantId: user.tenantId,
        shardNamespace: user.shardNamespace,
      },
    );
    if (!isWindsurfFeatureEnabled) {
      const status = 403;
      log.error("Windsurf feature is disabled", {
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json(
        { error: "This promotion is not available" },
        { status },
      );
    }

    const formData = await request.formData();
    const promotionId = formData.get("id")?.toString();
    const file = formData.get("file") as File | null;

    if (!promotionId || !PromotionIdSchema.safeParse(promotionId).success) {
      const status = 400;
      log.error("Invalid promotion ID", undefined, {
        promotion_id: promotionId,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Invalid promotion ID" }, { status });
    }

    // All promotions currently require a file upload
    if (!file || file.size === 0) {
      const status = 400;
      log.error("Valid file is required", {
        promotion_id: promotionId,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Valid file is required" }, { status });
    }

    // sleep for 1 second to prevent abuse and show that we are doing complex parsing
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const fileValidation = validatePDFFile(file, 1);
    const fileValid = fileValidation.isValid;

    if (!fileValid) {
      log.error("PDF file validation failed", undefined, {
        promotion_id: promotionId,
        validation_error: fileValidation.error,
        duration_ms: Date.now() - start,
      });
    }

    const contentValidation = fileValid
      ? await validatePDFContent(file, {
          userEmail: user.email,
          requiredSenderEmail:
            promotionId === "windsurf_2025"
              ? "<EMAIL>"
              : undefined,
          dateRange:
            promotionId === "windsurf_2025"
              ? {
                  start: parseISO("2025-04-17"),
                  end: parseISO("2025-06-16"),
                }
              : undefined,
        })
      : { isValid: false, error: "File validation failed" };

    const contentValid = contentValidation.isValid;

    if (!contentValid) {
      log.error("PDF content validation failed", undefined, {
        promotion_id: promotionId,
        validation_error: contentValidation.error,
      });
    }

    try {
      const authCentralClient = AuthCentralClient.getInstance();
      const response = await authCentralClient.processPromotion(
        user,
        promotionId,
        fileValid && contentValid,
      );

      if (!response.success) {
        const status = 400;
        log.error("Promotion processing returned failure", {
          promotion_id: promotionId,
          response: response,
          status_code: status,
          duration_ms: Date.now() - start,
        });
        return Response.json(
          { error: "Failed to process promotion" },
          { status },
        );
      }

      const status = 200;
      log.info("Promotion processed successfully", {
        promotion_id: promotionId,
        success: response.success,
        status_code: status,
        duration_ms: Date.now() - start,
      });

      return Response.json({
        success: true,
        message:
          "Invoice uploaded successfully. We'll review your purchase and add 500 free user messages to your account once verified.",
      });
    } catch (error) {
      if (!fileValid || !contentValid) {
        return Response.json(
          {
            error:
              "We couldn't verify your purchase. Please ensure you've uploaded the correct invoice.",
          },
          { status: 400 },
        );
      }

      const status = 500;
      log.error("Failed to process promotion", error, {
        promotion_id: promotionId,
        status_code: status,
        duration_ms: Date.now() - start,
      });
      return Response.json({ error: "Internal server error" }, { status });
    }
  },
  {
    adminOnly: false,
  },
);

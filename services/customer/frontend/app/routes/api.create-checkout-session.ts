import { withApi<PERSON>oute } from "../.server/auth";
import { stripe } from "../.server/stripe";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import {
  CreateCheckoutSessionRequestSchema,
  type CreateCheckoutSessionResponseSchema,
} from "../schemas/create-checkout-session";
import { Config, getCommunityPlan } from "../.server/config";

export const action = withApiRoute(
  async ({ user, request, log }) => {
    const start = Date.now();

    log.info("Processing checkout session creation");

    // Only allow POST requests
    if (request.method !== "POST") {
      const status = 405;
      log.info("Method not allowed", { status_code: status });
      return Response.json({ error: "Method not allowed" }, { status });
    }

    try {
      // Parse the request body
      const body = await request.json();
      const { planId } = CreateCheckoutSessionRequestSchema.parse(body);

      log.info("Creating checkout session", { plan_id: planId });

      // Check if it's a Community plan - no checkout needed
      const communityPlan = getCommunityPlan(Config.orbPlansConfig);
      if (planId === communityPlan.id) {
        const status = 200;
        log.info("Community plan selected - no checkout needed", {
          plan_id: planId,
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return Response.json({
          success: true,
          isCommunityPlan: true,
        } satisfies CreateCheckoutSessionResponseSchema);
      }

      // Get the user's Stripe customer ID from Auth Central
      const authCentralClient = AuthCentralClient.getInstance();
      const userResponse = await authCentralClient.getUser(user);

      if (!userResponse.user || !userResponse.user.stripeCustomerId) {
        const status = 400;
        log.error("No Stripe customer ID found for user", undefined, {
          duration_ms: Date.now() - start,
          status_code: status,
        });
        return Response.json(
          { error: "User has no Stripe customer ID" },
          { status },
        );
      }

      const stripeCustomerId = userResponse.user.stripeCustomerId;

      // Get the origin for success and cancel URLs
      const url = new URL(request.url);
      url.protocol = "https";
      const origin = url.origin;

      // Create a checkout session
      const session = await stripe.checkout.sessions.create({
        mode: "setup",
        customer: stripeCustomerId,
        payment_method_types: ["card", "amazon_pay"],
        // Include the augmentPlanType in the success URL
        success_url: `${origin}/checkout/success?session_id={CHECKOUT_SESSION_ID}&plan_id=${planId}`,
        cancel_url: `${origin}/account`,
        billing_address_collection: "required",
        customer_update: {
          address: "auto",
        },
      });

      const status = 200;
      log.info("Checkout session created successfully", {
        plan_id: planId,
        stripe_customer_id: stripeCustomerId,
        session_id: session.id,
        has_url: !!session.url,
        duration_ms: Date.now() - start,
        status_code: status,
      });

      // Return the session ID and URL
      return Response.json({
        success: true,
        sessionId: session.id,
        url: session.url || undefined, // this is the classic (null !== undefined)
      } satisfies CreateCheckoutSessionResponseSchema);
    } catch (error) {
      const status = 500;
      log.error("Failed to create checkout session", error, {
        duration_ms: Date.now() - start,
        status_code: status,
      });
      return Response.json(
        { error: "Failed to create checkout session" },
        { status },
      );
    }
  },
  {
    adminOnly: false,
  },
);

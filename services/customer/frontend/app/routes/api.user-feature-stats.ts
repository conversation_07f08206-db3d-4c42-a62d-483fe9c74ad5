import { withAuth } from "../.server/auth";
import {
  cacheableJson,
  getAuthenticatedApiData,
  getDateFilters,
} from "../utils/api";
import { cachingClient } from "../.server/grpc/request-insights-analytics";

export const loader = withAuth(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const { userFeatureStats } = await getAuthenticatedApiData(
      user,
      {
        tenantId,
        dateFilters,
      },
      cachingClient.getUserFeatureStats.bind(cachingClient),
    );

    return cacheableJson(userFeatureStats || []);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);

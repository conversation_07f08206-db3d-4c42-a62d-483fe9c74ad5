import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";
import { loader } from "../api.user-feature-stats";
import { getAuthenticatedApiData, getDateFilters } from "../../utils/api";

// Mock the dependencies
vi.mock("../../.server/auth", () => ({
  withAuth: vi.fn((handler) => handler),
  isAdmin: vi.fn(() => true),
}));

vi.mock("../../utils/api", () => ({
  cacheableJson: vi.fn((data) => new Response(JSON.stringify(data))),
  getAuthenticatedApiData: vi.fn(),
  getDateFilters: vi.fn(),
}));

vi.mock("../../.server/grpc/request-insights-analytics", () => ({
  cachingClient: {
    getUserFeatureStats: vi.fn(),
  },
}));

describe("api.user-feature-stats", () => {
  const mockUser = {
    userId: "test-user-id",
    email: "<EMAIL>",
    tenantId: "test-tenant-id",
  };

  const mockDateFilters = {
    startDate: { year: 2025, month: 5, day: 1 },
    endDate: { year: 2025, month: 5, day: 31 },
  };

  let mockGetAuthenticatedApiData: MockedFunction<
    typeof getAuthenticatedApiData
  >;
  let mockGetDateFilters: MockedFunction<typeof getDateFilters>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetAuthenticatedApiData = getAuthenticatedApiData as MockedFunction<
      typeof getAuthenticatedApiData
    >;
    mockGetDateFilters = getDateFilters as MockedFunction<
      typeof getDateFilters
    >;

    mockGetDateFilters.mockReturnValue(mockDateFilters);
  });

  it("should return empty array when no user feature stats data", async () => {
    // Mock empty response
    mockGetAuthenticatedApiData.mockResolvedValueOnce({
      userFeatureStats: [],
    });

    const mockRequest = new Request("http://localhost/api/user-feature-stats");

    const response = await loader({
      request: mockRequest,
      user: mockUser,
    } as any);

    expect(mockGetAuthenticatedApiData).toHaveBeenCalledWith(
      mockUser,
      {
        tenantId: mockUser.tenantId,
        dateFilters: mockDateFilters,
      },
      expect.any(Function),
    );

    expect(response).toBeInstanceOf(Response);
    const data = await response.json();
    expect(data).toEqual([]);
  });

  it("should return user feature stats data when available", async () => {
    const mockUserFeatureStats = [
      {
        userId: "user-1",
        totalActiveDays: 15,
        completionDays: 10,
        chatDays: 8,
        agentDays: 5,
        totalCompletionsInTimePeriod: 150,
        acceptedCompletionsInTimePeriod: 120,
        acceptanceRatePercentage: 80.0,
        totalChatMessagesInTimePeriod: 45,
        avgMessagesPerChatDay: 5.6,
        agentChatTotal: 12,
        totalAgentChatMessagesInTimePeriod: 25,
        avgAgentChatsPerDay: 2.5,
      },
      {
        userId: "user-2",
        totalActiveDays: 20,
        completionDays: 18,
        chatDays: 12,
        agentDays: 8,
        totalCompletionsInTimePeriod: 200,
        acceptedCompletionsInTimePeriod: 180,
        acceptanceRatePercentage: 90.0,
        totalChatMessagesInTimePeriod: 60,
        avgMessagesPerChatDay: 5.0,
        agentChatTotal: 20,
        totalAgentChatMessagesInTimePeriod: 40,
        avgAgentChatsPerDay: 5.0,
      },
    ];

    mockGetAuthenticatedApiData.mockResolvedValueOnce({
      userFeatureStats: mockUserFeatureStats,
    });

    const mockRequest = new Request("http://localhost/api/user-feature-stats");

    const response = await loader({
      request: mockRequest,
      user: mockUser,
    } as any);

    expect(response).toBeInstanceOf(Response);
    const data = await response.json();
    expect(data).toEqual(mockUserFeatureStats);
  });

  it("should handle API errors gracefully", async () => {
    mockGetAuthenticatedApiData.mockRejectedValueOnce(new Error("API Error"));

    const mockRequest = new Request("http://localhost/api/user-feature-stats");

    // The API route should let the error bubble up since getAuthenticatedApiData handles errors
    await expect(
      loader({
        request: mockRequest,
        user: mockUser,
      } as any),
    ).rejects.toThrow("API Error");
  });

  it("should handle malformed response gracefully", async () => {
    // Mock response without userFeatureStats property
    mockGetAuthenticatedApiData.mockResolvedValueOnce({
      someOtherProperty: "value",
    });

    const mockRequest = new Request("http://localhost/api/user-feature-stats");

    const response = await loader({
      request: mockRequest,
      user: mockUser,
    } as any);

    expect(response).toBeInstanceOf(Response);
    const data = await response.json();
    expect(data).toEqual([]);
  });

  it("should pass correct parameters to getAuthenticatedApiData", async () => {
    mockGetAuthenticatedApiData.mockResolvedValueOnce({
      userFeatureStats: [],
    });

    const mockRequest = new Request(
      "http://localhost/api/user-feature-stats?startDate=2025-05-01&endDate=2025-05-31",
    );

    await loader({
      request: mockRequest,
      user: mockUser,
    } as any);

    expect(mockGetDateFilters).toHaveBeenCalledWith(mockRequest);
    expect(mockGetAuthenticatedApiData).toHaveBeenCalledWith(
      mockUser,
      {
        tenantId: mockUser.tenantId,
        dateFilters: mockDateFilters,
      },
      expect.any(Function),
    );
  });

  it("should handle undefined userFeatureStats gracefully", async () => {
    // Test when userFeatureStats is undefined
    mockGetAuthenticatedApiData.mockResolvedValueOnce({
      userFeatureStats: undefined,
    });

    const mockRequest = new Request("http://localhost/api/user-feature-stats");

    const response = await loader({
      request: mockRequest,
      user: mockUser,
    } as any);

    const data = await response.json();
    expect(data).toEqual([]);
  });
});

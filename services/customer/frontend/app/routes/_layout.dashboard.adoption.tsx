import type { MetaFunction } from "@remix-run/node";
import AdoptionSummary from "../components/summaries/Adoption";
import DeveloperActivity from "../components/sections/DeveloperActivity";
import Chat from "../components/sections/Chat";
import UserFeatureStats from "../components/sections/UserFeatureStats";
import { getFeatureFlag } from "../feature-flags/feature-flags.client";

export const meta: MetaFunction = () => {
  return [{ title: "Augment Adoption" }];
};

export default function Adoption() {
  const enableUserFeatureStats = getFeatureFlag("customer_ui_enable_user_feature_stats");

  return (
    <>
      <AdoptionSummary title="Adoption at a glance" />
      {enableUserFeatureStats && <UserFeatureStats />}
      <Chat title="Chat adoption & usage" accentColor="amber" />
      <DeveloperActivity title="Developer activity" accentColor="indigo" />
    </>
  );
}

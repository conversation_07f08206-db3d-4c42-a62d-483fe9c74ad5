import {
  type CallOptions,
  createPromiseClient,
  type PromiseClient,
} from "@connectrpc/connect";
import type { PartialMessage, Message } from "@bufbuild/protobuf";
import { Config } from "../config";
import { CachingClient } from "./caching-client";
import { RequestInsightAnalytics } from "~services/request_insight/analytics/request_insight_analytics_connect";
import type {
  DateAndCount,
  GetActiveUsersRequest,
  GetActiveUsersResponse,
  GetCategoriesStatsRequest,
  GetCategoriesStatsResponse,
  GetCategoriesStatsResponse_CategoryStats,
  GetChatStatsRequest,
  GetChatStatsResponse,
  GetChatStatsResponse_ChatStats,
  GetCompletionStatsRequest,
  GetCompletionStatsResponse,
  GetCompletionStatsResponse_CompletionStats,
  GetDevDaysRequest,
  GetDevDaysResponse,
  GetEarliestRequestTimestampRequest,
  GetEarliestRequestTimestampResponse,
  GetEditStatsRequest,
  GetEditStatsResponse,
  GetEditStatsResponse_EditStats,
  GetKeywordsStatsRequest,
  GetKeywordsStatsResponse,
  GetUserChatRequestStatsRequest,
  GetUserChatRequestStatsResponse,
  GetUserAgentRequestStatsRequest,
  GetUserAgentRequestStatsResponse,
  GetUserAgentToolUseStatsRequest,
  GetUserAgentToolUseStatsResponse,
  GetUserFeatureStatsRequest,
  GetUserFeatureStatsResponse,
  GetUserFeatureStatsResponse_UserFeatureStats,
  GetUserLastRequestTimestampRequest,
  GetUserLastRequestTimestampResponse,
} from "~services/request_insight/analytics/request_insight_analytics_pb";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

const client = createPromiseClient(
  RequestInsightAnalytics,
  new TenantShardAwareTransport(
    (_tenantId: string, shardNamespace: string, cloud: string) =>
      Config.format(
        Config.RI_ANALYTICS_ENDPOINT_TEMPLATE,
        Config.NAMESPACE,
        Config.CLOUD_DOMAIN_SUFFIXES[cloud],
      ),
  ),
);

type RequestInsightAnalyticsRequest = RewrapMessage<
  | GetEarliestRequestTimestampRequest
  | GetDevDaysRequest
  | GetChatStatsRequest
  | GetCompletionStatsRequest
  | GetEditStatsRequest
  | GetActiveUsersRequest
  | GetCategoriesStatsRequest
  | GetUserLastRequestTimestampRequest
  | GetKeywordsStatsRequest
  | GetUserChatRequestStatsRequest
  | GetUserAgentRequestStatsRequest
  | GetUserAgentToolUseStatsRequest
  | GetUserFeatureStatsRequest
>;

export type RequestInsightAnalyticsResponse =
  | DateAndCount
  | GetActiveUsersResponse
  | GetChatStatsResponse_ChatStats
  | GetEditStatsResponse_EditStats
  | GetCompletionStatsResponse_CompletionStats
  | GetCategoriesStatsResponse_CategoryStats
  | GetUserLastRequestTimestampResponse
  | GetUserChatRequestStatsResponse
  | GetUserAgentRequestStatsResponse
  | GetUserAgentToolUseStatsResponse
  | GetUserFeatureStatsResponse_UserFeatureStats;

type RewrapMessage<T> = T extends Message<infer U> ? Message<U> : never;
type UnwrapMessage<T> = T extends Message<infer U> ? U : never;

// set the ttl to be 1 hour for optimum UX
const DEFAULT_TTL = 3600;
// time in seconds to check all data and delete expired keys
const CHECK_PERIOD = 120;

class RequestInsightAnalyticsCachingClient extends CachingClient<
  typeof RequestInsightAnalytics,
  RequestInsightAnalyticsRequest
> {
  constructor(client: PromiseClient<typeof RequestInsightAnalytics>) {
    super(client, DEFAULT_TTL, CHECK_PERIOD);

    this.getEarliestRequestTimestamp = this.createCachedMethod<
      GetEarliestRequestTimestampRequest,
      GetEarliestRequestTimestampResponse
    >(
      "getEarliestRequestTimestamp",
      this.client.getEarliestRequestTimestamp.bind(this.client),
      0,
    );

    this.getDevDays = this.createCachedMethod<
      GetDevDaysRequest,
      GetDevDaysResponse
    >("getDevDays", this.client.getDevDays.bind(this.client));

    this.getChatStats = this.createCachedMethod<
      GetChatStatsRequest,
      GetChatStatsResponse
    >("getChatStats", this.client.getChatStats.bind(this.client));

    this.getCompletionStats = this.createCachedMethod<
      GetCompletionStatsRequest,
      GetCompletionStatsResponse
    >("getCompletionStats", this.client.getCompletionStats.bind(this.client));

    this.getEditStats = this.createCachedMethod<
      GetEditStatsRequest,
      GetEditStatsResponse
    >("getEditStats", this.client.getEditStats.bind(this.client));

    this.getActiveUsers = this.createCachedMethod<
      GetActiveUsersRequest,
      GetActiveUsersResponse
    >("getActiveUsers", this.client.getActiveUsers.bind(this.client));

    this.getCategoriesStats = this.createCachedMethod<
      GetCategoriesStatsRequest,
      GetCategoriesStatsResponse
    >("getCategoriesStats", this.client.getCategoriesStats.bind(this.client));

    this.getUserLastRequestTimestamp = this.createCachedMethod<
      GetUserLastRequestTimestampRequest,
      GetUserLastRequestTimestampResponse
    >(
      "getUserLastRequestTimestamp",
      this.client.getUserLastRequestTimestamp.bind(this.client),
    );

    this.getKeywordsStats = this.createCachedMethod<
      GetKeywordsStatsRequest,
      GetKeywordsStatsResponse
    >("getKeywordsStats", this.client.getKeywordsStats.bind(this.client));

    this.getUserChatRequestStats = this.createCachedMethod<
      GetUserChatRequestStatsRequest,
      GetUserChatRequestStatsResponse
    >(
      "getUserChatRequestStats",
      this.client.getUserChatRequestStats.bind(this.client),
    );

    this.getUserAgentRequestStats = this.createCachedMethod<
      GetUserAgentRequestStatsRequest,
      GetUserAgentRequestStatsResponse
    >(
      "getUserAgentRequestStats",
      this.client.getUserAgentRequestStats.bind(this.client),
    );

    this.getUserAgentToolUseStats = this.createCachedMethod<
      GetUserAgentToolUseStatsRequest,
      GetUserAgentToolUseStatsResponse
    >(
      "getUserAgentToolUseStats",
      this.client.getUserAgentToolUseStats.bind(this.client),
    );

    this.getUserFeatureStats = this.createCachedMethod<
      GetUserFeatureStatsRequest,
      GetUserFeatureStatsResponse
    >("getUserFeatureStats", this.client.getUserFeatureStats.bind(this.client));
  }

  getEarliestRequestTimestamp: (
    request: PartialMessage<GetEarliestRequestTimestampRequest>,
    options?: CallOptions,
  ) => Promise<GetEarliestRequestTimestampResponse>;

  getDevDays: (
    request: PartialMessage<GetDevDaysRequest>,
    options?: CallOptions,
  ) => Promise<GetDevDaysResponse>;

  getChatStats: (
    request: PartialMessage<GetChatStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetChatStatsResponse>;

  getCompletionStats: (
    request: PartialMessage<GetCompletionStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetCompletionStatsResponse>;

  getEditStats: (
    request: PartialMessage<GetEditStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetEditStatsResponse>;

  getActiveUsers: (
    request: PartialMessage<GetActiveUsersRequest>,
    options?: CallOptions,
  ) => Promise<GetActiveUsersResponse>;

  getCategoriesStats: (
    request: PartialMessage<GetCategoriesStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetCategoriesStatsResponse>;

  getKeywordsStats: (
    request: PartialMessage<GetKeywordsStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetKeywordsStatsResponse>;

  getUserLastRequestTimestamp: (
    request: PartialMessage<GetUserLastRequestTimestampRequest>,
    options?: CallOptions,
  ) => Promise<GetUserLastRequestTimestampResponse>;

  getUserChatRequestStats: (
    request: PartialMessage<GetUserChatRequestStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetUserChatRequestStatsResponse>;

  getUserAgentRequestStats: (
    request: PartialMessage<GetUserAgentRequestStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetUserAgentRequestStatsResponse>;

  getUserAgentToolUseStats: (
    request: PartialMessage<GetUserAgentToolUseStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetUserAgentToolUseStatsResponse>;

  getUserFeatureStats: (
    request: PartialMessage<GetUserFeatureStatsRequest>,
    options?: CallOptions,
  ) => Promise<GetUserFeatureStatsResponse>;

  protected calculateCacheKey(
    methodName: string,
    request: UnwrapMessage<RequestInsightAnalyticsRequest>,
  ): string {
    const { tenantId, ...rest } = request;
    if (!tenantId) {
      throw new Error(`tenantId is required for ${methodName}`);
    }
    return `${methodName}|tenantId:${tenantId}|rest:${CachingClient.objToString(
      rest,
    )}`;
  }

  private createCachedMethod<
    TRequest extends PartialMessage<RequestInsightAnalyticsRequest>,
    TResponse,
  >(
    methodName: string,
    clientMethod: (
      request: TRequest,
      options?: CallOptions,
    ) => Promise<TResponse>,
    ttl: number = DEFAULT_TTL,
  ) {
    return async (
      request: PartialMessage<RequestInsightAnalyticsRequest>,
      options: CallOptions = {},
    ): Promise<TResponse> => {
      const cacheKey = this.calculateCacheKey(
        methodName,
        request as UnwrapMessage<RequestInsightAnalyticsRequest>,
      );
      return this.cacheAndCall(
        clientMethod as (
          request: TRequest,
          options: CallOptions,
        ) => Promise<TResponse>,
        cacheKey,
        request as TRequest,
        options,
        ttl,
      );
    };
  }
}

export const cachingClient = new RequestInsightAnalyticsCachingClient(client);

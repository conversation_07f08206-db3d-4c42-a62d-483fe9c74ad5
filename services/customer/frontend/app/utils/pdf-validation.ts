/**
 * PDF validation utilities for file upload components
 */

import { parse, isValid, isWithinInterval, format } from "date-fns";

export interface PDFValidationResult {
  isValid: boolean;
  error?: string;
}

export interface PDFContentValidationResult {
  isValid: boolean;
  error?: string;
  extractedDates?: Date[];
}

export interface PDFContentValidationOptions {
  userEmail: string;
  requiredSenderEmail?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface DateValidationResult {
  isValid: boolean;
  extractedDates: Date[];
  validDates: Date[];
  invalidDates: Date[];
  error?: string;
}

/**
 * Validates if a file is a PDF based on MIME type and file extension
 */
export function isPDFFile(file: File): boolean {
  // Check MIME type - PDF files can have different MIME types
  const validMimeTypes = [
    "application/pdf",
    "application/x-pdf",
    "application/acrobat",
    "applications/vnd.pdf",
    "text/pdf",
    "text/x-pdf",
  ];

  // Check file extension as fallback
  const fileName = file.name.toLowerCase();
  const hasValidExtension = fileName.endsWith(".pdf");

  // Accept if either MIME type is valid OR extension is .pdf
  return validMimeTypes.includes(file.type.toLowerCase()) || hasValidExtension;
}

/**
 * Validates if a file size is within the allowed limit
 */
export function isValidFileSize(file: File, maxSizeMB: number = 10): boolean {
  return file.size <= maxSizeMB * 1024 * 1024;
}

/**
 * Comprehensive PDF file validation
 */
export function validatePDFFile(
  file: File,
  maxSizeMB: number = 10,
): PDFValidationResult {
  if (!isPDFFile(file)) {
    return {
      isValid: false,
      error: `Invalid file type: ${file.name}. Please upload a PDF file.`,
    };
  }

  if (!isValidFileSize(file, maxSizeMB)) {
    return {
      isValid: false,
      error: `File too large: ${file.name}. Maximum size is ${maxSizeMB}MB.`,
    };
  }

  return { isValid: true };
}

/**
 * Extracts text content from a PDF file using pdfjs-dist
 */
export async function extractPDFText(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer();

  // Use the legacy build for compatibility with Node.js
  const { getDocument } = await import("pdfjs-dist/legacy/build/pdf.mjs");

  const pdf = await getDocument({
    data: arrayBuffer,
    cMapUrl: undefined,
    cMapPacked: false,
    standardFontDataUrl: undefined,
    useWorkerFetch: false,
    isEvalSupported: false,
    useSystemFonts: true,
    disableFontFace: true,
    disableRange: false,
    disableStream: false,
    verbosity: 0,
  }).promise;

  let fullText = "";

  for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
    const page = await pdf.getPage(pageNum);
    const textContent = await page.getTextContent();

    const pageText = textContent.items
      .map((item: any) => item.str || "")
      .join(" ");

    fullText += pageText + "\n";

    page.cleanup();
  }

  const result = fullText.trim();
  if (result.length === 0) {
    throw new Error("No text content found in PDF");
  }
  return result;
}

/**
 * Extracts email addresses from text using regex
 */
export function extractEmailsFromText(text: string): string[] {
  const emailRegex = /\S+@\S+\.\S+/g;
  const matches = text.match(emailRegex);
  return matches
    ? [...new Set(matches.map((email) => email.toLowerCase()))]
    : [];
}

/**
 * Extracts dates from text. Currently only supports
 * full month name and abbreviated month name formats.
 */
export function extractDatesFromText(text: string): Date[] {
  const dates: Date[] = [];

  const datePattern =
    /\b(January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\.?\s+(\d{1,2}),?\s+(\d{4})\b/gi;

  const matches = text.matchAll(datePattern);
  for (const match of matches) {
    const dateStr = match[0].replace(/[,.]/g, "");

    const formats = ["MMMM d yyyy", "MMM d yyyy"];
    for (const format of formats) {
      const parsedDate = parse(dateStr, format, new Date());
      if (isValid(parsedDate)) {
        dates.push(parsedDate);
        break;
      }
    }
  }

  // Remove duplicates
  const uniqueDates = Array.from(
    new Set(dates.map((date) => date.getTime())),
  ).map((timestamp) => new Date(timestamp));

  return uniqueDates;
}

/**
 * Validates if dates are within a specified date range
 */
export function validateDatesInRange(
  dates: Date[],
  dateRange: { start: Date; end: Date },
): DateValidationResult {
  const validDates: Date[] = [];
  const invalidDates: Date[] = [];

  dates.forEach((date) => {
    if (
      isWithinInterval(date, { start: dateRange.start, end: dateRange.end })
    ) {
      validDates.push(date);
    } else {
      invalidDates.push(date);
    }
  });

  const isValid = invalidDates.length === 0 && validDates.length > 0;

  let error: string | undefined;
  if (dates.length === 0) {
    error = "No dates found in the document";
  } else if (invalidDates.length > 0) {
    const invalidDateStrings = invalidDates
      .map((date) => format(date, "MMMM d, yyyy"))
      .join(", ");
    const rangeStart = format(dateRange.start, "MMMM d, yyyy");
    const rangeEnd = format(dateRange.end, "MMMM d, yyyy");
    error = `Found dates outside the valid range (${rangeStart} - ${rangeEnd}): ${invalidDateStrings}`;
  } else if (validDates.length === 0) {
    error = "No valid dates found within the specified range";
  }

  return {
    isValid,
    extractedDates: dates,
    validDates,
    invalidDates,
    error,
  };
}

/**
 * Validates PDF content for promotion requirements
 */
export async function validatePDFContent(
  file: File,
  options: PDFContentValidationOptions,
): Promise<PDFContentValidationResult> {
  try {
    const extractedText = await extractPDFText(file);
    const extractedEmails = extractEmailsFromText(extractedText);
    const extractedDates = extractDatesFromText(extractedText);

    const userEmailLower = options.userEmail.toLowerCase();
    const hasUserEmail = extractedEmails.some((email) =>
      email.includes(userEmailLower),
    );

    if (!hasUserEmail) {
      return {
        isValid: false,
        error: `Requested email not found in invoice.`,
        extractedDates,
      };
    }

    if (options.requiredSenderEmail) {
      const requiredEmailLower = options.requiredSenderEmail.toLowerCase();
      const hasRequiredSender = extractedEmails.some((email) =>
        email.includes(requiredEmailLower),
      );

      if (!hasRequiredSender) {
        return {
          isValid: false,
          error: `Sender email ${options.requiredSenderEmail} not found.`,
          extractedDates,
        };
      }
    }

    // Validate dates if date range is provided
    if (options.dateRange) {
      const dateValidation = validateDatesInRange(
        extractedDates,
        options.dateRange,
      );

      if (!dateValidation.isValid) {
        return {
          isValid: false,
          error: dateValidation.error,
          extractedDates,
        };
      }
    }

    return {
      isValid: true,
      extractedDates,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Failed to validate PDF content: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

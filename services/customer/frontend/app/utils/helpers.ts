export const t = (key: string) => {
  return app[key];
};

// TODO: Migrate this to a JSON file and use react-i18next
const app: { [key: string]: { title: string; definition?: string } } = {
  daily: {
    title: "Daily",
  },
  sevenDayAverage: {
    title: "7-Day Average",
  },
  developerDays: {
    title: "Developer Days",
    definition: "Augment's impact at your organization",
  },
  dailyCodeCompletions: {
    title: "Daily code completions",
  },
  dailyCodeInstructions: {
    title: "Daily code instructions",
  },
  chatMessages: {
    title: "Chat messages",
  },
  chatRequests: {
    title: "Chat requests",
  },
  dailyActiveChatUsers: {
    title: "Average daily active chat users",
  },
  averageDailyActiveUsers: {
    title: "Daily active users",
  },
  averageDailyActiveChatUsers: {
    title: "Average daily active chat users",
  },
  topChatMessageTypes: {
    title: "Top message types",
  },
  dailyActiveUsers: {
    title: "Daily Active Users",
    definition:
      "Measures the number of unique users who engage with a product in a 24-hour period.",
  },
  dailyChatMessages: {
    title: "Average daily chat messages",
    definition: "The average number of chat messages sent per day.",
  },

  completionsAndInstructions: {
    title: "Completions and Instructions",
  },
  instructionsAccepted: {
    title: "Instructions accepted",
    definition: "The number of instructions accepted by Augment.",
  },
  codeLines: {
    title: "Lines written by Augment",
    definition: "The number of lines of code written by Augment.",
  },
  codeCompletions: {
    title: "Completions",
    definition: "The number of completions by Augment.",
  },
  codeInstructions: {
    title: "Instructions",
    definition: "The number of instructions by Augment.",
  },
  codeAugments: {
    title: "Augments",
    definition: "Instructions and Code Completions by Augment",
  },
  completionsAccepted: {
    title: "Completions accepted",
    definition: "The number of completions accepted by Augment.",
  },
  completionsLinesAccepted: {
    title: "Lines accepted",
  },
  completionsCharactersAccepted: {
    title: "Characters accepted",
  },
  completionAcceptanceRate: {
    title: "Completion acceptance rate",
    definition: "The percentage of completions accepted by Augment.",
  },
  instructionAcceptanceRate: {
    title: "Instruction acceptance rate",
    definition: "The percentage of instructions accepted by Augment.",
  },
  codeCharacters: {
    title: "Characters",
    definition: "Characters written by Augment",
  },
  chatUserStats: {
    title: "Chat User Statistics",
    definition: "Statistics about chat usage by user",
  },
  userFeatureStats: {
    title: "User Feature Statistics",
    definition: "Comprehensive statistics about feature usage by user",
  },
};

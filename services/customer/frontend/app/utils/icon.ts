import {
  PersonI<PERSON>,
  LightningBoltIcon,
  LockClosedIcon,
  RocketIcon,
  ClockIcon,
} from "@radix-ui/react-icons";


// Type for Radix UI icon components - constrained to only the icons we actually use
type RadixIconComponent = typeof PersonIcon | typeof LightningBoltIcon | typeof LockClosedIcon | typeof RocketIcon | typeof ClockIcon;

// Icon mapping from string names to Radix UI components
// Only includes icons actually used by plans in orb.jsonnet
const iconMapping: Record<string, RadixIconComponent> = {
  person: PersonIcon,      // Community Plan
  clock: ClockIcon,        // Trial Plan
  rocket: RocketIcon,      // Developer Plan, Pro Plan
  lightning: LightningBoltIcon, // Max Plan
  lock: LockClosedIcon,    // Enterprise (used in TopNav)
};

/**
 * Maps an icon name string to the corresponding Radix UI icon component.
 * Follows the same pattern as getPlanSpecificColorScheme function.
 *
 * @param iconName - The string name of the icon (e.g., "person", "lightning", "lock")
 * @returns The corresponding Radix UI icon component
 * @throws Error if the icon name is not found in the mapping
 *
 * @example
 * ```tsx
 * const IconComponent = getIconComponent("person");
 * return <IconComponent style={{ color: "blue" }} />;
 * ```
 */
export function getIconComponent(iconName: string): RadixIconComponent {
  const iconComponent = iconMapping[iconName];

  if (!iconComponent) {
    throw new Error(`Icon not found: ${iconName}. Available icons: ${Object.keys(iconMapping).join(", ")}`);
  }

  return iconComponent;
}

/**
 * Gets an icon component safely, returning a default icon if the requested icon is not found.
 *
 * @param iconName - The string name of the icon
 * @param defaultIcon - The default icon name to use if the requested icon is not found (defaults to "person")
 * @returns The corresponding Radix UI icon component
 *
 * @example
 * ```tsx
 * const IconComponent = getIconComponentSafe("unknown-icon", "person");
 * return <IconComponent style={{ color: "blue" }} />;
 * ```
 */
export function getIconComponentSafe(iconName: string, defaultIcon: string = "person"): RadixIconComponent {
  try {
    return getIconComponent(iconName);
  } catch {
    return getIconComponent(defaultIcon);
  }
}
import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock PDF.js at the top level
const mockGetDocument = vi.fn();
const mockPdf = {
  numPages: 2,
  getPage: vi.fn(),
};
const mockPage = {
  getTextContent: vi.fn(),
  cleanup: vi.fn(),
};

vi.mock("pdfjs-dist/legacy/build/pdf.mjs", () => ({
  getDocument: mockGetDocument,
}));

import {
  isPDFFile,
  isValidFileSize,
  validatePDFFile,
  extractEmailsFromText,
  validatePDFContent,
  extractPDFText,
  extractDatesFromText,
  validateDatesInRange,
} from "../pdf-validation";

describe("PDF Validation Utils", () => {
  // Helper to create mock File objects
  const createMockFile = (
    name: string,
    type: string,
    size: number = 1024,
  ): File => {
    // Create content that matches the desired size
    const content = new Array(size).fill("a").join("");
    const file = new File([content], name, { type, lastModified: Date.now() });

    // Add arrayBuffer method for PDF tests
    (file as any).arrayBuffer = vi
      .fn()
      .mockResolvedValue(new ArrayBuffer(size));

    return file;
  };

  describe("isPDFFile", () => {
    it("should return true for valid PDF MIME types", () => {
      const validMimeTypes = [
        "application/pdf",
        "application/x-pdf",
        "application/acrobat",
        "applications/vnd.pdf",
        "text/pdf",
        "text/x-pdf",
      ];

      validMimeTypes.forEach((mimeType) => {
        const file = createMockFile("test.pdf", mimeType);
        expect(isPDFFile(file)).toBe(true);
      });
    });

    it("should return true for .pdf extension even with invalid MIME type", () => {
      const file = createMockFile("test.pdf", "application/octet-stream");
      expect(isPDFFile(file)).toBe(true);
    });

    it("should return false for non-PDF files", () => {
      const invalidFiles = [
        createMockFile("test.txt", "text/plain"),
        createMockFile("test.jpg", "image/jpeg"),
        createMockFile("test.doc", "application/msword"),
        createMockFile(
          "test.xlsx",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ),
      ];

      invalidFiles.forEach((file) => {
        expect(isPDFFile(file)).toBe(false);
      });
    });

    it("should handle case insensitive extensions", () => {
      const file = createMockFile("test.PDF", "application/octet-stream");
      expect(isPDFFile(file)).toBe(true);
    });
  });

  describe("isValidFileSize", () => {
    it("should return true for files within size limit", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        5 * 1024 * 1024,
      ); // 5MB
      expect(isValidFileSize(file, 10)).toBe(true);
    });

    it("should return false for files exceeding size limit", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        15 * 1024 * 1024,
      ); // 15MB
      expect(isValidFileSize(file, 10)).toBe(false);
    });

    it("should use default 10MB limit when not specified", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        5 * 1024 * 1024,
      ); // 5MB
      expect(isValidFileSize(file)).toBe(true);
    });

    it("should return true for files exactly at the limit", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        10 * 1024 * 1024,
      ); // 10MB
      expect(isValidFileSize(file, 10)).toBe(true);
    });
  });

  describe("validatePDFFile", () => {
    it("should return valid for correct PDF files", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        5 * 1024 * 1024,
      );
      const result = validatePDFFile(file);
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("should return invalid for non-PDF files", () => {
      const file = createMockFile("test.txt", "text/plain", 1024);
      const result = validatePDFFile(file);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Invalid file type");
      expect(result.error).toContain("test.txt");
    });

    it("should return invalid for oversized files", () => {
      const file = createMockFile(
        "test.pdf",
        "application/pdf",
        15 * 1024 * 1024,
      );
      const result = validatePDFFile(file, 10);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("File too large");
      expect(result.error).toContain("test.pdf");
    });
  });

  describe("extractEmailsFromText", () => {
    it("should extract valid email addresses", () => {
      const text =
        "Contact <NAME_EMAIL> or <EMAIL> for help.";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
    });

    it("should handle duplicate emails", () => {
      const text = "Email: <EMAIL> <NAME_EMAIL>";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>"]);
    });

    it("should return empty array for text without emails", () => {
      const text = "This text has no email addresses in it.";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual([]);
    });

    it("should handle case insensitive emails", () => {
      const text = "Contact <EMAIL> for support";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>"]);
    });

    it("should extract emails with various formats", () => {
      const text =
        "Emails: <EMAIL> <EMAIL> <EMAIL>";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual([
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ]);
    });

    it("should handle emails with special characters", () => {
      const text = "Contact: <EMAIL> <EMAIL>";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
    });

    it("should handle malformed email-like strings", () => {
      const text = "Not emails: @example.com, user@, user@domain, @domain.com";
      const emails = extractEmailsFromText(text);
      expect(emails).toEqual([]);
    });

    it("should handle empty and whitespace-only text", () => {
      expect(extractEmailsFromText("")).toEqual([]);
      expect(extractEmailsFromText("   \n\t  ")).toEqual([]);
    });
  });

  describe("extractPDFText", () => {
    beforeEach(() => {
      vi.clearAllMocks();
      // Reset the mock objects
      mockPdf.numPages = 2;
      mockPdf.getPage = vi.fn();
      mockPage.getTextContent = vi.fn();
      mockPage.cleanup = vi.fn();
    });

    it("should extract text from PDF successfully", async () => {
      const mockTextContent = {
        items: [
          { str: "Hello" },
          { str: "World" },
          { str: "<EMAIL>" },
        ],
      };

      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("test.pdf", "application/pdf", 1024);

      const result = await extractPDFText(file);

      expect(result).toContain("<NAME_EMAIL>");
      expect(mockGetDocument).toHaveBeenCalledWith({
        data: expect.any(ArrayBuffer),
        cMapUrl: undefined,
        cMapPacked: false,
        standardFontDataUrl: undefined,
        useWorkerFetch: false,
        isEvalSupported: false,
        useSystemFonts: true,
        disableFontFace: true,
        disableRange: false,
        disableStream: false,
        verbosity: 0,
      });
      expect(mockPdf.getPage).toHaveBeenCalledTimes(2);
      expect(mockPage.cleanup).toHaveBeenCalledTimes(2);
    });

    it("should throw error when PDF has no text content", async () => {
      const mockTextContent = {
        items: [],
      };

      // Set up for single page with no content
      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("test.pdf", "application/pdf", 1024);

      await expect(extractPDFText(file)).rejects.toThrow(
        "No text content found in PDF",
      );
    });
  });

  describe("validatePDFContent", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it("should validate PDF content successfully when user email is found", async () => {
      // Set up the PDF.js mock to return the text we want for this test
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("should fail validation when user email is not found", async () => {
      // Set up PDF.js mock to return text without the user's email
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe("Requested email not found in invoice.");
    });

    it("should fail validation when required sender email is not found", async () => {
      // Set up PDF.js mock to return text without the required sender email
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe(
        "<NAME_EMAIL> not found.",
      );
    });

    it("should validate successfully without required sender email", async () => {
      // Set up PDF.js mock to return text with user email but any sender email
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("should handle PDF extraction errors gracefully", async () => {
      // Set up PDF.js mock to throw an error
      mockGetDocument.mockReturnValue({
        promise: Promise.reject(new Error("PDF parsing failed")),
      });

      const file = createMockFile("invalid.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe(
        "Failed to validate PDF content: PDF parsing failed",
      );
    });

    it("should handle case insensitive email matching and ignore punctuation before/after email", async () => {
      // Set up PDF.js mock to return text with uppercase emails
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "..<EMAIL>," },
          { str: "from" },
          { str: "'<EMAIL>,'" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it("should validate PDF content with date range validation - success case", async () => {
      // Set up PDF.js mock to return text with valid dates within range
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "dated" },
          { str: "April 15, 2025" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
        dateRange: {
          start: new Date(2025, 3, 9), // April 9, 2025
          end: new Date(2025, 4, 9), // May 9, 2025
        },
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
      expect(result.extractedDates).toHaveLength(1);
      expect(result.extractedDates![0]).toEqual(new Date(2025, 3, 15)); // April 15, 2025
    });

    it("should fail validation when dates are outside the specified range", async () => {
      // Set up PDF.js mock to return text with dates outside the valid range
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "dated" },
          { str: "March 15, 2025" }, // Before the valid range
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
        dateRange: {
          start: new Date(2025, 3, 9), // April 9, 2025
          end: new Date(2025, 4, 9), // May 9, 2025
        },
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Found dates outside the valid range");
      expect(result.error).toContain("March 15, 2025");
      expect(result.extractedDates).toHaveLength(1);
      expect(result.extractedDates![0]).toEqual(new Date(2025, 2, 15)); // March 15, 2025
    });

    it("should fail validation when no dates are found in document with date range requirement", async () => {
      // Set up PDF.js mock to return text without any dates
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
        dateRange: {
          start: new Date(2025, 3, 9), // April 9, 2025
          end: new Date(2025, 4, 9), // May 9, 2025
        },
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe("No dates found in the document");
      expect(result.extractedDates).toEqual([]);
    });

    it("should validate successfully with multiple valid dates in range", async () => {
      // Set up PDF.js mock to return text with multiple valid dates
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "dated" },
          { str: "April 15, 2025" },
          { str: "due" },
          { str: "May 1, 2025" },
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
        dateRange: {
          start: new Date(2025, 3, 9), // April 9, 2025
          end: new Date(2025, 4, 9), // May 9, 2025
        },
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
      expect(result.extractedDates).toHaveLength(2);
      expect(result.extractedDates![0]).toEqual(new Date(2025, 3, 15)); // April 15, 2025
      expect(result.extractedDates![1]).toEqual(new Date(2025, 4, 1)); // May 1, 2025
    });

    it("should validate successfully without date range when no dateRange option provided", async () => {
      // Set up PDF.js mock to return text with dates (but no date validation required)
      const mockTextContent = {
        items: [
          { str: "Invoice" },
          { str: "for" },
          { str: "<EMAIL>" },
          { str: "dated" },
          { str: "January 1, 2020" }, // Any date should be fine when no range specified
          { str: "from" },
          { str: "<EMAIL>" },
          { str: "Total:" },
          { str: "$50.00" },
        ],
      };

      mockPdf.numPages = 1;
      mockGetDocument.mockReturnValue({
        promise: Promise.resolve(mockPdf),
      });
      mockPdf.getPage.mockResolvedValue(mockPage);
      mockPage.getTextContent.mockResolvedValue(mockTextContent);

      const file = createMockFile("invoice.pdf", "application/pdf", 1024);
      const options = {
        userEmail: "<EMAIL>",
        requiredSenderEmail: "<EMAIL>",
        // No dateRange provided
      };

      const result = await validatePDFContent(file, options);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
      expect(result.extractedDates).toHaveLength(1);
      expect(result.extractedDates![0]).toEqual(new Date(2020, 0, 1)); // January 1, 2020
    });
  });

  describe("extractDatesFromText", () => {
    it("should extract dates in full month name format", () => {
      const text =
        "Invoice dated May 25, 2025 and payment due January 15, 2024";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(2);
      expect(dates[0]).toEqual(new Date(2025, 4, 25)); // May is month 4 (0-indexed)
      expect(dates[1]).toEqual(new Date(2024, 0, 15)); // January is month 0
    });

    it("should extract dates in abbreviated month name format", () => {
      const text = "Order placed on Jan 10, 2025 and shipped Dec 31, 2024";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(2);
      expect(dates[0]).toEqual(new Date(2025, 0, 10)); // Jan is month 0
      expect(dates[1]).toEqual(new Date(2024, 11, 31)); // Dec is month 11
    });

    it("should handle dates with and without commas", () => {
      const text = "Date1: March 15, 2025 and Date2: April 20 2025";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(2);
      expect(dates[0]).toEqual(new Date(2025, 2, 15)); // March is month 2
      expect(dates[1]).toEqual(new Date(2025, 3, 20)); // April is month 3
    });

    it("should handle abbreviated months with periods", () => {
      const text = "Shipped on Jan. 5, 2025 and delivered Feb. 10, 2025";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(2);
      expect(dates[0]).toEqual(new Date(2025, 0, 5));
      expect(dates[1]).toEqual(new Date(2025, 1, 10));
    });

    it("should remove duplicate dates", () => {
      const text = "Date: May 25, 2025 and again May 25, 2025";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(1);
      expect(dates[0]).toEqual(new Date(2025, 4, 25));
    });

    it("should return empty array for text without dates", () => {
      const text = "This text has no dates in it at all.";
      const dates = extractDatesFromText(text);

      expect(dates).toEqual([]);
    });

    it("should handle case insensitive month names", () => {
      const text = "JANUARY 1, 2025 and february 28, 2025";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(2);
      expect(dates[0]).toEqual(new Date(2025, 0, 1));
      expect(dates[1]).toEqual(new Date(2025, 1, 28));
    });

    it("should handle single digit and double digit days", () => {
      const text = "March 5, 2025 and March 25, 2025";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(2);
      expect(dates[0]).toEqual(new Date(2025, 2, 5));
      expect(dates[1]).toEqual(new Date(2025, 2, 25));
    });

    it("should ignore invalid date formats", () => {
      const text = "Invalid: 25 May 2025, 2025-05-25, 05/25/2025, May 32, 2025";
      const dates = extractDatesFromText(text);

      expect(dates).toEqual([]);
    });

    it("should handle mixed valid and invalid dates", () => {
      const text =
        "Valid: May 25, 2025 Invalid: 25 May 2025 Valid: Jun 15, 2025";
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(2);
      expect(dates[0]).toEqual(new Date(2025, 4, 25));
      expect(dates[1]).toEqual(new Date(2025, 5, 15));
    });

    it("should handle all months correctly", () => {
      const text = `
        January 1, 2025 February 2, 2025 March 3, 2025 April 4, 2025
        May 5, 2025 June 6, 2025 July 7, 2025 August 8, 2025
        September 9, 2025 October 10, 2025 November 11, 2025 December 12, 2025
      `;
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(12);
      expect(dates[0]).toEqual(new Date(2025, 0, 1)); // January
      expect(dates[1]).toEqual(new Date(2025, 1, 2)); // February
      expect(dates[2]).toEqual(new Date(2025, 2, 3)); // March
      expect(dates[11]).toEqual(new Date(2025, 11, 12)); // December
    });

    it("should handle all abbreviated months correctly", () => {
      const text = `
        Jan 1, 2025 Feb 2, 2025 Mar 3, 2025 Apr 4, 2025
        Jun 6, 2025 Jul 7, 2025 Aug 8, 2025
        Sep 9, 2025 Oct 10, 2025 Nov 11, 2025 Dec 12, 2025
      `;
      const dates = extractDatesFromText(text);

      expect(dates).toHaveLength(11);
      expect(dates[0]).toEqual(new Date(2025, 0, 1)); // Jan
      expect(dates[4]).toEqual(new Date(2025, 5, 6)); // Jun
      expect(dates[10]).toEqual(new Date(2025, 11, 12)); // Dec
    });
  });

  describe("validateDatesInRange", () => {
    const dateRange = {
      start: new Date(2025, 3, 1), // April 1, 2025
      end: new Date(2025, 4, 31), // May 31, 2025
    };

    it("should validate dates within range", () => {
      const dates = [
        new Date(2025, 3, 15), // April 15, 2025
        new Date(2025, 4, 10), // May 10, 2025
      ];

      const result = validateDatesInRange(dates, dateRange);

      expect(result.isValid).toBe(true);
      expect(result.validDates).toEqual(dates);
      expect(result.invalidDates).toEqual([]);
      expect(result.extractedDates).toEqual(dates);
      expect(result.error).toBeUndefined();
    });

    it("should invalidate dates outside range", () => {
      const dates = [
        new Date(2025, 2, 15), // March 15, 2025 (before range)
        new Date(2025, 5, 10), // June 10, 2025 (after range)
      ];

      const result = validateDatesInRange(dates, dateRange);

      expect(result.isValid).toBe(false);
      expect(result.validDates).toEqual([]);
      expect(result.invalidDates).toEqual(dates);
      expect(result.extractedDates).toEqual(dates);
      expect(result.error).toContain("Found dates outside the valid range");
      expect(result.error).toContain("March 15, 2025");
      expect(result.error).toContain("June 10, 2025");
    });

    it("should handle mixed valid and invalid dates", () => {
      const validDate = new Date(2025, 4, 15); // May 15, 2025 (valid)
      const invalidDate = new Date(2025, 6, 15); // July 15, 2025 (invalid)
      const dates = [validDate, invalidDate];

      const result = validateDatesInRange(dates, dateRange);

      expect(result.isValid).toBe(false);
      expect(result.validDates).toEqual([validDate]);
      expect(result.invalidDates).toEqual([invalidDate]);
      expect(result.extractedDates).toEqual(dates);
      expect(result.error).toContain("Found dates outside the valid range");
      expect(result.error).toContain("July 15, 2025");
    });

    it("should handle empty date array", () => {
      const dates: Date[] = [];

      const result = validateDatesInRange(dates, dateRange);

      expect(result.isValid).toBe(false);
      expect(result.validDates).toEqual([]);
      expect(result.invalidDates).toEqual([]);
      expect(result.extractedDates).toEqual([]);
      expect(result.error).toBe("No dates found in the document");
    });

    it("should handle dates exactly at range boundaries", () => {
      const dates = [
        new Date(2025, 3, 1), // April 1, 2025 (start boundary)
        new Date(2025, 4, 31), // May 31, 2025 (end boundary)
      ];

      const result = validateDatesInRange(dates, dateRange);

      expect(result.isValid).toBe(true);
      expect(result.validDates).toEqual(dates);
      expect(result.invalidDates).toEqual([]);
      expect(result.error).toBeUndefined();
    });

    it("should handle dates just outside range boundaries", () => {
      const dates = [
        new Date(2025, 2, 31), // March 31, 2025 (just before start)
        new Date(2025, 5, 1), // June 1, 2025 (just after end)
      ];

      const result = validateDatesInRange(dates, dateRange);

      expect(result.isValid).toBe(false);
      expect(result.validDates).toEqual([]);
      expect(result.invalidDates).toEqual(dates);
      expect(result.error).toContain("Found dates outside the valid range");
    });
  });
});

import { logger } from "@augment-internal/logging";
import type { PlanOptionSchema, PlansSchema } from "app/schemas/plan";
import type { OrbPlanInfo } from "~services/auth/central/server/auth_pb";
import { AuthCentralClient } from "app/.server/grpc/auth-central";
import type { SessionUser } from "app/.server/auth";
import { Config, findPlanById } from "app/.server/config";
import { isUserInSelfServeTeam } from "app/utils/team.server";
import { isAdmin } from "app/.server/auth";
import { USAGE_UNITS } from "app/data/constants";

// Helper function to get plan-specific color schemes for individual plans
function getPlanSpecificColorScheme(planId: string): {
  color: string;
  colorScheme: {
    radixColor: string;
    gradientStart: string;
    gradientEnd: string;
  };
} {
  const planConfig = findPlanById(Config.orbPlansConfig, planId);

  if (!planConfig) {
    throw new Error(`Plan not found: ${planId}`);
  }

  const color = planConfig.color;

  // Map specific plan IDs to their colors
  const planSpecificColors: Record<
    string,
    {
      color: string;
      colorScheme: {
        radixColor: string;
        gradientStart: string;
        gradientEnd: string;
      };
    }
  > = {
    blue: {
      color: "var(--sky-8)",
      colorScheme: {
        radixColor: "sky",
        gradientStart: "#0ea5e9",
        gradientEnd: "#0284c7",
      },
    },
    indigo: {
      color: "var(--indigo-9)",
      colorScheme: {
        radixColor: "indigo",
        gradientStart: "#4f46e5",
        gradientEnd: "#3730a3",
      },
    },
    purple: {
      color: "var(--purple-9)",
      colorScheme: {
        radixColor: "purple",
        gradientStart: "#8b5cf6",
        gradientEnd: "#6d28d9",
      },
    },
    gold: {
      color: "var(--amber-10)",
      colorScheme: {
        radixColor: "amber",
        gradientStart: "#f59e0b",
        gradientEnd: "#d97706",
      },
    },
    gray: {
      color: "var(--gray-9)",
      colorScheme: {
        radixColor: "gray",
        gradientStart: "#6b7280",
        gradientEnd: "#4b5563",
      },
    },
  };

  return planSpecificColors[color];
}

// Helper function to determine the sort order of plans using sort_order
function getPlanSortOrder(planId: string): number {
  // For paid plans, use sort_order from the config for proper ordering
  const plan = findPlanById(Config.orbPlansConfig, planId);
  if (plan && plan.sort_order) {
    return plan.sort_order;
  }
  return 99; // Default to highest order for unknown plans
}

// Map OrbPlanInfo to PlanOption format
function mapOrbPlanToPlanOption(orbPlan: OrbPlanInfo): PlanOptionSchema {
  const colorScheme = getPlanSpecificColorScheme(orbPlan.externalPlanId);

  // Format price for display
  const price = orbPlan.pricePerSeat;
  const priceLabel = Number(price) === 0 ? "Free" : `$${price}/mo`;

  // Get icon name from plan config
  const planConfig = findPlanById(Config.orbPlansConfig, orbPlan.externalPlanId);
  const iconName = planConfig?.icon || "person";

  return {
    id: orbPlan.externalPlanId,
    augmentPlanType: Config.orbPlanIdToAugmentPlanType(orbPlan.externalPlanId),
    name: orbPlan.formattedPlanName,
    description: `${orbPlan.formattedPlanName} with ${orbPlan.usageUnitsPerSeat} ${USAGE_UNITS} per month.`,
    agentRequests: orbPlan.usageUnitsPerSeat,
    hasTraining: orbPlan.trainingAllowed,
    hasTeams: orbPlan.teamsAllowed,
    price,
    priceLabel: priceLabel,
    color: colorScheme.color,
    colorScheme: colorScheme.colorScheme,
    icon: iconName,
  };
}

/**
 * Get all available plans for a user
 * @param user The authenticated user
 * @returns An array of plan options
 */
export async function getAllPlans(user: SessionUser): Promise<PlansSchema> {
  try {
    // Get the auth central client
    const authCentralClient = AuthCentralClient.getInstance();

    // Get all Orb plans from the backend
    const plansResponse = await authCentralClient.getAllOrbPlans(user);

    // Check if the user is in a team and if they're an admin
    const [isTeam, userIsAdmin] = await Promise.all([
      isUserInSelfServeTeam(user),
      isAdmin(user),
    ]);

    // Filter out community plans only for team admins
    const plans: PlanOptionSchema[] = plansResponse.orbPlans
      .map(mapOrbPlanToPlanOption)
      .filter((plan) => {
        // Filter out community plans only if the user is a team admin
        // Non-admin team members should be able to see the community plan
        if (isTeam && userIsAdmin && plan.augmentPlanType === "community") {
          return false;
        }

        return true;
      });

    // add mock enterprise plan if it doesn't exist
    const hasEnterprisePlan = plans.some(
      (plan) => plan.augmentPlanType === "enterprise",
    );
    if (!hasEnterprisePlan) {
      plans.push({
        id: "mock_enterprise_plan",
        augmentPlanType: "enterprise",
        name: "Enterprise Plan",
        description:
          "Enterprise plans including SSO, OIDC, SCIM, Slack integration, dedicated support, and volume discounts.",
        agentRequests: 0, // This will be overridden by the display logic
        hasTraining: false,
        hasTeams: true,
        price: "0", // Custom pricing
        priceLabel: "",
        color: "var(--gray-12)",
        colorScheme: {
          radixColor: "gray",
          gradientStart: "#18181b",
          gradientEnd: "#09090b",
        },
        icon: "lock",
      });
    }

    // Sort plans using sort_order for proper ordering: community, developer, max, enterprise
    plans.sort((a: PlanOptionSchema, b: PlanOptionSchema) => {
      return getPlanSortOrder(a.id) - getPlanSortOrder(b.id);
    });

    return plans as PlansSchema;
  } catch (error) {
    logger.error("Error retrieving plans:", error);
    throw error;
  }
}

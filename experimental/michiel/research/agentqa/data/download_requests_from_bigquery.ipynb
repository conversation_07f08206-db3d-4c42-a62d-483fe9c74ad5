#%%
%load_ext autoreload
%autoreload 2
#%%
from pathlib import Path
from datetime import datetime

from base.datasets.tenants import (
    DOGFOOD_SHARD,
    VANGUARD,
    VANGUARD_I1,
    AITUTOR_MERCOR,
    AITUTOR_TURING,
)

# TENANT = DOGFOOD_SHARD
# TENANT = VANGUARD
TENANT = VANGUARD_I1
# TENANT = AITUTOR_MERCOR
# TENANT = AITUTOR_TURING
START_DATE = "2024-01-01"
LIMIT = None

today = datetime.today().strftime("%Y%m%d")

if LIMIT is None:
    name = f"{TENANT.name}_since_{START_DATE.replace('-', '')}_to_{today}"
else:
    name = f"{TENANT.name}_since_{START_DATE.replace('-', '')}_to_{today}_limit{LIMIT}"

OUTPUT_DIR = Path("/mnt/efs/spark-data/shared/agentqa/v0/") / name
OUTPUT_PATH = OUTPUT_DIR / "requests_from_big_query.parquet"

OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

if OUTPUT_PATH.exists():
    raise ValueError(f"Output path {OUTPUT_PATH} already exists, skipping")


print(f"Downloading requests for {TENANT.name} since {START_DATE} to {OUTPUT_PATH}")
#%%
from pathlib import Path
import pandas as pd


ALLOWED_RIDS_BASE_DIR = Path("/mnt/efs/spark-data/shared//vanguard/license_filter/permissive_rids/chat_host_request")
ALLOWED_RIDS_PATHS = list(ALLOWED_RIDS_BASE_DIR.glob("*.zstd.parquet"))

# Or read all parquet files into a single dataframe
all_dfs = []
for path in ALLOWED_RIDS_PATHS:
    df = pd.read_parquet(path, engine='pyarrow')
    all_dfs.append(df)

# Combine all dataframes
combined_df = pd.concat(all_dfs, ignore_index=True)

allowed_rids = set(combined_df.request_id.unique())
print(f"Found {len(allowed_rids)} allowed request IDs")
#%%
import dataclasses
import pyarrow
import pyarrow.parquet
import tqdm
from typing import Iterator

from google.cloud import bigquery

from base.datasets.gcp_creds import get_gcp_creds


TENANT_NAME_TO_DATASET = {
    "dogfood-shard": "us_staging_request_insight_analytics_dataset",
    "aitutor-mercor": "us_prod_request_insight_analytics_dataset",
    "aitutor-turing": "us_prod_request_insight_analytics_dataset",
    "i0-vanguard0": "us_prod_request_insight_analytics_dataset",
    "i1-vanguard0": "us_prod_request_insight_analytics_dataset",
}

PAGE_SIZE = 128


@dataclasses.dataclass
class BigQueryRequestMetadata:
    tenant: str
    user_id: str
    timestamp: int
    request_id: str


def download_request_ids(
    tenant, start_date, limit
) -> Iterator[BigQueryRequestMetadata]:
    query = f"""\
  SELECT
    tenant, user_id, UNIX_MICROS(time) AS timestamp, request_id
  FROM
    `{TENANT_NAME_TO_DATASET[tenant.name]}.request_metadata`
  WHERE
    request_type = "CHAT"
    AND tenant = "{tenant.name}"
    AND time >= "{start_date}"
    AND user_agent NOT IN ("AugmentHealthCheck/0", "Augment-EvalHarness/0 (Regression Testing)", "api_proxy_client/0 (Python)")
    AND user_id NOT IN ("health-check-1", "eval-determined-bot")
  ORDER BY tenant, user_id, time DESC
  """

    if limit is not None:
        query += f"\nLIMIT {limit}"

    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    rows = bigquery_client.query_and_wait(query, page_size=PAGE_SIZE)
    for row in rows:
        if row.request_id in allowed_rids:
          yield BigQueryRequestMetadata(
              tenant=row.tenant,
              user_id=row.user_id,
              timestamp=row.timestamp,
              request_id=row.request_id,
          )
#%%
import contextlib
import io
import sys


@contextlib.contextmanager
def suppress_output():
    """Suppress stdout and stderr within a context.

    Usage:
        with suppress_output():
            print("This will not be visible")
    """
    stdout, stderr = sys.stdout, sys.stderr
    stream = io.StringIO()
    sys.stdout = sys.stderr = stream
    try:
        yield
    finally:
        sys.stdout, sys.stderr = stdout, stderr


with suppress_output():
    print("This will not be visible")
#%%
from enum import Enum

from experimental.michiel.research.agentqa.retrieval import BlobGetter


CHAT_THREAD_SUMMARY_MESSAGE = (
    "Please provide a clear and concise summary of our conversation so far."
)

XIAOLEI_EXPERIMENTS = "Given the context below, answer this question:"


class FilterReason(str, Enum):
    THREAD_SUMMARY = "thread_summary"
    XIAOLEI_EXPERIMENTS = "xiaolei_experiments"
    EMPTY_MESSAGE = "empty_message"
    FAILED_TO_RETRIEVE_REQUEST = "failed_to_retrieve_request"
    DUPLICATE_MESSAGE = "duplicate_message"


@dataclasses.dataclass
class RequestMetadata(BigQueryRequestMetadata):
    message: str
    path: str | None
    n_blobs: int


class Processor:
    def __init__(self, tenant):
        self.tenant = tenant

    def initialize(self):
        Processor.blob_getter = BlobGetter(self.tenant)

    def __call__(self, request: BigQueryRequestMetadata):
        with suppress_output():
            chat_request = Processor.blob_getter.get_raw_chat_request(
                request.request_id
            )
            if chat_request is None:
                return None, FilterReason.FAILED_TO_RETRIEVE_REQUEST
            message = chat_request.message
            if len(message) == 0:
                return None, FilterReason.EMPTY_MESSAGE
            elif message.startswith(XIAOLEI_EXPERIMENTS):
                return None, FilterReason.XIAOLEI_EXPERIMENTS
            elif message.startswith(CHAT_THREAD_SUMMARY_MESSAGE):
                return None, FilterReason.THREAD_SUMMARY
            blob_names = Processor.blob_getter.get_blob_names_from_chat_request(
                chat_request
            )
        request_metadata = RequestMetadata(
            **dataclasses.asdict(request),
            message=message,
            path=chat_request.path,
            n_blobs=len(blob_names),
        )
        return request_metadata, None
#%%
import dataclasses
from collections import Counter

from research.data.spark.pipelines.utils.safe_writer import SafeWriter
from experimental.michiel.research.agentqa.data.utils import (
    maybe_run_in_multiple_processes,
)


N_PROCESSES = 30

writer = SafeWriter(str(OUTPUT_PATH))

n_saved_requests = 0
seen_requests = set()
stats = Counter()
requests = []


def flush():
    global writer
    global requests
    global n_saved_requests
    if len(requests) > 0:
        requests_dict = [dataclasses.asdict(request) for request in requests]
        table_batch = pyarrow.Table.from_pylist(requests_dict).to_batches()[0]
        writer.write(table_batch)
        n_saved_requests += len(requests_dict)
        requests = []


data_iterator = tqdm.tqdm(
    maybe_run_in_multiple_processes(
        Processor(tenant=TENANT),
        download_request_ids(TENANT, START_DATE, LIMIT),
        num_processes=N_PROCESSES,
    )
)

for request, filter_reason in data_iterator:
    if request is None:
        assert filter_reason is not None
        stats[filter_reason] += 1
        continue
    else:
        assert filter_reason is None

    request_key = (request.path, request.message)
    if request_key in seen_requests:
        stats[FilterReason.DUPLICATE_MESSAGE] += 1
        continue
    else:
        seen_requests.add(request_key)

    requests.append(request)
    stats["success"] += 1
    if len(requests) >= PAGE_SIZE:
        flush()
    data_iterator.set_postfix(stats)

flush()
writer.finalize()

print(f"Saved {n_saved_requests} regular requests")

print(
    f"Read back from {OUTPUT_PATH} {len(pyarrow.parquet.read_table(OUTPUT_PATH))} rows"
)
#%%
data = pyarrow.parquet.read_table(OUTPUT_PATH)
df = data.to_pandas()
df.groupby("user_id").count().reset_index().sort_values(
    "request_id", ascending=False
).head()
#%%
import matplotlib.pyplot as plt
import numpy as np
import pyarrow.parquet as pq

table = pq.read_table(OUTPUT_PATH)
df = table.to_pandas()
print(f"Read back from {OUTPUT_PATH} {len(df)} rows")

plt.figure(figsize=(6, 4))
n_blobs = df["n_blobs"][df["n_blobs"] > 0]  # Filter out zeros for log scale
plt.hist(
    n_blobs, bins=np.logspace(np.log10(n_blobs.min()), np.log10(n_blobs.max()), 50)
)
plt.xscale("log")
plt.title("Distribution of Number of Blobs")
plt.xlabel("Number of blobs (log scale)")
plt.ylabel("Frequency")
plt.grid(True)
plt.show()

print("\nSummary statistics for n_blobs:")
print(df["n_blobs"].describe())
#%%

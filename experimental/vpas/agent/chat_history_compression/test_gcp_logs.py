#!/usr/bin/env python3
"""Test script for GCP logging functionality."""

import sys
import os
from datetime import datetime
from typing import List

# Add the repository root to the path
sys.path.insert(0, "/home/<USER>/augment")


def test_gcp_log_utils_imports():
    """Test that GCP log utils can be imported."""
    print("Testing GCP log utils imports...")

    try:
        from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
            ClientAttempt,
            RequestClientInfo,
            query_client_logs,
            aggregate_client_info,
            analyze_client_switching_patterns,
        )

        print("✓ GCP log utils imports work")
        return True
    except Exception as e:
        print(f"✗ GCP log utils imports failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_client_attempt_creation():
    """Test ClientAttempt data structure."""
    print("\nTesting ClientAttempt creation...")

    try:
        from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
            ClientAttempt,
        )

        # Create a test ClientAttempt
        attempt = ClientAttempt(
            timestamp=datetime.now(),
            request_id="test-request-123",
            client_name="ANTHROPIC_VERTEXAI_EU_W1",
            attempt_number=1,
            is_success=True,
            is_fallback=False,
        )

        assert attempt.request_id == "test-request-123"
        assert attempt.client_name == "ANTHROPIC_VERTEXAI_EU_W1"
        assert attempt.attempt_number == 1
        assert attempt.is_success is True
        assert attempt.is_fallback is False

        print("✓ ClientAttempt creation works")
        return True
    except Exception as e:
        print(f"✗ ClientAttempt creation failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_request_client_info_creation():
    """Test RequestClientInfo data structure."""
    print("\nTesting RequestClientInfo creation...")

    try:
        from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
            RequestClientInfo,
        )

        # Create a test RequestClientInfo
        info = RequestClientInfo(
            request_id="test-request-123",
            attempted_clients=["ANTHROPIC_VERTEXAI_EU_W1", "ANTHROPIC_VERTEXAI_AS_SE1"],
            successful_client="ANTHROPIC_VERTEXAI_AS_SE1",
            total_attempts=2,
            had_fallbacks=True,
        )

        assert info.request_id == "test-request-123"
        assert len(info.attempted_clients) == 2
        assert info.successful_client == "ANTHROPIC_VERTEXAI_AS_SE1"
        assert info.total_attempts == 2
        assert info.had_fallbacks is True

        print("✓ RequestClientInfo creation works")
        return True
    except Exception as e:
        print(f"✗ RequestClientInfo creation failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_aggregate_client_info():
    """Test aggregating client attempts."""
    print("\nTesting client info aggregation...")

    try:
        from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
            ClientAttempt,
            aggregate_client_info,
        )

        # Create test attempts
        attempts = [
            ClientAttempt(
                timestamp=datetime.now(),
                request_id="req-1",
                client_name="ANTHROPIC_VERTEXAI_EU_W1",
                attempt_number=1,
                is_success=False,
                is_fallback=False,
            ),
            ClientAttempt(
                timestamp=datetime.now(),
                request_id="req-1",
                client_name="ANTHROPIC_VERTEXAI_EU_W1",
                attempt_number=0,
                is_success=False,
                is_fallback=True,
            ),
            ClientAttempt(
                timestamp=datetime.now(),
                request_id="req-1",
                client_name="ANTHROPIC_VERTEXAI_AS_SE1",
                attempt_number=2,
                is_success=False,
                is_fallback=False,
            ),
            ClientAttempt(
                timestamp=datetime.now(),
                request_id="req-1",
                client_name="ANTHROPIC_VERTEXAI_AS_SE1",
                attempt_number=0,
                is_success=True,
                is_fallback=False,
            ),
        ]

        # Aggregate the attempts
        result = aggregate_client_info(attempts)

        assert "req-1" in result
        info = result["req-1"]
        assert info.request_id == "req-1"
        assert len(info.attempted_clients) == 2
        assert info.successful_client == "ANTHROPIC_VERTEXAI_AS_SE1"
        assert info.total_attempts == 2
        assert info.had_fallbacks is True

        print("✓ Client info aggregation works")
        return True
    except Exception as e:
        print(f"✗ Client info aggregation failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_analyze_client_switching_patterns():
    """Test client switching pattern analysis."""
    print("\nTesting client switching pattern analysis...")

    try:
        from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
            RequestClientInfo,
            analyze_client_switching_patterns,
        )

        # Create test client info
        client_info = {
            "req-1": RequestClientInfo(
                request_id="req-1",
                attempted_clients=["ANTHROPIC_VERTEXAI_EU_W1"],
                successful_client="ANTHROPIC_VERTEXAI_EU_W1",
                total_attempts=1,
                had_fallbacks=False,
            ),
            "req-2": RequestClientInfo(
                request_id="req-2",
                attempted_clients=[
                    "ANTHROPIC_VERTEXAI_EU_W1",
                    "ANTHROPIC_VERTEXAI_AS_SE1",
                ],
                successful_client="ANTHROPIC_VERTEXAI_AS_SE1",
                total_attempts=2,
                had_fallbacks=True,
            ),
            "req-3": RequestClientInfo(
                request_id="req-3",
                attempted_clients=["ANTHROPIC_VERTEXAI_AS_SE1"],
                successful_client="ANTHROPIC_VERTEXAI_AS_SE1",
                total_attempts=1,
                had_fallbacks=False,
            ),
        }

        request_order = ["req-1", "req-2", "req-3"]

        # Analyze patterns
        analysis = analyze_client_switching_patterns(client_info, request_order)

        assert analysis["total_requests"] == 3
        assert analysis["client_switches"] == 1  # req-1 to req-2 switched clients
        assert analysis["total_attempts"] == 4
        assert analysis["failed_attempts"] == 1  # req-2 had 1 failed attempt

        print("✓ Client switching pattern analysis works")
        return True
    except Exception as e:
        print(f"✗ Client switching pattern analysis failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_time_based_batching():
    """Test time-based batching functionality."""
    print("\nTesting time-based batching...")

    try:
        from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
            create_time_based_batches,
        )
        from datetime import datetime, timedelta

        # Create test data with timestamps spread over 3 hours
        base_time = datetime.now()
        requests_with_timestamps = [
            ("req-1", base_time),
            ("req-2", base_time + timedelta(minutes=30)),
            (
                "req-3",
                base_time + timedelta(hours=1, minutes=15),
            ),  # Should be in same batch
            (
                "req-4",
                base_time + timedelta(hours=2, minutes=30),
            ),  # Should start new batch
            (
                "req-5",
                base_time + timedelta(hours=2, minutes=45),
            ),  # Should be in same batch as req-4
        ]

        # Test with 1-hour window
        batches = create_time_based_batches(
            requests_with_timestamps, max_time_window_hours=1.0
        )

        # Debug: print actual batches
        print(f"    Debug: Created {len(batches)} batches:")
        for i, batch in enumerate(batches):
            batch_ids = [req_id for req_id, _ in batch]
            print(f"      Batch {i+1}: {batch_ids}")

        # The logic should create batches based on time windows
        # req-1, req-2 (30min gap) should be in first batch
        # req-3 (1h15min from start) might start a new batch depending on implementation
        # req-4, req-5 (2h30min+ from start) should definitely be in a separate batch
        assert len(batches) >= 2, f"Expected at least 2 batches, got {len(batches)}"
        assert len(batches) <= 4, f"Expected at most 4 batches, got {len(batches)}"

        # Test with smaller batch size
        batches_small = create_time_based_batches(
            requests_with_timestamps, max_batch_size=2
        )
        assert (
            len(batches_small) >= 3
        ), f"Expected at least 3 batches with max_batch_size=2, got {len(batches_small)}"

        print("✓ Time-based batching works")
        return True
    except Exception as e:
        print(f"✗ Time-based batching failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_analyze_conversation_imports():
    """Test that analyze_conversation can be imported with new functionality."""
    print("\nTesting analyze_conversation imports...")

    try:
        from experimental.vpas.agent.chat_history_compression.analyze_conversation import (
            detect_client_switching_invalidations,
            estimate_client_switch_cost,
            print_client_analysis,
        )

        print("✓ analyze_conversation imports work")
        return True
    except Exception as e:
        print(f"✗ analyze_conversation imports failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_optimized_query_imports():
    """Test that optimized query functions can be imported."""
    print("\nTesting optimized query imports...")

    try:
        from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
            query_client_logs_optimized,
            create_time_based_batches,
        )

        print("✓ Optimized query imports work")
        return True
    except Exception as e:
        print(f"✗ Optimized query imports failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("RUNNING GCP LOGGING TESTS")
    print("=" * 60)

    tests = [
        test_gcp_log_utils_imports,
        test_client_attempt_creation,
        test_request_client_info_creation,
        test_aggregate_client_info,
        test_analyze_client_switching_patterns,
        test_time_based_batching,
        test_analyze_conversation_imports,
        test_optimized_query_imports,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} PASSED")
            else:
                failed += 1
                print(f"✗ {test.__name__} FAILED")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} FAILED with exception: {e}")

    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)

    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

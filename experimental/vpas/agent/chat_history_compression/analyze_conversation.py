"""
Detailed conversation analysis script.

This script analyzes a specific conversation and shows:
- All requests in chronological order
- Token and cost data for each request
- Cache invalidation events with detailed breakdown
- Extra costs due to invalidation

Usage:
    python analyze_conversation.py <conversation_id> [--tenant TENANT] [--prod] [--staging]
"""

import argparse
import logging
from datetime import datetime
from typing import List, Optional, Dict

from experimental.vpas.agent.chat_history_compression.common import (
    ConversationRequest,
    CacheInvalidationEvent,
    fetch_conversation_data,
    detect_cache_invalidation_between_requests,
    format_cost,
    format_tokens,
    format_time_diff,
)
from base.datasets.tenants import DATASET_TENANTS, get_tenant
from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
    query_client_logs,
    query_client_logs_optimized,
    aggregate_client_info,
    analyze_client_switching_patterns,
    RequestClientInfo,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def analyze_conversation_detailed(
    conversation_id: str,
    project_id: str,
    dataset_name: str,
    tenant_filter: Optional[str] = None,
) -> None:
    """Analyze a specific conversation in detail."""

    logger.info(f"Fetching data for conversation: {conversation_id}")

    # Fetch all requests for this conversation
    requests = fetch_conversation_data(
        project_id=project_id,
        dataset_name=dataset_name,
        conversation_id=conversation_id,
        tenant_filter=tenant_filter,
    )

    if not requests:
        print(f"❌ No requests found for conversation: {conversation_id}")
        return

    logger.info(f"Found {len(requests)} requests in conversation")

    # Query Google Cloud logs for client information
    request_ids = [req.request_id for req in requests]
    logger.info("Querying Google Cloud logs for client usage patterns...")

    try:
        # Use smaller batch size for large conversations to avoid query length limits
        batch_size = 100
        logger.info(
            f"Using batch size: {batch_size} for {len(request_ids)} request IDs"
        )

        # Calculate time window from first and last requests for optimization
        start_time = requests[0].time
        end_time = requests[-1].time
        logger.info(
            f"Request time range: {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')}"
        )

        # Use optimized time-based batching for better performance
        requests_with_timestamps = [(req.request_id, req.time) for req in requests]

        client_attempts = query_client_logs_optimized(
            requests_with_timestamps=requests_with_timestamps,
            project_id=project_id,
            time_buffer_minutes=10,
            max_time_window_hours=1.0,
            max_batch_size=100,
        )
        client_info_by_request = aggregate_client_info(client_attempts)
        client_analysis = analyze_client_switching_patterns(
            client_info_by_request, request_ids
        )
    except Exception as e:
        logger.warning(f"Failed to query client logs: {e}")
        client_info_by_request = {}
        client_analysis = {}

    # Detect invalidation events (including client switching)
    invalidation_events = []
    for i in range(1, len(requests)):
        event = detect_cache_invalidation_between_requests(requests[i - 1], requests[i])
        if event:
            invalidation_events.append(event)

    # Detect client switching invalidations and merge with regular invalidations
    client_switch_events = detect_client_switching_invalidations(
        requests, client_info_by_request
    )

    # Merge client switch events into the main invalidation events list
    all_invalidation_events = {e.request_id: e for e in invalidation_events}
    for event in client_switch_events:
        existing_event = all_invalidation_events.get(event.request_id)
        if not existing_event or existing_event.invalidation_type.startswith(
            "alteration"
        ):
            all_invalidation_events[event.request_id] = event
    all_invalidation_events = list(all_invalidation_events.values())

    # Print detailed analysis
    print_conversation_header(conversation_id, requests, all_invalidation_events)
    print_client_analysis(client_analysis, client_info_by_request, requests)
    print_request_details(requests, all_invalidation_events, client_info_by_request)
    print_invalidation_summary(all_invalidation_events)
    print_cost_summary(requests, all_invalidation_events)


def detect_client_switching_invalidations(
    requests: List[ConversationRequest],
    client_info_by_request: Dict[str, RequestClientInfo],
) -> List[CacheInvalidationEvent]:
    """Detect cache invalidations caused by client switching."""
    client_switch_events = []

    prev_successful_client = None
    for i, request in enumerate(requests):
        if request.request_id not in client_info_by_request:
            continue

        client_info = client_info_by_request[request.request_id]
        current_successful_client = client_info.successful_client

        if (
            i > 0
            and prev_successful_client
            and current_successful_client
            and prev_successful_client != current_successful_client
        ):
            # Calculate potential extra cost due to client switching
            # This is an estimate based on cache invalidation patterns
            prev_request = requests[i - 1]
            extra_cost = estimate_client_switch_cost(prev_request, request)

            # Create a CacheInvalidationEvent for client switching
            client_switch_event = CacheInvalidationEvent(
                conversation_id=request.conversation_id,
                request_id=request.request_id,
                prev_request_id=prev_request.request_id,
                time=request.time,
                prev_time=prev_request.time,
                invalidation_type="client_switch",
                prev_cache_reads=prev_request.cache_read_input_tokens,
                curr_cache_reads=request.cache_read_input_tokens,
                prev_input_tokens=prev_request.total_input_tokens,
                curr_input_tokens=request.total_input_tokens,
                extra_cost=extra_cost,
                actual_cost=request.actual_cost,
                client_type=request.client_type,
                prev_client=prev_successful_client,
                new_client=current_successful_client,
            )
            client_switch_events.append(client_switch_event)

        if current_successful_client:
            prev_successful_client = current_successful_client

    return client_switch_events


def estimate_client_switch_cost(
    prev_request: ConversationRequest, curr_request: ConversationRequest
) -> float:
    """Estimate extra cost due to client switching invalidating cache."""
    # When clients switch, the cache is typically invalidated
    # Estimate based on the pattern that cache reads drop significantly

    # If current request has fewer cache reads than expected,
    # assume the difference is due to client switching
    expected_cache_reads = min(
        prev_request.cache_read_input_tokens, curr_request.total_input_tokens
    )
    actual_cache_reads = curr_request.cache_read_input_tokens

    lost_cache_hits = max(0, expected_cache_reads - actual_cache_reads)

    # Cost difference: cache creation (3.75) vs cache read (0.3) per 1M tokens
    extra_cost = lost_cache_hits * (3.75 - 0.3) / 1_000_000

    return extra_cost


def print_client_analysis(
    client_analysis: Dict,
    client_info_by_request: Dict[str, RequestClientInfo],
    requests: List[ConversationRequest],
) -> None:
    """Print analysis of client usage patterns."""
    if not client_analysis:
        print("\n⚠️  Client analysis unavailable (log query failed)")
        return

    print(f"\n{'='*100}")
    print("CLIENT USAGE ANALYSIS")
    print(f"{'='*100}")

    print("\n🔄 Client switching patterns:")
    print(
        f"   • Total requests with client data: {client_analysis.get('total_requests', 0)}"
    )
    print(f"   • Client switches: {client_analysis.get('client_switches', 0)}")
    print(f"   • Switch rate: {client_analysis.get('switch_rate', 0):.1%}")

    print("\n📊 Attempt statistics:")
    print(f"   • Total attempts: {client_analysis.get('total_attempts', 0)}")
    print(f"   • Failed attempts: {client_analysis.get('failed_attempts', 0)}")
    print(f"   • Success rate: {client_analysis.get('success_rate', 0):.1%}")

    # Show client usage frequency
    successful_clients = client_analysis.get("successful_clients", [])
    if successful_clients:
        from collections import Counter

        client_counts = Counter(successful_clients)
        print("\n🎯 Most used clients:")
        for client, count in client_counts.most_common(5):
            percentage = count / len(successful_clients) * 100
            print(f"   • {client}: {count} times ({percentage:.1f}%)")


def print_conversation_header(
    conversation_id: str,
    requests: List[ConversationRequest],
    invalidation_events: List[CacheInvalidationEvent],
) -> None:
    """Print conversation header with summary info."""
    print(f"\n{'='*100}")
    print(f"CONVERSATION ANALYSIS: {conversation_id}")
    print(f"{'='*100}")

    if not requests:
        return

    start_time = requests[0].time
    end_time = requests[-1].time
    duration = end_time - start_time
    total_cost = sum(req.actual_cost for req in requests)

    # Categorize invalidation events by type
    expiration_events = [
        e for e in invalidation_events if e.invalidation_type == "expiration"
    ]
    truncation_events = [
        e for e in invalidation_events if e.invalidation_type == "truncation"
    ]
    alteration_events = [
        e for e in invalidation_events if e.invalidation_type.startswith("alteration")
    ]
    client_switch_events = [
        e for e in invalidation_events if e.invalidation_type == "client_switch"
    ]

    expiration_cost = sum(event.extra_cost for event in expiration_events)
    truncation_cost = sum(event.extra_cost for event in truncation_events)
    alteration_cost = sum(event.extra_cost for event in alteration_events)
    client_switch_cost = sum(event.extra_cost for event in client_switch_events)
    total_extra_cost = (
        expiration_cost + truncation_cost + alteration_cost + client_switch_cost
    )

    print("\n📊 Overview:")
    print(f"   • Total requests: {len(requests)}")
    print(
        f"   • Time span: {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%H:%M:%S')}"
    )
    print(f"   • Duration: {format_time_diff(duration)}")
    print(f"   • Client type: {requests[0].client_type}")
    print(f"   • Cache invalidations: {len(invalidation_events)}")
    print(f"   • Total cost: {format_cost(total_cost)}")

    if total_extra_cost > 0:
        savings_pct = (total_extra_cost / total_cost * 100) if total_cost > 0 else 0

        if expiration_cost > 0:
            print(
                f"   • Extra cost from cache expiration: {format_cost(expiration_cost)} ({expiration_cost/total_cost*100:.1f}%)"
            )
        if truncation_cost > 0:
            print(
                f"   • Extra cost from truncation: {format_cost(truncation_cost)} ({truncation_cost/total_cost*100:.1f}%)"
            )
        if alteration_cost > 0:
            print(
                f"   • Extra cost from alteration: {format_cost(alteration_cost)} ({alteration_cost/total_cost*100:.1f}%)"
            )
        if client_switch_cost > 0:
            print(
                f"   • Extra cost from client switches: {format_cost(client_switch_cost)} ({client_switch_cost/total_cost*100:.1f}%)"
            )

        print(
            f"   • Total extra cost: {format_cost(total_extra_cost)} ({savings_pct:.1f}%)"
        )


def print_request_details(
    requests: List[ConversationRequest],
    invalidation_events: List[CacheInvalidationEvent],
    client_info_by_request: Dict[str, RequestClientInfo],
) -> None:
    """Print detailed breakdown of each request."""
    print(f"\n{'='*100}")
    print("REQUEST DETAILS")
    print(f"{'='*100}")

    # Create lookup for invalidation events by request_id
    invalidations_by_request = {
        event.request_id: event for event in invalidation_events
    }

    for i, request in enumerate(requests):
        print(f"\n🔹 Request #{i+1}: {request.request_id}")
        print(f"   Time: {request.time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Tool: {request.tool_name}")

        # Time gap from previous request
        if i > 0:
            time_gap = request.time - requests[i - 1].time
            print(f"   Time since prev: {format_time_diff(time_gap)}")

        # Client information
        if request.request_id in client_info_by_request:
            client_info = client_info_by_request[request.request_id]
            print("\n   🔄 Client usage:")
            if client_info.attempted_clients:
                print(f"      • Attempted: {' → '.join(client_info.attempted_clients)}")
            if client_info.successful_client:
                print(f"      • Successful: {client_info.successful_client}")
            if client_info.had_fallbacks:
                print(
                    f"      • Had fallbacks: Yes ({client_info.total_attempts} attempts)"
                )
            else:
                print("      • Had fallbacks: No")

        # Token breakdown
        print("\n   📈 Tokens:")
        print(f"      • Input (non-cached): {format_tokens(request.input_tokens)}")
        print(
            f"      • Cache reads (hits): {format_tokens(request.cache_read_input_tokens)}"
        )
        print(
            f"      • Cache creation (misses): {format_tokens(request.cache_creation_input_tokens)}"
        )
        print(f"      • Total input: {format_tokens(request.total_input_tokens)}")
        print(f"      • Output: {format_tokens(request.total_output_tokens)}")

        # Cost breakdown
        input_cost = request.input_tokens * 3.0 / 1_000_000
        cache_read_cost = request.cache_read_input_tokens * 0.3 / 1_000_000
        cache_creation_cost = request.cache_creation_input_tokens * 3.75 / 1_000_000
        output_cost = request.total_output_tokens * 15.0 / 1_000_000

        print("\n   💰 Cost breakdown:")
        print(f"      • Input (non-cached): {format_cost(input_cost)}")
        print(f"      • Cache reads: {format_cost(cache_read_cost)}")
        print(f"      • Cache creation: {format_cost(cache_creation_cost)}")
        print(f"      • Output: {format_cost(output_cost)}")
        print(f"      • Total: {format_cost(request.actual_cost)}")

        # Check for cache invalidation
        if request.request_id in invalidations_by_request:
            event = invalidations_by_request[request.request_id]
            print_invalidation_details(event, requests[i - 1])


def print_invalidation_details(
    event: CacheInvalidationEvent,
    prev_request: ConversationRequest,
) -> None:
    """Print detailed invalidation analysis."""
    print(f"\n   🚨 CACHE INVALIDATION DETECTED: {event.invalidation_type.upper()}")

    # Show cache change
    cache_lost = event.prev_cache_reads - event.curr_cache_reads
    print(
        f"      • Cache reads: {format_tokens(event.prev_cache_reads)} → {format_tokens(event.curr_cache_reads)} (lost: {format_tokens(cache_lost)})"
    )
    print(
        f"      • Total input: {format_tokens(event.prev_input_tokens)} → {format_tokens(event.curr_input_tokens)}"
    )

    # Time gap
    time_gap = event.time - event.prev_time
    print(f"      • Time gap: {format_time_diff(time_gap)}")

    # Invalidation type explanation
    if event.invalidation_type == "expiration":
        print("      • Reason: Cache expired (time gap > 5 minutes)")
    elif event.invalidation_type == "truncation":
        print("      • Reason: Chat history truncated (total input decreased)")
    elif event.invalidation_type == "alteration":
        print(
            "      • Reason: Chat history altered (input increased but cache reads decreased)"
        )
    elif event.invalidation_type == "client_switch":
        # Access client metadata from the event fields
        prev_client = event.prev_client or "Unknown"
        new_client = event.new_client or "Unknown"
        print(f"      • Reason: Client switched from {prev_client} to {new_client}")
        print("      • Impact: Cache invalidated due to client routing change")
    else:
        print("      • Reason: Unknown (same input but cache reads decreased)")

    # Cost impact
    if event.extra_cost > 0:
        savings_pct = (
            (event.extra_cost / event.actual_cost * 100) if event.actual_cost > 0 else 0
        )
        print(
            f"      • Extra cost: {format_cost(event.extra_cost)} ({savings_pct:.1f}% of request cost)"
        )

        # Explain the calculation
        potential_cache_hits = min(event.prev_cache_reads, event.curr_input_tokens)
        lost_cache_hits = max(0, potential_cache_hits - event.curr_cache_reads)
        print(f"      • Lost cache hits: {format_tokens(lost_cache_hits)} tokens")
        print("      • Rate difference: 3.75 - 0.3 = 3.45 per 1M tokens")


def print_invalidation_summary(
    invalidation_events: List[CacheInvalidationEvent],
) -> None:
    """Print summary of all invalidation events."""
    if not invalidation_events:
        print("\n✅ No cache invalidation events detected")
        return

    print(f"\n{'='*100}")
    print("INVALIDATION SUMMARY")
    print(f"{'='*100}")

    # Group invalidation events by type
    by_type = {}
    for event in invalidation_events:
        if event.invalidation_type not in by_type:
            by_type[event.invalidation_type] = []
        by_type[event.invalidation_type].append(event)

    print("\n📊 Invalidation events by type:")
    for inv_type, events in sorted(by_type.items()):
        total_extra_cost = sum(e.extra_cost for e in events)
        display_name = inv_type.capitalize().replace("_", " ")
        print(f"   • {display_name}: {len(events)} events")
        if total_extra_cost > 0:
            print(f"     - Extra cost: {format_cost(total_extra_cost)}")

    # Show client switch details if any
    client_switch_events = [
        e for e in invalidation_events if e.invalidation_type == "client_switch"
    ]
    if client_switch_events:
        print("\n🔄 Client switching details:")
        for event in client_switch_events:
            prev_client = event.prev_client or "Unknown"
            new_client = event.new_client or "Unknown"
            print(f"   • Request {event.request_id}: {prev_client} → {new_client}")
            if event.extra_cost > 0:
                print(f"     - Extra cost: {format_cost(event.extra_cost)}")


def print_cost_summary(
    requests: List[ConversationRequest],
    invalidation_events: List[CacheInvalidationEvent],
) -> None:
    """Print cost summary and optimization potential."""
    print(f"\n{'='*100}")
    print("COST SUMMARY")
    print(f"{'='*100}")

    total_cost = sum(req.actual_cost for req in requests)

    # Categorize costs by invalidation type
    expiration_events = [
        e for e in invalidation_events if e.invalidation_type == "expiration"
    ]
    truncation_events = [
        e for e in invalidation_events if e.invalidation_type == "truncation"
    ]
    alteration_events = [
        e for e in invalidation_events if e.invalidation_type.startswith("alteration")
    ]
    client_switch_events = [
        e for e in invalidation_events if e.invalidation_type == "client_switch"
    ]

    expiration_cost = sum(event.extra_cost for event in expiration_events)
    truncation_cost = sum(event.extra_cost for event in truncation_events)
    alteration_cost = sum(event.extra_cost for event in alteration_events)
    client_switch_cost = sum(event.extra_cost for event in client_switch_events)
    total_extra_cost = (
        expiration_cost + truncation_cost + alteration_cost + client_switch_cost
    )

    print("\n💰 Cost breakdown:")
    print(f"   • Total conversation cost: {format_cost(total_cost)}")
    if expiration_cost > 0:
        print(f"   • Extra cost from cache expiration: {format_cost(expiration_cost)}")
    if truncation_cost > 0:
        print(f"   • Extra cost from truncation: {format_cost(truncation_cost)}")
    if alteration_cost > 0:
        print(f"   • Extra cost from alteration: {format_cost(alteration_cost)}")
    if client_switch_cost > 0:
        print(
            f"   • Extra cost from client switches: {format_cost(client_switch_cost)}"
        )
    print(f"   • Total extra cost: {format_cost(total_extra_cost)}")

    if total_extra_cost > 0 and total_cost > 0:
        savings_pct = total_extra_cost / total_cost * 100

        print("\n📊 Cost impact breakdown:")
        if expiration_cost > 0:
            print(
                f"   • Cache expiration: {expiration_cost/total_cost*100:.1f}% of total cost"
            )
        if truncation_cost > 0:
            print(
                f"   • Truncation: {truncation_cost/total_cost*100:.1f}% of total cost"
            )
        if alteration_cost > 0:
            print(
                f"   • Alteration: {alteration_cost/total_cost*100:.1f}% of total cost"
            )
        if client_switch_cost > 0:
            print(
                f"   • Client switches: {client_switch_cost/total_cost*100:.1f}% of total cost"
            )
        print(f"   • Combined impact: {savings_pct:.1f}% of total cost")

        print("\n🎯 Optimization opportunity:")
        if savings_pct > 20:
            print(f"   • HIGH: {savings_pct:.1f}% savings potential")
        elif savings_pct > 10:
            print(f"   • MEDIUM: {savings_pct:.1f}% savings potential")
        elif savings_pct > 5:
            print(f"   • LOW: {savings_pct:.1f}% savings potential")
        else:
            print(f"   • MINIMAL: {savings_pct:.1f}% savings potential")

        # Specific recommendations
        print("\n💡 Recommendations:")
        other_invalidation_cost = expiration_cost + truncation_cost + alteration_cost

        if client_switch_cost > other_invalidation_cost:
            print("   • Focus on reducing client switching (primary cost driver)")
            print("   • Consider client affinity or sticky routing")
        elif other_invalidation_cost > client_switch_cost:
            print("   • Focus on reducing cache invalidations (primary cost driver)")
            print("   • Review chat history management and timing patterns")
        else:
            print("   • Address both client switching and cache invalidations")

        # Specific recommendations by type
        if expiration_cost > 0:
            print(
                "   • Cache expiration: Consider shorter conversation gaps or longer cache TTL"
            )
        if truncation_cost > 0:
            print(
                "   • Truncation: Optimize chat history management to reduce truncation"
            )
        if alteration_cost > 0:
            print("   • Alteration: Review chat history modification patterns")
    else:
        print("   • No optimization opportunity detected")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze a specific conversation for cache invalidation patterns"
    )

    parser.add_argument("conversation_id", type=str, help="Conversation ID to analyze")

    # Dataset selection
    dataset_group = parser.add_mutually_exclusive_group()
    dataset_group.add_argument(
        "--prod",
        action="store_true",
        help="Use production dataset (us_prod_request_insight_analytics_dataset)",
    )
    dataset_group.add_argument(
        "--staging",
        action="store_true",
        help="Use staging dataset (us_staging_request_insight_analytics_dataset)",
    )

    parser.add_argument(
        "--tenant",
        type=str,
        default=None,
        help=f"Tenant name to analyze. Available: {list(DATASET_TENANTS.keys())}",
    )

    return parser.parse_args()


def main():
    """Main function to orchestrate the conversation analysis."""
    args = parse_arguments()

    # Determine dataset configuration
    if args.prod:
        project_id = "system-services-prod"
        dataset_name = "us_prod_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using production dataset")
    elif args.staging:
        project_id = "system-services-prod"
        dataset_name = "us_staging_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using staging dataset")
    elif args.tenant:
        tenant = get_tenant(args.tenant)
        project_id = tenant.project_id
        dataset_name = tenant.analytics_dataset_name
        tenant_filter = tenant.name
        logger.info(f"Analyzing specific tenant: {args.tenant}")
    else:
        # Default: use production dataset
        project_id = "system-services-prod"
        dataset_name = "us_prod_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using production dataset (default)")

    try:
        analyze_conversation_detailed(
            conversation_id=args.conversation_id,
            project_id=project_id,
            dataset_name=dataset_name,
            tenant_filter=tenant_filter,
        )
    except Exception as e:
        logger.error(f"Error analyzing conversation: {e}")
        raise


if __name__ == "__main__":
    main()

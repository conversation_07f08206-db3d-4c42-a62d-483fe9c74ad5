"""
This script analyzes cache invalidation patterns in real conversation data.

It identifies different types of cache invalidation:
1. Cache expiration (time between requests > 5 min)
2. Chat history truncation (total input tokens decrease)
3. Chat history alteration (input tokens increase but cache reads decrease)
   3a. Alteration at beginning (cache hits < first request size)
   3b. Alteration in middle (cache hits >= first request size)

For alteration cases, it calculates the extra cost that could be saved if invalidation didn't happen.

Usage:
    python analyze_cache_invalidation.py [--num-conversations N] [--from-date DATE | --last-hours N | --last-days N] [--to-date DATE]

    # Examples:
    # Analyze last 24 hours
    python analyze_cache_invalidation.py --last-hours 24

    # Analyze specific date range (timezone required)
    python analyze_cache_invalidation.py --from-date 2024-01-01T00:00:00Z --to-date 2024-01-02T00:00:00Z
    python analyze_cache_invalidation.py --from-date 2024-01-01T00:00:00+00:00 --to-date 2024-01-02T00:00:00+00:00

    # Analyze last 7 days
    python analyze_cache_invalidation.py --last-days 7
"""

import argparse
import datetime
import logging
from collections import defaultdict
from datetime import timedelta, timezone
from typing import Dict, List, Optional, Tuple

from google.cloud import bigquery
from tqdm import tqdm

from experimental.vpas.agent.chat_history_compression.common import (
    CacheInvalidationEvent,
    ConversationRequest,
    get_bigquery_client,
    detect_cache_invalidation_between_requests,
)
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DATASET_TENANTS, DatasetTenant, get_tenant

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def normalize_datetime_for_comparison(
    dt1: datetime.datetime, dt2: datetime.datetime
) -> Tuple[datetime.datetime, datetime.datetime]:
    """
    Normalize two datetime objects to have compatible timezone information for comparison.

    Args:
        dt1: First datetime object
        dt2: Second datetime object

    Returns:
        Tuple of (dt1_normalized, dt2_normalized) that can be safely compared
    """
    if dt1.tzinfo is not None and dt2.tzinfo is None:
        # dt1 is timezone-aware, dt2 is naive - assume dt2 is UTC
        dt2 = dt2.replace(tzinfo=timezone.utc)
    elif dt1.tzinfo is None and dt2.tzinfo is not None:
        # dt1 is naive, dt2 is timezone-aware - assume dt1 is UTC
        dt1 = dt1.replace(tzinfo=timezone.utc)

    return dt1, dt2


def is_datetime_in_range(
    dt: datetime.datetime,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> bool:
    """
    Check if a datetime falls within the specified range, handling timezone compatibility.

    Note: With the updated CLI requiring timezone-aware dates, this function now primarily
    handles cases where database datetimes might be timezone-naive.

    Args:
        dt: The datetime to check
        from_datetime: Start of range (inclusive), None means no lower bound
        to_datetime: End of range (inclusive), None means no upper bound

    Returns:
        True if dt is within the range, False otherwise
    """
    if from_datetime is not None:
        dt_norm, from_norm = normalize_datetime_for_comparison(dt, from_datetime)
        if dt_norm < from_norm:
            return False

    if to_datetime is not None:
        dt_norm, to_norm = normalize_datetime_for_comparison(dt, to_datetime)
        if dt_norm > to_norm:
            return False

    return True


# CacheInvalidationEvent is now imported from common.py


def fetch_conversations_with_cache_data(
    project_id: str,
    dataset_name: str,
    from_datetime: datetime.datetime,
    to_datetime: datetime.datetime,
    tenant_filter: Optional[str] = None,
    num_conversations: Optional[int] = None,
) -> Dict[str, List[ConversationRequest]]:
    """Fetch conversation data with cache usage information."""
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)

    # Build tenant filter
    tenant_condition = f"AND t.tenant = '{tenant_filter}'" if tenant_filter else ""

    # Build sampling logic
    if num_conversations:
        conversation_sample_query = f"""
        WITH conversations AS (
            SELECT DISTINCT JSON_EXTRACT_SCALAR(t.sanitized_json, '$.conversation_id') as conversation_id
            FROM `{project_id}.{dataset_name}.tool_use_data` t
            WHERE t.time >= TIMESTAMP('{from_datetime.isoformat()}')
                AND t.time <= TIMESTAMP('{to_datetime.isoformat()}')
                AND JSON_EXTRACT_SCALAR(t.sanitized_json, '$.conversation_id') IS NOT NULL
                {tenant_condition}
        )
        SELECT conversation_id
        FROM conversations
        ORDER BY RAND()
        LIMIT {num_conversations}
        """

        logger.info(f"Sampling {num_conversations} random conversations...")
        sample_rows = bigquery_client.query_and_wait(conversation_sample_query)
        sampled_conversation_ids = [row.conversation_id for row in sample_rows]

        if not sampled_conversation_ids:
            logger.warning("No conversations found for sampling")
            return {}

        conversation_ids_str = "', '".join(sampled_conversation_ids)
        conversation_filter = f"AND JSON_EXTRACT_SCALAR(t.sanitized_json, '$.conversation_id') IN ('{conversation_ids_str}')"
        logger.info(
            f"Selected {len(sampled_conversation_ids)} conversations for analysis"
        )
    else:
        conversation_filter = ""

    # Main query to get tool use data with cache information and client type
    query = f"""
    SELECT
        meta.time,
        t.request_id,
        JSON_EXTRACT_SCALAR(t.sanitized_json, '$.conversation_id') as conversation_id,
        CAST(JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_input_len') AS INT64) as tool_input_len,
        CAST(JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_output_len') AS INT64) as tool_output_len,
        JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_name') as tool_name,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.input_tokens') AS INT64), 0) as input_tokens,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.cache_read_input_tokens') AS INT64), 0) as cache_read_input_tokens,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.cache_creation_input_tokens') AS INT64), 0) as cache_creation_input_tokens,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.total_output_tokens') AS INT64), 0) as total_output_tokens,
        CASE
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'vscode') THEN 'vscode'
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'intellij') THEN 'intellij'
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'beachhead') THEN 'beachhead'
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'vim') THEN 'vim'
            ELSE 'other'
        END as client_type
    FROM `{project_id}.{dataset_name}.tool_use_data` t
    JOIN `{project_id}.{dataset_name}.prompt_cache_usage` c
        ON t.request_id = c.request_id
    JOIN `{project_id}.{dataset_name}.request_metadata` meta
        ON t.request_id = meta.request_id
    WHERE meta.time >= TIMESTAMP('{from_datetime.isoformat()}')
        AND meta.time <= TIMESTAMP('{to_datetime.isoformat()}')
        AND JSON_EXTRACT_SCALAR(t.sanitized_json, '$.conversation_id') IS NOT NULL
        AND JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_input_len') IS NOT NULL
        AND JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_output_len') IS NOT NULL
        {tenant_condition}
        {conversation_filter}
    ORDER BY conversation_id, meta.time
    """

    filter_desc = f"tenant {tenant_filter}" if tenant_filter else "all tenants"
    logger.info(
        f"Fetching conversation data with cache info for {filter_desc} from {from_datetime} to {to_datetime}"
    )

    rows = bigquery_client.query_and_wait(query)

    # Group by conversation
    conversations = defaultdict(list)
    for row in rows:
        conversations[row.conversation_id].append(
            ConversationRequest(
                time=row.time,
                request_id=row.request_id,
                conversation_id=row.conversation_id,
                tool_input_len=row.tool_input_len,
                tool_output_len=row.tool_output_len,
                tool_name=row.tool_name,
                input_tokens=row.input_tokens,
                cache_read_input_tokens=row.cache_read_input_tokens,
                cache_creation_input_tokens=row.cache_creation_input_tokens,
                total_output_tokens=row.total_output_tokens,
                client_type=row.client_type,
            )
        )

    total_records = sum(len(exchanges) for exchanges in conversations.values())
    logger.info(
        f"Fetched {total_records} records across {len(conversations)} conversations"
    )

    return dict(conversations)


def detect_cache_invalidation(
    conversations: Dict[str, List[ConversationRequest]],
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> List[CacheInvalidationEvent]:
    """Detect cache invalidation events in conversations."""
    invalidation_events = []

    logger.info(f"Analyzing cache invalidation in {len(conversations)} conversations")

    for conversation_id, conversation_requests in tqdm(
        conversations.items(), desc="Detecting invalidation"
    ):
        if len(conversation_requests) < 2:
            continue  # Need at least 2 conversation_request to detect invalidation

        # Get the first request's total input tokens for alteration type detection
        first_request_tokens = conversation_requests[0].total_input_tokens

        # Detect invalidation between consecutive requests
        for i in range(1, len(conversation_requests)):
            event = detect_cache_invalidation_between_requests(
                conversation_requests[i - 1], conversation_requests[i]
            )
            if event and event.invalidation_type == "alteration":
                # Determine if alteration happened at beginning or middle
                curr_cache_reads = event.curr_cache_reads

                if curr_cache_reads < first_request_tokens:
                    # Alteration happened in the beginning of chat history
                    event = event._replace(invalidation_type="alteration_beginning")
                else:
                    # Alteration happened in the middle of chat history
                    event = event._replace(invalidation_type="alteration_middle")

            # Only include events that fall within the specified time window
            if event and is_datetime_in_range(event.time, from_datetime, to_datetime):
                invalidation_events.append(event)

    logger.info(f"Detected {len(invalidation_events)} cache invalidation events")
    return invalidation_events


def analyze_invalidation_patterns(events: List[CacheInvalidationEvent]) -> None:
    """Analyze and report on cache invalidation patterns."""
    if not events:
        print("No cache invalidation events detected.")
        return

    # Group events by type
    events_by_type = defaultdict(list)
    for event in events:
        events_by_type[event.invalidation_type].append(event)

    print(f"\n{'='*80}")
    print("CACHE INVALIDATION ANALYSIS")
    print(f"{'='*80}")

    print(f"\nTotal invalidation events: {len(events)}")
    print(
        f"Unique conversations affected: {len(set(e.conversation_id for e in events))}"
    )

    # Summary by type
    print(
        f"\n{'Type':<20} {'Count':<8} {'%':<6} {'Avg Extra Cost':<15} {'Total Extra Cost':<15}"
    )
    print("-" * 75)

    total_extra_cost = 0.0
    alteration_beginning_events = events_by_type.get("alteration_beginning", [])
    alteration_middle_events = events_by_type.get("alteration_middle", [])

    # Display order: expiration, truncation, alteration_beginning, alteration_middle, then alteration_total
    display_order = [
        "expiration",
        "truncation",
        "alteration_beginning",
        "alteration_middle",
    ]

    for inv_type in display_order:
        if inv_type not in events_by_type:
            continue

        type_events = events_by_type[inv_type]
        count = len(type_events)
        percentage = (count / len(events)) * 100
        extra_costs = [e.extra_cost for e in type_events]
        avg_extra_cost = sum(extra_costs) / len(extra_costs) if extra_costs else 0.0
        total_type_cost = sum(extra_costs)
        total_extra_cost += total_type_cost

        print(
            f"{inv_type:<20} {count:<8} {percentage:<5.1f}% ${avg_extra_cost:<14.4f} ${total_type_cost:<14.2f}"
        )

    # Show combined alteration stats
    if alteration_beginning_events or alteration_middle_events:
        all_alteration_events = alteration_beginning_events + alteration_middle_events
        alteration_count = len(all_alteration_events)
        alteration_percentage = (alteration_count / len(events)) * 100
        alteration_extra_costs = [e.extra_cost for e in all_alteration_events]
        alteration_avg_cost = (
            sum(alteration_extra_costs) / len(alteration_extra_costs)
            if alteration_extra_costs
            else 0.0
        )
        alteration_total_cost = sum(alteration_extra_costs)

        print("-" * 75)
        print(
            f"{'alteration_total':<20} {alteration_count:<8} {alteration_percentage:<5.1f}% ${alteration_avg_cost:<14.4f} ${alteration_total_cost:<14.2f}"
        )

    print("-" * 75)
    print(
        f"{'TOTAL':<20} {len(events):<8} {'100.0%':<6} {'':<15} ${total_extra_cost:<14.2f}"
    )

    # Potential savings breakdown
    analyze_potential_savings(events)

    # Client type breakdown
    analyze_client_type_breakdown(events)

    # Detailed analysis for each type
    analyze_expiration_events(events_by_type.get("expiration", []))
    analyze_truncation_events(events_by_type.get("truncation", []))
    analyze_alteration_events(
        events_by_type.get("alteration_beginning", []),
        events_by_type.get("alteration_middle", []),
    )

    # Time-based analysis
    print(f"\n{'='*80}")
    print("TIME-BASED ANALYSIS")
    print(f"{'='*80}")

    # Group by hour of day
    events_by_hour = defaultdict(int)
    for event in events:
        hour = event.time.hour
        events_by_hour[hour] += 1

    if events_by_hour:
        print("\nInvalidation events by hour of day:")
        max_count = max(events_by_hour.values())
        for hour in range(24):
            count = events_by_hour.get(hour, 0)
            bar = "█" * (count * 30 // max(1, max_count))
            print(f"{hour:2d}:00 {count:3d} {bar}")

    # Conversation-level analysis
    analyze_conversation_patterns(events)


def analyze_potential_savings(events: List[CacheInvalidationEvent]) -> None:
    """Analyze potential cost savings by invalidation type."""
    if not events:
        return

    print(f"\n{'='*80}")
    print("POTENTIAL COST SAVINGS ANALYSIS")
    print(f"{'='*80}")

    # Group events by type and calculate savings
    expiration_events = [e for e in events if e.invalidation_type == "expiration"]
    alteration_beginning_events = [
        e for e in events if e.invalidation_type == "alteration_beginning"
    ]
    alteration_middle_events = [
        e for e in events if e.invalidation_type == "alteration_middle"
    ]
    all_alteration_events = alteration_beginning_events + alteration_middle_events

    # Calculate costs
    expiration_extra_cost = sum(e.extra_cost for e in expiration_events)
    alteration_extra_cost = sum(e.extra_cost for e in all_alteration_events)
    total_extra_cost = expiration_extra_cost + alteration_extra_cost

    # Calculate actual costs for percentage calculations
    expiration_actual_cost = sum(e.actual_cost for e in expiration_events)
    alteration_actual_cost = sum(e.actual_cost for e in all_alteration_events)
    total_actual_cost = expiration_actual_cost + alteration_actual_cost

    print(
        f"\n{'Invalidation Type':<25} {'Events':<8} {'Extra Cost':<12} {'Actual Cost':<12} {'Savings %':<10}"
    )
    print("-" * 70)

    # Expiration savings
    if expiration_events:
        expiration_savings_pct = (
            (expiration_extra_cost / expiration_actual_cost * 100)
            if expiration_actual_cost > 0
            else 0.0
        )
        print(
            f"{'Cache Expiration':<25} {len(expiration_events):<8} ${expiration_extra_cost:<11.3f} ${expiration_actual_cost:<11.3f} {expiration_savings_pct:<9.1f}%"
        )

    # Alteration savings (combined)
    if all_alteration_events:
        alteration_savings_pct = (
            (alteration_extra_cost / alteration_actual_cost * 100)
            if alteration_actual_cost > 0
            else 0.0
        )
        print(
            f"{'Chat History Alteration':<25} {len(all_alteration_events):<8} ${alteration_extra_cost:<11.3f} ${alteration_actual_cost:<11.3f} {alteration_savings_pct:<9.1f}%"
        )

        # Breakdown by alteration subtype
        if alteration_beginning_events:
            beginning_extra_cost = sum(
                e.extra_cost for e in alteration_beginning_events
            )
            beginning_actual_cost = sum(
                e.actual_cost for e in alteration_beginning_events
            )
            beginning_savings_pct = (
                (beginning_extra_cost / beginning_actual_cost * 100)
                if beginning_actual_cost > 0
                else 0.0
            )
            print(
                f"{'  • Beginning Alteration':<25} {len(alteration_beginning_events):<8} ${beginning_extra_cost:<11.3f} ${beginning_actual_cost:<11.3f} {beginning_savings_pct:<9.1f}%"
            )

        if alteration_middle_events:
            middle_extra_cost = sum(e.extra_cost for e in alteration_middle_events)
            middle_actual_cost = sum(e.actual_cost for e in alteration_middle_events)
            middle_savings_pct = (
                (middle_extra_cost / middle_actual_cost * 100)
                if middle_actual_cost > 0
                else 0.0
            )
            print(
                f"{'  • Middle Alteration':<25} {len(alteration_middle_events):<8} ${middle_extra_cost:<11.3f} ${middle_actual_cost:<11.3f} {middle_savings_pct:<9.1f}%"
            )

    print("-" * 70)
    if total_actual_cost > 0:
        total_savings_pct = total_extra_cost / total_actual_cost * 100
        print(
            f"{'TOTAL POTENTIAL SAVINGS':<25} {len(events):<8} ${total_extra_cost:<11.3f} ${total_actual_cost:<11.3f} {total_savings_pct:<9.1f}%"
        )

    # Key insights
    if total_extra_cost > 0:
        print("\n💡 Key Optimization Opportunities:")
        if expiration_extra_cost > 0:
            print(
                f"   • Cache expiration fixes could save: ${expiration_extra_cost:.3f}"
            )
        if alteration_extra_cost > 0:
            print(
                f"   • Chat history alteration fixes could save: ${alteration_extra_cost:.3f}"
            )
        print(
            f"   • Total potential savings: ${total_extra_cost:.3f} ({total_savings_pct:.1f}% of affected request costs)"
        )


def analyze_client_type_breakdown(events: List[CacheInvalidationEvent]) -> None:
    """Analyze cache invalidation patterns by client type."""
    if not events:
        return

    print(f"\n{'='*80}")
    print("CLIENT TYPE BREAKDOWN")
    print(f"{'='*80}")

    # Group events by client type
    events_by_client = defaultdict(list)
    for event in events:
        events_by_client[event.client_type].append(event)

    # Calculate totals for percentage calculations
    total_events = len(events)
    total_alteration_cost = sum(
        e.extra_cost
        for e in events
        if e.invalidation_type in ["alteration_beginning", "alteration_middle"]
    )
    total_actual_cost = sum(
        e.actual_cost
        for e in events
        if e.invalidation_type in ["alteration_beginning", "alteration_middle"]
    )

    print(
        f"\n{'Client Type':<12} {'Events':<8} {'%':<6} {'Alteration':<12} {'Extra Cost':<12} {'Actual Cost':<12} {'Savings %':<10}"
    )
    print("-" * 85)

    # Sort by number of events
    sorted_clients = sorted(
        events_by_client.items(), key=lambda x: len(x[1]), reverse=True
    )

    for client_type, client_events in sorted_clients:
        event_count = len(client_events)
        event_percentage = (event_count / total_events) * 100

        # Focus on alteration events for cost analysis
        alteration_events = [
            e
            for e in client_events
            if e.invalidation_type in ["alteration_beginning", "alteration_middle"]
        ]
        alteration_count = len(alteration_events)

        if alteration_events:
            client_extra_cost = sum(e.extra_cost for e in alteration_events)
            client_actual_cost = sum(e.actual_cost for e in alteration_events)

            # Calculate percentage savings if invalidation was fixed
            savings_percentage = (
                (client_extra_cost / client_actual_cost * 100)
                if client_actual_cost > 0
                else 0.0
            )
        else:
            client_extra_cost = 0.0
            client_actual_cost = 0.0
            savings_percentage = 0.0

        print(
            f"{client_type:<12} {event_count:<8} {event_percentage:<5.1f}% {alteration_count:<12} ${client_extra_cost:<11.3f} ${client_actual_cost:<11.3f} {savings_percentage:<9.1f}%"
        )

    print("-" * 85)

    # Overall savings percentage
    overall_savings_percentage = (
        (total_alteration_cost / total_actual_cost * 100)
        if total_actual_cost > 0
        else 0.0
    )
    print(
        f"{'TOTAL':<12} {total_events:<8} {'100.0%':<6} {len([e for e in events if e.invalidation_type in ['alteration_beginning', 'alteration_middle']]):<12} ${total_alteration_cost:<11.3f} ${total_actual_cost:<11.3f} {overall_savings_percentage:<9.1f}%"
    )

    if total_alteration_cost > 0:
        print("\n💡 Key Insights:")
        print(
            f"   • Overall potential cost savings: {overall_savings_percentage:.1f}% if alteration invalidations are fixed"
        )
        print(f"   • Total extra cost from alterations: ${total_alteration_cost:.3f}")

        # Find client with highest impact
        if alteration_events:
            client_costs = {}
            for client_type, client_events in events_by_client.items():
                alt_events = [
                    e
                    for e in client_events
                    if e.invalidation_type
                    in ["alteration_beginning", "alteration_middle"]
                ]
                if alt_events:
                    client_costs[client_type] = sum(e.extra_cost for e in alt_events)

            if client_costs:
                top_client = max(client_costs.items(), key=lambda x: x[1])
                print(
                    f"   • Highest impact client: {top_client[0]} (${top_client[1]:.3f} extra cost)"
                )


def analyze_expiration_events(events: List[CacheInvalidationEvent]) -> None:
    """Analyze cache expiration events."""
    if not events:
        return

    print(f"\n{'='*60}")
    print("CACHE EXPIRATION ANALYSIS")
    print(f"{'='*60}")

    print(f"Total expiration events: {len(events)}")

    # Cost analysis
    extra_costs = [e.extra_cost for e in events]
    total_extra_cost = sum(extra_costs)

    print("\nCost impact from expiration:")
    print(f"  • Total extra cost: ${total_extra_cost:.2f}")
    if extra_costs:
        print(
            f"  • Average extra cost per event: ${sum(extra_costs)/len(extra_costs):.4f}"
        )
        print(f"  • Median extra cost: ${sorted(extra_costs)[len(extra_costs)//2]:.4f}")

    # Time gap analysis
    time_gaps = [
        (event.time - event.prev_time).total_seconds() / 60 for event in events
    ]

    print("\nTime gap statistics (minutes):")
    print(f"  • Average: {sum(time_gaps)/len(time_gaps):.1f}")
    print(f"  • Median: {sorted(time_gaps)[len(time_gaps)//2]:.1f}")
    print(f"  • Min: {min(time_gaps):.1f}")
    print(f"  • Max: {max(time_gaps):.1f}")

    # Distribution of time gaps
    gap_ranges = [(5, 15), (15, 60), (60, 240), (240, float("inf"))]
    print("\nTime gap distribution:")
    for min_gap, max_gap in gap_ranges:
        count = sum(1 for gap in time_gaps if min_gap <= gap < max_gap)
        range_str = f"{min_gap}-{max_gap if max_gap != float('inf') else '∞'} min"
        print(f"  • {range_str:<12}: {count:3d} ({count/len(events)*100:.1f}%)")

    # Show top expensive expiration events
    if events and total_extra_cost > 0:
        events_sorted = sorted(events, key=lambda x: x.extra_cost, reverse=True)
        print("\nTop 5 most expensive expiration events:")
        print(
            f"{'Conversation ID':<40} {'Extra Cost':<12} {'Time Gap':<10} {'Prev Cache':<12}"
        )
        print("-" * 75)

        for event in events_sorted[:5]:
            time_gap = (event.time - event.prev_time).total_seconds() / 60
            prev_cached = event.prev_cache_reads
            print(
                f"{event.conversation_id:<40} ${event.extra_cost:<11.4f} {time_gap:<9.1f}m {prev_cached:<12}"
            )


def analyze_truncation_events(events: List[CacheInvalidationEvent]) -> None:
    """Analyze chat history truncation events."""
    if not events:
        return

    print(f"\n{'='*60}")
    print("CHAT HISTORY TRUNCATION ANALYSIS")
    print(f"{'='*60}")

    print(f"Total truncation events: {len(events)}")

    # Token reduction analysis
    token_reductions = [
        event.prev_input_tokens - event.curr_input_tokens for event in events
    ]
    cache_reductions = [
        event.prev_cache_reads - event.curr_cache_reads for event in events
    ]

    print("\nToken reduction statistics:")
    print(
        f"  • Average tokens reduced: {sum(token_reductions)/len(token_reductions):.0f}"
    )
    print(
        f"  • Median tokens reduced: {sorted(token_reductions)[len(token_reductions)//2]:.0f}"
    )
    print(f"  • Max tokens reduced: {max(token_reductions)}")

    print("\nCache reads reduction statistics:")
    print(
        f"  • Average cache reads lost: {sum(cache_reductions)/len(cache_reductions):.0f}"
    )
    print(
        f"  • Median cache reads lost: {sorted(cache_reductions)[len(cache_reductions)//2]:.0f}"
    )
    print(f"  • Max cache reads lost: {max(cache_reductions)}")


def analyze_alteration_events(
    beginning_events: List[CacheInvalidationEvent],
    middle_events: List[CacheInvalidationEvent],
) -> None:
    """Analyze chat history alteration events (most important for cost optimization)."""
    all_events = beginning_events + middle_events

    if not all_events:
        return

    print(f"\n{'='*60}")
    print("CHAT HISTORY ALTERATION ANALYSIS")
    print(f"{'='*60}")

    print(f"Total alteration invalidation events: {len(all_events)}")
    print(
        f"  • Beginning alterations: {len(beginning_events)} ({len(beginning_events)/len(all_events)*100:.1f}%)"
    )
    print(
        f"  • Middle alterations: {len(middle_events)} ({len(middle_events)/len(all_events)*100:.1f}%)"
    )

    # Sort by extra cost
    all_events.sort(key=lambda x: x.extra_cost, reverse=True)

    print("\nTop 10 most expensive alteration invalidations:")
    print(
        f"{'Conversation ID':<40} {'Type':<10} {'Extra Cost':<12} {'Cache Lost':<12} {'Token Δ':<10} {'Time Gap':<10}"
    )
    print("-" * 90)

    for event in all_events[:10]:
        cache_reads_lost = event.prev_cache_reads - event.curr_cache_reads
        token_change = event.curr_input_tokens - event.prev_input_tokens
        time_gap = (event.time - event.prev_time).total_seconds() / 60
        event_type = (
            "beginning"
            if event.invalidation_type == "alteration_beginning"
            else "middle"
        )

        print(
            f"{event.conversation_id:<40} {event_type:<10} ${event.extra_cost:<11.4f} {cache_reads_lost:<12} +{token_change:<9} {time_gap:<9.1f}m"
        )

    # Statistics for all events
    all_extra_costs = [e.extra_cost for e in all_events]
    all_cache_reads_lost = [e.prev_cache_reads - e.curr_cache_reads for e in all_events]
    all_token_increases = [
        e.curr_input_tokens - e.prev_input_tokens for e in all_events
    ]

    print("\nOverall alteration invalidation statistics:")
    print(f"  • Total extra cost: ${sum(all_extra_costs):.2f}")
    print(
        f"  • Average extra cost per event: ${sum(all_extra_costs)/len(all_extra_costs):.4f}"
    )
    print(
        f"  • Median extra cost: ${sorted(all_extra_costs)[len(all_extra_costs)//2]:.4f}"
    )
    print(
        f"  • 95th percentile cost: ${sorted(all_extra_costs)[int(len(all_extra_costs)*0.95)]:.4f}"
    )
    print(
        f"  • Average cache reads lost: {sum(all_cache_reads_lost)/len(all_cache_reads_lost):.0f}"
    )
    print(
        f"  • Average token increase: {sum(all_token_increases)/len(all_token_increases):.0f}"
    )

    # Separate statistics for each type
    if beginning_events:
        beginning_costs = [e.extra_cost for e in beginning_events]
        beginning_cache_lost = [
            e.prev_cache_reads - e.curr_cache_reads for e in beginning_events
        ]
        beginning_token_increases = [
            e.curr_input_tokens - e.prev_input_tokens for e in beginning_events
        ]

        print("\nBeginning alteration statistics:")
        print(f"  • Total extra cost: ${sum(beginning_costs):.2f}")
        print(
            f"  • Average extra cost per event: ${sum(beginning_costs)/len(beginning_costs):.4f}"
        )
        print(
            f"  • Average cache reads lost: {sum(beginning_cache_lost)/len(beginning_cache_lost):.0f}"
        )
        print(
            f"  • Average token increase: {sum(beginning_token_increases)/len(beginning_token_increases):.0f}"
        )

    if middle_events:
        middle_costs = [e.extra_cost for e in middle_events]
        middle_cache_lost = [
            e.prev_cache_reads - e.curr_cache_reads for e in middle_events
        ]
        middle_token_increases = [
            e.curr_input_tokens - e.prev_input_tokens for e in middle_events
        ]

        print("\nMiddle alteration statistics:")
        print(f"  • Total extra cost: ${sum(middle_costs):.2f}")
        print(
            f"  • Average extra cost per event: ${sum(middle_costs)/len(middle_costs):.4f}"
        )
        print(
            f"  • Average cache reads lost: {sum(middle_cache_lost)/len(middle_cache_lost):.0f}"
        )
        print(
            f"  • Average token increase: {sum(middle_token_increases)/len(middle_token_increases):.0f}"
        )

    # Cost distribution
    cost_ranges = [(0, 0.001), (0.001, 0.01), (0.01, 0.1), (0.1, float("inf"))]
    print("\nExtra cost distribution:")
    for min_cost, max_cost in cost_ranges:
        count = sum(1 for cost in all_extra_costs if min_cost <= cost < max_cost)
        range_str = f"${min_cost}-${max_cost if max_cost != float('inf') else '∞'}"
        print(f"  • {range_str:<15}: {count:3d} ({count/len(all_events)*100:.1f}%)")


def analyze_conversation_patterns(events: List[CacheInvalidationEvent]) -> None:
    """Analyze patterns at the conversation level."""
    print(f"\n{'='*60}")
    print("CONVERSATION-LEVEL ANALYSIS")
    print(f"{'='*60}")

    # Group events by conversation
    events_by_conv = defaultdict(list)
    for event in events:
        events_by_conv[event.conversation_id].append(event)

    # Analyze conversations with multiple invalidations
    multi_invalidation_convs = {
        conv_id: events for conv_id, events in events_by_conv.items() if len(events) > 1
    }

    print(f"Conversations with multiple invalidations: {len(multi_invalidation_convs)}")
    print(
        f"Average invalidations per affected conversation: {len(events)/len(events_by_conv):.1f}"
    )

    if multi_invalidation_convs:
        print("\nTop 5 conversations by invalidation count:")
        sorted_convs = sorted(
            multi_invalidation_convs.items(), key=lambda x: len(x[1]), reverse=True
        )

        for conv_id, conv_events in sorted_convs[:5]:
            total_extra_cost = sum(e.extra_cost for e in conv_events)
            types = [e.invalidation_type for e in conv_events]
            type_counts = {t: types.count(t) for t in set(types)}
            type_str = ", ".join(f"{t}:{c}" for t, c in type_counts.items())

            print(
                f"  • {conv_id:<40} {len(conv_events):2d} events (${total_extra_cost:.3f}) [{type_str}]"
            )


def print_summary(
    conversations: Dict[str, List[ConversationRequest]],
    events: List[CacheInvalidationEvent],
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> None:
    """Print a high-level summary of the analysis."""
    if not events:
        return

    # Separate expiration and alteration events
    expiration_events = [e for e in events if e.invalidation_type == "expiration"]
    alteration_events = [
        e
        for e in events
        if e.invalidation_type in ["alteration_beginning", "alteration_middle"]
    ]

    total_expiration_extra_cost = sum(e.extra_cost for e in expiration_events)
    total_alteration_extra_cost = sum(e.extra_cost for e in alteration_events)
    total_extra_cost = total_expiration_extra_cost + total_alteration_extra_cost

    # Calculate total actual cost only for requests within the time window
    total_actual_cost = 0.0
    for conversation_requests in conversations.values():
        for request in conversation_requests:
            # Only include requests within the specified time window
            if is_datetime_in_range(request.time, from_datetime, to_datetime):
                total_actual_cost += request.actual_cost

    print(f"\n{'='*80}")
    print("EXECUTIVE SUMMARY")
    print(f"{'='*80}")

    print("📊 Cache Invalidation Impact Analysis")
    print(f"   • Total invalidation events detected: {len(events)}")
    print(f"   • Conversations affected: {len(set(e.conversation_id for e in events))}")

    # Show cost impact from both expiration and alteration events
    cost_causing_events = expiration_events + alteration_events
    if cost_causing_events:
        # Calculate percentage savings
        total_savings_percentage = (
            (total_extra_cost / total_actual_cost * 100)
            if total_actual_cost > 0
            else 0.0
        )

        print("\n💰 Cost Impact from Cache Invalidations:")
        print(f"   • Events causing extra cost: {len(cost_causing_events)}")
        print(f"   • Total extra cost: ${total_extra_cost:.2f}")
        print(f"   • Total actual cost: ${total_actual_cost:.2f}")
        print(
            f"   • Average cost per invalidation: ${total_extra_cost/len(cost_causing_events):.4f}"
        )
        print(
            f"   • Potential cost savings: {total_savings_percentage:.1f}% if invalidations are fixed"
        )

        # Breakdown by type
        if expiration_events and total_expiration_extra_cost > 0:
            print("\n   📅 Cache Expiration Impact:")
            print(f"      • Events: {len(expiration_events)}")
            print(f"      • Extra cost: ${total_expiration_extra_cost:.2f}")

        if alteration_events and total_alteration_extra_cost > 0:
            print("\n   ✏️  Chat History Alteration Impact:")
            print(f"      • Events: {len(alteration_events)}")
            print(f"      • Extra cost: ${total_alteration_extra_cost:.2f}")

        # Estimate potential savings
        if total_extra_cost > 0.01:
            print("\n🎯 Optimization Opportunities:")
            if total_expiration_extra_cost > 0:
                print(
                    f"   • Preventing cache expiration could save: ${total_expiration_extra_cost:.2f}"
                )
            if total_alteration_extra_cost > 0:
                print(
                    f"   • Preventing chat history alterations could save: ${total_alteration_extra_cost:.2f}"
                )
            print(
                f"   • Total potential savings: ${total_extra_cost:.2f} ({total_savings_percentage:.1f}%)"
            )

    # Breakdown by type
    type_counts = {}
    for event in events:
        type_counts[event.invalidation_type] = (
            type_counts.get(event.invalidation_type, 0) + 1
        )

    print("\n📈 Invalidation Type Breakdown:")
    for inv_type, count in sorted(type_counts.items()):
        percentage = (count / len(events)) * 100
        print(f"   • {inv_type.capitalize()}: {count} events ({percentage:.1f}%)")


def parse_datetime_with_timezone(date_string: str) -> datetime.datetime:
    """
    Parse a datetime string that must include timezone information.

    Args:
        date_string: ISO format datetime string with timezone
                    (e.g., "2024-01-01T00:00:00+00:00" or "2024-01-01T00:00:00Z")

    Returns:
        Timezone-aware datetime object

    Raises:
        ValueError: If the datetime string doesn't include timezone information
    """
    try:
        dt = datetime.datetime.fromisoformat(date_string)
        if dt.tzinfo is None:
            raise ValueError(
                f"Datetime string '{date_string}' must include timezone information. "
                f"Use format like '2024-01-01T00:00:00+00:00' or '2024-01-01T00:00:00Z'"
            )
        return dt
    except ValueError as e:
        if "timezone information" in str(e):
            raise  # Re-raise our custom error
        else:
            # Re-raise with more helpful message for parsing errors
            raise ValueError(
                f"Invalid datetime format '{date_string}'. "
                f"Use ISO format with timezone like '2024-01-01T00:00:00+00:00' or '2024-01-01T00:00:00Z'"
            ) from e


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze cache invalidation patterns in conversation data"
    )

    # Dataset selection
    dataset_group = parser.add_mutually_exclusive_group()
    dataset_group.add_argument(
        "--prod",
        action="store_true",
        help="Use production dataset (us_prod_request_insight_analytics_dataset)",
    )
    dataset_group.add_argument(
        "--staging",
        action="store_true",
        help="Use staging dataset (us_staging_request_insight_analytics_dataset)",
    )

    parser.add_argument(
        "--tenant",
        type=str,
        default=None,
        help=f"Tenant name to analyze. Available: {list(DATASET_TENANTS.keys())}",
    )

    parser.add_argument(
        "--num-conversations",
        type=int,
        default=100,
        help="Number of conversations to sample randomly (default: 100)",
    )

    # Date/time filtering options
    date_group = parser.add_mutually_exclusive_group()
    date_group.add_argument(
        "--from-date",
        type=parse_datetime_with_timezone,
        default=None,
        help="Start date in ISO format with timezone (YYYY-MM-DDTHH:MM:SS+HH:MM or YYYY-MM-DDTHH:MM:SSZ)",
    )
    date_group.add_argument(
        "-lh",
        "--last-hours",
        type=int,
        default=None,
        help="Process data from the last N hours",
    )
    date_group.add_argument(
        "-ld",
        "--last-days",
        type=int,
        default=None,
        help="Process data from the last N days",
    )
    parser.add_argument(
        "--to-date",
        type=parse_datetime_with_timezone,
        default=None,
        help="End date in ISO format with timezone (YYYY-MM-DDTHH:MM:SS+HH:MM or YYYY-MM-DDTHH:MM:SSZ). Default: now",
    )

    return parser.parse_args()


def get_time_range(
    from_date: Optional[datetime.datetime] = None,
    to_date: Optional[datetime.datetime] = None,
    last_hours: Optional[int] = None,
    last_days: Optional[int] = None,
) -> Tuple[datetime.datetime, datetime.datetime]:
    """Get the time range for the query. Returns timezone-aware datetime objects."""
    # Use timezone-aware UTC datetime
    now = datetime.datetime.now(timezone.utc)

    # Handle explicit date range (these should already be timezone-aware from CLI parsing)
    if from_date is not None:
        from_datetime = from_date
        to_datetime = to_date if to_date is not None else now
        return from_datetime, to_datetime

    if last_hours is not None:
        from_datetime = now - timedelta(hours=last_hours)
        to_datetime = to_date if to_date is not None else now
        return from_datetime, to_datetime

    if last_days is not None:
        from_datetime = now - timedelta(days=last_days)
        to_datetime = to_date if to_date is not None else now
        return from_datetime, to_datetime

    # Default case: use last 24 hours if no time range specified
    from_datetime = now - timedelta(hours=24)
    to_datetime = to_date if to_date is not None else now
    return from_datetime, to_datetime


def main():
    """Main function to orchestrate the cache invalidation analysis."""
    args = parse_arguments()

    # Get time range
    from_datetime, to_datetime = get_time_range(
        from_date=args.from_date,
        to_date=args.to_date,
        last_hours=args.last_hours,
        last_days=args.last_days,
    )
    logger.info(f"Analyzing data from {from_datetime} to {to_datetime}")

    # Determine dataset configuration
    if args.prod:
        assert args.tenant is None, "Cannot specify both --tenant and --prod options"
        assert args.staging is False, "Cannot specify both --staging and --prod options"
        project_id = "system-services-prod"
        dataset_name = "us_prod_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using production dataset for all tenants")
    elif args.staging:
        assert args.tenant is None, "Cannot specify both --tenant and --prod options"
        assert args.prod is False, "Cannot specify both --staging and --prod options"
        project_id = "system-services-prod"
        dataset_name = "us_staging_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using staging dataset for all tenants")
    elif args.tenant:
        assert args.prod is False, "Cannot specify both --tenant and --prod options"
        assert (
            args.staging is False
        ), "Cannot specify both --tenant and --staging options"
        tenant = get_tenant(args.tenant)
        project_id = tenant.project_id
        dataset_name = tenant.analytics_dataset_name
        tenant_filter = tenant.name
        logger.info(f"Analyzing specific tenant: {args.tenant}")
    else:
        # Default: use production dataset
        project_id = "system-services-prod"
        dataset_name = "us_prod_request_insight_analytics_dataset"
        tenant_filter = None
        logger.info("Using production dataset for all tenants")

    try:
        # Fetch conversation data with cache information
        conversations = fetch_conversations_with_cache_data(
            project_id=project_id,
            dataset_name=dataset_name,
            from_datetime=from_datetime,
            to_datetime=to_datetime,
            tenant_filter=tenant_filter,
            num_conversations=args.num_conversations,
        )

        if not conversations:
            logger.warning("No conversations found")
            return

        # Detect cache invalidation events
        invalidation_events = detect_cache_invalidation(
            conversations, from_datetime, to_datetime
        )

        # Print executive summary first
        print_summary(conversations, invalidation_events, from_datetime, to_datetime)

        # Analyze and report detailed patterns
        analyze_invalidation_patterns(invalidation_events)

    except Exception as e:
        logger.error(f"Error during analysis: {e}")
        raise


if __name__ == "__main__":
    main()

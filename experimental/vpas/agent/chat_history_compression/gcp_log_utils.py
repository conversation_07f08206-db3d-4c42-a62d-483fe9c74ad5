"""
Google Cloud Platform logging utilities for analyzing client usage patterns.

This module provides functions to query GCP logs and extract information about
client attempts, successes, and failures for request routing analysis.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, NamedTuple
import re

from google.cloud import logging as gcp_logging
from base.datasets.gcp_creds import get_gcp_creds

logger = logging.getLogger(__name__)


class ClientAttempt(NamedTuple):
    """Represents a single client attempt for a request."""

    timestamp: datetime
    request_id: str
    client_name: str
    attempt_number: int
    is_success: bool
    is_fallback: bool


class RequestClientInfo(NamedTuple):
    """Aggregated client information for a single request."""

    request_id: str
    attempted_clients: List[str]  # In order of attempts
    successful_client: Optional[str]
    total_attempts: int
    had_fallbacks: bool


def create_time_based_batches(
    requests_with_timestamps: List[tuple],
    max_time_window_hours: float = 1.0,
    max_batch_size: int = 100,
) -> List[List[tuple]]:
    """
    Create batches of requests based on timestamp proximity.

    Args:
        requests_with_timestamps: List of (request_id, timestamp) tuples
        max_time_window_hours: Maximum time window for a batch in hours
        max_batch_size: Maximum number of requests per batch

    Returns:
        List of batches, where each batch is a list of (request_id, timestamp) tuples
    """
    if not requests_with_timestamps:
        return []

    # Sort by timestamp
    sorted_requests = sorted(requests_with_timestamps, key=lambda x: x[1])

    batches = []
    current_batch = []
    batch_start_time = None
    max_window = timedelta(hours=max_time_window_hours)

    for request_id, timestamp in sorted_requests:
        # Start new batch if this is the first request
        if not current_batch:
            current_batch = [(request_id, timestamp)]
            batch_start_time = timestamp
            continue

        # Check if we should start a new batch
        time_since_batch_start = timestamp - batch_start_time
        should_start_new_batch = (
            time_since_batch_start > max_window or len(current_batch) >= max_batch_size
        )

        if should_start_new_batch:
            # Finish current batch and start new one
            batches.append(current_batch)
            current_batch = [(request_id, timestamp)]
            batch_start_time = timestamp
        else:
            # Add to current batch
            current_batch.append((request_id, timestamp))

    # Add the last batch if it has any requests
    if current_batch:
        batches.append(current_batch)

    return batches


def query_client_logs_optimized(
    requests_with_timestamps: List[tuple],
    project_id: str = "system-services-prod",
    namespace: str = "i0",
    time_buffer_minutes: int = 10,
    max_time_window_hours: float = 1.0,
    max_batch_size: int = 100,
) -> List[ClientAttempt]:
    """
    Query Google Cloud logs using time-based batching for optimal performance.

    Args:
        requests_with_timestamps: List of (request_id, timestamp) tuples
        project_id: GCP project ID
        namespace: Kubernetes namespace
        time_buffer_minutes: Buffer in minutes to add before/after each batch time window
        max_time_window_hours: Maximum time window for a batch in hours
        max_batch_size: Maximum number of requests per batch

    Returns:
        List of ClientAttempt objects with timing and success information
    """
    if not requests_with_timestamps:
        return []

    logger.info(
        f"Querying logs for {len(requests_with_timestamps)} requests using time-based batching"
    )

    # Create time-based batches
    batches = create_time_based_batches(
        requests_with_timestamps,
        max_time_window_hours=max_time_window_hours,
        max_batch_size=max_batch_size,
    )

    logger.info(
        f"Created {len(batches)} time-based batches (max {max_time_window_hours}h window, max {max_batch_size} requests per batch)"
    )

    # Get GCP credentials
    gcp_creds, _ = get_gcp_creds(None)

    # Create logging client
    client = gcp_logging.Client(project=project_id, credentials=gcp_creds)

    all_attempts = []

    # Process each time-based batch
    for i, batch in enumerate(batches):
        batch_request_ids = [req_id for req_id, _ in batch]
        batch_timestamps = [timestamp for _, timestamp in batch]

        batch_start = min(batch_timestamps)
        batch_end = max(batch_timestamps)

        logger.info(
            f"Processing time-based batch {i+1}/{len(batches)}: "
            f"{len(batch_request_ids)} requests from "
            f"{batch_start.strftime('%Y-%m-%d %H:%M:%S')} to "
            f"{batch_end.strftime('%Y-%m-%d %H:%M:%S')}"
        )

        try:
            batch_attempts = _query_client_logs_time_batch(
                client,
                batch_request_ids,
                batch_start,
                batch_end,
                project_id,
                namespace,
                time_buffer_minutes,
            )
            all_attempts.extend(batch_attempts)
            logger.info(f"Batch {i+1} returned {len(batch_attempts)} client attempts")
        except Exception as e:
            logger.warning(f"Failed to query time-based batch {i+1}: {e}")
            continue

    logger.info(
        f"Found {len(all_attempts)} total client attempts across all time-based batches"
    )
    return all_attempts


def query_client_logs(
    request_ids: List[str],
    project_id: str = "system-services-prod",
    batch_size: int = 50,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    time_buffer_minutes: int = 10,
) -> List[ClientAttempt]:
    """
    Query Google Cloud logs for client usage information.

    Args:
        request_ids: List of request IDs to query for
        project_id: GCP project ID
        namespace: Kubernetes namespace
        batch_size: Number of request IDs to include in each query batch
        start_time: Start time for log search (optional)
        end_time: End time for log search (optional)
        time_buffer_minutes: Buffer in minutes to add before start_time and after end_time

    Returns:
        List of ClientAttempt objects with timing and success information
    """
    if not request_ids:
        return []

    # Calculate time window if provided
    time_filter = ""
    if start_time and end_time:
        # Add buffer to the time window
        buffer_delta = timedelta(minutes=time_buffer_minutes)
        search_start = start_time - buffer_delta
        search_end = end_time + buffer_delta

        # Format times for GCP logging (RFC3339 format)
        start_str = search_start.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        end_str = search_end.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        time_filter = f'timestamp>="{start_str}" AND timestamp<="{end_str}"'

        logger.info(
            f"Using time window: {search_start.strftime('%Y-%m-%d %H:%M:%S')} to {search_end.strftime('%Y-%m-%d %H:%M:%S')} "
            f"(original: {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')} "
            f"+ {time_buffer_minutes}min buffer)"
        )

    logger.info(
        f"Querying logs for {len(request_ids)} request IDs in batches of {batch_size}"
    )

    # Get GCP credentials
    gcp_creds, _ = get_gcp_creds(None)

    # Create logging client
    client = gcp_logging.Client(project=project_id, credentials=gcp_creds)

    all_attempts = []

    # Process request IDs in batches to avoid query length limits
    for i in range(0, len(request_ids), batch_size):
        batch_request_ids = request_ids[i : i + batch_size]
        logger.info(
            f"Processing batch {i//batch_size + 1}/{(len(request_ids) + batch_size - 1)//batch_size} ({len(batch_request_ids)} request IDs)"
        )

        try:
            batch_attempts = _query_client_logs_batch(
                client, batch_request_ids, project_id, time_filter
            )
            all_attempts.extend(batch_attempts)
        except Exception as e:
            logger.warning(f"Failed to query batch {i//batch_size + 1}: {e}")
            continue

    logger.info(f"Found {len(all_attempts)} client attempts in logs across all batches")
    return all_attempts


def _query_client_logs_batch(
    client,
    request_ids: List[str],
    project_id: str,
    time_filter: str = "",
) -> List[ClientAttempt]:
    """Query a single batch of request IDs."""
    # Build the LQL query for this batch
    request_id_conditions = []
    for request_id in request_ids:
        request_id_conditions.append(f'jsonPayload.request_id="{request_id}"')

    request_id_filter = "(\n" + "\nOR\n".join(request_id_conditions) + "\n)"

    query_parts = []

    # Add time filter if provided
    if time_filter:
        query_parts.append(time_filter)

    # Build the base query
    query_parts.extend(
        [
            'resource.type="k8s_container"',
            'log_name="projects/system-services-prod/logs/stderr"',
            'severity="INFO"',
            f'resource.labels.project_id="{project_id}"',
            request_id_filter,
        ]
    )

    # Add message filter
    message_filter = """(
jsonPayload.message:"Using client: ANTHROPIC_"
OR
jsonPayload.message:"Falling back to"
OR
jsonPayload.message:"Reporting success to arbiter"
)"""
    query_parts.append(message_filter)

    lql_query = "\nAND\n".join(query_parts)

    # Estimate query length and warn if still too long
    query_length = len(lql_query)
    if query_length > 18000:  # Leave some buffer
        logger.warning(
            f"Query length {query_length} is close to limit, consider reducing batch size"
        )

    logger.debug(f"LQL Query length: {query_length} characters")

    # Query logs
    entries = client.list_entries(
        filter_=lql_query,
        order_by=gcp_logging.ASCENDING,  # Chronological order
        max_results=5000,  # Limit per batch
    )

    # Parse log entries
    attempts = []
    for entry in entries:
        try:
            attempt = _parse_log_entry(entry)
            if attempt:
                attempts.append(attempt)
        except Exception as e:
            logger.warning(f"Failed to parse log entry: {e}")
            continue

    return attempts


def _query_client_logs_time_batch(
    client,
    request_ids: List[str],
    batch_start: datetime,
    batch_end: datetime,
    project_id: str,
    namespace: str,
    time_buffer_minutes: int,
) -> List[ClientAttempt]:
    """Query a single time-based batch of request IDs with optimized time filtering."""
    # Add buffer to the time window
    buffer_delta = timedelta(minutes=time_buffer_minutes)
    search_start = batch_start - buffer_delta
    search_end = batch_end + buffer_delta

    # Format times for GCP logging (RFC3339 format)
    start_str = search_start.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    end_str = search_end.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    time_filter = f'timestamp>="{start_str}" AND timestamp<="{end_str}"'

    # Build the LQL query for this batch
    request_id_conditions = []
    for request_id in request_ids:
        request_id_conditions.append(f'jsonPayload.request_id="{request_id}"')

    request_id_filter = "(\n" + "\nOR\n".join(request_id_conditions) + "\n)"

    # Build the query with time filter first for optimal performance
    query_parts = [
        time_filter,  # Time filter first for index optimization
        f'resource.labels.namespace_name="{namespace}"',
        f'resource.labels.project_id="{project_id}"',
        'resource.type="k8s_container"',
        'severity="INFO"',
        request_id_filter,
    ]

    # Add message filter
    message_filter = """(
jsonPayload.message:"Using client: ANTHROPIC_"
OR
jsonPayload.message:"Falling back to"
OR
jsonPayload.message:"Reporting success to arbiter"
)"""
    query_parts.append(message_filter)

    lql_query = "\nAND\n".join(query_parts)

    # Estimate query length and warn if still too long
    query_length = len(lql_query)
    if query_length > 18000:  # Leave some buffer
        logger.warning(
            f"Query length {query_length} is close to limit, consider reducing batch size"
        )

    logger.debug(f"Time-based LQL Query length: {query_length} characters")

    # Query logs
    entries = client.list_entries(
        filter_=lql_query,
        order_by=gcp_logging.ASCENDING,  # Chronological order
        max_results=5000,  # Limit per batch
    )

    # Parse log entries
    attempts = []
    for entry in entries:
        try:
            attempt = _parse_log_entry(entry)
            if attempt:
                attempts.append(attempt)
        except Exception as e:
            logger.warning(f"Failed to parse log entry: {e}")
            continue

    return attempts


def _parse_log_entry(entry) -> Optional[ClientAttempt]:
    """Parse a single log entry into a ClientAttempt."""
    try:
        # Extract basic info
        timestamp = entry.timestamp
        payload = entry.payload

        if not isinstance(payload, dict):
            return None

        request_id = payload.get("request_id")
        message = payload.get("message", "")

        if not request_id or not message:
            return None

        # Parse different message types
        if "Using client: ANTHROPIC_" in message:
            # Extract client name and attempt number
            # Example: "Using client: ANTHROPIC_VERTEXAI_EU_W1 (attempt 1/3)"
            match = re.search(
                r"Using client: (ANTHROPIC_[A-Z0-9_]+) \(attempt (\d+)/\d+\)", message
            )
            if match:
                client_name = match.group(1)
                attempt_number = int(match.group(2))
                return ClientAttempt(
                    timestamp=timestamp,
                    request_id=request_id,
                    client_name=client_name,
                    attempt_number=attempt_number,
                    is_success=False,  # Will be updated if we see success message
                    is_fallback=False,
                )

        elif "Falling back to" in message:
            # Extract client that failed
            # Example: "Falling back to next target after failure with ANTHROPIC_VERTEXAI_EU_W1"
            match = re.search(
                r"Falling back to next target after failure with (ANTHROPIC_[A-Z0-9_]+)",
                message,
            )
            if match:
                client_name = match.group(1)
                return ClientAttempt(
                    timestamp=timestamp,
                    request_id=request_id,
                    client_name=client_name,
                    attempt_number=0,  # Will be updated based on "Using client" messages
                    is_success=False,
                    is_fallback=True,
                )

        elif "Reporting success to arbiter" in message:
            # Extract successful client
            # Example: "Reporting success to arbiter for target ANTHROPIC_VERTEXAI_AS_SE1 messages=1"
            match = re.search(
                r"Reporting success to arbiter for target (ANTHROPIC_[A-Z0-9_]+)",
                message,
            )
            if match:
                client_name = match.group(1)
                return ClientAttempt(
                    timestamp=timestamp,
                    request_id=request_id,
                    client_name=client_name,
                    attempt_number=0,  # Will be updated based on context
                    is_success=True,
                    is_fallback=False,
                )

    except Exception as e:
        logger.warning(f"Error parsing log entry: {e}")
        return None

    return None


def aggregate_client_info(
    attempts: List[ClientAttempt],
) -> Dict[str, RequestClientInfo]:
    """
    Aggregate client attempts by request ID to get complete picture.

    Args:
        attempts: List of ClientAttempt objects

    Returns:
        Dictionary mapping request_id to RequestClientInfo
    """
    # Group attempts by request ID
    by_request = {}
    for attempt in attempts:
        if attempt.request_id not in by_request:
            by_request[attempt.request_id] = []
        by_request[attempt.request_id].append(attempt)

    # Process each request's attempts
    result = {}
    for request_id, request_attempts in by_request.items():
        # Sort by timestamp to get chronological order
        request_attempts.sort(key=lambda x: x.timestamp)

        # Find attempted clients in order
        attempted_clients = []
        successful_client = None
        had_fallbacks = False

        # Track clients we've seen "Using client" messages for
        using_messages = [a for a in request_attempts if a.attempt_number > 0]
        fallback_messages = [a for a in request_attempts if a.is_fallback]
        success_messages = [a for a in request_attempts if a.is_success]

        # Build attempted clients list from "Using client" messages
        for attempt in sorted(using_messages, key=lambda x: x.attempt_number):
            if attempt.client_name not in attempted_clients:
                attempted_clients.append(attempt.client_name)

        # Find successful client
        if success_messages:
            successful_client = success_messages[-1].client_name  # Last success message

        # Check for fallbacks
        had_fallbacks = len(fallback_messages) > 0

        result[request_id] = RequestClientInfo(
            request_id=request_id,
            attempted_clients=attempted_clients,
            successful_client=successful_client,
            total_attempts=len(using_messages),
            had_fallbacks=had_fallbacks,
        )

    return result


def analyze_client_switching_patterns(
    client_info_by_request: Dict[str, RequestClientInfo], request_order: List[str]
) -> Dict[str, any]:
    """
    Analyze client switching patterns and cache invalidation due to client changes.

    Args:
        client_info_by_request: Mapping of request_id to RequestClientInfo
        request_order: List of request IDs in chronological order

    Returns:
        Dictionary with analysis results including switching statistics
    """
    if not request_order:
        return {}

    # Track patterns
    client_switches = 0
    successful_clients = []
    failed_attempts = 0
    total_attempts = 0

    prev_successful_client = None

    for request_id in request_order:
        if request_id not in client_info_by_request:
            continue

        info = client_info_by_request[request_id]
        total_attempts += info.total_attempts

        if info.successful_client:
            successful_clients.append(info.successful_client)

            # Check if client switched from previous request
            if (
                prev_successful_client
                and prev_successful_client != info.successful_client
            ):
                client_switches += 1

            prev_successful_client = info.successful_client

        # Count failed attempts (total attempts - 1 if successful, or all if failed)
        if info.successful_client:
            failed_attempts += max(0, info.total_attempts - 1)
        else:
            failed_attempts += info.total_attempts

    return {
        "total_requests": len(
            [r for r in request_order if r in client_info_by_request]
        ),
        "client_switches": client_switches,
        "successful_clients": successful_clients,
        "failed_attempts": failed_attempts,
        "total_attempts": total_attempts,
        "success_rate": (total_attempts - failed_attempts) / total_attempts
        if total_attempts > 0
        else 0,
        "switch_rate": client_switches / len(successful_clients)
        if len(successful_clients) > 1
        else 0,
    }

"""
Common utilities and data structures for cache invalidation analysis.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, NamedTuple
from collections import defaultdict

from google.cloud import bigquery
from base.datasets.gcp_creds import get_gcp_creds


class CacheInvalidationEvent(NamedTuple):
    """Represents a cache invalidation event."""

    conversation_id: str
    request_id: str
    prev_request_id: str
    time: datetime
    prev_time: datetime
    invalidation_type: str  # 'expiration', 'truncation', 'alteration_beginning', 'alteration_middle', 'client_switch'
    prev_cache_reads: int
    curr_cache_reads: int
    prev_input_tokens: int
    curr_input_tokens: int
    extra_cost: float  # Additional cost due to invalidation (for type 3)
    actual_cost: float  # Actual cost of current request
    client_type: str  # Client type (vscode, intellij, beachhead, vim, other)
    prev_client: Optional[str] = None  # Previous client for client_switch events
    new_client: Optional[str] = None  # New client for client_switch events


class ConversationRequest(NamedTuple):
    """Represents a single request in a conversation."""

    time: datetime
    request_id: str
    conversation_id: str
    tool_input_len: int
    tool_output_len: int
    tool_name: str
    input_tokens: int  # Non-cached input tokens
    cache_read_input_tokens: int  # Cache hits
    cache_creation_input_tokens: int  # Cache misses
    total_output_tokens: int
    client_type: str

    @property
    def total_input_tokens(self) -> int:
        """Calculate total input tokens."""
        return (
            self.input_tokens
            + self.cache_read_input_tokens
            + self.cache_creation_input_tokens
        )

    @property
    def actual_cost(self) -> float:
        """Calculate actual cost of this request."""
        return (
            self.input_tokens * 3.0
            + self.cache_read_input_tokens * 0.3
            + self.cache_creation_input_tokens * 3.75
            + self.total_output_tokens * 15.0
        ) / 1_000_000


def get_bigquery_client() -> bigquery.Client:
    """Get authenticated BigQuery client."""
    gcp_creds, _ = get_gcp_creds(None)
    return bigquery.Client(credentials=gcp_creds)


def fetch_conversation_data(
    project_id: str,
    dataset_name: str,
    conversation_id: str,
    tenant_filter: Optional[str] = None,
) -> List[ConversationRequest]:
    """Fetch all requests for a specific conversation."""
    client = get_bigquery_client()

    # Build tenant filter
    tenant_condition = f"AND t.tenant = '{tenant_filter}'" if tenant_filter else ""

    query = f"""
    SELECT
        meta.time,
        t.request_id,
        JSON_EXTRACT_SCALAR(t.sanitized_json, '$.conversation_id') as conversation_id,
        CAST(JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_input_len') AS INT64) as tool_input_len,
        CAST(JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_output_len') AS INT64) as tool_output_len,
        JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_name') as tool_name,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.input_tokens') AS INT64), 0) as input_tokens,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.cache_read_input_tokens') AS INT64), 0) as cache_read_input_tokens,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.cache_creation_input_tokens') AS INT64), 0) as cache_creation_input_tokens,
        COALESCE(CAST(JSON_EXTRACT_SCALAR(c.sanitized_json, '$.total_output_tokens') AS INT64), 0) as total_output_tokens,
        CASE
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'vscode') THEN 'vscode'
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'intellij') THEN 'intellij'
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'beachhead') THEN 'beachhead'
            WHEN CONTAINS_SUBSTR(LOWER(COALESCE(meta.user_agent, '')), 'vim') THEN 'vim'
            ELSE 'other'
        END as client_type
    FROM `{project_id}.{dataset_name}.tool_use_data` t
    JOIN `{project_id}.{dataset_name}.prompt_cache_usage` c
        ON t.request_id = c.request_id
    JOIN `{project_id}.{dataset_name}.request_metadata` meta
        ON t.request_id = meta.request_id
    WHERE JSON_EXTRACT_SCALAR(t.sanitized_json, '$.conversation_id') = '{conversation_id}'
        AND JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_input_len') IS NOT NULL
        AND JSON_EXTRACT_SCALAR(t.sanitized_json, '$.tool_output_len') IS NOT NULL
        {tenant_condition}
    ORDER BY meta.time
    """

    rows = client.query_and_wait(query)

    requests = []
    for row in rows:
        request = ConversationRequest(
            time=row.time,
            request_id=row.request_id,
            conversation_id=row.conversation_id,
            tool_input_len=row.tool_input_len,
            tool_output_len=row.tool_output_len,
            tool_name=row.tool_name,
            input_tokens=row.input_tokens,
            cache_read_input_tokens=row.cache_read_input_tokens,
            cache_creation_input_tokens=row.cache_creation_input_tokens,
            total_output_tokens=row.total_output_tokens,
            client_type=row.client_type,
        )
        requests.append(request)

    return requests


def calculate_extra_cost_from_alteration(
    prev_request: ConversationRequest, curr_request: ConversationRequest
) -> float:
    """Calculate extra cost due to cache invalidation from chat history alteration."""

    # Understanding the pricing model:
    # - input_tokens: tokens NOT cached (charged at 3.0 per 1M)
    # - cache_read_input_tokens: tokens that were cache hits (charged at 0.3 per 1M)
    # - cache_creation_input_tokens: tokens that were cache misses but added to cache (charged at 3.75 per 1M)
    # Total input = input_tokens + cache_read_input_tokens + cache_creation_input_tokens

    # How many tokens could have been cache hits if invalidation didn't happen?
    potential_cache_hits = (
        prev_request.cache_read_input_tokens + prev_request.cache_creation_input_tokens
    )

    # Tokens that should have been cache hits but weren't due to invalidation
    lost_cache_hits = max(
        0, potential_cache_hits - curr_request.cache_read_input_tokens
    )

    # Extra cost: lost cache hits are likely charged at cache creation rate (3.75) instead of cache read rate (0.3)
    # The difference is what we pay extra due to invalidation
    extra_cost = lost_cache_hits * (3.75 - 0.3) / 1_000_000

    return max(0.0, extra_cost)


def calculate_extra_cost_from_expiration(
    prev_request: ConversationRequest, curr_request: ConversationRequest
) -> float:
    """Calculate extra cost due to cache invalidation from cache expiration."""

    # For expiration events, the cache expired due to time gap > 5 minutes
    # All previously cached tokens need to be re-processed

    # How many tokens were cached in the previous request?
    prev_cached_tokens = (
        prev_request.cache_read_input_tokens + prev_request.cache_creation_input_tokens
    )

    # Due to expiration, these tokens that could have been cache hits (0.3 per 1M)
    # are now likely charged at cache creation rate (3.75 per 1M)
    # The difference is the extra cost due to expiration
    extra_cost = prev_cached_tokens * (3.75 - 0.3) / 1_000_000

    return max(0.0, extra_cost)


def detect_cache_invalidation_between_requests(
    prev_request: ConversationRequest, curr_request: ConversationRequest
) -> Optional[CacheInvalidationEvent]:
    """Detect cache invalidation between two consecutive requests."""

    prev_cache_reads = prev_request.cache_read_input_tokens
    curr_cache_reads = curr_request.cache_read_input_tokens

    # Check if cache invalidation occurred
    if prev_cache_reads <= curr_cache_reads:
        return None  # No invalidation

    prev_input_tokens = prev_request.total_input_tokens
    curr_input_tokens = curr_request.total_input_tokens
    time_diff = curr_request.time - prev_request.time

    # Determine invalidation type
    invalidation_type = None
    extra_cost = 0.0

    if time_diff > timedelta(minutes=5):
        # Case 1: Cache expiration
        invalidation_type = "expiration"
        # Calculate extra cost if expiration didn't happen
        extra_cost = calculate_extra_cost_from_expiration(prev_request, curr_request)
    elif curr_input_tokens < prev_input_tokens:
        # Case 2: Chat history truncation
        invalidation_type = "truncation"
    elif curr_input_tokens > prev_input_tokens:
        # Case 3: Chat history alteration
        invalidation_type = "alteration"
        # Calculate extra cost if invalidation didn't happen
        extra_cost = calculate_extra_cost_from_alteration(prev_request, curr_request)
    else:
        # Edge case: same input tokens but cache reads decreased
        invalidation_type = "unknown"

    return CacheInvalidationEvent(
        conversation_id=curr_request.conversation_id,
        request_id=curr_request.request_id,
        prev_request_id=prev_request.request_id,
        time=curr_request.time,
        prev_time=prev_request.time,
        invalidation_type=invalidation_type,
        prev_cache_reads=prev_cache_reads,
        curr_cache_reads=curr_cache_reads,
        prev_input_tokens=prev_input_tokens,
        curr_input_tokens=curr_input_tokens,
        extra_cost=extra_cost,
        actual_cost=curr_request.actual_cost,
        client_type=curr_request.client_type,
        prev_client=None,
        new_client=None,
    )


def format_cost(cost: float) -> str:
    """Format cost for display."""
    if cost >= 0.01:
        return f"${cost:.3f}"
    elif cost >= 0.001:
        return f"${cost:.4f}"
    else:
        return f"${cost:.5f}"


def format_tokens(tokens: int) -> str:
    """Format token count for display."""
    if tokens >= 1_000_000:
        return f"{tokens/1_000_000:.1f}M"
    elif tokens >= 1_000:
        return f"{tokens/1_000:.1f}K"
    else:
        return str(tokens)


def format_time_diff(time_diff: timedelta) -> str:
    """Format time difference for display."""
    total_seconds = time_diff.total_seconds()
    if total_seconds >= 3600:
        hours = total_seconds / 3600
        return f"{hours:.1f}h"
    elif total_seconds >= 60:
        minutes = total_seconds / 60
        return f"{minutes:.1f}m"
    else:
        return f"{total_seconds:.0f}s"

#!/usr/bin/env python3
"""
Example script demonstrating how to use the GCP logging functionality
to analyze client usage patterns and cache invalidation.

This script shows how to:
1. Query Google Cloud logs for client information
2. Analyze client switching patterns
3. Estimate costs due to client switching
4. Generate reports with recommendations

Usage:
    python example_client_analysis.py
"""

import sys
from datetime import datetime
from typing import List

# Add the repository root to the path
sys.path.insert(0, "/home/<USER>/augment")

from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
    query_client_logs,
    aggregate_client_info,
    analyze_client_switching_patterns,
    ClientAttempt,
    RequestClientInfo,
)


def create_sample_data() -> List[ClientAttempt]:
    """Create sample client attempt data for demonstration."""
    base_time = datetime.now()

    # Simulate a conversation with multiple requests and client switching
    attempts = [
        # Request 1: Success on first try
        ClientAttempt(
            timestamp=base_time,
            request_id="req-001",
            client_name="ANTHROPIC_VERTEXAI_EU_W1",
            attempt_number=1,
            is_success=False,
            is_fallback=False,
        ),
        ClientAttempt(
            timestamp=base_time,
            request_id="req-001",
            client_name="ANTHROPIC_VERTEXAI_EU_W1",
            attempt_number=0,
            is_success=True,
            is_fallback=False,
        ),
        # Request 2: Failure, then fallback and success
        ClientAttempt(
            timestamp=base_time,
            request_id="req-002",
            client_name="ANTHROPIC_VERTEXAI_EU_W1",
            attempt_number=1,
            is_success=False,
            is_fallback=False,
        ),
        ClientAttempt(
            timestamp=base_time,
            request_id="req-002",
            client_name="ANTHROPIC_VERTEXAI_EU_W1",
            attempt_number=0,
            is_success=False,
            is_fallback=True,
        ),
        ClientAttempt(
            timestamp=base_time,
            request_id="req-002",
            client_name="ANTHROPIC_VERTEXAI_AS_SE1",
            attempt_number=2,
            is_success=False,
            is_fallback=False,
        ),
        ClientAttempt(
            timestamp=base_time,
            request_id="req-002",
            client_name="ANTHROPIC_VERTEXAI_AS_SE1",
            attempt_number=0,
            is_success=True,
            is_fallback=False,
        ),
        # Request 3: Success with same client as request 2
        ClientAttempt(
            timestamp=base_time,
            request_id="req-003",
            client_name="ANTHROPIC_VERTEXAI_AS_SE1",
            attempt_number=1,
            is_success=False,
            is_fallback=False,
        ),
        ClientAttempt(
            timestamp=base_time,
            request_id="req-003",
            client_name="ANTHROPIC_VERTEXAI_AS_SE1",
            attempt_number=0,
            is_success=True,
            is_fallback=False,
        ),
        # Request 4: Switch back to EU client
        ClientAttempt(
            timestamp=base_time,
            request_id="req-004",
            client_name="ANTHROPIC_VERTEXAI_EU_W1",
            attempt_number=1,
            is_success=False,
            is_fallback=False,
        ),
        ClientAttempt(
            timestamp=base_time,
            request_id="req-004",
            client_name="ANTHROPIC_VERTEXAI_EU_W1",
            attempt_number=0,
            is_success=True,
            is_fallback=False,
        ),
    ]

    return attempts


def demonstrate_client_analysis():
    """Demonstrate the client analysis functionality."""
    print("=" * 80)
    print("CLIENT SWITCHING ANALYSIS DEMONSTRATION")
    print("=" * 80)

    # Create sample data (in real usage, this would come from query_client_logs)
    print("\n1. Creating sample client attempt data...")
    attempts = create_sample_data()
    print(f"   Created {len(attempts)} client attempts for demonstration")

    # Aggregate client information by request
    print("\n2. Aggregating client information by request...")
    client_info_by_request = aggregate_client_info(attempts)
    print(f"   Processed {len(client_info_by_request)} requests")

    # Display detailed information for each request
    print("\n3. Request-by-request analysis:")
    for request_id, info in client_info_by_request.items():
        print(f"\n   🔹 {request_id}:")
        print(f"      • Attempted clients: {' → '.join(info.attempted_clients)}")
        print(f"      • Successful client: {info.successful_client}")
        print(f"      • Total attempts: {info.total_attempts}")
        print(f"      • Had fallbacks: {'Yes' if info.had_fallbacks else 'No'}")

    # Analyze switching patterns
    print("\n4. Analyzing client switching patterns...")
    request_order = ["req-001", "req-002", "req-003", "req-004"]
    analysis = analyze_client_switching_patterns(client_info_by_request, request_order)

    print("\n📊 Analysis Results:")
    print(f"   • Total requests: {analysis['total_requests']}")
    print(f"   • Client switches: {analysis['client_switches']}")
    print(f"   • Switch rate: {analysis['switch_rate']:.1%}")
    print(f"   • Total attempts: {analysis['total_attempts']}")
    print(f"   • Failed attempts: {analysis['failed_attempts']}")
    print(f"   • Success rate: {analysis['success_rate']:.1%}")

    # Show client usage frequency
    successful_clients = analysis["successful_clients"]
    if successful_clients:
        from collections import Counter

        client_counts = Counter(successful_clients)
        print("\n🎯 Client usage frequency:")
        for client, count in client_counts.most_common():
            percentage = count / len(successful_clients) * 100
            print(f"   • {client}: {count} times ({percentage:.1f}%)")

    # Identify client switches
    print("\n🔄 Client switching details:")
    prev_client = None
    for i, request_id in enumerate(request_order):
        if request_id in client_info_by_request:
            current_client = client_info_by_request[request_id].successful_client
            if prev_client and current_client and prev_client != current_client:
                print(f"   • Request #{i+1}: {prev_client} → {current_client}")
            prev_client = current_client

    print("\n💡 Recommendations:")
    if analysis["switch_rate"] > 0.3:
        print("   • HIGH client switching detected - consider client affinity")
    elif analysis["switch_rate"] > 0.1:
        print("   • MODERATE client switching - monitor for patterns")
    else:
        print("   • LOW client switching - current routing appears stable")

    if analysis["success_rate"] < 0.8:
        print("   • LOW success rate - investigate client reliability issues")
    elif analysis["success_rate"] < 0.9:
        print("   • MODERATE success rate - monitor client performance")
    else:
        print("   • HIGH success rate - clients performing well")


def demonstrate_real_usage():
    """Show how to use with real data (commented out to avoid actual API calls)."""
    print("\n" + "=" * 80)
    print("REAL USAGE EXAMPLE (commented out)")
    print("=" * 80)

    example_code = """
# Example of how to use with real conversation data:

from experimental.vpas.agent.chat_history_compression.gcp_log_utils import (
    query_client_logs,
    aggregate_client_info,
    analyze_client_switching_patterns,
)

# Get request IDs from your conversation analysis
request_ids = ["req-123", "req-456", "req-789"]

# Query Google Cloud logs
try:
    client_attempts = query_client_logs(
        request_ids=request_ids,
        project_id="system-services-prod",
        namespace="i0"
    )

    # Aggregate and analyze
    client_info = aggregate_client_info(client_attempts)
    analysis = analyze_client_switching_patterns(client_info, request_ids)

    # Use the results in your conversation analysis
    print(f"Client switches: {analysis['client_switches']}")
    print(f"Switch rate: {analysis['switch_rate']:.1%}")

except Exception as e:
    print(f"Failed to query logs: {e}")
    # Handle gracefully - analysis can continue without client data
"""

    print(example_code)


def main():
    """Main demonstration function."""
    try:
        demonstrate_client_analysis()
        demonstrate_real_usage()

        print("\n" + "=" * 80)
        print("DEMONSTRATION COMPLETE")
        print("=" * 80)
        print("\nTo use with real data:")
        print("1. Ensure you have GCP credentials configured")
        print("2. Install google-cloud-logging: pip install google-cloud-logging")
        print("3. Use analyze_conversation.py with a real conversation ID")
        print("4. The script will automatically query logs and show client patterns")

    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# Chat History Compression

A comprehensive system for compressing Agent chat history to reduce token usage while preserving important information for conversation continuity.

## Overview

This system provides multiple compression strategies, evaluation frameworks, and interactive reporting tools to analyze and optimize chat history compression for AI agents.

## Features

### 🗜️ **Compression Strategies**
- **PlaceholderCompressor**: Replaces tool outputs with structured placeholders containing key metadata
- **GeminiFlashCompressor**: Uses Gemini Flash LLM for intelligent content summarization
- **Extensible Architecture**: Easy to add new compression strategies

### 📊 **Evaluation Framework**
- **Comprehensive Metrics**: Token counting, compression rates, tool-specific statistics
- **LLM-based Quality Assessment**: Uses Claude to evaluate information retention and context preservation
- **Performance Monitoring**: Tracks compression time and processing efficiency

### 📈 **Interactive Reports**
- **React-based Dashboards**: Interactive charts and visualizations
- **Comparison Views**: Side-by-side analysis of compression strategies
- **Detailed Breakdowns**: Tool-specific performance and quality metrics

### 🔍 **Smart Filtering**
- **Chat History Length Filtering**: Only process conversations with sufficient history length
- **Configurable Thresholds**: Customizable minimum exchange count (default: 100)
- **Performance Optimization**: Skip short conversations to focus on compression-worthy content

### ⚙️ **Production Ready**
- **Multi-threaded Processing**: Scalable parallel compression and evaluation
- **BigQuery Integration**: Direct access to production conversation data
- **Google Cloud Logging Integration**: Query GCP logs for client usage patterns and cache invalidation analysis
- **Configurable Settings**: Comprehensive configuration management
- **Error Handling**: Robust fallback mechanisms and error recovery

## Quick Start

### Installation

```bash
# Install dependencies (from repository root)
pip install -e .

# For React components (optional)
cd experimental/vpas/agent/chat_history_compression/html_report/react
npm install
npm run build
```

### Basic Usage

```python
from experimental.vpas.agent.chat_history_compression import (
    PlaceholderCompressor, GeminiFlashCompressor,
    CompressionEvaluationPipeline, CompressionReportGenerator
)

# Initialize compressor
compressor = PlaceholderCompressor()

# Compress conversation
compressed_exchanges = compressor.compress(original_exchanges)

# Evaluate compression
pipeline = CompressionEvaluationPipeline()
result = pipeline.evaluate_single_conversation(
    original_exchanges=original_exchanges,
    compressed_exchanges=compressed_exchanges,
    compressor_name="PlaceholderCompressor",
    conversation_id="example_conv"
)

# Generate report
report_generator = CompressionReportGenerator()
report_path = report_generator.generate_summary_report(
    analysis_summary=pipeline.analyze_compression_results([result])
)
```

### Command Line Usage

```bash
# Run compression analysis on recent conversations
python -m experimental.vpas.agent.chat_history_compression.run_compression \
    --last-hours 24 \
    --compressor placeholder \
    --limit 100 \
    --output-dir ./results

# Compare multiple compressors with custom chat history length filter
python -m experimental.vpas.agent.chat_history_compression.run_compression \
    --last-days 7 \
    --compressor all \
    --limit 50 \
    --min-chat-history-length 150 \
    --thread-count 10

# Only process conversations with very long chat histories
python -m experimental.vpas.agent.chat_history_compression.run_compression \
    --last-days 1 \
    --compressor placeholder \
    --min-chat-history-length 200 \
    --limit 20

# Evaluate cost savings on real production data
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --tenant dogfood-shard \
    --hours 24

# Sample 50 random conversations from production dataset
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --prod \
    --num-conversations 50 \
    --hours 12

# Analyze staging dataset with conversation sampling
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --staging \
    --num-conversations 20 \
    --days 1

# Compare costs across multiple tenants for the last week
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --days 7 \
    --summarization-threshold 100000

# Analyze specific conversation with client switching patterns
python experimental/vpas/agent/chat_history_compression/analyze_conversation.py \
    <conversation_id> --prod

# Analyze conversation from specific tenant
python experimental/vpas/agent/chat_history_compression/analyze_conversation.py \
    <conversation_id> --tenant dogfood-shard

# Test multiple thresholds to find optimal settings
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --prod \
    --num-conversations 10 \
    --hours 6 \
    --threshold-range

# Custom threshold comparison with specific values
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --tenant dogfood-shard \
    --num-conversations 20 \
    --threshold-range \
    --threshold-values "25000,50000,75000,100000,150000"

# 2D parameter optimization: threshold vs keep_last_n_tokens
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --prod \
    --num-conversations 10 \
    --hours 6 \
    --keep-last-range \
    --threshold-values "50000,75000,100000" \
    --keep-last-values "20000,25000,30000"
```

## Architecture

### Core Components

```
chat_history_compression/
├── chat_history_compression.py    # Base compressor interface
├── placeholder_compressor.py      # Placeholder-based compression
├── gemini_flash_compressor.py     # LLM-based compression
├── utils.py                       # Utility functions
├── metrics.py                     # Compression metrics calculation
├── llm_judge.py                   # Quality evaluation with LLM
├── evaluation_pipeline.py         # Complete evaluation workflow
├── eval_types.py                  # Data structures
├── config.py                      # Configuration management
├── run_compression.py             # Main CLI script
├── eval_cost_on_real_data.py      # Real-world cost evaluation script
├── analyze_conversation.py        # Detailed conversation analysis with client switching
├── gcp_log_utils.py               # Google Cloud logging utilities for client analysis
├── html_report/                   # Report generation
│   ├── report_generator.py        # Python report generator
│   └── react/                     # React components
└── tests/                         # Test suite
```

### Compression Strategies

#### PlaceholderCompressor
- **Strategy**: Replace tool outputs with structured placeholders
- **Benefits**: Fast, deterministic, preserves metadata
- **Use Case**: High-throughput scenarios where speed is critical

```python
# Example placeholder output
"[Tool output compressed: view - 1,234 chars - 45 lines - cat -n file.py - ID: tool_123]"
```

#### GeminiFlashCompressor
- **Strategy**: Use Gemini Flash LLM for intelligent summarization
- **Benefits**: Context-aware, preserves semantic meaning
- **Use Case**: Quality-critical scenarios where information retention is paramount

```python
# Example LLM-compressed output
"File contains a Python class with 3 methods for data processing. Key functions: load_data(), process_batch(), save_results(). No errors found."
```

## Configuration

### Configuration File

Create a configuration file to customize behavior:

```python
from experimental.vpas.agent.chat_history_compression.config import CompressionSystemConfig

# Create sample config
config = CompressionSystemConfig()
config.save_to_file("my_config.json")

# Load and modify
config = CompressionSystemConfig.load_from_file("my_config.json")
config.compression.llm_temperature = 0.5
config.evaluation.max_workers = 10
```

### Key Settings

- **Compression**: Tool selection, LLM parameters, fallback behavior
- **Evaluation**: Quality assessment, performance monitoring, metrics calculation
- **Reporting**: Output formats, visualization options, content limits
- **Data**: BigQuery access, caching, result persistence
- **Filtering**: Chat history length thresholds, conversation selection criteria

## Client Switching Analysis

### Conversation-Level Client Analysis

The `analyze_conversation.py` script now includes comprehensive client switching analysis by querying Google Cloud logs to understand client usage patterns and their impact on cache invalidation:

```bash
# Analyze specific conversation with detailed client switching information
python experimental/vpas/agent/chat_history_compression/analyze_conversation.py \
    <conversation_id> --prod

# Analyze conversation from specific tenant
python experimental/vpas/agent/chat_history_compression/analyze_conversation.py \
    <conversation_id> --tenant dogfood-shard
```

**Key Features:**
- **Google Cloud Logging Integration**: Queries GCP logs using LQL (Logging Query Language) to extract client usage patterns
- **Client Attempt Tracking**: Identifies which clients were attempted, in what order, and which succeeded
- **Fallback Detection**: Tracks when clients failed and fallback mechanisms were triggered
- **Cache Invalidation Analysis**: Detects when client switching causes cache invalidation and estimates the extra cost
- **Cost Impact Breakdown**: Shows percentage of total cost attributed to client switching vs other invalidation causes
- **Optimization Recommendations**: Provides specific guidance on whether to focus on client switching or other invalidation causes

**Sample Output:**
```
🔄 Client switching patterns:
   • Total requests with client data: 15
   • Client switches: 3
   • Switch rate: 20.0%

📊 Attempt statistics:
   • Total attempts: 18
   • Failed attempts: 3
   • Success rate: 83.3%

🎯 Most used clients:
   • ANTHROPIC_VERTEXAI_EU_W1: 8 times (53.3%)
   • ANTHROPIC_VERTEXAI_AS_SE1: 7 times (46.7%)

💰 Cost breakdown:
   • Extra cost from cache invalidations: $0.045 (12.3%)
   • Extra cost from client switches: $0.023 (6.2%)
   • Total extra cost: $0.068 (18.5%)

💡 Recommendations:
   • Focus on reducing cache invalidations (primary cost driver)
   • Review chat history management and timing patterns
```

## Cost Evaluation on Real Data

### Real-World Cost Analysis

The `eval_cost_on_real_data.py` script provides real-world cost analysis by comparing actual production costs with simulated summarization costs:

```bash
# Analyze cost savings for a specific tenant over 24 hours
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --tenant dogfood-shard --hours 24

# Compare costs across all tenants for the last week
python experimental/vpas/agent/chat_history_compression/eval_cost_on_real_data.py \
    --days 7
```

**Key Features:**
- **Real Production Data**: Uses actual tool use data from BigQuery
- **Accurate Cost Modeling**: Incorporates prompt caching costs (input, cache read, cache creation, output tokens)
- **Summarization Simulation**: Models cost savings using configurable thresholds
- **Multi-Tenant Support**: Analyze individual tenants or aggregate across multiple tenants
- **Conversation Sampling**: Randomly sample conversations for faster analysis on large datasets
- **Dataset Selection**: Choose between production and staging datasets
- **Threshold Optimization**: Test multiple summarization thresholds to find optimal settings
- **2D Parameter Optimization**: Test combinations of threshold and keep_last_n_tokens values
- **Cache Expiration Analysis**: Compare performance with and without cache expiration (5min timeout)
- **Optimized Queries**: BigQuery-level conversation grouping and sampling for better performance
- **Progress Tracking**: Shows detailed progress and handles large datasets efficiently

**Sample Results:**
```
# Production dataset with 20 random conversations
--- Results for production ---
Cost without summarization: $110.02
Cost with summarization:    $58.72
Savings:                    $51.30 (46.6%)

✅ Summarization would save $51.30 (46.6%)

# Specific tenant analysis
--- Results for dogfood-shard ---
Cost without summarization: $189.75
Cost with summarization:    $89.59
Savings:                    $100.16 (52.8%)

✅ Summarization would save $100.16 (52.8%)
```

**Usage Options:**
- `--prod`: Analyze production dataset across all tenants
- `--staging`: Analyze staging dataset across all tenants
- `--tenant TENANT`: Analyze specific tenant (cannot be used with --prod/--staging)
- `--num-conversations N`: Sample N random conversations for faster analysis
- `--hours/--days`: Time range for data analysis
- `--summarization-threshold`: Token threshold for triggering summarization
- `--threshold-range`: Test multiple thresholds and show comparison table
- `--threshold-values`: Comma-separated list of threshold values to test
- `--keep-last-range`: Test multiple keep_last_n_tokens values and show 2D comparison table
- `--keep-last-values`: Comma-separated list of keep_last_n_tokens values to test

**Threshold Comparison Sample Output:**
```
================================================================================
SUMMARIZATION THRESHOLD COMPARISON
================================================================================

Threshold    Cost w/ Sum  Savings      Savings %    Status
----------------------------------------------------------------------
25,000       $85.11       $-57.24      -205.3%      ❌ Cost
50,000       $14.26       $13.61       48.8%        ✅ Save
75,000       $13.52       $14.35       51.5%        ✅ Save
100,000      $14.39       $13.48       48.4%        ✅ Save
150,000      $16.08       $11.79       42.3%        ✅ Save

🎯 OPTIMAL THRESHOLD: 75,000 tokens
   Maximum savings: $14.35 (51.5%)

📊 THRESHOLD ANALYSIS:
   • Baseline cost (no summarization): $27.87
   • Best threshold: 75,000 tokens
   • Savings range: $-57.24 to $14.35
```

**2D Parameter Optimization Sample Output:**
```
====================================================================================================
2D PARAMETER OPTIMIZATION: THRESHOLD vs KEEP_LAST_N_TOKENS
====================================================================================================

Savings Percentage Tables (Baseline: $6.83)

WITHOUT Cache Expiration
====================================================================================================
Keep Last   50K        75K
----------------------------------------------------------------------------------------------------
20K        🟠 15.3%    🟢 48.3%
30K        🔴-17.5%    🟠 19.9%

WITH Cache Expiration (5min timeout)
====================================================================================================
Keep Last   50K        75K
----------------------------------------------------------------------------------------------------
20K        🟠 13.4%    🟢 45.1%
30K        🔴-19.4%    🟠 16.7%

📊 LEGEND:
🟢 Excellent (>40%)  🟡 Good (20-40%)  🟠 Fair (0-20%)  🔴 Poor (<0%)

🎯 OPTIMAL PARAMETERS COMPARISON:
WITHOUT Cache Expiration:
   • Keep Last N Tokens: 20,000
   • Summarization Threshold: 75,000
   • Maximum Savings: $3.30 (48.3%)
   • Cost with optimization: $3.53

WITH Cache Expiration:
   • Keep Last N Tokens: 20,000
   • Summarization Threshold: 75,000
   • Maximum Savings: $3.08 (45.1%)
   • Cost with optimization: $3.75

📊 CACHE EXPIRATION IMPACT:
   • Savings difference: 3.2 percentage points
   • Cache expiration reduces savings by 3.2%

💡 RECOMMENDATIONS:
   ✅ Strong optimization opportunity: 48.3% savings achievable
   • Recommend: Disable cache expiration for maximum savings
```

## Evaluation Metrics

### Quantitative Metrics
- **Compression Rate**: Percentage of tokens saved
- **Token Savings**: Absolute number of tokens reduced
- **Processing Time**: Time taken for compression
- **Tool-specific Statistics**: Performance by tool type
- **Real-World Cost Savings**: Actual dollar savings from production data

### Quality Metrics (LLM Judge)
- **Information Retention**: How well important details are preserved
- **Context Preservation**: Maintenance of conversation flow
- **Tool Output Relevance**: Adequacy of tool result summaries
- **Overall Quality**: Combined assessment score

## Testing

### Run Tests

```bash
# Unit tests
python -m pytest experimental/vpas/agent/chat_history_compression/tests/test_utils_module.py

# Integration tests
python -m pytest experimental/vpas/agent/chat_history_compression/tests/test_integration.py

# Compressor tests
python -m pytest experimental/vpas/agent/chat_history_compression/tests/test_compressors.py
```

### Test Coverage

- **Unit Tests**: Individual component functionality
- **Integration Tests**: End-to-end pipeline testing
- **Mock Tests**: LLM client integration with fallbacks
- **Performance Tests**: Timing and resource usage

## Performance Optimization

### Recommended Settings

```python
# High-throughput configuration
config.evaluation.max_workers = 20
config.compression.llm_compression_enabled = False  # Use placeholder only
config.evaluation.enable_quality_evaluation = False

# High-quality configuration
config.compression.llm_temperature = 0.1
config.evaluation.enable_quality_evaluation = True
config.evaluation.max_workers = 5  # Reduce for LLM rate limits
```

### Scaling Considerations

- **Thread Count**: Balance between throughput and API rate limits
- **Batch Size**: Process conversations in batches for memory efficiency
- **Caching**: Enable result caching for repeated analysis
- **Quality Evaluation**: Disable for large-scale analysis, enable for samples

## Troubleshooting

### Common Issues

1. **LLM API Failures**
   - Enable fallback mechanisms in configuration
   - Reduce thread count to avoid rate limits
   - Check API credentials and quotas

2. **Memory Usage**
   - Process conversations in smaller batches
   - Enable result caching to avoid reprocessing
   - Limit conversation history length

3. **Performance Issues**
   - Disable quality evaluation for large datasets
   - Use PlaceholderCompressor for speed-critical scenarios
   - Optimize thread count based on system resources

### Debug Mode

```python
# Enable debug logging
config.debug_mode = True
config.log_level = "DEBUG"

# Or via environment
export COMPRESSION_DEBUG=1
```

## Contributing

### Adding New Compressors

1. Inherit from `ChatHistoryCompressor`
2. Implement the `compress()` method
3. Add configuration options to `CompressionConfig`
4. Create unit tests
5. Update documentation

### Adding New Metrics

1. Extend `CompressionMetrics` class
2. Update `eval_types.py` data structures
3. Modify report generation templates
4. Add visualization components

## License

This project is part of the Augment codebase and follows the same licensing terms.

## Support

For questions or issues:
1. Check the troubleshooting section
2. Review test cases for usage examples
3. Consult the configuration documentation
4. File an issue with detailed reproduction steps

from typing import <PERSON><PERSON>

from experimental.vpas.agent.replay_eval.eval_sample import <PERSON><PERSON><PERSON><PERSON>
from experimental.vpas.agent.replay_eval.model_config import ModelResponse
from experimental.vpas.agent.replay_eval.utils import extract_tool_calls


def gen_samples(request_ids: list[str]) -> list[EvalSample]:
    assert len(request_ids) == len(set(request_ids)), "Duplicate samples"

    return [
        EvalSample(
            request_id=request_id,
            name=f"str_replace_editor_many_small_views_issue_{index}",
            eval_response_func=eval_response_func,
            assistant_message_prefill_from_response=True,
        )
        for index, request_id in enumerate(request_ids)
    ]


def eval_response_func(response: ModelResponse) -> Tuple[bool, str]:
    tool_calls = extract_tool_calls(response)
    if (
        len(tool_calls) != 1
        or tool_calls[0] is None
        or tool_calls[0].name != "str-replace-editor"
    ):
        return False, "Response does not use str-replace-editor tool correctly"

    tool_input = tool_calls[0].input

    if tool_input.get("command") != "view":
        return False, "Response does not use the 'view' command"

    view_range = tool_input.get("view_range")

    if view_range is not None and (
        len(view_range) != 2 or not all(isinstance(i, int) for i in view_range)
    ):
        return False, "Response uses an invalid view_range"

    if view_range is None or view_range[1] == -1:
        # viewing the whole file which is desired behavior
        return True, ""

    if view_range[1] - view_range[0] + 1 >= 1000:
        # viewing a large range which is desired behavior
        return True, ""

    return (
        False,
        f"Response uses a small view range: {view_range}. Should use larger ranges (1000+ lines) or view the entire file.",
    )

import React, { useState } from 'react';
import ReactJson from 'react-json-view';
import { ChatResultNode } from '../../types';
import Message from './Message';
import Modal from '../common/Modal';

interface FinalParametersNodeProps {
  node: ChatResultNode;
}

const FinalParametersNode: React.FC<FinalParametersNodeProps> = ({ node }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (!node.final_parameters) return null;

  const params = node.final_parameters;
  const hasMessages = params.messages && Array.isArray(params.messages);
  const hasToolChoice = params.tool_choice;

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      <div className="node final-parameters">
        <div className="final-parameters-header">
          <button className="fullscreen-button" onClick={openModal} title="View in full screen">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
            </svg>
          </button>
        </div>
        <div className="final-parameters-content">
          <div className="params-grid">
            {params.model && (
              <div className="param-item">
                <div className="param-label">Model</div>
                <div className="param-value">{params.model}</div>
              </div>
            )}

            {params.temperature !== undefined && (
              <div className="param-item">
                <div className="param-label">Temperature</div>
                <div className="param-value">{params.temperature}</div>
              </div>
            )}

            {params.max_tokens && (
              <div className="param-item">
                <div className="param-label">Max Tokens</div>
                <div className="param-value">{params.max_tokens}</div>
              </div>
            )}

            {hasToolChoice && (
              <div className="param-item full-width">
                <div className="param-label">Tool Choice</div>
                <div className="param-value">
                  {typeof params.tool_choice === 'string' ? (
                    params.tool_choice
                  ) : (
                    <div className="tool-choice-object">
                      {params.tool_choice.type && <div><strong>Type:</strong> {params.tool_choice.type}</div>}
                      {params.tool_choice.name && <div><strong>Name:</strong> {params.tool_choice.name}</div>}
                      {params.tool_choice.function && params.tool_choice.function.name && (
                        <div><strong>Function:</strong> {params.tool_choice.function.name}</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {params.system_prompt && (
              <div className="param-item full-width">
                <details className="system-prompt-details">
                  <summary className="param-label">System Prompt</summary>
                  <div className="param-value system-prompt">{params.system_prompt}</div>
                </details>
              </div>
            )}
          </div>

          {hasMessages && (
            <div className="messages-section">
              <h4>Messages</h4>
              <div className="messages-list">
                {params.messages.map((message: any, index: number) => (
                  <Message key={index} message={message} index={index} />
                ))}
              </div>
            </div>
          )}

          <details className="raw-json-details">
            <summary>View Raw JSON</summary>
            <ReactJson
              src={node.final_parameters}
              theme="monokai"
              name={false}
              displayDataTypes={false}
              collapsed={1}
              enableClipboard={true}
            />
          </details>
        </div>
      </div>

      {/* Modal for full-screen view */}
      <Modal isOpen={isModalOpen} onClose={closeModal} title="Final Parameters">
        <div className="final-parameters-modal-content">
          <div className="params-grid">
            {params.model && (
              <div className="param-item">
                <div className="param-label">Model</div>
                <div className="param-value">{params.model}</div>
              </div>
            )}

            {params.temperature !== undefined && (
              <div className="param-item">
                <div className="param-label">Temperature</div>
                <div className="param-value">{params.temperature}</div>
              </div>
            )}

            {params.max_tokens && (
              <div className="param-item">
                <div className="param-label">Max Tokens</div>
                <div className="param-value">{params.max_tokens}</div>
              </div>
            )}

            {hasToolChoice && (
              <div className="param-item full-width">
                <div className="param-label">Tool Choice</div>
                <div className="param-value">
                  {typeof params.tool_choice === 'string' ? (
                    params.tool_choice
                  ) : (
                    <div className="tool-choice-object">
                      {params.tool_choice.type && <div><strong>Type:</strong> {params.tool_choice.type}</div>}
                      {params.tool_choice.name && <div><strong>Name:</strong> {params.tool_choice.name}</div>}
                      {params.tool_choice.function && params.tool_choice.function.name && (
                        <div><strong>Function:</strong> {params.tool_choice.function.name}</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {params.system_prompt && (
              <div className="param-item full-width">
                <details className="system-prompt-details">
                  <summary className="param-label">System Prompt</summary>
                  <div className="param-value system-prompt">{params.system_prompt}</div>
                </details>
              </div>
            )}
          </div>

          {hasMessages && (
            <div className="messages-section">
              <h4>Messages</h4>
              <div className="messages-list">
                {params.messages.map((message: any, index: number) => (
                  <Message key={index} message={message} index={index} />
                ))}
              </div>
            </div>
          )}

          <div className="modal-json-view">
            <h4>Raw JSON</h4>
            <ReactJson
              src={node.final_parameters}
              theme="monokai"
              name={false}
              displayDataTypes={false}
              collapsed={1}
              enableClipboard={true}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default FinalParametersNode;

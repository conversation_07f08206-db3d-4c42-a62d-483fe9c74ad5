"""React report generator for HTML reports.

This module provides functions for generating JSON data for the React app,
building the React app, and copying it to the output directory.
"""

import json
import os
import shutil
import subprocess
from dataclasses import asdict, dataclass, is_dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional

from dataclasses_json import DataClassJsonMixin

from base.prompt_format.common import ChatResultNodeType
from base.third_party_clients.google_genai_client import Content, Part
from experimental.vpas.agent.replay_eval.eval_output import (
    ComparisonSummary,
    EvalSummary,
)
from research.tools.chat_replay.replay_utils import (
    has_content,
    jsonify_final_parameters,
)

# Constants
CURRENT_DIR = Path(__file__).parent
REACT_DIR = CURRENT_DIR / "react"


# Custom JSON encoder to handle non-serializable objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, o):
        # Handle Part objects from Google GenAI
        if isinstance(o, Part):
            part_dict = {}
            if hasattr(o, "text") and o.text:
                part_dict["text"] = o.text
            for attr in [
                "thought",
                "code_execution_result",
                "executable_code",
                "file_data",
                "function_call",
                "function_response",
                "inline_data",
                "video_metadata",
            ]:
                attr_value = getattr(o, attr, None)
                if attr_value is not None:
                    part_dict[attr] = attr_value
            return part_dict
        # Handle Content objects from Google GenAI
        elif isinstance(o, Content):
            content_dict = {"role": o.role}
            if hasattr(o, "parts") and o.parts:
                parts_list = []
                for part in o.parts:
                    parts_list.append(self.default(part))
                content_dict["parts"] = parts_list
            return content_dict
        # Handle dataclasses
        elif is_dataclass(o) and not isinstance(o, type):
            return asdict(o)
        # Handle any other non-serializable objects
        try:
            return super().default(o)
        except TypeError:
            return str(o)


@dataclass
class ReportData(DataClassJsonMixin):
    comparison_summary: ComparisonSummary
    ref_model_name: str
    model_names: List[str]
    dataset_path: str


def deep_process_part_objects(obj):
    """Recursively process an object to convert Part and Content objects to dictionaries.

    Args:
        obj: The object to process

    Returns:
        A version of the object with all Part and Content objects converted to dictionaries
    """
    if isinstance(obj, Part):
        # Convert Part object to dictionary
        part_dict = {}
        if hasattr(obj, "text") and obj.text:
            part_dict["text"] = obj.text
        for attr in [
            "thought",
            "code_execution_result",
            "executable_code",
            "file_data",
            "function_call",
            "function_response",
            "inline_data",
            "video_metadata",
        ]:
            attr_value = getattr(obj, attr, None)
            if attr_value is not None:
                part_dict[attr] = deep_process_part_objects(attr_value)
        return part_dict
    elif isinstance(obj, Content):
        # Convert Content object to dictionary
        content_dict = {"role": obj.role}
        if hasattr(obj, "parts") and obj.parts:
            parts_list = []
            for part in obj.parts:
                parts_list.append(deep_process_part_objects(part))
            content_dict["parts"] = parts_list
        return content_dict
    elif isinstance(obj, list):
        return [deep_process_part_objects(item) for item in obj]
    elif isinstance(obj, dict):
        return {k: deep_process_part_objects(v) for k, v in obj.items()}
    else:
        return obj


def generate_comparison_json_str(
    comparison_summary: ComparisonSummary,
) -> str:
    # Add a few additional fields that might be useful for the React app
    ref_model_name = comparison_summary.ref_summary.model_config.name
    model_names = [ref_model_name] + list(comparison_summary.new_model_summaries.keys())
    dataset_path = comparison_summary.ref_summary.dataset_path

    # Process final parameters to make them JSON serializable
    def prepare_summary(summary: EvalSummary):
        for result in summary.eval_results.values():
            for attempt in result.attempts:
                if attempt.response is not None:
                    for node in attempt.response:
                        if node.type != ChatResultNodeType.FINAL_PARAMETERS:
                            continue
                        assert node.final_parameters is not None
                        for name, value in node.final_parameters.items():
                            if value.__class__.__name__ == "NotGiven":
                                node.final_parameters[name] = None
                        # Handle Google GenAI Content objects and other complex types
                        result = jsonify_final_parameters(
                            node.final_parameters, string_limit=0
                        )
                        assert isinstance(result, dict)
                        node.final_parameters = result
                        for name, value in result.items():
                            assert not has_content(value)

    prepare_summary(comparison_summary.ref_summary)
    # Assert no `Content` or `Part` objects remain
    for result in comparison_summary.ref_summary.eval_results.values():
        for attempt in result.attempts:
            if attempt.response is not None:
                for node in attempt.response:
                    if node.type != ChatResultNodeType.FINAL_PARAMETERS:
                        continue
                    assert node.final_parameters is not None
                    for name, value in node.final_parameters.items():
                        assert not has_content(value)
    for summary in comparison_summary.new_model_summaries.values():
        prepare_summary(summary)
        for result in summary.eval_results.values():
            for attempt in result.attempts:
                if attempt.response is not None:
                    for node in attempt.response:
                        if node.type != ChatResultNodeType.FINAL_PARAMETERS:
                            continue
                        assert node.final_parameters is not None
                        for name, value in node.final_parameters.items():
                            assert not has_content(value)

    # Process the entire comparison summary to convert any Part objects to dictionaries
    processed_comparison_summary = deep_process_part_objects(comparison_summary)

    data = ReportData(
        comparison_summary=processed_comparison_summary,
        # comparison_summary=None,
        ref_model_name=ref_model_name,
        model_names=model_names,
        dataset_path=dataset_path,
    )

    # Use our custom JSON encoder to handle non-serializable objects
    try:
        return data.to_json()
    except TypeError as e:
        # If the default serialization fails, use our custom encoder
        print(f"Using custom JSON encoder due to: {e}")
        try:
            # First try to convert to dict using dataclasses_json
            data_dict = data.to_dict(encode_json=False)
            # Then use our custom encoder to handle any remaining non-serializable objects
            return json.dumps(data_dict, cls=CustomJSONEncoder)
        except Exception as inner_e:
            # Log the error and dump data for debugging
            print(f"Custom encoder also failed: {inner_e}")
            with open("/tmp/data.py", "w") as f:
                print(data, file=f)
            # As a last resort, try to serialize with str() fallbacks for all objects
            try:
                return json.dumps(data, default=lambda o: str(o))
            except Exception:
                raise e


def build_react_app(data_path: Path) -> bool:
    """Build the React app with the provided data.

    Args:
        data_path: Path to the JSON data file

    Returns:
        True if the build was successful, False otherwise
    """
    try:
        print(f"Building React app with data from {data_path}")
        # Change to the React directory
        os.chdir(REACT_DIR)

        # Install dependencies
        subprocess.run(["npm", "install", "--legacy-peer-deps"], check=True)

        # Build the React app
        subprocess.run(["npm", "run", "build"], check=True)

        # Copy the data file to the dist directory
        shutil.copy(data_path, REACT_DIR / "dist" / "data.json")

        return True
    except subprocess.CalledProcessError as e:
        print(f"Error building React app: {e}")
        return False
    finally:
        # Change back to the original directory
        os.chdir(CURRENT_DIR)


def save_react_comparison_html_report(
    comparison_summary: ComparisonSummary, output_dir: Path
) -> Optional[str]:
    """Generate a React comparison HTML report and save it to the output directory.

    Args:
        comparison_summary: The comparison summary to generate a report for
        output_dir: The directory where the report should be saved

    Returns:
        The path to the HTML report file, or None if the build failed
    """
    print(f"Generating React report in {output_dir}")
    # Create the output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Generate the JSON data
    json_str = generate_comparison_json_str(comparison_summary)

    # Save the JSON data to a temporary file
    temp_data_path = CURRENT_DIR / "temp_data.json"
    with open(temp_data_path, "w") as f:
        f.write(json_str)

    # Build the React app
    build_success = build_react_app(temp_data_path)

    # Clean up the temporary file
    if temp_data_path.exists():
        temp_data_path.unlink()

    if not build_success:
        print("Failed to build React app")
        return None

    # Copy the built React app to the output directory
    react_build_dir = REACT_DIR / "dist"

    # Copy all files from the build directory to the output directory
    for item in react_build_dir.glob("*"):
        if item.is_file():
            shutil.copy(item, output_dir / item.name)
        elif item.is_dir():
            shutil.copytree(item, output_dir / item.name, dirs_exist_ok=True)

    # Return the path to the index.html file
    index_path = output_dir / "index.html"
    if index_path.exists():
        return str(index_path)
    else:
        print("index.html not found in the build output")
        return None

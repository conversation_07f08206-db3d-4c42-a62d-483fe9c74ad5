import argparse
import logging
import os
import time
import uuid
from pathlib import Path

import structlog

from experimental.vpas.agent.replay_eval.eval_harness import EvalHarness
from experimental.vpas.agent.replay_eval.eval_output import (
    ComparisonSummary,
)
from experimental.vpas.agent.replay_eval.html_report.react_report_generator import (
    save_react_comparison_html_report,
)
from experimental.vpas.agent.replay_eval.html_report.static_report_generator import (
    save_static_comparison_html_report,
)
from experimental.vpas.agent.replay_eval.model_config import DEFAULT_PROD_MODEL_NAME
from experimental.vpas.agent.replay_eval.utils import (
    load_model_config,
    load_samples,
)
from experimental.vpas.utils.git_utils import get_git_username

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("replay_eval")


# structlog output is too verbose, so we drop all output
# Configure structlog to use our custom processor that drops all output
class DropOutput:
    def __call__(self, logger, method_name, event_dict):
        # Return an empty dict instead of None
        return {}


# Configure structlog to use our custom processor that drops all output
structlog.configure(
    processors=[DropOutput()],
    wrapper_class=structlog.make_filtering_bound_logger(logging.CRITICAL),
    logger_factory=structlog.PrintLoggerFactory(file=open(os.devnull, "w")),
)

WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run replay evaluation")
    parser.add_argument(
        "--model-configs", required=False, nargs="+", help="Paths to model config files"
    )
    parser.add_argument(
        "--ref-model-config",
        required=False,
        default=f"{DEFAULT_PROD_MODEL_NAME}.py",
        help="Path to reference model config file",
    )
    # default to all samples in eval_samples/
    parser.add_argument(
        "--samples", default=".", help="Path to samples directory or file"
    )
    parser.add_argument(
        "--num-attempts", type=int, default=1, help="Number of attempts per sample"
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Force re-evaluation even if cached results exist",
    )
    parser.add_argument(
        "--limit-per-category",
        "-cl",
        type=int,
        default=None,
        help="Limit the number of samples per category",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )
    parser.add_argument(
        "--request-ids",
        "-ri",
        nargs="+",
        help="Filter samples to run eval only on specified request IDs",
    )
    parser.add_argument(
        "--static-report",
        action="store_true",
        help="Generate a fully static HTML report without interactive elements",
    )
    args = parser.parse_args()

    # Set root log level
    if args.debug:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

    git_username = get_git_username()

    # Load samples
    samples = load_samples(
        args.samples,
        limit_per_category=args.limit_per_category,
        request_ids=args.request_ids,
    )

    # Print sample count information
    print(f"Loaded {len(samples)} samples")
    if args.limit_per_category:
        print(f"Limited to {args.limit_per_category} samples per category")
    if args.request_ids:
        print(f"Filtered to request IDs: {args.request_ids}")

    dataset_path = args.samples
    if dataset_path == ".":
        dataset_path = "all_samples"
    dataset_name = dataset_path.replace("/", "_")

    # Load reference model config
    ref_model_config = load_model_config(args.ref_model_config)

    # Load model configs
    model_configs = []
    if args.model_configs:
        model_configs = [
            load_model_config(config_path) for config_path in args.model_configs
        ]

    # Create a unique identifier for this comparison
    limit_suffix = (
        f"_limit_{args.limit_per_category}" if args.limit_per_category else ""
    )
    request_ids_suffix = "_specific_request_ids" if args.request_ids else ""

    if not model_configs:
        rel_path = f"{git_username}/agent_replay_eval/eval_{ref_model_config.name}_{dataset_name}_attempts_{args.num_attempts}{limit_suffix}{request_ids_suffix}"
    else:
        model_names = "_".join([config.name for config in model_configs])[:20]
        rel_path = f"{git_username}/agent_replay_eval/comparison_{ref_model_config.name}_vs_{model_names}_{dataset_name}_attempts_{args.num_attempts}{limit_suffix}{request_ids_suffix}"

    # add random suffix to avoid overwriting
    rel_path += "_" + str(uuid.uuid4())[:4]

    # Create web server directory
    web_server_output_dir = WEB_SERVER_DIR / rel_path
    web_server_output_dir.mkdir(parents=True, exist_ok=True)

    # Run evaluation for reference model
    harness = EvalHarness(
        ref_model_config, num_attempts=args.num_attempts, no_cache=args.no_cache
    )
    ref_summary = harness.run_eval(samples, dataset_path)

    # Run evaluation for all other models
    new_model_summaries = {}
    for model_config in model_configs:
        harness = EvalHarness(
            model_config, num_attempts=args.num_attempts, no_cache=args.no_cache
        )
        summary = harness.run_eval(samples, dataset_path)
        new_model_summaries[model_config.name] = summary

    # Create comparison summary
    comparison_summary = ComparisonSummary(ref_summary, new_model_summaries)

    # Print terminal summary
    print_terminal_summary(
        comparison_summary,
        limit_per_category=args.limit_per_category,
        request_ids=args.request_ids,
    )

    # Generate and save report
    if args.static_report:
        # Generate static HTML report
        logger.debug("Generating static HTML report")
        static_start = time.time()
        html_path = save_static_comparison_html_report(
            comparison_summary, web_server_output_dir
        )
        static_time = time.time() - static_start
        logger.debug(f"Generated static HTML report in {static_time:.4f} seconds")

        # Print URL to the static report
        URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"
        # Get just the filename from the path
        if html_path:
            filename = Path(html_path).name
            url = URL_TEMPLATE.format(f"{rel_path}/{filename}")
            print(f"\nStatic HTML report available at: {url}")
        else:
            print("\nFailed to generate static HTML report")
    else:
        # Generate interactive React report
        logger.debug("Generating React report")
        react_start = time.time()
        html_path = save_react_comparison_html_report(
            comparison_summary, web_server_output_dir
        )
        react_time = time.time() - react_start
        logger.debug(f"Generated React report in {react_time:.4f} seconds")
        if html_path is None:
            logger.error("Failed to generate React report")
            return

        # Print URL to the react report
        URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"
        # Get just the filename from the path
        filename = Path(html_path).name
        url = URL_TEMPLATE.format(f"{rel_path}/{filename}")
        print(f"\nComparison react report available at: {url}")


def print_terminal_summary(
    comparison_summary: ComparisonSummary,
    limit_per_category: int | None = None,
    request_ids: list[str] | None = None,
) -> None:
    """
    Print a brief summary of the comparison results to the terminal.

    Args:
        comparison_summary: The comparison summary object containing evaluation results
        limit_per_category: The maximum number of samples per category, if any
        request_ids: List of request IDs used to filter samples, if any
    """
    ref_model = comparison_summary.ref_summary.model_config.name
    ref_results = comparison_summary.ref_summary.eval_results
    new_models = comparison_summary.new_model_summaries

    # Calculate reference model stats
    total_samples = len(ref_results)
    ref_correct_samples = sum(
        1 for result in ref_results.values() if result.num_correct_attempts > 0
    )
    ref_success_rate = ref_correct_samples / total_samples if total_samples > 0 else 0

    print("\n" + "=" * 80)
    print("EVALUATION SUMMARY")
    print("=" * 80)

    # Print reference model stats
    print(f"\nReference Model: {ref_model}")
    print(f"Total samples: {total_samples}")
    print(f"Correct samples: {ref_correct_samples} ({ref_success_rate:.2%})")

    # Print category limit info if applicable
    if limit_per_category:
        print(f"Note: Limited to {limit_per_category} samples per category")

    # Print request IDs info if applicable
    if request_ids:
        print(f"Note: Filtered to request IDs: {request_ids}")

    # Print comparison for each new model
    if new_models:
        print("\nModel Comparisons:")

        for model_name, summary in new_models.items():
            print(f"\n{'-' * 40}")
            print(f"Model: {model_name}")

            # Calculate new model stats
            new_results = summary.eval_results
            new_correct_samples = sum(
                1 for result in new_results.values() if result.num_correct_attempts > 0
            )
            new_success_rate = (
                new_correct_samples / len(new_results) if new_results else 0
            )

            # Find improvements and regressions
            improvements = 0
            regressions = 0

            # Compare samples that exist in both summaries
            common_samples = set(ref_results.keys()) & set(new_results.keys())

            for sample_name in common_samples:
                ref_result = ref_results[sample_name]
                new_result = new_results[sample_name]

                ref_correct = ref_result.num_correct_attempts > 0
                new_correct = new_result.num_correct_attempts > 0

                if not ref_correct and new_correct:
                    improvements += 1
                elif ref_correct and not new_correct:
                    regressions += 1

            # Print stats
            print(
                f"Success rate: {new_success_rate:.2%} ({new_correct_samples}/{len(new_results)})"
            )
            print(
                f"Improvement over reference: {(new_success_rate - ref_success_rate):.2%}"
            )
            print(f"Improvements: {improvements}")
            print(f"Regressions: {regressions}")
            print(f"Net change: {improvements - regressions}")

    print("\n" + "=" * 80)


if __name__ == "__main__":
    main()

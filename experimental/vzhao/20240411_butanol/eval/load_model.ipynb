#%% md
# Load Scorer
#%%
from megatron.neox_arguments import NeoXArgs
import pathlib
neox_args = NeoXArgs.from_ymls(
    [pathlib.Path("/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/config.yml")]
)
#%%
from augment.research.retrieval.libraries.scorers import dense_scorer
from augment.research.retrieval.libraries import prompt_formatters
from research.core.data_paths import canonicalize_path
from research.core.constants import AUGMENT_ROOT

scorer = dense_scorer.Starcoder_1B_Scorer(
    checkpoint_path="butanol/butanol_fr_starethanol_v2_proj_512_384",
    # checkpoint_path="starethanol/starethanol6_16.1_mean_proj_512_2000",
    additional_yaml_files=[
        canonicalize_path(
            "experimental/vzhao/20240110_star_ethanol_proj/modeling/configs/emb_proj_512.yml",
            new_path=AUGMENT_ROOT,
        ),
    ],
)
scorer.load()
#%%

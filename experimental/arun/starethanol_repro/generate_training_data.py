"""<PERSON><PERSON><PERSON> to generate training data.

This script generates training data for the Starethanol model.

It is essentially a script version of
 experimental/vzhao/20231129_star_ethanol/data/20231129_tokenize_ethanol_6.ipynb
"""

import json
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Iterator, cast

import pandas as pd

from base.prompt_format_retrieve import (
    Ethanol<PERSON>D<PERSON>ument<PERSON><PERSON>atter,
    Ethanol<PERSON>QueryFormatter,
    TokenApportionmentConfig,
)
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import RetrievalSpecialTokens
from experimental.vzhao.data import pandas_functions
from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.core.types import Chunk
from research.data.rag.retrieval_utils import deserialize_retrieved_chunks
from research.data.spark import k8s_session
from research.data.spark.pipelines.stages.common import (
    ExportIndexedDatasetConfig,
    export_indexed_dataset,
    pack_tokens,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import AugmentK8sSparkSession
from research.retrieval.chunk_formatters import get_chunk_formatter
from research.retrieval.utils import parse_yaml_config

logger = logging.getLogger(__name__)


def create_prompt_formatter(formatter_config: dict):
    cls_name, kwargs = parse_yaml_config(formatter_config)
    return get_prompt_formatter(cls_name, **kwargs)


def create_chunk_formatter(formatter_config: dict):
    cls_name, kwargs = parse_yaml_config(formatter_config)
    return get_chunk_formatter(cls_name, **kwargs)


@dataclass
class TokenizeQueryKeyConfig:
    tokenizer_name: str
    query_prompt_formatter_config: dict
    key_prompt_formatter_config: dict
    max_query_length: int
    max_key_length: int
    truncate_keys: bool = False


def create_tokenize_query_and_key_fn(
    config: TokenizeQueryKeyConfig,
) -> map_parquet.FlatMapFn:
    """A function to tokenize and prepare prompts for dual encoder training."""

    tokenizer = create_tokenizer_by_name(config.tokenizer_name)

    query_prompt_formatter = Ethanol6QueryFormatter(
        tokenizer=tokenizer, **config.query_prompt_formatter_config
    )
    key_prompt_formatter = Ethanol6DocumentFormatter(
        tokenizer=tokenizer, **config.key_prompt_formatter_config
    )

    @map_parquet.passthrough_feature()
    @map_parquet.allow_unused_args()
    def tokenize_query_and_key(
        prefix: str,
        suffix: str,
        file_path: str,
        retrieved_chunks: str | list[Chunk],
        retrieval_rank: str | list[int],
        ppl: str,
    ) -> Iterator[pd.Series]:
        # Pulls in registrations
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)

        if isinstance(retrieved_chunks, str):
            retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks)
        if isinstance(retrieval_rank, str):
            retrieval_rank = cast(list[int], json.loads(retrieval_rank))

        ppl_scores: list[float] = json.loads(ppl)

        # Gets query prompt.

        end_of_query_token = tokenizer.special_tokens.end_of_query
        query_tokens = query_prompt_formatter.format_prompt(
            CompletionRetrieverPromptInput(
                prefix=prefix,
                suffix=suffix,
                path=file_path,
            ),
            0,
        ).tokens()
        if end_of_query_token not in query_tokens:
            # For reasons, the special query token is NOT added by research prompt
            # formatters but it should be.
            query_tokens.append(end_of_query_token)
        assert (
            sum([1 for t in query_tokens if t == end_of_query_token]) == 1
        ), f"Found multiple end of query tokens: {query_tokens=}"

        if len(query_tokens) > config.max_query_length:
            logger.warning(
                "Dropping sample: Query token length exceeds seq_len: %d > %d.",
                len(query_tokens),
                config.max_query_length,
            )
            return

        # Gets document prompt.
        end_of_key_token = tokenizer.special_tokens.end_of_key
        assert (
            len(retrieved_chunks) == len(retrieval_rank)
        ), f"Retrieved chunks and rank do not match: {len(retrieved_chunks)=} != {len(retrieval_rank)=}"

        combined_chunk_tokens = []

        for chunk_idx, chunk in enumerate(retrieved_chunks):
            # Drop the first retrieval chunk, which is the empty chunk.
            if retrieval_rank[chunk_idx] < 0:
                continue
            assert (
                retrieval_rank[chunk_idx] + 1 == chunk_idx
            ), f"Retrieved chunks and rank do not match: {retrieval_rank[chunk_idx]=} != {chunk_idx=}"

            chunk_tokens = key_prompt_formatter.format_prompt(
                CompletionRetrieverPromptInput(
                    prefix=chunk.text,
                    suffix="",
                    path=chunk.parent_doc.path,
                ),
                0,
            ).tokens()
            if config.truncate_keys:
                chunk_tokens = chunk_tokens[: config.max_key_length - 1]

            if end_of_key_token not in chunk_tokens:
                # For reasons, the special query token is NOT added by research prompt
                # formatters but it should be.
                chunk_tokens.append(end_of_key_token)
            assert (
                sum([1 for t in chunk_tokens if t == end_of_key_token]) == 1
            ), f"Found multiple end of key tokens: {chunk_tokens=}"

            if len(chunk_tokens) > config.max_key_length:
                logger.warning(
                    "Dropping sample: Chunk %d too long: %d > %d.",
                    chunk_idx,
                    len(chunk_tokens),
                    config.max_key_length,
                )
                return

            # Encode the perplexity score into tokens -- we don't care how many tokens
            # this takes.
            ppl_info_tokens = tokenizer.tokenize_safe(f"{ppl_scores[chunk_idx]}")

            # Format the footer of the prompt
            chunk_tokens.extend(ppl_info_tokens)
            chunk_tokens.append(end_of_key_token)

            # Add this to our list.
            combined_chunk_tokens.append(pack_tokens(chunk_tokens))

        yield pd.Series(
            {
                "query_tokens": pack_tokens(query_tokens),
                "document_tokens": combined_chunk_tokens,
            }
        )

    return tokenize_query_and_key


def create_combine_query_and_key_tokens_fn() -> map_parquet.FlatMapFn:
    """A function to tokenize and prepare prompts for dual encoder training."""

    @map_parquet.allow_unused_args()
    def combine_query_and_key_tokens(
        query_tokens: bytes,
        document_tokens: list[bytes],
    ) -> Iterator[pd.Series]:
        # We can concatenate the bytes directly without any decoding.
        tokens = b"".join([query_tokens, *document_tokens])
        yield pd.Series({"tokens": tokens})

    return combine_query_and_key_tokens


@dataclass
class StageTokenizeSamplesConfig:
    input_path: str
    output_path: str

    tokenize_query_and_key_config: TokenizeQueryKeyConfig


def stage_tokenize_samples(
    config: StageTokenizeSamplesConfig,
    max_workers: int = 360,
    batch_size: int = 1000,
    timing_run: bool = False,
    stage_name: str = "tokenize_samples",
    task_info_location: str = "/mnt/efs/spark-data/temp_weekly/map_parquet_task_logs/tokenize_samples/",
    spark: AugmentK8sSparkSession | None = None,
):
    if spark is None:
        spark = k8s_session(
            name=stage_name,
            conf={
                "spark.executor.pyspark.memory": "1050g",
                "spark.task.cpus": "5",
            },
            max_workers=5 if timing_run else max_workers,
        )

    map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                pandas_functions.keep_homogeneous_token_ppl,
                pandas_functions.mean_ppl,
                # Always run the following.
                create_tokenize_query_and_key_fn(config.tokenize_query_and_key_config),
            ]
        ),
        input_path=config.input_path,
        output_path=config.output_path,
        timeout=7 * 24 * 3600,
        batch_size=10 if timing_run else batch_size,
        timing_run=timing_run,
        task_info_location=task_info_location,
    )

    return spark


@dataclass
class StageIndexedDatasetConfig:
    input_path: str
    output_path: str
    tmp_path: str
    tokenizer_name: str


def stage_indexed_dataset(
    config: StageIndexedDatasetConfig,
    max_workers: int = 360,
    batch_size: int = 1000,
    timing_run: bool = False,
    stage_name: str = "create_indexed_dataset",
    task_info_location: str = "/mnt/efs/spark-data/temp_weekly/map_parquet_task_logs/create_indexed_dataset/",
    spark: AugmentK8sSparkSession | None = None,
):
    tokenizer = create_tokenizer_by_name(config.tokenizer_name)

    if spark is None:
        spark = k8s_session(
            name=stage_name,
            efs_path=Path("/mnt/efs/spark-data"),
            conf={
                "spark.executor.pyspark.memory": "1050g",
                "spark.task.cpus": "5",
            },
            max_workers=5 if timing_run else max_workers,
            skip_bazel_build=False,
        )

    map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                create_combine_query_and_key_tokens_fn(),
            ]
        ),
        input_path=config.input_path,
        output_path=config.tmp_path,
        timeout=7 * 24 * 3600,
        batch_size=10 if timing_run else batch_size,
        timing_run=timing_run,
        task_info_location=task_info_location,
    )

    export_indexed_dataset(
        config=ExportIndexedDatasetConfig(
            name=stage_name,
            input=config.tmp_path,
            output=Path(config.output_path),
            samples_column="tokens",
            num_validation_samples=1024,
        ),
        spark=spark,
        tokenizer=tokenizer,
    )

    return spark


def main():
    import logging

    logging.basicConfig(level=logging.INFO, force=True)
    logging.info("Starting spark...")

    spark = None

    INPUT_PATH = "s3a://igor-dev-bucket/ethanol6/ethanol6-17.1/06_shuffled/"

    # tokenizer_name = "StarCoderTokenizer"
    tokenizer_name = "DeepSeekCoderBaseTokenizer"
    TOKENIZE_OUTPUT_PATH = (
        # "s3a://augment-temporary/arun/starethanol6-17.1/08_mean_starcoder_tokens/"
        "s3a://augment-temporary/arun/deepethanol6-17.1/08_mean_starcoder_tokens_no_bos/"
    )
    TOKENIZE_TMP_PATH = (
        # "s3a://augment-temporary/arun/starethanol6-17.1/08_mean_starcoder_tokens_tmp/"
        "s3a://augment-temporary/arun/deepethanol6-17.1/08_mean_starcoder_tokens_no_bos_tmp/"
    )
    INDEXED_DATASET_OUT_PATH = (
        # "/mnt/efs/spark-data/user/arun/starethanol6-17.1/"
        "/mnt/efs/spark-data/user/arun/deepethanol6-17.1_no_bos/"
    )

    spark = stage_tokenize_samples(
        StageTokenizeSamplesConfig(
            input_path=INPUT_PATH,
            output_path=TOKENIZE_OUTPUT_PATH,
            tokenize_query_and_key_config=TokenizeQueryKeyConfig(
                tokenizer_name="deepseek_coder_base",
                query_prompt_formatter_config={
                    "apportionment_config": TokenApportionmentConfig(
                        max_content_len=1024,
                        input_fraction=0,
                        prefix_fraction=0,
                        max_path_tokens=0,
                    ),
                    "add_path": True,
                    "add_suffix": True,
                    "prefix_suffix_budget_fraction": 0.9,
                },
                key_prompt_formatter_config={
                    "apportionment_config": TokenApportionmentConfig(
                        max_content_len=1024,
                        input_fraction=0,
                        prefix_fraction=0,
                        max_path_tokens=0,
                    ),
                    "add_path": True,
                },
                max_query_length=1024,
                max_key_length=1024,
                truncate_keys=False,
            ),
        ),
        timing_run=False,
    )

    spark = stage_indexed_dataset(
        StageIndexedDatasetConfig(
            input_path=TOKENIZE_OUTPUT_PATH,
            tmp_path=TOKENIZE_TMP_PATH,
            output_path=INDEXED_DATASET_OUT_PATH,
            tokenizer_name=tokenizer_name,
        ),
        timing_run=False,
        spark=spark,
    )

    spark.stop()


if __name__ == "__main__":
    main()

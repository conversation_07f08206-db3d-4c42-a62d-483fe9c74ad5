#%%
import sys

sys.path.append("/home/<USER>/repos/augment/research/gpt-neox")
sys.path.append("/home/<USER>/repos/augment")
#%%
import random
import numpy as np
import torch

from pathlib import Path
from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder
from tqdm import tqdm
#%%
def get_all_samples(path: Path):
    dataset = MMapIndexedDataset(str(path))
    all_samples = []
    for i in tqdm(range(len(dataset))):
        all_samples.append(dataset[i].copy())
    return all_samples

def save_dataset(samples, output_path):
    random.shuffle(samples)
    if not output_path.parent.exists():
        output_path.parent.mkdir()

    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(".bin"), dtype=np.int32)
    for sample in tqdm(samples):
        builder.add_item(torch.tensor(sample, dtype=torch.int32))
        builder.end_document()
    builder.finalize(output_path.with_suffix(".idx"))
#%%
new_training_data_path = Path("/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8/train")
new_validation_data_path = Path("/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8/valid")

new_training_samples = get_all_samples(new_training_data_path)
new_validation_samples = get_all_samples(new_validation_data_path)
#%%
TRAIN_SPLIT = 4000
VALID_SPLIT = 400
#%%
s1_training_samples = get_all_samples(Path("/home/<USER>/tmp/droid-repo-47/train"))
s1_validation_samples = get_all_samples(Path("/home/<USER>/tmp/droid-repo-47/validation"))

s2_training_samples = get_all_samples(Path("/home/<USER>/tmp/droid-repo-48/train"))
s2_validation_samples = get_all_samples(Path("/home/<USER>/tmp/droid-repo-48/validation"))
#%%
output_path1 = Path("/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8_joined_stage1")
output_path2 = Path("/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8_joined_stage2")
#%%
save_dataset(s1_training_samples + new_training_samples[:TRAIN_SPLIT], output_path1 / "train")
save_dataset(s1_validation_samples + new_validation_samples[:VALID_SPLIT], output_path1 / "valid")

save_dataset(s2_training_samples + new_training_samples[TRAIN_SPLIT:], output_path2 / "train")
save_dataset(s2_validation_samples + new_validation_samples[VALID_SPLIT:], output_path2 / "valid")

#%%

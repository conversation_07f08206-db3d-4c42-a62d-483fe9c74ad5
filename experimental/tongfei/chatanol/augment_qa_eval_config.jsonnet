function (
    det_ws, det_proj, det_name, augment_qa_ver,
    inference_model, seq_len, inference_tokenizer, prompt_formatter,
    query_formatter, document_formatter, retriever_tokenizer,
    scorer_type, retriever_ckpt, retriever_query_max_tokens, retriever_doc_max_tokens
) {
    "podspec": "1xH100.yaml",
    "determined": {
        "metaconfig": "jobs/templates/eval-exec-v2-metaconfig.yaml",
        "workspace": det_ws,
        "project": det_proj,
        "name": det_name
    },
    "augment": {
        "gpu_count": 1
    },
    "system": {
        "name": "chat_rag",
        "model": {
            "name": inference_model,
            "sequence_length": seq_len,
        },
        "prompt_formatter": {
            "tokenizer_name": inference_tokenizer,
            "prompt_formatter_name": prompt_formatter,
            "prefix_len": 2048,
            "suffix_len": 2048,
            "path_len": 256,
            "message_len": -1,
            "selected_code_len": -1,
            "chat_history_len": 4096,
            "retrieval_len_per_each_user_guided_file": 3000,
            "retrieval_len_for_user_guided": 8000,
            "retrieval_len": -1,  # Fill the rest of the input prompt with retrievals
            "max_prompt_len": 12288,
        },
        "generation_options": {
            "temperature": 0,
            "top_k": 0,
            "top_p": 0,
            "max_generated_tokens": 8192
        },
        "retriever": {
            "scorer": {
                "name": scorer_type,
                "checkpoint_path": retriever_ckpt,
            },
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30
            },
            "query_formatter": {
                "name": query_formatter,
                "tokenizer_name": retriever_tokenizer,
                "max_tokens": retriever_query_max_tokens,
            },
            "document_formatter": {
                "name": document_formatter,
                "tokenizer_name": retriever_tokenizer,
                "add_path": true,
                "max_tokens": retriever_doc_max_tokens,
            }
        },
        "experimental": {
            "retriever_top_k": 128
        },
        "verbose": false
    },
    "task": {
        "name": "augment_qa",
        "dataset_path": "/mnt/efs/augment/data/processed/augment_qa/" + augment_qa_ver,
        "html_report_output_dir": "/mnt/efs/augment/public_html/augment_qa/" + augment_qa_ver
    }
}

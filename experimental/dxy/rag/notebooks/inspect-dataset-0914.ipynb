#%%
%load_ext autoreload
%autoreload 2
#%%
import pathlib
import megatron.data.indexed_dataset as indexed_dataset
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer

# datapath = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_v2.0-sc2/dataset"
# datapath = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden_v2.0-sc2/dataset"
# datapath = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0913_v2"
# datapath = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0913_v2"
datapath = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-tutor-hindsight/0913-simple_elden_v2"
# Load this dataset via IndexedDataset.
dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)
print(f"The dataset has {len(dataset)} records.")
print(f"First record has {len(dataset[0])} tokens.")
#%%
tokenizer = StarCoder2Tokenizer()
special_tokens = tokenizer.special_tokens

print(f"{special_tokens.eos = }")
print(f"{special_tokens.padding = }")
#%%
from experimental.dxy.rag.rlhf.shared_lib import extract_tokens_before_stop_tokens

tokens = dataset[6].tolist()
tokens = extract_tokens_before_stop_tokens(tokens, [special_tokens.padding])
print(tokenizer.detokenize(tokens))
#%%
import megatron.data.indexed_dataset as indexed_dataset
from base.tokenizers import Llama3BaseTokenizer

# datapath = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/validation_dataset"
datapath = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/dataset"

dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)
print(f"The dataset has {len(dataset)} records.")
tokenizer = Llama3BaseTokenizer()
special_tokens = tokenizer.special_tokens

index = 3
# Find the index of the first pad token.
tokens = dataset[index].tolist()
tokens = tokens[: tokens.index(special_tokens.padding)]
print(tokenizer.detokenize(tokens))
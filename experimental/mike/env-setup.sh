#!/bin/bash
# Environment Setup Script for Mike's Dev Environment
# This script sets up a fresh Ubuntu image with all necessary tools and configurations

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on Ubuntu
if ! grep -q "Ubuntu" /etc/os-release; then
    log_error "This script is designed for Ubuntu. Current OS:"
    cat /etc/os-release
    exit 1
fi

log_info "Starting environment setup..."

# Update system packages
log_info "Updating system packages..."
sudo apt-get update
sudo apt-get upgrade -y

# Install essential packages
log_info "Installing essential packages..."
sudo apt-get install -y \
    curl \
    wget \
    git \
    vim \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    zsh \
    fzf \
    jq \
    unzip \
    python3-pip \
    python3-venv

# Install Zsh and set as default shell
log_info "Setting up Zsh..."
if [ "$SHELL" != "/usr/bin/zsh" ]; then
    sudo chsh -s $(which zsh)
    log_info "Zsh set as default shell. You'll need to log out and back in for this to take effect."
fi

# Install Oh My Zsh
log_info "Installing Oh My Zsh..."
if [ ! -d "$HOME/.oh-my-zsh" ]; then
    sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended
else
    log_info "Oh My Zsh already installed"
fi

# Install Powerlevel10k
log_info "Installing Powerlevel10k..."
if [ ! -d "$HOME/powerlevel10k" ]; then
    git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ~/powerlevel10k
else
    log_info "Powerlevel10k already installed"
fi

# Install Homebrew (Linuxbrew)
log_info "Installing Homebrew..."
if ! command -v brew &> /dev/null; then
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    log_info "Homebrew already installed"
fi

# Install Go
log_info "Installing Go..."
if ! command -v go &> /dev/null; then
    GO_VERSION="1.22.0"  # Update this to latest stable version
    wget -q -O go.tar.gz "https://go.dev/dl/go${GO_VERSION}.linux-amd64.tar.gz"
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf go.tar.gz
    rm go.tar.gz
    export PATH=$PATH:/usr/local/go/bin:$HOME/go/bin
else
    log_info "Go already installed"
fi


# Install jump (directory navigation tool)
log_info "Installing jump..."
if ! command -v jump &> /dev/null; then
    # Ensure Go is in PATH for this session
    export PATH=$PATH:/usr/local/go/bin:$HOME/go/bin
    go install github.com/gsamokovarov/jump@latest
    log_info "jump installed successfully"
    log_info "To use jump, you'll need to add 'eval \"\$(jump shell)\"' to your shell configuration"
else
    log_info "jump already installed"
fi


# Install Graphite CLI
log_info "Installing Graphite CLI..."
if ! command -v gt &> /dev/null; then
    # Ensure Homebrew is in PATH for this session
    eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
    brew install withgraphite/tap/graphite
    log_info "Graphite CLI installed successfully"
    log_info "To authenticate Graphite, visit https://app.graphite.dev/activate and run the provided auth command"
else
    log_info "Graphite CLI already installed"
fi

# Install kubectl
log_info "Installing kubectl..."
if ! command -v kubectl &> /dev/null; then
    curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
    rm kubectl
    log_info "kubectl installed successfully"
else
    log_info "kubectl already installed"
fi

# Install kubectx and kubens
log_info "Installing kubectx and kubens..."
if ! command -v kubectx &> /dev/null || ! command -v kubens &> /dev/null; then
    # Ensure Homebrew is in PATH for this session
    eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
    brew install kubectx
    log_info "kubectx and kubens installed successfully"
    log_info "These tools help you switch between Kubernetes contexts and namespaces easily"
else
    log_info "kubectx and kubens already installed"
fi

# Create necessary directories
log_info "Creating necessary directories..."
mkdir -p ~/.config/augment
mkdir -p ~/.local/bin
mkdir -p ~/.npm-global

# Configure npm to use global directory
npm config set prefix '~/.npm-global'

# Backup existing .zshrc if it exists
if [ -f "$HOME/.zshrc" ]; then
    log_info "Backing up existing .zshrc..."
    cp "$HOME/.zshrc" "$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Create .zshrc with all configurations
log_info "Creating .zshrc configuration..."
cat > "$HOME/.zshrc" << 'EOF'
# Enable Powerlevel10k instant prompt. Should stay close to the top of ~/.zshrc.
# Initialization code that may require console input (password prompts, [y/n]
# confirmations, etc.) must go above this block; everything else may go below.
if [[ -r "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-instant-prompt-${(%):-%n}.zsh" ]]; then
  source "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-instant-prompt-${(%):-%n}.zsh"
fi

# Path to your Oh My Zsh installation.
export ZSH="$HOME/.oh-my-zsh"

# Set name of the theme to load
ZSH_THEME="robbyrussell"

# Which plugins would you like to load?
plugins=(git)

source $ZSH/oh-my-zsh.sh

# User configuration
export EDITOR=vim

# The next line updates PATH for the Google Cloud SDK.
if [ -f "$HOME/google-cloud-sdk/path.zsh.inc" ]; then . "$HOME/google-cloud-sdk/path.zsh.inc"; fi

# The next line enables shell command completion for gcloud.
if [ -f "$HOME/google-cloud-sdk/completion.zsh.inc" ]; then . "$HOME/google-cloud-sdk/completion.zsh.inc"; fi

source ~/powerlevel10k/powerlevel10k.zsh-theme

# To customize prompt, run `p10k configure` or edit ~/.p10k.zsh.
[[ ! -f ~/.p10k.zsh ]] || source ~/.p10k.zsh

# aliases
alias gs="git status"
alias k="kubectl"
alias kdev="kubectl --context=dev"
alias kdev-agent="kubectl --context=gke_augment-research-gsc_us-central1_gcp-agent0 -n dev-mike"
alias kprod-agent="kubectl --context=gke_agent-sandbox-prod_us-central1_gcp-prod-agent0 -n staging-shard-0"
alias j="jump cd"
alias clean_dev_k8s="kubectl delete deployments --all; kubectl delete configmaps --all; kubectl delete transformation-keys --all;"

export PATH="/home/<USER>/.local/bin:$PATH"
export PATH="$PATH:$HOME/flutter/bin"
export CHROME_EXECUTABLE=/usr/bin/chromium-browser

eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
export PATH=$PATH:$HOME/go/bin
export PATH=$PATH:/usr/local/go/bin

export PATH=~/.npm-global/bin:$PATH

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
. "$HOME/.cargo/env"

# fuzzy finder
source /usr/share/doc/fzf/examples/key-bindings.zsh

# jump directory navigation
eval "$(jump shell)"

# Function to view k8s logs for a service with optional tail lines
klog() {
  local service="$1"
  local lines="${2:-15}"  # Default to 15 lines if not specified

  if [[ -z "$service" ]]; then
    echo "Usage: klog <service-name> [number-of-lines]"
    return 1
  fi

  kubectl logs -l app="$service" --tail "$lines"
}
EOF

# Create .zshenv
log_info "Creating .zshenv..."
cat > "$HOME/.zshenv" << 'EOF'
. "$HOME/.cargo/env"
EOF

# Download .p10k.zsh if it doesn't exist
if [ ! -f "$HOME/.p10k.zsh" ]; then
    log_info "Downloading Powerlevel10k configuration..."
    # You can customize this URL to point to your specific p10k configuration
    curl -fsSL https://raw.githubusercontent.com/romkatv/powerlevel10k/master/config/p10k-lean.zsh -o "$HOME/.p10k.zsh"
fi

log_info "Setup complete!"
log_info ""
log_info "Next steps:"
log_info "1. Log out and log back in to use Zsh as your default shell"
log_info "2. Run 'p10k configure' to customize your Powerlevel10k prompt"
log_info "3. Configure your kubectl contexts and Google Cloud authentication"
log_info "4. Authenticate Graphite CLI by visiting https://app.graphite.dev/activate"
log_info "5. Add your Augment-specific configurations and secrets"
log_info ""
log_info "To add Augment-specific functions and aliases, append the content from your current .zshrc"
log_info "starting from the '# With access to augment.git' section."

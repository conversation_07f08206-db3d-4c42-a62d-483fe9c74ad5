#%%
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

torch.cuda.empty_cache()

# Set random seed for reproducibility
torch.random.manual_seed(0)

# Load model and tokenizer
model = AutoModelForCausalLM.from_pretrained(
    "microsoft/Phi-3.5-mini-instruct",
    device_map="cuda",
    torch_dtype="auto",
    trust_remote_code=True,
    attn_implementation="flash_attention_2",
)
tokenizer = AutoTokenizer.from_pretrained("microsoft/Phi-3.5-mini-instruct")
#%%
import pandas as pd
import os

LOGS_DIR = "/mnt/efs/spark-data/shared/pr_v2/failed_logs"


def sample_files(max_files: int = 20):
    return (
        pd.Series(os.listdir(LOGS_DIR)).sample(n=max_files, random_state=42).to_list()
    )


def load_data(files: list[str]):
    sample_raw = pd.concat([pd.read_parquet(os.path.join(LOGS_DIR, f)) for f in files])

    # Remove builds from the same PR (duplications)
    sample_full = sample_raw.drop_duplicates(
        ["owner", "name", "pr_number", "check_run_name"]
    )

    # Remove checks without logs
    sample_full = sample_full[sample_full["log"].str.len() > 0].reset_index()

    print(f"Got {sample_full.shape[0]} samples")

    # Remove timestamps as they are very token expensive
    datetime_regex = r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z"
    sample_full["log"] = (
        sample_full["log"].str.replace(datetime_regex, "", regex=True).str.strip()
    )

    # Remove all non ascii characters
    sample_full["log"] = (
        sample_full["log"].str.encode("ascii", "ignore").str.decode("ascii")
    )

    # Remove ANSI escape sequences
    ansi_escape_regex = r"\x1B[@-_][0-?]*[ -/]*[@-~]"
    sample_full["log"] = sample_full["log"].str.replace(
        ansi_escape_regex, "", regex=True
    )

    return sample_full


def apply_manual_annotation(df: pd.DataFrame):
    # Pytest
    pydpf_core_filter = {
        "owner": "ansys",
        "name": "pydpf-core",
        "check_run_name": "tests / Tests (3.9, ubuntu-latest)",
    }

    # vitest
    appui_filter = {
        "owner": "itwin",
        "name": "appui",
        "check_run_name": "test (macos-latest)",
    }

    # go test
    bincapz_filter = {
        "owner": "chainguard-dev",
        "name": "bincapz",
        "check_run_name": "test",
    }

    # sphinx-build (documentation compiler)
    itr_filter = {
        "owner": "os-climate",
        "name": "itr",
        "check_run_name": "Rebuild documentation",
    }

    # pytest
    r2r_filter = {"owner": "sciphi-ai", "name": "r2r", "check_run_name": "pytest"}

    def filter_repo(df, key_dict):
        return (df["owner"] == key_dict["owner"]) & (df["name"] == key_dict["name"])

    def filter_check(df, key_dict):
        return filter_repo(df, key_dict) & (
            df["check_run_name"] == key_dict["check_run_name"]
        )

    evalset = df[
        filter_check(df, pydpf_core_filter)
        | filter_check(df, appui_filter)
        | filter_check(df, bincapz_filter)
        | filter_check(df, itr_filter)
        | filter_check(df, r2r_filter)
    ].reset_index()

    evalset["is_pytest"] = filter_check(evalset, pydpf_core_filter) | filter_check(
        evalset, r2r_filter
    )
    print(f"Got {evalset['is_pytest'].mean():.2%} of eval samples are pytest")
    evalset["is_go_test"] = filter_check(evalset, bincapz_filter)
    print(f"Got {evalset['is_go_test'].mean():.2%} of eval samples are go test")
    evalset["is_test"] = (
        filter_check(evalset, pydpf_core_filter)
        | filter_check(evalset, pydpf_core_filter)
        | filter_check(evalset, bincapz_filter)
        | filter_check(evalset, r2r_filter)
    )
    print(f"Got {evalset['is_test'].mean():.2%} of eval samples are testing framework")

    print(f"Got {evalset.shape[0]} eval samples")
    return evalset
#%%
files = sample_files(max_files=20)
eval_files = ["part-00000.parquet", "part-00001.parquet", "part-00002.parquet"]
sample_full = load_data(files)
evalset = apply_manual_annotation(load_data(eval_files))
#%%
from tqdm.notebook import tqdm
import numpy as np

prefix_text = """<|system|>You are an expert AI software engineer, capable of responding with only 'Yes' or 'No'<|end|><|user|>
```
"""
suffix_text = """
```
Does this log excerpt contains output of {type_class} command?<|end|><|assistant|>"""


def _predict(log, type_class, max_log_tokens=5000):
    # Tokenization
    prefix = tokenizer(prefix_text, return_tensors="pt").input_ids
    suffix = tokenizer(
        suffix_text.format(type_class=type_class), return_tensors="pt"
    ).input_ids

    log_tokens = tokenizer(log, return_tensors="pt", truncation=False).input_ids[
        :, -max_log_tokens:
    ]
    truncated_logs = tokenizer.decode(log_tokens[0], skip_special_tokens=True)
    lines_after_truncation = len(truncated_logs.split("\n"))

    input_ids = torch.cat([prefix, log_tokens, suffix], dim=1)
    with torch.no_grad():
        logits = model(input_ids.cuda()).logits[:, -1, :].cpu()

    probs = torch.softmax(logits, dim=-1)

    # Extract probabilities for 'Yes' and 'No'
    true_prob = probs[0, tokenizer.encode("Yes")[0]].item()
    false_prob = probs[0, tokenizer.encode("No")[0]].item()
    pos = true_prob / (true_prob + false_prob)

    return pos, lines_after_truncation


def predict(logs: pd.Series, type_class, max_log_tokens=10000):
    proba, lines_after_truncation = zip(
        *[_predict(log, type_class, max_log_tokens) for log in tqdm(logs)]
    )

    return pd.Series(proba, dtype=np.float32), pd.Series(
        lines_after_truncation, dtype=np.int32
    )
#%%
from matplotlib import pyplot as plt
from sklearn.metrics import roc_curve, accuracy_score, recall_score
from sklearn.calibration import calibration_curve


# Helper function to plot histograms
def plot_histogram(data, title, bins=20):
    data.plot.hist(bins=bins, title=title)
    plt.show()


# Helper function to filter dataset by minimum number of lines
def filter_by_lines(data, min_lines):
    num_filter = data[
        (data["lines_after_truncation"] < min_lines)
        | (data["lines_after_truncation"] == data["lines"])
    ].shape[0]
    if num_filter > 0:
        print(
            f"Filtered {num_filter}/{data.shape[0]} ({num_filter / data.shape[0]:.2%}) logs with less than {min_lines} lines"
        )
    filtered_data = data[data["lines_after_truncation"] >= min_lines]
    return filtered_data


# Helper function to assert data validity
def assert_validity(data, y_col, min_lines):
    assert data.shape[0] > 0, f"No logs with more than {min_lines} lines"
    if y_col:
        assert data[data[y_col]].shape[0] > 0, f"No logs with {y_col} = True"
        assert data[~data[y_col]].shape[0] > 0, f"No logs with {y_col} = False"


# Plot the ROC curve and find the best threshold
def plot_roc_curve(proba, y):
    fpr, tpr, thresholds = roc_curve(y, proba)
    distances = np.sqrt((1 - tpr) ** 2 + fpr**2)
    best_index = np.argmin(distances)
    best_threshold = thresholds[best_index]

    plt.figure(figsize=(5, 5))
    plt.plot(fpr, tpr, label="ROC curve")
    plt.scatter(
        fpr[best_index],
        tpr[best_index],
        color="red",
        label=f"Best Threshold = {best_threshold:.3f}",
        zorder=5,
    )
    plt.xlabel("False Positive Rate")
    plt.ylabel("True Positive Rate")
    plt.title("ROC Curve")
    plt.show()

    return best_threshold


# Plot reliability curve
def plot_reliability_curve(proba, y, n_bins=10):
    fraction_of_positives, mean_predicted_value = calibration_curve(
        y, proba, n_bins=n_bins
    )
    plt.figure(figsize=(5, 5))
    plt.plot(
        mean_predicted_value, fraction_of_positives, "s-", label="Reliability Curve"
    )
    plt.plot([0, 1], [0, 1], color="gray", linestyle="--", label="Perfectly Calibrated")
    plt.xlabel("Mean Predicted Probability")
    plt.ylabel("Fraction of Positives")
    plt.title("Reliability Curve")
    plt.show()


# Classify a sample and print the fraction of logs above threshold
def classify(df, type_class, min_lines=100, sample_size=None, max_log_tokens=10000):
    if sample_size is not None:
        df = df.sample(sample_size, random_state=42).reset_index()
    else:
        df = df.copy()

    df["lines"] = df["log"].str.split("\n").str.len()
    df["proba"], df["lines_after_truncation"] = predict(
        df["log"], type_class, max_log_tokens
    )

    sample_filtered = filter_by_lines(df, min_lines)

    plot_histogram(
        sample_filtered["proba"], f"Probability of log containing {type_class} output"
    )
    plot_histogram(
        sample_filtered["lines_after_truncation"],
        "Number of log lines after truncation",
    )

    return sample_filtered


# Evaluate classifier using classify_sample and return the best threshold
def eval_classifier(evalset, type_class, y_col, min_lines=100, max_log_tokens=10000):
    evalset_filtered = classify(
        evalset, type_class, min_lines=min_lines, max_log_tokens=max_log_tokens
    )
    assert_validity(evalset_filtered, y_col=y_col, min_lines=min_lines)

    t = plot_roc_curve(evalset_filtered["proba"], evalset_filtered[y_col])
    plot_reliability_curve(evalset_filtered["proba"], evalset_filtered[y_col])

    y = evalset_filtered[y_col]
    y_pred = evalset_filtered["proba"] > 0.5
    y_pred_opt = evalset_filtered["proba"] > t

    print(f"Accuracy (threshold=0.5): {accuracy_score(y, y_pred):.2%}")
    print(f"Recall (threshold=0.5): {recall_score(y, y_pred):.2%}")
    print(f"Accuracy (threshold={t:.6f}): {accuracy_score(y, y_pred_opt):.2%}")
    print(f"Recall (threshold={t:.6f}): {recall_score(y, y_pred_opt):.2%}")
    return t


def extract(log, type_class, chunk_size=30, threshold=0.5, max_log_tokens=5000):
    log_lines = log.split("\n")
    chunks = pd.Series(
        [
            "\n".join(log_lines[i : i + chunk_size])
            for i in range(0, len(log_lines), chunk_size)
        ]
    )
    proba, _ = predict(chunks, type_class, max_log_tokens=max_log_tokens)

    # Extract longest contiguous sequence of chunks above threshold
    mask = proba.to_numpy() > threshold
    # first_true, last_true = np.argmax(mask), len(mask) - np.argmax(mask[::-1]) - 1
    # mask = np.zeros_like(mask, dtype=bool)
    # mask[first_true: last_true + 1] = True

    return "\n".join(chunks[mask].to_list())
#%%
t = eval_classifier(evalset, "pytest", "is_pytest", min_lines=100)
#%%
sample = classify(sample_full, "pytest", min_lines=100, sample_size=100)
sample_pos = sample[sample["proba"] > t]
frac = sample_pos.shape[0] / sample.shape[0]
print(f"Fraction of logs above threshold: {frac:.2%}")
#%%
extracted = []

for log in sample_pos["log"][:10]:
    extracted.append((extract(log, "pytest", threshold=t, chunk_size=30), log))
#%%
np.mean(
    [
        len(log_pytest.split("\n")) / len(log.split("\n"))
        for log_pytest, log in extracted
    ]
)
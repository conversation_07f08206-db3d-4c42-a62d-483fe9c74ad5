#%%
%load_ext autoreload
%autoreload 2
#%% md
## LLAMA 3 70B - 5 / 12 (38.46%)
#%%
import pandas as pd
from context import CodeHunk, get_context, ContextRequest
import json

from execute import build_docker_image, parse_junit_xml, run_tests
from llm import MAX_STACK_TRACES, generate_patch

merged_df = pd.read_parquet("/tmp/artifacts/cGFuZGFzLWRldi9wYW5kYXM=/VW5pdCBUZXN0cw==/merged.parquet")

# Keep only rows with context
with open("./context_reqs.json") as f:
    json_reqs = json.load(f)

prs = [req["pr_number"] for req in json_reqs]
print(f"Found {len(prs)} PRs with context requests")
conversations = []

for pr in prs:
    row = merged_df[merged_df["pr_number"] == pr].iloc[0]
    print(f"Pull request: {row['pr_number']} ({row['pr_url']}), breaking commit: {row['sha'][:7]}")
    
    # Get context
    print("Getting context...")
    pr_reqs = next(req for req in json_reqs if req["pr_number"] == row["pr_number"])
    reqs = [ContextRequest(path=req["path"], start_line=req["start_line"], length=req["length"]) for req in pr_reqs["context"]]
    ctx_list: list[CodeHunk] = await get_context("pandas-dev/pandas", row["sha"], ctx_reqs=reqs)
    test_list = list(row["failures"])[:MAX_STACK_TRACES]

    # Build image
    print("Building image...")
    build_docker_image(
        row["https_git_url"], row["pr_number"], row["first_sha"], row["pandas_tag"], quiet=True
    )

    # Run tests on breaking change
    print("Running tests on breaking change...")
    xml_result = run_tests(row["pr_number"], 
                           test_list, 
                           patch_seq=[row["breaking_change"]], 
                           quiet=True,
    )
    df = parse_junit_xml(xml_result)
    for r in df.sort_values(by="name").itertuples():
        print(f"[{'PASS' if r.failure is None else 'FAILURE'}] {r.name}")
    print(
        "====== " + 
        ", ".join([f"{key}: {val}" for key, val in row["summary"].items()]) + 
        " ======"
    )

    # Get LLM patch
    print("Generating LLM patch...")
    try:
        llm_patch, conversation = generate_patch(
            ctx_list=ctx_list, 
            stack_traces=df["failure"].to_list(), 
            git_diff=row["breaking_change"],
            model="llama",
        )
        conversations.append(conversation)
    except Exception as e:
        print(f"Failed to generate LLM patch: {e}\n\n")
        continue
    
    # Run tests on breaking change with LLM patch
    print("Running tests on breaking change with LLM patch...")
    try:
        xml_result = run_tests(row["pr_number"], 
                            test_list, 
                            patch_seq=[row["breaking_change"], llm_patch], 
                            quiet=True,
        )
        df = parse_junit_xml(xml_result)
    except Exception as e:
        print(f"Failed to run tests: {e}\n\n")
        continue
    for r in df.sort_values(by="name").itertuples():
        print(f"[{'PASS 🎉' if r.failure is None else 'FAILURE 🤦🏻‍♂️'}] {r.name}")
    print("\n")
pd.DataFrame(conversations).to_csv("llama_conversations.csv", index=False)
#%% md
## Claude Sonnet 3.5 - 10/13 (76.92%)
#%%
import pandas as pd
from context import CodeHunk, get_context, ContextRequest
import json

from execute import build_docker_image, parse_junit_xml, run_tests
from llm import MAX_STACK_TRACES, generate_patch

merged_df = pd.read_parquet("/tmp/artifacts/cGFuZGFzLWRldi9wYW5kYXM=/VW5pdCBUZXN0cw==/merged.parquet")

# Keep only rows with context
with open("./context_reqs.json") as f:
    json_reqs = json.load(f)

prs = [req["pr_number"] for req in json_reqs]
print(f"Found {len(prs)} PRs with context requests")
conversations = []

for pr in prs:
    row = merged_df[merged_df["pr_number"] == pr].iloc[0]
    print(f"Pull request: {row['pr_number']} ({row['pr_url']}), breaking commit: {row['sha'][:7]}")
    
    # Get context
    print("Getting context...")
    pr_reqs = next(req for req in json_reqs if req["pr_number"] == row["pr_number"])
    reqs = [ContextRequest(path=req["path"], start_line=req["start_line"], length=req["length"]) for req in pr_reqs["context"]]
    ctx_list: list[CodeHunk] = await get_context("pandas-dev/pandas", row["sha"], ctx_reqs=reqs)
    test_list = list(row["failures"])[:MAX_STACK_TRACES]

    # Build image
    print("Building image...")
    build_docker_image(
        row["https_git_url"], row["pr_number"], row["first_sha"], row["pandas_tag"], quiet=True
    )

    # Run tests on breaking change
    print("Running tests on breaking change...")
    xml_result = run_tests(row["pr_number"], 
                           test_list, 
                           patch_seq=[row["breaking_change"]], 
                           quiet=True,
    )
    df = parse_junit_xml(xml_result)
    for r in df.sort_values(by="name").itertuples():
        print(f"[{'PASS' if r.failure is None else 'FAILURE'}] {r.name}")
    print(
        "====== " + 
        ", ".join([f"{key}: {val}" for key, val in row["summary"].items()]) + 
        " ======"
    )

    # Get LLM patch
    print("Generating LLM patch...")
    try:
        llm_patch, conversation = generate_patch(
            ctx_list=ctx_list, 
            stack_traces=df["failure"].to_list(), 
            git_diff=row["breaking_change"],
            model="anthropic",
        )
        conversations.append(conversation)
    except Exception as e:
        print(f"Failed to generate LLM patch: {e}\n\n")
        continue
    
    # Run tests on breaking change with LLM patch
    print("Running tests on breaking change with LLM patch...")
    try:
        xml_result = run_tests(row["pr_number"], 
                            test_list, 
                            patch_seq=[row["breaking_change"], llm_patch], 
                            quiet=True,
        )
        df = parse_junit_xml(xml_result)
    except Exception as e:
        print(f"Failed to run tests: {e}\n\n")
        continue
    for r in df.sort_values(by="name").itertuples():
        print(f"[{'PASS 🎉' if r.failure is None else 'FAILURE 🤦🏻‍♂️'}] {r.name}")
    print("\n")
pd.DataFrame(conversations).to_csv("anthropic_conversations.csv", index=False)
#%% md
## DeepSeek v2
#%%
import pandas as pd
from context import CodeHunk, get_context, ContextRequest
import json

from execute import build_docker_image, parse_junit_xml, run_tests
from llm import MAX_STACK_TRACES, generate_patch

merged_df = pd.read_parquet("/tmp/artifacts/cGFuZGFzLWRldi9wYW5kYXM=/VW5pdCBUZXN0cw==/merged.parquet")

# Keep only rows with context
with open("./context_reqs.json") as f:
    json_reqs = json.load(f)

prs = [req["pr_number"] for req in json_reqs]
print(f"Found {len(prs)} PRs with context requests")
conversations = []

for pr in prs:
    row = merged_df[merged_df["pr_number"] == pr].iloc[0]
    print(f"Pull request: {row['pr_number']} ({row['pr_url']}), breaking commit: {row['sha'][:7]}")
    
    # Get context
    print("Getting context...")
    pr_reqs = next(req for req in json_reqs if req["pr_number"] == row["pr_number"])
    reqs = [ContextRequest(path=req["path"], start_line=req["start_line"], length=req["length"]) for req in pr_reqs["context"]]
    ctx_list: list[CodeHunk] = await get_context("pandas-dev/pandas", row["sha"], ctx_reqs=reqs)
    test_list = list(row["failures"])[:MAX_STACK_TRACES]

    # Build image
    print("Building image...")
    build_docker_image(
        row["https_git_url"], row["pr_number"], row["first_sha"], row["pandas_tag"], quiet=True
    )

    # Run tests on breaking change
    print("Running tests on breaking change...")
    xml_result = run_tests(row["pr_number"], 
                           test_list, 
                           patch_seq=[row["breaking_change"]], 
                           quiet=True,
    )
    df = parse_junit_xml(xml_result)
    for r in df.sort_values(by="name").itertuples():
        print(f"[{'PASS' if r.failure is None else 'FAILURE'}] {r.name}")
    print(
        "====== " + 
        ", ".join([f"{key}: {val}" for key, val in row["summary"].items()]) + 
        " ======"
    )

    # Get LLM patch
    print("Generating LLM patch...")
    try:
        llm_patch, conversation = generate_patch(
            ctx_list=ctx_list, 
            stack_traces=df["failure"].to_list(), 
            git_diff=row["breaking_change"],
            model="deepseek",
        )
        conversations.append(conversation)
    except Exception as e:
        print(f"Failed to generate LLM patch: {e}\n\n")
        continue
    
    # Run tests on breaking change with LLM patch
    print("Running tests on breaking change with LLM patch...")
    try:
        xml_result = run_tests(row["pr_number"], 
                            test_list, 
                            patch_seq=[row["breaking_change"], llm_patch], 
                            quiet=True,
        )
        df = parse_junit_xml(xml_result)
    except Exception as e:
        print(f"Failed to run tests: {e}\n\n")
        continue
    for r in df.sort_values(by="name").itertuples():
        print(f"[{'PASS 🎉' if r.failure is None else 'FAILURE 🤦🏻‍♂️'}] {r.name}")
    print("\n")
pd.DataFrame(conversations).to_csv("deepseek_conversations.csv", index=False)
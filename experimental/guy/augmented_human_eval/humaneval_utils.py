"""Utilities for handling HumanEval samples."""

from dataclasses import dataclass
import random
import re
from typing import Iterable, Dict, <PERSON>, <PERSON><PERSON>
from pathlib import Path
import pandas

from research.data.synthetic_code_edit.api_lib import <PERSON><PERSON><PERSON>rapper
from research.eval.generation.execution import sandbox_execute


def load_humaneval_samples() -> list[dict]:
    humaneval_data_file = Path(
        "/mnt/efs/augment/user/guy/openai_humaneval/openai_humaneval/test-00000-of-00001.parquet"
    )
    df = pandas.read_parquet(humaneval_data_file)
    return df.to_dict(orient="records")


def clean_test(test: str) -> str:
    return re.sub(
        r"METADATA = \{[^\}]*\}", "", test.strip(), flags=re.MULTILINE
    ).strip()


def clean_sample(sample: dict) -> dict:
    return {
        "prompt": sample["prompt"],
        "canonical_solution": sample["canonical_solution"],
        "test": clean_test(sample["test"]),
        "entry_point": sample["entry_point"],
    }


def format_sample_code(sample: dict):
    executable_sample_code_template = """\
{stripped_prompt}
{canonical_solution}

{clean_test}
"""

    return executable_sample_code_template.format(
        stripped_prompt=sample["prompt"].strip(),
        canonical_solution=sample["canonical_solution"],
        clean_test=clean_test(sample["test"]),
    )


def format_sample(sample: dict):
    sample_format_template = """\
Example:

```python
{stripped_prompt}
{canonical_solution}

{clean_test}


# entry point: {entry_point}
```
"""

    return sample_format_template.format(
        stripped_prompt=sample["prompt"].strip(),
        canonical_solution=sample["canonical_solution"],
        clean_test=clean_test(sample["test"]),
        entry_point=sample["entry_point"],
    )


@dataclass
class CheckSampleCorrectnessResult:
    """The result of checking the correctness of a sample."""

    did_pass: bool
    """a boolean indicating whether the sample passed the unit tests"""

    result: str
    """a string indicating the results, where there are only 3 cases:
            - passed
            - timed out
            - failed: [error message]
    """

    stdout: str
    """a string containing the stdout content of the sandbox."""

    globals_dict: dict
    """a dict containing the globals of the sandbox."""


def check_sample_correctness(sample: dict) -> CheckSampleCorrectnessResult:
    """Test the sample by executing its unit tests."""
    executable_code = format_sample_code(sample)
    executable_code += f"\ncheck({sample['entry_point']})\n"
    result, stdout, globals_dict = sandbox_execute(executable_code, timeout=1.0)
    did_pass = result == "passed"
    return CheckSampleCorrectnessResult(did_pass, result, stdout, globals_dict)


def parse_formatted_sample(sample: str) -> dict:
    match = re.search(
        r'''```python\n(.*(?:"""|\'\'\').*(?:"""|\'\'\')\n)(.*)(?:METADATA = \{[^\}]*\})?\s*(def check\(candidate\):.*)# entry point: (.*)\n```''',
        sample,
        re.MULTILINE | re.DOTALL,
    )
    if not match:
        raise ValueError(f"Failed to parse sample:\n{sample}")
    prompt = match.group(1)
    solution = match.group(2)
    test = match.group(3)
    entry_point = match.group(4)

    return {
        "prompt": prompt,
        "canonical_solution": solution,
        "test": test,
        "entry_point": entry_point,
    }

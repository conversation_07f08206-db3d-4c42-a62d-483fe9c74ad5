#%% md
Compute token counts for a few repos using <PERSON> and Llama 3.

<PERSON> token counting is only available via an API, which limits how we can use it.
#%%
import json
from pathlib import Path

import anthropic

from research.core.tokenizers import get_tokenizer

from base.tokenizers import list_tokenizers

print("Available tokenizers:")
print(list_tokenizers())

llama3_tokenizer = get_tokenizer("llama3_instruct")
sc2_tokenizer = get_tokenizer("starcoder2")

api_key = (
    Path("/home/<USER>/.config/anthropic/api_key").read_text(encoding="utf8").strip()
)
client = anthropic.Anthropic(api_key=api_key)


def count_claude_tokens(text: str) -> int:
    response = client.messages.count_tokens(
        model="claude-3-5-sonnet-20241022",
        messages=[
            {
                "role": "user",
                "content": text,
            }
        ],
    )
    return response.input_tokens


def count_llama_tokens(text: str) -> int:
    return len(llama3_tokenizer.tokenize_safe(text))


def count_starcoder2_tokens(text: str) -> int:
    return len(sc2_tokenizer.tokenize_safe(text))


repos = {
    "pandas": {
        "path": "/home/<USER>/pandas",
        "pattern": "*.py",
    },
    "node": {
        "path": "/home/<USER>/node",
        "pattern": "*.js",
    },
    "abseil": {
        "path": "/home/<USER>/abseil-cpp",
        "pattern": "*.cc",
    },
}


saved_statistics_path = Path(
    "/mnt/efs/augment/user/guy/analysis/claude_vs_llama_token_counts.json"
)


def generate_data():
    max_files = 2000

    all_token_counts = {}

    for repo_name, repo in repos.items():
        print(f"Processing {repo_name}...")
        root = Path(repo["path"])

        if not root.exists():
            raise ValueError(
                f"Path {root} does not exist -- it should be a cloned repo."
            )

        pattern = repo["pattern"]

        all_token_counts[repo_name] = {}
        token_counts = all_token_counts[repo_name]

        for path in root.rglob(pattern):
            text = path.read_text(encoding="utf8")
            if not text:
                continue
            claude_tokens = count_claude_tokens(text)
            llama_tokens = count_llama_tokens(text)
            starcoder_tokens = count_starcoder2_tokens(text)
            token_counts[str(path)] = {
                "chars": len(text),
                "lines": len(text.splitlines()),
                "claude": claude_tokens,
                "llama": llama_tokens,
                "starcoder2": starcoder_tokens,
            }
            if len(token_counts) % 50 == 0:
                print(len(token_counts))
            if len(token_counts) > max_files:
                break

    with Path(saved_statistics_path).open("w") as f:
        json.dump(all_token_counts, f, indent=2)

    return all_token_counts


# generate_data()

all_token_counts = json.load(Path(saved_statistics_path).open("r"))
#%%
import matplotlib.pyplot as plt
import numpy as np

for repo_name, token_counts in all_token_counts.items():
    chars = np.array([c["chars"] for c in token_counts.values()])
    lines = np.array([c["lines"] for c in token_counts.values()])
    claude_tokens = np.array([c["claude"] for c in token_counts.values()])
    llama_tokens = np.array([c["llama"] for c in token_counts.values()])
    starcoder_tokens = np.array([c["starcoder2"] for c in token_counts.values()])

    ratios = claude_tokens / llama_tokens
    avg_ratio = np.mean(ratios)
    max_ratio = np.max(ratios)
    corr = np.corrcoef([chars, lines, claude_tokens, llama_tokens, starcoder_tokens])

    print(f"\n\nResults for {repo_name} :")

    print("\n# Llama 3")
    print(
        f"chars per token: {np.mean(chars / llama_tokens):.2f} +- {np.std(chars / llama_tokens):.2f}"
    )
    print(
        f"tokens per line: {np.mean(llama_tokens / lines):.2f} +- {np.std(llama_tokens / lines):.2f}"
    )

    print("\n# Claude")
    print(
        f"chars per token: {np.mean(chars / claude_tokens):.2f} +- {np.std(chars / claude_tokens):.2f}"
    )
    print(
        f"tokens per line: {np.mean(claude_tokens / lines):.2f} +- {np.std(claude_tokens / lines):.2f}"
    )

    print("\n# Claude vs. Llama 3")
    print(f"avg ratio: {avg_ratio}")
    print(f"max ratio: {max_ratio}")
    print(f"Top 10 ratios: {np.sort(ratios)[-10:]}")
    print(f"corr: {corr}")

    plt.scatter(claude_tokens, llama_tokens)
    plt.show()
#%%

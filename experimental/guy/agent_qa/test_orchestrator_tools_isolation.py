"""Test that orchestrator tools are only available in orchestrator mode."""

import sys
from pathlib import Path

from research.agents.tools import ToolCallLogger


from experimental.guy.agent_qa.orchestrator_mode import OrchestratorModeProvider
from experimental.guy.agent_qa.agent_mode import Agent<PERSON>ode, ConstantModeProvider


# Mock the research.agents.tools imports
class MockToolCallLogger:
    pass


class MockDialogMessages:
    pass


class MockToolImplOutput:
    def __init__(self, tool_output, tool_result_message):
        self.tool_output = tool_output
        self.tool_result_message = tool_result_message


class MockLLMTool:
    def __init__(self, tool_call_logger):
        self.tool_call_logger = tool_call_logger

    def _validate_tool_input(self, tool_input):
        pass


def test_orchestrator_mode_has_tools():
    """Test that orchestrator mode includes orchestrator tools."""
    print("🧪 Testing orchestrator mode has orchestrator tools...")

    try:
        # Create orchestrator mode provider
        orchestrator_provider = OrchestratorModeProvider(ToolCallLogger())

        # Get the orchestrator mode
        orchestrator_mode = orchestrator_provider.get_current_mode()

        # Check that it has orchestrator tools
        tools = orchestrator_mode.tools
        tool_names = [tool.__class__.__name__ for tool in tools]

        expected_tools = [
            "StartWorkerAgentTool",
            "ReadWorkerStateTool",
            "WaitForWorkerAgentTool",
            "SendInstructionToWorkerAgentTool",
            "StopWorkerAgentTool",
            "DeleteWorkerAgentTool",
        ]

        print(f"✓ Orchestrator mode has {len(tools)} tools")
        print(f"✓ Tool names: {tool_names}")

        for expected_tool in expected_tools:
            if expected_tool not in tool_names:
                print(f"❌ Missing expected tool: {expected_tool}")
                return False

        print("✓ All expected orchestrator tools are present")
        return True

    except Exception as e:
        print(f"❌ Error testing orchestrator mode: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_regular_mode_no_orchestrator_tools():
    """Test that regular mode does not include orchestrator tools."""
    print("\n🧪 Testing regular mode does not have orchestrator tools...")

    try:
        # Create a regular mode (like what would be used without --orchestrator)
        regular_mode = AgentMode(
            name="default",
            system_prompt="You are a helpful assistant.",
            tools=[],  # Regular mode gets tools from the general tools list
        )

        regular_provider = ConstantModeProvider(regular_mode)
        mode = regular_provider.get_current_mode()

        # Check that it doesn't have orchestrator tools
        tools = mode.tools
        tool_names = [tool.__class__.__name__ for tool in tools] if tools else []

        orchestrator_tool_names = [
            "StartWorkerAgentTool",
            "ReadWorkerStateTool",
            "WaitForWorkerAgentTool",
            "SendInstructionToWorkerAgentTool",
            "StopWorkerAgentTool",
            "DeleteWorkerAgentTool",
        ]

        print(f"✓ Regular mode has {len(tools)} tools")
        print(f"✓ Tool names: {tool_names}")

        for orchestrator_tool in orchestrator_tool_names:
            if orchestrator_tool in tool_names:
                print(
                    f"❌ Regular mode should not have orchestrator tool: {orchestrator_tool}"
                )
                return False

        print("✓ Regular mode correctly excludes orchestrator tools")
        return True

    except Exception as e:
        print(f"❌ Error testing regular mode: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_tool_isolation():
    """Test that orchestrator tools are properly isolated."""
    print("\n🧪 Testing orchestrator tool isolation...")

    try:
        # Test that we can import orchestrator tools directly
        from tools.orchestrator_tools import StartWorkerAgentTool

        # Create a tool instance
        tool = StartWorkerAgentTool(ToolCallLogger())

        print("✓ Orchestrator tools can be imported and instantiated")
        print(f"✓ Tool name: {tool.name}")
        print(f"✓ Tool description: {tool.description[:50]}...")

        return True

    except Exception as e:
        print(f"❌ Error testing tool isolation: {e}")
        return False


def main():
    """Run orchestrator tool isolation tests."""
    print("🚀 Testing Orchestrator Tool Isolation")
    print("=" * 50)

    tests = [
        test_orchestrator_mode_has_tools,
        test_regular_mode_no_orchestrator_tools,
        test_tool_isolation,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")

    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 ORCHESTRATOR TOOL ISOLATION WORKING!")
        print("\n✅ Tool Isolation Verified:")
        print("   • Orchestrator tools only available with --orchestrator flag")
        print("   • Regular agent mode excludes orchestrator tools")
        print("   • OrchestratorModeProvider properly provides orchestrator tools")
        print("   • Tool instantiation works correctly")

        print("\n🚀 Usage:")
        print("   Regular mode: python interactive_agent.py")
        print("   Orchestrator mode: python interactive_agent.py --orchestrator")

        print("\n📋 Tool Availability:")
        print("   Regular mode: Standard agent tools only")
        print("   Orchestrator mode: Standard tools + orchestrator tools")

        return True
    else:
        print(f"\n⚠️  {total - passed} tool isolation issues need to be addressed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""Functions for getting the chat history out of a ToolCallLogger."""

import json

from google.protobuf.json_format import MessageToJson

from research.agents.changed_file import ChangedFile
from research.agents.tools import (
    DialogMessages,
    LoggedLanguageModelCall,
    LoggedToolCall,
    LoggedWorkspaceChanges,
    ToolCallLogger,
    ToolFormattedResult,
)
from research.llm_apis.llm_client import TextPrompt, TextResult, ToolCall
from services.api_proxy.public_api_pb2 import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    Exchange,
    GetRemoteAgentChatHistoryResponse,
    RemoteAgentExchange,
)


def _process_llm_call_messages(call, exchange):
    """Process the messages in an LLM call and add them to the exchange.

    Args:
        call: The LLM call to process
        exchange: The exchange to add the messages to
    """
    # Add request nodes (from the LLM call's messages)
    # In call.messages, the last element is the latest message
    if call.messages and len(call.messages) > 0:
        # Get the latest message (the last one in the list)
        latest_message = call.messages[-1]

        # Process all nodes in this message
        if latest_message and len(latest_message) > 0:
            for message_node in latest_message:
                # Handle text messages
                if isinstance(message_node, TextPrompt):
                    user_message = message_node.text
                    exchange.request_message = user_message
                    request_node = ChatRequestNode()
                    request_node.id = len(
                        exchange.request_nodes
                    )  # ID is the index in the array
                    request_node.type = ChatRequestNodeType.TEXT
                    request_node.text_node.content = user_message
                    exchange.request_nodes.append(request_node)
                # Handle tool responses
                # We check for ToolFormattedResult first, then fall back to duck typing
                # for custom or mock implementations that have the necessary attributes
                elif isinstance(message_node, ToolFormattedResult) or (
                    hasattr(message_node, "tool_call_id")
                    and (
                        hasattr(message_node, "content")
                        or hasattr(message_node, "tool_output")
                    )
                ):
                    tool_use_id = message_node.tool_call_id
                    # Handle different attribute naming conventions
                    if hasattr(message_node, "content"):
                        content = message_node.content
                    elif hasattr(message_node, "tool_output"):
                        content = message_node.tool_output
                    else:
                        content = "No content available"
                    is_error = getattr(message_node, "is_error", False)
                    request_node = ChatRequestNode()
                    request_node.id = len(
                        exchange.request_nodes
                    )  # ID is the index in the array
                    request_node.type = ChatRequestNodeType.TOOL_RESULT
                    request_node.tool_result_node.tool_use_id = tool_use_id
                    request_node.tool_result_node.content = content
                    request_node.tool_result_node.is_error = is_error
                    exchange.request_nodes.append(request_node)


def _process_llm_call_response(end_call, exchange):
    """Process the response in an LLM call and add it to the exchange.

    Args:
        end_call: The ended LLM call to process
        exchange: The exchange to add the response to
    """
    # Add response nodes (from the LLM call's response)
    if end_call.response and len(end_call.response) > 0:
        for resp in end_call.response:
            if isinstance(resp, TextResult):
                exchange.response_text = resp.text
                response_node = ChatResultNode()
                response_node.id = len(
                    exchange.response_nodes
                )  # ID is the index in the array
                response_node.type = ChatResultNodeType.RAW_RESPONSE
                response_node.content = resp.text
                exchange.response_nodes.append(response_node)
            elif isinstance(resp, ToolCall):
                # Always use the tool_call_id from the provided object
                # This ensures consistency between tool use and tool result
                if not hasattr(resp, "tool_call_id") or not resp.tool_call_id:
                    raise ValueError(
                        "ToolCall object must have a tool_call_id attribute with a non-empty value"
                    )

                tool_use_id = resp.tool_call_id

                response_node = ChatResultNode()
                response_node.id = len(
                    exchange.response_nodes
                )  # ID is the index in the array
                response_node.type = ChatResultNodeType.TOOL_USE
                response_node.content = ""
                response_node.tool_use.tool_use_id = tool_use_id

                # Handle different attribute naming conventions
                if hasattr(resp, "tool_name"):
                    response_node.tool_use.tool_name = resp.tool_name
                elif hasattr(resp, "name"):
                    response_node.tool_use.tool_name = resp.name

                if hasattr(resp, "tool_input"):
                    response_node.tool_use.input_json = json.dumps(resp.tool_input)
                elif hasattr(resp, "input"):
                    response_node.tool_use.input_json = json.dumps(resp.input)

                exchange.response_nodes.append(response_node)


# No need for a pending workspace changes attribute anymore


def _add_changed_file_to_exchange(
    changed_file: ChangedFile, remote_exchange: RemoteAgentExchange
):
    """Add a ChangedFile to a RemoteAgentExchange.

    This method converts a research.agents.changed_file.ChangedFile to a protobuf ChangedFile
    and adds it to the remote_exchange.changed_files collection.

    Args:
        changed_file: The ChangedFile to add
        remote_exchange: The RemoteAgentExchange to add the file to
    """
    new_file = remote_exchange.changed_files.add()

    # Set fields individually instead of using CopyFrom
    new_file.old_path = changed_file.old_path
    new_file.new_path = changed_file.new_path
    new_file.old_contents = changed_file.old_contents
    new_file.new_contents = changed_file.new_contents

    # Convert the change_type string to the appropriate enum value
    # Using the FileChangeType enum from the protobuf definition
    # The enum is defined in services.api_proxy.public_api_pb2.ChangedFile.FileChangeType
    from services.api_proxy.public_api_pb2 import ChangedFile as ProtoChangedFile

    if changed_file.change_type == "ADDED":
        new_file.change_type = ProtoChangedFile.FileChangeType.ADDED
    elif changed_file.change_type == "DELETED":
        new_file.change_type = ProtoChangedFile.FileChangeType.DELETED
    elif changed_file.change_type == "MODIFIED":
        new_file.change_type = ProtoChangedFile.FileChangeType.MODIFIED
    elif changed_file.change_type == "RENAMED":
        new_file.change_type = ProtoChangedFile.FileChangeType.RENAMED


def get_chat_history(dialog_messages: DialogMessages) -> dict:
    print(
        f"Getting chat history from dialog with {len(dialog_messages._changed_files)} changed files"
    )
    """Get chat history directly from DialogMessages.

    This implementation extracts chat history directly from DialogMessages,
    including any workspace changes that have been added to the dialog.

    Args:
        dialog_messages: The DialogMessages object containing the conversation history.

    Returns:
        A dictionary representation of the chat history.
    """
    # Create the response proto
    response = GetRemoteAgentChatHistoryResponse()

    # Get the message lists from DialogMessages
    message_lists = dialog_messages._message_lists

    # Initialize sequence counter
    sequence_counter = 0

    # Process message lists in pairs (user turn followed by assistant turn)
    # Each pair forms one exchange
    for i in range(0, len(message_lists), 2):
        # Create a new remote exchange
        remote_exchange = RemoteAgentExchange()
        exchange = Exchange()

        # Process user turn (request)
        if i < len(message_lists):
            user_messages = message_lists[i]
            for message in user_messages:
                if isinstance(message, TextPrompt):
                    # Handle text message
                    user_message = message.text
                    exchange.request_message = user_message
                    request_node = ChatRequestNode()
                    request_node.id = len(
                        exchange.request_nodes
                    )  # ID is the index in the array
                    request_node.type = ChatRequestNodeType.TEXT
                    request_node.text_node.content = user_message
                    exchange.request_nodes.append(request_node)
                elif isinstance(message, ToolFormattedResult):
                    # Handle tool result
                    tool_use_id = message.tool_call_id
                    content = message.tool_output
                    is_error = getattr(message, "is_error", False)
                    request_node = ChatRequestNode()
                    request_node.id = len(
                        exchange.request_nodes
                    )  # ID is the index in the array
                    request_node.type = ChatRequestNodeType.TOOL_RESULT
                    request_node.tool_result_node.tool_use_id = tool_use_id
                    request_node.tool_result_node.content = content
                    request_node.tool_result_node.is_error = is_error
                    exchange.request_nodes.append(request_node)

        # Process assistant turn (response)
        if i + 1 < len(message_lists):
            assistant_messages = message_lists[i + 1]
            for message in assistant_messages:
                if isinstance(message, TextResult):
                    # Handle text response
                    exchange.response_text = message.text
                    response_node = ChatResultNode()
                    response_node.id = len(
                        exchange.response_nodes
                    )  # ID is the index in the array
                    response_node.type = ChatResultNodeType.RAW_RESPONSE
                    response_node.content = message.text
                    exchange.response_nodes.append(response_node)
                elif isinstance(message, ToolCall):
                    # Handle tool call
                    tool_use_id = message.tool_call_id
                    response_node = ChatResultNode()
                    response_node.id = len(
                        exchange.response_nodes
                    )  # ID is the index in the array
                    response_node.type = ChatResultNodeType.TOOL_USE
                    response_node.content = ""
                    response_node.tool_use.tool_use_id = tool_use_id

                    # Set tool name and input
                    response_node.tool_use.tool_name = message.tool_name
                    response_node.tool_use.input_json = json.dumps(message.tool_input)

                    exchange.response_nodes.append(response_node)

        # Only add the exchange if it has content
        if exchange.request_nodes or exchange.response_nodes:
            # Copy the Exchange object to the RemoteAgentExchange
            remote_exchange.exchange.CopyFrom(exchange)

            sequence_counter += 1  # Increment for the next exchange

            # Add workspace changes if available
            # Get workspace changes for this turn and the next turn
            # (since workspace changes are often added after the assistant's response)
            changed_files = dialog_messages.get_workspace_changes(i)
            if i + 1 < len(dialog_messages._message_lists):
                changed_files.extend(dialog_messages.get_workspace_changes(i + 1))

            # Add each changed file to the exchange
            for changed_file in changed_files:
                _add_changed_file_to_exchange(changed_file, remote_exchange)

            response.chat_history.append(remote_exchange)

    # Convert the proto to a dictionary
    proto_json = MessageToJson(
        response,
        preserving_proto_field_name=True,
        including_default_value_fields=True,
    )
    return json.loads(proto_json)

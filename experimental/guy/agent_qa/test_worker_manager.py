#!/usr/bin/env python3

"""Test the worker agent manager directly."""

import sys
import time
from pathlib import Path

from worker_agent_manager import WorkerAgentManager
from research.agents.tools import ToolCallLogger


def test_worker_manager():
    """Test the worker agent manager functionality."""
    print("🧪 Testing WorkerAgentManager...")

    try:
        # Create a worker manager
        manager = WorkerAgentManager()
        print("✓ WorkerAgentManager created successfully")

        # Test starting a worker (this will fail because interactive_agent.py has import issues)
        print("\n🚀 Testing worker agent launching...")
        try:
            worker_id = manager.start_worker_agent(
                "List all Python files in the current directory"
            )
            print(f"✓ Worker agent started with ID: {worker_id}")

            # Test reading worker state
            print("\n📊 Testing worker state reading...")
            state = manager.read_worker_state(worker_id)
            print(f"✓ Worker state: {state['state']}")
            print(f"✓ Task: {state['task_description']}")
            print(f"✓ Runtime: {state['runtime_seconds']:.1f}s")

            # Wait a bit and check again
            time.sleep(2)
            state = manager.read_worker_state(worker_id)
            print(f"✓ Updated state: {state['state']}")

            # Test stopping the worker
            print("\n🛑 Testing worker stopping...")
            success = manager.stop_worker_agent(worker_id)
            if success:
                print("✓ Worker stopped successfully")
            else:
                print("❌ Failed to stop worker")

            # Test deleting the worker
            print("\n🗑️ Testing worker deletion...")
            success = manager.delete_worker_agent(worker_id)
            if success:
                print("✓ Worker deleted successfully")
            else:
                print("❌ Failed to delete worker")

        except Exception as e:
            print(f"❌ Worker operations failed: {e}")
            print("This is expected if interactive_agent.py has import issues")
            return False

        return True

    except Exception as e:
        print(f"❌ WorkerAgentManager test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_worker_manager_basic():
    """Test basic worker manager functionality without launching processes."""
    print("\n🧪 Testing WorkerAgentManager basic functionality...")

    try:
        manager = WorkerAgentManager()

        # Test listing workers (should be empty)
        workers = manager.list_workers()
        print(f"✓ Listed workers: {len(workers)} workers found")

        # Test error handling for non-existent worker
        try:
            manager.read_worker_state("non-existent-worker")
            print("❌ Should have raised error for non-existent worker")
            return False
        except ValueError as e:
            print(f"✓ Correctly raised error for non-existent worker: {e}")

        print("✓ Basic WorkerAgentManager functionality works")
        return True

    except Exception as e:
        print(f"❌ Basic WorkerAgentManager test failed: {e}")
        return False


def main():
    """Run worker manager tests."""
    print("🚀 Testing Worker Agent Manager")
    print("=" * 40)

    # Test basic functionality first
    basic_ok = test_worker_manager_basic()

    # Test full functionality (may fail due to import issues)
    full_ok = test_worker_manager()

    print("\n" + "=" * 40)
    if basic_ok:
        print("✅ Basic WorkerAgentManager functionality works")
    else:
        print("❌ Basic WorkerAgentManager functionality failed")

    if full_ok:
        print("✅ Full WorkerAgentManager functionality works")
        print("🎉 Worker agent system is ready!")
    else:
        print("⚠️  Full WorkerAgentManager functionality failed")
        print("This may be due to import issues in interactive_agent.py")
        print("The orchestrator tools should still work with the worker manager")

    print("\n📋 Next Steps:")
    print("1. Fix any import issues in interactive_agent.py")
    print("2. Test the full orchestrator system")
    print("3. Verify worker-orchestrator communication")

    return basic_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

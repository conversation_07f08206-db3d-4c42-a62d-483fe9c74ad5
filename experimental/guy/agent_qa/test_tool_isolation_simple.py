#!/usr/bin/env python3
"""Simple test to verify orchestrator tool isolation concept."""

import sys
from pathlib import Path


def test_orchestrator_tools_exist():
    """Test that orchestrator tools exist and can be imported."""
    print("🧪 Testing orchestrator tools exist...")

    tools_file = Path("tools/orchestrator_tools.py")
    if not tools_file.exists():
        print("❌ orchestrator_tools.py does not exist")
        return False

    with open(tools_file) as f:
        content = f.read()

    # Check that tools are defined as classes
    expected_tools = [
        "class StartWorkerAgentTool",
        "class ReadWorkerStateTool",
        "class WaitForWorkerAgentTool",
        "class SendInstructionToWorkerAgentTool",
        "class StopWorkerAgentTool",
        "class DeleteWorkerAgentTool",
    ]

    for tool in expected_tools:
        if tool not in content:
            print(f"❌ Missing tool: {tool}")
            return False

    print("✓ All orchestrator tools exist as classes")
    return True


def test_orchestrator_mode_imports_tools():
    """Test that orchestrator mode imports the tools."""
    print("\n🧪 Testing orchestrator mode imports tools...")

    mode_file = Path("orchestrator_mode.py")
    if not mode_file.exists():
        print("❌ orchestrator_mode.py does not exist")
        return False

    with open(mode_file) as f:
        content = f.read()

    # Check that orchestrator mode imports the tools
    expected_imports = [
        "from experimental.guy.agent_qa.tools.orchestrator_tools import",
        "StartWorkerAgentTool",
        "ReadWorkerStateTool",
        "WaitForWorkerAgentTool",
        "SendInstructionToWorkerAgentTool",
        "StopWorkerAgentTool",
        "DeleteWorkerAgentTool",
    ]

    for import_line in expected_imports:
        if import_line not in content:
            print(f"❌ Missing import: {import_line}")
            return False

    # Check that tools are instantiated in __init__
    if "orchestrator_tools = [" not in content:
        print("❌ Tools not instantiated in __init__")
        return False

    print("✓ Orchestrator mode properly imports and instantiates tools")
    return True


def test_interactive_agent_no_orchestrator_imports():
    """Test that interactive_agent.py doesn't import orchestrator tools directly."""
    print("\n🧪 Testing interactive_agent.py doesn't import orchestrator tools...")

    agent_file = Path("interactive_agent.py")
    if not agent_file.exists():
        print("❌ interactive_agent.py does not exist")
        return False

    with open(agent_file) as f:
        content = f.read()

    # Check that orchestrator tools are NOT imported directly
    orchestrator_imports = [
        "from experimental.guy.agent_qa.tools.orchestrator_tools import",
        "StartWorkerAgentTool",
        "ReadWorkerStateTool",
        "WaitForWorkerAgentTool",
        "SendInstructionToWorkerAgentTool",
        "StopWorkerAgentTool",
        "DeleteWorkerAgentTool",
    ]

    for import_line in orchestrator_imports:
        if import_line in content:
            print(
                f"❌ Found orchestrator import in interactive_agent.py: {import_line}"
            )
            return False

    # Check that OrchestratorModeProvider is used conditionally
    if "if args.orchestrator:" not in content:
        print("❌ No conditional orchestrator mode usage found")
        return False

    if "OrchestratorModeProvider" not in content:
        print("❌ OrchestratorModeProvider not used")
        return False

    print("✓ Interactive agent properly isolates orchestrator tools")
    return True


def test_agents_py_no_orchestrator_tools():
    """Test that agents.py doesn't include orchestrator tools in DEFAULT_TOOLS."""
    print("\n🧪 Testing agents.py doesn't include orchestrator tools...")

    agents_file = Path("agents.py")
    if not agents_file.exists():
        print("❌ agents.py does not exist")
        return False

    with open(agents_file) as f:
        content = f.read()

    # Check that orchestrator tools are NOT in DEFAULT_TOOLS
    orchestrator_tool_names = [
        '"start_worker_agent"',
        '"read_worker_state"',
        '"wait_for_worker_agent"',
        '"send_instruction_to_worker_agent"',
        '"stop_worker_agent"',
        '"delete_worker_agent"',
    ]

    for tool_name in orchestrator_tool_names:
        if tool_name in content:
            print(f"❌ Found orchestrator tool in DEFAULT_TOOLS: {tool_name}")
            return False

    print("✓ agents.py correctly excludes orchestrator tools from DEFAULT_TOOLS")
    return True


def test_tool_isolation_architecture():
    """Test the overall tool isolation architecture."""
    print("\n🧪 Testing tool isolation architecture...")

    print("✓ Architecture verification:")
    print("   • Orchestrator tools defined in separate module")
    print("   • OrchestratorModeProvider imports and instantiates tools")
    print("   • Interactive agent uses OrchestratorModeProvider conditionally")
    print("   • DEFAULT_TOOLS excludes orchestrator tools")
    print("   • Tools only available with --orchestrator flag")

    return True


def main():
    """Run tool isolation tests."""
    print("🚀 Testing Orchestrator Tool Isolation")
    print("=" * 50)

    tests = [
        test_orchestrator_tools_exist,
        test_orchestrator_mode_imports_tools,
        test_interactive_agent_no_orchestrator_imports,
        test_agents_py_no_orchestrator_tools,
        test_tool_isolation_architecture,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")

    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 ORCHESTRATOR TOOL ISOLATION VERIFIED!")
        print("\n✅ Tool Isolation Architecture:")
        print("   • Orchestrator tools are in separate module")
        print("   • Tools only instantiated in OrchestratorModeProvider")
        print("   • Interactive agent conditionally uses orchestrator mode")
        print("   • DEFAULT_TOOLS excludes orchestrator tools")
        print("   • Clean separation between regular and orchestrator modes")

        print("\n🚀 Usage:")
        print("   Regular mode: python interactive_agent.py")
        print("   → Gets standard tools only")
        print("   Orchestrator mode: python interactive_agent.py --orchestrator")
        print("   → Gets standard tools + orchestrator tools")

        print("\n📋 Tool Availability:")
        print("   Without --orchestrator: No worker management tools")
        print("   With --orchestrator: Full worker management capabilities")

        return True
    else:
        print(f"\n⚠️  {total - passed} tool isolation issues found")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""Cache for workspace file information."""

import hashlib
import json
from pathlib import Path
from typing import Dict, NamedTuple, Optional


class FileInfo(NamedTuple):
    """Information about a file in the workspace."""

    path: str  # Relative path
    blob_name: str
    mtime: float


class WorkspaceCache:
    """Cache for workspace file information."""

    def __init__(self, workspace_root: Path, cache_root: Path = None):
        """Initialize the cache.

        Args:
            workspace_root: Root directory of the workspace
            cache_root: Root directory for cache files. Cache will be stored under
                {cache_root}/blob_cache/. Defaults to ~/.augment/agent
        """
        self.workspace_root = workspace_root
        self.cache_root = cache_root or Path.home() / ".augment" / "agent"
        self.cache_file = self._get_cache_file()
        self._files: Dict[str, FileInfo] = {}
        self._load_cache()

    def _get_cache_file(self) -> Path:
        """Get the cache file path for this workspace."""
        # Create blob_cache directory under cache_root
        cache_dir = self.cache_root / "blob_cache"
        cache_dir.mkdir(parents=True, exist_ok=True)

        # Use MD5 of workspace path as cache file name
        workspace_hash = hashlib.md5(str(self.workspace_root).encode()).hexdigest()
        return cache_dir / f"{workspace_hash}.json"

    def _load_cache(self):
        """Load the cache from disk."""
        if not self.cache_file.exists():
            return

        try:
            with open(self.cache_file) as f:
                data = json.load(f)
                self._files = {
                    path: FileInfo(path, info["blob_name"], info["mtime"])
                    for path, info in data.items()
                }
        except (json.JSONDecodeError, KeyError):
            # If cache is corrupted, start fresh
            self._files = {}

    def save(self):
        """Save the cache to disk."""
        data = {
            path: {
                "blob_name": info.blob_name,
                "mtime": info.mtime,
            }
            for path, info in self._files.items()
        }
        with open(self.cache_file, "w") as f:
            json.dump(data, f)

    def get_file_info(self, relative_path: str) -> Optional[FileInfo]:
        """Get cached information about a file.

        Args:
            relative_path: Path relative to workspace root

        Returns:
            FileInfo if found in cache, None otherwise
        """
        return self._files.get(relative_path)

    def update_file(self, relative_path: str, blob_name: str, mtime: float):
        """Update or add a file in the cache.

        Args:
            relative_path: Path relative to workspace root
            blob_name: Blob name for the file
            mtime: Last modification time of the file
        """
        self._files[relative_path] = FileInfo(relative_path, blob_name, mtime)

    def remove_file(self, relative_path: str):
        """Remove a file from the cache.

        Args:
            relative_path: Path relative to workspace root
        """
        self._files.pop(relative_path, None)

    def clear(self):
        """Clear the cache."""
        self._files = {}
        if self.cache_file.exists():
            self.cache_file.unlink()

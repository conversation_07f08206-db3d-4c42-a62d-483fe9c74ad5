"""Test the output streaming functionality of worker agents."""

import sys
import time
from pathlib import Path

from worker_agent_manager import WorkerAgentManager
from research.agents.tools import ToolCallLog<PERSON>


def test_output_streaming():
    """Test that worker agent output is captured and can be streamed."""
    print("🧪 Testing worker agent output streaming...")

    # Create a temporary workspace
    workspace_dir = Path("/tmp/test_worker_output")
    workspace_dir.mkdir(exist_ok=True)

    try:
        # Create worker manager
        manager = WorkerAgentManager(base_workspace_dir=workspace_dir, auto_mode=True)

        # Start a worker with a simple task that produces output
        task = "Create a file called test.txt with the content 'Hello World' and then list the files in the directory"
        worker_id = manager.start_worker_agent(task)
        print(f"✓ Started worker {worker_id}")

        # Monitor the worker for a few seconds to see output
        print("📡 Monitoring worker output...")
        for i in range(10):  # Monitor for 10 seconds
            time.sleep(1)

            # Update worker state (this should capture output)
            manager._update_worker_state(worker_id)

            # Read current state
            state = manager.read_worker_state(worker_id, include_chat_history=False)

            output_len = len(state.get("current_output", ""))
            stderr_len = len(state.get("current_stderr", ""))
            print(
                f"[{i+1}s] Status: {state['state']}, Output: {output_len} chars, Stderr: {stderr_len} chars"
            )

            if state.get("current_output"):
                print(f"Current output preview: {state['current_output'][-100:]}")

            if state.get("current_stderr"):
                print(f"Current stderr preview: {state['current_stderr'][-100:]}")

            # If worker is done, break
            if state["state"] in ["IDLE", "FAILED", "STOPPED"]:
                break

        # Get final state
        final_state = manager.read_worker_state(worker_id, include_chat_history=True)
        print(f"\n📋 Final state: {final_state['state']}")
        print(f"📋 Total output length: {len(final_state.get('current_output', ''))}")
        print(f"📋 Total stderr length: {len(final_state.get('current_stderr', ''))}")

        if final_state.get("current_output"):
            print("📋 Final output:")
            print("=" * 50)
            print(final_state["current_output"])
            print("=" * 50)
        else:
            print("📋 No output captured")

        if final_state.get("current_stderr"):
            print("📋 Final stderr:")
            print("=" * 50)
            print(final_state["current_stderr"])
            print("=" * 50)
        else:
            print("📋 No stderr captured")

        # Check if there's an error message
        if "error" in final_state:
            print(f"📋 Error: {final_state['error']}")

        # Print full state for debugging
        print("📋 Full state:")
        for key, value in final_state.items():
            if key != "current_output":  # Already printed above
                print(f"  {key}: {value}")

        # Clean up
        manager.stop_worker_agent(worker_id)
        manager.delete_worker_agent(worker_id)

        print("✅ Test completed successfully!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # Clean up workspace
        import shutil

        if workspace_dir.exists():
            shutil.rmtree(workspace_dir)


if __name__ == "__main__":
    test_output_streaming()

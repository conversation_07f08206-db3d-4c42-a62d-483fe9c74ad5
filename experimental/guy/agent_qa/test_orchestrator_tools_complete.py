#!/usr/bin/env python3
"""Test that orchestrator mode has all standard tools plus orchestrator tools."""

import sys
from pathlib import Path


def test_orchestrator_has_all_tools():
    """Test that orchestrator mode includes both standard and orchestrator tools."""
    print("🧪 Testing orchestrator mode has all tools...")

    # Check that orchestrator mode imports standard tools
    mode_file = Path("orchestrator_mode.py")
    if not mode_file.exists():
        print("❌ orchestrator_mode.py does not exist")
        return False

    with open(mode_file) as f:
        content = f.read()

    # Check that it accepts standard_tools parameter
    if "standard_tools: list" not in content:
        print("❌ OrchestratorModeProvider doesn't accept standard_tools parameter")
        return False

    # Check that it combines tools
    if "all_tools = standard_tools + orchestrator_tools" not in content:
        print(
            "❌ OrchestratorModeProvider doesn't combine standard and orchestrator tools"
        )
        return False

    print("✓ OrchestratorModeProvider properly combines all tools")
    return True


def test_interactive_agent_passes_tools():
    """Test that interactive_agent.py passes standard tools to orchestrator mode."""
    print("\n🧪 Testing interactive_agent.py passes tools to orchestrator...")

    agent_file = Path("interactive_agent.py")
    if not agent_file.exists():
        print("❌ interactive_agent.py does not exist")
        return False

    with open(agent_file) as f:
        content = f.read()

    # Check that tools are passed to OrchestratorModeProvider
    if (
        "OrchestratorModeProvider(\n                tool_call_logger,\n                default_system_prompt,\n                tools,"
        not in content
    ):
        print("❌ interactive_agent.py doesn't pass tools to OrchestratorModeProvider")
        return False

    print("✓ interactive_agent.py properly passes tools to orchestrator mode")
    return True


def test_tool_combination_logic():
    """Test the tool combination logic conceptually."""
    print("\n🧪 Testing tool combination logic...")

    # Simulate what should happen
    standard_tools = ["tool1", "tool2", "tool3"]  # Mock standard tools
    orchestrator_tools = [
        "start_worker",
        "read_worker",
        "wait_worker",
    ]  # Mock orchestrator tools

    # This is what should happen in OrchestratorModeProvider
    all_tools = standard_tools + orchestrator_tools

    expected_tools = [
        "tool1",
        "tool2",
        "tool3",
        "start_worker",
        "read_worker",
        "wait_worker",
    ]

    if all_tools != expected_tools:
        print(
            f"❌ Tool combination logic failed. Expected {expected_tools}, got {all_tools}"
        )
        return False

    print("✓ Tool combination logic works correctly")
    print(f"✓ Combined tools: {all_tools}")
    return True


def test_orchestrator_tool_availability():
    """Test that orchestrator tools are still properly defined."""
    print("\n🧪 Testing orchestrator tools are still available...")

    tools_file = Path("tools/orchestrator_tools.py")
    if not tools_file.exists():
        print("❌ orchestrator_tools.py does not exist")
        return False

    with open(tools_file) as f:
        content = f.read()

    expected_tools = [
        "StartWorkerAgentTool",
        "ReadWorkerStateTool",
        "WaitForWorkerAgentTool",
        "SendInstructionToWorkerAgentTool",
        "StopWorkerAgentTool",
        "DeleteWorkerAgentTool",
    ]

    for tool in expected_tools:
        if f"class {tool}" not in content:
            print(f"❌ Missing orchestrator tool: {tool}")
            return False

    print("✓ All orchestrator tools are properly defined")
    return True


def main():
    """Run all tool combination tests."""
    print("🚀 Testing Orchestrator Tool Combination")
    print("=" * 50)

    tests = [
        test_orchestrator_has_all_tools,
        test_interactive_agent_passes_tools,
        test_tool_combination_logic,
        test_orchestrator_tool_availability,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")

    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 ORCHESTRATOR TOOL COMBINATION WORKING!")
        print("\n✅ Tool Combination Verified:")
        print("   • OrchestratorModeProvider accepts standard tools")
        print("   • Standard tools + orchestrator tools are combined")
        print("   • interactive_agent.py passes tools correctly")
        print("   • All orchestrator tools are available")

        print("\n🚀 Orchestrator Mode Now Has:")
        print("   • All standard agent tools (file operations, bash, etc.)")
        print("   • All 6 orchestrator tools (worker management)")
        print("   • Complete functionality for complex tasks")

        print("\n📋 Usage:")
        print("   python interactive_agent.py --orchestrator")
        print("   → Gets ALL tools: standard + orchestrator")

        return True
    else:
        print(f"\n⚠️  {total - passed} tool combination issues found")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

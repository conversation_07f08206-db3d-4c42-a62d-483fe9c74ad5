"""Test the orchestrator tools with real worker agent functionality."""

import sys

from research.agents.tools import ToolCallLogger
from tools.orchestrator_tools import (
    StartWorkerAgentTool,
    ReadWorkerStateTool,
    WaitForWorkerAgentTool,
    SendInstructionToWorkerAgentTool,
    StopWorkerAgentTool,
    DeleteWorkerAgentTool,
)


def test_start_worker_tool():
    """Test the start worker agent tool."""
    print("🧪 Testing StartWorkerAgentTool...")

    try:
        tool = StartWorkerAgentTool(ToolCallLogger())

        # Test starting a worker
        result = tool.run_impl({"task_description": "Test task for worker agent"})

        print(f"✓ Tool output: {result.tool_output[:100]}...")
        print(f"✓ Result message: {result.tool_result_message}")

        # Extract worker ID from output
        lines = result.tool_output.split("\n")
        worker_id = None
        for line in lines:
            if "Started worker agent with ID:" in line:
                worker_id = line.split("ID: ")[1]
                break

        if worker_id:
            print(f"✓ Worker ID extracted: {worker_id}")
            return worker_id
        else:
            print("❌ Could not extract worker ID")
            return None

    except Exception as e:
        print(f"❌ StartWorkerAgentTool failed: {e}")
        import traceback

        traceback.print_exc()
        return None


def test_read_worker_state_tool(worker_id):
    """Test the read worker state tool."""
    print(f"\n🧪 Testing ReadWorkerStateTool with worker {worker_id}...")

    try:
        tool = ReadWorkerStateTool(ToolCallLogger())

        # Test reading worker state
        result = tool.run_impl(
            {"worker_agent_id": worker_id, "include_chat_history": True}
        )

        print(f"✓ Tool output: {result.tool_output[:200]}...")
        print(f"✓ Result message: {result.tool_result_message}")
        return True

    except Exception as e:
        print(f"❌ ReadWorkerStateTool failed: {e}")
        return False


def test_send_instruction_tool(worker_id):
    """Test the send instruction tool."""
    print(f"\n🧪 Testing SendInstructionToWorkerAgentTool with worker {worker_id}...")

    try:
        tool = SendInstructionToWorkerAgentTool(ToolCallLogger())

        # Test sending instruction
        result = tool.run_impl(
            {
                "worker_agent_id": worker_id,
                "message": "Please focus on Python files only",
            }
        )

        print(f"✓ Tool output: {result.tool_output[:200]}...")
        print(f"✓ Result message: {result.tool_result_message}")
        return True

    except Exception as e:
        print(f"❌ SendInstructionToWorkerAgentTool failed: {e}")
        return False


def test_stop_worker_tool(worker_id):
    """Test the stop worker tool."""
    print(f"\n🧪 Testing StopWorkerAgentTool with worker {worker_id}...")

    try:
        tool = StopWorkerAgentTool(ToolCallLogger())

        # Test stopping worker
        result = tool.run_impl({"worker_agent_id": worker_id})

        print(f"✓ Tool output: {result.tool_output[:200]}...")
        print(f"✓ Result message: {result.tool_result_message}")
        return True

    except Exception as e:
        print(f"❌ StopWorkerAgentTool failed: {e}")
        return False


def test_delete_worker_tool(worker_id):
    """Test the delete worker tool."""
    print(f"\n🧪 Testing DeleteWorkerAgentTool with worker {worker_id}...")

    try:
        tool = DeleteWorkerAgentTool(ToolCallLogger())

        # Test deleting worker
        result = tool.run_impl({"worker_agent_id": worker_id})

        print(f"✓ Tool output: {result.tool_output[:200]}...")
        print(f"✓ Result message: {result.tool_result_message}")
        return True

    except Exception as e:
        print(f"❌ DeleteWorkerAgentTool failed: {e}")
        return False


def main():
    """Run orchestrator tools tests."""
    print("🚀 Testing Orchestrator Tools with Real Worker Manager")
    print("=" * 60)

    # Test starting a worker
    worker_id = test_start_worker_tool()
    if not worker_id:
        print("❌ Cannot continue tests without a worker ID")
        return False

    # Test reading worker state
    read_ok = test_read_worker_state_tool(worker_id)

    # Test sending instruction
    send_ok = test_send_instruction_tool(worker_id)

    # Test stopping worker
    stop_ok = test_stop_worker_tool(worker_id)

    # Test deleting worker
    delete_ok = test_delete_worker_tool(worker_id)

    print("\n" + "=" * 60)

    all_passed = all([read_ok, send_ok, stop_ok, delete_ok])

    if all_passed:
        print("🎉 ALL ORCHESTRATOR TOOLS WORKING!")
        print("\n✅ Orchestrator System Status:")
        print("   • Worker agent launching: ✅ Working")
        print("   • Worker state monitoring: ✅ Working")
        print("   • Worker instruction sending: ✅ Working")
        print("   • Worker process management: ✅ Working")
        print("   • Worker cleanup: ✅ Working")

        print("\n🚀 Ready for Production Use:")
        print("   python interactive_agent.py --orchestrator")

        print("\n📋 Example Orchestrator Commands:")
        print("   • start_worker_agent: Launch workers for different tasks")
        print("   • read_worker_state: Monitor worker progress")
        print("   • wait_for_worker_agent: Wait for task completion")
        print("   • send_instruction_to_worker_agent: Provide guidance")
        print("   • stop_worker_agent: Terminate workers")
        print("   • delete_worker_agent: Clean up resources")

        return True
    else:
        print("⚠️  Some orchestrator tools had issues")
        print("The system may still work but needs investigation")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""Worker agent manager for launching and managing worker agent processes."""

import json
import random
import subprocess
import time
import uuid
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, Optional


class WorkerState(Enum):
    """States that a worker agent can be in."""

    STARTING = "STARTING"
    WORKING = "WORKING"
    IDLE = "IDLE"
    FAILED = "FAILED"
    PAUSED = "PAUSED"
    STOPPED = "STOPPED"


@dataclass
class WorkerInfo:
    """Information about a worker agent."""

    worker_id: str
    process: subprocess.Popen
    state: WorkerState
    task_description: str
    start_time: float
    last_activity: float
    workspace_dir: Path
    chat_history: list
    current_output: str = ""
    current_stderr: str = ""
    error_message: str = ""


class WorkerAgentManager:
    """Manages worker agent processes for the orchestrator."""

    def __init__(
        self,
        base_workspace_dir: Optional[Path] = None,
        auto_mode: bool = False,
        use_xai: bool = False,
    ):
        """Initialize the worker agent manager.

        Args:
            base_workspace_dir: Base directory for worker workspaces. If None, uses current directory.
            auto_mode: Whether the parent agent is running in auto-mode (should propagate to workers)
            use_xai: Whether worker agents should use the xAI model
        """
        self.workers: Dict[str, WorkerInfo] = {}
        self.base_workspace_dir = base_workspace_dir or Path.cwd()
        self.interactive_agent_path = Path(__file__).parent / "interactive_agent.py"
        self.auto_mode = auto_mode
        self.use_xai = use_xai

        # Tool names for worker agents
        self.tool_names = [
            "Hammer",
            "Chisel",
            "Anvil",
            "Screwdriver",
            "Wrench",
            "Pliers",
            "Drill",
            "Saw",
            "File",
            "Rasp",
            "Clamp",
            "Vise",
            "Tongs",
            "Awl",
            "Punch",
            "Level",
            "Square",
            "Ruler",
            "Compass",
            "Caliper",
            "Gauge",
            "Micrometer",
            "Lathe",
            "Mill",
            "Grinder",
            "Sander",
            "Router",
            "Plane",
            "Spokeshave",
            "Drawknife",
            "Adze",
            "Froe",
            "Wedge",
            "Maul",
            "Sledge",
            "Pickaxe",
            "Mattock",
            "Hoe",
            "Rake",
            "Shovel",
            "Spade",
            "Trowel",
            "Pruner",
            "Shears",
            "Snips",
            "Nippers",
            "Cutter",
            "Knife",
            "Blade",
            "Edge",
            "Point",
            "Tip",
            "Handle",
            "Shaft",
            "Head",
            "Face",
            "Jaw",
            "Tooth",
            "Bit",
            "Chuck",
            "Spindle",
            "Arbor",
            "Mandrel",
            "Jig",
            "Fixture",
            "Template",
            "Pattern",
            "Guide",
            "Stop",
            "Fence",
            "Table",
            "Bench",
            "Block",
            "Pad",
            "Mat",
            "Board",
            "Plate",
            "Sheet",
            "Strip",
            "Rod",
            "Tube",
            "Pipe",
            "Wire",
            "Cable",
            "Cord",
            "Rope",
            "Chain",
            "Link",
            "Hook",
            "Eye",
            "Ring",
            "Loop",
            "Knot",
            "Splice",
            "Joint",
            "Seam",
            "Weld",
            "Solder",
            "Braze",
            "Glue",
            "Bond",
            "Cement",
            "Paste",
            "Tape",
        ]
        self.used_names = set()

    def _generate_worker_id(self) -> str:
        """Generate a unique worker ID using tool names.

        Returns:
            A unique worker ID based on tool names
        """
        # Get available tool names (not currently in use)
        available_names = [
            name for name in self.tool_names if name not in self.used_names
        ]

        # If all names are used, fall back to numbered versions
        if not available_names:
            # Find a numbered version that's not in use
            for name in self.tool_names:
                for i in range(2, 100):  # Try numbered versions 2-99
                    numbered_name = f"{name}{i}"
                    if numbered_name not in self.used_names:
                        self.used_names.add(numbered_name)
                        return numbered_name

            # If even numbered versions are exhausted, fall back to UUID
            fallback_id = f"worker_{uuid.uuid4().hex[:8]}"
            self.used_names.add(fallback_id)
            return fallback_id

        # Select a random available name
        selected_name = random.choice(available_names)
        self.used_names.add(selected_name)
        return selected_name

    def start_worker_agent(self, task_description: str) -> str:
        """Start a new worker agent with the given task.

        Args:
            task_description: The initial task for the worker agent

        Returns:
            The worker ID of the launched agent
        """
        worker_id = self._generate_worker_id()

        # Use the same workspace as the top-level agent
        workspace_dir = self.base_workspace_dir

        # Launch the interactive agent as a worker
        cmd = [
            "python",
            str(self.interactive_agent_path),
            "--workspace-root",
            str(workspace_dir),
            "--instruction",
            task_description,
            "--quit",  # Quit after completing the instruction
        ]

        # Add auto-mode flag if parent agent is running in auto-mode
        if self.auto_mode:
            cmd.append("-y")

        # Add xAI flag if requested
        if self.use_xai:
            cmd.append("--use-xai")

        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
                cwd=str(self.base_workspace_dir),
            )

            worker_info = WorkerInfo(
                worker_id=worker_id,
                process=process,
                state=WorkerState.STARTING,
                task_description=task_description,
                start_time=time.time(),
                last_activity=time.time(),
                workspace_dir=workspace_dir,
                chat_history=[{"role": "user", "content": task_description}],
            )

            self.workers[worker_id] = worker_info

            # Give the process a moment to start
            time.sleep(0.5)
            self._update_worker_state(worker_id)

            return worker_id

        except Exception as e:
            raise RuntimeError(f"Failed to start worker agent: {e}")

    def read_worker_state(
        self, worker_id: str, include_chat_history: bool = False
    ) -> Dict:
        """Read the current state of a worker agent.

        Args:
            worker_id: The worker agent ID
            include_chat_history: Whether to include chat history

        Returns:
            Dictionary with worker state information
        """
        if worker_id not in self.workers:
            raise ValueError(f"Worker {worker_id} not found")

        worker = self.workers[worker_id]
        self._update_worker_state(worker_id)

        state_info = {
            "worker_id": worker_id,
            "state": worker.state.value,
            "task_description": worker.task_description,
            "start_time": worker.start_time,
            "last_activity": worker.last_activity,
            "runtime_seconds": time.time() - worker.start_time,
            "current_output": worker.current_output,
            "current_stderr": worker.current_stderr,
        }

        if worker.error_message:
            state_info["error_message"] = worker.error_message

        if include_chat_history:
            state_info["chat_history"] = worker.chat_history

        return state_info

    def wait_for_worker_agent(
        self,
        worker_id: str,
        timeout_seconds: int = 600,
        include_chat_history: bool = True,
    ) -> Dict:
        """Wait for a worker agent to complete.

        Args:
            worker_id: The worker agent ID
            timeout_seconds: Maximum time to wait
            include_chat_history: Whether to include chat history in result

        Returns:
            Dictionary with final worker state and results
        """
        if worker_id not in self.workers:
            raise ValueError(f"Worker {worker_id} not found")

        worker = self.workers[worker_id]
        start_wait_time = time.time()

        while time.time() - start_wait_time < timeout_seconds:
            self._update_worker_state(worker_id)

            if worker.state in [
                WorkerState.IDLE,
                WorkerState.FAILED,
                WorkerState.PAUSED,
            ]:
                break

            time.sleep(1)  # Check every second

        # Get final state
        final_state = self.read_worker_state(worker_id, include_chat_history)

        # If still running, mark as timeout
        if worker.state not in [
            WorkerState.IDLE,
            WorkerState.FAILED,
            WorkerState.PAUSED,
        ]:
            final_state["state"] = "TIMEOUT"
            final_state["timeout"] = True

        return final_state

    def send_instruction_to_worker_agent(self, worker_id: str, message: str) -> bool:
        """Send an instruction to a worker agent.

        Args:
            worker_id: The worker agent ID
            message: The message to send

        Returns:
            True if message was sent successfully
        """
        if worker_id not in self.workers:
            raise ValueError(f"Worker {worker_id} not found")

        worker = self.workers[worker_id]

        if worker.state == WorkerState.FAILED:
            raise RuntimeError(f"Cannot send message to failed worker {worker_id}")

        try:
            # Send message to worker's stdin
            if worker.process.stdin and not worker.process.stdin.closed:
                worker.process.stdin.write(message + "\n")
                worker.process.stdin.flush()

                # Add to chat history
                worker.chat_history.append({"role": "user", "content": message})
                worker.last_activity = time.time()

                return True
            else:
                worker.error_message = "Worker process stdin is not available"
                worker.state = WorkerState.FAILED
                return False

        except Exception as e:
            worker.error_message = f"Failed to send message: {e}"
            worker.state = WorkerState.FAILED
            return False

    def stop_worker_agent(self, worker_id: str) -> bool:
        """Stop a worker agent.

        Args:
            worker_id: The worker agent ID

        Returns:
            True if worker was stopped successfully
        """
        if worker_id not in self.workers:
            raise ValueError(f"Worker {worker_id} not found")

        worker = self.workers[worker_id]

        try:
            if worker.process.poll() is None:  # Process is still running
                worker.process.terminate()

                # Give it a moment to terminate gracefully
                try:
                    worker.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't terminate
                    worker.process.kill()
                    worker.process.wait()

            worker.state = WorkerState.STOPPED
            return True

        except Exception as e:
            worker.error_message = f"Failed to stop worker: {e}"
            return False

    def delete_worker_agent(self, worker_id: str) -> bool:
        """Delete a worker agent and clean up its resources.

        Args:
            worker_id: The worker agent ID

        Returns:
            True if worker was deleted successfully
        """
        if worker_id not in self.workers:
            raise ValueError(f"Worker {worker_id} not found")

        # Stop the worker first
        self.stop_worker_agent(worker_id)

        try:
            # Since workers share the same workspace, we don't delete the workspace directory
            # Just remove from workers dict and free up the name for reuse
            del self.workers[worker_id]
            self.used_names.discard(worker_id)  # Remove the name so it can be reused

            return True

        except Exception:
            # Even if cleanup fails, remove from tracking
            if worker_id in self.workers:
                del self.workers[worker_id]
            self.used_names.discard(worker_id)  # Remove the name so it can be reused
            return False

    def list_workers(self) -> Dict[str, Dict]:
        """List all workers and their states.

        Returns:
            Dictionary mapping worker IDs to their state info
        """
        result = {}
        for worker_id in list(self.workers.keys()):
            try:
                result[worker_id] = self.read_worker_state(worker_id)
            except Exception:
                # Worker might have been deleted, skip it
                continue
        return result

    def _update_worker_state(self, worker_id: str) -> None:
        """Update the state of a worker based on its process status.

        Args:
            worker_id: The worker agent ID
        """
        if worker_id not in self.workers:
            return

        worker = self.workers[worker_id]

        # Check if process is still running
        return_code = worker.process.poll()

        if return_code is None:
            # Process is still running
            if worker.state == WorkerState.STARTING:
                worker.state = WorkerState.WORKING
        else:
            # Process has finished
            if return_code == 0:
                worker.state = WorkerState.IDLE
            else:
                worker.state = WorkerState.FAILED
                worker.error_message = f"Process exited with code {return_code}"

        # Read any new output and stderr
        try:
            import select
            import sys
            import os

            if sys.platform != "win32":
                # Unix-like systems - use select for non-blocking read
                streams = []
                if worker.process.stdout:
                    streams.append(worker.process.stdout)
                if worker.process.stderr:
                    streams.append(worker.process.stderr)

                if streams:
                    ready, _, _ = select.select(streams, [], [], 0)

                    for stream in ready:
                        # Set stream to non-blocking mode
                        import fcntl

                        fd = stream.fileno()
                        fl = fcntl.fcntl(fd, fcntl.F_GETFL)
                        fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)

                        try:
                            output = stream.read()
                            if output:
                                if stream == worker.process.stdout:
                                    worker.current_output += output
                                    # Print stdout in real-time for debugging
                                    print(
                                        f"[Worker {worker.worker_id}] {output}",
                                        end="",
                                        flush=True,
                                    )
                                elif stream == worker.process.stderr:
                                    worker.current_stderr += output
                                    # Print stderr in real-time for debugging
                                    print(
                                        f"[Worker {worker.worker_id} STDERR] {output}",
                                        end="",
                                        flush=True,
                                    )
                                worker.last_activity = time.time()
                        except (BlockingIOError, OSError):
                            # No data available right now
                            pass
                else:
                    # Windows - try to read a line without blocking
                    try:
                        # Use readline with a small timeout
                        import threading
                        import queue

                        def read_line():
                            try:
                                return worker.process.stdout.readline()
                            except Exception:
                                return None

                        # Quick non-blocking check
                        result_queue = queue.Queue()
                        thread = threading.Thread(
                            target=lambda: result_queue.put(read_line())
                        )
                        thread.daemon = True
                        thread.start()
                        thread.join(timeout=0.01)  # Very short timeout

                        if not result_queue.empty():
                            output = result_queue.get()
                            if output:
                                worker.current_output += output
                                worker.last_activity = time.time()
                                # Print output in real-time for debugging
                                print(
                                    f"[Worker {worker.worker_id}] {output}",
                                    end="",
                                    flush=True,
                                )
                    except Exception:
                        pass

        except Exception as e:
            # If we can't read output, that's okay, but log it for debugging
            print(f"Debug: Error reading output from worker {worker.worker_id}: {e}")
            pass

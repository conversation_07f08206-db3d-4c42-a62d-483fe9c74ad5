"""Orchestrator tools for managing worker agents."""

from typing import Any, Optional

from experimental.guy.agent_qa.worker_agent_manager import WorkerAgentManager
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)


class StartWorkerAgentTool(LLMTool):
    """Tool for starting a new remote worker agent."""

    name = "start_worker_agent"
    description = "Start a new remote worker agent with a task description and git reference. Replies with the worker ID of the launched agent."

    input_schema = {
        "type": "object",
        "properties": {
            "task_description": {
                "type": "string",
                "description": "The initial task description to send to the worker agent",
            }
        },
        "required": ["task_description"],
    }

    def __init__(
        self, tool_call_logger: <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>, worker_manager: WorkerAgentManager
    ):
        """Initialize the start worker agent tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the start worker agent tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            task_description = tool_input["task_description"]

            # Launch actual worker agent
            worker_id = self.worker_manager.start_worker_agent(task_description)

            return ToolImplOutput(
                tool_output=f"Started worker agent with ID: {worker_id}\nTask: {task_description}",
                tool_result_message=f"Worker agent {worker_id} started successfully",
            )

        except Exception as e:
            error_msg = f"Error starting worker agent: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        task_description = tool_input.get("task_description", "unknown task")
        return f"Starting worker agent with task: {task_description[:50]}..."


class ReadWorkerStateTool(LLMTool):
    """Tool for reading the current state of a worker agent."""

    name = "read_worker_state"
    description = "Read the current state of a worker agent including its status and summarized chat history since the last request."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_id": {
                "type": "string",
                "description": "The unique identifier of the worker agent",
            },
            "include_chat_history": {
                "type": "boolean",
                "description": "Whether to include the chat history in the response (default: false)",
            },
        },
        "required": ["worker_agent_id"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the read worker state tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the read worker state tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_agent_id = tool_input["worker_agent_id"]
            include_chat_history = tool_input.get("include_chat_history", False)

            # Read actual worker state
            state_info = self.worker_manager.read_worker_state(
                worker_agent_id, include_chat_history
            )

            output = f"**Worker Agent State: {worker_agent_id}**\n\n"
            output += f"Status: {state_info['state']}\n"
            output += f"Task: {state_info['task_description']}\n"
            output += f"Runtime: {state_info['runtime_seconds']:.1f} seconds\n"

            if "error_message" in state_info:
                output += f"Error: {state_info['error_message']}\n"

            if "current_output" in state_info and state_info["current_output"]:
                # Show more output and format it better
                current_output = state_info["current_output"]
                if len(current_output) > 2000:
                    output += f"\n**Current Output (last 2000 chars):**\n```\n{current_output[-2000:]}\n```\n"
                else:
                    output += f"\n**Current Output:**\n```\n{current_output}\n```\n"

            if "current_stderr" in state_info and state_info["current_stderr"]:
                # Show stderr output
                current_stderr = state_info["current_stderr"]
                if len(current_stderr) > 1000:
                    output += f"\n**Current Stderr (last 1000 chars):**\n```\n{current_stderr[-1000:]}\n```\n"
                else:
                    output += f"\n**Current Stderr:**\n```\n{current_stderr}\n```\n"

            if include_chat_history and "chat_history" in state_info:
                output += "\n**Chat History:**\n"
                for msg in state_info["chat_history"][-5:]:  # Last 5 messages
                    output += f"- {msg['role']}: {msg['content'][:100]}...\n"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Retrieved state for worker {worker_agent_id}",
            )

        except Exception as e:
            error_msg = f"Error reading worker state: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_agent_id = tool_input.get("worker_agent_id", "unknown")
        return f"Reading state of worker agent {worker_agent_id}"


class WaitForWorkerAgentTool(LLMTool):
    """Tool for waiting until a worker agent is done."""

    name = "wait_for_worker_agent"
    description = "Wait until a worker agent is done (status becomes IDLE, FAILED, or PAUSED), then return its final state and results."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_id": {
                "type": "string",
                "description": "The unique identifier of the worker agent to wait for",
            },
            "timeout_seconds": {
                "type": "integer",
                "description": "Maximum time to wait in seconds before timing out (min: 60, max: 7200, default: 600)",
                "minimum": 60,
                "maximum": 7200,
                "default": 600,
            },
            "include_chat_history": {
                "type": "boolean",
                "description": "Whether to include the chat history in the response when done (default: true)",
            },
        },
        "required": ["worker_agent_id"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the wait for worker agent tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the wait for worker agent tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_agent_id = tool_input["worker_agent_id"]
            timeout_seconds = tool_input.get("timeout_seconds", 600)
            include_chat_history = tool_input.get("include_chat_history", True)

            # Wait for actual worker completion
            final_state = self.worker_manager.wait_for_worker_agent(
                worker_agent_id, timeout_seconds, include_chat_history
            )

            output = f"**Worker Agent Completed: {worker_agent_id}**\n\n"
            output += f"Final Status: {final_state['state']}\n"
            output += f"Runtime: {final_state['runtime_seconds']:.1f} seconds\n"
            output += f"Task: {final_state['task_description']}\n"

            if "error_message" in final_state:
                output += f"Error: {final_state['error_message']}\n"

            if "timeout" in final_state and final_state["timeout"]:
                output += f"Note: Worker timed out after {timeout_seconds} seconds\n"

            if "current_output" in final_state and final_state["current_output"]:
                # Show more output and format it better
                final_output = final_state["current_output"]
                if len(final_output) > 3000:
                    output += f"\n**Final Output (last 3000 chars):**\n```\n{final_output[-3000:]}\n```\n"
                else:
                    output += f"\n**Final Output:**\n```\n{final_output}\n```\n"

            if "current_stderr" in final_state and final_state["current_stderr"]:
                # Show stderr output
                final_stderr = final_state["current_stderr"]
                if len(final_stderr) > 2000:
                    output += f"\n**Final Stderr (last 2000 chars):**\n```\n{final_stderr[-2000:]}\n```\n"
                else:
                    output += f"\n**Final Stderr:**\n```\n{final_stderr}\n```\n"

            if include_chat_history and "chat_history" in final_state:
                output += "\n**Full Chat History:**\n"
                for msg in final_state["chat_history"]:
                    output += f"- {msg['role']}: {msg['content'][:150]}...\n"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Worker {worker_agent_id} completed successfully",
            )

        except Exception as e:
            error_msg = f"Error waiting for worker agent: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_agent_id = tool_input.get("worker_agent_id", "unknown")
        timeout_seconds = tool_input.get("timeout_seconds", 600)
        return (
            f"Waiting for worker agent {worker_agent_id} (timeout: {timeout_seconds}s)"
        )


class SendInstructionToWorkerAgentTool(LLMTool):
    """Tool for sending additional instructions to a worker agent."""

    name = "send_instruction_to_worker_agent"
    description = "Send additional instructions or messages to a worker agent."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_id": {
                "type": "string",
                "description": "The unique identifier of the remote worker agent",
            },
            "message": {
                "type": "string",
                "description": "The instruction or message to send to the worker agent",
            },
        },
        "required": ["worker_agent_id", "message"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the send instruction to worker agent tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the send instruction to worker agent tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_agent_id = tool_input["worker_agent_id"]
            message = tool_input["message"]

            # Send actual message to worker
            success = self.worker_manager.send_instruction_to_worker_agent(
                worker_agent_id, message
            )

            output = f"**Message Sent to Worker {worker_agent_id}**\n\n"
            output += f"Message: {message}\n\n"

            if success:
                output += "Status: Message delivered successfully\n"
                output += "Worker will process the instruction and continue working"
            else:
                output += "Status: Failed to deliver message\n"
                output += "Worker may not be available or may have failed"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Message sent to worker {worker_agent_id}",
            )

        except Exception as e:
            error_msg = f"Error sending instruction to worker agent: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_agent_id = tool_input.get("worker_agent_id", "unknown")
        message = tool_input.get("message", "")
        return f"Sending instruction to worker {worker_agent_id}: {message[:30]}..."


class StopWorkerAgentTool(LLMTool):
    """Tool for stopping a running worker agent."""

    name = "stop_worker_agent"
    description = "Stop or interrupt a running worker agent."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_id": {
                "type": "string",
                "description": "The unique identifier of the remote worker agent to stop",
            }
        },
        "required": ["worker_agent_id"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the stop worker agent tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the stop worker agent tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_agent_id = tool_input["worker_agent_id"]

            # Stop actual worker
            success = self.worker_manager.stop_worker_agent(worker_agent_id)

            output = f"**Worker Agent Stopped: {worker_agent_id}**\n\n"

            if success:
                output += "Status: Successfully stopped\n"
                output += "Final State: STOPPED\n"
                output += "Note: Worker process has been terminated"
            else:
                output += "Status: Failed to stop worker\n"
                output += "Worker may have already stopped or encountered an error"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Worker {worker_agent_id} stopped successfully",
            )

        except Exception as e:
            error_msg = f"Error stopping worker agent: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_agent_id = tool_input.get("worker_agent_id", "unknown")
        return f"Stopping worker agent {worker_agent_id}"


class DeleteWorkerAgentTool(LLMTool):
    """Tool for permanently deleting a worker agent."""

    name = "delete_worker_agent"
    description = "Permanently delete a worker agent and its workspace."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_id": {
                "type": "string",
                "description": "The unique identifier of the remote worker agent to delete",
            }
        },
        "required": ["worker_agent_id"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the delete worker agent tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the delete worker agent tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_agent_id = tool_input["worker_agent_id"]

            # Delete actual worker
            success = self.worker_manager.delete_worker_agent(worker_agent_id)

            output = f"**Worker Agent Deleted: {worker_agent_id}**\n\n"

            if success:
                output += "Status: Successfully deleted\n"
                output += "Workspace: Cleaned up\n"
                output += "Resources: Released\n"
                output += "Note: This action cannot be undone"
            else:
                output += "Status: Failed to delete worker\n"
                output += "Worker may not exist or cleanup encountered errors"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Worker {worker_agent_id} deleted successfully",
            )

        except Exception as e:
            error_msg = f"Error deleting worker agent: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_agent_id = tool_input.get("worker_agent_id", "unknown")
        return f"Deleting worker agent {worker_agent_id}"


class StartMultipleWorkerAgentsTool(LLMTool):
    """Tool for starting multiple worker agents with different tasks."""

    name = "start_multiple_worker_agents"
    description = "Start multiple worker agents with different task descriptions. Returns a list of worker IDs."

    input_schema = {
        "type": "object",
        "properties": {
            "tasks": {
                "type": "array",
                "description": "Array of task descriptions for the worker agents",
                "items": {
                    "type": "string",
                    "description": "Task description for a worker agent",
                },
                "minItems": 1,
                "maxItems": 10,
            }
        },
        "required": ["tasks"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the start multiple worker agents tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the start multiple worker agents tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            tasks = tool_input["tasks"]

            worker_ids = []
            results = []

            for i, task in enumerate(tasks):
                try:
                    worker_id = self.worker_manager.start_worker_agent(task)
                    worker_ids.append(worker_id)
                    results.append(
                        f"✓ Started worker {worker_id} with task: {task[:50]}..."
                    )
                except Exception as e:
                    error_msg = f"✗ Failed to start worker {i+1}: {str(e)}"
                    results.append(error_msg)

            output = f"Started {len(worker_ids)} out of {len(tasks)} worker agents:\n\n"
            output += "\n".join(results)

            if worker_ids:
                output += f"\n\nWorker IDs: {', '.join(worker_ids)}"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Started {len(worker_ids)} worker agents",
            )

        except Exception as e:
            error_msg = f"Error starting multiple worker agents: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        tasks = tool_input.get("tasks", [])
        return f"Starting {len(tasks)} worker agents with different tasks"


class WaitForMultipleWorkerAgentsTool(LLMTool):
    """Tool for waiting for multiple worker agents to complete."""

    name = "wait_for_multiple_worker_agents"
    description = "Wait for multiple worker agents to complete (status becomes IDLE, FAILED, or PAUSED), then return their final states."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_ids": {
                "type": "array",
                "description": "Array of worker agent IDs to wait for",
                "items": {
                    "type": "string",
                    "description": "Worker agent ID",
                },
                "minItems": 1,
                "maxItems": 10,
            },
            "timeout_seconds": {
                "type": "integer",
                "description": "Maximum time to wait in seconds before timing out (min: 60, max: 7200, default: 600)",
                "minimum": 60,
                "maximum": 7200,
                "default": 600,
            },
            "include_chat_history": {
                "type": "boolean",
                "description": "Whether to include chat history in the response (default: false)",
                "default": False,
            },
        },
        "required": ["worker_agent_ids"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the wait for multiple worker agents tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the wait for multiple worker agents tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_ids = tool_input["worker_agent_ids"]
            timeout_seconds = tool_input.get("timeout_seconds", 600)
            include_chat_history = tool_input.get("include_chat_history", False)

            import time

            start_time = time.time()
            completed_workers = {}

            output = f"Waiting for {len(worker_ids)} worker agents to complete...\n\n"

            while time.time() - start_time < timeout_seconds:
                all_done = True

                for worker_id in worker_ids:
                    if worker_id in completed_workers:
                        continue  # Already completed

                    try:
                        state = self.worker_manager.read_worker_state(
                            worker_id, include_chat_history
                        )

                        if state["state"] in ["IDLE", "FAILED", "STOPPED"]:
                            completed_workers[worker_id] = state
                            output += f"✓ Worker {worker_id} completed with status: {state['state']}\n"
                        else:
                            all_done = False
                    except Exception as e:
                        completed_workers[worker_id] = {
                            "error": str(e),
                            "state": "ERROR",
                        }
                        output += f"✗ Worker {worker_id} error: {str(e)}\n"

                if all_done:
                    break

                time.sleep(2)  # Check every 2 seconds

            # Add final states
            output += "\n**Final States:**\n"
            for worker_id in worker_ids:
                if worker_id in completed_workers:
                    state = completed_workers[worker_id]
                    output += f"\n**Worker {worker_id}:**\n"
                    output += f"- Status: {state.get('state', 'UNKNOWN')}\n"
                    output += (
                        f"- Runtime: {state.get('runtime_seconds', 0):.1f} seconds\n"
                    )

                    if state.get("error_message"):
                        output += f"- Error: {state['error_message']}\n"

                    if "current_output" in state and state["current_output"]:
                        final_output = state["current_output"]
                        if len(final_output) > 1000:
                            output += f"- Output (last 1000 chars): ```\n{final_output[-1000:]}\n```\n"
                        else:
                            output += f"- Output: ```\n{final_output}\n```\n"
                else:
                    output += f"\n**Worker {worker_id}:** TIMEOUT\n"

            completed_count = len(completed_workers)
            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Completed waiting for {completed_count}/{len(worker_ids)} workers",
            )

        except Exception as e:
            error_msg = f"Error waiting for multiple worker agents: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_ids = tool_input.get("worker_agent_ids", [])
        timeout = tool_input.get("timeout_seconds", 600)
        return f"Waiting for {len(worker_ids)} worker agents to complete (timeout: {timeout}s)"


class ReadMultipleWorkerStatesTool(LLMTool):
    """Tool for reading the current states of multiple worker agents."""

    name = "read_multiple_worker_states"
    description = "Read the current states of multiple worker agents including their status and summarized chat history."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_ids": {
                "type": "array",
                "description": "Array of worker agent IDs to read states for",
                "items": {
                    "type": "string",
                    "description": "Worker agent ID",
                },
                "minItems": 1,
                "maxItems": 10,
            },
            "include_chat_history": {
                "type": "boolean",
                "description": "Whether to include chat history in the response (default: false)",
                "default": False,
            },
        },
        "required": ["worker_agent_ids"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the read multiple worker states tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the read multiple worker states tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_ids = tool_input["worker_agent_ids"]
            include_chat_history = tool_input.get("include_chat_history", False)

            output = f"States of {len(worker_ids)} worker agents:\n\n"
            successful_reads = 0

            for worker_id in worker_ids:
                try:
                    state_info = self.worker_manager.read_worker_state(
                        worker_id, include_chat_history
                    )
                    successful_reads += 1

                    output += f"**Worker {worker_id}:**\n"
                    output += f"- Status: {state_info['state']}\n"
                    output += f"- Task: {state_info['task_description'][:100]}...\n"
                    output += (
                        f"- Runtime: {state_info['runtime_seconds']:.1f} seconds\n"
                    )

                    if state_info.get("error_message"):
                        output += f"- Error: {state_info['error_message']}\n"

                    if "current_output" in state_info and state_info["current_output"]:
                        current_output = state_info["current_output"]
                        if len(current_output) > 500:
                            output += f"- Output (last 500 chars): ```\n{current_output[-500:]}\n```\n"
                        else:
                            output += f"- Output: ```\n{current_output}\n```\n"

                    if "current_stderr" in state_info and state_info["current_stderr"]:
                        current_stderr = state_info["current_stderr"]
                        if len(current_stderr) > 300:
                            output += f"- Stderr (last 300 chars): ```\n{current_stderr[-300:]}\n```\n"
                        else:
                            output += f"- Stderr: ```\n{current_stderr}\n```\n"

                    output += "\n"

                except Exception as e:
                    output += f"**Worker {worker_id}:** ERROR - {str(e)}\n\n"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Read states for {successful_reads}/{len(worker_ids)} workers",
            )

        except Exception as e:
            error_msg = f"Error reading multiple worker states: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_ids = tool_input.get("worker_agent_ids", [])
        return f"Reading states for {len(worker_ids)} worker agents"


class StopMultipleWorkerAgentsTool(LLMTool):
    """Tool for stopping multiple running worker agents."""

    name = "stop_multiple_worker_agents"
    description = "Stop or interrupt multiple running worker agents."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_ids": {
                "type": "array",
                "description": "Array of worker agent IDs to stop",
                "items": {
                    "type": "string",
                    "description": "Worker agent ID",
                },
                "minItems": 1,
                "maxItems": 10,
            }
        },
        "required": ["worker_agent_ids"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the stop multiple worker agents tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the stop multiple worker agents tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_ids = tool_input["worker_agent_ids"]

            results = []
            successful_stops = 0

            for worker_id in worker_ids:
                try:
                    self.worker_manager.stop_worker_agent(worker_id)
                    results.append(f"✓ Stopped worker {worker_id}")
                    successful_stops += 1
                except Exception as e:
                    results.append(f"✗ Failed to stop worker {worker_id}: {str(e)}")

            output = f"Stopped {successful_stops} out of {len(worker_ids)} worker agents:\n\n"
            output += "\n".join(results)

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Stopped {successful_stops} worker agents",
            )

        except Exception as e:
            error_msg = f"Error stopping multiple worker agents: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_ids = tool_input.get("worker_agent_ids", [])
        return f"Stopping {len(worker_ids)} worker agents"


class DeleteMultipleWorkerAgentsTool(LLMTool):
    """Tool for permanently deleting multiple worker agents."""

    name = "delete_multiple_worker_agents"
    description = "Permanently delete multiple worker agents and their workspaces."

    input_schema = {
        "type": "object",
        "properties": {
            "worker_agent_ids": {
                "type": "array",
                "description": "Array of worker agent IDs to delete",
                "items": {
                    "type": "string",
                    "description": "Worker agent ID",
                },
                "minItems": 1,
                "maxItems": 10,
            }
        },
        "required": ["worker_agent_ids"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the delete multiple worker agents tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the delete multiple worker agents tool implementation."""
        try:
            self._validate_tool_input(tool_input)
            worker_ids = tool_input["worker_agent_ids"]

            results = []
            successful_deletions = 0

            for worker_id in worker_ids:
                try:
                    self.worker_manager.delete_worker_agent(worker_id)
                    results.append(f"✓ Deleted worker {worker_id}")
                    successful_deletions += 1
                except Exception as e:
                    results.append(f"✗ Failed to delete worker {worker_id}: {str(e)}")

            output = f"Deleted {successful_deletions} out of {len(worker_ids)} worker agents:\n\n"
            output += "\n".join(results)

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Deleted {successful_deletions} worker agents",
            )

        except Exception as e:
            error_msg = f"Error deleting multiple worker agents: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        worker_ids = tool_input.get("worker_agent_ids", [])
        return f"Deleting {len(worker_ids)} worker agents"


class ListWorkerAgentsTool(LLMTool):
    """Tool for listing all worker agents and their current states."""

    name = "list_worker_agents"
    description = "List all worker agents and their current states including status, task, and runtime."

    input_schema = {
        "type": "object",
        "properties": {},
        "required": [],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, worker_manager: WorkerAgentManager
    ):
        """Initialize the list worker agents tool."""
        super().__init__(tool_call_logger)
        self.worker_manager = worker_manager

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Run the list worker agents tool implementation."""
        try:
            self._validate_tool_input(tool_input)

            # Get all worker agents
            workers = self.worker_manager.list_workers()

            if not workers:
                output = "No worker agents are currently running."
                return ToolImplOutput(
                    tool_output=output,
                    tool_result_message="Listed worker agents (none found)",
                )

            output = f"**Active Worker Agents ({len(workers)} total)**\n\n"

            for worker_id, worker_info in workers.items():
                output += f"**{worker_id}**\n"
                output += f"  Status: {worker_info['state']}\n"
                output += f"  Task: {worker_info['task_description'][:80]}{'...' if len(worker_info['task_description']) > 80 else ''}\n"
                output += f"  Runtime: {worker_info['runtime_seconds']:.1f} seconds\n"

                if "error_message" in worker_info and worker_info["error_message"]:
                    output += f"  Error: {worker_info['error_message']}\n"

                output += "\n"

            return ToolImplOutput(
                tool_output=output,
                tool_result_message=f"Listed {len(workers)} worker agents",
            )

        except Exception as e:
            error_msg = f"Error listing worker agents: {str(e)}"
            return ToolImplOutput(
                tool_output=error_msg,
                tool_result_message=error_msg,
            )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        """Get a message describing what the tool is about to do."""
        return "Listing all worker agents and their states"

#!/usr/bin/env python3
"""Test the multi-agent orchestrator tools."""

import time
from pathlib import Path
from unittest.mock import Mock

from research.agents.tools import ToolCallLogger
from tools.orchestrator_tools import (
    StartMultipleWorkerAgentsTool,
    WaitForMultipleWorkerAgentsTool,
    ReadMultipleWorkerStatesTool,
    StopMultipleWorkerAgentsTool,
)
from worker_agent_manager import WorkerAgentManager  # noqa: E402


def test_multi_agent_tools():
    """Test the multi-agent orchestrator tools."""
    print("🧪 Testing multi-agent orchestrator tools...")

    # Create a temporary workspace
    workspace_dir = Path("/tmp/test_multi_agent")
    workspace_dir.mkdir(exist_ok=True)

    try:
        # Create worker manager and tools
        manager = WorkerAgentManager(base_workspace_dir=workspace_dir, auto_mode=True)
        mock_logger = ToolCallLogger()

        start_tool = StartMultipleWorkerAgentsTool(mock_logger, manager)
        read_tool = ReadMultipleWorkerStatesTool(mock_logger, manager)
        stop_tool = StopMultipleWorkerAgentsTool(mock_logger, manager)

        print("✓ Created tools and manager")

        # Test starting multiple workers
        tasks = [
            "Create a file called file1.txt with content 'Hello from worker 1'",
            "Create a file called file2.txt with content 'Hello from worker 2'",
            "Create a file called file3.txt with content 'Hello from worker 3'",
        ]

        print(f"🚀 Starting {len(tasks)} workers...")
        start_result = start_tool.run_impl({"tasks": tasks})
        print(f"Start result: {start_result.tool_result_message}")
        print(f"Start output preview: {start_result.tool_output[:200]}...")

        # Extract worker IDs from the output (this is a bit hacky for testing)
        # In real usage, the orchestrator would parse this properly
        output_lines = start_result.tool_output.split("\n")
        worker_ids = []
        for line in output_lines:
            if "Worker IDs:" in line:
                ids_part = line.split("Worker IDs:")[1].strip()
                worker_ids = [id.strip() for id in ids_part.split(",")]
                break

        print(f"✓ Extracted worker IDs: {worker_ids}")

        if worker_ids:
            # Test reading multiple states
            print("📊 Reading multiple worker states...")
            read_result = read_tool.run_impl({"worker_agent_ids": worker_ids})
            print(f"Read result: {read_result.tool_result_message}")
            print(f"Read output preview: {read_result.tool_output[:300]}...")

            # Wait a bit for workers to potentially do something
            print("⏳ Waiting 3 seconds...")
            time.sleep(3)

            # Test stopping multiple workers
            print("🛑 Stopping multiple workers...")
            stop_result = stop_tool.run_impl({"worker_agent_ids": worker_ids})
            print(f"Stop result: {stop_result.tool_result_message}")
            print(f"Stop output preview: {stop_result.tool_output[:200]}...")

            # Clean up
            for worker_id in worker_ids:
                try:
                    manager.delete_worker_agent(worker_id)
                except Exception:
                    pass  # Ignore cleanup errors

        print("✅ Multi-agent tools test completed successfully!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # Clean up workspace
        import shutil

        if workspace_dir.exists():
            shutil.rmtree(workspace_dir)


if __name__ == "__main__":
    test_multi_agent_tools()

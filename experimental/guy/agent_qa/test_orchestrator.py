#!/usr/bin/env python3
"""Test the new orchestrator system."""

import sys
from pathlib import Path


def test_orchestrator_tools_exist():
    """Test that orchestrator tools exist."""
    print("🧪 Testing orchestrator tools...")

    tools_file = Path("tools/orchestrator_tools.py")
    if not tools_file.exists():
        print("❌ orchestrator_tools.py does not exist")
        return False

    with open(tools_file) as f:
        content = f.read()

    required_tools = [
        "StartWorkerAgentTool",
        "ReadWorkerStateTool",
        "WaitForWorkerAgentTool",
        "SendInstructionToWorkerAgentTool",
        "StopWorkerAgentTool",
        "DeleteWorkerAgentTool",
    ]

    for tool in required_tools:
        if tool not in content:
            print(f"❌ Missing tool: {tool}")
            return False

    print("✓ All orchestrator tools exist")
    return True


def test_orchestrator_mode_provider():
    """Test that orchestrator mode provider exists."""
    print("🧪 Testing orchestrator mode provider...")

    mode_file = Path("orchestrator_mode.py")
    if not mode_file.exists():
        print("❌ orchestrator_mode.py does not exist")
        return False

    with open(mode_file) as f:
        content = f.read()

    required_elements = [
        "OrchestratorModeProvider",
        "get_current_mode",
        "update_mode",
        "_get_orchestrator_system_prompt",
    ]

    for element in required_elements:
        if element not in content:
            print(f"❌ Missing element: {element}")
            return False

    print("✓ Orchestrator mode provider exists")
    return True


def test_interactive_agent_integration():
    """Test that interactive agent has orchestrator integration."""
    print("🧪 Testing interactive agent integration...")

    agent_file = Path("interactive_agent.py")
    if not agent_file.exists():
        print("❌ interactive_agent.py does not exist")
        return False

    with open(agent_file) as f:
        content = f.read()

    required_elements = [
        "--orchestrator",
        "OrchestratorModeProvider",
        "StartWorkerAgentTool",
        "args.orchestrator",
    ]

    for element in required_elements:
        if element not in content:
            print(f"❌ Missing element: {element}")
            return False

    print("✓ Interactive agent has orchestrator integration")
    return True


def test_system_prompt_content():
    """Test that the system prompt contains expected content."""
    print("🧪 Testing system prompt content...")

    mode_file = Path("orchestrator_mode.py")
    with open(mode_file) as f:
        content = f.read()

    expected_phrases = [
        "orchestrator agent",
        "worker agents",
        "Task Analysis and Planning",
        "Worker Agent Management",
        "Quality Control and Integration",
        "start_worker_agent",
        "wait_for_worker_agent",
    ]

    for phrase in expected_phrases:
        if phrase not in content:
            print(f"❌ Missing phrase in system prompt: {phrase}")
            return False

    print("✓ System prompt contains expected content")
    return True


def test_tool_schemas():
    """Test that tools have proper schemas."""
    print("🧪 Testing tool schemas...")

    try:
        # Check schemas by reading the file directly
        tools_file = Path("tools/orchestrator_tools.py")
        with open(tools_file) as f:
            content = f.read()

        # Check for required schema elements
        schema_checks = [
            ("task_description", "StartWorkerAgentTool"),
            ("worker_agent_id", "ReadWorkerStateTool"),
            ("worker_agent_id", "WaitForWorkerAgentTool"),
            ("timeout_seconds", "WaitForWorkerAgentTool"),
            ("message", "SendInstructionToWorkerAgentTool"),
        ]

        for field, tool in schema_checks:
            if field not in content:
                print(f"❌ {tool} missing {field} in schema")
                return False

        print("✓ Tool schemas are correct")
        return True

    except Exception as e:
        print(f"❌ Error testing tool schemas: {e}")
        return False


def test_orchestrator_notes_match():
    """Test that implementation matches the orchestrator notes."""
    print("🧪 Testing implementation matches notes...")

    notes_file = Path("tools/orchestrator_notes.md")
    if not notes_file.exists():
        print("❌ orchestrator_notes.md does not exist")
        return False

    # Notes file exists, now check implementation

    # Check that all tools from notes are implemented
    expected_tools = [
        "start_worker_agent",
        "read_worker_state",
        "wait_for_worker_agent",
        "send_instruction_to_worker_agent",
        "stop_worker_agent",
        "delete_worker_agent",
    ]

    tools_file = Path("tools/orchestrator_tools.py")
    with open(tools_file) as f:
        tools_content = f.read()

    for tool in expected_tools:
        # Convert to class name format
        class_name = "".join(word.capitalize() for word in tool.split("_")) + "Tool"
        if class_name not in tools_content:
            print(f"❌ Tool {tool} not implemented as {class_name}")
            return False

    print("✓ Implementation matches orchestrator notes")
    return True


def main():
    """Run all orchestrator tests."""
    print("🚀 Testing New Orchestrator System")
    print("=" * 40)

    tests = [
        test_orchestrator_tools_exist,
        test_orchestrator_mode_provider,
        test_interactive_agent_integration,
        test_system_prompt_content,
        test_tool_schemas,
        test_orchestrator_notes_match,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")
            print()

    print("=" * 40)
    print(f"📊 Results: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 NEW ORCHESTRATOR SYSTEM READY!")
        print("\n✅ Implementation Complete:")
        print("   • Orchestrator tools implemented with proper schemas")
        print("   • Orchestrator mode provider created")
        print("   • Interactive agent integration with --orchestrator flag")
        print("   • System prompt matches specifications")
        print("   • All tools from orchestrator_notes.md implemented")

        print("\n🚀 Usage:")
        print("   python experimental/guy/agent_qa/interactive_agent.py --orchestrator")

        print("\n📋 Available Tools:")
        print("   • start_worker_agent - Launch new worker agents")
        print("   • read_worker_state - Check worker status")
        print("   • wait_for_worker_agent - Wait for completion")
        print("   • send_instruction_to_worker_agent - Send messages")
        print("   • stop_worker_agent - Stop workers")
        print("   • delete_worker_agent - Clean up workers")

        print("\n⚠️  Next Steps:")
        print("   • Implement real worker agent launching (currently mock)")
        print("   • Implement worker-orchestrator communication protocol")
        print("   • Test with real worker agents")

        return True
    else:
        print(f"\n⚠️  {total - passed} issues need to be addressed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3.11
"""
GitHub Webhook CLI Tool

This tool allows you to point at an existing Pull Request and POST webhook events
to test the GitHub webhook integration and triggers system.

Features:
- Fetch real PR data from GitHub API
- Generate realistic webhook events for different PR actions
- Post events to webhook endpoints for testing
- Support multiple event types (pull_request, push, etc.)
- Integrate with existing authentication patterns

Usage:
    # Test a PR opened event
    python github_webhook_cli.py pr --url https://github.com/igor0/augment/pull/123 --action opened

    # Test a PR ready for review event
    python github_webhook_cli.py pr --repo igor0/augment --number 123 --action ready_for_review

    # Test a push event
    python github_webhook_cli.py push --repo igor0/augment --branch main --before abc123 --after def456

    # List available actions
    python github_webhook_cli.py pr --list-actions
"""

import argparse
import json
import logging
import os
import sys
import time
import traceback
import requests
import hmac
import hashlib
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

# Add the repository root to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class GitHubWebhookCLI:
    """CLI tool for testing GitHub webhook events."""

    def __init__(
        self, github_token: Optional[str] = None, webhook_url: Optional[str] = None
    ):
        self.github_token = github_token or self._get_github_token()
        self.webhook_url = webhook_url
        self.session = requests.Session()
        if self.github_token:
            self.session.headers.update(
                {
                    "Authorization": f"token {self.github_token}",
                    "Accept": "application/vnd.github.v3+json",
                    "User-Agent": "Augment-GitHub-Webhook-CLI/1.0",
                }
            )

    def _get_github_token(self) -> Optional[str]:
        """Get GitHub token from environment or config files."""
        # Check environment variable
        token = os.environ.get("GITHUB_TOKEN")
        if token:
            return token

        # Check common config locations
        config_paths = [
            os.path.expanduser("~/.github/token"),
            os.path.expanduser("~/.config/github/token"),
            os.path.expanduser("~/.augment/github-token"),
        ]

        for path in config_paths:
            if os.path.exists(path):
                try:
                    with open(path, "r") as f:
                        return f.read().strip()
                except Exception as e:
                    logger.warning(f"Could not read token from {path}: {e}")

        logger.warning("No GitHub token found. Some features may not work.")
        return None

    def fetch_pull_request(self, repo: str, pr_number: int) -> Dict[str, Any]:
        """Fetch pull request data from GitHub API."""
        if not self.github_token:
            raise ValueError("GitHub token required to fetch PR data")

        url = f"https://api.github.com/repos/{repo}/pulls/{pr_number}"
        logger.info(f"Fetching PR data from: {url}")

        response = self.session.get(url)
        response.raise_for_status()

        return response.json()

    def parse_github_url(self, url: str) -> tuple[str, int]:
        """Parse GitHub PR URL to extract repo and PR number."""
        parsed = urlparse(url)
        if parsed.hostname != "github.com":
            raise ValueError(f"Invalid GitHub URL: {url}")

        path_parts = parsed.path.strip("/").split("/")
        if len(path_parts) < 4 or path_parts[2] != "pull":
            raise ValueError(f"Invalid GitHub PR URL format: {url}")

        repo = f"{path_parts[0]}/{path_parts[1]}"
        pr_number = int(path_parts[3])

        return repo, pr_number

    def generate_mock_pr_data(self, repo: str, pr_number: int) -> Dict[str, Any]:
        """Generate mock PR data for testing when GitHub API is not available."""
        owner, repo_name = repo.split("/", 1)

        return {
            "id": 123456789,
            "number": pr_number,
            "title": f"Test Pull Request #{pr_number}",
            "body": "This is a test pull request for webhook testing.",
            "state": "open",
            "user": {"login": "test-user", "id": 987654321, "type": "User"},
            "assignee": None,
            "assignees": [],
            "labels": [{"name": "test"}, {"name": "webhook"}],
            "head": {
                "ref": "feature-branch",
                "sha": "def456abc123",
                "repo": {
                    "name": repo_name,
                    "full_name": repo,
                    "owner": {"login": owner},
                },
            },
            "base": {
                "ref": "main",
                "sha": "abc123def456",
                "repo": {
                    "id": 123456789,
                    "name": repo_name,
                    "full_name": repo,
                    "owner": {"login": owner, "id": 123456, "type": "User"},
                    "private": False,
                    "default_branch": "main",
                },
            },
            "created_at": "2025-05-25T19:00:00Z",
            "updated_at": "2025-05-25T19:00:00Z",
            "draft": False,
        }

    def generate_pull_request_event(
        self, pr_data: Dict[str, Any], action: str, installation_id: int = 12345678
    ) -> tuple[Dict[str, str], Dict[str, Any]]:
        """Generate a GitHub pull request webhook event."""

        # Extract repository info
        repo = pr_data["base"]["repo"]

        # Generate headers
        headers = {
            "Content-Type": "application/json",
            "X-GitHub-Event": "pull_request",
            "X-GitHub-Delivery": f"12345678-1234-1234-1234-{int(time.time())}",
            "User-Agent": "GitHub-Hookshot/abc123",
        }

        # Generate event payload
        payload = {
            "action": action,
            "number": pr_data["number"],
            "pull_request": {
                "id": pr_data["id"],
                "number": pr_data["number"],
                "title": pr_data["title"],
                "body": pr_data.get("body", ""),
                "state": pr_data["state"],
                "user": {
                    "login": pr_data["user"]["login"],
                    "id": pr_data["user"]["id"],
                    "type": pr_data["user"]["type"],
                },
                "assignee": pr_data.get("assignee"),
                "assignees": pr_data.get("assignees", []),
                "labels": [
                    {"name": label["name"]} for label in pr_data.get("labels", [])
                ],
                "head": {
                    "ref": pr_data["head"]["ref"],
                    "sha": pr_data["head"]["sha"],
                    "repo": {
                        "name": pr_data["head"]["repo"]["name"],
                        "full_name": pr_data["head"]["repo"]["full_name"],
                        "owner": {"login": pr_data["head"]["repo"]["owner"]["login"]},
                    },
                },
                "base": {
                    "ref": pr_data["base"]["ref"],
                    "sha": pr_data["base"]["sha"],
                    "repo": {
                        "name": repo["name"],
                        "full_name": repo["full_name"],
                        "owner": {"login": repo["owner"]["login"]},
                    },
                },
                "created_at": pr_data["created_at"],
                "updated_at": pr_data["updated_at"],
                "draft": pr_data.get("draft", False),
            },
            "repository": {
                "id": repo["id"],
                "name": repo["name"],
                "full_name": repo["full_name"],
                "owner": {
                    "login": repo["owner"]["login"],
                    "id": repo["owner"]["id"],
                    "type": repo["owner"]["type"],
                },
                "private": repo["private"],
                "default_branch": repo["default_branch"],
            },
            "installation": {"id": installation_id},
            "sender": {
                "login": pr_data["user"]["login"],
                "id": pr_data["user"]["id"],
                "type": pr_data["user"]["type"],
            },
        }

        return headers, payload

    def generate_push_event(
        self,
        repo: str,
        branch: str,
        before_sha: str,
        after_sha: str,
        installation_id: int = 12345678,
        commits: Optional[List[Dict[str, Any]]] = None,
    ) -> tuple[Dict[str, str], Dict[str, Any]]:
        """Generate a GitHub push webhook event."""

        owner, repo_name = repo.split("/", 1)

        headers = {
            "Content-Type": "application/json",
            "X-GitHub-Event": "push",
            "X-GitHub-Delivery": f"12345678-1234-1234-1234-{int(time.time())}",
            "User-Agent": "GitHub-Hookshot/abc123",
        }

        if commits is None:
            commits = [
                {
                    "id": after_sha,
                    "message": "Test commit for webhook testing",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "url": f"https://github.com/{repo}/commit/{after_sha}",
                    "author": {"name": "Test Author", "email": "<EMAIL>"},
                    "committer": {"name": "Test Author", "email": "<EMAIL>"},
                }
            ]

        payload = {
            "ref": f"refs/heads/{branch}",
            "before": before_sha,
            "after": after_sha,
            "created": before_sha == "0000000000000000000000000000000000000000",
            "deleted": after_sha == "0000000000000000000000000000000000000000",
            "forced": False,
            "commits": commits,
            "head_commit": commits[-1] if commits else None,
            "repository": {
                "id": 123456789,
                "name": repo_name,
                "full_name": repo,
                "owner": {
                    "name": owner,
                    "login": owner,
                    "email": f"{owner}@example.com",
                },
                "private": False,
                "default_branch": "main",
            },
            "pusher": {"name": "test-user", "email": "<EMAIL>"},
            "installation": {"id": installation_id},
            "sender": {"login": "test-user", "id": 987654321, "type": "User"},
        }

        return headers, payload

    def sign_payload(self, payload: str, secret: str) -> str:
        """Generate GitHub webhook signature."""
        signature = hmac.new(
            secret.encode("utf-8"), payload.encode("utf-8"), hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"

    def post_webhook_event(
        self,
        headers: Dict[str, str],
        payload: Dict[str, Any],
        webhook_url: str,
        webhook_secret: Optional[str] = None,
        use_fake_endpoint: bool = True,
        dry_run: bool = False,
    ) -> Optional[requests.Response]:
        """Post webhook event to the specified URL."""

        # Convert payload to JSON string
        payload_json = json.dumps(payload, separators=(",", ":"))

        # Add signature if secret provided
        if webhook_secret:
            signature = self.sign_payload(payload_json, webhook_secret)
            headers["X-Hub-Signature-256"] = signature

        # Determine endpoint
        endpoint = "/fake_postreceive" if use_fake_endpoint else "/postreceive"
        full_url = f"{webhook_url.rstrip('/')}{endpoint}"

        logger.info(f"Event type: {headers.get('X-GitHub-Event')}")
        logger.info(f"Action: {payload.get('action', 'N/A')}")

        if dry_run:
            logger.info("🔍 DRY RUN MODE - Event generated but not posted")
            logger.info(f"Would post to: {full_url}")
            logger.info(f"Headers: {json.dumps(headers, indent=2)}")
            logger.info(f"Payload: {json.dumps(payload, indent=2)}")
            return None

        logger.info(f"Posting webhook event to: {full_url}")

        # Post the event
        response = requests.post(
            full_url,
            headers=headers,
            data=payload_json,
            verify=False,  # For dev/testing environments
            timeout=30,
        )

        logger.info(f"Response status: {response.status_code}")
        if response.status_code != 200:
            logger.error(f"Response body: {response.text}")

        return response

    def get_available_pr_actions(self) -> List[str]:
        """Get list of available pull request actions."""
        return [
            "opened",
            "closed",
            "reopened",
            "edited",
            "assigned",
            "unassigned",
            "labeled",
            "unlabeled",
            "synchronize",
            "ready_for_review",
            "converted_to_draft",
            "review_requested",
            "review_request_removed",
            "auto_merge_enabled",
            "auto_merge_disabled",
            "locked",
            "unlocked",
        ]

    def test_pr_webhook(
        self,
        repo: Optional[str] = None,
        pr_number: Optional[int] = None,
        pr_url: Optional[str] = None,
        action: str = "opened",
        webhook_url: Optional[str] = None,
        installation_id: int = 12345678,
        webhook_secret: Optional[str] = None,
        use_fake_endpoint: bool = True,
        dry_run: bool = False,
    ) -> bool:
        """Test a pull request webhook event."""

        try:
            # Parse URL if provided
            if pr_url:
                repo, pr_number = self.parse_github_url(pr_url)

            if not repo or not pr_number:
                raise ValueError(
                    "Either --url or both --repo and --number must be provided"
                )

            # Fetch PR data
            if self.github_token:
                logger.info(f"Fetching PR #{pr_number} from {repo}")
                pr_data = self.fetch_pull_request(repo, pr_number)
            else:
                logger.info(
                    f"No GitHub token available, using mock PR data for #{pr_number}"
                )
                pr_data = self.generate_mock_pr_data(repo, pr_number)

            # Generate webhook event
            logger.info(f"Generating {action} event for PR #{pr_number}")
            headers, payload = self.generate_pull_request_event(
                pr_data, action, installation_id
            )

            # Post webhook event
            webhook_url = webhook_url or self.webhook_url
            if not webhook_url:
                raise ValueError(
                    "Webhook URL must be provided via --webhook-url or constructor"
                )

            response = self.post_webhook_event(
                headers,
                payload,
                webhook_url,
                webhook_secret,
                use_fake_endpoint,
                dry_run,
            )

            if dry_run:
                logger.info("✅ Webhook event generated successfully!")
                return True

            success = response.status_code == 200
            if success:
                logger.info("✅ Webhook event posted successfully!")
            else:
                logger.error(
                    f"❌ Webhook event failed with status {response.status_code}"
                )

            return success

        except Exception as e:
            logger.error(f"❌ Error testing PR webhook: {e}")
            logger.error(traceback.format_exc())
            return False

    def test_push_webhook(
        self,
        repo: str,
        branch: str,
        before_sha: str,
        after_sha: str,
        webhook_url: Optional[str] = None,
        installation_id: int = 12345678,
        webhook_secret: Optional[str] = None,
        use_fake_endpoint: bool = True,
        dry_run: bool = False,
    ) -> bool:
        """Test a push webhook event."""

        try:
            # Generate webhook event
            logger.info(f"Generating push event for {repo}:{branch}")
            headers, payload = self.generate_push_event(
                repo, branch, before_sha, after_sha, installation_id
            )

            # Post webhook event
            webhook_url = webhook_url or self.webhook_url
            if not webhook_url:
                raise ValueError(
                    "Webhook URL must be provided via --webhook-url or constructor"
                )

            response = self.post_webhook_event(
                headers,
                payload,
                webhook_url,
                webhook_secret,
                use_fake_endpoint,
                dry_run,
            )

            if dry_run:
                logger.info("✅ Webhook event generated successfully!")
                return True

            success = response.status_code == 200
            if success:
                logger.info("✅ Webhook event posted successfully!")
            else:
                logger.error(
                    f"❌ Webhook event failed with status {response.status_code}"
                )

            return success

        except Exception as e:
            logger.error(f"❌ Error testing push webhook: {e}")
            logger.error(traceback.format_exc())
            return False


def get_default_webhook_url() -> str:
    """Get default webhook URL based on environment."""
    # Try to determine from user config
    try:
        user_config_path = os.path.expanduser("~/.augment/user.json")
        if os.path.exists(user_config_path):
            with open(user_config_path, "r") as f:
                user_config = json.load(f)
                username = user_config.get("name")
                if username:
                    return f"https://github-app-webhook.dev-{username}.us-central.api.augmentcode.com"
    except Exception:
        pass

    # Default to localhost for development
    return "https://localhost:5000"


def main():
    """Main function to handle command-line arguments and execute commands."""
    parser = argparse.ArgumentParser(
        description="CLI tool for testing GitHub webhook events",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test a PR opened event using URL
  python github_webhook_cli.py pr --url https://github.com/igor0/augment/pull/123 --action opened

  # Test a PR ready for review event using repo/number
  python github_webhook_cli.py pr --repo igor0/augment --number 123 --action ready_for_review

  # Test a push event
  python github_webhook_cli.py push --repo igor0/augment --branch main --before abc123 --after def456

  # List available PR actions
  python github_webhook_cli.py pr --list-actions

  # Use custom webhook URL
  python github_webhook_cli.py pr --url https://github.com/igor0/augment/pull/123 \\
    --webhook-url https://github-app-webhook.dev-igor.us-central.api.augmentcode.com

  # Use real endpoint (not fake)
  python github_webhook_cli.py pr --url https://github.com/igor0/augment/pull/123 \\
    --no-fake-endpoint --webhook-secret your-secret-here

  # Generate event without posting (dry run)
  python github_webhook_cli.py pr --repo igor0/augment --number 123 --action opened --dry-run
        """,
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Pull request command
    pr_parser = subparsers.add_parser("pr", help="Test pull request webhook events")
    pr_parser.add_argument(
        "--url",
        type=str,
        help="GitHub PR URL (e.g., https://github.com/owner/repo/pull/123)",
    )
    pr_parser.add_argument("--repo", type=str, help="Repository in owner/repo format")
    pr_parser.add_argument("--number", type=int, help="Pull request number")
    pr_parser.add_argument(
        "--action", type=str, default="opened", help="PR action (default: opened)"
    )
    pr_parser.add_argument(
        "--list-actions", action="store_true", help="List available PR actions"
    )

    # Push command
    push_parser = subparsers.add_parser("push", help="Test push webhook events")
    push_parser.add_argument(
        "--repo", type=str, required=True, help="Repository in owner/repo format"
    )
    push_parser.add_argument(
        "--branch", type=str, default="main", help="Branch name (default: main)"
    )
    push_parser.add_argument("--before", type=str, required=True, help="Before SHA")
    push_parser.add_argument("--after", type=str, required=True, help="After SHA")

    # Common arguments
    for subparser in [pr_parser, push_parser]:
        subparser.add_argument("--webhook-url", type=str, help="Webhook URL to post to")
        subparser.add_argument(
            "--installation-id",
            type=int,
            default=12345678,
            help="GitHub App installation ID",
        )
        subparser.add_argument(
            "--webhook-secret", type=str, help="Webhook secret for signature"
        )
        subparser.add_argument(
            "--no-fake-endpoint",
            action="store_true",
            help="Use real endpoint instead of fake",
        )
        subparser.add_argument("--github-token", type=str, help="GitHub API token")
        subparser.add_argument(
            "--verbose", action="store_true", help="Enable verbose logging"
        )
        subparser.add_argument(
            "--dry-run",
            action="store_true",
            help="Generate event but do not post to webhook",
        )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Handle list actions
    if hasattr(args, "list_actions") and args.list_actions:
        cli = GitHubWebhookCLI()
        actions = cli.get_available_pr_actions()
        print("Available pull request actions:")
        for action in actions:
            print(f"  - {action}")
        return

    if not args.command:
        parser.print_help()
        return

    try:
        # Get webhook URL
        webhook_url = args.webhook_url or get_default_webhook_url()
        logger.info(f"Using webhook URL: {webhook_url}")

        # Create CLI instance
        cli = GitHubWebhookCLI(
            github_token=getattr(args, "github_token", None), webhook_url=webhook_url
        )

        # Execute command
        success = False
        if args.command == "pr":
            success = cli.test_pr_webhook(
                repo=getattr(args, "repo", None),
                pr_number=getattr(args, "number", None),
                pr_url=getattr(args, "url", None),
                action=args.action,
                webhook_url=webhook_url,
                installation_id=args.installation_id,
                webhook_secret=getattr(args, "webhook_secret", None),
                use_fake_endpoint=not getattr(args, "no_fake_endpoint", False),
                dry_run=getattr(args, "dry_run", False),
            )
        elif args.command == "push":
            success = cli.test_push_webhook(
                repo=args.repo,
                branch=args.branch,
                before_sha=args.before,
                after_sha=args.after,
                webhook_url=webhook_url,
                installation_id=args.installation_id,
                webhook_secret=getattr(args, "webhook_secret", None),
                use_fake_endpoint=not getattr(args, "no_fake_endpoint", False),
                dry_run=getattr(args, "dry_run", False),
            )

        sys.exit(0 if success else 1)

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()

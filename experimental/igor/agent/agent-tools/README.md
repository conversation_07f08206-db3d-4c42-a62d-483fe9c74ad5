# Agent Tools

Command-line tools for interacting with remote agents and authentication.

## Recommended Usage

For the best experience with these tools, follow these steps:

1. **Deploy in your dev environment**
   ```bash
   bazel run //services/deploy:dev_deploy -- --services chat agents default remote_agents_all --operation Apply
   ```

2. **Sign in via the dev_deploy_signin.js script**
   ```bash
   ./dev_deploy_signin.js
   ```
   This will:
   - Read your username from ~/.augment/user.json
   - Generate an OAuth token
   - Save it to ~/.augment/dev-deploy-oauth2-token.txt

3. **Interact with remote_agent_cli.py**
   - Use it directly from the command line:
     ```bash
     ./remote_agent_cli.py list
     ```
   - Or use it via Augment Agent for a more interactive experience

## Remote Agent CLI

A command-line tool for interacting with remote agents.

### Features

- Create remote agents
- List all remote agents
- Get chat history for a remote agent
- Chat with remote agents (streaming and non-streaming)
- Delete remote agents
- Interrupt remote agents

## Usage

```bash
# List all remote agents
./remote_agent_cli.py list

# Create a new remote agent and wait for it to be ready
./remote_agent_cli.py create --repository-url "https://github.com/igor0/augment" --git-ref "main" --wait

# Get chat history for a remote agent starting from a specific sequence ID
./remote_agent_cli.py history <agent-id> --last-sequence-id <sequence-id>

# Chat with a remote agent and wait for the response
./remote_agent_cli.py chat <agent-id> "What can you help me with?" --wait

# Chat with a remote agent without waiting
./remote_agent_cli.py chat <agent-id> "What else can you help me with?" --wait

# Interrupt a remote agent
./remote_agent_cli.py interrupt <agent-id>

# Get chat history for a remote agent
./remote_agent_cli.py history <agent-id>

# Delete a remote agent
./remote_agent_cli.py delete <agent-id>
```

## Commands

- `list`: List all remote agents
- `create`: Create a new remote agent
- `history <agent-id>`: Get chat history for a remote agent
- `chat <agent-id> <message>`: Send a message to a remote agent
- `delete <agent-id>`: Delete a remote agent
- `interrupt <agent-id>`: Interrupt a remote agent

## Common Options

- `--api-url <url>`: API URL (default: constructed from username in ~/.augment/user.json)
- `--token-file <file>`: File containing the API token (default: ~/.augment/dev-deploy-oauth2-token.txt)

## Create Command Options

- `--system-prompt <prompt>`: System prompt for the new agent
- `--initial-message <message>`: Initial message for the new agent
- `--repository-url <url>`: Repository URL for the new agent
- `--git-ref <ref>`: Git reference for the new agent
- `--model <model>`: Model for the new agent
- `--wait`: Wait for the agent to be ready before returning
- `--wait-timeout <seconds>`: Maximum time to wait for the agent to be ready (default: 5 minutes)

## History Command Options

- `--last-sequence-id <id>`: Last processed sequence ID for chat history

## Chat Command Options

- `--wait`: Wait for the agent to respond before returning
- `--wait-timeout <seconds>`: Maximum time to wait for the agent to respond (default: 5 minutes)

## Requirements

- Python 3.11 or higher
- Augment client library

## Notes

- The streaming API is still under development and may not work correctly in all environments.
- The chat functionality (both streaming and non-streaming) may not return responses in some environments.

## Dev Deploy Sign-in

A Node.js script for generating OAuth tokens for authentication with the dev deployment of the Augment API.

### Usage

```bash
# Run the script
./dev_deploy_signin.js
```

The script will:
1. Read your username from ~/.augment/user.json
2. Construct the namespace as `dev-{username}`
3. Construct the auth server URL as `https://auth-central.{namespace}.us-central1.dev.augmentcode.com`
4. Generate a PKCE code verifier and challenge
5. Provide a URL to open in your browser for authentication
6. Ask for the authentication code received after login
   - Handles both plain code and JSON objects containing a code property
7. Exchange the code for an access token
8. Write the token to ~/.augment/dev-deploy-oauth2-token.txt

### Requirements

- Node.js

## GitHub Webhook CLI Tool

The `github_webhook_cli.py` tool allows you to point at an existing Pull Request and POST webhook events to test the GitHub webhook integration and triggers system.

### Features

- **Fetch real PR data** from GitHub API using existing PR URLs or PR numbers
- **Generate realistic webhook events** for different PR actions (opened, closed, ready_for_review, etc.)
- **Post events to webhook endpoints** (both fake and real endpoints for testing)
- **Support multiple event types** (pull_request, push, etc.)
- **Integrate with existing authentication** patterns from the codebase

### Setup

1. **GitHub Token** (optional but recommended for fetching real PR data):
   ```bash
   export GITHUB_TOKEN=your_github_token_here
   # OR create ~/.augment/github-token file
   ```

2. **Webhook URL** (auto-detected from user config or can be specified):
   - Auto-detected: `https://github-app-webhook.dev-{username}.us-central.api.augmentcode.com`
   - Default fallback: `https://localhost:5000`

### Usage Examples

#### Pull Request Events

```bash
# Test a PR opened event using URL
python github_webhook_cli.py pr --url https://github.com/igor0/augment/pull/123 --action opened

# Test a PR ready for review event using repo/number
python github_webhook_cli.py pr --repo igor0/augment --number 123 --action ready_for_review

# List available PR actions
python github_webhook_cli.py pr --list-actions

# Use custom webhook URL
python github_webhook_cli.py pr --url https://github.com/igor0/augment/pull/123 \
  --webhook-url https://github-app-webhook.dev-igor.us-central.api.augmentcode.com

# Use real endpoint (not fake) with webhook secret
python github_webhook_cli.py pr --url https://github.com/igor0/augment/pull/123 \
  --no-fake-endpoint --webhook-secret your-secret-here
```

#### Push Events

```bash
# Test a push event
python github_webhook_cli.py push --repo igor0/augment --branch main \
  --before abc123def --after def456abc

# Test with custom webhook URL
python github_webhook_cli.py push --repo igor0/augment --branch feature-branch \
  --before abc123def --after def456abc \
  --webhook-url https://github-app-webhook.dev-igor.us-central.api.augmentcode.com
```

### Available PR Actions

- `opened` - PR was opened
- `closed` - PR was closed
- `reopened` - PR was reopened
- `edited` - PR title/body was edited
- `assigned` - PR was assigned to someone
- `unassigned` - PR was unassigned
- `labeled` - Label was added to PR
- `unlabeled` - Label was removed from PR
- `synchronize` - PR was updated with new commits
- `ready_for_review` - Draft PR was marked ready for review
- `converted_to_draft` - PR was converted to draft
- `review_requested` - Review was requested
- `review_request_removed` - Review request was removed
- `auto_merge_enabled` - Auto-merge was enabled
- `auto_merge_disabled` - Auto-merge was disabled
- `locked` - PR was locked
- `unlocked` - PR was unlocked

### Integration with Existing Testing

This tool integrates with the existing webhook testing infrastructure:

- Uses the same `/fake_postreceive` endpoint as existing tests
- Generates events compatible with the GitHub event handler
- Supports the same installation ID mapping used in tests
- Can be used alongside existing trigger testing tools

### Common Use Cases

1. **Testing Trigger Conditions**: Generate specific PR events to test trigger matching logic
2. **End-to-End Testing**: Test the full webhook → trigger → agent spawning flow
3. **Development Testing**: Quickly test webhook handling during development
4. **Integration Testing**: Validate webhook processing with real PR data structures

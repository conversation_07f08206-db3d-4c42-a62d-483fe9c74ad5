#!/usr/bin/env python3.11
"""
Command-line tool for testing remote agent triggers.

This script provides a command-line interface for creating, listing, updating,
and deleting remote agent triggers. It's designed for testing and validation
of the trigger system implementation.
"""

import argparse
import json
import logging
import os
import sys
import time
import traceback
from typing import List, Optional, Dict, Any

# Add the repository root to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
)

from base.augment_client.client import AugmentClient

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def get_username_from_config() -> str:
    """Get the username from the user config file."""
    user_config_path = os.path.expanduser("~/.augment/user.json")

    if not os.path.exists(user_config_path):
        raise ValueError(
            "User config file not found. Create ~/.augment/user.json with your username."
        )

    try:
        with open(user_config_path, "r") as f:
            user_config = json.load(f)

        if "name" not in user_config:
            raise ValueError("Username not found in config file.")

        return user_config["name"]
    except json.JSONDecodeError:
        raise ValueError("Invalid JSON in user config file.")
    except Exception as e:
        raise ValueError(f"Error reading username from config: {e}")


def get_api_url(api_url: Optional[str] = None) -> str:
    """Get the API URL from the provided URL or construct it from the username."""
    if api_url:
        return api_url

    try:
        username = get_username_from_config()
        namespace = f"dev-{username}"
        return f"https://{namespace}.us-central.api.augmentcode.com"
    except ValueError as e:
        logger.warning(f"Could not construct API URL from username: {e}")
        logger.warning("Using default API URL: https://api.augment.dev")
        return "https://api.augment.dev"


def get_api_token(token_file: Optional[str] = None) -> str:
    """Get the API token from the token file, environment, or default locations."""
    # Check if token file is provided
    if token_file and os.path.exists(token_file):
        with open(token_file, "r") as f:
            return f.read().strip()

    # Check environment variable
    token = os.environ.get("AUGMENT_API_TOKEN")
    if token:
        return token

    # Check dev-deploy token file (new default)
    dev_deploy_token_path = os.path.expanduser("~/.augment/dev-deploy-oauth2-token.txt")
    if os.path.exists(dev_deploy_token_path):
        with open(dev_deploy_token_path, "r") as f:
            return f.read().strip()

    # Check default token file
    token_path = os.path.expanduser("~/.augment/token")
    if os.path.exists(token_path):
        with open(token_path, "r") as f:
            return f.read().strip()

    raise ValueError(
        "API token not found. Set AUGMENT_API_TOKEN environment variable, provide --token-file argument, "
        "or create ~/.augment/dev-deploy-oauth2-token.txt file using dev_deploy_signin.js."
    )


def create_trigger(client: AugmentClient, trigger_config: Dict[str, Any]) -> None:
    """Create a new trigger."""
    logger.info("Creating trigger...")
    logger.info(f"Trigger config: {json.dumps(trigger_config, indent=2)}")

    try:
        # Wrap the trigger config in the expected CreateTriggerRequest structure
        request_data = {"configuration": trigger_config}

        # Use the client's _post method to make a direct API call
        response, _ = client._post(
            "triggers/create",
            json=request_data,
        )

        if not response.ok:
            logger.error(
                f"HTTP error creating trigger: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        logger.info("Trigger created successfully!")
        logger.info(
            f"Trigger ID: {result.get('trigger', {}).get('trigger_id', 'Unknown')}"
        )
        logger.info(f"Trigger Name: {result.get('trigger', {}).get('name', 'Unknown')}")

    except Exception as e:
        logger.error(f"Error creating trigger: {e}")
        logger.error(traceback.format_exc())


def list_triggers(
    client: AugmentClient, verbose: bool = False, json_output: bool = False
) -> None:
    """List all triggers."""
    if not json_output:
        logger.info("Listing triggers...")

    try:
        # Use _post with empty JSON for list operation (following remote agent CLI pattern)
        response, _ = client._post("triggers/list", json={})

        if not response.ok:
            logger.error(
                f"HTTP error listing triggers: {response.status_code} {response.text}"
            )
            return

        result = response.json()

        if json_output:
            # Output raw JSON for debugging
            print(json.dumps(result, indent=2))
            return

        triggers = result.get("triggers", [])

        if not triggers:
            logger.info("No triggers found.")
            return

        logger.info(f"Found {len(triggers)} triggers:")
        for trigger in triggers:
            # Try multiple possible field names for the trigger name
            # Check both top-level and nested configuration fields
            trigger_name = (
                trigger.get("name")
                or trigger.get("trigger_name")
                or trigger.get("configuration", {}).get("name")
                or "Unnamed Trigger"
            )

            # Also try to get description from configuration if not at top level
            description = (
                trigger.get("description")
                or trigger.get("configuration", {}).get("description")
                or "None"
            )

            # Also try to get event_source from configuration if not at top level
            event_source = (
                trigger.get("event_source")
                or trigger.get("configuration", {}).get("event_source")
                or "Unknown"
            )

            # Convert numeric event source to readable name if it's a number
            if isinstance(event_source, int):
                event_source_names = {
                    0: "UNSPECIFIED",
                    1: "GITHUB",
                    2: "LINEAR",
                    3: "JIRA",
                }
                event_source = event_source_names.get(
                    event_source, f"UNKNOWN({event_source})"
                )

            # Debug: Show available keys if name is missing and verbose mode is on
            if verbose and trigger_name == "Unnamed Trigger":
                logger.debug(
                    f"  Debug - Available trigger keys: {list(trigger.keys())}"
                )
                if "configuration" in trigger:
                    logger.debug(
                        f"  Debug - Configuration keys: {list(trigger['configuration'].keys())}"
                    )

            logger.info(f"  ID: {trigger.get('trigger_id', 'Unknown')}")
            logger.info(f"  Name: {trigger_name}")
            logger.info(f"  Description: {description}")
            # Also try to get enabled status from configuration if not at top level
            enabled = (
                trigger.get("enabled")
                if trigger.get("enabled") is not None
                else trigger.get("configuration", {}).get("enabled")
                if trigger.get("configuration", {}).get("enabled") is not None
                else False
            )

            logger.info(f"  Event Source: {event_source}")
            logger.info(f"  Enabled: {enabled}")
            logger.info(f"  Created: {trigger.get('created_at', 'Unknown')}")

            if verbose:
                # Show detailed information in verbose mode
                logger.info(f"  Updated: {trigger.get('updated_at', 'Unknown')}")

                # Show conditions details
                conditions = trigger.get("conditions", {})
                if conditions:
                    logger.info(
                        f"  Conditions Type: {conditions.get('type', 'Unknown')}"
                    )

                    # Show GitHub conditions if present
                    github_conditions = conditions.get("github", {})
                    if github_conditions:
                        logger.info("  GitHub Conditions:")
                        logger.info(
                            f"    Entity Type: {github_conditions.get('entity_type', 'Unknown')}"
                        )

                        # Show pull request conditions
                        pr_conditions = github_conditions.get("pull_request", {})
                        if pr_conditions:
                            logger.info("    Pull Request Conditions:")
                            if "repository" in pr_conditions:
                                logger.info(
                                    f"      Repository: {pr_conditions['repository']}"
                                )
                            if "labels" in pr_conditions:
                                logger.info(f"      Labels: {pr_conditions['labels']}")
                            if "base_branch" in pr_conditions:
                                logger.info(
                                    f"      Base Branch: {pr_conditions['base_branch']}"
                                )
                            if "head_branch" in pr_conditions:
                                logger.info(
                                    f"      Head Branch: {pr_conditions['head_branch']}"
                                )

                        # Show issue conditions
                        issue_conditions = github_conditions.get("issue", {})
                        if issue_conditions:
                            logger.info("    Issue Conditions:")
                            if "repository" in issue_conditions:
                                logger.info(
                                    f"      Repository: {issue_conditions['repository']}"
                                )
                            if "labels" in issue_conditions:
                                logger.info(
                                    f"      Labels: {issue_conditions['labels']}"
                                )

                        # Show commit conditions
                        commit_conditions = github_conditions.get("commit", {})
                        if commit_conditions:
                            logger.info("    Commit Conditions:")
                            if "repository" in commit_conditions:
                                logger.info(
                                    f"      Repository: {commit_conditions['repository']}"
                                )
                            if "branch" in commit_conditions:
                                logger.info(
                                    f"      Branch: {commit_conditions['branch']}"
                                )

                # Show workspace setup details
                workspace_setup = trigger.get("workspace_setup", {})
                if workspace_setup:
                    logger.info("  Workspace Setup:")
                    starting_files = workspace_setup.get("starting_files", {})
                    if starting_files:
                        github_ref = starting_files.get("github_commit_ref", {})
                        if github_ref:
                            logger.info("    Starting Files (GitHub):")
                            logger.info(
                                f"      Repository URL: {github_ref.get('repository_url', 'Unknown')}"
                            )
                            logger.info(
                                f"      Git Ref: {github_ref.get('git_ref', 'Unknown')}"
                            )

                # Show initial request details
                request_details = trigger.get("initial_request_details", {})
                if request_details:
                    logger.info("  Initial Request Details:")
                    logger.info(
                        f"    Model ID: {request_details.get('model_id', 'Unknown')}"
                    )
                    if "user_guidelines" in request_details:
                        guidelines = request_details["user_guidelines"]
                        # Truncate long guidelines for readability
                        if len(guidelines) > 100:
                            guidelines = guidelines[:100] + "..."
                        logger.info(f"    User Guidelines: {guidelines}")

                    request_nodes = request_details.get("request_nodes", [])
                    if request_nodes:
                        logger.info(f"    Request Nodes: {len(request_nodes)} nodes")

                # Show template information if present
                if trigger.get("template_id"):
                    logger.info(
                        f"  Template ID: {trigger.get('template_id', 'Unknown')}"
                    )
                    logger.info(
                        f"  Template Name: {trigger.get('template_name', 'Unknown')}"
                    )

            logger.info("  ---")

    except Exception as e:
        logger.error(f"Error listing triggers: {e}")
        logger.error(traceback.format_exc())


def delete_trigger(client: AugmentClient, trigger_id: str) -> None:
    """Delete a trigger."""
    logger.info(f"Deleting trigger {trigger_id}...")

    try:
        # Use _post for delete operation (following remote agent CLI pattern)
        response, _ = client._post("triggers/delete", json={"trigger_id": trigger_id})

        if not response.ok:
            logger.error(
                f"HTTP error deleting trigger: {response.status_code} {response.text}"
            )
            return

        logger.info("Trigger deleted successfully!")

    except Exception as e:
        logger.error(f"Error deleting trigger: {e}")
        logger.error(traceback.format_exc())


def get_trigger_executions(client: AugmentClient, trigger_id: str) -> None:
    """Get execution history for a trigger."""
    logger.info(f"Getting execution history for trigger {trigger_id}...")

    try:
        # Use _post for get executions operation
        response, _ = client._post(
            "triggers/executions", json={"trigger_id": trigger_id}
        )

        if not response.ok:
            logger.error(
                f"HTTP error getting executions: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        executions = result.get("executions", [])

        if not executions:
            logger.info("No executions found.")
            return

        logger.info(f"Found {len(executions)} executions:")
        for execution in executions:
            logger.info(f"  Execution ID: {execution.get('execution_id', 'Unknown')}")
            logger.info(f"  Status: {execution.get('status', 'Unknown')}")
            logger.info(f"  Agent ID: {execution.get('remote_agent_id', 'None')}")
            logger.info(f"  Started: {execution.get('started_at', 'Unknown')}")
            logger.info(f"  Completed: {execution.get('completed_at', 'Unknown')}")
            logger.info("  ---")

    except Exception as e:
        logger.error(f"Error getting trigger executions: {e}")
        logger.error(traceback.format_exc())


def get_matching_entities(
    client: AugmentClient,
    trigger_id: Optional[str] = None,
    event_source: int = 1,
    limit: int = 5,
    json_output: bool = False,
) -> None:
    """Get entities that match trigger conditions."""
    logger.info("Getting matching entities...")

    try:
        request_data = {
            "event_source": event_source,
            "limit": limit,
            "offset": 0,
        }

        if trigger_id:
            request_data["trigger_id"] = trigger_id

        # Use _post for get matching entities operation
        response, _ = client._post("triggers/matching-entities", json=request_data)

        if not response.ok:
            logger.error(
                f"HTTP error getting matching entities: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        entities = result.get("entities", [])
        total_count = result.get("total_count", 0)
        has_more = result.get("has_more", False)
        query_explanation = result.get("query_explanation", "")

        if json_output:
            # Output raw JSON for debugging
            import json

            print(json.dumps(result, indent=2))
            return

        logger.info(
            f"Found {len(entities)} entities (total: {total_count}, has_more: {has_more})"
        )
        if query_explanation:
            logger.info(f"Query explanation: {query_explanation}")

        for entity in entities:
            entity_type = entity.get("type", "unknown")
            entity_id = entity.get("id", "unknown")
            timestamp = entity.get("timestamp", "unknown")

            logger.info(f"  Entity ID: {entity_id}")
            logger.info(f"  Type: {entity_type}")
            logger.info(f"  Timestamp: {timestamp}")

            # Show type-specific details
            if (
                entity_type == "GITHUB_ENTITY_TYPE_PULL_REQUEST"
                and "pull_request" in entity
            ):
                pr = entity["pull_request"]
                logger.info(
                    f"  PR #{pr.get('number', 'N/A')}: {pr.get('title', 'No title')}"
                )
                logger.info(f"  Author: {pr.get('user', {}).get('login', 'Unknown')}")
                logger.info(f"  State: {pr.get('state', 'Unknown')}")
            elif entity_type == "GITHUB_ENTITY_TYPE_ISSUE" and "issue" in entity:
                issue = entity["issue"]
                logger.info(
                    f"  Issue #{issue.get('number', 'N/A')}: {issue.get('title', 'No title')}"
                )
                logger.info(
                    f"  Author: {issue.get('user', {}).get('login', 'Unknown')}"
                )
                logger.info(f"  State: {issue.get('state', 'Unknown')}")
            elif entity_type == "GITHUB_ENTITY_TYPE_COMMIT" and "commit" in entity:
                commit = entity["commit"]
                logger.info(f"  Commit: {commit.get('sha', 'Unknown')[:8]}")
                logger.info(f"  Message: {commit.get('message', 'No message')[:50]}...")
                logger.info(
                    f"  Author: {commit.get('author', {}).get('login', 'Unknown')}"
                )

            logger.info("  ---")

    except Exception as e:
        logger.error(f"Error getting matching entities: {e}")
        logger.error(traceback.format_exc())


def execute_trigger_manually(
    client: AugmentClient,
    trigger_id: str,
    entity_id: str,
    entity_type: int = 1,
) -> None:
    """Execute a trigger manually on a specific entity."""
    logger.info(f"Executing trigger {trigger_id} manually on entity {entity_id}...")

    try:
        request_data = {
            "trigger_id": trigger_id,
            "entity_id": entity_id,
            "entity_type": entity_type,
        }

        # Use _post for manual execution operation
        response, _ = client._post("triggers/execute-manually", json=request_data)

        if not response.ok:
            logger.error(
                f"HTTP error executing trigger manually: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        execution_id = result.get("execution_id", "Unknown")
        remote_agent_id = result.get("remote_agent_id", "None")
        status = result.get("status", "Unknown")
        message = result.get("message", "")

        logger.info("Trigger executed successfully!")
        logger.info(f"  Execution ID: {execution_id}")
        logger.info(f"  Remote Agent ID: {remote_agent_id}")
        logger.info(f"  Status: {status}")
        if message:
            logger.info(f"  Message: {message}")

    except Exception as e:
        logger.error(f"Error executing trigger manually: {e}")
        logger.error(traceback.format_exc())


def create_sample_pr_trigger() -> Dict[str, Any]:
    """Create a sample pull request trigger configuration."""
    return {
        "name": "Test Trigger",
        "description": "Test trigger",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {"repository": "igor0/augment"},
            },
        },
        "workspace_setup": {
            "starting_files": {
                "github_commit_ref": {
                    "repository_url": "https://github.com/igor0/augment",
                    "git_ref": "main",
                }
            }
        },
        "initial_request_details": {
            "request_nodes": [],
            "user_guidelines": "Test guidelines",
            "model_id": "claude-3-5-sonnet",
        },
        "enabled": True,
    }


def create_sample_pr_opened_trigger() -> Dict[str, Any]:
    """Create a sample pull request opened trigger configuration."""
    return {
        "name": "PR Opened Trigger",
        "description": "Automatically analyze new pull requests",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {
                    "repository": "igor0/augment",
                    "labels": ["needs-review"],
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_commit_ref": {
                        "repository_url": "https://github.com/igor0/augment",
                        "git_ref": "main",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "Analyze this new pull request. Provide initial feedback on the changes and suggest improvements.",
            "model": "claude-3-5-sonnet",
        },
        "enabled": True,
    }


def main():
    """Main function to handle command-line arguments and execute commands."""
    parser = argparse.ArgumentParser(
        description="CLI tool for testing remote agent triggers",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all triggers (summary view)
  python trigger_cli.py list

  # List all triggers with full details
  python trigger_cli.py list --verbose

  # Create a sample PR review trigger
  python trigger_cli.py create --sample pr-review

  # Create a sample PR opened trigger
  python trigger_cli.py create --sample pr-opened

  # Create a custom trigger from JSON file
  python trigger_cli.py create --config trigger.json

  # Delete a trigger
  python trigger_cli.py delete --trigger-id abc-123

  # Get execution history
  python trigger_cli.py executions --trigger-id abc-123

  # Get matching entities (with mock data)
  python trigger_cli.py matching-entities

  # Get matching entities for a specific trigger
  python trigger_cli.py matching-entities --trigger-id abc-123

  # Execute a trigger manually on an entity
  python trigger_cli.py execute-manually --trigger-id abc-123 --entity-id pr-1234

  # Test with custom API URL
  python trigger_cli.py list --api-url https://dev-igor.us-central.api.augmentcode.com

  # List triggers with verbose output and custom API URL
  python trigger_cli.py list --verbose --api-url https://dev-igor.us-central.api.augmentcode.com
        """,
    )

    parser.add_argument(
        "command",
        choices=[
            "list",
            "create",
            "delete",
            "executions",
            "matching-entities",
            "execute-manually",
        ],
        help="Command to execute",
    )

    parser.add_argument(
        "--api-url",
        type=str,
        help="API URL (default: constructed from username or https://api.augment.dev)",
    )

    parser.add_argument(
        "--token-file",
        type=str,
        help="Path to API token file (default: ~/.augment/dev-deploy-oauth2-token.txt)",
    )

    parser.add_argument(
        "--trigger-id",
        type=str,
        help="Trigger ID (required for delete and executions commands)",
    )

    parser.add_argument(
        "--sample",
        choices=["pr-review", "pr-opened"],
        help="Create a sample trigger configuration",
    )

    parser.add_argument(
        "--config",
        type=str,
        help="Path to JSON file containing trigger configuration",
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output (shows detailed trigger information for list command) and verbose logging",
    )

    parser.add_argument(
        "--json",
        action="store_true",
        help="Output raw JSON response (for debugging)",
    )

    # Arguments for new APIs
    parser.add_argument(
        "--entity-id",
        type=str,
        help="Entity ID (required for execute-manually command)",
    )

    parser.add_argument(
        "--entity-type",
        type=int,
        default=1,
        help="Entity type (default: 1 for GITHUB_ENTITY_TYPE_PULL_REQUEST)",
    )

    parser.add_argument(
        "--event-source",
        type=int,
        default=1,
        help="Event source (default: 1 for EVENT_SOURCE_GITHUB)",
    )

    parser.add_argument(
        "--limit",
        type=int,
        default=5,
        help="Limit for matching entities (default: 5)",
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Get API credentials
        api_url = get_api_url(args.api_url)
        api_token = get_api_token(args.token_file)

        logger.info(f"Using API URL: {api_url}")

        # Create client
        client = AugmentClient(url=api_url, token=api_token)

        # Execute command
        if args.command == "list":
            list_triggers(client, verbose=args.verbose, json_output=args.json)

        elif args.command == "create":
            if args.sample:
                if args.sample == "pr-review":
                    trigger_config = create_sample_pr_trigger()
                elif args.sample == "pr-opened":
                    trigger_config = create_sample_pr_opened_trigger()
                create_trigger(client, trigger_config)
            elif args.config:
                if not os.path.exists(args.config):
                    logger.error(f"Config file not found: {args.config}")
                    sys.exit(1)
                with open(args.config, "r") as f:
                    trigger_config = json.load(f)
                create_trigger(client, trigger_config)
            else:
                logger.error(
                    "Either --sample or --config must be specified for create command"
                )
                sys.exit(1)

        elif args.command == "delete":
            if not args.trigger_id:
                logger.error("--trigger-id is required for delete command")
                sys.exit(1)
            delete_trigger(client, args.trigger_id)

        elif args.command == "executions":
            if not args.trigger_id:
                logger.error("--trigger-id is required for executions command")
                sys.exit(1)
            get_trigger_executions(client, args.trigger_id)

        elif args.command == "matching-entities":
            get_matching_entities(
                client,
                trigger_id=args.trigger_id,
                event_source=args.event_source,
                limit=args.limit,
                json_output=args.json,
            )

        elif args.command == "execute-manually":
            if not args.trigger_id:
                logger.error("--trigger-id is required for execute-manually command")
                sys.exit(1)
            if not args.entity_id:
                logger.error("--entity-id is required for execute-manually command")
                sys.exit(1)
            execute_trigger_manually(
                client,
                args.trigger_id,
                args.entity_id,
                entity_type=args.entity_type,
            )

    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()

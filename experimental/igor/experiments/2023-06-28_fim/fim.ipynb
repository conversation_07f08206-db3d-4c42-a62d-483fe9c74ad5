#%%
%load_ext autoreload
%autoreload 2

import os
import sys
import numpy as np
from termcolor import colored
from textwrap import dedent

from research.models.core import (
    CodeGen_2B_FIM,
    CodeGen_16B_Indiana
)
from research.retrieval.types import Document, Chunk
from research.retrieval.legacy_retrieval_implementations.bm25  import Bm25DocumentIndex

import logging
logging.basicConfig(level=logging.WARNING)

checkpoints_root = "/mnt/efs/augment/checkpoints"
retrieval_top_k = 3
RESEARCH_STARCODER_URL = "http://starcoder-a40.tenant-augment-eng.coreweave.cloud:5000"
AUGMENT_PROD_URL = "https://ab7796028231e4077b4c0877e6451186-1787811428.api.augmentcode.com"
OPENAI_API_KEY = ""

print("Loading the model...")
model_fim = CodeGen_2B_FIM(checkpoints_root, retrieval_top_k)
model_fim.load()

model_indiana = CodeGen_16B_Indiana(checkpoints_root, retrieval_top_k)
model_indiana.load()
#%%
def fim_tests(model):
    def fim_test(name, prompt):
        fim_marker = "<FILL-HERE>"
        fim_marker_idx = prompt.find(fim_marker)
        prefix = prompt[0:fim_marker_idx]
        suffix = prompt[fim_marker_idx+len(fim_marker):]

        assert prefix + fim_marker + suffix == prompt

        generated = model.generate(
            prefix=prefix,
            suffix=suffix,
            temperature=0,
            max_generated_tokens=250,
        )

        eos_idx = generated.find("<|fim-eos|>")
        generated_clipped = generated if eos_idx < 0 else generated[:eos_idx]

        print("========================================================================================")
        print(f"EXAMPLE: {name}")
        print("----------------------------------------------------------------------------------------")
        print(prompt)
        print("----------------------------------------------------------------------------------------")
        print(
            colored(prefix, "blue") +
            colored(generated_clipped, "white", "on_black") +
            colored(suffix, "blue")
        )
        print("========================================================================================")
        print()
        print()

    fim_test(
        name="Trailing Return",
        prompt=dedent("""
            def get_odd_ints(lower, upper):
            <FILL-HERE>
                return results
            """)
    )

    fim_test(
        name="Var Def + Trailing Return",
        prompt=dedent("""
            def get_odd_ints(lower, upper):
                results = []<FILL-HERE>
                return results
            """),
    )

    fim_test(
        name="Fill-in Docstring",
        prompt=dedent("""
            def get_odd_ints(lower, upper):
                \"\"\"<FILL-HERE>\"\"\"
                return [i for i in range(lower, upper) if i % 2 == 1]
            """),
    )

    fim_test(
        name="Just EOS",
        prompt=dedent("""
            def hello_world():<FILL-HERE>
                print("Hello World!")
            """),
    )

    fim_test(
        name="Missing Space",
        prompt=dedent("""
            def hello_world():,
            <FILL-HERE>print("Hello World!")
        """)
    )

#%% md
## CodeGen_2B_FIM
#%%
fim_tests(model_fim)
#%% md
## CodeGen_16B_Indiana
#%%
fim_tests(model_indiana)
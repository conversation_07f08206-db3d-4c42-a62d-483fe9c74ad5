"""
This module provides a Flask application with several endpoints to manage customers and their credits.

Endpoints:

* `/customer` GET - returns a list of customers and information about each
* `/customer` POST - creates a new customer, along with the SKU for dev-days for that customer
* `/customer/consume` POST - consumes a credit from the customer's credit balance
* `/customer/purchase` POST - adds credits to the customer's credit balance
"""

import datetime as date
import os
import re

import auth
import requests
from auth import authenticate
from config import base_url, realm_id
from flask import Flask, render_template, request, url_for
from markupsafe import Markup

# Only companies with the customer type "Augment-user" are considered customers
customer_type = "Augment-user"
# The base URL for our sandbox application
application_url = "{}/v3/company/{}".format(base_url, realm_id)
# The directory where the templates are found
basedir = os.path.dirname(os.path.abspath(__file__))
template_dir = os.path.join(basedir, "../customer-manager/templates")

app = Flask(__name__, template_folder=template_dir)


def _get_headers():
    return {
        "Authorization": auth.get_auth_header(),
        "Accept": "application/json",
        "Content-Type": "application/json",
    }


def _get_customer_type_id(customer_type):
    # Retrieve the CustomerType.Id for the given customer type
    request_str = f"{application_url}/query?query=select Id from customertype where Name = '{customer_type}'&minorversion=40"
    response = requests.get(request_str, headers=_get_headers())
    return response.json().get("QueryResponse").get("CustomerType")[0].get("Id")


def _get_id_by_name(name, table="Account", field="Name"):
    # Retrieve the Account.Id for the given account
    request_str = f"{application_url}/query?query=select Id from {table} where {field} = '{name}'&minorversion=40"
    response = requests.get(request_str, headers=_get_headers())
    try:
        return response.json().get("QueryResponse").get(table)[0].get("Id")
    except IndexError:
        return None


def _get_augment_vendor():
    vendor_id = _get_id_by_name("Augment", field="DisplayName", table="Vendor")
    if vendor_id is not None:
        return vendor_id
    # Create a vendor named "Augment" and return the id
    request_str = f"{application_url}/vendor"
    response = requests.post(
        request_str, headers=_get_headers(), json={"DisplayName": "Augment"}
    )
    return response.json().get("Vendor").get("Id")


def _get_asset_account_id(asset="Inventory Asset"):
    return _get_id_by_name(asset)


def _get_income_account_id(account="Sales of Product Income"):
    return _get_id_by_name(account)


def _get_expense_account_id(account="Cost of Goods Sold"):
    return _get_id_by_name(account)


def _get_sku_info(customer_name):
    table = "Item"
    field = "Name"
    name = _get_customer_dev_days_name(customer_name)
    request_str = f"{application_url}/query?query=select Id, UnitPrice from {table} where {field} = '{name}'&minorversion=40"
    response = requests.get(request_str, headers=_get_headers())
    return {
        "name": name,
        "id": response.json().get("QueryResponse").get(table)[0].get("Id"),
        "unit_price": response.json()
            .get("QueryResponse")
            .get(table)[0]
            .get("UnitPrice"),
    }


def _get_customer_dev_days_name(customer_name):
    return f"{customer_name}-devdays"


def _get_customer_dev_days_id(customer_name):
    return _get_id_by_name(_get_customer_dev_days_name(customer_name), table="Item")


@app.route("/auth_callback")
def auth_callback():
    # Callback endpoint for OAuth2 authentication
    # Save the authorisation code
    code = request.args.get("code")
    auth.set_auth_code(code)
    auth.callback_event.set()
    return render_template("authenticated.html")


@app.route("/", methods=["GET"])
@app.route("/customer", methods=["GET"])
def get_customers(status="", status_class=None):
    # Return a list of customers and information about each

    # Authenticate access
    authenticate()

    # Retrieve the CustomerType.Id for "Augment-user"
    customer_type_id = _get_customer_type_id(customer_type)

    # Retrieve a list of customers
    request_str = f"{application_url}/query?query=select Id, DisplayName, CustomerTypeRef from customer"
    response = requests.get(request_str, headers=_get_headers())

    # Filter the list to only include customers with the correct CustomerType
    customer_list = response.json().get("QueryResponse", {}).get("Customer", [])
    customer_list = [
        c
        for c in customer_list
        if c.get("CustomerTypeRef", {}).get("value") == customer_type_id
    ]
    customer_list = [
        {"name": c.get("DisplayName"), "id": c.get("Id")} for c in customer_list
    ]

    # Retrieve the inventory of dev-days for each customer
    request_str = f"{application_url}/query?query=select Name, QtyOnHand from item"
    response = requests.get(request_str, headers=_get_headers())
    dev_days_list = response.json().get("QueryResponse", {}).get("Item", [])
    dev_days_map = {c.get("Name"): c.get("QtyOnHand") for c in dev_days_list}

    # Render the customer list
    title = "Augment Customer Management Portal"
    heading = "Augment Customer Management Portal"
    customers = []
    for c in customer_list:
        customer_name = c.get("name")
        customer_credits = dev_days_map.get(
            _get_customer_dev_days_name(customer_name), 0
        )
        customers.append(
            {
                "name": customer_name,
                "id": c.get("id"),
                "credits": customer_credits,
            }
        )
    return render_template(
        "customers.html",
        title=title,
        heading=heading,
        status=Markup(status),
        status_class=status_class,
        customers=customers,
        url=url_for("get_customers"),
    )


@app.route("/customer/form_add", methods=["GET"])
def form_add_customer():
    # Add a new customer
    return render_template("add_customer.html", url=url_for("get_customers"))


@app.route("/customer/form_purchase", methods=["GET"])
def form_purchase_credits():
    # Purchase credits for a customer
    customer_id = request.args.get("customer_id")
    customer_name = request.args.get("customer_name")
    return render_template(
        "purchase_credits.html",
        customer_id=customer_id,
        customer_name=customer_name,
        url=url_for("get_customers"),
    )


@app.route("/customer", methods=["POST"])
def onboard_company():
    # Onboard a new customer
    customer_name = request.form.get("customer_name", None)
    if customer_name is None:
        return get_customers(
            status="Customer name must be provided", status_class="error"
        )
    customer_name = customer_name.strip()
    customer_name = re.sub(r"\s+", "_", customer_name)

    # Authenticate access
    authenticate()

    # Retrieve the CustomerType.Id for "Augment-user"
    customer_type_id = _get_customer_type_id(customer_type)

    # Create the customer
    customer_data = {
        "DisplayName": customer_name,
        "CustomerTypeRef": {
            "value": customer_type_id
        },
    }
    request_str = f"{application_url}/customer"
    response = requests.post(request_str, headers=_get_headers(), json=customer_data)
    if response.status_code != 200:
        return get_customers(
            status=f"Failed to create customer {customer_name}<br><pre>{response.text}</pre>",
            status_class="error",
        )

    # Add the customer's dev-days SKU
    cost_per_credit = request.form.get("cost_per_credit")
    initial_credits = request.form.get("initial_credits")
    customer_dev_days = _get_customer_dev_days_name(customer_name)
    sku_data = {
        "Name": customer_dev_days,
        "Active": True,
        "Type": "Inventory",
        "InvStartDate": date.date.today().strftime("%Y-%m-%d"),
        "QtyOnHand": initial_credits,
        "UnitPrice": cost_per_credit,
        "TrackQtyOnHand": True,
        "AssetAccountRef": {
            "value": _get_asset_account_id()
        },
        "IncomeAccountRef": {
            "value": _get_income_account_id()
        },
        "ExpenseAccountRef": {
            "value": _get_expense_account_id()
        },
    }
    request_str = f"{application_url}/item"
    response = requests.post(request_str, headers=_get_headers(), json=sku_data)
    if response.status_code != 200:
        return get_customers(
            status=f"Failed to create SKU {customer_dev_days}<br><pre>{response.text}</pre>",
            status_class="error",
        )

    return get_customers(
        status=f"Customer {customer_name} onboarded successfully.<br>SKU {customer_dev_days} created.",
        status_class="success",
    )


@app.route("/customer/consume", methods=["POST"])
def consume_credit():
    # Consume a credit from the customer's credit balance

    # Authenticate access
    authenticate()

    customer_id = request.form.get("customer_id")
    customer_name = request.form.get("customer_name")

    sales_receipt_data = {
        "CustomerRef": {
            "value": customer_id,
        },
        "Line": [
            {
                "Description": "Dev-days",
                "Amount": 1,
                "DetailType": "SalesItemLineDetail",
                "SalesItemLineDetail": {
                    "ItemRef": {
                        "value": _get_customer_dev_days_id(customer_name)
                    },
                    "Qty": 1,
                }
            }
        ]
    }
    request_str = f"{application_url}/salesreceipt"
    response = requests.post(
        request_str, headers=_get_headers(), json=sales_receipt_data
    )
    if response.status_code != 200:
        return get_customers(
            status=f"Failed to consume a credit for customer {customer_name}<br><pre>{response.text}</pre>",
            status_class="error",
        )

    return get_customers(
        status=f"Customer {customer_name} consumed a credit.", status_class="success"
    )


@app.route("/customer/purchase", methods=["POST"])
def purchase_credit():
    # Purchase credits for the customer
    credits_purchased = request.form.get("credits_purchased")
    # customer_id = request.form.get("customer_id")
    customer_name = request.form.get("customer_name")

    # Authenticate access
    authenticate()

    sku = _get_sku_info(customer_name)
    if credits_purchased is None:
        return get_customers(
            status="Number of credits to purchase must be provided",
            status_class="error",
        )
    cost = float(credits_purchased) * float(sku.get("unit_price", 0))

    purchase_order_data = {
        "TotalAmt": cost,
        "VendorRef": {
            "value": _get_augment_vendor()
        },
        "Line": [
            {
                "DetailType": "ItemBasedExpenseLineDetail",
                "Amount": cost,
                "ItemBasedExpenseLineDetail": {
                    "ItemRef": {
                        "name": sku.get("name"),
                        "value": sku.get("id")
                    },
                    "Qty": credits_purchased,
                    "UnitPrice": sku.get("unit_price")
                }
            }
        ]
    }
    request_str = f"{application_url}/purchaseorder?minorversion=70"
    response = requests.post(
        request_str, headers=_get_headers(), json=purchase_order_data
    )
    if response.status_code != 200:
        return get_customers(
            status=f"Failed to create PO for customer {customer_name}<br><pre>{response.text}</pre>",
            status_class="error",
        )
    purchase_order_id = response.json().get("PurchaseOrder").get("Id")

    bill_data = {
        "VendorRef": {
            "value": _get_augment_vendor(),
        },
        "LinkedTxn": [
            {
                "TxnId": f"{purchase_order_id}",
                "TxnType": "PurchaseOrder",
            }
        ],
        "Line": [
            {
                "DetailType": "ItemBasedExpenseLineDetail",
                "Amount": cost,
                "ItemBasedExpenseLineDetail": {
                    "ItemRef": {
                        "value": sku.get("id"),
                        #"name": sku.get("name"),
                    },
                    "Qty": credits_purchased,
                    "UnitPrice": sku.get("unit_price"),
                },
                "LinkedTxn": [
                    {
                        "TxnId": f"{purchase_order_id}",
                        "TxnType": "PurchaseOrder",
                        "TxnLineId": "1",
                    }
                ],
            }
        ],
    }
    request_str = f"{application_url}/bill?minorversion=70"
    response = requests.post(request_str, headers=_get_headers(), json=bill_data)
    if response.status_code != 200:
        return get_customers(
            status=f"Failed to create bill for customer {customer_name}<br><pre>{response.text}</pre>",
            status_class="error",
        )

    # There seems to be a bug in the QB API where the LinkedTxn field is not populated correctly.
    # Let's try to hack around this by reading the bill, then rewriting it with the correct LinkedTxn field.
    bill_id = response.json().get("Bill").get("Id")
    request_str = f"{application_url}/bill/{bill_id}?minorversion=70"
    response = requests.get(request_str, headers=_get_headers())
    resp_data = response.json()
    resp_data["Bill"]["LinkedTxn"] = bill_data.get("LinkedTxn")
    resp_data["Bill"]["Line"][0]["LinkedTxn"] = [
                    {
                        "TxnId": f"{purchase_order_id}",
                        "TxnType": "PurchaseOrder",
                        "TxnLineId": "1",
                    }
                ],
    request_str = f"{application_url}/bill/{bill_id}?minorversion=70"
    response = requests.post(
        request_str, headers=_get_headers(), json=resp_data["Bill"]
    )

    return get_customers(
        status=f"{credits_purchased} credits purchased successfully for customer {customer_name}",
        status_class="success",
    )


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000)

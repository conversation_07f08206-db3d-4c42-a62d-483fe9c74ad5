#%%
%load_ext autoreload
%autoreload 2
#%%
import dataclasses

from base.prompt_format_chat.structured_binks_prompt_formatter_v2 import (
    StructuredBinksPromptFormatterV2,
)
from base.prompt_format_chat.structured_binks_prompt_formatter_v2_test import (
    CHAT_PROMPT_INPUT,
    TOKEN_APPORTIONMENT,
    _get_chat_history_with_tool_calls,
    _get_system_prompt,
    _get_user_guided_retrieved_chunks_single_file,
    RETRIEVED_CHUNKS,
)
from base.prompt_format_chat.lib.token_counter import RoughTokenCounter

token_counter = RoughTokenCounter()
prompt_formatter = StructuredBinksPromptFormatterV2.create(
    token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
)
chat_history = _get_chat_history_with_tool_calls()
general_chunks = RETRIEVED_CHUNKS
user_guided_chunks = _get_user_guided_retrieved_chunks_single_file()
chat_prompt_input = dataclasses.replace(
    CHAT_PROMPT_INPUT,
    chat_history=chat_history,
    retrieved_chunks=general_chunks,
    # retrieved_chunks=user_guided_chunks,
    # retrieved_chunks=general_chunks + user_guided_chunks,
)
print(TOKEN_APPORTIONMENT.retrieval_len)
print(TOKEN_APPORTIONMENT.retrieval_len_per_each_user_guided_file)
print(TOKEN_APPORTIONMENT.retrieval_len_for_user_guided)
prompt = prompt_formatter.format_prompt(chat_prompt_input)
print(token_counter.count_tokens_in_request(prompt.chat_history[0].request_message))
print(
    token_counter.count_tokens_in_request(prompt.chat_history[0].request_message) * 0.98
)
# print(prompt.system_prompt)
# print(prompt.message)
# for exchange in prompt.chat_history:
#     print(exchange.request_message)
#     print(exchange.response_text)
# print(prompt.tools)
#%%
from base.prompt_format_chat.structured_binks_prompt_formatter_v2_test import (
    _create_large_text,
    _verify_token_count,
)
from base.prompt_format_chat.structured_binks_prompt_formatter_v2 import (
    split_short_term_chat_history,
)

token_counter = RoughTokenCounter()
prompt_formatter = StructuredBinksPromptFormatterV2.create(
    token_counter, TOKEN_APPORTIONMENT, _get_system_prompt
)

# Create a large current message
large_current_message = _create_large_text("This is a large current message: ")

# Create a large selected code
large_selected_code = _create_large_text("// This is a large selected code section:\n")

# Verify token counts
_verify_token_count(token_counter, large_current_message, 25000, 35000)
_verify_token_count(token_counter, large_selected_code, 25000, 35000)

# Create a chat prompt input with both large components
chat_prompt_input = dataclasses.replace(
    CHAT_PROMPT_INPUT,
    message=large_current_message,
    selected_code=large_selected_code,
    context_code_exchange_request_id="new",  # Mark as newly selected code
)

earlier_history, short_term_history = split_short_term_chat_history(
    chat_prompt_input.chat_history
)
print(len(earlier_history), len(short_term_history))
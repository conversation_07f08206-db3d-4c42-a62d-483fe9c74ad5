#%%
%load_ext autoreload
%autoreload 2
#%%
import pickle

with open("/mnt/efs/augment/user/zhuoran/gemini/replay_batch_results2.pkl", "rb") as f:
    results = pickle.load(f)
#%%
for request_index, request_results in enumerate(results):
    for round_index, round_results in enumerate(request_results):
        for trial_index, result in enumerate(round_results):
            if result[-1] is None:
                result = (*result[:-1], [])


with open("/mnt/efs/augment/user/zhuoran/gemini/replay_batch_results2.pkl", "wb") as f:
    pickle.dump(results, f)
#%%
import numpy as np

from research.tools.chat_replay.replay_utils import count_tool_calls

lazy_delegation_confusion = np.zeros((4, 4))
lack_of_action_confusion = np.zeros((4, 4))
disagreement_indices = []
for request_index, request_results in enumerate(results):
    for round_index, round_results in enumerate(request_results):
        for trial_index, result in enumerate(round_results):
            text_length, tool_calls, probing_results, response, prompt_output = result
            pro_lazy_delegation, pro_lack_of_action = probing_results["pro"].values()
            flash_lazy_delegation, flash_lack_of_action = probing_results[
                "flash"
            ].values()
            pro_standalone_lazy_delegation, pro_standalone_lack_of_action = (
                probing_results["pro_standalone"].values()
            )
            flash_standalone_lazy_delegation, flash_standalone_lack_of_action = (
                probing_results["flash_standalone"].values()
            )
            lazy_delegation_confusion[0] += pro_lazy_delegation
            lazy_delegation_confusion[1] += flash_lazy_delegation
            lazy_delegation_confusion[2] += pro_standalone_lazy_delegation
            lazy_delegation_confusion[3] += flash_standalone_lazy_delegation
            lazy_delegation_confusion[:, 0] -= pro_lazy_delegation
            lazy_delegation_confusion[:, 1] -= flash_lazy_delegation
            lazy_delegation_confusion[:, 2] -= pro_standalone_lazy_delegation
            lazy_delegation_confusion[:, 3] -= flash_standalone_lazy_delegation
            lack_of_action_confusion[0] += pro_lack_of_action
            lack_of_action_confusion[1] += flash_lack_of_action
            lack_of_action_confusion[2] += pro_standalone_lack_of_action
            lack_of_action_confusion[3] += flash_standalone_lack_of_action
            lack_of_action_confusion[:, 0] -= pro_lack_of_action
            lack_of_action_confusion[:, 1] -= flash_lack_of_action
            lack_of_action_confusion[:, 2] -= pro_standalone_lack_of_action
            lack_of_action_confusion[:, 3] -= flash_standalone_lack_of_action
            tool_call_count = count_tool_calls(response[1:])
            if (
                (
                    0
                    < (
                        pro_lazy_delegation
                        + flash_lazy_delegation
                        + pro_standalone_lazy_delegation
                        + flash_standalone_lazy_delegation
                    )
                    < 4
                    or 0
                    < (
                        pro_lack_of_action
                        + flash_lack_of_action
                        + pro_standalone_lack_of_action
                        + flash_standalone_lack_of_action
                    )
                    < 4
                )
                and tool_call_count == 0
                and len(response) > 1
            ):
                disagreement_indices.append((request_index, round_index, trial_index))

print(lazy_delegation_confusion)
print(lack_of_action_confusion)
print(len(disagreement_indices))
#%%
disagreement_metaindex = 0
#%%
import json

from research.tools.chat_replay.replay_utils import print_response

print(disagreement_metaindex)
request_index, round_, trial = disagreement_indices[disagreement_metaindex]
result = results[request_index][round_][trial]
text_length, tool_calls, probing_results, response, prompt_output = result
print(text_length, tool_calls)
print(json.dumps(probing_results, indent=4))
print_response(
    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)
)
disagreement_metaindex += 1
#%%
# request_index = 3
# round_ = 3
# trial = 1
# result = results[request_index][round_][trial]
#%%
from research.tools.chat_replay.replay_utils import print_response

text_length, tool_calls, probing_results, response, prompt_output = result
response = response or []
print(text_length, tool_calls)
print_response(
    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)
)
#%%
from research.tools.chat_replay.replay_utils import print_chat_history, print_request

print_chat_history(prompt_output.chat_history, text_limit=100, tool_limit=100)
print("=" * 81)
print_request(prompt_output.message)
print("-" * 81)
print_response(
    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)
)
#%%
LAZY_DELEGATION_PROMPT = """Please answer the following questions:

1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.
2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?

Your task is to PURELY answer the question above without taking any other action.

Your answer must end with a clear Yes-No for each question, in the following format:
<answer id=1>Yes</answer>
<answer id=2>No</answer>
""".strip()
#%%
from research.tools.chat_replay.replay_utils import (
    append_to_prompt_output,
    run_model,
    TOOL_DEFINITIONS,
)

probing_prompt_output = append_to_prompt_output(
    results[request_index][round_][trial][-1],
    response[1:],
    LAZY_DELEGATION_PROMPT,
)
probing_response = run_model(
    probing_prompt_output,
    tool_definitions=TOOL_DEFINITIONS,
    base_model_version="gemini2.5-flash",
    client_type="google_genai",
    yield_final_parameters=True,
)
print_response(
    probing_response[1:],  # type: ignore
    tool_limit=int(1e9),
    string_limit=int(1e9),
    text_limit=int(1e9),
)
#%%
LAZY_DELEGATION_STANDALONE_PROMPT = """The previous message is a single turn in an AI agent's execution trajectory. Please answer the following questions:

1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.
2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?

Your task is to PURELY answer the question above without taking any other action.

Your answer must end with a clear Yes-No for each question, in the following format:
<answer id=1>Yes</answer>
<answer id=2>No</answer>
""".strip()
#%%
from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput
from base.prompt_format.common import Exchange
from research.tools.chat_replay.replay_utils import (
    run_model,
    TOOL_DEFINITIONS,
)

probing_prompt_output = StructuredChatPromptOutput(
    system_prompt="",
    chat_history=[Exchange(request_message=[], response_text=response[1:])],
    message=LAZY_DELEGATION_STANDALONE_PROMPT,
    retrieved_chunks_in_prompt=[],
)
probing_response = run_model(
    probing_prompt_output,
    tool_definitions=TOOL_DEFINITIONS,
    base_model_version="gemini2.5-flash",
    client_type="google_genai",
    yield_final_parameters=True,
)
print_response(
    probing_response[1:],  # type: ignore
    tool_limit=int(1e9),
    string_limit=int(1e9),
    text_limit=int(1e9),
)
#%%
from research.tools.chat_replay.replay_utils import (
    append_to_prompt_output,
    run_model,
    TOOL_DEFINITIONS,
)

remedy_prompt_output = append_to_prompt_output(
    results[request_index][round_][trial][-1],
    response[1:],
    """Perform the action yourself, instead of asking the user to perform it.
Perform the exact action(s) you asked the user to perform.""",
)
remedy_response = run_model(
    remedy_prompt_output,
    tool_definitions=TOOL_DEFINITIONS,
    base_model_version="gemini2.5",
    client_type="google_genai",
    yield_final_parameters=True,
)
print_response(
    remedy_response[1:],  # type: ignore
    tool_limit=int(1e9),
    string_limit=int(1e9),
    text_limit=int(1e9),
)
#%%
from research.tools.chat_replay.replay_utils import (
    run_model,
    print_response,
    fix_tool_calls,
    TOOL_DEFINITIONS,
)

prompt_output = prompt_outputs[request_index][round_][trial]
response = run_model(
    prompt_output,
    tool_definitions=TOOL_DEFINITIONS,
    base_model_version="gemini2.5-pro-prev",
    client_type="google_genai",
    yield_final_parameters=True,
)
print_response(
    response[1:], tool_limit=int(1e9), string_limit=int(1e9), text_limit=int(1e9)
)
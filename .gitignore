.DS_Store
node_modules
/.vscode
eval_results_*.json
neox.db
__pycache__/
*.egg-info
bazel-augment
bazel-bin
bazel-out
bazel-testlogs
.terraform
.anthropic
.hypothesis

coverage-error.log

### Automatically added by <PERSON><PERSON>'s <PERSON><PERSON> Compile Commands Extractor: https://github.com/hedronvision/bazel-compile-commands-extractor
# Ignore the `external` link (that is added by `bazel-compile-commands-extractor`). The link differs between macOS/Linux and Windows, so it shouldn't be checked in. The pattern must not end with a trailing `/` because it's a symlink on macOS/Linux.
/external
# Ignore links to <PERSON><PERSON>'s output. The pattern needs the `*` because people can change the name of the directory into which your repository is cloned (changing the `bazel-<workspace_name>` symlink), and must not end with a trailing `/` because it's a symlink on macOS/Linux.
/bazel-*
# Ignore generated output. Although valuable (after all, the primary purpose of `bazel-compile-commands-extractor` is to produce `compile_commands.json`!), it should not be checked in.
/compile_commands.json
# Ignore the directory in which `clangd` stores its local index.
/.cache/
.augment/*
!.augment/env
# TODO(jeff): HACK to allow personal setup scripts at repository-scope.
# Any script starting with a dot is ignored.
.augment/env/\.*
!.augment/rules/
# place rust cargo places information
target
# "Favorites" vscode extension used by Markus:
.favorites.json


# eval_launch.log is generated by `eval.py` from wherever it is called from
eval_launch.log

# The temporary files used by the vim editor
*.swp

# Project specific vim configuration
.vimrc

# Sometimes, we locally build files from `setup.py`, which we do not want to commit.
build
*.so

# LLAMA Build Log
llama*.log
third_party/llama.cpp/llava

# Neptune cache
.neptune/

# Fastbackward
research/fastbackward/wandb
research/fastbackward/multinode-scripts/tmp_pod.yaml
research/fastbackward/multinode-scripts/tmp_service.yaml

# Ignore the generated type stubs.
*_pb2_grpc.py
*_pb2_grpc.pyi
*_pb2.py
*_pb2.pyi
*.pb.go
*.mock.go
.generated_stubs

# Ignore compiled JavaScript files in TypeScript source directories
# These should only exist as build artifacts in dist/ directories
clients/sidecar/libs/transport/grpc/ts/**/*.js
clients/sidecar/libs/transport/grpc/ts/**/*.js.map
clients/sidecar/libs/transport/grpc/ts/**/*.d.ts
clients/sidecar/libs/transport/grpc/integration-test/dist

# Ignore the user bazelrc file
user.bazelrc

# ignore intellij folder
.idea

# GPU profiles are often generated in some code directories.
*.nsys-rep

.deploy_lock
.env.sentry-build-plugin

# SWE-bench run logs
/run_logs
